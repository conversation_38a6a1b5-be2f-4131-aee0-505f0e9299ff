{"_format": "hh-sol-artifact-1", "contractName": "Checkpoints", "sourceName": "@openzeppelin/contracts/utils/structs/Checkpoints.sol", "abi": [{"inputs": [], "name": "CheckpointUnorderedInsertion", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea26469706673582212209d0763cd8afdfa69a0fd1ead90a10240bb74b5b3f038caee841bbbcd2411c0a064736f6c63430008140033", "deployedBytecode": "0x600080fdfea26469706673582212209d0763cd8afdfa69a0fd1ead90a10240bb74b5b3f038caee841bbbcd2411c0a064736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}