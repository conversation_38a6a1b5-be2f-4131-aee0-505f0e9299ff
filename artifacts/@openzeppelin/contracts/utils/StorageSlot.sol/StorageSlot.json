{"_format": "hh-sol-artifact-1", "contractName": "StorageSlot", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "abi": [], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220ae25a39ead407bec927c6b9123beab38814d0e8e606e6b51ab70c388ccb21b8f64736f6c63430008140033", "deployedBytecode": "0x600080fdfea2646970667358221220ae25a39ead407bec927c6b9123beab38814d0e8e606e6b51ab70c388ccb21b8f64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}