{"_format": "hh-sol-artifact-1", "contractName": "Errors", "sourceName": "@openzeppelin/contracts/utils/Errors.sol", "abi": [{"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "FailedDeployment", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "MissingPrecompile", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea26469706673582212201b283e935c822c7731e35bbfd9d8aff275ff943e0d060069d9a4c8a183fb146164736f6c63430008140033", "deployedBytecode": "0x600080fdfea26469706673582212201b283e935c822c7731e35bbfd9d8aff275ff943e0d060069d9a4c8a183fb146164736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}