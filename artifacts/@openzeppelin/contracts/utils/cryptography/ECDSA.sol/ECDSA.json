{"_format": "hh-sol-artifact-1", "contractName": "ECDSA", "sourceName": "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "abi": [{"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220dd83379cd91612060872577f24e4aa198892a5e5275b61c6186bb6de502962a664736f6c63430008140033", "deployedBytecode": "0x600080fdfea2646970667358221220dd83379cd91612060872577f24e4aa198892a5e5275b61c6186bb6de502962a664736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}