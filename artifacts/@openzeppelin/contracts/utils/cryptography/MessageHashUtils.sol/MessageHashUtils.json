{"_format": "hh-sol-artifact-1", "contractName": "MessageHashUtils", "sourceName": "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol", "abi": [], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220f0207ad9e362a615aa90358e523b6ce04d4e69f6202f53043b8bdd8f730f26b364736f6c63430008140033", "deployedBytecode": "0x600080fdfea2646970667358221220f0207ad9e362a615aa90358e523b6ce04d4e69f6202f53043b8bdd8f730f26b364736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}