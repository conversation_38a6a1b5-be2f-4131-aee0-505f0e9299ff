{"_format": "hh-sol-artifact-1", "contractName": "ShortStrings", "sourceName": "@openzeppelin/contracts/utils/ShortStrings.sol", "abi": [{"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220be38f9d217646f870120140167ee3868bb6453ebeb69dd60ff12eeb686a8166564736f6c63430008140033", "deployedBytecode": "0x600080fdfea2646970667358221220be38f9d217646f870120140167ee3868bb6453ebeb69dd60ff12eeb686a8166564736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}