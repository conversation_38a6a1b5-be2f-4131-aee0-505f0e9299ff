{"_format": "hh-sol-artifact-1", "contractName": "Strings", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "StringsInsufficientHexLength", "type": "error"}, {"inputs": [], "name": "StringsInvalidAddressFormat", "type": "error"}, {"inputs": [], "name": "StringsInvalidChar", "type": "error"}], "bytecode": "0x60808060405234601757603a9081601d823930815050f35b600080fdfe600080fdfea2646970667358221220d08bc96a8d6dcc6448a3f0db7c13cd229f9ccdca095b3b8c535911fcf74d27b164736f6c63430008140033", "deployedBytecode": "0x600080fdfea2646970667358221220d08bc96a8d6dcc6448a3f0db7c13cd229f9ccdca095b3b8c535911fcf74d27b164736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}