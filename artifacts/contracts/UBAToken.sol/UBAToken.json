{"_format": "hh-sol-artifact-1", "contractName": "UBAToken", "sourceName": "contracts/UBAToken.sol", "abi": [{"inputs": [{"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "CheckpointUnorderedInsertion", "type": "error"}, {"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "increasedSupply", "type": "uint256"}, {"internalType": "uint256", "name": "cap", "type": "uint256"}], "name": "ERC20ExceededSafeSupply", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "timepoint", "type": "uint256"}, {"internalType": "uint48", "name": "clock", "type": "uint48"}], "name": "ERC5805FutureLookup", "type": "error"}, {"inputs": [], "name": "ERC6372InconsistentClock", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "name": "InvalidAccountNonce", "type": "error"}, {"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "SafeCastOverflowedUintDowncast", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "expiry", "type": "uint256"}], "name": "VotesExpiredSignature", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "delegator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "fromDelegate", "type": "address"}, {"indexed": true, "internalType": "address", "name": "toDelegate", "type": "address"}], "name": "DelegateChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "delegate", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "previousVotes", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newVotes", "type": "uint256"}], "name": "DelegateVotesChanged", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EIP712DomainChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "FeeDistributorAddressUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "burner", "type": "address"}], "name": "TokensBurnedForFees", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensMinted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "CLOCK_MODE", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "INITIAL_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burnForFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint32", "name": "pos", "type": "uint32"}], "name": "checkpoints", "outputs": [{"components": [{"internalType": "uint48", "name": "_key", "type": "uint48"}, {"internalType": "uint208", "name": "_value", "type": "uint208"}], "internalType": "struct Checkpoints.Checkpoint208", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "clock", "outputs": [{"internalType": "uint48", "name": "", "type": "uint48"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}], "name": "delegate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "delegates", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeDistributorContractAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "timepoint", "type": "uint256"}], "name": "getPastTotalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "timepoint", "type": "uint256"}], "name": "getPastVotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getVotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "numCheckpoints", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "recoverToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newFeeDistributor", "type": "address"}], "name": "setFeeDistributorAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}