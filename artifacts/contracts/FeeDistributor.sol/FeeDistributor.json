{"_format": "hh-sol-artifact-1", "contractName": "FeeDistributor", "sourceName": "contracts/FeeDistributor.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_ubaToken", "type": "address"}, {"internalType": "address", "name": "_uniswapRouter", "type": "address"}, {"internalType": "address", "name": "_weth", "type": "address"}, {"internalType": "uint256", "name": "_minimumProcessAmount", "type": "uint256"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FeesCollected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "ubaAmount", "type": "uint256"}], "name": "FeesProcessed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "MinimumProcessAmountUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "WETH", "outputs": [{"internalType": "contract IWETH", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "collectFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "collectedFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyRecoverToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "getCollectedFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minimumProcessAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint24", "name": "fee", "type": "uint24"}], "name": "processFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "ubaToken", "outputs": [{"internalType": "contract UBAToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "uniswap<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "contract IUniswapV3SwapRouter", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "updateMinimumProcessAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}