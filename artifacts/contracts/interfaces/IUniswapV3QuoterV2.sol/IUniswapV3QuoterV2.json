{"_format": "hh-sol-artifact-1", "contractName": "IUniswapV3QuoterV2", "sourceName": "contracts/interfaces/IUniswapV3QuoterV2.sol", "abi": [{"inputs": [{"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint24", "name": "fee", "type": "uint24"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint160", "name": "sqrtPriceLimitX96", "type": "uint160"}], "name": "quoteExactInputSingle", "outputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}, {"internalType": "uint160", "name": "sqrtPriceX96AfterList", "type": "uint160"}, {"internalType": "uint32", "name": "initializedTicksInPath", "type": "uint32"}, {"internalType": "uint256", "name": "gasEstimate", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}