{"version": 3, "file": "TransactionEnvelopeEip1559.js", "sourceRoot": "", "sources": ["../../core/TransactionEnvelopeEip1559.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,UAAU,MAAM,iBAAiB,CAAA;AAC7C,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AAEvC,OAAO,KAAK,IAAI,MAAM,WAAW,CAAA;AACjC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAC3C,OAAO,KAAK,mBAAmB,MAAM,0BAA0B,CAAA;AAiC/D,MAAM,CAAC,MAAM,cAAc,GAAG,MAAe,CAAA;AAK7C,MAAM,CAAC,MAAM,IAAI,GAAG,SAAkB,CAAA;AAGtC;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,MAAM,CACpB,QAAuD;IAEvD,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE,YAAY,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAA;IACpE,IAAI,OAAO,IAAI,CAAC;QACd,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;IAChE,IAAI,EAAE;QAAE,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;IAC7C,IAAI,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,IAAI,GAAG,EAAE;QACxD,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAA;IAC5E,IACE,oBAAoB;QACpB,YAAY;QACZ,oBAAoB,GAAG,YAAY;QAEnC,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,CAAC;YAChD,YAAY;YACZ,oBAAoB;SACrB,CAAC,CAAA;AACN,CAAC;AAWD;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,UAAU,WAAW,CACzB,UAAsB;IAEtB,MAAM,gBAAgB,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAA;IAE5D,MAAM,CACJ,OAAO,EACP,KAAK,EACL,oBAAoB,EACpB,YAAY,EACZ,GAAG,EACH,EAAE,EACF,KAAK,EACL,IAAI,EACJ,UAAU,EACV,OAAO,EACP,CAAC,EACD,CAAC,EACF,GAAG,gBAAsC,CAAA;IAE1C,IAAI,CAAC,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,IAAI,gBAAgB,CAAC,MAAM,KAAK,EAAE,CAAC;QACpE,MAAM,IAAI,mBAAmB,CAAC,sBAAsB,CAAC;YACnD,UAAU,EAAE;gBACV,OAAO;gBACP,KAAK;gBACL,oBAAoB;gBACpB,YAAY;gBACZ,GAAG;gBACH,EAAE;gBACF,KAAK;gBACL,IAAI;gBACJ,UAAU;gBACV,GAAG,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC;oBAC7B,CAAC,CAAC;wBACE,OAAO;wBACP,CAAC;wBACD,CAAC;qBACF;oBACH,CAAC,CAAC,EAAE,CAAC;aACR;YACD,UAAU;YACV,IAAI;SACL,CAAC,CAAA;IAEJ,IAAI,WAAW,GAAG;QAChB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;QACxB,IAAI;KACyB,CAAA;IAC/B,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,IAAI;QAAE,WAAW,CAAC,EAAE,GAAG,EAAE,CAAA;IACxD,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,IAAI;QAAE,WAAW,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;IACpE,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI;QAAE,WAAW,CAAC,IAAI,GAAG,IAAI,CAAA;IAChE,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrB,WAAW,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IACzD,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI;QAAE,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;IAC5E,IAAI,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,YAAY,KAAK,IAAI;QACrD,WAAW,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;IACjD,IAAI,GAAG,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,oBAAoB,KAAK,IAAI;QACrE,WAAW,CAAC,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAA;IACjE,IAAI,UAAW,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,KAAK,IAAI;QACjD,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,UAAiB,CAAC,CAAA;IAEtE,MAAM,SAAS,GACb,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IACtE,IAAI,SAAS;QACX,WAAW,GAAG;YACZ,GAAG,WAAW;YACd,GAAG,SAAS;SACiB,CAAA;IAEjC,MAAM,CAAC,WAAW,CAAC,CAAA;IAEnB,OAAO,WAAW,CAAA;AACpB,CAAC;AAMD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2EG;AACH,MAAM,UAAU,IAAI,CAMlB,QAGc,EACd,UAAmC,EAAE;IAErC,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAA;IAE7B,MAAM,SAAS,GAAG,CAChB,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAClC,CAAA;IAE/B,MAAM,CAAC,SAAS,CAAC,CAAA;IAEjB,OAAO;QACL,GAAG,SAAS;QACZ,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,IAAI,EAAE,SAAS;KACP,CAAA;AACZ,CAAC;AA8BD;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAM,UAAU,cAAc,CAC5B,QAAoC;IAEpC,OAAO,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;AAC1C,CAAC;AAQD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,UAAU,IAAI,CAClB,QAAyE,EACzE,UAAiC,EAAE;IAEnC,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAA;IAC3B,OAAO,IAAI,CAAC,SAAS,CACnB,SAAS,CAAC;QACR,GAAG,QAAQ;QACX,GAAG,CAAC,OAAO;YACT,CAAC,CAAC;gBACE,CAAC,EAAE,SAAS;gBACZ,CAAC,EAAE,SAAS;gBACZ,OAAO,EAAE,SAAS;gBAClB,CAAC,EAAE,SAAS;aACb;YACH,CAAC,CAAC,EAAE,CAAC;KACR,CAAC,CACH,CAAA;AACH,CAAC;AAgBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiDG;AACH,MAAM,UAAU,SAAS,CACvB,QAAuD,EACvD,UAA6B,EAAE;IAE/B,MAAM,EACJ,OAAO,EACP,GAAG,EACH,KAAK,EACL,EAAE,EACF,KAAK,EACL,YAAY,EACZ,oBAAoB,EACpB,UAAU,EACV,IAAI,EACJ,KAAK,GACN,GAAG,QAAQ,CAAA;IAEZ,MAAM,CAAC,QAAQ,CAAC,CAAA;IAEhB,MAAM,eAAe,GAAG,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;IAE1D,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,IAAI,QAAQ,CAAC,CAAA;IAElE,MAAM,UAAU,GAAG;QACjB,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC;QACvB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QACpC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI;QAClE,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;QAClD,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QAChC,EAAE,IAAI,IAAI;QACV,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QACpC,IAAI,IAAI,KAAK,IAAI,IAAI;QACrB,eAAe;QACf,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KACnD,CAAA;IAED,OAAO,GAAG,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAe,CAAA;AAC1E,CAAC;AAiBD;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,MAAM,UAAU,KAAK,CAAC,QAAkD;IACtE,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAE7C,OAAO;QACL,GAAG,QAAQ;QACX,OAAO,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;QACzC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK;QACrC,IAAI,EAAE,KAAK;QACX,GAAG,CAAC,OAAO,QAAQ,CAAC,GAAG,KAAK,QAAQ;YAClC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACvC,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;YACpC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3C,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;YACpC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3C,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,YAAY,KAAK,QAAQ;YAC3C,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACzD,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,OAAO,QAAQ,CAAC,oBAAoB,KAAK,QAAQ;YACnD,CAAC,CAAC;gBACE,oBAAoB,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,oBAAoB,CAAC;aACpE;YACH,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;KACxC,CAAA;AACZ,CAAC;AAMD;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,UAAU,QAAQ,CACtB,QAAuD;IAEvD,IAAI,CAAC;QACH,MAAM,CAAC,QAAQ,CAAC,CAAA;QAChB,OAAO,IAAI,CAAA;IACb,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC"}