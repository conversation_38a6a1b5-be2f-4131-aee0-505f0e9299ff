"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fromRpc = fromRpc;
exports.toRpc = toRpc;
const Hex = require("./Hex.js");
function fromRpc(log, _options = {}) {
    return {
        ...log,
        blockNumber: log.blockNumber ? BigInt(log.blockNumber) : null,
        logIndex: log.logIndex ? Number(log.logIndex) : null,
        transactionIndex: log.transactionIndex
            ? Number(log.transactionIndex)
            : null,
    };
}
function toRpc(log, _options = {}) {
    return {
        address: log.address,
        blockHash: log.blockHash,
        blockNumber: typeof log.blockNumber === 'bigint'
            ? Hex.fromNumber(log.blockNumber)
            : null,
        data: log.data,
        logIndex: typeof log.logIndex === 'number' ? Hex.fromNumber(log.logIndex) : null,
        topics: log.topics,
        transactionHash: log.transactionHash,
        transactionIndex: typeof log.transactionIndex === 'number'
            ? Hex.fromNumber(log.transactionIndex)
            : null,
        removed: log.removed,
    };
}
//# sourceMappingURL=Log.js.map