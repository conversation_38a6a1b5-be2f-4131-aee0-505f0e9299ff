{"version": 3, "file": "verkle.js", "sourceRoot": "", "sources": ["../../src/verkle.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,aAAa,EACb,UAAU,EACV,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,EACb,cAAc,EACd,OAAO,GACR,MAAM,YAAY,CAAA;AA2BnB;;;;;;;GAOG;AACH,MAAM,UAAU,aAAa,CAC3B,GAAiB,EACjB,OAAgB,EAChB,YAA6B,CAAC;IAE9B,MAAM,SAAS,GAAG,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC,CAAA;IAEtD,IAAI,cAA0B,CAAA;IAC9B,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QACjC,cAAc,GAAG,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAA;KAC3E;SAAM;QACL,cAAc,GAAG,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;KACzF;IAED,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAE1E,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,iBAAiB,CAC/B,GAAiB,EACjB,YAAwB,EACxB,gBAAwC;IAExC,OAAO,GAAG,CAAC,8BAA8B,CACvC,UAAU,CAAC,YAAY,CAAC,EACxB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CACjC,CAAA;AACH,CAAC;AA2CD,MAAM,CAAN,IAAY,cAMX;AAND,WAAY,cAAc;IACxB,yDAAW,CAAA;IACX,yDAAW,CAAA;IACX,qDAAS,CAAA;IACT,2DAAY,CAAA;IACZ,2DAAY,CAAA;AACd,CAAC,EANW,cAAc,KAAd,cAAc,QAMzB;AAED,MAAM,CAAC,MAAM,uBAAuB,GAAG,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;AACzE,MAAM,CAAC,MAAM,uBAAuB,GAAG,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;AACzE,MAAM,CAAC,MAAM,qBAAqB,GAAG,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;AACrE,MAAM,CAAC,MAAM,yBAAyB,GAAG,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;AAC5E,MAAM,CAAC,MAAM,yBAAyB,GAAG,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;AAE5E,MAAM,CAAC,MAAM,4BAA4B,GAAG,EAAE,CAAA;AAC9C,MAAM,CAAC,MAAM,kBAAkB,GAAG,GAAG,CAAA;AACrC,MAAM,CAAC,MAAM,iBAAiB,GAAG,GAAG,CAAA;AACpC,MAAM,CAAC,MAAM,0BAA0B,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,CAAA;AAEnE;;;;;;GAMG;AAEH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,IAAgB,EAAE,IAAiC,EAAE,EAAE;IAClF,QAAQ,IAAI,EAAE;QACZ,KAAK,cAAc,CAAC,OAAO;YACzB,OAAO,WAAW,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAA;QACnD,KAAK,cAAc,CAAC,OAAO;YACzB,OAAO,WAAW,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAA;QACnD,KAAK,cAAc,CAAC,KAAK;YACvB,OAAO,WAAW,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAA;QACjD,KAAK,cAAc,CAAC,QAAQ;YAC1B,OAAO,WAAW,CAAC,IAAI,EAAE,yBAAyB,CAAC,CAAA;QACrD,KAAK,cAAc,CAAC,QAAQ;YAC1B,OAAO,WAAW,CAAC,IAAI,EAAE,yBAAyB,CAAC,CAAA;QACrD;YACE,OAAO,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;KACjC;AACH,CAAC,CAAA;AAED,MAAM,UAAU,kCAAkC,CAAC,UAAkB;IAInE,IAAI,QAAgB,CAAA;IACpB,IAAI,UAAU,GAAG,kBAAkB,GAAG,4BAA4B,EAAE;QAClE,QAAQ,GAAG,MAAM,CAAC,4BAA4B,CAAC,GAAG,UAAU,CAAA;KAC7D;SAAM;QACL,QAAQ,GAAG,0BAA0B,GAAG,UAAU,CAAA;KACnD;IAED,MAAM,SAAS,GAAG,QAAQ,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAA;IACtD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAA;IAE7D,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAA;AAChC,CAAC;AAED,MAAM,UAAU,gCAAgC,CAAC,OAAe;IAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,kBAAkB,GAAG,OAAO,CAAC,GAAG,iBAAiB,CAAC,CAAA;IAChF,MAAM,QAAQ,GAAG,CAAC,kBAAkB,GAAG,OAAO,CAAC,GAAG,iBAAiB,CAAA;IACnE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAA;AAChC,CAAC;AAED,MAAM,CAAC,MAAM,4BAA4B,GAAG,KAAK,EAC/C,OAAgB,EAChB,OAAe,EACf,YAA0B,EAC1B,EAAE;IACF,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,gCAAgC,CAAC,OAAO,CAAC,CAAA;IACzE,OAAO,WAAW,CAAC,aAAa,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;AACxF,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,IAAgB,EAAE,EAAE;IAC/C,mCAAmC;IACnC,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE;QAC1B,MAAM,aAAa,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAA;QAC7C,IAAI,GAAG,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,CAAA;KACzD;IAED,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;AACpC,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,8BAA8B,GAAG,KAAK,EACjD,OAAgB,EAChB,UAAkB,EAClB,YAA0B,EAC1B,EAAE;IACF,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,kCAAkC,CAAC,UAAU,CAAC,CAAA;IAE9E,OAAO,WAAW,CAAC,aAAa,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;AACxF,CAAC,CAAA"}