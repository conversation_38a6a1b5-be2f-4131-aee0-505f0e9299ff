{"version": 3, "file": "address.js", "sourceRoot": "", "sources": ["../../src/address.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,YAAY,GACb,MAAM,cAAc,CAAA;AACrB,OAAO,EACL,aAAa,EACb,aAAa,EACb,UAAU,EACV,WAAW,EACX,UAAU,EACV,KAAK,GACN,MAAM,YAAY,CAAA;AACnB,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAA;AAIzC;;GAEG;AACH,MAAM,OAAO,OAAO;IAGlB,YAAY,KAAiB;QAC3B,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;SAC1C;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAI;QACT,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;IAC/B,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,UAAU,CAAC,GAAW;QAC3B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,EAAE,CAAC,CAAA;SAChD;QACD,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA;IACrC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,aAAa,CAAC,MAAkB;QACrC,IAAI,CAAC,CAAC,MAAM,YAAY,UAAU,CAAC,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;SACnD;QACD,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;QAClC,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,cAAc,CAAC,UAAsB;QAC1C,IAAI,CAAC,CAAC,UAAU,YAAY,UAAU,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;SACpD;QACD,MAAM,KAAK,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAA;QAC1C,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAa,EAAE,KAAa;QAC1C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAA;SACjD;QACD,OAAO,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACvE,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,SAAS,CAAC,IAAa,EAAE,IAAgB,EAAE,QAAoB;QACpE,IAAI,CAAC,CAAC,IAAI,YAAY,UAAU,CAAC,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;SACpD;QACD,IAAI,CAAC,CAAC,QAAQ,YAAY,UAAU,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;SACxD;QACD,OAAO,IAAI,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAA;IAClE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAgB;QACrB,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;IACpC,CAAC;IAED;;;OAGG;IACH,2BAA2B;QACzB,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACzC,MAAM,QAAQ,GAAG,QAAQ,CAAA;QACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;QACjC,OAAO,OAAO,IAAI,QAAQ,IAAI,OAAO,IAAI,QAAQ,CAAA;IACnD,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACnC,CAAC;CACF"}