export var KeyEncoding;
(function (KeyEncoding) {
    KeyEncoding["String"] = "string";
    KeyEncoding["Bytes"] = "view";
    KeyEncoding["Number"] = "number";
})(KeyEncoding || (KeyEncoding = {}));
export var ValueEncoding;
(function (ValueEncoding) {
    ValueEncoding["String"] = "string";
    ValueEncoding["Bytes"] = "view";
    ValueEncoding["JSON"] = "json";
})(ValueEncoding || (ValueEncoding = {}));
//# sourceMappingURL=db.js.map