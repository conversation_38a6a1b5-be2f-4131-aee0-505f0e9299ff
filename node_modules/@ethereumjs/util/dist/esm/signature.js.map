{"version": 3, "file": "signature.js", "sourceRoot": "", "sources": ["../../src/signature.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAA;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,oCAAoC,CAAA;AAE9D,OAAO,EACL,aAAa,EACb,UAAU,EACV,UAAU,EACV,WAAW,EACX,aAAa,EACb,OAAO,EACP,WAAW,GACZ,MAAM,YAAY,CAAA;AACnB,OAAO,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,eAAe,EACf,qBAAqB,GACtB,MAAM,gBAAgB,CAAA;AACvB,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAA;AAU5C;;;;;GAKG;AACH,MAAM,UAAU,MAAM,CACpB,OAAmB,EACnB,UAAsB,EACtB,OAAgB;IAEhB,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;IAC/C,MAAM,GAAG,GAAG,GAAG,CAAC,iBAAiB,EAAE,CAAA;IACnC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC1B,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IAE3B,MAAM,CAAC,GACL,OAAO,KAAK,SAAS;QACnB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAS,GAAG,EAAE,CAAC;QAC5B,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAS,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAA;IAE7D,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAA;AACpB,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,CAAS,EAAE,OAAgB;IAC9D,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,QAAQ;QAAE,OAAO,CAAC,CAAA;IAE9C,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,OAAO,CAAC,GAAG,SAAS,CAAA;KACrB;IACD,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;AAC9C,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB;IAC1C,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,QAAQ,CAAA;AACvD,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,UACvB,OAAmB,EACnB,CAAS,EACT,CAAa,EACb,CAAa,EACb,OAAgB;IAEhB,MAAM,SAAS,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IACzE,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;IACvF,MAAM,YAAY,GAAG,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;IAClD,OAAO,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;AAChD,CAAC,CAAA;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,UACtB,CAAS,EACT,CAAa,EACb,CAAa,EACb,OAAgB;IAEhB,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,6EAA6E;IAE7E,OAAO,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACxF,CAAC,CAAA;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,UAC1B,CAAS,EACT,CAAa,EACb,CAAa,EACb,OAAgB;IAEhB,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAClC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,QAAQ,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE;QACvF,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;KACd;IAED,OAAO,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;AAC7E,CAAC,CAAA;AAED;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,GAAsB;IACxD,MAAM,KAAK,GAAe,OAAO,CAAC,GAAG,CAAC,CAAA;IAEtC,IAAI,CAAa,CAAA;IACjB,IAAI,CAAa,CAAA;IACjB,IAAI,CAAS,CAAA;IACb,IAAI,KAAK,CAAC,MAAM,IAAI,EAAE,EAAE;QACtB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACzB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QAC1B,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;KACtC;SAAM,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,EAAE;QAC9B,6EAA6E;QAC7E,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACzB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QAC1B,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QACnD,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;KACb;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;KAC5C;IAED,gDAAgD;IAChD,IAAI,CAAC,GAAG,EAAE,EAAE;QACV,CAAC,GAAG,CAAC,GAAG,SAAS,CAAA;KAClB;IAED,OAAO;QACL,CAAC;QACD,CAAC;QACD,CAAC;KACF,CAAA;AACH,CAAC,CAAA;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,UAC9B,CAAS,EACT,CAAa,EACb,CAAa,EACb,mBAA4B,IAAI,EAChC,OAAgB;IAEhB,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;QACtC,OAAO,KAAK,CAAA;KACb;IAED,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD,OAAO,KAAK,CAAA;KACb;IAED,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;IAChC,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;IAEhC,IACE,OAAO,KAAK,QAAQ;QACpB,OAAO,IAAI,eAAe;QAC1B,OAAO,KAAK,QAAQ;QACpB,OAAO,IAAI,eAAe,EAC1B;QACA,OAAO,KAAK,CAAA;KACb;IAED,IAAI,gBAAgB,IAAI,OAAO,IAAI,qBAAqB,EAAE;QACxD,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED;;;;;GAKG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,UAAU,OAAmB;IAC9D,aAAa,CAAC,OAAO,CAAC,CAAA;IACtB,MAAM,MAAM,GAAG,WAAW,CAAC,mCAAmC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;IAC/E,OAAO,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAA;AAChD,CAAC,CAAA"}