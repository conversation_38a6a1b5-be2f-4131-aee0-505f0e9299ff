{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../src/internal.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;GAsBG;AAEH,OAAO,EAAE,oBAAoB,EAAE,WAAW,EAAE,MAAM,YAAY,CAAA;AAI9D;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CAAC,KAAa,EAAE,MAAe;IACxD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC;QAAE,OAAO,KAAK,CAAA;IAE/E,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM;QAAE,OAAO,KAAK,CAAA;IAEhG,OAAO,IAAI,CAAA;AACb,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,GAAW,EAAU,EAAE;IACpD,IAAI,OAAO,GAAG,KAAK,QAAQ;QACzB,MAAM,IAAI,KAAK,CAAC,0DAA0D,OAAO,GAAG,EAAE,CAAC,CAAA;IAEzF,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;AAC9C,CAAC,CAAA;AAED;;;;GAIG;AACH,MAAM,UAAU,SAAS,CAAC,KAAa;IACrC,IAAI,CAAC,GAAG,KAAK,CAAA;IAEb,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,qDAAqD,OAAO,CAAC,EAAE,CAAC,CAAA;KACjF;IAED,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC;QAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAA;IAE7B,OAAO,CAAC,CAAA;AACV,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,GAAW;IACvC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,iEAAiE,OAAO,GAAG,EAAE,CAAC,CAAA;KAC/F;IAED,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,CAAA;AACpC,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,kBAAkB,CAChC,QAAmB,EACnB,MAAiB,EACjB,IAAc;IAEd,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;QACpC,MAAM,IAAI,KAAK,CACb,mFAAmF,OAAO,QAAQ,GAAG,CACtG,CAAA;KACF;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE;QAClC,MAAM,IAAI,KAAK,CACb,iFAAiF,OAAO,MAAM,GAAG,CAClG,CAAA;KACF;IAED,OAAO,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;AAC1F,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,OAAO,CAAC,GAAW;IACjC,IAAI,GAAG,GAAG,EAAE,CAAA;IACZ,IAAI,CAAC,GAAG,CAAC,CAAA;IACT,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAA;IAEpB,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI;QAAE,CAAC,GAAG,CAAC,CAAA;IAEvC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;QACpB,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QAC3C,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;KACjC;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,QAAQ,CAAC,WAAmB;IAC1C,MAAM,GAAG,GAAG,WAAW,CAAC,WAAW,CAAC,CAAA;IAEpC,OAAO,KAAK,SAAS,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,CAAA;AAC5E,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,SAAS,CAAC,WAAmB;IAC3C,IAAI,GAAG,GAAG,EAAE,CAAA;IACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC3C,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QACtC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC3B,GAAG,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;KAClC;IAED,OAAO,KAAK,GAAG,EAAE,CAAA;AACnB,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,OAAO,CAAC,MAAgC,EAAE,GAAW,EAAE,UAAoB;IACzF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,+DAA+D,OAAO,MAAM,EAAE,CAAC,CAAA;KAChG;IACD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,MAAM,IAAI,KAAK,CACb,iEAAiE,OAAO,MAAM,EAAE,CACjF,CAAA;KACF;IAED,MAAM,MAAM,GAAG,EAAE,CAAA;IAEjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QAC1B,IAAI,UAAU,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE;YACjC,KAAK,GAAG,EAAE,CAAA;SACX;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,kDAAkD,OAAO,KAAK,EAAE,CAAC,CAAA;SAClF;QACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;KACnB;IAED,OAAO,MAAM,CAAA;AACf,CAAC"}