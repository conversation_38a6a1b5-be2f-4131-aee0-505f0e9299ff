{"version": 3, "file": "requests.d.ts", "sourceRoot": "", "sources": ["../../src/requests.ts"], "names": [], "mappings": "AAaA,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAA;AAEnD,oBAAY,YAAY,GAAG,UAAU,CAAA;AAErC,oBAAY,aAAa;IACvB,OAAO,IAAO;IACd,UAAU,IAAO;IACjB,aAAa,IAAO;CACrB;AAED,oBAAY,gBAAgB,GAAG;IAC7B,MAAM,EAAE,iBAAiB,CAAA;IACzB,qBAAqB,EAAE,iBAAiB,CAAA;IACxC,MAAM,EAAE,iBAAiB,CAAA;IACzB,SAAS,EAAE,iBAAiB,CAAA;IAC5B,KAAK,EAAE,iBAAiB,CAAA;CACzB,CAAA;AAED,oBAAY,mBAAmB,GAAG;IAChC,aAAa,EAAE,iBAAiB,CAAA;IAChC,eAAe,EAAE,iBAAiB,CAAA;IAClC,MAAM,EAAE,iBAAiB,CAAA;CAC1B,CAAA;AAED,oBAAY,sBAAsB,GAAG;IACnC,aAAa,EAAE,iBAAiB,CAAA;IAChC,YAAY,EAAE,iBAAiB,CAAA;IAC/B,YAAY,EAAE,iBAAiB,CAAA;CAChC,CAAA;AAED,MAAM,WAAW,WAAW;IAC1B,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAA;IACzC,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,mBAAmB,CAAA;IAC/C,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,sBAAsB,CAAA;CACtD;AAED,oBAAY,kBAAkB,GAAG;IAC/B,MAAM,EAAE,UAAU,CAAA;IAClB,qBAAqB,EAAE,UAAU,CAAA;IACjC,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,UAAU,CAAA;IACrB,KAAK,EAAE,MAAM,CAAA;CACd,CAAA;AAED,oBAAY,qBAAqB,GAAG;IAClC,aAAa,EAAE,UAAU,CAAA;IACzB,eAAe,EAAE,UAAU,CAAA;IAC3B,MAAM,EAAE,MAAM,CAAA;CACf,CAAA;AAED,oBAAY,wBAAwB,GAAG;IACrC,aAAa,EAAE,UAAU,CAAA;IACzB,YAAY,EAAE,UAAU,CAAA;IACxB,YAAY,EAAE,UAAU,CAAA;CACzB,CAAA;AAED,MAAM,WAAW,WAAW;IAC1B,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,kBAAkB,CAAA;IAC3C,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,qBAAqB,CAAA;IACjD,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,wBAAwB,CAAA;CACxD;AAED,oBAAY,gBAAgB,GAAG,WAAW,CAAC,aAAa,CAAC,CAAA;AAEzD,MAAM,WAAW,kBAAkB,CAAC,CAAC,SAAS,aAAa,GAAG,aAAa;IACzE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;IAChB,SAAS,IAAI,UAAU,CAAA;IACvB,MAAM,IAAI,WAAW,CAAC,CAAC,CAAC,CAAA;CACzB;AAED,8BAAsB,SAAS,CAAC,CAAC,SAAS,aAAa,CAAE,YAAW,kBAAkB,CAAC,CAAC,CAAC;IACvF,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAA;IAChB,QAAQ,CAAC,SAAS,IAAI,UAAU;IAChC,QAAQ,CAAC,MAAM,IAAI,WAAW,CAAC,CAAC,CAAC;gBACrB,IAAI,EAAE,CAAC;CAGpB;AAED,qBAAa,cAAe,SAAQ,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC;aAEhD,MAAM,EAAE,UAAU;aAClB,qBAAqB,EAAE,UAAU;aACjC,MAAM,EAAE,MAAM;aACd,SAAS,EAAE,UAAU;aACrB,KAAK,EAAE,MAAM;gBAJb,MAAM,EAAE,UAAU,EAClB,qBAAqB,EAAE,UAAU,EACjC,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,UAAU,EACrB,KAAK,EAAE,MAAM;WAKjB,eAAe,CAAC,WAAW,EAAE,kBAAkB,GAAG,cAAc;WAKhE,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,GAAG,cAAc;IAWlE,SAAS;IAWT,MAAM,IAAI,gBAAgB;WAUZ,WAAW,CAAC,KAAK,EAAE,UAAU,GAAG,cAAc;CAY7D;AAED,qBAAa,iBAAkB,SAAQ,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC;aAEtD,aAAa,EAAE,UAAU;aACzB,eAAe,EAAE,UAAU;aAC3B,MAAM,EAAE,MAAM;gBAFd,aAAa,EAAE,UAAU,EACzB,eAAe,EAAE,UAAU,EAC3B,MAAM,EAAE,MAAM;WAKlB,eAAe,CAAC,cAAc,EAAE,qBAAqB,GAAG,iBAAiB;WAKzE,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,GAAG,iBAAiB;IASxE,SAAS;IAST,MAAM,IAAI,mBAAmB;WAQf,WAAW,CAAC,KAAK,EAAE,UAAU,GAAG,iBAAiB;CAYhE;AAED,qBAAa,oBAAqB,SAAQ,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC;aAE5D,aAAa,EAAE,UAAU;aACzB,YAAY,EAAE,UAAU;aACxB,YAAY,EAAE,UAAU;gBAFxB,aAAa,EAAE,UAAU,EACzB,YAAY,EAAE,UAAU,EACxB,YAAY,EAAE,UAAU;WAK5B,eAAe,CAAC,iBAAiB,EAAE,wBAAwB,GAAG,oBAAoB;WAKlF,QAAQ,CAAC,QAAQ,EAAE,sBAAsB,GAAG,oBAAoB;IAS9E,SAAS;IAOT,MAAM,IAAI,sBAAsB;WAQlB,WAAW,CAAC,KAAK,EAAE,UAAU,GAAG,oBAAoB;CAYnE;AAED,qBAAa,gBAAgB;WACb,qBAAqB,CAAC,KAAK,EAAE,UAAU,GAAG,SAAS,CAAC,aAAa,CAAC;CAYjF"}