{"version": 3, "file": "abi.js", "sourceRoot": "", "sources": ["../../errors/abi.ts"], "names": [], "mappings": ";;;AAGA,oEAA8E;AAC9E,mDAA4C;AAE5C,uCAAqC;AAKrC,MAAa,2BAA4B,SAAQ,mBAAS;IACxD,YAAY,EAAE,QAAQ,EAAwB;QAC5C,KAAK,CACH;YACE,yCAAyC;YACzC,gFAAgF;SACjF,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,6BAA6B;SACpC,CACF,CAAA;IACH,CAAC;CACF;AAbD,kEAaC;AAOD,MAAa,iCAAkC,SAAQ,mBAAS;IAC9D,YAAY,EAAE,QAAQ,EAAwB;QAC5C,KAAK,CACH;YACE,kHAAkH;YAClH,qGAAqG;SACtG,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,mCAAmC;SAC1C,CACF,CAAA;IACH,CAAC;CACF;AAbD,8EAaC;AAMD,MAAa,+BAAgC,SAAQ,mBAAS;IAC5D,YAAY,EAAE,IAAI,EAAE,IAAI,EAA+B;QACrD,KAAK,CACH;YACE,gBAAgB,IAAI,oBAAoB;YACxC,2DAA2D;SAC5D,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,YAAY,EAAE,CAAC,SAAS,IAAI,KAAK,IAAI,SAAS,CAAC;YAC/C,IAAI,EAAE,iCAAiC;SACxC,CACF,CAAA;IACH,CAAC;CACF;AAbD,0EAaC;AAMD,MAAa,gCAAiC,SAAQ,mBAAS;IAK7D,YAAY,EACV,IAAI,EACJ,MAAM,EACN,IAAI,GACyD;QAC7D,KAAK,CACH,CAAC,gBAAgB,IAAI,2CAA2C,CAAC,CAAC,IAAI,CACpE,IAAI,CACL,EACD;YACE,YAAY,EAAE;gBACZ,YAAY,IAAA,kCAAe,EAAC,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,GAAG;gBAC7D,WAAW,IAAI,KAAK,IAAI,SAAS;aAClC;YACD,IAAI,EAAE,kCAAkC;SACzC,CACF,CAAA;QApBH;;;;;WAAS;QACT;;;;;WAA+B;QAC/B;;;;;WAAY;QAoBV,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AA3BD,4EA2BC;AAKD,MAAa,wBAAyB,SAAQ,mBAAS;IACrD;QACE,KAAK,CAAC,qDAAqD,EAAE;YAC3D,IAAI,EAAE,0BAA0B;SACjC,CAAC,CAAA;IACJ,CAAC;CACF;AAND,4DAMC;AAMD,MAAa,mCAAoC,SAAQ,mBAAS;IAChE,YAAY,EACV,cAAc,EACd,WAAW,EACX,IAAI,GAC0D;QAC9D,KAAK,CACH;YACE,+CAA+C,IAAI,GAAG;YACtD,oBAAoB,cAAc,EAAE;YACpC,iBAAiB,WAAW,EAAE;SAC/B,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,EAAE,IAAI,EAAE,qCAAqC,EAAE,CAChD,CAAA;IACH,CAAC;CACF;AAfD,kFAeC;AAMD,MAAa,iCAAkC,SAAQ,mBAAS;IAC9D,YAAY,EAAE,YAAY,EAAE,KAAK,EAAwC;QACvE,KAAK,CACH,kBAAkB,KAAK,WAAW,IAAA,cAAI,EACpC,KAAK,CACN,wCAAwC,YAAY,IAAI,EACzD,EAAE,IAAI,EAAE,mCAAmC,EAAE,CAC9C,CAAA;IACH,CAAC;CACF;AATD,8EASC;AAMD,MAAa,8BAA+B,SAAQ,mBAAS;IAC3D,YAAY,EACV,cAAc,EACd,WAAW,GACqC;QAChD,KAAK,CACH;YACE,6CAA6C;YAC7C,6BAA6B,cAAc,EAAE;YAC7C,0BAA0B,WAAW,EAAE;SACxC,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,EAAE,IAAI,EAAE,gCAAgC,EAAE,CAC3C,CAAA;IACH,CAAC;CACF;AAdD,wEAcC;AAKD,MAAa,2BAA4B,SAAQ,mBAAS;IACxD,YAAY,SAAiB,EAAE,EAAE,QAAQ,EAAwB;QAC/D,KAAK,CACH;YACE,0CAA0C,SAAS,WAAW,SAAS,4DAA4D;YACnI,0EAA0E;YAC1E,0EAA0E;SAC3E,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,6BAA6B;SACpC,CACF,CAAA;IACH,CAAC;CACF;AAdD,kEAcC;AAKD,MAAa,qBAAsB,SAAQ,mBAAS;IAClD,YACE,SAA8B,EAC9B,EAAE,QAAQ,KAAwC,EAAE;QAEpD,KAAK,CACH;YACE,SAAS,SAAS,CAAC,CAAC,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,mBAAmB;YAC9D,0EAA0E;SAC3E,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,uBAAuB;SAC9B,CACF,CAAA;IACH,CAAC;CACF;AAhBD,sDAgBC;AAMD,MAAa,8BAA+B,SAAQ,mBAAS;IAG3D,YAAY,SAAc,EAAE,EAAE,QAAQ,EAAwB;QAC5D,KAAK,CACH;YACE,4BAA4B,SAAS,qBAAqB;YAC1D,0EAA0E;YAC1E,sFAAsF,SAAS,GAAG;SACnG,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,gCAAgC;SACvC,CACF,CAAA;QAbH;;;;;WAAc;QAcZ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;IAC5B,CAAC;CACF;AAjBD,wEAiBC;AAMD,MAAa,iCAAkC,SAAQ,mBAAS;IAC9D,YAAY,EAAE,QAAQ,EAAwB;QAC5C,KAAK,CAAC,mDAAmD,EAAE;YACzD,QAAQ;YACR,IAAI,EAAE,mCAAmC;SAC1C,CAAC,CAAA;IACJ,CAAC;CACF;AAPD,8EAOC;AAMD,MAAa,8BAA+B,SAAQ,mBAAS;IAC3D,YAAY,SAAc,EAAE,EAAE,QAAQ,EAAwB;QAC5D,KAAK,CACH;YACE,4BAA4B,SAAS,qBAAqB;YAC1D,0EAA0E;YAC1E,8EAA8E,SAAS,GAAG;SAC3F,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,gCAAgC;SACvC,CACF,CAAA;IACH,CAAC;CACF;AAdD,wEAcC;AAKD,MAAa,qBAAsB,SAAQ,mBAAS;IAClD,YACE,SAA8B,EAC9B,EAAE,QAAQ,KAAwC,EAAE;QAEpD,KAAK,CACH;YACE,SAAS,SAAS,CAAC,CAAC,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,mBAAmB;YAC9D,0EAA0E;SAC3E,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,uBAAuB;SAC9B,CACF,CAAA;IACH,CAAC;CACF;AAhBD,sDAgBC;AAKD,MAAa,wBAAyB,SAAQ,mBAAS;IACrD,YACE,YAAiC,EACjC,EAAE,QAAQ,KAAwC,EAAE;QAEpD,KAAK,CACH;YACE,YAAY,YAAY,CAAC,CAAC,CAAC,IAAI,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,mBAAmB;YACvE,6EAA6E;SAC9E,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,0BAA0B;SACjC,CACF,CAAA;IACH,CAAC;CACF;AAhBD,4DAgBC;AAMD,MAAa,+BAAgC,SAAQ,mBAAS;IAC5D,YAAY,YAAoB,EAAE,EAAE,QAAQ,EAAwB;QAClE,KAAK,CACH;YACE,aAAa,YAAY,4CAA4C;YACrE,6EAA6E;YAC7E,6EAA6E;SAC9E,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,iCAAiC;SACxC,CACF,CAAA;IACH,CAAC;CACF;AAdD,0EAcC;AAMD,MAAa,iCAAkC,SAAQ,mBAAS;IAC9D,YAAY,SAAc,EAAE,EAAE,QAAQ,EAAwB;QAC5D,KAAK,CACH;YACE,+BAA+B,SAAS,qBAAqB;YAC7D,6EAA6E;YAC7E,8EAA8E,SAAS,GAAG;SAC3F,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,QAAQ;YACR,IAAI,EAAE,mCAAmC;SAC1C,CACF,CAAA;IACH,CAAC;CACF;AAdD,8EAcC;AAKD,MAAa,qBAAsB,SAAQ,mBAAS;IAClD,YACE,CAAyC,EACzC,CAAyC;QAEzC,KAAK,CAAC,gDAAgD,EAAE;YACtD,YAAY,EAAE;gBACZ,KAAK,CAAC,CAAC,IAAI,WAAW,IAAA,gCAAa,EAAC,CAAC,CAAC,OAAO,CAAC,SAAS;gBACvD,KAAK,CAAC,CAAC,IAAI,WAAW,IAAA,gCAAa,EAAC,CAAC,CAAC,OAAO,CAAC,IAAI;gBAClD,EAAE;gBACF,wEAAwE;gBACxE,+CAA+C;aAChD;YACD,IAAI,EAAE,uBAAuB;SAC9B,CAAC,CAAA;IACJ,CAAC;CACF;AAhBD,sDAgBC;AAKD,MAAa,sBAAuB,SAAQ,mBAAS;IACnD,YAAY,EACV,YAAY,EACZ,SAAS,GACmC;QAC5C,KAAK,CAAC,iBAAiB,YAAY,cAAc,SAAS,GAAG,EAAE;YAC7D,IAAI,EAAE,wBAAwB;SAC/B,CAAC,CAAA;IACJ,CAAC;CACF;AATD,wDASC;AAKD,MAAa,qBAAsB,SAAQ,mBAAS;IAMlD,YAAY,EACV,OAAO,EACP,IAAI,EACJ,MAAM,EACN,IAAI,GAML;QACC,KAAK,CACH;YACE,gBAAgB,IAAI,uDAAuD;SAC5E,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,YAAY,EAAE;gBACZ,YAAY,IAAA,kCAAe,EAAC,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,GAAG;gBAC7D,WAAW,IAAI,KAAK,IAAI,SAAS;aAClC;YACD,IAAI,EAAE,uBAAuB;SAC9B,CACF,CAAA;QA3BH;;;;;WAAiB;QACjB;;;;;WAAS;QACT;;;;;WAA+B;QAC/B;;;;;WAAY;QA0BV,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AAnCD,sDAmCC;AAKD,MAAa,uBAAwB,SAAQ,mBAAS;IAGpD,YAAY,EACV,OAAO,EACP,KAAK,GAIN;QACC,KAAK,CACH;YACE,+CACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EACpC,cAAc,IAAA,gCAAa,EAAC,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,IAAI;SAChE,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,EAAE,IAAI,EAAE,yBAAyB,EAAE,CACpC,CAAA;QAhBH;;;;;WAAiB;QAkBf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;IACxB,CAAC;CACF;AArBD,0DAqBC;AAKD,MAAa,2BAA4B,SAAQ,mBAAS;IACxD,YAAY,IAAY,EAAE,EAAE,QAAQ,EAAwB;QAC1D,KAAK,CACH;YACE,SAAS,IAAI,iCAAiC;YAC9C,kCAAkC;SACnC,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,EAAE,QAAQ,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAC7C,CAAA;IACH,CAAC;CACF;AAVD,kEAUC;AAKD,MAAa,2BAA4B,SAAQ,mBAAS;IACxD,YAAY,IAAY,EAAE,EAAE,QAAQ,EAAwB;QAC1D,KAAK,CACH;YACE,SAAS,IAAI,iCAAiC;YAC9C,kCAAkC;SACnC,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,EAAE,QAAQ,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAC7C,CAAA;IACH,CAAC;CACF;AAVD,kEAUC;AAKD,MAAa,iBAAkB,SAAQ,mBAAS;IAC9C,YAAY,KAAc;QACxB,KAAK,CAAC,CAAC,UAAU,KAAK,yBAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3D,IAAI,EAAE,mBAAmB;SAC1B,CAAC,CAAA;IACJ,CAAC;CACF;AAND,8CAMC;AAKD,MAAa,0BAA2B,SAAQ,mBAAS;IACvD,YAAY,IAAY;QACtB,KAAK,CACH;YACE,IAAI,IAAI,mCAAmC;YAC3C,2CAA2C;SAC5C,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,EAAE,IAAI,EAAE,4BAA4B,EAAE,CACvC,CAAA;IACH,CAAC;CACF;AAVD,gEAUC;AAKD,MAAa,wBAAyB,SAAQ,mBAAS;IACrD,YAAY,IAAa;QACvB,KAAK,CAAC,SAAS,IAAI,yCAAyC,EAAE;YAC5D,IAAI,EAAE,0BAA0B;SACjC,CAAC,CAAA;IACJ,CAAC;CACF;AAND,4DAMC"}