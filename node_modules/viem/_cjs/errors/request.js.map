{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../../errors/request.ts"], "names": [], "mappings": ";;;AAAA,wDAAiD;AAEjD,uCAAqC;AACrC,yCAAmC;AAKnC,MAAa,gBAAiB,SAAQ,mBAAS;IAM7C,YAAY,EACV,IAAI,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,GAQJ;QACC,KAAK,CAAC,sBAAsB,EAAE;YAC5B,KAAK;YACL,OAAO;YACP,YAAY,EAAE;gBACZ,MAAM,IAAI,WAAW,MAAM,EAAE;gBAC7B,QAAQ,IAAA,iBAAM,EAAC,GAAG,CAAC,EAAE;gBACrB,IAAI,IAAI,iBAAiB,IAAA,wBAAS,EAAC,IAAI,CAAC,EAAE;aAC3C,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,kBAAkB;SACzB,CAAC,CAAA;QA7BJ;;;;;WAAwE;QACxE;;;;;WAA6B;QAC7B;;;;;WAA2B;QAC3B;;;;;WAAW;QA2BT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;IAChB,CAAC;CACF;AApCD,4CAoCC;AAKD,MAAa,qBAAsB,SAAQ,mBAAS;IAClD,YAAY,EACV,IAAI,EACJ,KAAK,EACL,OAAO,EACP,GAAG,GAMJ;QACC,KAAK,CAAC,2BAA2B,EAAE;YACjC,KAAK;YACL,OAAO;YACP,YAAY,EAAE;gBACZ,QAAQ,IAAA,iBAAM,EAAC,GAAG,CAAC,EAAE;gBACrB,IAAI,IAAI,iBAAiB,IAAA,wBAAS,EAAC,IAAI,CAAC,EAAE;aAC3C,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,uBAAuB;SAC9B,CAAC,CAAA;IACJ,CAAC;CACF;AAtBD,sDAsBC;AAKD,MAAa,eAAgB,SAAQ,mBAAS;IAI5C,YAAY,EACV,IAAI,EACJ,KAAK,EACL,GAAG,GAKJ;QACC,KAAK,CAAC,qBAAqB,EAAE;YAC3B,KAAK,EAAE,KAAY;YACnB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,YAAY,EAAE,CAAC,QAAQ,IAAA,iBAAM,EAAC,GAAG,CAAC,EAAE,EAAE,iBAAiB,IAAA,wBAAS,EAAC,IAAI,CAAC,EAAE,CAAC;YACzE,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAA;QAjBJ;;;;;WAAY;QACZ;;;;;WAAc;QAiBZ,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAA;IACxB,CAAC;CACF;AAtBD,0CAsBC;AAKD,MAAa,iBAAkB,SAAQ,mBAAS;IAC9C,YAAY,EACV,GAAG,MAGD,EAAE;QACJ,KAAK,CAAC,6BAA6B,EAAE;YACnC,YAAY,EAAE,CAAC,GAAG,IAAI,QAAQ,IAAA,iBAAM,EAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAa;YACxE,IAAI,EAAE,mBAAmB;SAC1B,CAAC,CAAA;IACJ,CAAC;CACF;AAXD,8CAWC;AAKD,MAAa,YAAa,SAAQ,mBAAS;IACzC,YAAY,EACV,IAAI,EACJ,GAAG,GAIJ;QACC,KAAK,CAAC,uCAAuC,EAAE;YAC7C,OAAO,EAAE,wBAAwB;YACjC,YAAY,EAAE,CAAC,QAAQ,IAAA,iBAAM,EAAC,GAAG,CAAC,EAAE,EAAE,iBAAiB,IAAA,wBAAS,EAAC,IAAI,CAAC,EAAE,CAAC;YACzE,IAAI,EAAE,cAAc;SACrB,CAAC,CAAA;IACJ,CAAC;CACF;AAdD,oCAcC"}