{"version": 3, "file": "transaction.js", "sourceRoot": "", "sources": ["../../errors/transaction.ts"], "names": [], "mappings": ";;;AAWA,kCAaC;AAlBD,iEAA0D;AAC1D,+DAAwD;AAExD,uCAAqC;AAErC,SAAgB,WAAW,CACzB,IAA4E;IAE5E,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;SACjC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACpB,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,KAAK;YAAE,OAAO,IAAI,CAAA;QACvD,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IACrB,CAAC,CAAC;SACD,MAAM,CAAC,OAAO,CAAuB,CAAA;IACxC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;IAC9E,OAAO,OAAO;SACX,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC;SACvE,IAAI,CAAC,IAAI,CAAC,CAAA;AACf,CAAC;AAKD,MAAa,gBAAiB,SAAQ,mBAAS;IAC7C;QACE,KAAK,CACH;YACE,+EAA+E;YAC/E,wGAAwG;SACzG,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAC7B,CAAA;IACH,CAAC;CACF;AAVD,4CAUC;AAKD,MAAa,mBAAoB,SAAQ,mBAAS;IAChD,YAAY,EAAE,CAAC,EAAiB;QAC9B,KAAK,CAAC,wBAAwB,CAAC,uBAAuB,EAAE;YACtD,IAAI,EAAE,qBAAqB;SAC5B,CAAC,CAAA;IACJ,CAAC;CACF;AAND,kDAMC;AAMD,MAAa,mCAAoC,SAAQ,mBAAS;IAChE,YAAY,EAAE,WAAW,EAA4C;QACnE,KAAK,CAAC,4DAA4D,EAAE;YAClE,YAAY,EAAE;gBACZ,uBAAuB;gBACvB,GAAG;gBACH,WAAW,CAAC,WAAW,CAAC;gBACxB,GAAG;gBACH,EAAE;gBACF,oCAAoC;gBACpC,mCAAmC;gBACnC,mDAAmD;gBACnD,8DAA8D;gBAC9D,+EAA+E;gBAC/E,wDAAwD;gBACxD,wCAAwC;aACzC;YACD,IAAI,EAAE,qCAAqC;SAC5C,CAAC,CAAA;IACJ,CAAC;CACF;AApBD,kFAoBC;AAMD,MAAa,qCAAsC,SAAQ,mBAAS;IAGlE,YAAY,EAAE,cAAc,EAA2B;QACrD,KAAK,CAAC,gCAAgC,cAAc,eAAe,EAAE;YACnE,IAAI,EAAE,kCAAkC;SACzC,CAAC,CAAA;QALJ;;;;;WAAmB;QAOjB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;IACtC,CAAC;CACF;AAVD,sFAUC;AAMD,MAAa,iCAAkC,SAAQ,mBAAS;IAI9D,YAAY,EACV,UAAU,EACV,qBAAqB,EACrB,IAAI,GAKL;QACC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;aACvC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;aACvE,MAAM,CAAC,OAAO,CAAC,CAAA;QAClB,KAAK,CAAC,2CAA2C,IAAI,iBAAiB,EAAE;YACtE,YAAY,EAAE;gBACZ,4BAA4B,qBAAqB,GAAG;gBACpD,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,uBAAuB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;aACtE,CAAC,MAAM,CAAC,OAAO,CAAC;YACjB,IAAI,EAAE,mCAAmC;SAC1C,CAAC,CAAA;QArBJ;;;;;WAA0B;QAC1B;;;;;WAAqB;QAsBnB,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAA;QAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AA3BD,8EA2BC;AAKD,MAAa,0BAA2B,SAAQ,mBAAS;IACvD,YAAY,EAAE,UAAU,EAAuB;QAC7C,KAAK,CACH,yBAAyB,UAAU,wCAAwC,IAAI,CAAC,KAAK,CACnF,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAC5B,SAAS,EACV,EAAE,IAAI,EAAE,4BAA4B,EAAE,CACvC,CAAA;IACH,CAAC;CACF;AATD,gEASC;AAKD,MAAa,yBAA0B,SAAQ,mBAAS;IAGtD,YACE,KAAgB,EAChB,EACE,OAAO,EACP,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,GAAG,EACH,QAAQ,EACR,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,EAAE,EACF,KAAK,GAKN;QAED,MAAM,UAAU,GAAG,WAAW,CAAC;YAC7B,KAAK,EAAE,KAAK,IAAI,GAAG,KAAK,EAAE,IAAI,SAAS,KAAK,EAAE,EAAE,GAAG;YACnD,IAAI,EAAE,OAAO,EAAE,OAAO;YACtB,EAAE;YACF,KAAK,EACH,OAAO,KAAK,KAAK,WAAW;gBAC5B,GAAG,IAAA,4BAAW,EAAC,KAAK,CAAC,IAAI,KAAK,EAAE,cAAc,EAAE,MAAM,IAAI,KAAK,EAAE;YACnE,IAAI;YACJ,GAAG;YACH,QAAQ,EACN,OAAO,QAAQ,KAAK,WAAW,IAAI,GAAG,IAAA,0BAAU,EAAC,QAAQ,CAAC,OAAO;YACnE,YAAY,EACV,OAAO,YAAY,KAAK,WAAW;gBACnC,GAAG,IAAA,0BAAU,EAAC,YAAY,CAAC,OAAO;YACpC,oBAAoB,EAClB,OAAO,oBAAoB,KAAK,WAAW;gBAC3C,GAAG,IAAA,0BAAU,EAAC,oBAAoB,CAAC,OAAO;YAC5C,KAAK;SACN,CAAC,CAAA;QAEF,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE;YACxB,KAAK;YACL,QAAQ;YACR,YAAY,EAAE;gBACZ,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3D,oBAAoB;gBACpB,UAAU;aACX,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,2BAA2B;SAClC,CAAC,CAAA;QAnDK;;;;;WAAgB;QAoDvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;CACF;AAvDD,8DAuDC;AAKD,MAAa,wBAAyB,SAAQ,mBAAS;IACrD,YAAY,EACV,SAAS,EACT,WAAW,EACX,QAAQ,EACR,IAAI,EACJ,KAAK,GAON;QACC,IAAI,UAAU,GAAG,aAAa,CAAA;QAC9B,IAAI,QAAQ,IAAI,KAAK,KAAK,SAAS;YACjC,UAAU,GAAG,8BAA8B,QAAQ,eAAe,KAAK,GAAG,CAAA;QAC5E,IAAI,SAAS,IAAI,KAAK,KAAK,SAAS;YAClC,UAAU,GAAG,8BAA8B,SAAS,eAAe,KAAK,GAAG,CAAA;QAC7E,IAAI,WAAW,IAAI,KAAK,KAAK,SAAS;YACpC,UAAU,GAAG,gCAAgC,WAAW,eAAe,KAAK,GAAG,CAAA;QACjF,IAAI,IAAI;YAAE,UAAU,GAAG,0BAA0B,IAAI,GAAG,CAAA;QACxD,KAAK,CAAC,GAAG,UAAU,sBAAsB,EAAE;YACzC,IAAI,EAAE,0BAA0B;SACjC,CAAC,CAAA;IACJ,CAAC;CACF;AA1BD,4DA0BC;AAMD,MAAa,+BAAgC,SAAQ,mBAAS;IAC5D,YAAY,EAAE,IAAI,EAAkB;QAClC,KAAK,CACH,kCAAkC,IAAI,4EAA4E,EAClH;YACE,IAAI,EAAE,iCAAiC;SACxC,CACF,CAAA;IACH,CAAC;CACF;AATD,0EASC;AAMD,MAAa,qCAAsC,SAAQ,mBAAS;IAClE,YAAY,EAAE,IAAI,EAAkB;QAClC,KAAK,CACH,sDAAsD,IAAI,oBAAoB,EAC9E,EAAE,IAAI,EAAE,uCAAuC,EAAE,CAClD,CAAA;IACH,CAAC;CACF;AAPD,sFAOC"}