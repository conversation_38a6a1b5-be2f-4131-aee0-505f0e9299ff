{"version": 3, "file": "typedData.js", "sourceRoot": "", "sources": ["../../errors/typedData.ts"], "names": [], "mappings": ";;;AAEA,wDAAiD;AACjD,uCAAqC;AAKrC,MAAa,kBAAmB,SAAQ,mBAAS;IAC/C,YAAY,EAAE,MAAM,EAAuB;QACzC,KAAK,CAAC,mBAAmB,IAAA,wBAAS,EAAC,MAAM,CAAC,IAAI,EAAE;YAC9C,YAAY,EAAE,CAAC,iCAAiC,CAAC;SAClD,CAAC,CAAA;IACJ,CAAC;CACF;AAND,gDAMC;AAKD,MAAa,uBAAwB,SAAQ,mBAAS;IACpD,YAAY,EACV,WAAW,EACX,KAAK,GAC+D;QACpE,KAAK,CACH,0BAA0B,WAAW,uBAAuB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EACnG;YACE,QAAQ,EAAE,uDAAuD;YACjE,YAAY,EAAE,CAAC,kDAAkD,CAAC;SACnE,CACF,CAAA;IACH,CAAC;CACF;AAbD,0DAaC;AAKD,MAAa,sBAAuB,SAAQ,mBAAS;IACnD,YAAY,EAAE,IAAI,EAAoB;QACpC,KAAK,CAAC,gBAAgB,IAAI,eAAe,EAAE;YACzC,YAAY,EAAE,CAAC,0CAA0C,CAAC;YAC1D,IAAI,EAAE,wBAAwB;SAC/B,CAAC,CAAA;IACJ,CAAC;CACF;AAPD,wDAOC"}