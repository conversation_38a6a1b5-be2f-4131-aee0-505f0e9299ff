{"version": 3, "file": "rpc.js", "sourceRoot": "", "sources": ["../../errors/rpc.ts"], "names": [], "mappings": ";;;AACA,uCAAqC;AACrC,6CAA8C;AAE9C,MAAM,gBAAgB,GAAG,CAAC,CAAC,CAAA;AAgC3B,MAAa,QAA8C,SAAQ,mBAAS;IAG1E,YACE,KAAY,EACZ,EACE,IAAI,EACJ,QAAQ,EACR,YAAY,EACZ,IAAI,EACJ,YAAY,GACW;QAEzB,KAAK,CAAC,YAAY,EAAE;YAClB,KAAK;YACL,QAAQ;YACR,YAAY,EACV,YAAY,IAAK,KAAqC,EAAE,YAAY;YACtE,IAAI,EAAE,IAAI,IAAI,UAAU;SACzB,CAAC,CAAA;QAlBJ;;;;;WAA2B;QAmBzB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAA;QAC9B,IAAI,CAAC,IAAI,GAAG,CACV,KAAK,YAAY,4BAAe,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,gBAAgB,CAAC,CAClE,CAAA;IACZ,CAAC;CACF;AAzBD,4BAyBC;AAyBD,MAAa,gBAEX,SAAQ,QAA8B;IAGtC,YACE,KAAY,EACZ,OAIC;QAED,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;QAVvB;;;;;WAAoB;QAYlB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;IAC1B,CAAC;CACF;AAjBD,4CAiBC;AAWD,MAAa,aAAc,SAAQ,QAAQ;IAGzC,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,IAAI,EAAE,eAAe;YACrB,YAAY,EACV,uGAAuG;SAC1G,CAAC,CAAA;IACJ,CAAC;;AAVH,sCAWC;AAVQ;;;;WAAO,CAAC,KAAc;GAAA;AAqB/B,MAAa,sBAAuB,SAAQ,QAAQ;IAGlD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,sBAAsB,CAAC,IAAI;YACjC,IAAI,EAAE,wBAAwB;YAC9B,YAAY,EAAE,qCAAqC;SACpD,CAAC,CAAA;IACJ,CAAC;;AATH,wDAUC;AATQ;;;;WAAO,CAAC,KAAc;GAAA;AAoB/B,MAAa,sBAAuB,SAAQ,QAAQ;IAGlD,YAAY,KAAY,EAAE,EAAE,MAAM,KAA0B,EAAE;QAC5D,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,sBAAsB,CAAC,IAAI;YACjC,IAAI,EAAE,wBAAwB;YAC9B,YAAY,EAAE,aAAa,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,qCAAqC;SAC7F,CAAC,CAAA;IACJ,CAAC;;AATH,wDAUC;AATQ;;;;WAAO,CAAC,KAAc;GAAA;AAoB/B,MAAa,qBAAsB,SAAQ,QAAQ;IAGjD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,qBAAqB,CAAC,IAAI;YAChC,IAAI,EAAE,uBAAuB;YAC7B,YAAY,EAAE;gBACZ,qDAAqD;gBACrD,wDAAwD;aACzD,CAAC,IAAI,CAAC,IAAI,CAAC;SACb,CAAC,CAAA;IACJ,CAAC;;AAZH,sDAaC;AAZQ;;;;WAAO,CAAC,KAAc;GAAA;AAuB/B,MAAa,gBAAiB,SAAQ,QAAQ;IAG5C,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,gBAAgB,CAAC,IAAI;YAC3B,IAAI,EAAE,kBAAkB;YACxB,YAAY,EAAE,iCAAiC;SAChD,CAAC,CAAA;IACJ,CAAC;;AATH,4CAUC;AATQ;;;;WAAO,CAAC,KAAc;GAAA;AAoB/B,MAAa,oBAAqB,SAAQ,QAAQ;IAGhD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,oBAAoB,CAAC,IAAI;YAC/B,IAAI,EAAE,sBAAsB;YAC5B,YAAY,EAAE;gBACZ,gCAAgC;gBAChC,wDAAwD;aACzD,CAAC,IAAI,CAAC,IAAI,CAAC;SACb,CAAC,CAAA;IACJ,CAAC;;AAZH,oDAaC;AAZQ;;;;WAAO,CAAC,KAAc;GAAA;AAuB/B,MAAa,wBAAyB,SAAQ,QAAQ;IAIpD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,wBAAwB,CAAC,IAAI;YACnC,IAAI,EAAE,0BAA0B;YAChC,YAAY,EAAE,+BAA+B;SAC9C,CAAC,CAAA;QARK;;;;mBAAO,0BAA0B;WAAA;IAS1C,CAAC;;AAVH,4DAWC;AATQ;;;;WAAO,CAAC,KAAc;EAAlB,CAAkB;AAoB/B,MAAa,2BAA4B,SAAQ,QAAQ;IAGvD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,2BAA2B,CAAC,IAAI;YACtC,IAAI,EAAE,6BAA6B;YACnC,YAAY,EAAE,mCAAmC;SAClD,CAAC,CAAA;IACJ,CAAC;;AATH,kEAUC;AATQ;;;;WAAO,CAAC,KAAc;GAAA;AAoB/B,MAAa,2BAA4B,SAAQ,QAAQ;IAGvD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,2BAA2B,CAAC,IAAI;YACtC,IAAI,EAAE,6BAA6B;YACnC,YAAY,EAAE,8BAA8B;SAC7C,CAAC,CAAA;IACJ,CAAC;;AATH,kEAUC;AATQ;;;;WAAO,CAAC,KAAc;GAAA;AAoB/B,MAAa,0BAA2B,SAAQ,QAAQ;IAGtD,YAAY,KAAY,EAAE,EAAE,MAAM,KAA0B,EAAE;QAC5D,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,0BAA0B,CAAC,IAAI;YACrC,IAAI,EAAE,4BAA4B;YAClC,YAAY,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,oBAAoB;SACxE,CAAC,CAAA;IACJ,CAAC;;AATH,gEAUC;AATQ;;;;WAAO,CAAC,KAAc;GAAA;AAoB/B,MAAa,qBAAsB,SAAQ,QAAQ;IAGjD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,qBAAqB,CAAC,IAAI;YAChC,IAAI,EAAE,uBAAuB;YAC7B,YAAY,EAAE,gCAAgC;SAC/C,CAAC,CAAA;IACJ,CAAC;;AATH,sDAUC;AATQ;;;;WAAO,CAAC,KAAc;GAAA;AAqB/B,MAAa,8BAA+B,SAAQ,QAAQ;IAG1D,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,8BAA8B,CAAC,IAAI;YACzC,IAAI,EAAE,gCAAgC;YACtC,YAAY,EAAE,gDAAgD;SAC/D,CAAC,CAAA;IACJ,CAAC;;AATH,wEAUC;AATQ;;;;WAAO,CAAC,KAAc;GAAA;AAoB/B,MAAa,wBAAyB,SAAQ,gBAAgB;IAG5D,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,wBAAwB,CAAC,IAAI;YACnC,IAAI,EAAE,0BAA0B;YAChC,YAAY,EAAE,4BAA4B;SAC3C,CAAC,CAAA;IACJ,CAAC;;AATH,4DAUC;AATQ;;;;WAAO,IAAa;GAAA;AAoB7B,MAAa,yBAA0B,SAAQ,gBAAgB;IAG7D,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,yBAAyB,CAAC,IAAI;YACpC,IAAI,EAAE,2BAA2B;YACjC,YAAY,EACV,0EAA0E;SAC7E,CAAC,CAAA;IACJ,CAAC;;AAVH,8DAWC;AAVQ;;;;WAAO,IAAa;GAAA;AAsB7B,MAAa,8BAA+B,SAAQ,gBAAgB;IAGlE,YAAY,KAAY,EAAE,EAAE,MAAM,KAA0B,EAAE;QAC5D,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,8BAA8B,CAAC,IAAI;YACzC,IAAI,EAAE,gCAAgC;YACtC,YAAY,EAAE,qDAAqD,MAAM,CAAC,CAAC,CAAC,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;SACpG,CAAC,CAAA;IACJ,CAAC;;AATH,wEAUC;AATQ;;;;WAAO,IAAa;GAAA;AAoB7B,MAAa,yBAA0B,SAAQ,gBAAgB;IAG7D,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,yBAAyB,CAAC,IAAI;YACpC,IAAI,EAAE,2BAA2B;YACjC,YAAY,EAAE,+CAA+C;SAC9D,CAAC,CAAA;IACJ,CAAC;;AATH,8DAUC;AATQ;;;;WAAO,IAAa;GAAA;AAoB7B,MAAa,sBAAuB,SAAQ,gBAAgB;IAG1D,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,sBAAsB,CAAC,IAAI;YACjC,IAAI,EAAE,wBAAwB;YAC9B,YAAY,EAAE,uDAAuD;SACtE,CAAC,CAAA;IACJ,CAAC;;AATH,wDAUC;AATQ;;;;WAAO,IAAa;GAAA;AAoB7B,MAAa,gBAAiB,SAAQ,gBAAgB;IAGpD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,gBAAgB,CAAC,IAAI;YAC3B,IAAI,EAAE,kBAAkB;YACxB,YAAY,EAAE,oDAAoD;SACnE,CAAC,CAAA;IACJ,CAAC;;AATH,4CAUC;AATQ;;;;WAAO,IAAa;GAAA;AAqB7B,MAAa,qCAAsC,SAAQ,gBAAgB;IAGzE,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,qCAAqC,CAAC,IAAI;YAChD,IAAI,EAAE,uCAAuC;YAC7C,YAAY,EACV,4EAA4E;SAC/E,CAAC,CAAA;IACJ,CAAC;;AAVH,sFAWC;AAVQ;;;;WAAO,IAAa;GAAA;AAqB7B,MAAa,uBAAwB,SAAQ,gBAAgB;IAG3D,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,uBAAuB,CAAC,IAAI;YAClC,IAAI,EAAE,yBAAyB;YAC/B,YAAY,EAAE,sDAAsD;SACrE,CAAC,CAAA;IACJ,CAAC;;AATH,0DAUC;AATQ;;;;WAAO,IAAa;GAAA;AAoB7B,MAAa,gBAAiB,SAAQ,gBAAgB;IAGpD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,gBAAgB,CAAC,IAAI;YAC3B,IAAI,EAAE,kBAAkB;YACxB,YAAY,EAAE,mDAAmD;SAClE,CAAC,CAAA;IACJ,CAAC;;AATH,4CAUC;AATQ;;;;WAAO,IAAa;GAAA;AAoB7B,MAAa,oBAAqB,SAAQ,gBAAgB;IAGxD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,oBAAoB,CAAC,IAAI;YAC/B,IAAI,EAAE,sBAAsB;YAC5B,YAAY,EAAE,oDAAoD;SACnE,CAAC,CAAA;IACJ,CAAC;;AATH,oDAUC;AATQ;;;;WAAO,IAAa;GAAA;AAoB7B,MAAa,mBAAoB,SAAQ,gBAAgB;IAGvD,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,mBAAmB,CAAC,IAAI;YAC9B,IAAI,EAAE,qBAAqB;YAC3B,YAAY,EAAE,yDAAyD;SACxE,CAAC,CAAA;IACJ,CAAC;;AATH,kDAUC;AATQ;;;;WAAO,IAAa;GAAA;AAqB7B,MAAa,qCAAsC,SAAQ,gBAAgB;IAGzE,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,qCAAqC,CAAC,IAAI;YAChD,IAAI,EAAE,uCAAuC;YAC7C,YAAY,EACV,uFAAuF;SAC1F,CAAC,CAAA;IACJ,CAAC;;AAVH,sFAWC;AAVQ;;;;WAAO,IAAa;GAAA;AAqB7B,MAAa,0BAA2B,SAAQ,gBAAgB;IAG9D,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,0BAA0B,CAAC,IAAI;YACrC,IAAI,EAAE,4BAA4B;YAClC,YAAY,EACV,2EAA2E;SAC9E,CAAC,CAAA;IACJ,CAAC;;AAVH,gEAWC;AAVQ;;;;WAAO,IAAa;GAAA;AAkB7B,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,KAAY;QACtB,KAAK,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,iBAAiB;YACvB,YAAY,EAAE,gCAAgC;SAC/C,CAAC,CAAA;IACJ,CAAC;CACF;AAPD,0CAOC"}