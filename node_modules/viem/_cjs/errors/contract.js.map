{"version": 3, "file": "contract.js", "sourceRoot": "", "sources": ["../../errors/contract.ts"], "names": [], "mappings": ";;;AAEA,uEAAgE;AAEhE,0DAAuD;AAGvD,4EAG0C;AAC1C,oEAA6D;AAC7D,oFAA6E;AAC7E,8DAAuD;AACvD,iEAA0D;AAC1D,+DAAwD;AAExD,qCAAyD;AACzD,uCAAqC;AACrC,yDAAwD;AACxD,qDAA8C;AAC9C,yCAA+C;AAK/C,MAAa,kBAAmB,SAAQ,mBAAS;IAG/C,YACE,KAAgB,EAChB,EACE,OAAO,EAAE,QAAQ,EACjB,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,GAAG,EACH,QAAQ,EACR,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,EAAE,EACF,KAAK,EACL,aAAa,GAId;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAA,8BAAY,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAC7D,IAAI,UAAU,GAAG,IAAA,4BAAW,EAAC;YAC3B,IAAI,EAAE,OAAO,EAAE,OAAO;YACtB,EAAE;YACF,KAAK,EACH,OAAO,KAAK,KAAK,WAAW;gBAC5B,GAAG,IAAA,4BAAW,EAAC,KAAK,CAAC,IAAI,KAAK,EAAE,cAAc,EAAE,MAAM,IAAI,KAAK,EAAE;YACnE,IAAI;YACJ,GAAG;YACH,QAAQ,EACN,OAAO,QAAQ,KAAK,WAAW,IAAI,GAAG,IAAA,0BAAU,EAAC,QAAQ,CAAC,OAAO;YACnE,YAAY,EACV,OAAO,YAAY,KAAK,WAAW;gBACnC,GAAG,IAAA,0BAAU,EAAC,YAAY,CAAC,OAAO;YACpC,oBAAoB,EAClB,OAAO,oBAAoB,KAAK,WAAW;gBAC3C,GAAG,IAAA,0BAAU,EAAC,oBAAoB,CAAC,OAAO;YAC5C,KAAK;SACN,CAAC,CAAA;QAEF,IAAI,aAAa,EAAE,CAAC;YAClB,UAAU,IAAI,KAAK,IAAA,sCAAmB,EAAC,aAAa,CAAC,EAAE,CAAA;QACzD,CAAC;QAED,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE;YACxB,KAAK;YACL,QAAQ;YACR,YAAY,EAAE;gBACZ,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3D,qBAAqB;gBACrB,UAAU;aACX,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,oBAAoB;SAC3B,CAAC,CAAA;QAvDK;;;;;WAAgB;QAwDvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;CACF;AA3DD,gDA2DC;AAMD,MAAa,8BAA+B,SAAQ,mBAAS;IAS3D,YACE,KAAgB,EAChB,EACE,GAAG,EACH,IAAI,EACJ,eAAe,EACf,QAAQ,EACR,YAAY,EACZ,MAAM,GAQP;QAED,MAAM,OAAO,GAAG,IAAA,0BAAU,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAA;QAC7D,MAAM,aAAa,GAAG,OAAO;YAC3B,CAAC,CAAC,IAAA,gDAAqB,EAAC;gBACpB,OAAO;gBACP,IAAI;gBACJ,mBAAmB,EAAE,KAAK;gBAC1B,WAAW,EAAE,KAAK;aACnB,CAAC;YACJ,CAAC,CAAC,SAAS,CAAA;QACb,MAAM,kBAAkB,GAAG,OAAO;YAChC,CAAC,CAAC,IAAA,gCAAa,EAAC,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;YAC/C,CAAC,CAAC,SAAS,CAAA;QAEb,MAAM,UAAU,GAAG,IAAA,4BAAW,EAAC;YAC7B,OAAO,EAAE,eAAe,IAAI,IAAA,6BAAkB,EAAC,eAAe,CAAC;YAC/D,QAAQ,EAAE,kBAAkB;YAC5B,IAAI,EACF,aAAa;gBACb,aAAa,KAAK,IAAI;gBACtB,GAAG,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;qBAC5C,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;qBACd,IAAI,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE;YAC/B,MAAM;SACP,CAAC,CAAA;QAEF,KAAK,CACH,KAAK,CAAC,YAAY;YAChB,oEAAoE,YAAY,IAAI,EACtF;YACE,KAAK;YACL,QAAQ;YACR,YAAY,EAAE;gBACZ,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3D,UAAU,IAAI,gBAAgB;gBAC9B,UAAU;aACX,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,gCAAgC;SACvC,CACF,CAAA;QAhEH;;;;;WAAQ;QACR;;;;;WAA4B;QACnB;;;;;WAAgB;QACzB;;;;;WAAqC;QACrC;;;;;WAAkC;QAClC;;;;;WAAoB;QACpB;;;;;WAA4B;QA2D1B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAA;QACtC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;CACF;AAzED,wEAyEC;AAMD,MAAa,6BAA8B,SAAQ,mBAAS;IAM1D,YAAY,EACV,GAAG,EACH,IAAI,EACJ,YAAY,EACZ,OAAO,GAMR;QACC,IAAI,KAAwB,CAAA;QAC5B,IAAI,WAAW,GAA4C,SAAS,CAAA;QACpE,IAAI,YAAkC,CAAA;QACtC,IAAI,MAA0B,CAAA;QAC9B,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACH,WAAW,GAAG,IAAA,wCAAiB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAA;gBAC9C,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,WAAW,CAAA;gBAC3D,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;oBAC1B,MAAM,GAAI,SAAsB,CAAC,CAAC,CAAC,CAAA;gBACrC,CAAC;qBAAM,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;oBACjC,MAAM,CAAC,QAAQ,CAAC,GAAG,SAAqB,CAAA;oBACxC,MAAM,GAAG,0BAAY,CAAC,QAAqC,CAAC,CAAA;gBAC9D,CAAC;qBAAM,CAAC;oBACN,MAAM,eAAe,GAAG,OAAO;wBAC7B,CAAC,CAAC,IAAA,gCAAa,EAAC,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;wBAC/C,CAAC,CAAC,SAAS,CAAA;oBACb,MAAM,aAAa,GACjB,OAAO,IAAI,SAAS;wBAClB,CAAC,CAAC,IAAA,gDAAqB,EAAC;4BACpB,OAAO;4BACP,IAAI,EAAE,SAAS;4BACf,mBAAmB,EAAE,KAAK;4BAC1B,WAAW,EAAE,KAAK;yBACnB,CAAC;wBACJ,CAAC,CAAC,SAAS,CAAA;oBAEf,YAAY,GAAG;wBACb,eAAe,CAAC,CAAC,CAAC,UAAU,eAAe,EAAE,CAAC,CAAC,CAAC,EAAE;wBAClD,aAAa,IAAI,aAAa,KAAK,IAAI;4BACrC,CAAC,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;iCAChD,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;iCACd,IAAI,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE;4BAC/B,CAAC,CAAC,EAAE;qBACP,CAAA;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,KAAK,GAAG,GAAY,CAAA;YACtB,CAAC;QACH,CAAC;aAAM,IAAI,OAAO;YAAE,MAAM,GAAG,OAAO,CAAA;QAEpC,IAAI,SAA0B,CAAA;QAC9B,IAAI,KAAK,YAAY,uCAA8B,EAAE,CAAC;YACpD,SAAS,GAAG,KAAK,CAAC,SAAS,CAAA;YAC3B,YAAY,GAAG;gBACb,+BAA+B,SAAS,4CAA4C;gBACpF,0EAA0E;gBAC1E,sFAAsF,SAAS,GAAG;aACnG,CAAA;QACH,CAAC;QAED,KAAK,CACH,CAAC,MAAM,IAAI,MAAM,KAAK,oBAAoB,CAAC,IAAI,SAAS;YACtD,CAAC,CAAC;gBACE,0BAA0B,YAAY,iCACpC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAC5B,GAAG;gBACH,MAAM,IAAI,SAAS;aACpB,CAAC,IAAI,CAAC,IAAI,CAAC;YACd,CAAC,CAAC,0BAA0B,YAAY,aAAa,EACvD;YACE,KAAK;YACL,YAAY;YACZ,IAAI,EAAE,+BAA+B;SACtC,CACF,CAAA;QAjFH;;;;;WAA8C;QAC9C;;;;;WAAqB;QACrB;;;;;WAA2B;QAC3B;;;;;WAA2B;QAgFzB,IAAI,CAAC,IAAI,GAAG,WAAW,CAAA;QACvB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;QACf,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;IAC5B,CAAC;CACF;AAzFD,sEAyFC;AAMD,MAAa,6BAA8B,SAAQ,mBAAS;IAC1D,YAAY,EAAE,YAAY,EAA4B;QACpD,KAAK,CAAC,0BAA0B,YAAY,4BAA4B,EAAE;YACxE,YAAY,EAAE;gBACZ,4CAA4C;gBAC5C,gDAAgD,YAAY,IAAI;gBAChE,uEAAuE;gBACvE,oCAAoC;aACrC;YACD,IAAI,EAAE,+BAA+B;SACtC,CAAC,CAAA;IACJ,CAAC;CACF;AAZD,sEAYC;AAMD,MAAa,mCAAoC,SAAQ,mBAAS;IAChE,YAAY,EAAE,OAAO,EAAqC;QACxD,KAAK,CACH,qDACE,OAAO,CAAC,CAAC,CAAC,iBAAiB,OAAO,IAAI,CAAC,CAAC,CAAC,EAC3C,EAAE,EACF;YACE,YAAY,EAAE;gBACZ,gBAAgB;gBAChB,sGAAsG;gBACtG,uGAAuG;aACxG;YACD,IAAI,EAAE,qCAAqC;SAC5C,CACF,CAAA;IACH,CAAC;CACF;AAhBD,kFAgBC;AAKD,MAAa,gBAAiB,SAAQ,mBAAS;IAK7C,YAAY,EACV,IAAI,EACJ,OAAO,GAIR;QACC,KAAK,CAAC,OAAO,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAA;QAXpD;;;;mBAAO,CAAC;WAAA;QAER;;;;;WAAmD;QAUjD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AAfD,4CAeC"}