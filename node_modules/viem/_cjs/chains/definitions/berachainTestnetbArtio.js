"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.berachainTestnetbArtio = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.berachainTestnetbArtio = (0, defineChain_js_1.defineChain)({
    id: 80084,
    name: '<PERSON><PERSON><PERSON><PERSON> bArtio',
    nativeCurrency: {
        decimals: 18,
        name: 'BER<PERSON> Token',
        symbol: 'BERA',
    },
    contracts: {
        multicall3: {
            address: '0xcA11bde05977b3631167028862bE2a173976CA11',
            blockCreated: 109269,
        },
        ensRegistry: {
            address: '0xB0eef18971290b333450586D33dcA6cE122651D2',
            blockCreated: 7736794,
        },
        ensUniversalResolver: {
            address: '0x41692Ef1EA0C79E6b73077E4A67572D2BDbD7057',
            blockCreated: 7736795,
        },
    },
    ensTlds: ['.bera'],
    rpcUrls: {
        default: { http: ['https://bartio.rpc.berachain.com'] },
    },
    blockExplorers: {
        default: {
            name: 'Berachain bArtio Beratrail',
            url: 'https://bartio.beratrail.io',
        },
    },
    testnet: true,
});
//# sourceMappingURL=berachainTestnetbArtio.js.map