{"version": 3, "file": "toBlobSidecars.js", "sourceRoot": "", "sources": ["../../../utils/blob/toBlobSidecars.ts"], "names": [], "mappings": ";;AAiFA,wCA8BC;AA1GD,mEAGgC;AAChC,yDAA+E;AAC/E,6CAA6D;AAuE7D,SAAgB,cAAc,CAY5B,UAAqD;IAErD,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,UAAU,CAAA;IACpC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,IAAA,oBAAO,EAAC,EAAE,IAAI,EAAE,IAAK,EAAE,EAAE,EAAE,CAAC,CAAA;IAC9D,MAAM,WAAW,GACf,UAAU,CAAC,WAAW,IAAI,IAAA,0CAAkB,EAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAI,EAAE,EAAE,EAAE,CAAC,CAAA;IACxE,MAAM,MAAM,GACV,UAAU,CAAC,MAAM,IAAI,IAAA,gCAAa,EAAC,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,GAAI,EAAE,EAAE,EAAE,CAAC,CAAA;IAE3E,MAAM,QAAQ,GAAiB,EAAE,CAAA;IACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;QACnC,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;YACd,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;YAC1B,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;SACjB,CAAC,CAAA;IAEJ,OAAO,QAAwC,CAAA;AACjD,CAAC"}