{"version": 3, "file": "encodeAbiParameters.js", "sourceRoot": "", "sources": ["../../../utils/abi/encodeAbiParameters.ts"], "names": [], "mappings": ";;AAoFA,kDAqBC;AA2TD,gDAQC;AAtaD,gDAW4B;AAC5B,wDAGgC;AAChC,kDAAgD;AAChD,0DAAiE;AAGjE,0DAA4E;AAC5E,iDAAgE;AAChE,2CAA6D;AAC7D,6CAA0D;AAC1D,+CAA6D;AAC7D,mDAO6B;AAC7B,0CAA0C;AA6C1C,SAAgB,mBAAmB,CAGjC,MAAc,EACd,MAES;IAET,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM;QACjC,MAAM,IAAI,uCAA8B,CAAC;YACvC,cAAc,EAAE,MAAM,CAAC,MAAgB;YACvC,WAAW,EAAE,MAAM,CAAC,MAAa;SAClC,CAAC,CAAA;IAEJ,MAAM,cAAc,GAAG,aAAa,CAAC;QACnC,MAAM,EAAE,MAAiC;QACzC,MAAM,EAAE,MAAa;KACtB,CAAC,CAAA;IACF,MAAM,IAAI,GAAG,YAAY,CAAC,cAAc,CAAC,CAAA;IACzC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,IAAI,CAAA;IAClC,OAAO,IAAI,CAAA;AACb,CAAC;AAWD,SAAS,aAAa,CAA+C,EACnE,MAAM,EACN,MAAM,GAIP;IACC,MAAM,cAAc,GAAoB,EAAE,CAAA;IAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC3E,CAAC;IACD,OAAO,cAAc,CAAA;AACvB,CAAC;AAcD,SAAS,YAAY,CAAmC,EACtD,KAAK,EACL,KAAK,GAIN;IACC,MAAM,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACtD,IAAI,eAAe,EAAE,CAAC;QACpB,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,eAAe,CAAA;QACtC,OAAO,WAAW,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;IAClE,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3B,OAAO,WAAW,CAAC,KAAyB,EAAE;YAC5C,KAAK,EAAE,KAA0B;SAClC,CAAC,CAAA;IACJ,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC7B,OAAO,aAAa,CAAC,KAAuB,CAAC,CAAA;IAC/C,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QAC1B,OAAO,UAAU,CAAC,KAA2B,CAAC,CAAA;IAChD,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QAClE,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;QAC3C,MAAM,CAAC,EAAE,AAAD,EAAG,IAAI,GAAG,KAAK,CAAC,GAAG,uBAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;QAC9D,OAAO,YAAY,CAAC,KAA0B,EAAE;YAC9C,MAAM;YACN,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;SACnB,CAAC,CAAA;IACJ,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;QACnC,OAAO,WAAW,CAAC,KAAuB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;IACxD,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,YAAY,CAAC,KAA0B,CAAC,CAAA;IACjD,CAAC;IACD,MAAM,IAAI,oCAA2B,CAAC,KAAK,CAAC,IAAI,EAAE;QAChD,QAAQ,EAAE,oCAAoC;KAC/C,CAAC,CAAA;AACJ,CAAC;AAMD,SAAS,YAAY,CAAC,cAA+B;IAEnD,IAAI,UAAU,GAAG,CAAC,CAAA;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/C,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA;QAC9C,IAAI,OAAO;YAAE,UAAU,IAAI,EAAE,CAAA;;YACxB,UAAU,IAAI,IAAA,cAAI,EAAC,OAAO,CAAC,CAAA;IAClC,CAAC;IAGD,MAAM,YAAY,GAAU,EAAE,CAAA;IAC9B,MAAM,aAAa,GAAU,EAAE,CAAA;IAC/B,IAAI,WAAW,GAAG,CAAC,CAAA;IACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/C,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA;QAC9C,IAAI,OAAO,EAAE,CAAC;YACZ,YAAY,CAAC,IAAI,CAAC,IAAA,sBAAW,EAAC,UAAU,GAAG,WAAW,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;YACtE,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC3B,WAAW,IAAI,IAAA,cAAI,EAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC5B,CAAC;IACH,CAAC;IAGD,OAAO,IAAA,kBAAM,EAAC,CAAC,GAAG,YAAY,EAAE,GAAG,aAAa,CAAC,CAAC,CAAA;AACpD,CAAC;AASD,SAAS,aAAa,CAAC,KAAU;IAC/B,IAAI,CAAC,IAAA,wBAAS,EAAC,KAAK,CAAC;QAAE,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAA;IACxE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,IAAA,eAAM,EAAC,KAAK,CAAC,WAAW,EAAS,CAAC,EAAE,CAAA;AACxE,CAAC;AAYD,SAAS,WAAW,CAClB,KAAyC,EACzC,EACE,MAAM,EACN,KAAK,GAIN;IAED,MAAM,OAAO,GAAG,MAAM,KAAK,IAAI,CAAA;IAE/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,MAAM,IAAI,0BAAiB,CAAC,KAAK,CAAC,CAAA;IAC7D,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM;QACrC,MAAM,IAAI,4CAAmC,CAAC;YAC5C,cAAc,EAAE,MAAO;YACvB,WAAW,EAAE,KAAK,CAAC,MAAM;YACzB,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,IAAI,MAAM,GAAG;SACjC,CAAC,CAAA;IAEJ,IAAI,YAAY,GAAG,KAAK,CAAA;IACxB,MAAM,cAAc,GAAoB,EAAE,CAAA;IAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,aAAa,GAAG,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAC9D,IAAI,aAAa,CAAC,OAAO;YAAE,YAAY,GAAG,IAAI,CAAA;QAC9C,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;IACpC,CAAC;IAED,IAAI,OAAO,IAAI,YAAY,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAG,YAAY,CAAC,cAAc,CAAC,CAAA;QACzC,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,MAAM,GAAG,IAAA,sBAAW,EAAC,cAAc,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAA;YAC/D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,kBAAM,EAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;aACrE,CAAA;QACH,CAAC;QACD,IAAI,YAAY;YAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;IAC3D,CAAC;IACD,OAAO;QACL,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,IAAA,kBAAM,EAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;KAC9D,CAAA;AACH,CAAC;AAUD,SAAS,WAAW,CAClB,KAAU,EACV,EAAE,KAAK,EAAoB;IAE3B,MAAM,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IAC/C,MAAM,SAAS,GAAG,IAAA,cAAI,EAAC,KAAK,CAAC,CAAA;IAC7B,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,IAAI,MAAM,GAAG,KAAK,CAAA;QAGlB,IAAI,SAAS,GAAG,EAAE,KAAK,CAAC;YACtB,MAAM,GAAG,IAAA,eAAM,EAAC,MAAM,EAAE;gBACtB,GAAG,EAAE,OAAO;gBACZ,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;aAClD,CAAC,CAAA;QACJ,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAA,kBAAM,EAAC,CAAC,IAAA,eAAM,EAAC,IAAA,sBAAW,EAAC,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;SACxE,CAAA;IACH,CAAC;IACD,IAAI,SAAS,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC1C,MAAM,IAAI,0CAAiC,CAAC;YAC1C,YAAY,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACxC,KAAK;SACN,CAAC,CAAA;IACJ,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,IAAA,eAAM,EAAC,KAAK,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,CAAA;AACrE,CAAC;AAID,SAAS,UAAU,CAAC,KAAc;IAChC,IAAI,OAAO,KAAK,KAAK,SAAS;QAC5B,MAAM,IAAI,mBAAS,CACjB,2BAA2B,KAAK,YAAY,OAAO,KAAK,qCAAqC,CAC9F,CAAA;IACH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,IAAA,eAAM,EAAC,IAAA,oBAAS,EAAC,KAAK,CAAC,CAAC,EAAE,CAAA;AAC9D,CAAC;AAID,SAAS,YAAY,CACnB,KAAa,EACb,EAAE,MAAM,EAAE,IAAI,GAAG,GAAG,EAAkD;IAEtE,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,MAAM,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAA;QAC1D,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QACnC,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG;YAC5B,MAAM,IAAI,oCAAsB,CAAC;gBAC/B,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE;gBACnB,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE;gBACnB,MAAM;gBACN,IAAI,EAAE,IAAI,GAAG,CAAC;gBACd,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;aACxB,CAAC,CAAA;IACN,CAAC;IACD,OAAO;QACL,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,IAAA,sBAAW,EAAC,KAAK,EAAE;YAC1B,IAAI,EAAE,EAAE;YACR,MAAM;SACP,CAAC;KACH,CAAA;AACH,CAAC;AAWD,SAAS,YAAY,CAAC,KAAa;IACjC,MAAM,QAAQ,GAAG,IAAA,sBAAW,EAAC,KAAK,CAAC,CAAA;IACnC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAA,cAAI,EAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAA;IAClD,MAAM,KAAK,GAAU,EAAE,CAAA;IACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,KAAK,CAAC,IAAI,CACR,IAAA,eAAM,EAAC,IAAA,gBAAK,EAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;YAC5C,GAAG,EAAE,OAAO;SACb,CAAC,CACH,CAAA;IACH,CAAC;IACD,OAAO;QACL,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAA,kBAAM,EAAC;YACd,IAAA,eAAM,EAAC,IAAA,sBAAW,EAAC,IAAA,cAAI,EAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YACjD,GAAG,KAAK;SACT,CAAC;KACH,CAAA;AACH,CAAC;AASD,SAAS,WAAW,CAGlB,KAAyC,EACzC,EAAE,KAAK,EAAoB;IAE3B,IAAI,OAAO,GAAG,KAAK,CAAA;IACnB,MAAM,cAAc,GAAoB,EAAE,CAAA;IAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACjD,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAClC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAA;QACpD,MAAM,aAAa,GAAG,YAAY,CAAC;YACjC,KAAK,EAAE,MAAM;YACb,KAAK,EAAG,KAAa,CAAC,KAAM,CAAuB;SACpD,CAAC,CAAA;QACF,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QAClC,IAAI,aAAa,CAAC,OAAO;YAAE,OAAO,GAAG,IAAI,CAAA;IAC3C,CAAC;IACD,OAAO;QACL,OAAO;QACP,OAAO,EAAE,OAAO;YACd,CAAC,CAAC,YAAY,CAAC,cAAc,CAAC;YAC9B,CAAC,CAAC,IAAA,kBAAM,EAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;KACzD,CAAA;AACH,CAAC;AAID,SAAgB,kBAAkB,CAChC,IAAY;IAEZ,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;IAC9C,OAAO,OAAO;QACZ,CAAC;YACC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,SAAS,CAAA;AACf,CAAC"}