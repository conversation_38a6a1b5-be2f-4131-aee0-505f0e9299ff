{"version": 3, "file": "encodeErrorResult.js", "sourceRoot": "", "sources": ["../../../utils/abi/encodeErrorResult.ts"], "names": [], "mappings": ";;AAoEA,8CA4BC;AA9FD,gDAG4B;AAM5B,iDAAsE;AACtE,yEAGsC;AAItC,qEAGiC;AACjC,yDAA+E;AAC/E,mDAAsE;AAEtE,MAAM,QAAQ,GAAG,kCAAkC,CAAA;AA0CnD,SAAgB,iBAAiB,CAI/B,UAAuD;IAEvD,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,UAAyC,CAAA;IAE1E,IAAI,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;IACpB,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,IAAI,GAAG,IAAA,0BAAU,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAA;QACvD,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,8BAAqB,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;QACnE,OAAO,GAAG,IAAI,CAAA;IAChB,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO;QAC1B,MAAM,IAAI,8BAAqB,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;IAE1D,MAAM,UAAU,GAAG,IAAA,gCAAa,EAAC,OAAO,CAAC,CAAA;IACzC,MAAM,SAAS,GAAG,IAAA,0CAAkB,EAAC,UAAU,CAAC,CAAA;IAEhD,IAAI,IAAI,GAAQ,IAAI,CAAA;IACpB,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5B,IAAI,CAAC,OAAO,CAAC,MAAM;YACjB,MAAM,IAAI,oCAA2B,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;QACnE,IAAI,GAAG,IAAA,4CAAmB,EAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAClD,CAAC;IACD,OAAO,IAAA,qBAAS,EAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAA;AACrC,CAAC"}