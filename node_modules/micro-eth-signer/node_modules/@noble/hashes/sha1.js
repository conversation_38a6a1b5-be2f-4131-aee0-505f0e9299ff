"use strict";
/**
 * SHA1 (RFC 3174) legacy hash function.
 * @module
 * @deprecated
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.sha1 = exports.SHA1 = void 0;
var legacy_ts_1 = require("./legacy.js");
Object.defineProperty(exports, "SHA1", { enumerable: true, get: function () { return legacy_ts_1.SHA1; } });
Object.defineProperty(exports, "sha1", { enumerable: true, get: function () { return legacy_ts_1.sha1; } });
//# sourceMappingURL=sha1.js.map