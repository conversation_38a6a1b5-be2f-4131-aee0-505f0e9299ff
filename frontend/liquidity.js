// Liquidity Management
class LiquidityManager {
    constructor() {
        this.currentPair = ['ETH', 'UBA'];
        this.selectedFeeTier = CONFIG.FEE_TIERS.MEDIUM;
        this.positions = [];
        
        this.initializeLiquidityListeners();
    }
    
    // Initialize liquidity-related event listeners
    initializeLiquidityListeners() {
        // Fee tier selector
        const feeTierSelect = document.getElementById('feeTier');
        if (feeTierSelect) {
            feeTierSelect.addEventListener('change', (e) => {
                this.selectedFeeTier = parseInt(e.target.value);
                this.updateLiquidityInfo();
            });
        }
        
        // Amount inputs
        const amount0Input = document.getElementById('liquidityAmount0');
        const amount1Input = document.getElementById('liquidityAmount1');
        
        if (amount0Input) {
            amount0Input.addEventListener('input', () => {
                this.updateLiquidityButton();
                this.calculateProportionalAmount(0);
            });
        }
        
        if (amount1Input) {
            amount1Input.addEventListener('input', () => {
                this.updateLiquidityButton();
                this.calculateProportionalAmount(1);
            });
        }
        
        // Add liquidity button
        const addLiquidityBtn = document.getElementById('addLiquidityBtn');
        if (addLiquidityBtn) {
            addLiquidityBtn.addEventListener('click', () => {
                this.addLiquidity();
            });
        }
        
        // Price range inputs
        const minPriceInput = document.getElementById('minPrice');
        const maxPriceInput = document.getElementById('maxPrice');
        
        if (minPriceInput) {
            minPriceInput.addEventListener('input', () => {
                this.updatePriceRange();
            });
        }
        
        if (maxPriceInput) {
            maxPriceInput.addEventListener('input', () => {
                this.updatePriceRange();
            });
        }
    }
    
    // Select pair token
    selectPairToken(index) {
        // For demo, we'll just toggle between ETH and UBA
        if (index === 0) {
            this.currentPair[0] = this.currentPair[0] === 'ETH' ? 'UBA' : 'ETH';
        } else {
            this.currentPair[1] = this.currentPair[1] === 'ETH' ? 'UBA' : 'ETH';
        }
        
        // Ensure tokens are different
        if (this.currentPair[0] === this.currentPair[1]) {
            this.currentPair[1] = this.currentPair[0] === 'ETH' ? 'UBA' : 'ETH';
        }
        
        this.updatePairUI();
        this.updateLiquidityInfo();
    }
    
    // Update pair UI
    updatePairUI() {
        const pairTokens = document.querySelectorAll('.pair-token');
        
        pairTokens.forEach((token, index) => {
            const tokenData = CONFIG.TOKENS[this.currentPair[index]];
            const icon = token.querySelector('.token-icon');
            const symbol = token.querySelector('span');
            
            if (icon) icon.textContent = tokenData.icon;
            if (symbol) symbol.textContent = tokenData.symbol;
        });
        
        // Update amount input labels
        const labels = document.querySelectorAll('.amount-input-group label');
        labels.forEach((label, index) => {
            if (this.currentPair[index]) {
                label.textContent = `Jumlah ${this.currentPair[index]}`;
            }
        });
    }
    
    // Set maximum liquidity amount
    async setMaxLiquidity(tokenIndex) {
        try {
            if (!window.uiManager.isWalletConnected) {
                window.uiManager.showStatus(CONFIG.ERRORS.WALLET_NOT_CONNECTED, 'error');
                return;
            }
            
            const tokenSymbol = this.currentPair[tokenIndex];
            const balance = window.uiManager.balances[tokenSymbol] || 0;
            let maxAmount = parseFloat(balance);
            
            // Leave some ETH for gas if it's ETH
            if (tokenSymbol === 'ETH') {
                maxAmount = Math.max(0, maxAmount - 0.01);
            }
            
            const amountInput = document.getElementById(`liquidityAmount${tokenIndex}`);
            if (amountInput) {
                amountInput.value = maxAmount.toFixed(6);
                this.updateLiquidityButton();
                this.calculateProportionalAmount(tokenIndex);
            }
            
        } catch (error) {
            console.error('Error setting max liquidity amount:', error);
            window.uiManager.showStatus('Error setting maximum amount', 'error');
        }
    }
    
    // Calculate proportional amount for the other token
    calculateProportionalAmount(changedIndex) {
        // For demo purposes, we'll use a simple 1:1000 ratio (ETH:UBA)
        const amount0Input = document.getElementById('liquidityAmount0');
        const amount1Input = document.getElementById('liquidityAmount1');
        
        if (!amount0Input || !amount1Input) return;
        
        const changedAmount = parseFloat(
            changedIndex === 0 ? amount0Input.value : amount1Input.value
        );
        
        if (!changedAmount || changedAmount <= 0) return;
        
        let proportionalAmount;
        
        if (this.currentPair[0] === 'ETH' && this.currentPair[1] === 'UBA') {
            proportionalAmount = changedIndex === 0 ? changedAmount * 1000 : changedAmount / 1000;
        } else if (this.currentPair[0] === 'UBA' && this.currentPair[1] === 'ETH') {
            proportionalAmount = changedIndex === 0 ? changedAmount / 1000 : changedAmount * 1000;
        } else {
            proportionalAmount = changedAmount; // 1:1 for same tokens (shouldn't happen)
        }
        
        // Update the other input
        if (changedIndex === 0) {
            amount1Input.value = proportionalAmount.toFixed(6);
        } else {
            amount0Input.value = proportionalAmount.toFixed(6);
        }
    }
    
    // Set price range presets
    setRange(type) {
        const minPriceInput = document.getElementById('minPrice');
        const maxPriceInput = document.getElementById('maxPrice');
        
        if (!minPriceInput || !maxPriceInput) return;
        
        // Mock price ranges for demo
        switch (type) {
            case 'full':
                minPriceInput.value = '0';
                maxPriceInput.value = '∞';
                break;
            case 'safe':
                minPriceInput.value = '800';
                maxPriceInput.value = '1200';
                break;
            case 'narrow':
                minPriceInput.value = '950';
                maxPriceInput.value = '1050';
                break;
        }
        
        this.updatePriceRange();
    }
    
    // Update price range information
    updatePriceRange() {
        // For demo purposes, just log the range
        const minPrice = document.getElementById('minPrice')?.value;
        const maxPrice = document.getElementById('maxPrice')?.value;
        
        console.log(`Price range: ${minPrice} - ${maxPrice}`);
    }
    
    // Update liquidity button state
    updateLiquidityButton() {
        const addLiquidityBtn = document.getElementById('addLiquidityBtn');
        const amount0Input = document.getElementById('liquidityAmount0');
        const amount1Input = document.getElementById('liquidityAmount1');
        
        if (!addLiquidityBtn) return;
        
        if (!window.uiManager.isWalletConnected) {
            addLiquidityBtn.textContent = 'Connect Wallet';
            addLiquidityBtn.disabled = true;
            return;
        }
        
        const amount0 = parseFloat(amount0Input?.value || 0);
        const amount1 = parseFloat(amount1Input?.value || 0);
        
        if (!amount0 || !amount1 || amount0 <= 0 || amount1 <= 0) {
            addLiquidityBtn.textContent = 'Enter amounts';
            addLiquidityBtn.disabled = true;
            return;
        }
        
        // Check balances
        const balance0 = parseFloat(window.uiManager.balances[this.currentPair[0]] || 0);
        const balance1 = parseFloat(window.uiManager.balances[this.currentPair[1]] || 0);
        
        if (amount0 > balance0 || amount1 > balance1) {
            addLiquidityBtn.textContent = 'Insufficient balance';
            addLiquidityBtn.disabled = true;
            return;
        }
        
        addLiquidityBtn.textContent = 'Add Liquidity';
        addLiquidityBtn.disabled = false;
    }
    
    // Update liquidity information
    updateLiquidityInfo() {
        // For demo purposes, just update the UI
        console.log(`Liquidity info updated for ${this.currentPair.join('/')} at ${this.selectedFeeTier} fee tier`);
    }
    
    // Add liquidity to pool
    async addLiquidity() {
        try {
            if (!window.uiManager.isWalletConnected) {
                window.uiManager.showStatus(CONFIG.ERRORS.WALLET_NOT_CONNECTED, 'error');
                return;
            }
            
            const amount0Input = document.getElementById('liquidityAmount0');
            const amount1Input = document.getElementById('liquidityAmount1');
            
            const amount0 = parseFloat(amount0Input.value);
            const amount1 = parseFloat(amount1Input.value);
            
            if (!amount0 || !amount1 || amount0 <= 0 || amount1 <= 0) {
                window.uiManager.showStatus(CONFIG.ERRORS.INVALID_AMOUNT, 'error');
                return;
            }
            
            window.uiManager.showLoading('Adding liquidity to pool...');
            
            // For demo purposes, we'll simulate the transaction
            // In production, this would interact with Uniswap V3 Position Manager
            
            await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate transaction time
            
            window.uiManager.hideLoading();
            window.uiManager.showStatus(CONFIG.SUCCESS.LIQUIDITY_ADDED, 'success');
            
            // Clear form
            amount0Input.value = '';
            amount1Input.value = '';
            
            // Update balances
            await window.uiManager.updateTokenBalances();
            this.updateLiquidityButton();
            
            // Refresh positions
            await this.loadPositions();
            
        } catch (error) {
            console.error('Add liquidity error:', error);
            window.uiManager.hideLoading();
            
            let errorMessage = CONFIG.ERRORS.TRANSACTION_FAILED;
            if (error.code === 4001) {
                errorMessage = CONFIG.ERRORS.USER_REJECTED;
            }
            
            window.uiManager.showStatus(errorMessage, 'error');
        }
    }
    
    // Load user's liquidity positions
    async loadPositions() {
        try {
            if (!window.uiManager.isWalletConnected) return;
            
            // For demo purposes, we'll show mock positions
            this.positions = [
                {
                    id: 1,
                    pair: 'ETH/UBA',
                    feeTier: '0.3%',
                    liquidity: '1.5 ETH + 1500 UBA',
                    range: '800 - 1200',
                    fees: '0.05 ETH + 50 UBA'
                }
            ];
            
            this.updatePositionsUI();
            
        } catch (error) {
            console.error('Error loading positions:', error);
        }
    }
    
    // Update positions UI
    updatePositionsUI() {
        const positionsGrid = document.getElementById('myPositions');
        const positionsList = document.getElementById('positionsList');
        
        if (this.positions.length === 0) {
            if (positionsGrid) {
                positionsGrid.innerHTML = '<div class="no-positions"><p>No liquidity positions found</p></div>';
            }
            return;
        }
        
        const positionsHTML = this.positions.map(position => `
            <div class="position-card">
                <div class="position-header">
                    <h4>${position.pair}</h4>
                    <span class="fee-tier">${position.feeTier}</span>
                </div>
                <div class="position-details">
                    <div class="detail-row">
                        <span>Liquidity:</span>
                        <span>${position.liquidity}</span>
                    </div>
                    <div class="detail-row">
                        <span>Range:</span>
                        <span>${position.range}</span>
                    </div>
                    <div class="detail-row">
                        <span>Unclaimed Fees:</span>
                        <span>${position.fees}</span>
                    </div>
                </div>
                <div class="position-actions">
                    <button class="action-btn secondary" onclick="claimFees(${position.id})">Claim Fees</button>
                    <button class="action-btn" onclick="removePosition(${position.id})">Remove</button>
                </div>
            </div>
        `).join('');
        
        if (positionsGrid) {
            positionsGrid.innerHTML = positionsHTML;
        }
        
        if (positionsList) {
            positionsList.innerHTML = positionsHTML;
        }
    }
    
    // Remove liquidity position
    async removePosition(positionId) {
        try {
            window.uiManager.showLoading('Removing liquidity position...');
            
            // Simulate transaction
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Remove position from array
            this.positions = this.positions.filter(p => p.id !== positionId);
            
            window.uiManager.hideLoading();
            window.uiManager.showStatus(CONFIG.SUCCESS.LIQUIDITY_REMOVED, 'success');
            
            // Update UI
            this.updatePositionsUI();
            await window.uiManager.updateTokenBalances();
            
        } catch (error) {
            console.error('Remove position error:', error);
            window.uiManager.hideLoading();
            window.uiManager.showStatus(CONFIG.ERRORS.TRANSACTION_FAILED, 'error');
        }
    }
    
    // Claim fees from position
    async claimFees(positionId) {
        try {
            window.uiManager.showLoading('Claiming fees...');
            
            // Simulate transaction
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            window.uiManager.hideLoading();
            window.uiManager.showStatus(CONFIG.SUCCESS.FEES_CLAIMED, 'success');
            
            // Update balances
            await window.uiManager.updateTokenBalances();
            
        } catch (error) {
            console.error('Claim fees error:', error);
            window.uiManager.hideLoading();
            window.uiManager.showStatus(CONFIG.ERRORS.TRANSACTION_FAILED, 'error');
        }
    }
}

// Global liquidity manager instance
window.liquidityManager = new LiquidityManager();

// Global utility functions
window.selectPairToken = (index) => window.liquidityManager.selectPairToken(index);
window.setMaxLiquidity = (index) => window.liquidityManager.setMaxLiquidity(index);
window.setRange = (type) => window.liquidityManager.setRange(type);
window.removePosition = (id) => window.liquidityManager.removePosition(id);
window.claimFees = (id) => window.liquidityManager.claimFees(id);
