# 🦄 UbaSwap DEX - Complete DeFi Platform

A comprehensive, multi-page DeFi platform with modern UI inspired by Uniswap V3. Built for the UbaSwap ecosystem on Sepolia testnet.

## 🌟 Complete Feature Set

### 🔄 **Swap Page (Tukar)**
- **One-click token swapping** between ETH and UBA
- **Real-time price quotes** with live updates
- **Slippage protection** with customizable tolerance
- **Transaction deadline** settings
- **MAX button** for quick balance usage
- **Animated swap direction** toggle
- **Protocol fee display** (0.5%)
- **Price impact** calculation

### 💧 **Liquidity Page (Likuiditas)**
- **Add Liquidity** to Uniswap V3 pools
- **Remove Liquidity** from existing positions
- **My Positions** management dashboard
- **Fee tier selection** (0.05%, 0.3%, 1%)
- **Price range** configuration
- **Proportional amount** calculation
- **Unclaimed fees** tracking
- **Position management** tools

### ⚡ **Swap & Forward Page (Tukar & Teruskan)**
- **Atomic transactions** - swap and send in one step
- **Custom recipient** address support
- **Gas optimization** through single transaction
- **Same swap interface** as main swap page
- **Recipient presets** (self, saved addresses)
- **Transaction atomicity** guarantee

### 💰 **Fees Page (Biaya)**
- **Protocol statistics** dashboard
- **Total fees collected** tracking
- **UBA tokens burned** counter
- **Transaction volume** metrics
- **Claimable fees** for users
- **Admin panel** for contract owners
- **Fee processing** controls
- **Protocol fee** adjustment tools

## Deployed Contracts (Sepolia Testnet)

- **UBA Token**: `******************************************`
- **SwapAndForward**: `******************************************`
- **FeeDistributor**: `******************************************`

## 🚀 How to Use

### 1. **Setup MetaMask**
   - Install MetaMask browser extension
   - Switch to Sepolia testnet (automatic)
   - Get some Sepolia ETH from a faucet

### 2. **Connect Your Wallet**
   - Open `index.html` in your browser
   - Click "Connect Wallet" in the top-right corner
   - Approve the MetaMask connection
   - The app will automatically switch to Sepolia if needed

### 3. **Start Swapping**
   - **Select tokens**: Click on token selectors to switch between ETH and UBA
   - **Enter amount**: Type the amount you want to swap
   - **Use MAX**: Click on "Balance" to use maximum available amount
   - **Review details**: Check the exchange rate and fees in the price info
   - **Swap tokens**: Click the swap button to execute the transaction
   - **Switch direction**: Use the arrow button to quickly reverse the swap direction

### 4. **UI Features**
   - **Real-time balances**: See your ETH and UBA balances update automatically
   - **Price estimation**: Get instant price quotes as you type
   - **Fee calculation**: See protocol fees and minimum received amounts
   - **Transaction status**: Get live updates during swap execution

## Getting Sepolia ETH

You can get free Sepolia ETH from these faucets:
- [Sepolia Faucet](https://sepoliafaucet.com/)
- [Alchemy Sepolia Faucet](https://sepoliafaucet.com/)
- [Chainlink Sepolia Faucet](https://faucets.chain.link/sepolia)

## Technical Details

- **Protocol Fee**: 0.5% on all swaps
- **Uniswap V3 Integration**: Uses Uniswap V3 for actual token swapping
- **Pool Fee**: 0.3% (standard Uniswap V3 pool)
- **Slippage**: Currently set to accept any amount (for demo purposes)

## Contract Verification

All contracts are verified on Etherscan:
- View on [Sepolia Etherscan](https://sepolia.etherscan.io/)

## Development

This is a simple HTML/CSS/JavaScript frontend using:
- Ethers.js v5 for blockchain interaction
- Vanilla JavaScript (no frameworks)
- Responsive CSS design

To run locally:
1. Clone the repository
2. Open `frontend/index.html` in your browser
3. Make sure MetaMask is installed and connected to Sepolia

## 🎯 **Tombol-tombol Utama & Fungsinya**

### **Navigation Bar**
- **🦄 UbaSwap Logo** - Kembali ke halaman utama
- **Tukar** - Halaman swap token utama
- **Likuiditas** - Manajemen posisi likuiditas
- **Tukar & Teruskan** - Fitur atomic swap & forward
- **Biaya** - Dashboard fee dan statistik protokol
- **🟢 Sepolia** - Indikator jaringan aktif
- **Connect Wallet** - Hubungkan/putuskan dompet

### **Halaman Swap (Tukar)**
- **⚙️ Settings** - Pengaturan slippage dan deadline
- **Balance: X.XX** - Klik untuk menggunakan saldo maksimum
- **Token Selector** - Klik untuk memilih token (ETH/UBA)
- **MAX** - Tombol cepat untuk jumlah maksimum
- **⇅ Swap Arrow** - Tukar posisi token input/output
- **Swap ETH for UBA** - Eksekusi transaksi swap

### **Halaman Likuiditas**
- **Tambah Likuiditas** - Tab untuk menambah likuiditas
- **Hapus Likuiditas** - Tab untuk menghapus posisi
- **Posisi Saya** - Tab untuk melihat posisi aktif
- **Fee Tier Selector** - Pilih tingkat fee (0.05%, 0.3%, 1%)
- **Full Range / Safe Range / Narrow Range** - Preset rentang harga
- **Tambah Likuiditas** - Eksekusi penambahan likuiditas
- **Claim Fees** - Klaim fee dari posisi
- **Remove** - Hapus posisi likuiditas

### **Halaman Swap & Forward**
- **☑️ Kirim ke alamat lain** - Toggle untuk alamat penerima custom
- **Dompet Saya / Alamat Tersimpan** - Preset alamat penerima
- **Lakukan Tukar & Teruskan** - Eksekusi transaksi atomik

### **Halaman Biaya**
- **Klaim Semua Biaya Saya** - Klaim fee yang tersedia
- **📊 Etherscan** - Lihat alamat di block explorer
- **🔌 Disconnect** - Putuskan koneksi dompet

### **Panel Admin (Hanya Owner)**
- **Proses Biaya** - Proses fee terkumpul menjadi UBA burn
- **Perbarui** (Min Amount) - Update minimum process amount
- **Perbarui** (Fee Rate) - Update protocol fee rate

## 🚀 **Cara Menggunakan Lengkap**

### **1. Koneksi Wallet**
1. Buka website (sudah terbuka di browser!)
2. Klik **"Connect Wallet"** di pojok kanan atas
3. Approve koneksi MetaMask
4. Website otomatis switch ke Sepolia testnet

### **2. Mendapatkan Test Token**
- Dapatkan Sepolia ETH dari [faucet](https://sepoliafaucet.com/)
- Swap sebagian ETH ke UBA untuk testing

### **3. Swap Token**
1. Pilih halaman **"Tukar"**
2. Klik token selector untuk memilih ETH atau UBA
3. Masukkan jumlah atau klik **"Balance"** untuk MAX
4. Review informasi harga dan fee
5. Klik **"Swap ETH for UBA"** untuk eksekusi

### **4. Menambah Likuiditas**
1. Pilih halaman **"Likuiditas"**
2. Klik tab **"Tambah Likuiditas"**
3. Pilih fee tier (disarankan 0.3%)
4. Masukkan jumlah untuk kedua token
5. Set rentang harga atau gunakan preset
6. Klik **"Tambah Likuiditas"**

### **5. Swap & Forward**
1. Pilih halaman **"Tukar & Teruskan"**
2. Set token dan jumlah seperti swap biasa
3. Centang **"Kirim ke alamat lain"** jika perlu
4. Masukkan alamat penerima
5. Klik **"Lakukan Tukar & Teruskan"**

### **6. Kelola Fee (Untuk User)**
1. Pilih halaman **"Biaya"**
2. Lihat statistik protokol
3. Klik **"Klaim Semua Biaya Saya"** jika ada

### **7. Admin Functions (Owner Only)**
1. Panel admin muncul otomatis jika Anda owner
2. **Proses Biaya** - Konversi fee ke UBA burn
3. **Update Settings** - Ubah parameter protokol

## 🎨 **Fitur UI Modern**

- **🌙 Dark theme** dengan aksen gradient pink/purple
- **🔄 Animated transitions** untuk semua interaksi
- **📱 Responsive design** untuk mobile dan desktop
- **⚡ Real-time updates** untuk balance dan harga
- **🎯 Click-to-max** functionality pada balance
- **🔧 Customizable settings** untuk slippage dan deadline
- **📊 Live price impact** calculation
- **🎪 Modal dialogs** untuk token selection dan settings

## 🛡️ **Security & Notes**

⚠️ **Testnet Only - Educational Purpose**:
- Gunakan hanya di Sepolia testnet
- Jangan gunakan private key mainnet
- Smart contracts belum diaudit untuk produksi
- Slippage protection minimal untuk demo
- Selalu verify contract address sebelum interact

## 🔗 **Links & Resources**

- **UBA Token**: [0x03504...632b](https://sepolia.etherscan.io/address/******************************************)
- **SwapAndForward**: [0xE781c...941B](https://sepolia.etherscan.io/address/******************************************)
- **FeeDistributor**: [0xBb61a...a0e](https://sepolia.etherscan.io/address/******************************************)
- **Sepolia Faucet**: [sepoliafaucet.com](https://sepoliafaucet.com/)
- **MetaMask**: [metamask.io](https://metamask.io/)

## 🆘 **Troubleshooting**

**Wallet tidak connect?**
- Pastikan MetaMask terinstall
- Refresh halaman dan coba lagi
- Check console browser untuk error

**Transaksi gagal?**
- Pastikan balance ETH cukup untuk gas
- Check slippage tolerance di settings
- Verify contract address benar

**Balance tidak update?**
- Tunggu beberapa detik untuk sinkronisasi
- Refresh halaman jika perlu
- Check network connection

**Admin panel tidak muncul?**
- Hanya contract owner yang bisa lihat
- Pastikan connected dengan address yang benar
