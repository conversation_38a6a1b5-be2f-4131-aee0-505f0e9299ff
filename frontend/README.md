# UbaSwap DEX Frontend

A simple frontend interface for interacting with the UbaSwap DEX smart contracts deployed on Sepolia testnet.

## Features

- 🔗 MetaMask wallet connection
- 🔄 Token swapping (ETH ↔ UBA Token)
- 💰 Real-time balance display
- 🌐 Automatic Sepolia network switching
- 📱 Responsive design

## Deployed Contracts (Sepolia Testnet)

- **UBA Token**: `******************************************`
- **SwapAndForward**: `******************************************`
- **FeeDistributor**: `******************************************`

## How to Use

1. **Setup MetaMask**:
   - Install MetaMask browser extension
   - Switch to Sepolia testnet
   - Get some Sepolia ETH from a faucet

2. **Open the DApp**:
   - Open `index.html` in your browser
   - Click "Connect MetaMask Wallet"
   - Approve the connection

3. **Perform Swaps**:
   - Select input token (ETH or UBA)
   - Enter amount to swap
   - Select output token
   - Enter recipient address (defaults to your address)
   - Click "Swap Tokens"

## Getting Sepolia ETH

You can get free Sepolia ETH from these faucets:
- [Sepolia Faucet](https://sepoliafaucet.com/)
- [Alchemy Sepolia Faucet](https://sepoliafaucet.com/)
- [Chainlink Sepolia Faucet](https://faucets.chain.link/sepolia)

## Technical Details

- **Protocol Fee**: 0.5% on all swaps
- **Uniswap V3 Integration**: Uses Uniswap V3 for actual token swapping
- **Pool Fee**: 0.3% (standard Uniswap V3 pool)
- **Slippage**: Currently set to accept any amount (for demo purposes)

## Contract Verification

All contracts are verified on Etherscan:
- View on [Sepolia Etherscan](https://sepolia.etherscan.io/)

## Development

This is a simple HTML/CSS/JavaScript frontend using:
- Ethers.js v5 for blockchain interaction
- Vanilla JavaScript (no frameworks)
- Responsive CSS design

To run locally:
1. Clone the repository
2. Open `frontend/index.html` in your browser
3. Make sure MetaMask is installed and connected to Sepolia

## Security Notes

⚠️ **This is for educational/demo purposes only**:
- Slippage protection is minimal
- No advanced error handling
- Not audited for production use
- Use only on testnet with test funds

## Support

If you encounter issues:
1. Make sure you're on Sepolia testnet
2. Check that you have sufficient ETH for gas fees
3. Verify contract addresses are correct
4. Check browser console for error messages
