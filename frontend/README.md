# 🦄 UbaSwap DEX Frontend

A modern, Uniswap-inspired frontend interface for interacting with the UbaSwap DEX smart contracts deployed on Sepolia testnet.

## ✨ Features

- 🔗 **One-click MetaMask wallet connection**
- 🔄 **Intuitive token swapping** (ETH ↔ UBA Token)
- 💰 **Real-time balance display** with click-to-max functionality
- 🌐 **Automatic Sepolia network switching**
- 📱 **Responsive design** that works on all devices
- 🎨 **Modern UI** inspired by Uniswap V3
- ⚡ **Live price estimation** and fee calculation
- 🔄 **One-click token switching** with animated arrows

## Deployed Contracts (Sepolia Testnet)

- **UBA Token**: `******************************************`
- **SwapAndForward**: `******************************************`
- **FeeDistributor**: `******************************************`

## 🚀 How to Use

### 1. **Setup MetaMask**
   - Install MetaMask browser extension
   - Switch to Sepolia testnet (automatic)
   - Get some Sepolia ETH from a faucet

### 2. **Connect Your Wallet**
   - Open `index.html` in your browser
   - Click "Connect Wallet" in the top-right corner
   - Approve the MetaMask connection
   - The app will automatically switch to Sepolia if needed

### 3. **Start Swapping**
   - **Select tokens**: Click on token selectors to switch between ETH and UBA
   - **Enter amount**: Type the amount you want to swap
   - **Use MAX**: Click on "Balance" to use maximum available amount
   - **Review details**: Check the exchange rate and fees in the price info
   - **Swap tokens**: Click the swap button to execute the transaction
   - **Switch direction**: Use the arrow button to quickly reverse the swap direction

### 4. **UI Features**
   - **Real-time balances**: See your ETH and UBA balances update automatically
   - **Price estimation**: Get instant price quotes as you type
   - **Fee calculation**: See protocol fees and minimum received amounts
   - **Transaction status**: Get live updates during swap execution

## Getting Sepolia ETH

You can get free Sepolia ETH from these faucets:
- [Sepolia Faucet](https://sepoliafaucet.com/)
- [Alchemy Sepolia Faucet](https://sepoliafaucet.com/)
- [Chainlink Sepolia Faucet](https://faucets.chain.link/sepolia)

## Technical Details

- **Protocol Fee**: 0.5% on all swaps
- **Uniswap V3 Integration**: Uses Uniswap V3 for actual token swapping
- **Pool Fee**: 0.3% (standard Uniswap V3 pool)
- **Slippage**: Currently set to accept any amount (for demo purposes)

## Contract Verification

All contracts are verified on Etherscan:
- View on [Sepolia Etherscan](https://sepolia.etherscan.io/)

## Development

This is a simple HTML/CSS/JavaScript frontend using:
- Ethers.js v5 for blockchain interaction
- Vanilla JavaScript (no frameworks)
- Responsive CSS design

To run locally:
1. Clone the repository
2. Open `frontend/index.html` in your browser
3. Make sure MetaMask is installed and connected to Sepolia

## Security Notes

⚠️ **This is for educational/demo purposes only**:
- Slippage protection is minimal
- No advanced error handling
- Not audited for production use
- Use only on testnet with test funds

## Support

If you encounter issues:
1. Make sure you're on Sepolia testnet
2. Check that you have sufficient ETH for gas fees
3. Verify contract addresses are correct
4. Check browser console for error messages
