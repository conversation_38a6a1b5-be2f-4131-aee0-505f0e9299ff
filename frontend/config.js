// UbaSwap Configuration
const CONFIG = {
    // Network Configuration
    SEPOLIA_CHAIN_ID: '0xaa36a7', // 11155111 in hex
    NETWORK_NAME: 'Sepolia',
    
    // Contract Addresses on Sepolia
    CONTRACTS: {
        UBA_TOKEN: '******************************************',
        SWAP_AND_FORWARD: '******************************************',
        FEE_DISTRIBUTOR: '******************************************',
        WETH: '******************************************',
        UNISWAP_ROUTER: '******************************************',
        UNISWAP_QUOTER: '******************************************'
    },
    
    // Token Configuration
    TOKENS: {
        ETH: {
            address: '******************************************',
            symbol: 'ETH',
            name: 'Ethereum',
            icon: 'Ξ',
            decimals: 18,
            color: '#627eea'
        },
        UBA: {
            address: '******************************************',
            symbol: 'UBA',
            name: 'UBA Token',
            icon: '🦄',
            decimals: 18,
            color: '#ff007a'
        }
    },
    
    // Protocol Configuration
    PROTOCOL: {
        FEE_BPS: 50, // 0.5%
        MAX_FEE_BPS: 1000, // 10%
        BASIS_POINTS: 10000,
        DEFAULT_SLIPPAGE: 0.5, // 0.5%
        DEFAULT_DEADLINE: 20, // 20 minutes
        MIN_PROCESS_AMOUNT: '1000000000000000000' // 1 ETH in wei
    },
    
    // Uniswap V3 Fee Tiers
    FEE_TIERS: {
        LOW: 500,    // 0.05%
        MEDIUM: 3000, // 0.3%
        HIGH: 10000   // 1%
    },
    
    // UI Configuration
    UI: {
        REFRESH_INTERVAL: 30000, // 30 seconds
        PRICE_UPDATE_INTERVAL: 5000, // 5 seconds
        BALANCE_DECIMALS: 4,
        AMOUNT_DECIMALS: 6
    },
    
    // External Links
    LINKS: {
        ETHERSCAN_BASE: 'https://sepolia.etherscan.io',
        FAUCET: 'https://sepoliafaucet.com',
        DOCS: 'https://docs.uniswap.org',
        GITHUB: 'https://github.com/ubaswap'
    },
    
    // Error Messages
    ERRORS: {
        WALLET_NOT_CONNECTED: 'Silakan hubungkan dompet Anda terlebih dahulu',
        WRONG_NETWORK: 'Silakan beralih ke jaringan Sepolia',
        INSUFFICIENT_BALANCE: 'Saldo tidak mencukupi',
        INVALID_AMOUNT: 'Jumlah tidak valid',
        INVALID_ADDRESS: 'Alamat tidak valid',
        TRANSACTION_FAILED: 'Transaksi gagal',
        USER_REJECTED: 'Transaksi ditolak oleh pengguna',
        SLIPPAGE_TOO_HIGH: 'Slippage terlalu tinggi'
    },
    
    // Success Messages
    SUCCESS: {
        WALLET_CONNECTED: 'Dompet berhasil terhubung!',
        TRANSACTION_SENT: 'Transaksi berhasil dikirim!',
        SWAP_COMPLETED: 'Swap berhasil diselesaikan!',
        LIQUIDITY_ADDED: 'Likuiditas berhasil ditambahkan!',
        LIQUIDITY_REMOVED: 'Likuiditas berhasil dihapus!',
        FEES_CLAIMED: 'Fee berhasil diklaim!'
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// Global access
window.CONFIG = CONFIG;
