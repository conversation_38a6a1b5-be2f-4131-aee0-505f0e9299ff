import {
  BaseFeeScalarError,
  BlockNotFoundError,
  Eip1559FeesNotSupportedError,
  EstimateGasExecutionError,
  FilterTypeNotSupportedError,
  MaxFeePerGasTooLowError,
  UrlRequiredError,
  assertCurrent<PERSON>hain,
  assertTransactionEIP1559,
  assertTransactionEIP2930,
  assertTransactionLegacy,
  bytesToBigInt,
  bytesToBool,
  bytesToNumber,
  bytesToRlp,
  bytesToString,
  compactSignatureToHex,
  compactSignatureToSignature,
  createClient,
  createPublicClient,
  createTestClient,
  createTransport,
  createWalletClient,
  custom,
  decodeDeployData,
  decodeEventLog,
  decodeFunctionData,
  defineBlock,
  defineChain,
  defineTransaction,
  defineTransactionReceipt,
  domainSeparator,
  encodeDeployData,
  encodeErrorResult,
  encodeEventTopics,
  encodeFunctionResult,
  encodePacked,
  extract<PERSON>hain,
  fallback,
  formatBlock,
  formatLog,
  formatTransaction,
  formatTransactionReceipt,
  fromBytes,
  fromRlp,
  getContract,
  getContractAddress,
  getCreate2Address,
  getCreateAddress,
  getSerializedTransactionType,
  getTransactionType,
  getTypesForEIP712Domain,
  hashDomain,
  hashMessage,
  hashTypedData,
  hexToCompactSignature,
  hexToRlp,
  hexToSignature,
  http,
  isBytes,
  isHash,
  maxInt104,
  maxInt112,
  maxInt120,
  maxInt128,
  maxInt136,
  maxInt144,
  maxInt152,
  maxInt16,
  maxInt160,
  maxInt168,
  maxInt176,
  maxInt184,
  maxInt192,
  maxInt200,
  maxInt208,
  maxInt216,
  maxInt224,
  maxInt232,
  maxInt24,
  maxInt240,
  maxInt248,
  maxInt256,
  maxInt32,
  maxInt40,
  maxInt48,
  maxInt56,
  maxInt64,
  maxInt72,
  maxInt8,
  maxInt80,
  maxInt88,
  maxInt96,
  maxUint104,
  maxUint112,
  maxUint120,
  maxUint128,
  maxUint136,
  maxUint144,
  maxUint152,
  maxUint16,
  maxUint160,
  maxUint168,
  maxUint176,
  maxUint184,
  maxUint192,
  maxUint200,
  maxUint208,
  maxUint216,
  maxUint224,
  maxUint232,
  maxUint24,
  maxUint240,
  maxUint248,
  maxUint256,
  maxUint32,
  maxUint40,
  maxUint48,
  maxUint56,
  maxUint64,
  maxUint72,
  maxUint8,
  maxUint80,
  maxUint88,
  maxUint96,
  minInt104,
  minInt112,
  minInt120,
  minInt128,
  minInt136,
  minInt144,
  minInt152,
  minInt16,
  minInt160,
  minInt168,
  minInt176,
  minInt184,
  minInt192,
  minInt200,
  minInt208,
  minInt216,
  minInt224,
  minInt232,
  minInt24,
  minInt240,
  minInt248,
  minInt256,
  minInt32,
  minInt40,
  minInt48,
  minInt56,
  minInt64,
  minInt72,
  minInt8,
  minInt80,
  minInt88,
  minInt96,
  parseEther,
  parseGwei,
  parseTransaction,
  parseUnits,
  presignMessagePrefix,
  publicActions,
  recoverAddress,
  recoverMessageAddress,
  recoverPublicKey,
  recoverTypedDataAddress,
  ripemd160,
  serializeAccessList,
  serializeTransaction,
  sha256,
  signatureToCompactSignature,
  signatureToHex,
  testActions,
  toRlp,
  transactionType,
  validateTypedData,
  verifyMessage,
  verifyTypedData,
  walletActions,
  webSocket,
  zeroAddress,
  zeroHash
} from "./chunk-JDCTZMVO.js";
import {
  EnsAvatarInvalidNftUriError,
  EnsAvatarUnsupportedNamespaceError,
  EnsAvatarUriResolutionError,
  getContractError,
  labelhash,
  namehash
} from "./chunk-Z2Q63RMA.js";
import {
  ccipFetch,
  isAddressEqual,
  offchainLookup,
  offchainLookupAbiItem,
  offchainLookupSignature
} from "./chunk-P4NPJVRO.js";
import {
  AbiConstructorNotFoundError,
  AbiConstructorParamsNotFoundError,
  AbiDecodingDataSizeInvalidError,
  AbiDecodingDataSizeTooSmallError,
  AbiDecodingZeroDataError,
  AbiEncodingArrayLengthMismatchError,
  AbiEncodingBytesSizeMismatchError,
  AbiEncodingLengthMismatchError,
  AbiErrorInputsNotFoundError,
  AbiErrorNotFoundError,
  AbiErrorSignatureNotFoundError,
  AbiEventNotFoundError,
  AbiEventSignatureEmptyTopicsError,
  AbiEventSignatureNotFoundError,
  AbiFunctionNotFoundError,
  AbiFunctionOutputsNotFoundError,
  AbiFunctionSignatureNotFoundError,
  BaseError,
  BytesSizeMismatchError,
  CallExecutionError,
  ChainDisconnectedError,
  ChainDoesNotSupportContract,
  ChainMismatchError,
  ChainNotFoundError,
  CircularReferenceError,
  ClientChainNotConfiguredError,
  ContractFunctionExecutionError,
  ContractFunctionRevertedError,
  ContractFunctionZeroDataError,
  DataLengthTooLongError,
  DataLengthTooShortError,
  DecodeLogDataMismatch,
  DecodeLogTopicsMismatch,
  ExecutionRevertedError,
  FeeCapTooHighError,
  FeeCapTooLowError,
  FeeConflictError,
  HttpRequestError,
  InsufficientFundsError,
  IntegerOutOfRangeError,
  InternalRpcError,
  IntrinsicGasTooHighError,
  IntrinsicGasTooLowError,
  InvalidAbiDecodingTypeError,
  InvalidAbiEncodingTypeError,
  InvalidAbiItemError,
  InvalidAbiParameterError,
  InvalidAbiParametersError,
  InvalidAbiTypeParameterError,
  InvalidAddressError,
  InvalidArrayError,
  InvalidBytesBooleanError,
  InvalidChainIdError,
  InvalidDefinitionTypeError,
  InvalidFunctionModifierError,
  InvalidHexBooleanError,
  InvalidHexValueError,
  InvalidInputRpcError,
  InvalidLegacyVError,
  InvalidModifierError,
  InvalidParameterError,
  InvalidParamsRpcError,
  InvalidParenthesisError,
  InvalidRequestRpcError,
  InvalidSerializableTransactionError,
  InvalidSerializedTransactionError,
  InvalidSerializedTransactionTypeError,
  InvalidSignatureError,
  InvalidStorageKeySizeError,
  InvalidStructSignatureError,
  JsonRpcVersionUnsupportedError,
  LimitExceededRpcError,
  MethodNotFoundRpcError,
  MethodNotSupportedRpcError,
  NonceMaxValueError,
  NonceTooHighError,
  NonceTooLowError,
  OffsetOutOfBoundsError,
  ParseRpcError,
  ProviderDisconnectedError,
  ProviderRpcError,
  RawContractError,
  ResourceNotFoundRpcError,
  ResourceUnavailableRpcError,
  RpcError,
  RpcRequestError,
  SizeExceedsPaddingSizeError,
  SizeOverflowError,
  SliceOffsetOutOfBoundsError,
  SolidityProtectedKeywordError,
  SwitchChainError,
  TimeoutError,
  TipAboveFeeCapError,
  TransactionExecutionError,
  TransactionNotFoundError,
  TransactionReceiptNotFoundError,
  TransactionRejectedRpcError,
  TransactionTypeNotSupportedError,
  UnauthorizedProviderError,
  UnknownNodeError,
  UnknownRpcError,
  UnknownSignatureError,
  UnknownTypeError,
  UnsupportedPackedAbiType,
  UnsupportedProviderMethodError,
  UserRejectedRequestError,
  WaitForTransactionReceiptTimeoutError,
  WebSocketRequestError,
  assertRequest,
  boolToBytes,
  boolToHex,
  bytesToHex,
  checksumAddress,
  concat,
  concatBytes,
  concatHex,
  decodeAbiParameters,
  decodeErrorResult,
  decodeFunctionResult,
  defineTransactionRequest,
  encodeAbiParameters,
  encodeFunctionData,
  etherUnits,
  formatEther,
  formatGwei,
  formatTransactionRequest,
  formatUnits,
  fromHex,
  getAbiItem,
  getAddress,
  getChainContractAddress,
  getEventSelector,
  getEventSignature,
  getFunctionSelector,
  getFunctionSignature,
  gweiUnits,
  hexToBigInt,
  hexToBool,
  hexToBytes,
  hexToNumber,
  hexToString,
  isAddress,
  isHex,
  keccak256,
  multicall3Abi,
  numberToBytes,
  numberToHex,
  pad,
  padBytes,
  padHex,
  parseAbi,
  parseAbiItem,
  parseAbiParameter,
  parseAbiParameters,
  rpcTransactionType,
  size,
  slice,
  sliceBytes,
  sliceHex,
  stringToBytes,
  stringToHex,
  stringify,
  toBytes,
  toHex,
  trim,
  weiUnits
} from "./chunk-XLLWCG7E.js";
import "./chunk-W6I35MAG.js";
import "./chunk-2B3V2GUC.js";
import "./chunk-W7S2ME4R.js";
export {
  AbiConstructorNotFoundError,
  AbiConstructorParamsNotFoundError,
  AbiDecodingDataSizeInvalidError,
  AbiDecodingDataSizeTooSmallError,
  AbiDecodingZeroDataError,
  AbiEncodingArrayLengthMismatchError,
  AbiEncodingBytesSizeMismatchError,
  AbiEncodingLengthMismatchError,
  AbiErrorInputsNotFoundError,
  AbiErrorNotFoundError,
  AbiErrorSignatureNotFoundError,
  AbiEventNotFoundError,
  AbiEventSignatureEmptyTopicsError,
  AbiEventSignatureNotFoundError,
  AbiFunctionNotFoundError,
  AbiFunctionOutputsNotFoundError,
  AbiFunctionSignatureNotFoundError,
  BaseError,
  BaseFeeScalarError,
  BlockNotFoundError,
  BytesSizeMismatchError,
  CallExecutionError,
  ChainDisconnectedError,
  ChainDoesNotSupportContract,
  ChainMismatchError,
  ChainNotFoundError,
  CircularReferenceError,
  ClientChainNotConfiguredError,
  ContractFunctionExecutionError,
  ContractFunctionRevertedError,
  ContractFunctionZeroDataError,
  DataLengthTooLongError,
  DataLengthTooShortError,
  DecodeLogDataMismatch,
  DecodeLogTopicsMismatch,
  Eip1559FeesNotSupportedError,
  EnsAvatarInvalidNftUriError,
  EnsAvatarUnsupportedNamespaceError,
  EnsAvatarUriResolutionError,
  EstimateGasExecutionError,
  ExecutionRevertedError,
  FeeCapTooHighError,
  FeeCapTooLowError,
  FeeConflictError,
  FilterTypeNotSupportedError,
  HttpRequestError,
  InsufficientFundsError,
  IntegerOutOfRangeError,
  InternalRpcError,
  IntrinsicGasTooHighError,
  IntrinsicGasTooLowError,
  InvalidAbiDecodingTypeError,
  InvalidAbiEncodingTypeError,
  InvalidAbiItemError,
  InvalidAbiParameterError,
  InvalidAbiParametersError,
  InvalidAbiTypeParameterError,
  InvalidAddressError,
  InvalidArrayError,
  InvalidBytesBooleanError,
  InvalidChainIdError,
  InvalidDefinitionTypeError,
  InvalidFunctionModifierError,
  InvalidHexBooleanError,
  InvalidHexValueError,
  InvalidInputRpcError,
  InvalidLegacyVError,
  InvalidModifierError,
  InvalidParameterError,
  InvalidParamsRpcError,
  InvalidParenthesisError,
  InvalidRequestRpcError,
  InvalidSerializableTransactionError,
  InvalidSerializedTransactionError,
  InvalidSerializedTransactionTypeError,
  InvalidSignatureError,
  InvalidStorageKeySizeError,
  InvalidStructSignatureError,
  JsonRpcVersionUnsupportedError,
  LimitExceededRpcError,
  MaxFeePerGasTooLowError,
  MethodNotFoundRpcError,
  MethodNotSupportedRpcError,
  NonceMaxValueError,
  NonceTooHighError,
  NonceTooLowError,
  OffsetOutOfBoundsError,
  ParseRpcError,
  ProviderDisconnectedError,
  ProviderRpcError,
  RawContractError,
  ResourceNotFoundRpcError,
  ResourceUnavailableRpcError,
  RpcError,
  RpcRequestError,
  SizeExceedsPaddingSizeError,
  SizeOverflowError,
  SliceOffsetOutOfBoundsError,
  SolidityProtectedKeywordError,
  SwitchChainError,
  TimeoutError,
  TipAboveFeeCapError,
  TransactionExecutionError,
  TransactionNotFoundError,
  TransactionReceiptNotFoundError,
  TransactionRejectedRpcError,
  TransactionTypeNotSupportedError,
  UnauthorizedProviderError,
  UnknownNodeError,
  UnknownRpcError,
  UnknownSignatureError,
  UnknownTypeError,
  UnsupportedPackedAbiType,
  UnsupportedProviderMethodError,
  UrlRequiredError,
  UserRejectedRequestError,
  WaitForTransactionReceiptTimeoutError,
  WebSocketRequestError,
  assertCurrentChain,
  assertRequest,
  assertTransactionEIP1559,
  assertTransactionEIP2930,
  assertTransactionLegacy,
  boolToBytes,
  boolToHex,
  bytesToBigInt,
  bytesToBigInt as bytesToBigint,
  bytesToBool,
  bytesToHex,
  bytesToNumber,
  bytesToRlp,
  bytesToString,
  ccipFetch,
  checksumAddress,
  compactSignatureToHex,
  compactSignatureToSignature,
  concat,
  concatBytes,
  concatHex,
  createClient,
  createPublicClient,
  createTestClient,
  createTransport,
  createWalletClient,
  custom,
  decodeAbiParameters,
  decodeDeployData,
  decodeErrorResult,
  decodeEventLog,
  decodeFunctionData,
  decodeFunctionResult,
  defineBlock,
  defineChain,
  defineTransaction,
  defineTransactionReceipt,
  defineTransactionRequest,
  domainSeparator,
  encodeAbiParameters,
  encodeDeployData,
  encodeErrorResult,
  encodeEventTopics,
  encodeFunctionData,
  encodeFunctionResult,
  encodePacked,
  etherUnits,
  extractChain,
  fallback,
  formatBlock,
  formatEther,
  formatGwei,
  formatLog,
  formatTransaction,
  formatTransactionReceipt,
  formatTransactionRequest,
  formatUnits,
  fromBytes,
  fromHex,
  fromRlp,
  getAbiItem,
  getAddress,
  getChainContractAddress,
  getContract,
  getContractAddress,
  getContractError,
  getCreate2Address,
  getCreateAddress,
  getEventSelector,
  getEventSignature,
  getFunctionSelector,
  getFunctionSignature,
  getSerializedTransactionType,
  getTransactionType,
  getTypesForEIP712Domain,
  gweiUnits,
  hashDomain,
  hashMessage,
  hashTypedData,
  hexToBigInt,
  hexToBool,
  hexToBytes,
  hexToCompactSignature,
  hexToNumber,
  hexToRlp,
  hexToSignature,
  hexToString,
  http,
  isAddress,
  isAddressEqual,
  isBytes,
  isHash,
  isHex,
  keccak256,
  labelhash,
  maxInt104,
  maxInt112,
  maxInt120,
  maxInt128,
  maxInt136,
  maxInt144,
  maxInt152,
  maxInt16,
  maxInt160,
  maxInt168,
  maxInt176,
  maxInt184,
  maxInt192,
  maxInt200,
  maxInt208,
  maxInt216,
  maxInt224,
  maxInt232,
  maxInt24,
  maxInt240,
  maxInt248,
  maxInt256,
  maxInt32,
  maxInt40,
  maxInt48,
  maxInt56,
  maxInt64,
  maxInt72,
  maxInt8,
  maxInt80,
  maxInt88,
  maxInt96,
  maxUint104,
  maxUint112,
  maxUint120,
  maxUint128,
  maxUint136,
  maxUint144,
  maxUint152,
  maxUint16,
  maxUint160,
  maxUint168,
  maxUint176,
  maxUint184,
  maxUint192,
  maxUint200,
  maxUint208,
  maxUint216,
  maxUint224,
  maxUint232,
  maxUint24,
  maxUint240,
  maxUint248,
  maxUint256,
  maxUint32,
  maxUint40,
  maxUint48,
  maxUint56,
  maxUint64,
  maxUint72,
  maxUint8,
  maxUint80,
  maxUint88,
  maxUint96,
  minInt104,
  minInt112,
  minInt120,
  minInt128,
  minInt136,
  minInt144,
  minInt152,
  minInt16,
  minInt160,
  minInt168,
  minInt176,
  minInt184,
  minInt192,
  minInt200,
  minInt208,
  minInt216,
  minInt224,
  minInt232,
  minInt24,
  minInt240,
  minInt248,
  minInt256,
  minInt32,
  minInt40,
  minInt48,
  minInt56,
  minInt64,
  minInt72,
  minInt8,
  minInt80,
  minInt88,
  minInt96,
  multicall3Abi,
  namehash,
  numberToBytes,
  numberToHex,
  offchainLookup,
  offchainLookupAbiItem,
  offchainLookupSignature,
  pad,
  padBytes,
  padHex,
  parseAbi,
  parseAbiItem,
  parseAbiParameter,
  parseAbiParameters,
  parseEther,
  parseGwei,
  parseTransaction,
  parseUnits,
  presignMessagePrefix,
  publicActions,
  recoverAddress,
  recoverMessageAddress,
  recoverPublicKey,
  recoverTypedDataAddress,
  ripemd160,
  rpcTransactionType,
  serializeAccessList,
  serializeTransaction,
  sha256,
  signatureToCompactSignature,
  signatureToHex,
  size,
  slice,
  sliceBytes,
  sliceHex,
  stringToBytes,
  stringToHex,
  stringify,
  testActions,
  toBytes,
  toHex,
  toRlp,
  transactionType,
  trim,
  validateTypedData,
  verifyMessage,
  verifyTypedData,
  walletActions,
  webSocket,
  weiUnits,
  zeroAddress,
  zeroHash
};
//# sourceMappingURL=viem.js.map
