"use client";
import "./chunk-QE4SLGDL.js";
import {
  acala,
  arbitrum,
  arbitrumGoerli,
  arbitrumNova,
  arbitrumSepolia,
  astar,
  astarZkatana,
  aurora,
  auroraTestnet,
  avalanche,
  avalancheFuji,
  bahamut,
  base,
  baseGoerli,
  baseSepolia,
  bearNetworkChainMainnet,
  bearNetworkChainTestnet,
  bitTorrent,
  bitTorrentTestnet,
  boba,
  bronos,
  bronosTestnet,
  bsc,
  bscTestnet,
  bxn,
  bxnTestnet,
  canto,
  celo,
  celoAlfajores,
  celoCannoli,
  chiliz,
  classic,
  confluxESpace,
  confluxESpaceTestnet,
  coreDao,
  cronos,
  cronosTestnet,
  crossbell,
  dfk,
  dogechain,
  edgeware,
  edgewareTestnet,
  ekta,
  ektaTestnet,
  eos,
  eosTestnet,
  evmos,
  evmosTestnet,
  fantom,
  fantomSonicTestnet,
  fantomTestnet,
  fibo,
  filecoin,
  filecoinCalibration,
  filecoinHyperspace,
  flare,
  flareTestnet,
  foundry,
  fuse,
  fuseSparknet,
  gnosis,
  gnosisChiado,
  gobi,
  goerli,
  haqqMainnet,
  haqqTestedge2,
  hardhat,
  harmonyOne,
  holesky,
  iotex,
  iotexTestnet,
  jbc,
  karura,
  kava,
  kavaTestnet,
  klaytn,
  klaytnBaobab,
  kroma,
  kromaSepolia,
  linea,
  lineaTestnet,
  localhost,
  lukso,
  mainnet,
  mandala,
  manta,
  mantaTestnet,
  mantle,
  mantleTestnet,
  meter,
  meterTestnet,
  metis,
  metisGoerli,
  mev,
  mevTestnet,
  modeTestnet,
  moonbaseAlpha,
  moonbeam,
  moonbeamDev,
  moonriver,
  neonDevnet,
  neonMainnet,
  nexi,
  nexilix,
  oasisTestnet,
  oasys,
  okc,
  opBNB,
  opBNBTestnet,
  optimism,
  optimismGoerli,
  optimismSepolia,
  pgn,
  pgnTestnet,
  plinga,
  polygon,
  polygonMumbai,
  polygonZkEvm,
  polygonZkEvmTestnet,
  pulsechain,
  pulsechainV4,
  qMainnet,
  qTestnet,
  rollux,
  rolluxTestnet,
  ronin,
  rootstock,
  saigon,
  sapphire,
  sapphireTestnet,
  scroll,
  scrollSepolia,
  scrollTestnet,
  sepolia,
  shardeumSphinx,
  shibarium,
  shimmer,
  shimmerTestnet,
  skaleBlockBrawlers,
  skaleCalypso,
  skaleCalypsoTestnet,
  skaleChaosTestnet,
  skaleCryptoBlades,
  skaleCryptoColosseum,
  skaleEuropa,
  skaleEuropaTestnet,
  skaleExorde,
  skaleHumanProtocol,
  skaleNebula,
  skaleNebulaTestnet,
  skaleRazor,
  skaleTitan,
  skaleTitanTestnet,
  songbird,
  songbirdTestnet,
  spicy,
  syscoin,
  syscoinTestnet,
  taikoJolnir,
  taikoTestnetSepolia,
  taraxa,
  taraxaTestnet,
  telos,
  telosTestnet,
  tenet,
  thunderTestnet,
  vechain,
  wanchain,
  wanchainTestnet,
  wemix,
  wemixTestnet,
  xdc,
  xdcTestnet,
  zetachainAthensTestnet,
  zhejiang,
  zilliqa,
  zilliqaTestnet,
  zkFair,
  zkFairTestnet,
  zkSync,
  zkSyncSepoliaTestnet,
  zkSyncTestnet,
  zora,
  zoraSepolia,
  zoraTestnet
} from "./chunk-5EBGCFML.js";
import "./chunk-JDCTZMVO.js";
import "./chunk-Z2Q63RMA.js";
import "./chunk-P4NPJVRO.js";
import "./chunk-XLLWCG7E.js";
import "./chunk-W6I35MAG.js";
import "./chunk-2B3V2GUC.js";
import "./chunk-W7S2ME4R.js";
export {
  acala,
  arbitrum,
  arbitrumGoerli,
  arbitrumNova,
  arbitrumSepolia,
  astar,
  astarZkatana,
  aurora,
  auroraTestnet,
  avalanche,
  avalancheFuji,
  bahamut,
  base,
  baseGoerli,
  baseSepolia,
  bearNetworkChainMainnet,
  bearNetworkChainTestnet,
  bitTorrent,
  bitTorrentTestnet,
  boba,
  bronos,
  bronosTestnet,
  bsc,
  bscTestnet,
  bxn,
  bxnTestnet,
  canto,
  celo,
  celoAlfajores,
  celoCannoli,
  chiliz,
  classic,
  confluxESpace,
  confluxESpaceTestnet,
  coreDao,
  cronos,
  cronosTestnet,
  crossbell,
  dfk,
  dogechain,
  edgeware,
  edgewareTestnet,
  ekta,
  ektaTestnet,
  eos,
  eosTestnet,
  evmos,
  evmosTestnet,
  fantom,
  fantomSonicTestnet,
  fantomTestnet,
  fibo,
  filecoin,
  filecoinCalibration,
  filecoinHyperspace,
  flare,
  flareTestnet,
  foundry,
  fuse,
  fuseSparknet,
  gnosis,
  gnosisChiado,
  gobi,
  goerli,
  haqqMainnet,
  haqqTestedge2,
  hardhat,
  harmonyOne,
  holesky,
  iotex,
  iotexTestnet,
  jbc,
  karura,
  kava,
  kavaTestnet,
  klaytn,
  klaytnBaobab,
  kroma,
  kromaSepolia,
  linea,
  lineaTestnet,
  localhost,
  lukso,
  mainnet,
  mandala,
  manta,
  mantaTestnet,
  mantle,
  mantleTestnet,
  meter,
  meterTestnet,
  metis,
  metisGoerli,
  mev,
  mevTestnet,
  modeTestnet,
  moonbaseAlpha,
  moonbeam,
  moonbeamDev,
  moonriver,
  neonDevnet,
  neonMainnet,
  nexi,
  nexilix,
  oasisTestnet,
  oasys,
  okc,
  opBNB,
  opBNBTestnet,
  optimism,
  optimismGoerli,
  optimismSepolia,
  pgn,
  pgnTestnet,
  plinga,
  polygon,
  polygonMumbai,
  polygonZkEvm,
  polygonZkEvmTestnet,
  pulsechain,
  pulsechainV4,
  qMainnet,
  qTestnet,
  rollux,
  rolluxTestnet,
  ronin,
  rootstock,
  saigon,
  sapphire,
  sapphireTestnet,
  scroll,
  scrollSepolia,
  scrollTestnet,
  sepolia,
  shardeumSphinx,
  shibarium,
  shimmer,
  shimmerTestnet,
  skaleBlockBrawlers,
  skaleCalypso,
  skaleCalypsoTestnet,
  skaleChaosTestnet,
  skaleCryptoBlades,
  skaleCryptoColosseum,
  skaleEuropa,
  skaleEuropaTestnet,
  skaleExorde,
  skaleHumanProtocol,
  skaleNebula,
  skaleNebulaTestnet,
  skaleRazor,
  skaleTitan,
  skaleTitanTestnet,
  songbird,
  songbirdTestnet,
  spicy,
  syscoin,
  syscoinTestnet,
  taikoJolnir,
  taikoTestnetSepolia,
  taraxa,
  taraxaTestnet,
  telos,
  telosTestnet,
  tenet,
  thunderTestnet,
  vechain,
  wanchain,
  wanchainTestnet,
  wemix,
  wemixTestnet,
  xdc,
  xdcTestnet,
  zetachainAthensTestnet,
  zhejiang,
  zilliqa,
  zilliqaTestnet,
  zkFair,
  zkFairTestnet,
  zkSync,
  zkSyncSepoliaTestnet,
  zkSyncTestnet,
  zora,
  zoraSepolia,
  zoraTestnet
};
//# sourceMappingURL=wagmi_chains.js.map
