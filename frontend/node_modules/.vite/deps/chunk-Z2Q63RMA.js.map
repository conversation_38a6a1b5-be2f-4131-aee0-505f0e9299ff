{"version": 3, "sources": ["../../viem/utils/errors/getContractError.ts", "../../viem/utils/ens/encodedLabelToLabelhash.ts", "../../viem/utils/ens/namehash.ts", "../../viem/utils/ens/labelhash.ts", "../../viem/utils/ens/errors.ts", "../../viem/utils/ens/encodeLabelhash.ts", "../../viem/utils/ens/packetToBytes.ts", "../../viem/utils/getAction.ts", "../../viem/actions/public/readContract.ts", "../../viem/actions/ens/getEnsAddress.ts", "../../viem/errors/ens.ts", "../../viem/actions/ens/getEnsText.ts", "../../viem/utils/ens/avatar/utils.ts", "../../viem/utils/ens/avatar/parseAvatarRecord.ts", "../../viem/actions/ens/getEnsAvatar.ts", "../../viem/actions/ens/getEnsName.ts", "../../viem/actions/ens/getEnsResolver.ts"], "sourcesContent": ["import type { Abi, Address } from 'abitype'\n\nimport { AbiDecodingZeroDataError } from '../../errors/abi.js'\nimport { BaseError } from '../../errors/base.js'\nimport {\n  ContractFunctionExecutionError,\n  type ContractFunctionExecutionErrorType,\n  ContractFunctionRevertedError,\n  type ContractFunctionRevertedErrorType,\n  ContractFunctionZeroDataError,\n  type ContractFunctionZeroDataErrorType,\n  RawContractError,\n} from '../../errors/contract.js'\nimport { InternalRpcError } from '../../errors/rpc.js'\nimport type { ErrorType } from '../../errors/utils.js'\n\nconst EXECUTION_REVERTED_ERROR_CODE = 3\n\nexport type GetContractErrorReturnType<cause = ErrorType> = Omit<\n  ContractFunctionExecutionErrorType,\n  'cause'\n> & {\n  cause:\n    | cause\n    | ContractFunctionZeroDataErrorType\n    | ContractFunctionRevertedErrorType\n}\n\nexport function getContractError<err extends ErrorType<string>>(\n  err: err,\n  {\n    abi,\n    address,\n    args,\n    docsPath,\n    functionName,\n    sender,\n  }: {\n    abi: Abi\n    args: any\n    address?: Address\n    docsPath?: string\n    functionName: string\n    sender?: Address\n  },\n): GetContractErrorReturnType {\n  const { code, data, message, shortMessage } = (\n    err instanceof RawContractError\n      ? err\n      : err instanceof BaseError\n        ? err.walk((err) => 'data' in (err as Error)) || err.walk()\n        : {}\n  ) as RawContractError\n\n  const cause = (() => {\n    if (err instanceof AbiDecodingZeroDataError)\n      return new ContractFunctionZeroDataError({ functionName })\n    if (\n      [EXECUTION_REVERTED_ERROR_CODE, InternalRpcError.code].includes(code) &&\n      (data || message || shortMessage)\n    ) {\n      return new ContractFunctionRevertedError({\n        abi,\n        data: typeof data === 'object' ? data.data : data,\n        functionName,\n        message: shortMessage ?? message,\n      })\n    }\n    return err\n  })()\n\n  return new ContractFunctionExecutionError(cause as BaseError, {\n    abi,\n    args,\n    contractAddress: address,\n    docsPath,\n    functionName,\n    sender,\n  }) as GetContractErrorReturnType\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport { type IsHexErrorType, isHex } from '../data/isHex.js'\n\nexport type EncodedLabelToLabelhashErrorType = IsHexErrorType | ErrorType\n\nexport function encodedLabelToLabelhash(label: string): Hex | null {\n  if (label.length !== 66) return null\n  if (label.indexOf('[') !== 0) return null\n  if (label.indexOf(']') !== 65) return null\n  const hash = `0x${label.slice(1, 65)}`\n  if (!isHex(hash)) return null\n  return hash\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport { type ConcatErrorType, concat } from '../data/concat.js'\nimport {\n  type StringToBytesErrorType,\n  type ToBytesErrorType,\n  stringToBytes,\n  toBytes,\n} from '../encoding/toBytes.js'\nimport { type BytesToHexErrorType, bytesToHex } from '../encoding/toHex.js'\nimport { type Keccak256ErrorType, keccak256 } from '../hash/keccak256.js'\nimport {\n  type EncodedLabelToLabelhashErrorType,\n  encodedLabelToLabelhash,\n} from './encodedLabelToLabelhash.js'\n\nexport type NamehashErrorType =\n  | BytesToHexErrorType\n  | EncodedLabelToLabelhashErrorType\n  | ToBytesErrorType\n  | Keccak256ErrorType\n  | StringToBytesErrorType\n  | ConcatErrorType\n  | ErrorType\n\n/**\n * @description Hashes ENS name\n *\n * - Since ENS names prohibit certain forbidden characters (e.g. underscore) and have other validation rules, you likely want to [normalize ENS names](https://docs.ens.domains/contract-api-reference/name-processing#normalising-names) with [UTS-46 normalization](https://unicode.org/reports/tr46) before passing them to `namehash`. You can use the built-in [`normalize`](https://viem.sh/docs/ens/utilities/normalize.html) function for this.\n *\n * @example\n * namehash('wevm.eth')\n * '0xf246651c1b9a6b141d19c2604e9a58f567973833990f830d882534a747801359'\n *\n * @link https://eips.ethereum.org/EIPS/eip-137\n */\nexport function namehash(name: string) {\n  let result = new Uint8Array(32).fill(0)\n  if (!name) return bytesToHex(result)\n\n  const labels = name.split('.')\n  // Iterate in reverse order building up hash\n  for (let i = labels.length - 1; i >= 0; i -= 1) {\n    const hashFromEncodedLabel = encodedLabelToLabelhash(labels[i])\n    const hashed = hashFromEncodedLabel\n      ? toBytes(hashFromEncodedLabel)\n      : keccak256(stringToBytes(labels[i]), 'bytes')\n    result = keccak256(concat([result, hashed]), 'bytes')\n  }\n\n  return bytesToHex(result)\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport {\n  type StringToBytesErrorType,\n  stringToBytes,\n} from '../encoding/toBytes.js'\nimport { type BytesToHexErrorType, bytesToHex } from '../encoding/toHex.js'\nimport { type Keccak256ErrorType, keccak256 } from '../hash/keccak256.js'\nimport {\n  type EncodedLabelToLabelhashErrorType,\n  encodedLabelToLabelhash,\n} from './encodedLabelToLabelhash.js'\n\nexport type LabelhashErrorType =\n  | BytesToHexErrorType\n  | EncodedLabelToLabelhashErrorType\n  | Keccak256ErrorType\n  | StringToBytesErrorType\n  | ErrorType\n\n/**\n * @description Hashes ENS label\n *\n * - Since ENS labels prohibit certain forbidden characters (e.g. underscore) and have other validation rules, you likely want to [normalize ENS labels](https://docs.ens.domains/contract-api-reference/name-processing#normalising-names) with [UTS-46 normalization](https://unicode.org/reports/tr46) before passing them to `labelhash`. You can use the built-in [`normalize`](https://viem.sh/docs/ens/utilities/normalize.html) function for this.\n *\n * @example\n * labelhash('eth')\n * '0x4f5b812789fc606be1b3b16908db13fc7a9adf7ca72641f84d75b47069d3d7f0'\n */\nexport function labelhash(label: string) {\n  const result = new Uint8Array(32).fill(0)\n  if (!label) return bytesToHex(result)\n  return encodedLabelToLabelhash(label) || keccak256(stringToBytes(label))\n}\n", "import { panicReasons } from '../../constants/solidity.js'\nimport { BaseError } from '../../errors/base.js'\nimport { ContractFunctionRevertedError } from '../../errors/contract.js'\nimport type { ErrorType } from '../../errors/utils.js'\n\nexport type IsNullUniversalResolverErrorErrorType = ErrorType\n\n/*\n * @description Checks if error is a valid null result UniversalResolver error\n */\nexport function isNullUniversalResolverError(\n  err: unknown,\n  callType: 'resolve' | 'reverse',\n): boolean {\n  if (!(err instanceof BaseError)) return false\n  const cause = err.walk((e) => e instanceof ContractFunctionRevertedError)\n  if (!(cause instanceof ContractFunctionRevertedError)) return false\n  if (cause.data?.errorName === 'ResolverNotFound') return true\n  if (cause.data?.errorName === 'ResolverWildcardNotSupported') return true\n  // Backwards compatibility for older UniversalResolver contracts\n  if (\n    cause.reason?.includes(\n      'Wildcard on non-extended resolvers is not supported',\n    )\n  )\n    return true\n  // No primary name set for address.\n  if (callType === 'reverse' && cause.reason === panicReasons[50]) return true\n  return false\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\n\nexport type EncodeLabelhashErrorType = ErrorType\n\nexport function encodeLabelhash(hash: Hex): `[${string}]` {\n  return `[${hash.slice(2)}]`\n}\n", "// Adapted from https://github.com/mafintosh/dns-packet\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray } from '../../types/misc.js'\nimport {\n  type StringToBytesErrorType,\n  stringToBytes,\n} from '../encoding/toBytes.js'\nimport {\n  type EncodeLabelhashErrorType,\n  encodeLabelhash,\n} from './encodeLabelhash.js'\nimport { type LabelhashErrorType, labelhash } from './labelhash.js'\n\nexport type PacketToBytesErrorType =\n  | EncodeLabelhashErrorType\n  | LabelhashErrorType\n  | StringToBytesErrorType\n  | ErrorType\n\n/*\n * @description Encodes a DNS packet into a ByteArray containing a UDP payload.\n */\nexport function packetToBytes(packet: string): ByteArray {\n  // strip leading and trailing `.`\n  const value = packet.replace(/^\\.|\\.$/gm, '')\n  if (value.length === 0) return new Uint8Array(1)\n\n  const bytes = new Uint8Array(stringToBytes(value).byteLength + 2)\n\n  let offset = 0\n  const list = value.split('.')\n  for (let i = 0; i < list.length; i++) {\n    let encoded = stringToBytes(list[i])\n    // if the length is > 255, make the encoded label value a labelhash\n    // this is compatible with the universal resolver\n    if (encoded.byteLength > 255)\n      encoded = stringToBytes(encodeLabelhash(labelhash(list[i])))\n    bytes[offset] = encoded.length\n    bytes.set(encoded, offset + 1)\n    offset += encoded.length + 1\n  }\n\n  if (bytes.byteLength !== offset + 1) return bytes.slice(0, offset + 1)\n\n  return bytes\n}\n", "import type { Client } from '../clients/createClient.js'\n\n/**\n * Retrieves and returns an action from the client (if exists), and falls\n * back to the tree-shakable action.\n *\n * Useful for extracting overridden actions from a client (ie. if a consumer\n * wants to override the `sendTransaction` implementation).\n */\nexport function getAction<params extends {}, returnType extends {}>(\n  client: Client,\n  action: (_: any, params: params) => returnType,\n  // Some minifiers drop `Function.prototype.name`, meaning that `action.name`\n  // will not work. For that case, the consumer needs to pass the name explicitly.\n  name: string,\n) {\n  return (params: params): returnType =>\n    (\n      client as Client & {\n        [key: string]: (params: params) => returnType\n      }\n    )[action.name || name]?.(params) ?? action(client, params)\n}\n", "import type { Abi } from 'abitype'\n\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport type { BaseError } from '../../errors/base.js'\nimport type { Chain } from '../../types/chain.js'\nimport type {\n  ContractFunctionConfig,\n  ContractFunctionResult,\n} from '../../types/contract.js'\nimport {\n  type DecodeFunctionResultErrorType,\n  type DecodeFunctionResultParameters,\n  decodeFunctionResult,\n} from '../../utils/abi/decodeFunctionResult.js'\nimport {\n  type EncodeFunctionDataErrorType,\n  type EncodeFunctionDataParameters,\n  encodeFunctionData,\n} from '../../utils/abi/encodeFunctionData.js'\nimport {\n  type GetContractErrorReturnType,\n  getContractError,\n} from '../../utils/errors/getContractError.js'\nimport { getAction } from '../../utils/getAction.js'\n\nimport { type CallErrorType, type CallParameters, call } from './call.js'\n\nexport type ReadContractParameters<\n  TAbi extends Abi | readonly unknown[] = Abi,\n  TFunctionName extends string = string,\n> = Pick<CallParameters, 'account' | 'blockNumber' | 'blockTag'> &\n  ContractFunctionConfig<TAbi, TFunctionName, 'view' | 'pure'>\n\nexport type ReadContractReturnType<\n  TAbi extends Abi | readonly unknown[] = Abi,\n  TFunctionName extends string = string,\n> = ContractFunctionResult<TAbi, TFunctionName>\n\nexport type ReadContractErrorType = GetContractErrorReturnType<\n  CallErrorType | EncodeFunctionDataErrorType | DecodeFunctionResultErrorType\n>\n\n/**\n * Calls a read-only function on a contract, and returns the response.\n *\n * - Docs: https://viem.sh/docs/contract/readContract.html\n * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/contracts/reading-contracts\n *\n * A \"read-only\" function (constant function) on a Solidity contract is denoted by a `view` or `pure` keyword. They can only read the state of the contract, and cannot make any changes to it. Since read-only methods do not change the state of the contract, they do not require any gas to be executed, and can be called by any user without the need to pay for gas.\n *\n * Internally, uses a [Public Client](https://viem.sh/docs/clients/public.html) to call the [`call` action](https://viem.sh/docs/actions/public/call.html) with [ABI-encoded `data`](https://viem.sh/docs/contract/encodeFunctionData.html).\n *\n * @param client - Client to use\n * @param parameters - {@link ReadContractParameters}\n * @returns The response from the contract. Type is inferred. {@link ReadContractReturnType}\n *\n * @example\n * import { createPublicClient, http, parseAbi } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { readContract } from 'viem/contract'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const result = await readContract(client, {\n *   address: '******************************************',\n *   abi: parseAbi(['function balanceOf(address) view returns (uint256)']),\n *   functionName: 'balanceOf',\n *   args: ['0xA0Cf798816D4b9b9866b5330EEa46a18382f251e'],\n * })\n * // 424122n\n */\nexport async function readContract<\n  TChain extends Chain | undefined,\n  const TAbi extends Abi | readonly unknown[],\n  TFunctionName extends string,\n>(\n  client: Client<Transport, TChain>,\n  {\n    abi,\n    address,\n    args,\n    functionName,\n    ...callRequest\n  }: ReadContractParameters<TAbi, TFunctionName>,\n): Promise<ReadContractReturnType<TAbi, TFunctionName>> {\n  const calldata = encodeFunctionData({\n    abi,\n    args,\n    functionName,\n  } as unknown as EncodeFunctionDataParameters<TAbi, TFunctionName>)\n  try {\n    const { data } = await getAction(\n      client,\n      call,\n      'call',\n    )({\n      data: calldata,\n      to: address,\n      ...callRequest,\n    } as unknown as CallParameters)\n    return decodeFunctionResult({\n      abi,\n      args,\n      functionName,\n      data: data || '0x',\n    } as DecodeFunctionResultParameters<\n      TAbi,\n      TFunctionName\n    >) as ReadContractReturnType<TAbi, TFunctionName>\n  } catch (err) {\n    throw getContractError(err as BaseError, {\n      abi: abi as Abi,\n      address,\n      args,\n      docsPath: '/docs/contract/readContract',\n      functionName,\n    })\n  }\n}\n", "import type { Address } from 'abitype'\n\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport {\n  addressResolverAbi,\n  universalResolverResolveAbi,\n} from '../../constants/abis.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { Prettify } from '../../types/utils.js'\nimport {\n  type DecodeFunctionResultErrorType,\n  decodeFunctionResult,\n} from '../../utils/abi/decodeFunctionResult.js'\nimport {\n  type EncodeFunctionDataErrorType,\n  encodeFunctionData,\n} from '../../utils/abi/encodeFunctionData.js'\nimport {\n  type GetChainContractAddressErrorType,\n  getChainContractAddress,\n} from '../../utils/chain/getChainContractAddress.js'\nimport { type TrimErrorType, trim } from '../../utils/data/trim.js'\nimport { type ToHexErrorType, toHex } from '../../utils/encoding/toHex.js'\nimport { isNullUniversalResolverError } from '../../utils/ens/errors.js'\nimport { type NamehashErrorType, namehash } from '../../utils/ens/namehash.js'\nimport {\n  type PacketToBytesErrorType,\n  packetToBytes,\n} from '../../utils/ens/packetToBytes.js'\nimport { getAction } from '../../utils/getAction.js'\nimport {\n  type ReadContractParameters,\n  readContract,\n} from '../public/readContract.js'\n\nexport type GetEnsAddressParameters = Prettify<\n  Pick<ReadContractParameters, 'blockNumber' | 'blockTag'> & {\n    /** ENSIP-9 compliant coinType used to resolve addresses for other chains */\n    coinType?: number\n    /** Name to get the address for. */\n    name: string\n    /** Address of ENS Universal Resolver Contract. */\n    universalResolverAddress?: Address\n  }\n>\n\nexport type GetEnsAddressReturnType = Address | null\n\nexport type GetEnsAddressErrorType =\n  | GetChainContractAddressErrorType\n  | EncodeFunctionDataErrorType\n  | NamehashErrorType\n  | ToHexErrorType\n  | PacketToBytesErrorType\n  | DecodeFunctionResultErrorType\n  | TrimErrorType\n  | ErrorType\n\n/**\n * Gets address for ENS name.\n *\n * - Docs: https://viem.sh/docs/ens/actions/getEnsAddress.html\n * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/ens\n *\n * Calls `resolve(bytes, bytes)` on ENS Universal Resolver Contract.\n *\n * Since ENS names prohibit certain forbidden characters (e.g. underscore) and have other validation rules, you likely want to [normalize ENS names](https://docs.ens.domains/contract-api-reference/name-processing#normalising-names) with [UTS-46 normalization](https://unicode.org/reports/tr46) before passing them to `getEnsAddress`. You can use the built-in [`normalize`](https://viem.sh/docs/ens/utilities/normalize.html) function for this.\n *\n * @param client - Client to use\n * @param parameters - {@link GetEnsAddressParameters}\n * @returns Address for ENS name or `null` if not found. {@link GetEnsAddressReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { getEnsAddress, normalize } from 'viem/ens'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const ensAddress = await getEnsAddress(client, {\n *   name: normalize('wevm.eth'),\n * })\n * // '******************************************'\n */\nexport async function getEnsAddress<TChain extends Chain | undefined>(\n  client: Client<Transport, TChain>,\n  {\n    blockNumber,\n    blockTag,\n    coinType,\n    name,\n    universalResolverAddress: universalResolverAddress_,\n  }: GetEnsAddressParameters,\n): Promise<GetEnsAddressReturnType> {\n  let universalResolverAddress = universalResolverAddress_\n  if (!universalResolverAddress) {\n    if (!client.chain)\n      throw new Error(\n        'client chain not configured. universalResolverAddress is required.',\n      )\n\n    universalResolverAddress = getChainContractAddress({\n      blockNumber,\n      chain: client.chain,\n      contract: 'ensUniversalResolver',\n    })\n  }\n\n  try {\n    const functionData = encodeFunctionData({\n      abi: addressResolverAbi,\n      functionName: 'addr',\n      ...(coinType != null\n        ? { args: [namehash(name), BigInt(coinType)] }\n        : { args: [namehash(name)] }),\n    })\n\n    const res = await getAction(\n      client,\n      readContract,\n      'readContract',\n    )({\n      address: universalResolverAddress,\n      abi: universalResolverResolveAbi,\n      functionName: 'resolve',\n      args: [toHex(packetToBytes(name)), functionData],\n      blockNumber,\n      blockTag,\n    })\n\n    if (res[0] === '0x') return null\n\n    const address = decodeFunctionResult({\n      abi: addressResolverAbi,\n      args: coinType != null ? [namehash(name), BigInt(coinType)] : undefined,\n      functionName: 'addr',\n      data: res[0],\n    })\n\n    if (address === '0x') return null\n    if (trim(address) === '0x00') return null\n    return address\n  } catch (err) {\n    if (isNullUniversalResolverError(err, 'resolve')) return null\n    throw err\n  }\n}\n", "import { BaseError } from './base.js'\n\nexport type EnsAvatarInvalidMetadataErrorType =\n  EnsAvatarInvalidMetadataError & {\n    name: 'EnsAvatarInvalidMetadataError'\n  }\nexport class EnsAvatarInvalidMetadataError extends BaseError {\n  override name = 'EnsAvatarInvalidMetadataError'\n  constructor({ data }: { data: any }) {\n    super(\n      'Unable to extract image from metadata. The metadata may be malformed or invalid.',\n      {\n        metaMessages: [\n          '- Metadata must be a JSON object with at least an `image`, `image_url` or `image_data` property.',\n          '',\n          `Provided data: ${JSON.stringify(data)}`,\n        ],\n      },\n    )\n  }\n}\n\nexport type EnsAvatarInvalidNftUriErrorType = EnsAvatarInvalidNftUriError & {\n  name: 'EnsAvatarInvalidNftUriError'\n}\nexport class EnsAvatarInvalidNftUriError extends BaseError {\n  override name = 'EnsAvatarInvalidNftUriError'\n  constructor({ reason }: { reason: string }) {\n    super(`ENS NFT avatar URI is invalid. ${reason}`)\n  }\n}\n\nexport type EnsAvatarUriResolutionErrorType = EnsAvatarUriResolutionError & {\n  name: 'EnsAvatarUriResolutionError'\n}\nexport class EnsAvatarUriResolutionError extends BaseError {\n  override name = 'EnsAvatarUriResolutionError'\n  constructor({ uri }: { uri: string }) {\n    super(\n      `Unable to resolve ENS avatar URI \"${uri}\". The URI may be malformed, invalid, or does not respond with a valid image.`,\n    )\n  }\n}\n\nexport type EnsAvatarUnsupportedNamespaceErrorType =\n  EnsAvatarUnsupportedNamespaceError & {\n    name: 'EnsAvatarUnsupportedNamespaceError'\n  }\nexport class EnsAvatarUnsupportedNamespaceError extends BaseError {\n  override name = 'EnsAvatarUnsupportedNamespaceError'\n  constructor({ namespace }: { namespace: string }) {\n    super(\n      `ENS NFT avatar namespace \"${namespace}\" is not supported. Must be \"erc721\" or \"erc1155\".`,\n    )\n  }\n}\n", "import type { Address } from 'abitype'\n\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport {\n  textResolverAbi,\n  universalResolverResolveAbi,\n} from '../../constants/abis.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { Prettify } from '../../types/utils.js'\nimport {\n  type DecodeFunctionResultErrorType,\n  decodeFunctionResult,\n} from '../../utils/abi/decodeFunctionResult.js'\nimport {\n  type EncodeFunctionDataErrorType,\n  encodeFunctionData,\n} from '../../utils/abi/encodeFunctionData.js'\nimport {\n  type GetChainContractAddressErrorType,\n  getChainContractAddress,\n} from '../../utils/chain/getChainContractAddress.js'\nimport { type ToHexErrorType, toHex } from '../../utils/encoding/toHex.js'\nimport { isNullUniversalResolverError } from '../../utils/ens/errors.js'\nimport { type NamehashErrorType, namehash } from '../../utils/ens/namehash.js'\nimport {\n  type PacketToBytesErrorType,\n  packetToBytes,\n} from '../../utils/ens/packetToBytes.js'\nimport { getAction } from '../../utils/getAction.js'\nimport {\n  type ReadContractErrorType,\n  type ReadContractParameters,\n  readContract,\n} from '../public/readContract.js'\n\nexport type GetEnsTextParameters = Prettify<\n  Pick<ReadContractParameters, 'blockNumber' | 'blockTag'> & {\n    /** ENS name to get Text for. */\n    name: string\n    /** Text record to retrieve. */\n    key: string\n    /** Address of ENS Universal Resolver Contract. */\n    universalResolverAddress?: Address\n  }\n>\n\nexport type GetEnsTextReturnType = string | null\n\nexport type GetEnsTextErrorType =\n  | GetChainContractAddressErrorType\n  | ReadContractErrorType\n  | ToHexErrorType\n  | PacketToBytesErrorType\n  | EncodeFunctionDataErrorType\n  | NamehashErrorType\n  | DecodeFunctionResultErrorType\n\n/**\n * Gets a text record for specified ENS name.\n *\n * - Docs: https://viem.sh/docs/ens/actions/getEnsResolver.html\n * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/ens\n *\n * Calls `resolve(bytes, bytes)` on ENS Universal Resolver Contract.\n *\n * Since ENS names prohibit certain forbidden characters (e.g. underscore) and have other validation rules, you likely want to [normalize ENS names](https://docs.ens.domains/contract-api-reference/name-processing#normalising-names) with [UTS-46 normalization](https://unicode.org/reports/tr46) before passing them to `getEnsAddress`. You can use the built-in [`normalize`](https://viem.sh/docs/ens/utilities/normalize.html) function for this.\n *\n * @param client - Client to use\n * @param parameters - {@link GetEnsTextParameters}\n * @returns Address for ENS resolver. {@link GetEnsTextReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { getEnsText, normalize } from 'viem/ens'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const twitterRecord = await getEnsText(client, {\n *   name: normalize('wevm.eth'),\n *   key: 'com.twitter',\n * })\n * // 'wagmi_sh'\n */\nexport async function getEnsText<TChain extends Chain | undefined>(\n  client: Client<Transport, TChain>,\n  {\n    blockNumber,\n    blockTag,\n    name,\n    key,\n    universalResolverAddress: universalResolverAddress_,\n  }: GetEnsTextParameters,\n): Promise<GetEnsTextReturnType> {\n  let universalResolverAddress = universalResolverAddress_\n  if (!universalResolverAddress) {\n    if (!client.chain)\n      throw new Error(\n        'client chain not configured. universalResolverAddress is required.',\n      )\n\n    universalResolverAddress = getChainContractAddress({\n      blockNumber,\n      chain: client.chain,\n      contract: 'ensUniversalResolver',\n    })\n  }\n\n  try {\n    const res = await getAction(\n      client,\n      readContract,\n      'readContract',\n    )({\n      address: universalResolverAddress,\n      abi: universalResolverResolveAbi,\n      functionName: 'resolve',\n      args: [\n        toHex(packetToBytes(name)),\n        encodeFunctionData({\n          abi: textResolverAbi,\n          functionName: 'text',\n          args: [namehash(name), key],\n        }),\n      ],\n      blockNumber,\n      blockTag,\n    })\n\n    if (res[0] === '0x') return null\n\n    const record = decodeFunctionResult({\n      abi: textResolverAbi,\n      functionName: 'text',\n      data: res[0],\n    })\n\n    return record === '' ? null : record\n  } catch (err) {\n    if (isNullUniversalResolverError(err, 'resolve')) return null\n    throw err\n  }\n}\n", "import type { Address } from 'abitype'\n\nimport {\n  type ReadContractErrorType,\n  readContract,\n} from '../../../actions/public/readContract.js'\nimport type { Client } from '../../../clients/createClient.js'\nimport type { Transport } from '../../../clients/transports/createTransport.js'\nimport {\n  EnsAvatarInvalidMetadataError,\n  type EnsAvatarInvalidMetadataErrorType,\n  EnsAvatarInvalidNftUriError,\n  type EnsAvatarInvalidNftUriErrorType,\n  EnsAvatarUnsupportedNamespaceError,\n  type EnsAvatarUnsupportedNamespaceErrorType,\n  EnsAvatarUriResolutionError,\n  type EnsAvatarUriResolutionErrorType,\n} from '../../../errors/ens.js'\nimport type { ErrorType } from '../../../errors/utils.js'\nimport type { Chain } from '../../../types/chain.js'\nimport type { AssetGatewayUrls } from '../../../types/ens.js'\n\ntype UriItem = {\n  uri: string\n  isOnChain: boolean\n  isEncoded: boolean\n}\n\nconst networkRegex =\n  /(?<protocol>https?:\\/\\/[^\\/]*|ipfs:\\/|ipns:\\/|ar:\\/)?(?<root>\\/)?(?<subpath>ipfs\\/|ipns\\/)?(?<target>[\\w\\-.]+)(?<subtarget>\\/.*)?/\nconst ipfsHashRegex =\n  /^(Qm[1-9A-HJ-NP-Za-km-z]{44,}|b[A-Za-z2-7]{58,}|B[A-Z2-7]{58,}|z[1-9A-HJ-NP-Za-km-z]{48,}|F[0-9A-F]{50,})(\\/(?<target>[\\w\\-.]+))?(?<subtarget>\\/.*)?$/\nconst base64Regex = /^data:([a-zA-Z\\-/+]*);base64,([^\"].*)/\nconst dataURIRegex = /^data:([a-zA-Z\\-/+]*)?(;[a-zA-Z0-9].*?)?(,)/\n\nexport type IsImageUriErrorType = ErrorType\n\nexport async function isImageUri(uri: string) {\n  try {\n    const res = await fetch(uri, { method: 'HEAD' })\n    // retrieve content type header to check if content is image\n    if (res.status === 200) {\n      const contentType = res.headers.get('content-type')\n      return contentType?.startsWith('image/')\n    }\n    return false\n  } catch (error: any) {\n    // if error is not cors related then fail\n    if (typeof error === 'object' && typeof error.response !== 'undefined') {\n      return false\n    }\n    // fail in NodeJS, since the error is not cors but any other network issue\n    // biome-ignore lint/suspicious/noPrototypeBuiltins:\n    if (!globalThis.hasOwnProperty('Image')) return false\n    // in case of cors, use image api to validate if given url is an actual image\n    return new Promise((resolve) => {\n      const img = new Image()\n      img.onload = () => {\n        resolve(true)\n      }\n      img.onerror = () => {\n        resolve(false)\n      }\n      img.src = uri\n    })\n  }\n}\n\nexport type GetGatewayErrorType = ErrorType\n\nexport function getGateway(custom: string | undefined, defaultGateway: string) {\n  if (!custom) return defaultGateway\n  if (custom.endsWith('/')) return custom.slice(0, -1)\n  return custom\n}\n\nexport type ResolveAvatarUriErrorType =\n  | GetGatewayErrorType\n  | EnsAvatarUriResolutionErrorType\n  | ErrorType\n\nexport function resolveAvatarUri({\n  uri,\n  gatewayUrls,\n}: {\n  uri: string\n  gatewayUrls?: AssetGatewayUrls | undefined\n}): UriItem {\n  const isEncoded = base64Regex.test(uri)\n  if (isEncoded) return { uri, isOnChain: true, isEncoded }\n\n  const ipfsGateway = getGateway(gatewayUrls?.ipfs, 'https://ipfs.io')\n  const arweaveGateway = getGateway(gatewayUrls?.arweave, 'https://arweave.net')\n\n  const networkRegexMatch = uri.match(networkRegex)\n  const {\n    protocol,\n    subpath,\n    target,\n    subtarget = '',\n  } = networkRegexMatch?.groups || {}\n\n  const isIPNS = protocol === 'ipns:/' || subpath === 'ipns/'\n  const isIPFS =\n    protocol === 'ipfs:/' || subpath === 'ipfs/' || ipfsHashRegex.test(uri)\n\n  if (uri.startsWith('http') && !isIPNS && !isIPFS) {\n    let replacedUri = uri\n    if (gatewayUrls?.arweave)\n      replacedUri = uri.replace(/https:\\/\\/arweave.net/g, gatewayUrls?.arweave)\n    return { uri: replacedUri, isOnChain: false, isEncoded: false }\n  }\n\n  if ((isIPNS || isIPFS) && target) {\n    return {\n      uri: `${ipfsGateway}/${isIPNS ? 'ipns' : 'ipfs'}/${target}${subtarget}`,\n      isOnChain: false,\n      isEncoded: false,\n    }\n  }\n\n  if (protocol === 'ar:/' && target) {\n    return {\n      uri: `${arweaveGateway}/${target}${subtarget || ''}`,\n      isOnChain: false,\n      isEncoded: false,\n    }\n  }\n\n  let parsedUri = uri.replace(dataURIRegex, '')\n  if (parsedUri.startsWith('<svg')) {\n    // if svg, base64 encode\n    parsedUri = `data:image/svg+xml;base64,${btoa(parsedUri)}`\n  }\n\n  if (parsedUri.startsWith('data:') || parsedUri.startsWith('{')) {\n    return {\n      uri: parsedUri,\n      isOnChain: true,\n      isEncoded: false,\n    }\n  }\n\n  throw new EnsAvatarUriResolutionError({ uri })\n}\n\nexport type GetJsonImageErrorType =\n  | EnsAvatarInvalidMetadataErrorType\n  | ErrorType\n\nexport function getJsonImage(data: any) {\n  // validation check for json data, must include one of theses properties\n  if (\n    typeof data !== 'object' ||\n    (!('image' in data) && !('image_url' in data) && !('image_data' in data))\n  ) {\n    throw new EnsAvatarInvalidMetadataError({ data })\n  }\n\n  return data.image || data.image_url || data.image_data\n}\n\nexport type GetMetadataAvatarUriErrorType =\n  | EnsAvatarUriResolutionErrorType\n  | ParseAvatarUriErrorType\n  | GetJsonImageErrorType\n  | ErrorType\n\nexport async function getMetadataAvatarUri({\n  gatewayUrls,\n  uri,\n}: {\n  gatewayUrls?: AssetGatewayUrls | undefined\n  uri: string\n}): Promise<string> {\n  try {\n    const res = await fetch(uri).then((res) => res.json())\n    const image = await parseAvatarUri({\n      gatewayUrls,\n      uri: getJsonImage(res),\n    })\n    return image\n  } catch {\n    throw new EnsAvatarUriResolutionError({ uri })\n  }\n}\n\nexport type ParseAvatarUriErrorType =\n  | ResolveAvatarUriErrorType\n  | IsImageUriErrorType\n  | EnsAvatarUriResolutionErrorType\n  | ErrorType\n\nexport async function parseAvatarUri({\n  gatewayUrls,\n  uri,\n}: {\n  gatewayUrls?: AssetGatewayUrls | undefined\n  uri: string\n}): Promise<string> {\n  const { uri: resolvedURI, isOnChain } = resolveAvatarUri({ uri, gatewayUrls })\n  if (isOnChain) return resolvedURI\n\n  // check if resolvedURI is an image, if it is return the url\n  const isImage = await isImageUri(resolvedURI)\n  if (isImage) return resolvedURI\n\n  throw new EnsAvatarUriResolutionError({ uri })\n}\n\ntype ParsedNft = {\n  chainID: number\n  namespace: string\n  contractAddress: Address\n  tokenID: string\n}\n\nexport type ParseNftUriErrorType = EnsAvatarInvalidNftUriErrorType | ErrorType\n\nexport function parseNftUri(uri_: string): ParsedNft {\n  let uri = uri_\n  // parse valid nft spec (CAIP-22/CAIP-29)\n  // @see: https://github.com/ChainAgnostic/CAIPs/tree/master/CAIPs\n  if (uri.startsWith('did:nft:')) {\n    // convert DID to CAIP\n    uri = uri.replace('did:nft:', '').replace(/_/g, '/')\n  }\n\n  const [reference, asset_namespace, tokenID] = uri.split('/')\n  const [eip_namespace, chainID] = reference.split(':')\n  const [erc_namespace, contractAddress] = asset_namespace.split(':')\n\n  if (!eip_namespace || eip_namespace.toLowerCase() !== 'eip155')\n    throw new EnsAvatarInvalidNftUriError({ reason: 'Only EIP-155 supported' })\n  if (!chainID)\n    throw new EnsAvatarInvalidNftUriError({ reason: 'Chain ID not found' })\n  if (!contractAddress)\n    throw new EnsAvatarInvalidNftUriError({\n      reason: 'Contract address not found',\n    })\n  if (!tokenID)\n    throw new EnsAvatarInvalidNftUriError({ reason: 'Token ID not found' })\n  if (!erc_namespace)\n    throw new EnsAvatarInvalidNftUriError({ reason: 'ERC namespace not found' })\n\n  return {\n    chainID: parseInt(chainID),\n    namespace: erc_namespace.toLowerCase(),\n    contractAddress: contractAddress as Address,\n    tokenID,\n  }\n}\n\nexport type GetNftTokenUriErrorType =\n  | ReadContractErrorType\n  | EnsAvatarUnsupportedNamespaceErrorType\n  | ErrorType\n\nexport async function getNftTokenUri<TChain extends Chain | undefined>(\n  client: Client<Transport, TChain>,\n  { nft }: { nft: ParsedNft },\n) {\n  if (nft.namespace === 'erc721') {\n    return readContract(client, {\n      address: nft.contractAddress,\n      abi: [\n        {\n          name: 'tokenURI',\n          type: 'function',\n          stateMutability: 'view',\n          inputs: [{ name: 'tokenId', type: 'uint256' }],\n          outputs: [{ name: '', type: 'string' }],\n        },\n      ],\n      functionName: 'tokenURI',\n      args: [BigInt(nft.tokenID)],\n    })\n  }\n  if (nft.namespace === 'erc1155') {\n    return readContract(client, {\n      address: nft.contractAddress,\n      abi: [\n        {\n          name: 'uri',\n          type: 'function',\n          stateMutability: 'view',\n          inputs: [{ name: '_id', type: 'uint256' }],\n          outputs: [{ name: '', type: 'string' }],\n        },\n      ],\n      functionName: 'uri',\n      args: [BigInt(nft.tokenID)],\n    })\n  }\n  throw new EnsAvatarUnsupportedNamespaceError({ namespace: nft.namespace })\n}\n", "import type { Client } from '../../../clients/createClient.js'\nimport type { Transport } from '../../../clients/transports/createTransport.js'\nimport type { ErrorType } from '../../../errors/utils.js'\nimport type { Chain } from '../../../types/chain.js'\nimport type { AssetGatewayUrls } from '../../../types/ens.js'\n\nimport {\n  type GetJsonImageErrorType,\n  type GetMetadataAvatarUriErrorType,\n  type GetNftTokenUriErrorType,\n  type ParseAvatarUriErrorType,\n  type ParseNftUriErrorType,\n  type ResolveAvatarUriErrorType,\n  getJsonImage,\n  getMetadataAvatarUri,\n  getNftTokenUri,\n  parseAvatarUri,\n  parseNftUri,\n  resolveAvatarUri,\n} from './utils.js'\n\nexport type ParseAvatarRecordErrorType =\n  | ParseNftAvatarUriErrorType\n  | ParseAvatarUriErrorType\n  | ErrorType\n\nexport async function parseAvatarRecord<T<PERSON><PERSON><PERSON> extends Chain | undefined>(\n  client: Client<Transport, TChain>,\n  {\n    gatewayUrls,\n    record,\n  }: {\n    gatewayUrls?: AssetGatewayUrls\n    record: string\n  },\n): Promise<string> {\n  if (/eip155:/i.test(record))\n    return parseNftAvatarUri(client, { gatewayUrls, record })\n  return parseAvatarUri({ uri: record, gatewayUrls })\n}\n\nexport type ParseNftAvatarUriErrorType =\n  | ParseNftUriErrorType\n  | GetNftTokenUriErrorType\n  | ResolveAvatarUriErrorType\n  | ParseAvatarUriErrorType\n  | GetJsonImageErrorType\n  | GetMetadataAvatarUriErrorType\n  | ErrorType\n\nasync function parseNftAvatarUri<TChain extends Chain | undefined>(\n  client: Client<Transport, TChain>,\n  {\n    gatewayUrls,\n    record,\n  }: {\n    gatewayUrls?: AssetGatewayUrls\n    record: string\n  },\n): Promise<string> {\n  // parse NFT URI into properties\n  const nft = parseNftUri(record)\n  // fetch tokenURI from the NFT contract\n  const nftUri = await getNftTokenUri(client, { nft })\n  // resolve the URI from the fetched tokenURI\n  const {\n    uri: resolvedNftUri,\n    isOnChain,\n    isEncoded,\n  } = resolveAvatarUri({ uri: nftUri, gatewayUrls })\n\n  // if the resolved URI is on chain, return the data\n  if (\n    isOnChain &&\n    (resolvedNftUri.includes('data:application/json;base64,') ||\n      resolvedNftUri.startsWith('{'))\n  ) {\n    const encodedJson = isEncoded\n      ? // if it is encoded, decode it\n        atob(resolvedNftUri.replace('data:application/json;base64,', ''))\n      : // if it isn't encoded assume it is a JSON string, but it could be anything (it will error if it is)\n        resolvedNftUri\n\n    const decoded = JSON.parse(encodedJson)\n    return parseAvatarUri({ uri: getJsonImage(decoded), gatewayUrls })\n  }\n\n  let uriTokenId = nft.tokenID\n  if (nft.namespace === 'erc1155')\n    uriTokenId = uriTokenId.replace('0x', '').padStart(64, '0')\n\n  return getMetadataAvatarUri({\n    gatewayUrls,\n    uri: resolvedNftUri.replace(/(?:0x)?{id}/, uriTokenId),\n  })\n}\n", "import type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { AssetGatewayUrls } from '../../types/ens.js'\nimport type { Prettify } from '../../types/utils.js'\nimport {\n  type ParseAvatarRecordErrorType,\n  parseAvatarRecord,\n} from '../../utils/ens/avatar/parseAvatarRecord.js'\nimport { getAction } from '../../utils/getAction.js'\n\nimport {\n  type GetEnsTextErrorType,\n  type GetEnsTextParameters,\n  getEnsText,\n} from './getEnsText.js'\n\nexport type GetEnsAvatarParameters = Prettify<\n  Omit<GetEnsTextParameters, 'key'> & {\n    /** Gateway urls to resolve IPFS and/or Arweave assets. */\n    gatewayUrls?: AssetGatewayUrls\n  }\n>\n\nexport type GetEnsAvatarReturnType = string | null\n\nexport type GetEnsAvatarErrorType =\n  | GetEnsTextErrorType\n  | ParseAvatarRecordErrorType\n  | ErrorType\n\n/**\n * Gets the avatar of an ENS name.\n *\n * - Docs: https://viem.sh/docs/ens/actions/getEnsAvatar.html\n * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/ens\n *\n * Calls [`getEnsText`](https://viem.sh/docs/ens/actions/getEnsText.html) with `key` set to `'avatar'`.\n *\n * Since ENS names prohibit certain forbidden characters (e.g. underscore) and have other validation rules, you likely want to [normalize ENS names](https://docs.ens.domains/contract-api-reference/name-processing#normalising-names) with [UTS-46 normalization](https://unicode.org/reports/tr46) before passing them to `getEnsAddress`. You can use the built-in [`normalize`](https://viem.sh/docs/ens/utilities/normalize.html) function for this.\n *\n * @param client - Client to use\n * @param parameters - {@link GetEnsAvatarParameters}\n * @returns Avatar URI or `null` if not found. {@link GetEnsAvatarReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { getEnsAvatar, normalize } from 'viem/ens'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const ensAvatar = await getEnsAvatar(client, {\n *   name: normalize('wevm.eth'),\n * })\n * // 'https://ipfs.io/ipfs/Qma8mnp6xV3J2cRNf3mTth5C8nV11CAnceVinc3y8jSbio'\n */\nexport async function getEnsAvatar<TChain extends Chain | undefined>(\n  client: Client<Transport, TChain>,\n  {\n    blockNumber,\n    blockTag,\n    gatewayUrls,\n    name,\n    universalResolverAddress,\n  }: GetEnsAvatarParameters,\n): Promise<GetEnsAvatarReturnType> {\n  const record = await getAction(\n    client,\n    getEnsText,\n    'getEnsText',\n  )({\n    blockNumber,\n    blockTag,\n    key: 'avatar',\n    name,\n    universalResolverAddress,\n  })\n  if (!record) return null\n  try {\n    return await parseAvatarRecord(client, { record, gatewayUrls })\n  } catch {\n    return null\n  }\n}\n", "import type { Address } from 'abitype'\n\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport { universalResolverReverseAbi } from '../../constants/abis.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { Prettify } from '../../types/utils.js'\nimport {\n  type GetChainContractAddressErrorType,\n  getChainContractAddress,\n} from '../../utils/chain/getChainContractAddress.js'\nimport { type ToHexErrorType, toHex } from '../../utils/encoding/toHex.js'\nimport { isNullUniversalResolverError } from '../../utils/ens/errors.js'\nimport {\n  type PacketToBytesErrorType,\n  packetToBytes,\n} from '../../utils/ens/packetToBytes.js'\nimport { getAction } from '../../utils/getAction.js'\nimport {\n  type ReadContractErrorType,\n  type ReadContractParameters,\n  readContract,\n} from '../public/readContract.js'\n\nexport type GetEnsNameParameters = Prettify<\n  Pick<ReadContractParameters, 'blockNumber' | 'blockTag'> & {\n    /** Address to get ENS name for. */\n    address: Address\n    /** Address of ENS Universal Resolver Contract. */\n    universalResolverAddress?: Address\n  }\n>\n\nexport type GetEnsNameReturnType = string | null\n\nexport type GetEnsNameErrorType =\n  | GetChainContractAddressErrorType\n  | ReadContractErrorType\n  | ToHexErrorType\n  | PacketToBytesErrorType\n  | ErrorType\n\n/**\n * Gets primary name for specified address.\n *\n * - Docs: https://viem.sh/docs/ens/actions/getEnsName.html\n * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/ens\n *\n * Calls `reverse(bytes)` on ENS Universal Resolver Contract to \"reverse resolve\" the address to the primary ENS name.\n *\n * @param client - Client to use\n * @param parameters - {@link GetEnsNameParameters}\n * @returns Name or `null` if not found. {@link GetEnsNameReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { getEnsName } from 'viem/ens'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const ensName = await getEnsName(client, {\n *   address: '******************************************',\n * })\n * // 'wevm.eth'\n */\nexport async function getEnsName<TChain extends Chain | undefined>(\n  client: Client<Transport, TChain>,\n  {\n    address,\n    blockNumber,\n    blockTag,\n    universalResolverAddress: universalResolverAddress_,\n  }: GetEnsNameParameters,\n): Promise<GetEnsNameReturnType> {\n  let universalResolverAddress = universalResolverAddress_\n  if (!universalResolverAddress) {\n    if (!client.chain)\n      throw new Error(\n        'client chain not configured. universalResolverAddress is required.',\n      )\n\n    universalResolverAddress = getChainContractAddress({\n      blockNumber,\n      chain: client.chain,\n      contract: 'ensUniversalResolver',\n    })\n  }\n\n  const reverseNode = `${address.toLowerCase().substring(2)}.addr.reverse`\n  try {\n    const [name, resolvedAddress] = await getAction(\n      client,\n      readContract,\n      'readContract',\n    )({\n      address: universalResolverAddress,\n      abi: universalResolverReverseAbi,\n      functionName: 'reverse',\n      args: [toHex(packetToBytes(reverseNode))],\n      blockNumber,\n      blockTag,\n    })\n    if (address.toLowerCase() !== resolvedAddress.toLowerCase()) return null\n    return name\n  } catch (err) {\n    if (isNullUniversalResolverError(err, 'reverse')) return null\n    throw err\n  }\n}\n", "import type { Address } from 'abitype'\n\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { Prettify } from '../../types/utils.js'\nimport {\n  type GetChainContractAddressErrorType,\n  getChainContractAddress,\n} from '../../utils/chain/getChainContractAddress.js'\nimport { type ToHexErrorType, toHex } from '../../utils/encoding/toHex.js'\nimport {\n  type PacketToBytesErrorType,\n  packetToBytes,\n} from '../../utils/ens/packetToBytes.js'\nimport { getAction } from '../../utils/getAction.js'\nimport {\n  type ReadContractParameters,\n  readContract,\n} from '../public/readContract.js'\n\nexport type GetEnsResolverParameters = Prettify<\n  Pick<ReadContractParameters, 'blockNumber' | 'blockTag'> & {\n    /** Name to get the address for. */\n    name: string\n    /** Address of ENS Universal Resolver Contract. */\n    universalResolverAddress?: Address\n  }\n>\n\nexport type GetEnsResolverReturnType = Address\n\nexport type GetEnsResolverErrorType =\n  | GetChainContractAddressErrorType\n  | ToHexErrorType\n  | PacketToBytesErrorType\n  | ErrorType\n\n/**\n * Gets resolver for ENS name.\n *\n * - Docs: https://viem.sh/docs/ens/actions/getEnsResolver.html\n * - Examples: https://stackblitz.com/github/wevm/viem/tree/main/examples/ens\n *\n * Calls `findResolver(bytes)` on ENS Universal Resolver Contract to retrieve the resolver of an ENS name.\n *\n * Since ENS names prohibit certain forbidden characters (e.g. underscore) and have other validation rules, you likely want to [normalize ENS names](https://docs.ens.domains/contract-api-reference/name-processing#normalising-names) with [UTS-46 normalization](https://unicode.org/reports/tr46) before passing them to `getEnsAddress`. You can use the built-in [`normalize`](https://viem.sh/docs/ens/utilities/normalize.html) function for this.\n *\n * @param client - Client to use\n * @param parameters - {@link GetEnsResolverParameters}\n * @returns Address for ENS resolver. {@link GetEnsResolverReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { getEnsResolver, normalize } from 'viem/ens'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const resolverAddress = await getEnsResolver(client, {\n *   name: normalize('wevm.eth'),\n * })\n * // '******************************************'\n */\nexport async function getEnsResolver<TChain extends Chain | undefined>(\n  client: Client<Transport, TChain>,\n  {\n    blockNumber,\n    blockTag,\n    name,\n    universalResolverAddress: universalResolverAddress_,\n  }: GetEnsResolverParameters,\n) {\n  let universalResolverAddress = universalResolverAddress_\n  if (!universalResolverAddress) {\n    if (!client.chain)\n      throw new Error(\n        'client chain not configured. universalResolverAddress is required.',\n      )\n\n    universalResolverAddress = getChainContractAddress({\n      blockNumber,\n      chain: client.chain,\n      contract: 'ensUniversalResolver',\n    })\n  }\n\n  const [resolverAddress] = await getAction(\n    client,\n    readContract,\n    'readContract',\n  )({\n    address: universalResolverAddress,\n    abi: [\n      {\n        inputs: [{ type: 'bytes' }],\n        name: 'findResolver',\n        outputs: [{ type: 'address' }, { type: 'bytes32' }],\n        stateMutability: 'view',\n        type: 'function',\n      },\n    ],\n    functionName: 'findResolver',\n    args: [toHex(packetToBytes(name))],\n    blockNumber,\n    blockTag,\n  })\n  return resolverAddress\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,gCAAgC;AAYhC,SAAU,iBACd,KACA,EACE,KACA,SACA,MACA,UACA,cACA,OAAM,GAQP;AAED,QAAM,EAAE,MAAM,MAAM,SAAS,aAAY,IACvC,eAAe,mBACX,MACA,eAAe,YACb,IAAI,KAAK,CAACA,SAAQ,UAAWA,IAAa,KAAK,IAAI,KAAI,IACvD,CAAA;AAGR,QAAM,SAAS,MAAK;AAClB,QAAI,eAAe;AACjB,aAAO,IAAI,8BAA8B,EAAE,aAAY,CAAE;AAC3D,QACE,CAAC,+BAA+B,iBAAiB,IAAI,EAAE,SAAS,IAAI,MACnE,QAAQ,WAAW,eACpB;AACA,aAAO,IAAI,8BAA8B;QACvC;QACA,MAAM,OAAO,SAAS,WAAW,KAAK,OAAO;QAC7C;QACA,SAAS,gBAAgB;OAC1B;;AAEH,WAAO;EACT,GAAE;AAEF,SAAO,IAAI,+BAA+B,OAAoB;IAC5D;IACA;IACA,iBAAiB;IACjB;IACA;IACA;GACD;AACH;;;ACzEM,SAAU,wBAAwB,OAAa;AACnD,MAAI,MAAM,WAAW;AAAI,WAAO;AAChC,MAAI,MAAM,QAAQ,GAAG,MAAM;AAAG,WAAO;AACrC,MAAI,MAAM,QAAQ,GAAG,MAAM;AAAI,WAAO;AACtC,QAAM,OAAO,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC;AACpC,MAAI,CAAC,MAAM,IAAI;AAAG,WAAO;AACzB,SAAO;AACT;;;ACsBM,SAAU,SAAS,MAAY;AACnC,MAAI,SAAS,IAAI,WAAW,EAAE,EAAE,KAAK,CAAC;AACtC,MAAI,CAAC;AAAM,WAAO,WAAW,MAAM;AAEnC,QAAM,SAAS,KAAK,MAAM,GAAG;AAE7B,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAC9C,UAAM,uBAAuB,wBAAwB,OAAO,CAAC,CAAC;AAC9D,UAAM,SAAS,uBACX,QAAQ,oBAAoB,IAC5B,UAAU,cAAc,OAAO,CAAC,CAAC,GAAG,OAAO;AAC/C,aAAS,UAAU,OAAO,CAAC,QAAQ,MAAM,CAAC,GAAG,OAAO;;AAGtD,SAAO,WAAW,MAAM;AAC1B;;;ACtBM,SAAU,UAAU,OAAa;AACrC,QAAM,SAAS,IAAI,WAAW,EAAE,EAAE,KAAK,CAAC;AACxC,MAAI,CAAC;AAAO,WAAO,WAAW,MAAM;AACpC,SAAO,wBAAwB,KAAK,KAAK,UAAU,cAAc,KAAK,CAAC;AACzE;;;ACtBM,SAAU,6BACd,KACA,UAA+B;AAZjC;AAcE,MAAI,EAAE,eAAe;AAAY,WAAO;AACxC,QAAM,QAAQ,IAAI,KAAK,CAAC,MAAM,aAAa,6BAA6B;AACxE,MAAI,EAAE,iBAAiB;AAAgC,WAAO;AAC9D,QAAI,WAAM,SAAN,mBAAY,eAAc;AAAoB,WAAO;AACzD,QAAI,WAAM,SAAN,mBAAY,eAAc;AAAgC,WAAO;AAErE,OACE,WAAM,WAAN,mBAAc,SACZ;AAGF,WAAO;AAET,MAAI,aAAa,aAAa,MAAM,WAAW,aAAa,EAAE;AAAG,WAAO;AACxE,SAAO;AACT;;;ACxBM,SAAU,gBAAgB,MAAS;AACvC,SAAO,IAAI,KAAK,MAAM,CAAC,CAAC;AAC1B;;;ACeM,SAAU,cAAc,QAAc;AAE1C,QAAM,QAAQ,OAAO,QAAQ,aAAa,EAAE;AAC5C,MAAI,MAAM,WAAW;AAAG,WAAO,IAAI,WAAW,CAAC;AAE/C,QAAM,QAAQ,IAAI,WAAW,cAAc,KAAK,EAAE,aAAa,CAAC;AAEhE,MAAI,SAAS;AACb,QAAM,OAAO,MAAM,MAAM,GAAG;AAC5B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,UAAU,cAAc,KAAK,CAAC,CAAC;AAGnC,QAAI,QAAQ,aAAa;AACvB,gBAAU,cAAc,gBAAgB,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7D,UAAM,MAAM,IAAI,QAAQ;AACxB,UAAM,IAAI,SAAS,SAAS,CAAC;AAC7B,cAAU,QAAQ,SAAS;;AAG7B,MAAI,MAAM,eAAe,SAAS;AAAG,WAAO,MAAM,MAAM,GAAG,SAAS,CAAC;AAErE,SAAO;AACT;;;ACpCM,SAAU,UACd,QACA,QAGA,MAAY;AAEZ,SAAO,CAAC,WAA4B;AAdtC;AAgBM,yBAGA,OAAO,QAAQ,UAHf,gCAGuB,YAAW,OAAO,QAAQ,MAAM;;AAC7D;;;ACoDA,eAAsB,aAKpB,QACA,EACE,KACA,SACA,MACA,cACA,GAAG,YAAW,GAC8B;AAE9C,QAAM,WAAW,mBAAmB;IAClC;IACA;IACA;GAC+D;AACjE,MAAI;AACF,UAAM,EAAE,KAAI,IAAK,MAAM,UACrB,QACA,MACA,MAAM,EACN;MACA,MAAM;MACN,IAAI;MACJ,GAAG;KACyB;AAC9B,WAAO,qBAAqB;MAC1B;MACA;MACA;MACA,MAAM,QAAQ;KAIf;WACM,KAAK;AACZ,UAAM,iBAAiB,KAAkB;MACvC;MACA;MACA;MACA,UAAU;MACV;KACD;;AAEL;;;ACjCA,eAAsB,cACpB,QACA,EACE,aACA,UACA,UACA,MACA,0BAA0B,0BAAyB,GAC3B;AAE1B,MAAI,2BAA2B;AAC/B,MAAI,CAAC,0BAA0B;AAC7B,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MACR,oEAAoE;AAGxE,+BAA2B,wBAAwB;MACjD;MACA,OAAO,OAAO;MACd,UAAU;KACX;;AAGH,MAAI;AACF,UAAM,eAAe,mBAAmB;MACtC,KAAK;MACL,cAAc;MACd,GAAI,YAAY,OACZ,EAAE,MAAM,CAAC,SAAS,IAAI,GAAG,OAAO,QAAQ,CAAC,EAAC,IAC1C,EAAE,MAAM,CAAC,SAAS,IAAI,CAAC,EAAC;KAC7B;AAED,UAAM,MAAM,MAAM,UAChB,QACA,cACA,cAAc,EACd;MACA,SAAS;MACT,KAAK;MACL,cAAc;MACd,MAAM,CAAC,MAAM,cAAc,IAAI,CAAC,GAAG,YAAY;MAC/C;MACA;KACD;AAED,QAAI,IAAI,CAAC,MAAM;AAAM,aAAO;AAE5B,UAAM,UAAU,qBAAqB;MACnC,KAAK;MACL,MAAM,YAAY,OAAO,CAAC,SAAS,IAAI,GAAG,OAAO,QAAQ,CAAC,IAAI;MAC9D,cAAc;MACd,MAAM,IAAI,CAAC;KACZ;AAED,QAAI,YAAY;AAAM,aAAO;AAC7B,QAAI,KAAK,OAAO,MAAM;AAAQ,aAAO;AACrC,WAAO;WACA,KAAK;AACZ,QAAI,6BAA6B,KAAK,SAAS;AAAG,aAAO;AACzD,UAAM;;AAEV;;;AChJM,IAAO,gCAAP,cAA6C,UAAS;EAE1D,YAAY,EAAE,KAAI,GAAiB;AACjC,UACE,oFACA;MACE,cAAc;QACZ;QACA;QACA,kBAAkB,KAAK,UAAU,IAAI,CAAC;;KAEzC;AAVI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYhB;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,EAAE,OAAM,GAAsB;AACxC,UAAM,kCAAkC,MAAM,EAAE;AAFzC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,EAAE,IAAG,GAAmB;AAClC,UACE,qCAAqC,GAAG,+EAA+E;AAHlH,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;AAOI,IAAO,qCAAP,cAAkD,UAAS;EAE/D,YAAY,EAAE,UAAS,GAAyB;AAC9C,UACE,6BAA6B,SAAS,oDAAoD;AAHrF,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;;;ACiCF,eAAsB,WACpB,QACA,EACE,aACA,UACA,MACA,KACA,0BAA0B,0BAAyB,GAC9B;AAEvB,MAAI,2BAA2B;AAC/B,MAAI,CAAC,0BAA0B;AAC7B,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MACR,oEAAoE;AAGxE,+BAA2B,wBAAwB;MACjD;MACA,OAAO,OAAO;MACd,UAAU;KACX;;AAGH,MAAI;AACF,UAAM,MAAM,MAAM,UAChB,QACA,cACA,cAAc,EACd;MACA,SAAS;MACT,KAAK;MACL,cAAc;MACd,MAAM;QACJ,MAAM,cAAc,IAAI,CAAC;QACzB,mBAAmB;UACjB,KAAK;UACL,cAAc;UACd,MAAM,CAAC,SAAS,IAAI,GAAG,GAAG;SAC3B;;MAEH;MACA;KACD;AAED,QAAI,IAAI,CAAC,MAAM;AAAM,aAAO;AAE5B,UAAM,SAAS,qBAAqB;MAClC,KAAK;MACL,cAAc;MACd,MAAM,IAAI,CAAC;KACZ;AAED,WAAO,WAAW,KAAK,OAAO;WACvB,KAAK;AACZ,QAAI,6BAA6B,KAAK,SAAS;AAAG,aAAO;AACzD,UAAM;;AAEV;;;ACrHA,IAAM,eACJ;AACF,IAAM,gBACJ;AACF,IAAM,cAAc;AACpB,IAAM,eAAe;AAIrB,eAAsB,WAAW,KAAW;AAC1C,MAAI;AACF,UAAM,MAAM,MAAM,MAAM,KAAK,EAAE,QAAQ,OAAM,CAAE;AAE/C,QAAI,IAAI,WAAW,KAAK;AACtB,YAAM,cAAc,IAAI,QAAQ,IAAI,cAAc;AAClD,aAAO,2CAAa,WAAW;;AAEjC,WAAO;WACA,OAAY;AAEnB,QAAI,OAAO,UAAU,YAAY,OAAO,MAAM,aAAa,aAAa;AACtE,aAAO;;AAIT,QAAI,CAAC,WAAW,eAAe,OAAO;AAAG,aAAO;AAEhD,WAAO,IAAI,QAAQ,CAAC,YAAW;AAC7B,YAAM,MAAM,IAAI,MAAK;AACrB,UAAI,SAAS,MAAK;AAChB,gBAAQ,IAAI;MACd;AACA,UAAI,UAAU,MAAK;AACjB,gBAAQ,KAAK;MACf;AACA,UAAI,MAAM;IACZ,CAAC;;AAEL;AAIM,SAAU,WAAW,QAA4B,gBAAsB;AAC3E,MAAI,CAAC;AAAQ,WAAO;AACpB,MAAI,OAAO,SAAS,GAAG;AAAG,WAAO,OAAO,MAAM,GAAG,EAAE;AACnD,SAAO;AACT;AAOM,SAAU,iBAAiB,EAC/B,KACA,YAAW,GAIZ;AACC,QAAM,YAAY,YAAY,KAAK,GAAG;AACtC,MAAI;AAAW,WAAO,EAAE,KAAK,WAAW,MAAM,UAAS;AAEvD,QAAM,cAAc,WAAW,2CAAa,MAAM,iBAAiB;AACnE,QAAM,iBAAiB,WAAW,2CAAa,SAAS,qBAAqB;AAE7E,QAAM,oBAAoB,IAAI,MAAM,YAAY;AAChD,QAAM,EACJ,UACA,SACA,QACA,YAAY,GAAE,KACZ,uDAAmB,WAAU,CAAA;AAEjC,QAAM,SAAS,aAAa,YAAY,YAAY;AACpD,QAAM,SACJ,aAAa,YAAY,YAAY,WAAW,cAAc,KAAK,GAAG;AAExE,MAAI,IAAI,WAAW,MAAM,KAAK,CAAC,UAAU,CAAC,QAAQ;AAChD,QAAI,cAAc;AAClB,QAAI,2CAAa;AACf,oBAAc,IAAI,QAAQ,0BAA0B,2CAAa,OAAO;AAC1E,WAAO,EAAE,KAAK,aAAa,WAAW,OAAO,WAAW,MAAK;;AAG/D,OAAK,UAAU,WAAW,QAAQ;AAChC,WAAO;MACL,KAAK,GAAG,WAAW,IAAI,SAAS,SAAS,MAAM,IAAI,MAAM,GAAG,SAAS;MACrE,WAAW;MACX,WAAW;;;AAIf,MAAI,aAAa,UAAU,QAAQ;AACjC,WAAO;MACL,KAAK,GAAG,cAAc,IAAI,MAAM,GAAG,aAAa,EAAE;MAClD,WAAW;MACX,WAAW;;;AAIf,MAAI,YAAY,IAAI,QAAQ,cAAc,EAAE;AAC5C,MAAI,UAAU,WAAW,MAAM,GAAG;AAEhC,gBAAY,6BAA6B,KAAK,SAAS,CAAC;;AAG1D,MAAI,UAAU,WAAW,OAAO,KAAK,UAAU,WAAW,GAAG,GAAG;AAC9D,WAAO;MACL,KAAK;MACL,WAAW;MACX,WAAW;;;AAIf,QAAM,IAAI,4BAA4B,EAAE,IAAG,CAAE;AAC/C;AAMM,SAAU,aAAa,MAAS;AAEpC,MACE,OAAO,SAAS,YACf,EAAE,WAAW,SAAS,EAAE,eAAe,SAAS,EAAE,gBAAgB,OACnE;AACA,UAAM,IAAI,8BAA8B,EAAE,KAAI,CAAE;;AAGlD,SAAO,KAAK,SAAS,KAAK,aAAa,KAAK;AAC9C;AAQA,eAAsB,qBAAqB,EACzC,aACA,IAAG,GAIJ;AACC,MAAI;AACF,UAAM,MAAM,MAAM,MAAM,GAAG,EAAE,KAAK,CAACC,SAAQA,KAAI,KAAI,CAAE;AACrD,UAAM,QAAQ,MAAM,eAAe;MACjC;MACA,KAAK,aAAa,GAAG;KACtB;AACD,WAAO;UACD;AACN,UAAM,IAAI,4BAA4B,EAAE,IAAG,CAAE;;AAEjD;AAQA,eAAsB,eAAe,EACnC,aACA,IAAG,GAIJ;AACC,QAAM,EAAE,KAAK,aAAa,UAAS,IAAK,iBAAiB,EAAE,KAAK,YAAW,CAAE;AAC7E,MAAI;AAAW,WAAO;AAGtB,QAAM,UAAU,MAAM,WAAW,WAAW;AAC5C,MAAI;AAAS,WAAO;AAEpB,QAAM,IAAI,4BAA4B,EAAE,IAAG,CAAE;AAC/C;AAWM,SAAU,YAAY,MAAY;AACtC,MAAI,MAAM;AAGV,MAAI,IAAI,WAAW,UAAU,GAAG;AAE9B,UAAM,IAAI,QAAQ,YAAY,EAAE,EAAE,QAAQ,MAAM,GAAG;;AAGrD,QAAM,CAAC,WAAW,iBAAiB,OAAO,IAAI,IAAI,MAAM,GAAG;AAC3D,QAAM,CAAC,eAAe,OAAO,IAAI,UAAU,MAAM,GAAG;AACpD,QAAM,CAAC,eAAe,eAAe,IAAI,gBAAgB,MAAM,GAAG;AAElE,MAAI,CAAC,iBAAiB,cAAc,YAAW,MAAO;AACpD,UAAM,IAAI,4BAA4B,EAAE,QAAQ,yBAAwB,CAAE;AAC5E,MAAI,CAAC;AACH,UAAM,IAAI,4BAA4B,EAAE,QAAQ,qBAAoB,CAAE;AACxE,MAAI,CAAC;AACH,UAAM,IAAI,4BAA4B;MACpC,QAAQ;KACT;AACH,MAAI,CAAC;AACH,UAAM,IAAI,4BAA4B,EAAE,QAAQ,qBAAoB,CAAE;AACxE,MAAI,CAAC;AACH,UAAM,IAAI,4BAA4B,EAAE,QAAQ,0BAAyB,CAAE;AAE7E,SAAO;IACL,SAAS,SAAS,OAAO;IACzB,WAAW,cAAc,YAAW;IACpC;IACA;;AAEJ;AAOA,eAAsB,eACpB,QACA,EAAE,IAAG,GAAsB;AAE3B,MAAI,IAAI,cAAc,UAAU;AAC9B,WAAO,aAAa,QAAQ;MAC1B,SAAS,IAAI;MACb,KAAK;QACH;UACE,MAAM;UACN,MAAM;UACN,iBAAiB;UACjB,QAAQ,CAAC,EAAE,MAAM,WAAW,MAAM,UAAS,CAAE;UAC7C,SAAS,CAAC,EAAE,MAAM,IAAI,MAAM,SAAQ,CAAE;;;MAG1C,cAAc;MACd,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC;KAC3B;;AAEH,MAAI,IAAI,cAAc,WAAW;AAC/B,WAAO,aAAa,QAAQ;MAC1B,SAAS,IAAI;MACb,KAAK;QACH;UACE,MAAM;UACN,MAAM;UACN,iBAAiB;UACjB,QAAQ,CAAC,EAAE,MAAM,OAAO,MAAM,UAAS,CAAE;UACzC,SAAS,CAAC,EAAE,MAAM,IAAI,MAAM,SAAQ,CAAE;;;MAG1C,cAAc;MACd,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC;KAC3B;;AAEH,QAAM,IAAI,mCAAmC,EAAE,WAAW,IAAI,UAAS,CAAE;AAC3E;;;AC7QA,eAAsB,kBACpB,QACA,EACE,aACA,OAAM,GAIP;AAED,MAAI,WAAW,KAAK,MAAM;AACxB,WAAO,kBAAkB,QAAQ,EAAE,aAAa,OAAM,CAAE;AAC1D,SAAO,eAAe,EAAE,KAAK,QAAQ,YAAW,CAAE;AACpD;AAWA,eAAe,kBACb,QACA,EACE,aACA,OAAM,GAIP;AAGD,QAAM,MAAM,YAAY,MAAM;AAE9B,QAAM,SAAS,MAAM,eAAe,QAAQ,EAAE,IAAG,CAAE;AAEnD,QAAM,EACJ,KAAK,gBACL,WACA,UAAS,IACP,iBAAiB,EAAE,KAAK,QAAQ,YAAW,CAAE;AAGjD,MACE,cACC,eAAe,SAAS,+BAA+B,KACtD,eAAe,WAAW,GAAG,IAC/B;AACA,UAAM,cAAc;;MAEhB,KAAK,eAAe,QAAQ,iCAAiC,EAAE,CAAC;;;MAEhE;;AAEJ,UAAM,UAAU,KAAK,MAAM,WAAW;AACtC,WAAO,eAAe,EAAE,KAAK,aAAa,OAAO,GAAG,YAAW,CAAE;;AAGnE,MAAI,aAAa,IAAI;AACrB,MAAI,IAAI,cAAc;AACpB,iBAAa,WAAW,QAAQ,MAAM,EAAE,EAAE,SAAS,IAAI,GAAG;AAE5D,SAAO,qBAAqB;IAC1B;IACA,KAAK,eAAe,QAAQ,eAAe,UAAU;GACtD;AACH;;;ACnCA,eAAsB,aACpB,QACA,EACE,aACA,UACA,aACA,MACA,yBAAwB,GACD;AAEzB,QAAM,SAAS,MAAM,UACnB,QACA,YACA,YAAY,EACZ;IACA;IACA;IACA,KAAK;IACL;IACA;GACD;AACD,MAAI,CAAC;AAAQ,WAAO;AACpB,MAAI;AACF,WAAO,MAAM,kBAAkB,QAAQ,EAAE,QAAQ,YAAW,CAAE;UACxD;AACN,WAAO;;AAEX;;;AClBA,eAAsB,WACpB,QACA,EACE,SACA,aACA,UACA,0BAA0B,0BAAyB,GAC9B;AAEvB,MAAI,2BAA2B;AAC/B,MAAI,CAAC,0BAA0B;AAC7B,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MACR,oEAAoE;AAGxE,+BAA2B,wBAAwB;MACjD;MACA,OAAO,OAAO;MACd,UAAU;KACX;;AAGH,QAAM,cAAc,GAAG,QAAQ,YAAW,EAAG,UAAU,CAAC,CAAC;AACzD,MAAI;AACF,UAAM,CAAC,MAAM,eAAe,IAAI,MAAM,UACpC,QACA,cACA,cAAc,EACd;MACA,SAAS;MACT,KAAK;MACL,cAAc;MACd,MAAM,CAAC,MAAM,cAAc,WAAW,CAAC,CAAC;MACxC;MACA;KACD;AACD,QAAI,QAAQ,YAAW,MAAO,gBAAgB,YAAW;AAAI,aAAO;AACpE,WAAO;WACA,KAAK;AACZ,QAAI,6BAA6B,KAAK,SAAS;AAAG,aAAO;AACzD,UAAM;;AAEV;;;AC7CA,eAAsB,eACpB,QACA,EACE,aACA,UACA,MACA,0BAA0B,0BAAyB,GAC1B;AAE3B,MAAI,2BAA2B;AAC/B,MAAI,CAAC,0BAA0B;AAC7B,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MACR,oEAAoE;AAGxE,+BAA2B,wBAAwB;MACjD;MACA,OAAO,OAAO;MACd,UAAU;KACX;;AAGH,QAAM,CAAC,eAAe,IAAI,MAAM,UAC9B,QACA,cACA,cAAc,EACd;IACA,SAAS;IACT,KAAK;MACH;QACE,QAAQ,CAAC,EAAE,MAAM,QAAO,CAAE;QAC1B,MAAM;QACN,SAAS,CAAC,EAAE,MAAM,UAAS,GAAI,EAAE,MAAM,UAAS,CAAE;QAClD,iBAAiB;QACjB,MAAM;;;IAGV,cAAc;IACd,MAAM,CAAC,MAAM,cAAc,IAAI,CAAC,CAAC;IACjC;IACA;GACD;AACD,SAAO;AACT;", "names": ["err", "res"]}