import {
  InfiniteQueryObserver,
  QueryClient,
  QueryClientProvider,
  QueryObserver,
  dehydrate,
  hydrate,
  notifyManager,
  parseMutationArgs,
  replaceEqualDeep,
  require_shim,
  useIsRestoring,
  useMutation,
  useQueryClient,
  useQueryErrorResetBoundary
} from "./chunk-C2QRFJSH.js";
import {
  require_react
} from "./chunk-O6JANDIR.js";
import {
  connect,
  createConfig,
  createStorage,
  deepEqual,
  disconnect,
  fetchBalance,
  fetchBlockNumber,
  fetchEnsAddress,
  fetchEnsAvatar,
  fetchEnsName,
  fetchEnsResolver,
  fetchFeeData,
  fetchToken,
  fetchTransaction,
  getAccount,
  getCallParameters,
  getNetwork,
  getPublicClient,
  getSendTransactionParameters,
  getWalletClient,
  getWebSocketPublicClient,
  noopStorage,
  prepareSendTransaction,
  prepareWriteContract,
  readContract,
  readContracts,
  sendTransaction,
  signMessage,
  signTypedData,
  switchNetwork,
  waitForTransaction,
  watchAccount,
  watchNetwork,
  watchPublicClient,
  watchWalletClient,
  watchWebSocketPublicClient,
  writeContract
} from "./chunk-HE7TYL65.js";
import {
  __commonJS,
  __toESM
} from "./chunk-W7S2ME4R.js";

// node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js
var require_with_selector_development = __commonJS({
  "node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"(exports) {
    "use strict";
    (function() {
      function is(x, y) {
        return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;
      }
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
      var React23 = require_react(), shim = require_shim(), objectIs = "function" === typeof Object.is ? Object.is : is, useSyncExternalStore3 = shim.useSyncExternalStore, useRef5 = React23.useRef, useEffect9 = React23.useEffect, useMemo7 = React23.useMemo, useDebugValue = React23.useDebugValue;
      exports.useSyncExternalStoreWithSelector = function(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {
        var instRef = useRef5(null);
        if (null === instRef.current) {
          var inst = { hasValue: false, value: null };
          instRef.current = inst;
        } else
          inst = instRef.current;
        instRef = useMemo7(
          function() {
            function memoizedSelector(nextSnapshot) {
              if (!hasMemo) {
                hasMemo = true;
                memoizedSnapshot = nextSnapshot;
                nextSnapshot = selector(nextSnapshot);
                if (void 0 !== isEqual && inst.hasValue) {
                  var currentSelection = inst.value;
                  if (isEqual(currentSelection, nextSnapshot))
                    return memoizedSelection = currentSelection;
                }
                return memoizedSelection = nextSnapshot;
              }
              currentSelection = memoizedSelection;
              if (objectIs(memoizedSnapshot, nextSnapshot))
                return currentSelection;
              var nextSelection = selector(nextSnapshot);
              if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))
                return memoizedSnapshot = nextSnapshot, currentSelection;
              memoizedSnapshot = nextSnapshot;
              return memoizedSelection = nextSelection;
            }
            var hasMemo = false, memoizedSnapshot, memoizedSelection, maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;
            return [
              function() {
                return memoizedSelector(getSnapshot());
              },
              null === maybeGetServerSnapshot ? void 0 : function() {
                return memoizedSelector(maybeGetServerSnapshot());
              }
            ];
          },
          [getSnapshot, getServerSnapshot, selector, isEqual]
        );
        var value = useSyncExternalStore3(subscribe, instRef[0], instRef[1]);
        useEffect9(
          function() {
            inst.hasValue = true;
            inst.value = value;
          },
          [value]
        );
        useDebugValue(value);
        return value;
      };
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
    })();
  }
});

// node_modules/use-sync-external-store/shim/with-selector.js
var require_with_selector = __commonJS({
  "node_modules/use-sync-external-store/shim/with-selector.js"(exports, module) {
    "use strict";
    if (false) {
      module.exports = null;
    } else {
      module.exports = require_with_selector_development();
    }
  }
});

// node_modules/@tanstack/query-sync-storage-persister/build/lib/index.mjs
function createSyncStoragePersister({
  storage,
  key = "REACT_QUERY_OFFLINE_CACHE",
  throttleTime = 1e3,
  serialize: serialize2 = JSON.stringify,
  deserialize: deserialize2 = JSON.parse,
  retry
}) {
  if (storage) {
    const trySave = (persistedClient) => {
      try {
        storage.setItem(key, serialize2(persistedClient));
        return;
      } catch (error) {
        return error;
      }
    };
    return {
      persistClient: throttle((persistedClient) => {
        let client = persistedClient;
        let error = trySave(client);
        let errorCount = 0;
        while (error && client) {
          errorCount++;
          client = retry == null ? void 0 : retry({
            persistedClient: client,
            error,
            errorCount
          });
          if (client) {
            error = trySave(client);
          }
        }
      }, throttleTime),
      restoreClient: () => {
        const cacheString = storage.getItem(key);
        if (!cacheString) {
          return;
        }
        return deserialize2(cacheString);
      },
      removeClient: () => {
        storage.removeItem(key);
      }
    };
  }
  return {
    persistClient: noop,
    restoreClient: () => void 0,
    removeClient: noop
  };
}
function throttle(func, wait = 100) {
  let timer = null;
  let params;
  return function(...args) {
    params = args;
    if (timer === null) {
      timer = setTimeout(() => {
        func(...params);
        timer = null;
      }, wait);
    }
  };
}
function noop() {
}

// node_modules/@tanstack/query-persist-client-core/build/lib/persist.mjs
var cacheableEventTypes = ["added", "removed", "updated"];
function isCacheableEventType(eventType) {
  return cacheableEventTypes.includes(eventType);
}
async function persistQueryClientRestore({
  queryClient,
  persister,
  maxAge = 1e3 * 60 * 60 * 24,
  buster = "",
  hydrateOptions
}) {
  try {
    const persistedClient = await persister.restoreClient();
    if (persistedClient) {
      if (persistedClient.timestamp) {
        const expired = Date.now() - persistedClient.timestamp > maxAge;
        const busted = persistedClient.buster !== buster;
        if (expired || busted) {
          persister.removeClient();
        } else {
          hydrate(queryClient, persistedClient.clientState, hydrateOptions);
        }
      } else {
        persister.removeClient();
      }
    }
  } catch (err) {
    if (true) {
      queryClient.getLogger().error(err);
      queryClient.getLogger().warn("Encountered an error attempting to restore client cache from persisted location. As a precaution, the persisted cache will be discarded.");
    }
    persister.removeClient();
  }
}
async function persistQueryClientSave({
  queryClient,
  persister,
  buster = "",
  dehydrateOptions
}) {
  const persistClient = {
    buster,
    timestamp: Date.now(),
    clientState: dehydrate(queryClient, dehydrateOptions)
  };
  await persister.persistClient(persistClient);
}
function persistQueryClientSubscribe(props) {
  const unsubscribeQueryCache = props.queryClient.getQueryCache().subscribe((event) => {
    if (isCacheableEventType(event.type)) {
      persistQueryClientSave(props);
    }
  });
  const unusbscribeMutationCache = props.queryClient.getMutationCache().subscribe((event) => {
    if (isCacheableEventType(event.type)) {
      persistQueryClientSave(props);
    }
  });
  return () => {
    unsubscribeQueryCache();
    unusbscribeMutationCache();
  };
}
function persistQueryClient(props) {
  let hasUnsubscribed = false;
  let persistQueryClientUnsubscribe;
  const unsubscribe = () => {
    hasUnsubscribed = true;
    persistQueryClientUnsubscribe == null ? void 0 : persistQueryClientUnsubscribe();
  };
  const restorePromise = persistQueryClientRestore(props).then(() => {
    if (!hasUnsubscribed) {
      persistQueryClientUnsubscribe = persistQueryClientSubscribe(props);
    }
  });
  return [unsubscribe, restorePromise];
}

// node_modules/@tanstack/react-query-persist-client/build/lib/PersistQueryClientProvider.mjs
var React = __toESM(require_react(), 1);

// node_modules/wagmi/dist/index.js
var React2 = __toESM(require_react());
var React9 = __toESM(require_react());
var React22 = __toESM(require_react());
var pkg = __toESM(require_shim());
var import_with_selector = __toESM(require_with_selector());
var React3 = __toESM(require_react());
var import_with_selector2 = __toESM(require_with_selector());
var React4 = __toESM(require_react());
var React7 = __toESM(require_react());
var React5 = __toESM(require_react());
var React6 = __toESM(require_react());
var React8 = __toESM(require_react());
var import_with_selector3 = __toESM(require_with_selector());
var React10 = __toESM(require_react());
var React11 = __toESM(require_react());
var import_react = __toESM(require_react());
var React12 = __toESM(require_react());
var React13 = __toESM(require_react());
var React14 = __toESM(require_react());
var React15 = __toESM(require_react());
var React16 = __toESM(require_react());
var React17 = __toESM(require_react());
var React18 = __toESM(require_react());
var React19 = __toESM(require_react());
var React20 = __toESM(require_react());
var React21 = __toESM(require_react());
function createConfig2({
  queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        cacheTime: 1e3 * 60 * 60 * 24,
        networkMode: "offlineFirst",
        refetchOnWindowFocus: false,
        retry: 0
      },
      mutations: {
        networkMode: "offlineFirst"
      }
    }
  }),
  storage = createStorage({
    storage: typeof window !== "undefined" && window.localStorage ? window.localStorage : noopStorage
  }),
  persister = typeof window !== "undefined" ? createSyncStoragePersister({
    key: "cache",
    storage,
    serialize: (x) => x,
    deserialize: (x) => x
  }) : void 0,
  ...args
}) {
  const config = createConfig({
    ...args,
    storage
  });
  if (persister)
    persistQueryClient({
      queryClient,
      persister,
      dehydrateOptions: {
        shouldDehydrateQuery: (query) => query.cacheTime !== 0 && query.queryKey[0].persist !== false
      }
    });
  return Object.assign(config, { queryClient });
}
var Context = React2.createContext(void 0);
var queryClientContext = React2.createContext(
  void 0
);
function WagmiConfig({
  children,
  config
}) {
  return React2.createElement(Context.Provider, {
    children: React2.createElement(QueryClientProvider, {
      children,
      client: config.queryClient,
      context: queryClientContext
    }),
    value: config
  });
}
function useConfig() {
  const config = React2.useContext(Context);
  if (!config)
    throw new Error(
      [
        "`useConfig` must be used within `WagmiConfig`.\n",
        "Read more: https://wagmi.sh/react/WagmiConfig"
      ].join("\n")
    );
  return config;
}
var useSyncExternalStore2 = pkg.useSyncExternalStore;
function isQueryKey(value) {
  return Array.isArray(value);
}
function isPlainObject(o) {
  if (!hasObjectPrototype(o)) {
    return false;
  }
  const ctor = o.constructor;
  if (typeof ctor === "undefined") {
    return true;
  }
  const prot = ctor.prototype;
  if (!hasObjectPrototype(prot)) {
    return false;
  }
  if (!prot.hasOwnProperty("isPrototypeOf")) {
    return false;
  }
  return true;
}
function hasObjectPrototype(o) {
  return Object.prototype.toString.call(o) === "[object Object]";
}
function parseQueryArgs(arg1, arg2, arg3) {
  if (!isQueryKey(arg1)) {
    return arg1;
  }
  if (typeof arg2 === "function") {
    return { ...arg3, queryKey: arg1, queryFn: arg2 };
  }
  return { ...arg2, queryKey: arg1 };
}
function queryKeyHashFn(queryKey17) {
  return JSON.stringify(
    queryKey17,
    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {
      result[key] = val[key];
      return result;
    }, {}) : typeof val === "bigint" ? val.toString() : val
  );
}
function shouldThrowError(_useErrorBoundary, params) {
  if (typeof _useErrorBoundary === "function") {
    return _useErrorBoundary(...params);
  }
  return !!_useErrorBoundary;
}
function trackResult(result, observer) {
  const trackedResult = {};
  Object.keys(result).forEach((key) => {
    Object.defineProperty(trackedResult, key, {
      configurable: false,
      enumerable: true,
      get: () => {
        observer.trackedProps.add(key);
        return result[key];
      }
    });
  });
  return trackedResult;
}
function useBaseQuery(options, Observer) {
  const queryClient = useQueryClient({ context: options.context });
  const isRestoring = useIsRestoring();
  const errorResetBoundary = useQueryErrorResetBoundary();
  const defaultedOptions = queryClient.defaultQueryOptions({
    ...options,
    queryKeyHashFn
  });
  defaultedOptions._optimisticResults = isRestoring ? "isRestoring" : "optimistic";
  if (defaultedOptions.onError) {
    defaultedOptions.onError = notifyManager.batchCalls(
      defaultedOptions.onError
    );
  }
  if (defaultedOptions.onSuccess) {
    defaultedOptions.onSuccess = notifyManager.batchCalls(
      defaultedOptions.onSuccess
    );
  }
  if (defaultedOptions.onSettled) {
    defaultedOptions.onSettled = notifyManager.batchCalls(
      defaultedOptions.onSettled
    );
  }
  if (defaultedOptions.suspense) {
    if (typeof defaultedOptions.staleTime !== "number") {
      defaultedOptions.staleTime = 1e3;
    }
  }
  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {
    if (!errorResetBoundary.isReset()) {
      defaultedOptions.retryOnMount = false;
    }
  }
  const [observer] = React22.useState(
    () => new Observer(
      queryClient,
      defaultedOptions
    )
  );
  const result = observer.getOptimisticResult(defaultedOptions);
  useSyncExternalStore2(
    React22.useCallback(
      (onStoreChange) => isRestoring ? () => void 0 : observer.subscribe(notifyManager.batchCalls(onStoreChange)),
      [observer, isRestoring]
    ),
    () => observer.getCurrentResult(),
    () => observer.getCurrentResult()
  );
  React22.useEffect(() => {
    errorResetBoundary.clearReset();
  }, [errorResetBoundary]);
  React22.useEffect(() => {
    observer.setOptions(defaultedOptions, { listeners: false });
  }, [defaultedOptions, observer]);
  if (defaultedOptions.suspense && result.isLoading && result.isFetching && !isRestoring) {
    throw observer.fetchOptimistic(defaultedOptions).then(({ data }) => {
      var _a, _b;
      (_a = defaultedOptions.onSuccess) == null ? void 0 : _a.call(defaultedOptions, data);
      (_b = defaultedOptions.onSettled) == null ? void 0 : _b.call(defaultedOptions, data, null);
    }).catch((error) => {
      var _a, _b;
      errorResetBoundary.clearReset();
      (_a = defaultedOptions.onError) == null ? void 0 : _a.call(defaultedOptions, error);
      (_b = defaultedOptions.onSettled) == null ? void 0 : _b.call(defaultedOptions, void 0, error);
    });
  }
  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && shouldThrowError(defaultedOptions.useErrorBoundary, [
    result.error,
    observer.getCurrentQuery()
  ])) {
    throw result.error;
  }
  const status = result.status === "loading" && result.fetchStatus === "idle" ? "idle" : result.status;
  const isIdle = status === "idle";
  const isLoading = status === "loading" && result.fetchStatus === "fetching";
  return {
    ...result,
    defaultedOptions,
    isIdle,
    isLoading,
    observer,
    status
  };
}
function useInfiniteQuery(arg1, arg2, arg3) {
  const parsedOptions = parseQueryArgs(arg1, arg2, arg3);
  const baseQuery = useBaseQuery(
    { context: queryClientContext, ...parsedOptions },
    InfiniteQueryObserver
  );
  const result = {
    data: baseQuery.data,
    error: baseQuery.error,
    fetchNextPage: baseQuery.fetchNextPage,
    fetchStatus: baseQuery.fetchStatus,
    hasNextPage: baseQuery.hasNextPage,
    isError: baseQuery.isError,
    isFetched: baseQuery.isFetched,
    isFetchedAfterMount: baseQuery.isFetchedAfterMount,
    isFetching: baseQuery.isFetching,
    isFetchingNextPage: baseQuery.isFetchingNextPage,
    isIdle: baseQuery.isIdle,
    isLoading: baseQuery.isLoading,
    isRefetching: baseQuery.isRefetching,
    isSuccess: baseQuery.isSuccess,
    refetch: baseQuery.refetch,
    status: baseQuery.status,
    internal: {
      dataUpdatedAt: baseQuery.dataUpdatedAt,
      errorUpdatedAt: baseQuery.errorUpdatedAt,
      failureCount: baseQuery.failureCount,
      isFetchedAfterMount: baseQuery.isFetchedAfterMount,
      isLoadingError: baseQuery.isLoadingError,
      isPaused: baseQuery.isPaused,
      isPlaceholderData: baseQuery.isPlaceholderData,
      isPreviousData: baseQuery.isPreviousData,
      isRefetchError: baseQuery.isRefetchError,
      isStale: baseQuery.isStale,
      remove: baseQuery.remove
    }
  };
  return !baseQuery.defaultedOptions.notifyOnChangeProps ? trackResult(result, baseQuery.observer) : result;
}
function useMutation2(arg1, arg2, arg3) {
  const options = parseMutationArgs(arg1, arg2, arg3);
  return useMutation({ context: queryClientContext, ...options });
}
function useQuery(arg1, arg2, arg3) {
  const parsedOptions = parseQueryArgs(arg1, arg2, arg3);
  const baseQuery = useBaseQuery({ context: queryClientContext, ...parsedOptions }, QueryObserver);
  const result = {
    data: baseQuery.data,
    error: baseQuery.error,
    fetchStatus: baseQuery.fetchStatus,
    isError: baseQuery.isError,
    isFetched: baseQuery.isFetched,
    isFetchedAfterMount: baseQuery.isFetchedAfterMount,
    isFetching: baseQuery.isFetching,
    isIdle: baseQuery.isIdle,
    isLoading: baseQuery.isLoading,
    isRefetching: baseQuery.isRefetching,
    isSuccess: baseQuery.isSuccess,
    refetch: baseQuery.refetch,
    status: baseQuery.status,
    internal: {
      dataUpdatedAt: baseQuery.dataUpdatedAt,
      errorUpdatedAt: baseQuery.errorUpdatedAt,
      failureCount: baseQuery.failureCount,
      isFetchedAfterMount: baseQuery.isFetchedAfterMount,
      isLoadingError: baseQuery.isLoadingError,
      isPaused: baseQuery.isPaused,
      isPlaceholderData: baseQuery.isPlaceholderData,
      isPreviousData: baseQuery.isPreviousData,
      isRefetchError: baseQuery.isRefetchError,
      isStale: baseQuery.isStale,
      remove: baseQuery.remove
    }
  };
  return !baseQuery.defaultedOptions.notifyOnChangeProps ? trackResult(result, baseQuery.observer) : result;
}
var useQueryClient2 = () => useQueryClient({ context: queryClientContext });
function usePublicClient({
  chainId
} = {}) {
  return (0, import_with_selector.useSyncExternalStoreWithSelector)(
    (cb) => watchPublicClient({ chainId }, cb),
    () => getPublicClient({ chainId }),
    () => getPublicClient({ chainId }),
    (x) => x,
    (a, b) => a.uid === b.uid
  );
}
function queryKey({ chainId }) {
  return [{ entity: "walletClient", chainId, persist: false }];
}
function queryFn({
  queryKey: [{ chainId }]
}) {
  return getWalletClient({ chainId });
}
function useWalletClient({
  chainId: chainId_,
  suspense,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const { connector } = useAccount();
  const chainId = useChainId({ chainId: chainId_ });
  const walletClientQuery = useQuery(queryKey({ chainId }), queryFn, {
    cacheTime: 0,
    enabled: Boolean(connector),
    staleTime: Infinity,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
  const queryClient = useQueryClient2();
  React3.useEffect(() => {
    const unwatch = watchWalletClient({ chainId }, (walletClient) => {
      if (walletClient)
        queryClient.invalidateQueries(queryKey({ chainId }));
      else
        queryClient.removeQueries(queryKey({ chainId }));
    });
    return unwatch;
  }, [queryClient, chainId]);
  return walletClientQuery;
}
function useWebSocketPublicClient({ chainId } = {}) {
  return (0, import_with_selector2.useSyncExternalStoreWithSelector)(
    (cb) => watchWebSocketPublicClient({ chainId }, cb),
    () => getWebSocketPublicClient({ chainId }),
    () => getWebSocketPublicClient({ chainId }),
    (x) => x,
    (a, b) => (a == null ? void 0 : a.uid) === (b == null ? void 0 : b.uid)
  );
}
function useChainId({ chainId } = {}) {
  const publicClient = usePublicClient({ chainId });
  return publicClient.chain.id;
}
function useForceUpdate() {
  const [, forceUpdate] = React4.useReducer((x) => x + 1, 0);
  return forceUpdate;
}
function queryKey2({ chainId, scopeKey }) {
  return [{ entity: "blockNumber", chainId, scopeKey }];
}
function queryFn2({
  queryKey: [{ chainId }]
}) {
  return fetchBlockNumber({ chainId });
}
function useBlockNumber({
  cacheTime = 0,
  chainId: chainId_,
  enabled = true,
  scopeKey,
  staleTime,
  suspense,
  watch = false,
  onBlock,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  const publicClient = usePublicClient({ chainId });
  const webSocketPublicClient = useWebSocketPublicClient({ chainId });
  const queryClient = useQueryClient2();
  React5.useEffect(() => {
    if (!enabled)
      return;
    if (!watch && !onBlock)
      return;
    const publicClient_ = webSocketPublicClient ?? publicClient;
    const unwatch = publicClient_.watchBlockNumber({
      onBlockNumber: (blockNumber) => {
        if (watch)
          queryClient.setQueryData(queryKey2({ chainId, scopeKey }), blockNumber);
        if (onBlock)
          onBlock(blockNumber);
      },
      emitOnBegin: true
    });
    return unwatch;
  }, [
    chainId,
    scopeKey,
    onBlock,
    publicClient,
    queryClient,
    watch,
    webSocketPublicClient,
    enabled
  ]);
  return useQuery(queryKey2({ scopeKey, chainId }), queryFn2, {
    cacheTime,
    enabled,
    staleTime,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
}
function queryKey3({
  chainId,
  formatUnits,
  scopeKey
}) {
  return [{ entity: "feeData", chainId, formatUnits, scopeKey }];
}
function queryFn3({
  queryKey: [{ chainId, formatUnits }]
}) {
  return fetchFeeData({ chainId, formatUnits });
}
function useFeeData({
  cacheTime,
  chainId: chainId_,
  enabled = true,
  formatUnits = "gwei",
  scopeKey,
  staleTime,
  suspense,
  watch,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  const queryKey_ = React6.useMemo(
    () => queryKey3({
      chainId,
      formatUnits,
      scopeKey
    }),
    [chainId, formatUnits, scopeKey]
  );
  const feeDataQuery = useQuery(queryKey_, queryFn3, {
    cacheTime,
    enabled,
    staleTime,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
  useInvalidateOnBlock({
    chainId,
    enabled: Boolean(enabled && watch),
    queryKey: queryKey_
  });
  return feeDataQuery;
}
function useInvalidateOnBlock({
  chainId,
  enabled,
  queryKey: queryKey17
}) {
  const queryClient = useQueryClient2();
  const onBlock = React7.useCallback(
    () => queryClient.invalidateQueries({ queryKey: queryKey17 }, { cancelRefetch: false }),
    [queryClient, queryKey17]
  );
  useBlockNumber({
    chainId,
    enabled,
    onBlock: enabled ? onBlock : void 0,
    scopeKey: enabled ? void 0 : "idle"
  });
}
var isPlainObject2 = (obj) => typeof obj === "object" && !Array.isArray(obj);
function useSyncExternalStoreWithTracked(subscribe, getSnapshot, getServerSnapshot = getSnapshot, isEqual = deepEqual) {
  const trackedKeys = React8.useRef([]);
  const result = (0, import_with_selector3.useSyncExternalStoreWithSelector)(
    subscribe,
    getSnapshot,
    getServerSnapshot,
    (x) => x,
    (a, b) => {
      if (isPlainObject2(a) && isPlainObject2(b) && trackedKeys.current.length) {
        for (const key of trackedKeys.current) {
          const equal = isEqual(
            a[key],
            b[key]
          );
          if (!equal)
            return false;
        }
        return true;
      }
      return isEqual(a, b);
    }
  );
  if (isPlainObject2(result)) {
    const trackedResult = { ...result };
    Object.defineProperties(
      trackedResult,
      Object.entries(trackedResult).reduce(
        (res, [key, value]) => {
          return {
            ...res,
            [key]: {
              configurable: false,
              enumerable: true,
              get: () => {
                if (!trackedKeys.current.includes(key)) {
                  trackedKeys.current.push(key);
                }
                return value;
              }
            }
          };
        },
        {}
      )
    );
    return trackedResult;
  }
  return result;
}
function useAccount({ onConnect, onDisconnect } = {}) {
  const config = useConfig();
  const watchAccount_ = React9.useCallback(
    (callback) => watchAccount(callback),
    [config]
  );
  const account = useSyncExternalStoreWithTracked(watchAccount_, getAccount);
  const previousAccountRef = React9.useRef();
  const previousAccount = previousAccountRef.current;
  React9.useEffect(() => {
    if ((previousAccount == null ? void 0 : previousAccount.status) !== "connected" && account.status === "connected") {
      onConnect == null ? void 0 : onConnect({
        address: account.address,
        connector: account.connector,
        isReconnected: (previousAccount == null ? void 0 : previousAccount.status) === "reconnecting" || (previousAccount == null ? void 0 : previousAccount.status) === void 0
      });
    }
    if ((previousAccount == null ? void 0 : previousAccount.status) === "connected" && account.status === "disconnected") {
      onDisconnect == null ? void 0 : onDisconnect();
    }
    previousAccountRef.current = account;
  }, [onConnect, onDisconnect, previousAccount, account]);
  return account;
}
function queryKey4({
  address,
  chainId,
  formatUnits,
  scopeKey,
  token
}) {
  return [
    {
      entity: "balance",
      address,
      chainId,
      formatUnits,
      scopeKey,
      token
    }
  ];
}
function queryFn4({
  queryKey: [{ address, chainId, formatUnits, token }]
}) {
  if (!address)
    throw new Error("address is required");
  return fetchBalance({ address, chainId, formatUnits, token });
}
function useBalance({
  address,
  cacheTime,
  chainId: chainId_,
  enabled = true,
  formatUnits,
  scopeKey,
  staleTime,
  suspense,
  token,
  watch,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  const queryKey_ = React10.useMemo(
    () => queryKey4({ address, chainId, formatUnits, scopeKey, token }),
    [address, chainId, formatUnits, scopeKey, token]
  );
  const balanceQuery = useQuery(queryKey_, queryFn4, {
    cacheTime,
    enabled: Boolean(enabled && address),
    staleTime,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
  useInvalidateOnBlock({
    chainId,
    enabled: Boolean(enabled && watch && address),
    queryKey: queryKey_
  });
  return balanceQuery;
}
var mutationKey = (args) => [{ entity: "connect", ...args }];
var mutationFn = (args) => {
  const { connector, chainId } = args;
  if (!connector)
    throw new Error("connector is required");
  return connect({ connector, chainId });
};
function useConnect({
  chainId,
  connector,
  onError,
  onMutate,
  onSettled,
  onSuccess
} = {}) {
  const config = useConfig();
  const {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    mutate,
    mutateAsync,
    reset,
    status,
    variables
  } = useMutation2(mutationKey({ connector, chainId }), mutationFn, {
    onError,
    onMutate,
    onSettled,
    onSuccess
  });
  const connect2 = React11.useCallback(
    (args) => {
      return mutate({
        chainId: (args == null ? void 0 : args.chainId) ?? chainId,
        connector: (args == null ? void 0 : args.connector) ?? connector
      });
    },
    [chainId, connector, mutate]
  );
  const connectAsync = React11.useCallback(
    (args) => {
      return mutateAsync({
        chainId: (args == null ? void 0 : args.chainId) ?? chainId,
        connector: (args == null ? void 0 : args.connector) ?? connector
      });
    },
    [chainId, connector, mutateAsync]
  );
  return {
    connect: connect2,
    connectAsync,
    connectors: config.connectors,
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    pendingConnector: variables == null ? void 0 : variables.connector,
    reset,
    status,
    variables
  };
}
var mutationKey2 = [{ entity: "disconnect" }];
var mutationFn2 = () => disconnect();
function useDisconnect({
  onError,
  onMutate,
  onSettled,
  onSuccess
} = {}) {
  const {
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    mutate: disconnect2,
    mutateAsync: disconnectAsync,
    reset,
    status
  } = useMutation2(mutationKey2, mutationFn2, {
    ...onError ? {
      onError(error2, _variables, context) {
        onError(error2, context);
      }
    } : {},
    onMutate,
    ...onSettled ? {
      onSettled(_data, error2, _variables, context) {
        onSettled(error2, context);
      }
    } : {},
    ...onSuccess ? {
      onSuccess(_data, _variables, context) {
        onSuccess(context);
      }
    } : {}
  });
  return {
    disconnect: disconnect2,
    disconnectAsync,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    reset,
    status
  };
}
function useNetwork() {
  const config = useConfig();
  const watchNetwork_ = (0, import_react.useCallback)(
    (callback) => watchNetwork(callback),
    [config]
  );
  return useSyncExternalStoreWithTracked(watchNetwork_, getNetwork);
}
var mutationKey3 = (args) => [{ entity: "signMessage", ...args }];
var mutationFn3 = (args) => {
  const { message } = args;
  if (!message)
    throw new Error("message is required");
  return signMessage({ message });
};
function useSignMessage({
  message,
  onError,
  onMutate,
  onSettled,
  onSuccess
} = {}) {
  const {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    mutate,
    mutateAsync,
    reset,
    status,
    variables
  } = useMutation2(mutationKey3({ message }), mutationFn3, {
    onError,
    onMutate,
    onSettled,
    onSuccess
  });
  const signMessage2 = React12.useCallback(
    (args) => mutate(args || { message }),
    [message, mutate]
  );
  const signMessageAsync = React12.useCallback(
    (args) => mutateAsync(args || { message }),
    [message, mutateAsync]
  );
  return {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    reset,
    signMessage: signMessage2,
    signMessageAsync,
    status,
    variables
  };
}
function mutationKey4({ domain, types, message, primaryType }) {
  return [
    { entity: "signTypedData", domain, types, message, primaryType }
  ];
}
function mutationFn4(args) {
  const { domain, types, primaryType, message } = args;
  if (!domain)
    throw new Error("domain is required");
  if (!types)
    throw new Error("types is required");
  if (!primaryType)
    throw new Error("primaryType is required");
  if (!message)
    throw new Error("message is required");
  return signTypedData({
    domain,
    message,
    primaryType,
    types
  });
}
function useSignTypedData({
  domain,
  types,
  message,
  primaryType,
  onError,
  onMutate,
  onSettled,
  onSuccess
} = {}) {
  const {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    mutate,
    mutateAsync,
    reset,
    status,
    variables
  } = useMutation2(
    mutationKey4({
      domain,
      message,
      primaryType,
      types
    }),
    mutationFn4,
    {
      onError,
      onMutate,
      onSettled,
      onSuccess
    }
  );
  const signTypedData2 = React13.useCallback(
    (args) => mutate({
      domain: (args == null ? void 0 : args.domain) ?? domain,
      types: (args == null ? void 0 : args.types) ?? types,
      message: (args == null ? void 0 : args.message) ?? message,
      primaryType: (args == null ? void 0 : args.primaryType) ?? primaryType
    }),
    [domain, types, primaryType, message, mutate]
  );
  const signTypedDataAsync = React13.useCallback(
    (args) => mutateAsync({
      domain: (args == null ? void 0 : args.domain) ?? domain,
      types: (args == null ? void 0 : args.types) ?? types,
      message: (args == null ? void 0 : args.message) ?? message,
      primaryType: (args == null ? void 0 : args.primaryType) ?? primaryType
    }),
    [domain, types, primaryType, message, mutateAsync]
  );
  return {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    reset,
    signTypedData: signTypedData2,
    signTypedDataAsync,
    status,
    variables
  };
}
var mutationKey5 = (args) => [{ entity: "switchNetwork", ...args }];
var mutationFn5 = (args) => {
  const { chainId } = args;
  if (!chainId)
    throw new Error("chainId is required");
  return switchNetwork({ chainId });
};
function useSwitchNetwork({
  chainId,
  throwForSwitchChainNotSupported,
  onError,
  onMutate,
  onSettled,
  onSuccess
} = {}) {
  var _a;
  const config = useConfig();
  const forceUpdate = useForceUpdate();
  const {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    mutate,
    mutateAsync,
    reset,
    status,
    variables
  } = useMutation2(mutationKey5({ chainId }), mutationFn5, {
    onError,
    onMutate,
    onSettled,
    onSuccess
  });
  const switchNetwork_ = React14.useCallback(
    (chainId_) => mutate({ chainId: chainId_ ?? chainId }),
    [chainId, mutate]
  );
  const switchNetworkAsync_ = React14.useCallback(
    (chainId_) => mutateAsync({ chainId: chainId_ ?? chainId }),
    [chainId, mutateAsync]
  );
  React14.useEffect(() => {
    const unwatch = config.subscribe(
      ({ chains, connector }) => ({
        chains,
        connector
      }),
      forceUpdate
    );
    return unwatch;
  }, [config, forceUpdate]);
  let switchNetwork2;
  let switchNetworkAsync;
  const supportsSwitchChain = !!((_a = config.connector) == null ? void 0 : _a.switchChain);
  if (throwForSwitchChainNotSupported || supportsSwitchChain) {
    switchNetwork2 = switchNetwork_;
    switchNetworkAsync = switchNetworkAsync_;
  }
  return {
    chains: config.chains ?? [],
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    pendingChainId: variables == null ? void 0 : variables.chainId,
    reset,
    status,
    switchNetwork: switchNetwork2,
    switchNetworkAsync,
    variables
  };
}
function useContractEvent({
  address,
  chainId,
  abi,
  listener,
  eventName
} = {}) {
  const publicClient = usePublicClient({ chainId });
  const webSocketPublicClient = useWebSocketPublicClient({ chainId });
  const unwatch = React15.useRef();
  React15.useEffect(() => {
    if (!abi || !address || !eventName)
      return;
    const publicClient_ = webSocketPublicClient || publicClient;
    unwatch.current = publicClient_.watchContractEvent({
      abi,
      address,
      eventName,
      onLogs: listener
    });
    return unwatch.current;
  }, [abi, address, eventName, publicClient.uid, webSocketPublicClient == null ? void 0 : webSocketPublicClient.uid]);
  return unwatch.current;
}
function queryKey5({
  allowFailure,
  blockNumber,
  blockTag,
  cacheKey,
  scopeKey
}) {
  return [
    {
      entity: "readContractsInfinite",
      allowFailure,
      blockNumber,
      blockTag,
      cacheKey,
      scopeKey
    }
  ];
}
function queryFn5({
  contracts
}) {
  return ({
    queryKey: [{ allowFailure, blockNumber, blockTag }],
    pageParam
  }) => {
    return readContracts({
      allowFailure,
      blockNumber,
      blockTag,
      contracts: contracts(pageParam || void 0)
    });
  };
}
function useContractInfiniteReads({
  allowFailure,
  blockNumber,
  blockTag,
  cacheKey,
  cacheTime,
  contracts,
  enabled: enabled_ = true,
  getNextPageParam,
  isDataEqual,
  keepPreviousData,
  onError,
  onSettled,
  onSuccess,
  scopeKey,
  select,
  staleTime,
  structuralSharing = (oldData, newData) => deepEqual(oldData, newData) ? oldData : replaceEqualDeep(oldData, newData),
  suspense
}) {
  const queryKey_ = React16.useMemo(
    () => queryKey5({ allowFailure, blockNumber, blockTag, cacheKey, scopeKey }),
    [allowFailure, blockNumber, blockTag, cacheKey, scopeKey]
  );
  const enabled = React16.useMemo(() => {
    const enabled2 = Boolean(enabled_ && contracts);
    return enabled2;
  }, [contracts, enabled_]);
  return useInfiniteQuery(queryKey_, queryFn5({ contracts }), {
    cacheTime,
    enabled,
    getNextPageParam,
    isDataEqual,
    keepPreviousData,
    select,
    staleTime,
    structuralSharing,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
}
function paginatedIndexesConfig(fn, {
  perPage,
  start,
  direction
}) {
  const contracts = (page = 0) => [...Array(perPage).keys()].map((index) => {
    return direction === "increment" ? start + index + page * perPage : start - index - page * perPage;
  }).filter((index) => index >= 0).flatMap(fn);
  return {
    contracts,
    getNextPageParam(lastPage, pages) {
      return (lastPage == null ? void 0 : lastPage.length) === perPage ? pages.length : void 0;
    }
  };
}
function queryKey6({
  account,
  address,
  args,
  blockNumber,
  blockTag,
  chainId,
  functionName,
  scopeKey
}) {
  return [
    {
      entity: "readContract",
      account,
      address,
      args,
      blockNumber,
      blockTag,
      chainId,
      functionName,
      scopeKey
    }
  ];
}
function queryFn6({ abi }) {
  return async ({
    queryKey: [
      { account, address, args, blockNumber, blockTag, chainId, functionName }
    ]
  }) => {
    if (!abi)
      throw new Error("abi is required");
    if (!address)
      throw new Error("address is required");
    return await readContract({
      account,
      address,
      args,
      blockNumber,
      blockTag,
      chainId,
      abi,
      functionName
    }) ?? null;
  };
}
function useContractRead({
  abi,
  address,
  account,
  args,
  blockNumber: blockNumberOverride,
  blockTag,
  cacheOnBlock = false,
  cacheTime,
  chainId: chainId_,
  enabled: enabled_ = true,
  functionName,
  isDataEqual,
  keepPreviousData,
  onError,
  onSettled,
  onSuccess,
  scopeKey,
  select,
  staleTime,
  structuralSharing = (oldData, newData) => deepEqual(oldData, newData) ? oldData : replaceEqualDeep(oldData, newData),
  suspense,
  watch
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  const { data: blockNumber_ } = useBlockNumber({
    chainId,
    enabled: watch || cacheOnBlock,
    scopeKey: watch || cacheOnBlock ? void 0 : "idle",
    watch
  });
  const blockNumber = blockNumberOverride ?? blockNumber_;
  const queryKey_ = React17.useMemo(
    () => queryKey6({
      account,
      address,
      args,
      blockNumber: cacheOnBlock ? blockNumber : void 0,
      blockTag,
      chainId,
      functionName,
      scopeKey
    }),
    [
      account,
      address,
      args,
      blockNumber,
      blockTag,
      cacheOnBlock,
      chainId,
      functionName,
      scopeKey
    ]
  );
  const enabled = React17.useMemo(() => {
    let enabled2 = Boolean(enabled_ && abi && address && functionName);
    if (cacheOnBlock)
      enabled2 = Boolean(enabled2 && blockNumber);
    return enabled2;
  }, [abi, address, blockNumber, cacheOnBlock, enabled_, functionName]);
  useInvalidateOnBlock({
    chainId,
    enabled: Boolean(enabled && watch && !cacheOnBlock),
    queryKey: queryKey_
  });
  return useQuery(
    queryKey_,
    queryFn6({
      abi
    }),
    {
      cacheTime,
      enabled,
      isDataEqual,
      keepPreviousData,
      select,
      staleTime,
      structuralSharing,
      suspense,
      onError,
      onSettled,
      onSuccess
    }
  );
}
function queryKey7({
  allowFailure,
  blockNumber,
  blockTag,
  chainId,
  contracts,
  scopeKey
}) {
  return [
    {
      entity: "readContracts",
      allowFailure,
      blockNumber,
      blockTag,
      chainId,
      scopeKey,
      contracts: (contracts ?? []).map(({ address, args, chainId: chainId2, functionName }) => ({
        address,
        args,
        chainId: chainId2,
        functionName
      }))
    }
  ];
}
function queryFn7({ abis }) {
  return ({
    queryKey: [{ allowFailure, blockNumber, blockTag, contracts: contracts_ }]
  }) => {
    const contracts = contracts_.map((contract, i) => ({
      ...contract,
      abi: abis[i]
    }));
    return readContracts({
      allowFailure,
      contracts,
      blockNumber,
      blockTag
    });
  };
}
function useContractReads({
  allowFailure: allowFailure_,
  blockNumber: blockNumberOverride,
  blockTag,
  cacheOnBlock = false,
  cacheTime,
  contracts,
  enabled: enabled_ = true,
  isDataEqual,
  keepPreviousData,
  onError,
  onSettled,
  onSuccess,
  scopeKey,
  select,
  staleTime,
  structuralSharing = (oldData, newData) => deepEqual(oldData, newData) ? oldData : replaceEqualDeep(oldData, newData),
  suspense,
  watch
} = {}) {
  const allowFailure = allowFailure_ ?? true;
  const { data: blockNumber_ } = useBlockNumber({
    enabled: watch || cacheOnBlock,
    watch
  });
  const chainId = useChainId();
  const blockNumber = blockNumberOverride ?? blockNumber_;
  const queryKey_ = React18.useMemo(
    () => queryKey7({
      allowFailure,
      blockNumber: cacheOnBlock ? blockNumber : void 0,
      blockTag,
      chainId,
      contracts,
      scopeKey
    }),
    [
      allowFailure,
      blockNumber,
      blockTag,
      cacheOnBlock,
      chainId,
      scopeKey,
      contracts
    ]
  );
  const enabled = React18.useMemo(() => {
    let enabled2 = Boolean(
      enabled_ && (contracts == null ? void 0 : contracts.every(
        (x) => x.abi && x.address && x.functionName
      ))
    );
    if (cacheOnBlock)
      enabled2 = Boolean(enabled2 && blockNumber);
    return enabled2;
  }, [blockNumber, cacheOnBlock, contracts, enabled_]);
  useInvalidateOnBlock({
    enabled: Boolean(enabled && watch && !cacheOnBlock),
    queryKey: queryKey_
  });
  const abis = (contracts ?? []).map(
    ({ abi }) => abi
  );
  return useQuery(queryKey_, queryFn7({ abis }), {
    cacheTime,
    enabled,
    isDataEqual,
    keepPreviousData,
    staleTime,
    select,
    structuralSharing,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
}
function mutationKey6({
  address,
  abi,
  functionName,
  ...config
}) {
  const {
    args,
    accessList,
    account,
    dataSuffix,
    gas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    nonce,
    request,
    value
  } = config;
  return [
    {
      entity: "writeContract",
      address,
      args,
      abi,
      accessList,
      account,
      dataSuffix,
      functionName,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      request,
      value
    }
  ];
}
function mutationFn6(config) {
  if (config.mode === "prepared") {
    if (!config.request)
      throw new Error("request is required");
    return writeContract({
      mode: "prepared",
      request: config.request
    });
  }
  if (!config.address)
    throw new Error("address is required");
  if (!config.abi)
    throw new Error("abi is required");
  if (!config.functionName)
    throw new Error("functionName is required");
  return writeContract({
    address: config.address,
    args: config.args,
    chainId: config.chainId,
    abi: config.abi,
    functionName: config.functionName,
    accessList: config.accessList,
    account: config.account,
    dataSuffix: config.dataSuffix,
    gas: config.gas,
    gasPrice: config.gasPrice,
    maxFeePerGas: config.maxFeePerGas,
    maxPriorityFeePerGas: config.maxPriorityFeePerGas,
    nonce: config.nonce,
    value: config.value
  });
}
function useContractWrite(config) {
  const {
    address,
    abi,
    args,
    chainId,
    functionName,
    mode,
    request,
    dataSuffix
  } = config;
  const {
    accessList,
    account,
    gas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    nonce,
    value
  } = getSendTransactionParameters(config);
  const {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    mutate,
    mutateAsync,
    reset,
    status,
    variables
  } = useMutation2(
    mutationKey6({
      address,
      abi,
      functionName,
      chainId,
      mode,
      args,
      accessList,
      account,
      dataSuffix,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      request,
      value
    }),
    mutationFn6,
    {
      onError: config.onError,
      onMutate: config.onMutate,
      onSettled: config.onSettled,
      onSuccess: config.onSuccess
    }
  );
  const write = React19.useMemo(() => {
    if (config.mode === "prepared") {
      if (!request)
        return void 0;
      return () => mutate({
        mode: "prepared",
        request: config.request,
        chainId: config.chainId
      });
    }
    return (overrideConfig) => mutate({
      address,
      args,
      abi,
      functionName,
      chainId,
      accessList,
      account,
      dataSuffix,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      value,
      ...overrideConfig
    });
  }, [
    accessList,
    account,
    abi,
    address,
    args,
    chainId,
    config.chainId,
    config.mode,
    config.request,
    dataSuffix,
    functionName,
    gas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    mutate,
    nonce,
    request,
    value
  ]);
  const writeAsync = React19.useMemo(() => {
    if (config.mode === "prepared") {
      if (!request)
        return void 0;
      return () => mutateAsync({
        mode: "prepared",
        request: config.request
      });
    }
    return (overrideConfig) => mutateAsync({
      address,
      args,
      abi,
      chainId,
      functionName,
      accessList,
      account,
      dataSuffix,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      value,
      ...overrideConfig
    });
  }, [
    accessList,
    account,
    abi,
    address,
    args,
    chainId,
    config.mode,
    config.request,
    dataSuffix,
    functionName,
    gas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    mutateAsync,
    nonce,
    request,
    value
  ]);
  return {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    reset,
    status,
    variables,
    write,
    writeAsync
  };
}
function queryKey8({
  accessList,
  account,
  activeChainId,
  args,
  address,
  blockNumber,
  blockTag,
  chainId,
  dataSuffix,
  functionName,
  gas,
  gasPrice,
  maxFeePerGas,
  maxPriorityFeePerGas,
  nonce,
  scopeKey,
  walletClientAddress,
  value
}) {
  return [
    {
      entity: "prepareContractTransaction",
      accessList,
      account,
      activeChainId,
      address,
      args,
      blockNumber,
      blockTag,
      chainId,
      dataSuffix,
      functionName,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      scopeKey,
      walletClientAddress,
      value
    }
  ];
}
function queryFn8({
  abi,
  walletClient
}) {
  return ({
    queryKey: [
      {
        accessList,
        account,
        args,
        address,
        blockNumber,
        blockTag,
        chainId,
        dataSuffix,
        functionName,
        gas,
        gasPrice,
        maxFeePerGas,
        maxPriorityFeePerGas,
        nonce,
        value
      }
    ]
  }) => {
    if (!abi)
      throw new Error("abi is required");
    if (!address)
      throw new Error("address is required");
    if (!functionName)
      throw new Error("functionName is required");
    return prepareWriteContract({
      abi,
      accessList,
      account,
      args,
      address,
      blockNumber,
      blockTag,
      chainId,
      dataSuffix,
      functionName,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      walletClient,
      value
    });
  };
}
function usePrepareContractWrite({
  address,
  abi,
  functionName,
  chainId,
  args,
  cacheTime,
  dataSuffix,
  enabled = true,
  scopeKey,
  staleTime,
  suspense,
  onError,
  onSettled,
  onSuccess,
  ...config
} = {}) {
  const { chain: activeChain } = useNetwork();
  const { data: walletClient } = useWalletClient({ chainId });
  const {
    accessList,
    account,
    blockNumber,
    blockTag,
    gas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    nonce,
    value
  } = getCallParameters(config);
  const prepareContractWriteQuery = useQuery(
    queryKey8({
      accessList,
      account,
      activeChainId: activeChain == null ? void 0 : activeChain.id,
      address,
      args,
      blockNumber,
      blockTag,
      chainId,
      dataSuffix,
      functionName,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      scopeKey,
      walletClientAddress: walletClient == null ? void 0 : walletClient.account.address,
      value
    }),
    queryFn8({
      abi,
      walletClient
    }),
    {
      cacheTime,
      enabled: Boolean(
        enabled && abi && address && functionName && walletClient
      ),
      staleTime,
      suspense,
      onError,
      onSettled,
      onSuccess
    }
  );
  return Object.assign(prepareContractWriteQuery, {
    config: {
      chainId,
      mode: "prepared",
      request: void 0,
      ...prepareContractWriteQuery.data
    }
  });
}
function queryKey9({
  address,
  chainId,
  formatUnits,
  scopeKey
}) {
  return [{ entity: "token", address, chainId, formatUnits, scopeKey }];
}
function queryFn9({
  queryKey: [{ address, chainId, formatUnits }]
}) {
  if (!address)
    throw new Error("address is required");
  return fetchToken({ address, chainId, formatUnits });
}
function useToken({
  address,
  chainId: chainId_,
  formatUnits = "ether",
  cacheTime,
  enabled = true,
  scopeKey,
  staleTime = 1e3 * 60 * 60 * 24,
  suspense,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  return useQuery(
    queryKey9({ address, chainId, formatUnits, scopeKey }),
    queryFn9,
    {
      cacheTime,
      enabled: Boolean(enabled && address),
      staleTime,
      suspense,
      onError,
      onSettled,
      onSuccess
    }
  );
}
function queryKey10({ chainId, name, scopeKey }) {
  return [{ entity: "ensAddress", chainId, name, scopeKey }];
}
function queryFn10({
  queryKey: [{ chainId, name }]
}) {
  if (!name)
    throw new Error("name is required");
  return fetchEnsAddress({ chainId, name });
}
function useEnsAddress({
  cacheTime,
  chainId: chainId_,
  enabled = true,
  name,
  scopeKey,
  staleTime = 1e3 * 60 * 60 * 24,
  suspense,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  return useQuery(queryKey10({ chainId, name, scopeKey }), queryFn10, {
    cacheTime,
    enabled: Boolean(enabled && chainId && name),
    staleTime,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
}
function queryKey11({ name, chainId, scopeKey }) {
  return [{ entity: "ensAvatar", name, chainId, scopeKey }];
}
function queryFn11({
  queryKey: [{ name, chainId }]
}) {
  if (!name)
    throw new Error("name is required");
  return fetchEnsAvatar({ name, chainId });
}
function useEnsAvatar({
  cacheTime,
  chainId: chainId_,
  enabled = true,
  name,
  scopeKey,
  staleTime = 1e3 * 60 * 60 * 24,
  suspense,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  return useQuery(queryKey11({ name, chainId, scopeKey }), queryFn11, {
    cacheTime,
    enabled: Boolean(enabled && name && chainId),
    staleTime,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
}
function queryKey12({
  address,
  chainId,
  scopeKey
}) {
  return [{ entity: "ensName", address, chainId, scopeKey }];
}
function queryFn12({
  queryKey: [{ address, chainId }]
}) {
  if (!address)
    throw new Error("address is required");
  return fetchEnsName({ address, chainId });
}
function useEnsName({
  address,
  cacheTime,
  chainId: chainId_,
  enabled = true,
  scopeKey,
  staleTime = 1e3 * 60 * 60 * 24,
  suspense,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  return useQuery(queryKey12({ address, chainId, scopeKey }), queryFn12, {
    cacheTime,
    enabled: Boolean(enabled && address && chainId),
    staleTime,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
}
function queryKey13({ chainId, name, scopeKey }) {
  return [
    { entity: "ensResolver", chainId, name, scopeKey, persist: false }
  ];
}
function queryFn13({
  queryKey: [{ chainId, name }]
}) {
  if (!name)
    throw new Error("name is required");
  return fetchEnsResolver({ chainId, name });
}
function useEnsResolver({
  chainId: chainId_,
  name,
  enabled = true,
  scopeKey,
  suspense,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  return useQuery(queryKey13({ chainId, name, scopeKey }), queryFn13, {
    cacheTime: 0,
    enabled: Boolean(enabled && chainId && name),
    suspense,
    onError,
    onSettled,
    onSuccess
  });
}
function queryKey14({
  accessList,
  account,
  activeChainId,
  chainId,
  data,
  gas,
  gasPrice,
  maxFeePerGas,
  maxPriorityFeePerGas,
  nonce,
  to,
  value,
  scopeKey,
  walletClientAddress
}) {
  return [
    {
      entity: "prepareSendTransaction",
      activeChainId,
      accessList,
      account,
      chainId,
      data,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      to,
      value,
      scopeKey,
      walletClientAddress
    }
  ];
}
function queryFn14({ walletClient }) {
  return ({
    queryKey: [
      {
        accessList,
        account,
        chainId,
        data,
        gas,
        gasPrice,
        maxFeePerGas,
        maxPriorityFeePerGas,
        nonce,
        to,
        value
      }
    ]
  }) => {
    if (!to)
      throw new Error("to is required");
    return prepareSendTransaction({
      accessList,
      account,
      chainId,
      data,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      to,
      value,
      walletClient
    });
  };
}
function usePrepareSendTransaction({
  accessList,
  account,
  chainId,
  cacheTime,
  data,
  enabled = true,
  gas,
  gasPrice,
  maxFeePerGas,
  maxPriorityFeePerGas,
  nonce,
  scopeKey,
  staleTime,
  suspense,
  to,
  value,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const { chain: activeChain } = useNetwork();
  const { data: walletClient } = useWalletClient({ chainId });
  const prepareSendTransactionQuery = useQuery(
    queryKey14({
      accessList,
      activeChainId: activeChain == null ? void 0 : activeChain.id,
      account,
      chainId,
      data,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      scopeKey,
      to,
      value,
      walletClientAddress: walletClient == null ? void 0 : walletClient.account.address
    }),
    queryFn14({ walletClient }),
    {
      cacheTime,
      enabled: Boolean(enabled && walletClient && to),
      staleTime,
      suspense,
      onError,
      onSettled,
      onSuccess
    }
  );
  return Object.assign(prepareSendTransactionQuery, {
    config: {
      mode: "prepared",
      ...prepareSendTransactionQuery.isSuccess ? prepareSendTransactionQuery.data : void 0
    }
  });
}
var mutationKey7 = (args) => [{ entity: "sendTransaction", ...args }];
var mutationFn7 = ({
  accessList,
  account,
  chainId,
  data,
  gas,
  gasPrice,
  maxFeePerGas,
  maxPriorityFeePerGas,
  mode,
  nonce,
  to,
  value
}) => {
  if (!to)
    throw new Error("to is required.");
  return sendTransaction({
    accessList,
    account,
    chainId,
    data,
    gas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    mode,
    nonce,
    to,
    value
  });
};
function useSendTransaction({
  accessList,
  account,
  chainId,
  data: data_,
  gas,
  gasPrice,
  maxFeePerGas,
  maxPriorityFeePerGas,
  mode,
  nonce,
  to,
  value,
  onError,
  onMutate,
  onSettled,
  onSuccess
} = {}) {
  const {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    mutate,
    mutateAsync,
    reset,
    status,
    variables
  } = useMutation2(
    mutationKey7({
      accessList,
      account,
      chainId,
      data: data_,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      mode,
      nonce,
      to,
      value
    }),
    mutationFn7,
    {
      onError,
      onMutate,
      onSettled,
      onSuccess
    }
  );
  const sendTransaction2 = React20.useCallback(
    (args) => mutate({
      chainId,
      mode,
      ...args || {
        accessList,
        account,
        chainId,
        data: data_,
        gas,
        gasPrice,
        maxFeePerGas,
        maxPriorityFeePerGas,
        mode,
        nonce,
        value,
        to
      }
    }),
    [
      accessList,
      account,
      chainId,
      data_,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      mode,
      mutate,
      nonce,
      to,
      value
    ]
  );
  const sendTransactionAsync = React20.useCallback(
    (args) => mutateAsync({
      chainId,
      mode,
      ...args || {
        accessList,
        account,
        chainId,
        data: data_,
        gas,
        gasPrice,
        maxFeePerGas,
        maxPriorityFeePerGas,
        mode,
        nonce,
        value,
        to
      }
    }),
    [
      accessList,
      account,
      chainId,
      data_,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      mode,
      mutateAsync,
      nonce,
      to,
      value
    ]
  );
  return {
    data,
    error,
    isError,
    isIdle,
    isLoading,
    isSuccess,
    reset,
    sendTransaction: mode === "prepared" && !to ? void 0 : sendTransaction2,
    sendTransactionAsync: mode === "prepared" && !to ? void 0 : sendTransactionAsync,
    status,
    variables
  };
}
function queryKey15({ chainId, hash, scopeKey }) {
  return [{ entity: "transaction", chainId, hash, scopeKey }];
}
function queryFn15({
  queryKey: [{ chainId, hash }]
}) {
  if (!hash)
    throw new Error("hash is required");
  return fetchTransaction({ chainId, hash });
}
function useTransaction({
  cacheTime = 0,
  chainId: chainId_,
  enabled = true,
  hash,
  scopeKey,
  staleTime,
  suspense,
  onError,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  return useQuery(queryKey15({ chainId, hash, scopeKey }), queryFn15, {
    cacheTime,
    enabled: Boolean(enabled && hash),
    staleTime,
    suspense,
    onError,
    onSettled,
    onSuccess
  });
}
function queryKey16({
  confirmations,
  chainId,
  hash,
  scopeKey,
  timeout
}) {
  return [
    {
      entity: "waitForTransaction",
      confirmations,
      chainId,
      hash,
      scopeKey,
      timeout
    }
  ];
}
function queryFn16({
  onReplaced
}) {
  return ({
    queryKey: [{ chainId, confirmations, hash, timeout }]
  }) => {
    if (!hash)
      throw new Error("hash is required");
    return waitForTransaction({
      chainId,
      confirmations,
      hash,
      onReplaced,
      timeout
    });
  };
}
function useWaitForTransaction({
  chainId: chainId_,
  confirmations,
  hash,
  timeout,
  cacheTime,
  enabled = true,
  scopeKey,
  staleTime,
  suspense,
  onError,
  onReplaced,
  onSettled,
  onSuccess
} = {}) {
  const chainId = useChainId({ chainId: chainId_ });
  return useQuery(
    queryKey16({ chainId, confirmations, hash, scopeKey, timeout }),
    queryFn16({ onReplaced }),
    {
      cacheTime,
      enabled: Boolean(enabled && hash),
      staleTime,
      suspense,
      onError,
      onSettled,
      onSuccess
    }
  );
}
function useWatchPendingTransactions({
  chainId: chainId_,
  enabled = true,
  listener
}) {
  const chainId = useChainId({ chainId: chainId_ });
  const publicClient = usePublicClient({ chainId });
  const webSocketPublicClient = useWebSocketPublicClient({ chainId });
  React21.useEffect(() => {
    if (!enabled)
      return;
    const publicClient_ = webSocketPublicClient ?? publicClient;
    return publicClient_.watchPendingTransactions({
      onTransactions: listener
    });
  }, [enabled, listener, publicClient, webSocketPublicClient]);
}

export {
  createConfig2 as createConfig,
  Context,
  WagmiConfig,
  useConfig,
  useInfiniteQuery,
  useMutation2 as useMutation,
  useQuery,
  useQueryClient2,
  usePublicClient,
  useWalletClient,
  useWebSocketPublicClient,
  useChainId,
  useBlockNumber,
  useFeeData,
  useAccount,
  useBalance,
  useConnect,
  useDisconnect,
  useNetwork,
  useSignMessage,
  useSignTypedData,
  useSwitchNetwork,
  useContractEvent,
  useContractInfiniteReads,
  paginatedIndexesConfig,
  useContractRead,
  useContractReads,
  useContractWrite,
  usePrepareContractWrite,
  useToken,
  useEnsAddress,
  useEnsAvatar,
  useEnsName,
  useEnsResolver,
  usePrepareSendTransaction,
  useSendTransaction,
  useTransaction,
  useWaitForTransaction,
  useWatchPendingTransactions
};
/*! Bundled license information:

use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js:
  (**
   * @license React
   * use-sync-external-store-shim/with-selector.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-WRN6L2RZ.js.map
