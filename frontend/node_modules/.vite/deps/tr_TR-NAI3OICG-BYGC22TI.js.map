{"version": 3, "sources": ["../../@rainbow-me/rainbowkit/dist/tr_TR-NAI3OICG.js"], "sourcesContent": ["\"use client\";\n// src/locales/tr_TR.json\nvar tr_TR_default = `{\n  \"connect_wallet\": {\n    \"label\": \"C\\xFCzdan\\u0131 Ba\\u011Fla\",\n    \"wrong_network\": {\n      \"label\": \"Yanl\\u0131\\u015F a\\u011F\"\n    }\n  },\n  \"intro\": {\n    \"title\": \"C\\xFCzdan nedir?\",\n    \"description\": \"Bir c\\xFCzdan, dijital varl\\u0131klar\\u0131 g\\xF6ndermek, almak, saklamak ve g\\xF6r\\xFCnt\\xFClemek i\\xE7in kullan\\u0131l\\u0131r. <PERSON>yn\\u0131 zamanda her web sitesinde yeni hesaplar ve \\u015Fifreler olu\\u015Fturman\\u0131za gerek kalmadan oturum a\\xE7man\\u0131n yeni bir yoludur.\",\n    \"digital_asset\": {\n      \"title\": \"Dijital Varl\\u0131klar\\u0131n\\u0131z \\u0130\\xE7in Bir Ev\",\n      \"description\": \"C\\xFCzdanlar, Ethereum ve NFT'ler gibi dijital varl\\u0131klar\\u0131 g\\xF6<PERSON><PERSON>k, almak, depolamak ve g\\xF6r\\xFCnt\\xFClemek i\\xE7in kullan\\u0131l\\u0131r.\"\n    },\n    \"login\": {\n      \"title\": \"Yeni Bir Giri\\u015F Yolu\",\n      \"description\": \"Her web sitesinde yeni hesap ve parolalar olu\\u015Fturmak yerine, sadece c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flay\\u0131n.\"\n    },\n    \"get\": {\n      \"label\": \"Bir C\\xFCzdan Edinin\"\n    },\n    \"learn_more\": {\n      \"label\": \"Daha fazla bilgi edinin\"\n    }\n  },\n  \"sign_in\": {\n    \"label\": \"Hesab\\u0131n\\u0131z\\u0131 do\\u011Frulay\\u0131n\",\n    \"description\": \"Ba\\u011Flant\\u0131y\\u0131 tamamlamak i\\xE7in, bu hesab\\u0131n sahibi oldu\\u011Funuzu do\\u011Frulamak i\\xE7in c\\xFCzdan\\u0131n\\u0131zdaki bir mesaja imza atmal\\u0131s\\u0131n\\u0131z.\",\n    \"message\": {\n      \"send\": \"Mesaj\\u0131 g\\xF6nder\",\n      \"preparing\": \"Mesaj haz\\u0131rlan\\u0131yor...\",\n      \"cancel\": \"\\u0130ptal\",\n      \"preparing_error\": \"Mesaj\\u0131 haz\\u0131rlarken hata olu\\u015Ftu, l\\xFCtfen tekrar deneyin!\"\n    },\n    \"signature\": {\n      \"waiting\": \"\\u0130mza bekleniyor...\",\n      \"verifying\": \"\\u0130mza do\\u011Frulan\\u0131yor...\",\n      \"signing_error\": \"Mesaj\\u0131 imzalarken hata olu\\u015Ftu, l\\xFCtfen tekrar deneyin!\",\n      \"verifying_error\": \"\\u0130mza do\\u011Frulan\\u0131rken hata olu\\u015Ftu, l\\xFCtfen tekrar deneyin!\",\n      \"oops_error\": \"Hata, bir \\u015Feyler yanl\\u0131\\u015F gitti!\"\n    }\n  },\n  \"connect\": {\n    \"label\": \"Ba\\u011Flan\",\n    \"title\": \"Bir C\\xFCzdan\\u0131 Ba\\u011Fla\",\n    \"new_to_ethereum\": {\n      \"description\": \"Ethereum c\\xFCzdanlar\\u0131na yeni misiniz?\",\n      \"learn_more\": {\n        \"label\": \"Daha fazla bilgi edinin\"\n      }\n    },\n    \"learn_more\": {\n      \"label\": \"Daha fazla bilgi edinin\"\n    },\n    \"recent\": \"Son\",\n    \"status\": {\n      \"opening\": \"%{wallet}a\\xE7\\u0131l\\u0131yor...\",\n      \"connecting\": \"Ba\\u011Flan\\u0131yor\",\n      \"connect_mobile\": \"%{wallet}'da devam edin\",\n      \"not_installed\": \"%{wallet} y\\xFCkl\\xFC de\\u011Fil\",\n      \"not_available\": \"%{wallet} kullan\\u0131labilir de\\u011Fil\",\n      \"confirm\": \"Ba\\u011Flant\\u0131y\\u0131 eklentide onaylay\\u0131n\",\n      \"confirm_mobile\": \"C\\xFCzdan\\u0131nda ba\\u011Flant\\u0131 iste\\u011Fini kabul et\"\n    },\n    \"secondary_action\": {\n      \"get\": {\n        \"description\": \"%{wallet}yok mu?\",\n        \"label\": \"AL\"\n      },\n      \"install\": {\n        \"label\": \"Y\\xDCKLE\"\n      },\n      \"retry\": {\n        \"label\": \"YEN\\u0130DEN DENE\"\n      }\n    },\n    \"walletconnect\": {\n      \"description\": {\n        \"full\": \"Resmi WalletConnect modal\\u0131na m\\u0131 ihtiyac\\u0131n\\u0131z var?\",\n        \"compact\": \"WalletConnect modal\\u0131na m\\u0131 ihtiyac\\u0131n\\u0131z var?\"\n      },\n      \"open\": {\n        \"label\": \"A\\xC7\"\n      }\n    }\n  },\n  \"connect_scan\": {\n    \"title\": \"%{wallet}ile tarama yap\\u0131n\",\n    \"fallback_title\": \"Telefonunuzla tarama yap\\u0131n\"\n  },\n  \"connector_group\": {\n    \"recommended\": \"Tavsiye Edilen\",\n    \"other\": \"Di\\u011Fer\",\n    \"popular\": \"Pop\\xFCler\",\n    \"more\": \"Daha Fazla\",\n    \"others\": \"Di\\u011Ferleri\"\n  },\n  \"get\": {\n    \"title\": \"Bir C\\xFCzdan Edinin\",\n    \"action\": {\n      \"label\": \"AL\"\n    },\n    \"mobile\": {\n      \"description\": \"Mobil C\\xFCzdan\"\n    },\n    \"extension\": {\n      \"description\": \"Taray\\u0131c\\u0131 Eklentisi\"\n    },\n    \"mobile_and_extension\": {\n      \"description\": \"Mobil C\\xFCzdan ve Eklenti\"\n    },\n    \"mobile_and_desktop\": {\n      \"description\": \"Mobil ve Masa\\xFCst\\xFC C\\xFCzdan\"\n    },\n    \"looking_for\": {\n      \"title\": \"Arad\\u0131\\u011F\\u0131n\\u0131z \\u015Fey bu de\\u011Fil mi?\",\n      \"mobile\": {\n        \"description\": \"Ana ekranda ba\\u015Fka bir c\\xFCzdan sa\\u011Flay\\u0131c\\u0131s\\u0131yla ba\\u015Flamak i\\xE7in bir c\\xFCzdan se\\xE7in.\"\n      },\n      \"desktop\": {\n        \"compact_description\": \"Ana ekranda ba\\u015Fka bir c\\xFCzdan sa\\u011Flay\\u0131c\\u0131s\\u0131yla ba\\u015Flamak i\\xE7in bir c\\xFCzdan se\\xE7in.\",\n        \"wide_description\": \"Ba\\u015Fka bir c\\xFCzdan sa\\u011Flay\\u0131c\\u0131s\\u0131yla ba\\u015Flamak i\\xE7in sol tarafta bir c\\xFCzdan se\\xE7in.\"\n      }\n    }\n  },\n  \"get_options\": {\n    \"title\": \"%{wallet}ile ba\\u015Flay\\u0131n\",\n    \"short_title\": \"%{wallet}Edinin\",\n    \"mobile\": {\n      \"title\": \"%{wallet} Mobil \\u0130\\xE7in\",\n      \"description\": \"Mobil c\\xFCzdan\\u0131 kullanarak Ethereum d\\xFCnyas\\u0131n\\u0131 ke\\u015Ffedin.\",\n      \"download\": {\n        \"label\": \"Uygulamay\\u0131 al\\u0131n\"\n      }\n    },\n    \"extension\": {\n      \"title\": \"%{wallet} i\\xE7in %{browser}\",\n      \"description\": \"C\\xFCzdan\\u0131n\\u0131za favori web taray\\u0131c\\u0131n\\u0131zdan do\\u011Frudan eri\\u015Fin.\",\n      \"download\": {\n        \"label\": \"%{browser}'e ekle\"\n      }\n    },\n    \"desktop\": {\n      \"title\": \"%{wallet} i\\xE7in %{platform}\",\n      \"description\": \"G\\xFC\\xE7l\\xFC masa\\xFCst\\xFCn\\xFCzden c\\xFCzdan\\u0131n\\u0131za yerel olarak eri\\u015Fin.\",\n      \"download\": {\n        \"label\": \"%{platform}ekleyin\"\n      }\n    }\n  },\n  \"get_mobile\": {\n    \"title\": \"%{wallet}'i y\\xFCkleyin\",\n    \"description\": \"iOS veya Android'de indirmek i\\xE7in telefonunuzla taray\\u0131n\",\n    \"continue\": {\n      \"label\": \"Devam et\"\n    }\n  },\n  \"get_instructions\": {\n    \"mobile\": {\n      \"connect\": {\n        \"label\": \"Ba\\u011Flan\"\n      },\n      \"learn_more\": {\n        \"label\": \"Daha fazla bilgi edinin\"\n      }\n    },\n    \"extension\": {\n      \"refresh\": {\n        \"label\": \"Yenile\"\n      },\n      \"learn_more\": {\n        \"label\": \"Daha fazla bilgi edinin\"\n      }\n    },\n    \"desktop\": {\n      \"connect\": {\n        \"label\": \"Ba\\u011Flan\"\n      },\n      \"learn_more\": {\n        \"label\": \"Daha fazla bilgi edinin\"\n      }\n    }\n  },\n  \"chains\": {\n    \"title\": \"A\\u011Flar\\u0131 De\\u011Fi\\u015Ftir\",\n    \"wrong_network\": \"Yanl\\u0131\\u015F a\\u011F alg\\u0131land\\u0131, devam etmek i\\xE7in ba\\u011Flant\\u0131y\\u0131 kesin veya de\\u011Fi\\u015Ftirin.\",\n    \"confirm\": \"C\\xFCzdan\\u0131nda Onayla\",\n    \"switching_not_supported\": \"C\\xFCzdan\\u0131n\\u0131z %{appName}. a\\u011Flar\\u0131 de\\u011Fi\\u015Ftirmeyi desteklemiyor. Bunun yerine c\\xFCzdan\\u0131n\\u0131zdan a\\u011Flar\\u0131 de\\u011Fi\\u015Ftirmeyi deneyin.\",\n    \"switching_not_supported_fallback\": \"C\\xFCzdan\\u0131n\\u0131z bu uygulamadan a\\u011Flar\\u0131 de\\u011Fi\\u015Ftirmeyi desteklemiyor. Bunun yerine c\\xFCzdan\\u0131n\\u0131zdaki a\\u011Flar\\u0131 de\\u011Fi\\u015Ftirmeyi deneyin.\",\n    \"disconnect\": \"Ba\\u011Flant\\u0131y\\u0131 Kes\",\n    \"connected\": \"Ba\\u011Fl\\u0131\"\n  },\n  \"profile\": {\n    \"disconnect\": {\n      \"label\": \"Ba\\u011Flant\\u0131y\\u0131 Kes\"\n    },\n    \"copy_address\": {\n      \"label\": \"Adresi Kopyala\",\n      \"copied\": \"Kopyaland\\u0131!\"\n    },\n    \"explorer\": {\n      \"label\": \"Explorer \\xFCzerinde daha fazlas\\u0131n\\u0131 g\\xF6r\\xFCn\"\n    },\n    \"transactions\": {\n      \"description\": \"%{appName} i\\u015Flem burada g\\xF6r\\xFCnecek...\",\n      \"description_fallback\": \"\\u0130\\u015Flemleriniz burada g\\xF6r\\xFCnecek...\",\n      \"recent\": {\n        \"title\": \"Son \\u0130\\u015Flemler\"\n      },\n      \"clear\": {\n        \"label\": \"Hepsini Temizle\"\n      }\n    }\n  },\n  \"wallet_connectors\": {\n    \"argent\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Argent'i ana ekran\\u0131n\\u0131za koyun.\",\n          \"title\": \"Argent uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\"\n        },\n        \"step2\": {\n          \"description\": \"Bir c\\xFCzdan ve kullan\\u0131c\\u0131 ad\\u0131 olu\\u015Fturun veya mevcut bir c\\xFCzdan\\u0131 i\\xE7e aktar\\u0131n.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"Tarad\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi g\\xF6r\\xFCnecektir.\",\n          \"title\": \"QR taray\\u0131c\\u0131 d\\xFC\\u011Fmesine dokunun\"\n        }\n      }\n    },\n    \"bifrost\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Bifrost C\\xFCzdan'\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\",\n          \"title\": \"Bifrost C\\xFCzdan uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\"\n        },\n        \"step2\": {\n          \"description\": \"Kurtarma ifadenizle bir c\\xFCzdan olu\\u015Fturun veya i\\xE7e aktar\\u0131n.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"Tarama i\\u015Flemi sonras\\u0131nda, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi g\\xF6z\\xFCkecektir.\",\n          \"title\": \"Taray\\u0131c\\u0131 d\\xFC\\u011Fmesine dokunun\"\n        }\n      }\n    },\n    \"bitget\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Bitget C\\xFCzdan\\u0131n\\u0131z\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\",\n          \"title\": \"Bitget C\\xFCzdan uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\\u015Fmay\\u0131n.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"Tarama yapt\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi g\\xF6r\\xFCnecektir.\",\n          \"title\": \"Tarama d\\xFC\\u011Fmesine dokunun\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Bitget C\\xFCzdan\\u0131n\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\",\n          \"title\": \"Bitget C\\xFCzdan eklentisini y\\xFCkleyin\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedeklemekten emin olun. Gizli ifadenizi hi\\xE7 kimseyle payla\\u015Fmay\\u0131n.\",\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\"\n        },\n        \"step3\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n ve taray\\u0131c\\u0131y\\u0131 yenileyin ve eklentiyi y\\xFCkleyin.\",\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\"\n        }\n      }\n    },\n    \"bitski\": {\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Bitski'yi g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\",\n          \"title\": \"Bitski eklentisini y\\xFCkleyin\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekledi\\u011Finizden emin olun. Gizli ifadenizi kimseyle payla\\u015Fmay\\u0131n.\",\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\"\n        },\n        \"step3\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\",\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\"\n        }\n      }\n    },\n    \"coin98\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Coin98 C\\xFCzdan\\u0131n\\u0131z\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\",\n          \"title\": \"Coin98 C\\xFCzdan uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\"\n        },\n        \"step2\": {\n          \"description\": \"Telefonunuzdaki yedekleme \\xF6zelli\\u011Fimizi kullanarak c\\xFCzdan\\u0131n\\u0131z\\u0131 kolayca yedekleyebilirsiniz.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"Tarama i\\u015Flemi yapt\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi g\\xF6r\\xFCnecektir.\",\n          \"title\": \"C\\xFCzdanBa\\u011Flant\\u0131s\\u0131 d\\xFC\\u011Fmesine dokunun\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131n sa\\u011F \\xFCst k\\xF6\\u015Fesinde t\\u0131klay\\u0131n ve Coin98 C\\xFCzdan\\u0131n\\u0131z\\u0131 kolay eri\\u015Fim i\\xE7in sabitleyin.\",\n          \"title\": \"Coin98 C\\xFCzdan eklentisini y\\xFCkleyin\"\n        },\n        \"step2\": {\n          \"description\": \"Yeni bir c\\xFCzdan olu\\u015Fturun veya mevcut birini i\\xE7e aktar\\u0131n.\",\n          \"title\": \"Bir c\\xFCzdan olu\\u015Fturun veya i\\xE7e aktar\\u0131n\"\n        },\n        \"step3\": {\n          \"description\": \"Coin98 C\\xFCzdan'\\u0131 kurduktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\",\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\"\n        }\n      }\n    },\n    \"coinbase\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"Coinbase C\\xFCzdan'\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz, b\\xF6ylece daha h\\u0131zl\\u0131 eri\\u015Fim sa\\u011Flan\\u0131r.\",\n          \"title\": \"Coinbase Wallet uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 bulut yedekleme \\xF6zelli\\u011Fini kullanarak kolayca yedekleyebilirsiniz.\",\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\"\n        },\n        \"step3\": {\n          \"description\": \"Tarama yapt\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flaman\\u0131z i\\xE7in bir ba\\u011Flant\\u0131 istemi belirecektir.\",\n          \"title\": \"Tarama d\\xFC\\u011Fmesine dokunun\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Coinbase Wallet'\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\",\n          \"title\": \"Coinbase Wallet uzant\\u0131s\\u0131n\\u0131 y\\xFCkleyin\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekleyin. Gizli ifadenizi asla ba\\u015Fkalar\\u0131yla payla\\u015Fmay\\u0131n.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\",\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\"\n        }\n      }\n    },\n    \"core\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Core'u ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\",\n          \"title\": \"Core uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131n yede\\u011Fini telefonunuzda bulunan yedekleme \\xF6zelli\\u011Fimizi kullanarak kolayca alabilirsiniz.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"Tarama yapt\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak \\xFCzere bir ba\\u011Flant\\u0131 istemi g\\xF6r\\xFCnecektir.\",\n          \"title\": \"WalletConnect d\\xFC\\u011Fmesine dokunun\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Core'u g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\",\n          \"title\": \"Core eklentisini y\\xFCkleyin\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedeklemeye dikkat edin. Gizli ifadenizi asla kimseyle payla\\u015Fmay\\u0131n.\",\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\"\n        },\n        \"step3\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, a\\u015Fa\\u011F\\u0131ya t\\u0131klayarak taray\\u0131c\\u0131y\\u0131 yenileyin ve eklentiyi y\\xFCkleyin.\",\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\"\n        }\n      }\n    },\n    \"fox\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in FoxWallet'\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\",\n          \"title\": \"FoxWallet uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekledi\\u011Finizden emin olun. Gizli ifadenizi asla kimseyle payla\\u015Fmay\\u0131n.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"Tarama yapt\\u0131ktan sonra c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flaman\\u0131z i\\xE7in bir ba\\u011Flant\\u0131 istemi belirecektir.\",\n          \"title\": \"Tarama d\\xFC\\u011Fmesine dokunun\"\n        }\n      }\n    },\n    \"frontier\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Frontier C\\xFCzdan\\u0131n\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\",\n          \"title\": \"Frontier C\\xFCzdan uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekledi\\u011Finizden emin olun. Gizli ifadenizi asla kimseyle payla\\u015Fmay\\u0131n.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"Taramadan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi g\\xF6r\\xFCnecektir.\",\n          \"title\": \"Tarama d\\xFC\\u011Fmesine dokunun\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Frontier C\\xFCzdan\\u0131n\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\",\n          \"title\": \"Frontier C\\xFCzdan eklentisini y\\xFCkleyin\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Gizli ifadenizi hi\\xE7 kimseyle payla\\u015Fmay\\u0131n.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemeye ve eklentiyi y\\xFCklemeye ba\\u015Flamak i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\",\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 Yenileyin\"\n        }\n      }\n    },\n    \"im_token\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"imToken uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in imToken uygulamas\\u0131n\\u0131 ana ekran\\u0131n\\u0131za koyun.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\",\n          \"description\": \"Yeni bir c\\xFCzdan olu\\u015Fturun veya mevcut bir c\\xFCzdan\\u0131 i\\xE7e aktar\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Sa\\u011F \\xFCst k\\xF6\\u015Fede Taray\\u0131c\\u0131 Simgesine dokunun\",\n          \"description\": \"Yeni Ba\\u011Flant\\u0131'y\\u0131 se\\xE7in, ard\\u0131ndan QR kodunu taray\\u0131n ve ba\\u011Flant\\u0131y\\u0131 onaylamak i\\xE7in istemi onaylay\\u0131n.\"\n        }\n      }\n    },\n    \"metamask\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"MetaMask uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in MetaMask'\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekleyin. Gizli kurtarma ifadenizi asla ba\\u015Fkalar\\u0131yla payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Tarama d\\xFC\\u011Fmesine dokunun\",\n          \"description\": \"Taramay\\u0131 yapt\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi belirecektir.\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"MetaMask eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in MetaMask'i g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Gizli ifadenizi hi\\xE7 kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 Yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"okx\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"OKX Wallet uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in OKX Wallet'\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekledi\\u011Finizden emin olun. Gizli c\\xFCmlenizi asla kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Tarama d\\xFC\\u011Fmesine dokunun\",\n          \"description\": \"Tarama yapt\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flama istemi g\\xF6r\\xFCnecektir.\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"OKX C\\xFCzdan eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in OKX C\\xFCzdan'\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekledi\\u011Finizden emin olun. Gizli c\\xFCmlenizi asla kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"omni\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Omni uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Omni'yi ana ekran\\u0131n\\u0131za ekleyin.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun ya da \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"Yeni bir c\\xFCzdan olu\\u015Fturun veya mevcut birini i\\xE7e aktar\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"QR simgesine dokunun ve taray\\u0131n\",\n          \"description\": \"Ana ekran\\u0131n\\u0131zdaki QR simgesine dokunun, kodu taray\\u0131n ve ba\\u011Flanmak i\\xE7in istemi onaylay\\u0131n.\"\n        }\n      }\n    },\n    \"token_pocket\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"TokenPocket uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in TokenPocket'\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekleyin. Gizli ifadenizi asla ba\\u015Fkalar\\u0131yla payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Tarama d\\xFC\\u011Fmesine dokunun\",\n          \"description\": \"Taramay\\u0131 yapt\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi belirecektir.\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"TokenPocket eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in TokenPocket'i g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Gizli c\\xFCmlenizi asla ba\\u015Fkalar\\u0131yla payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemekte ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"trust\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Trust Wallet uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Trust Wallet'\\u0131 ana ekran\\u0131n\\u0131za koyun.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\",\n          \"description\": \"Yeni bir c\\xFCzdan olu\\u015Fturun veya mevcut bir tane i\\xE7e aktar\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Ayarlar'da WalletConnect'e dokunun\",\n          \"description\": \"Yeni Ba\\u011Flant\\u0131'y\\u0131 se\\xE7in, ard\\u0131ndan QR kodunu taray\\u0131n ve ba\\u011Flanmak i\\xE7in istemi onaylay\\u0131n.\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Trust Wallet eklentisini y\\xFCkleyin\",\n          \"description\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131n sa\\u011F \\xFCst k\\xF6\\u015Fesine t\\u0131klay\\u0131n ve kolay eri\\u015Fim i\\xE7in Trust Wallet'i sabitleyin.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir c\\xFCzdan olu\\u015Fturun veya i\\xE7e aktar\\u0131n\",\n          \"description\": \"Yeni bir c\\xFCzdan olu\\u015Fturun veya mevcut bir tane i\\xE7e aktar\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"Trust Wallet'\\u0131 kurduktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"uniswap\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Uniswap uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Uniswap C\\xFCzdan\\u0131n\\u0131z\\u0131 ana ekran\\u0131n\\u0131za ekleyin.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya \\u0130\\xE7e Aktar\",\n          \"description\": \"Yeni bir c\\xFCzdan olu\\u015Fturun veya mevcut birini i\\xE7e aktar\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"QR ikonuna dokunun ve tarama yap\\u0131n\",\n          \"description\": \"Ana ekran\\u0131n\\u0131zdaki QR simgesine dokunun, kodu taray\\u0131n ve ba\\u011Flanmay\\u0131 onaylamak i\\xE7in istemi kabul edin.\"\n        }\n      }\n    },\n    \"zerion\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Zerion uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Zerion'un ana ekran\\u0131n\\u0131za konumland\\u0131rman\\u0131z\\u0131 \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekleyin. Gizli ifadenizi asla ba\\u015Fkalar\\u0131yla payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Tarama d\\xFC\\u011Fmesine bas\\u0131n\",\n          \"description\": \"Taramadan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi belirecektir.\"\n        }\n      },\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Zerion eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Zerion'u g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedeklemeye emin olun. Gizli ifadenizi asla ba\\u015Fkalar\\u0131yla payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"rainbow\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Rainbow uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Rainbow'u ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya \\u0130\\xE7e Aktar\",\n          \"description\": \"Telefonunuzdaki yedekleme \\xF6zelli\\u011Fimizi kullanarak c\\xFCzdan\\u0131n\\u0131z\\u0131 kolayca yedekleyebilirsiniz.\"\n        },\n        \"step3\": {\n          \"title\": \"Tarama d\\xFC\\u011Fmesine dokunun\",\n          \"description\": \"Tarama yapt\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flaman\\u0131z i\\xE7in bir ba\\u011Flant\\u0131 istemi belirecektir.\"\n        }\n      }\n    },\n    \"enkrypt\": {\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim sa\\u011Flamak i\\xE7in Enkrypt C\\xFCzdan'\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\",\n          \"title\": \"Enkrypt C\\xFCzdan eklentisini y\\xFCkleyin\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Gizli ifadenizi hi\\xE7 kimseyle payla\\u015Fmay\\u0131n.\",\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\"\n        },\n        \"step3\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\",\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\"\n        }\n      }\n    },\n    \"frame\": {\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim sa\\u011Flamak i\\xE7in Frame'\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\",\n          \"title\": \"Frame ve e\\u015Flik eden uzant\\u0131y\\u0131 y\\xFCkleyin\"\n        },\n        \"step2\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekledi\\u011Finizden emin olun. Gizli ifadenizi asla ba\\u015Fkas\\u0131yla payla\\u015Fmay\\u0131n.\",\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya \\u0130\\xE7e Aktar\"\n        },\n        \"step3\": {\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve uzant\\u0131y\\u0131 y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\",\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\"\n        }\n      }\n    },\n    \"one_key\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"OneKey Wallet uzant\\u0131s\\u0131n\\u0131 y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in OneKey Wallet'\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekledi\\u011Finizden emin olun. Gizli ifadenizi kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"phantom\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Phantom eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha kolay eri\\u015Fim sa\\u011Flamak i\\xE7in Phantom'u g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntem kullanarak yedekledi\\u011Finizden emin olun. Gizli kurtarma ifadenizi kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"rabby\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Rabby eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Rabby'yi g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Gizli ifadenizi asla ba\\u015Fkalar\\u0131yla payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131daki d\\xFC\\u011Fmeye t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"safeheron\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Core eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Safeheron'u g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Gizli ifadenizi hi\\xE7 kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"taho\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Taho uzant\\u0131s\\u0131n\\u0131 y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Taho'yu g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Gizli ifadenizi hi\\xE7 kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"talisman\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Talisman eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Talisman'\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Ethereum C\\xFCzdan\\u0131 Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Kurtarma ifadenizi hi\\xE7 kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"xdefi\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"XDEFI C\\xFCzdan eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in XDEFI Wallet'\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun veya \\u0130\\xE7e Aktar\\u0131n\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Gizli ifadenizi hi\\xE7 kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ayarlad\\u0131ktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      }\n    },\n    \"zeal\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Zeal eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Zeal'\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n ve taray\\u0131c\\u0131y\\u0131 yenileyin ve eklentiyi y\\xFCkleyin.\"\n        }\n      }\n    },\n    \"safepal\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"SafePal Wallet eklentisini y\\xFCkleyin\",\n          \"description\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131n sa\\u011F \\xFCst k\\xF6\\u015Fesine t\\u0131klay\\u0131n ve kolay eri\\u015Fim i\\xE7in SafePal Wallet'\\u0131 sabitleyin.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir c\\xFCzdan olu\\u015Fturun veya i\\xE7e aktar\\u0131n\",\n          \"description\": \"Yeni bir c\\xFCzdan olu\\u015Fturun veya mevcut birini i\\xE7e aktar\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"SafePal C\\xFCzdan'\\u0131 kurduktan sonra, taray\\u0131c\\u0131y\\u0131 yenilemek ve eklentiyi y\\xFCklemek i\\xE7in a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n.\"\n        }\n      },\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"SafePal C\\xFCzdan uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"SafePal C\\xFCzdan'\\u0131 ana ekran\\u0131n\\u0131za koyun, c\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\",\n          \"description\": \"Yeni bir c\\xFCzdan olu\\u015Fturun veya mevcut birini i\\xE7e aktar\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Ayarlar'da WalletConnect'e dokunun\",\n          \"description\": \"Yeni Ba\\u011Flant\\u0131'y\\u0131 se\\xE7in, ard\\u0131ndan QR kodunu taray\\u0131n ve ba\\u011Flant\\u0131y\\u0131 onaylamak i\\xE7in istemi onaylay\\u0131n.\"\n        }\n      }\n    },\n    \"desig\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Desig eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha kolay eri\\u015Fim sa\\u011Flamak i\\xE7in Desig'i g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Bir C\\xFCzdan Olu\\u015Fturun\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n ve taray\\u0131c\\u0131y\\u0131 yenileyin ve eklentiyi y\\xFCkleyin.\"\n        }\n      }\n    },\n    \"subwallet\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"SubWallet eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in SubWallet'\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedekledi\\u011Finizden emin olun. Kurtarma ifadenizi hi\\xE7 kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n ve taray\\u0131c\\u0131y\\u0131 yenileyin ve eklentiyi y\\xFCkleyin.\"\n        }\n      },\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"SubWallet uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in SubWallet'\\u0131 ana ekran\\u0131n\\u0131za koymenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131 d\\xFC\\u011Fmesine dokunun\",\n          \"description\": \"Tarad\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi g\\xF6r\\xFCnecektir.\"\n        }\n      }\n    },\n    \"clv\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"CLV C\\xFCzdan\\u0131 eklentisini y\\xFCkleyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131za daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in CLV C\\xFCzdan\\u0131n\\u0131 g\\xF6rev \\xE7ubu\\u011Funuza sabitlemenizi \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131n\\u0131z\\u0131 yenileyin\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 kurduktan sonra, a\\u015Fa\\u011F\\u0131ya t\\u0131klay\\u0131n ve taray\\u0131c\\u0131y\\u0131 yenileyin ve eklentiyi y\\xFCkleyin.\"\n        }\n      },\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"CLV C\\xFCzdan uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in CLV C\\xFCzdan\\u0131n\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"C\\xFCzdan Olu\\u015Ftur veya C\\xFCzdan\\u0131 \\u0130\\xE7e Aktar\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 g\\xFCvenli bir y\\xF6ntemle yedeklemeye emin olun. Gizli ifadenizi asla kimseyle payla\\u015Fmay\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Taray\\u0131c\\u0131 d\\xFC\\u011Fmesine dokunun\",\n          \"description\": \"Tarad\\u0131ktan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi g\\xF6r\\xFCnecektir.\"\n        }\n      }\n    },\n    \"okto\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Okto uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"H\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Okto'yu ana ekran\\u0131n\\u0131za ekleyin\"\n        },\n        \"step2\": {\n          \"title\": \"MPC C\\xFCzdan\\u0131 olu\\u015Fturun\",\n          \"description\": \"Bir hesap olu\\u015Fturun ve bir c\\xFCzdan olu\\u015Fturun\"\n        },\n        \"step3\": {\n          \"title\": \"Ayarlar'da WalletConnect'e dokunun\",\n          \"description\": \"Sa\\u011F \\xFCstteki Tarama QR simgesine dokunun ve ba\\u011Flanmak i\\xE7in istemi onaylay\\u0131n.\"\n        }\n      }\n    },\n    \"ledger\": {\n      \"desktop\": {\n        \"step1\": {\n          \"title\": \"Ledger Live uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Ledger Live'\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Ledger'\\u0131n\\u0131z\\u0131 kurun\",\n          \"description\": \"Yeni bir Ledger kurun veya mevcut birine ba\\u011Flan\\u0131n.\"\n        },\n        \"step3\": {\n          \"title\": \"Ba\\u011Flan\",\n          \"description\": \"C\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi belirecektir.\"\n        }\n      },\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Ledger Live uygulamas\\u0131n\\u0131 a\\xE7\\u0131n\",\n          \"description\": \"Daha h\\u0131zl\\u0131 eri\\u015Fim i\\xE7in Ledger Live'\\u0131 ana ekran\\u0131n\\u0131za koyman\\u0131z\\u0131 \\xF6neririz.\"\n        },\n        \"step2\": {\n          \"title\": \"Ledger'\\u0131n\\u0131z\\u0131 kurun\",\n          \"description\": \"Masa\\xFCst\\xFC uygulama ile senkronize olabilir veya Ledger'\\u0131n\\u0131z\\u0131 ba\\u011Flayabilirsiniz.\"\n        },\n        \"step3\": {\n          \"title\": \"Kodu taray\\u0131n\",\n          \"description\": \"WalletConnect'e dokunun ve ard\\u0131ndan Taray\\u0131c\\u0131'ya ge\\xE7in. Taramadan sonra, c\\xFCzdan\\u0131n\\u0131z\\u0131 ba\\u011Flamak i\\xE7in bir ba\\u011Flant\\u0131 istemi belirecektir.\"\n        }\n      }\n    }\n  }\n}\n`;\nexport {\n  tr_TR_default as default\n};\n"], "mappings": ";;;;AAEA,IAAI,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}