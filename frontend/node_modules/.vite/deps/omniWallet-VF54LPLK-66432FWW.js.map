{"version": 3, "sources": ["../../@rainbow-me/rainbowkit/dist/wallets/walletConnectors/omniWallet-VF54LPLK.js"], "sourcesContent": ["\"use client\";\n// src/wallets/walletConnectors/omniWallet/omniWallet.svg\nvar omniWallet_default = \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgdmlld0JveD0iMCAwIDUxMiA1MTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiBmaWxsPSJibGFjayIvPgo8cGF0aCBkPSJNNjcuODI3NSAyODQuNjQyQzY1LjI3NTggMjc3Ljc0NSA2NCAyNzAuNiA2NCAyNjMuMTkzQzY0IDI1NC4yNTUgNjUuMjc1OCAyNDYuMDkgNjguMDgyNiAyMzguNDI4QzkwLjU1MTYgMTc3LjQwNyAxNzAuOTc5IDE1OCAyNjguNzY2IDE1OEMzNjYuNTUyIDE1OCA0MjguMzM4IDE4NS4wNjIgNDQ0LjE3MyAyMjcuOTU5QzQ0Ni43MjQgMjM0Ljg1NSA0NDggMjQyIDQ0OCAyNDkuNDA3QzQ0OCAyNTguMzQ1IDQ0Ni43MjQgMjY2LjUxIDQ0My45MTcgMjc0LjE3M0M0MjEuNDQ4IDMzNS4xOTMgMzQxLjAyMSAzNTQuNiAyNDMuMjM1IDM1NC42QzE0NS40NDggMzU0LjYgODMuNjYyMiAzMjcuNTMxIDY3LjgyNzUgMjg0LjY0MlpNMTkzLjY5NiAyNzMuMTUyQzE5OC44IDI4Ni45MzggMjIwLjUwMyAyOTguNDI4IDI1MC44ODkgMjk4LjQyOEMyODQuMzM4IDI5OC40MjggMzEwLjEyNCAyODguNDY5IDMxOC4wNDEgMjY3LjI3NkMzMjAuNTkzIDI2MC44ODkgMzIwLjg0OCAyNTcuMDYyIDMyMC44NDggMjUyLjcyNEMzMjAuODQ4IDI0OC44OTcgMzIwLjg0OCAyNDUuNTcyIDMxOC4yOTYgMjM5LjQ0OEMzMTMuMTkzIDIyNS42NjIgMjkxLjQ5IDIxNC4xNzMgMjYxLjEwMyAyMTQuMTczQzIyNy42NTUgMjE0LjE3MyAyMDEuODY5IDIyNC4xMzEgMTkzLjk1MSAyNDUuMzI0QzE5MS40IDI1MS43MTEgMTkxLjE0NSAyNTUuNTM4IDE5MS4xNDUgMjU5Ljg3NkMxOTEuMTQ1IDI2My43MDQgMTkxLjE0NSAyNjcuMDI4IDE5My42OTYgMjczLjE1MloiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yMjkuNDc3IDI2My40N0MyMzEuNjUgMjY5LjMzOSAyNDAuODg3IDI3NC4yMzEgMjUzLjgyNyAyNzQuMjMxQzI2OC4wNjYgMjc0LjIzMSAyNzkuMDQ1IDI2OS45OTUgMjgyLjQxMyAyNjAuOTY5QzI4My40OTkgMjU4LjI1IDI4My42MDkgMjU2LjYyNCAyODMuNjA5IDI1NC43NzNDMjgzLjYwOSAyNTMuMTM5IDI4My42MDkgMjUxLjczMiAyODIuNTIyIDI0OS4xMjJDMjgwLjM1IDI0My4yNTQgMjcxLjExMyAyMzguMzYyIDI1OC4xNzMgMjM4LjM2MkMyNDMuOTM0IDIzOC4zNjIgMjMyLjk1NSAyNDIuNTk4IDIyOS41ODcgMjUxLjYyM0MyMjguNTAxIDI1NC4zNDIgMjI4LjM5MSAyNTUuOTY4IDIyOC4zOTEgMjU3LjgyQzIyOC4zOTEgMjU5LjQ1MyAyMjguMzkxIDI2MC44NiAyMjkuNDc3IDI2My40N1oiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=\";\nexport {\n  omniWallet_default as default\n};\n"], "mappings": ";;;;AAEA,IAAI,qBAAqB;", "names": []}