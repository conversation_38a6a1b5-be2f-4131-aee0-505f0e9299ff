{"version": 3, "sources": ["../../@wagmi/core/dist/chunk-MQXBDTVK.js"], "sourcesContent": ["var __accessCheck = (obj, member, msg) => {\n  if (!member.has(obj))\n    throw TypeError(\"Cannot \" + msg);\n};\nvar __privateGet = (obj, member, getter) => {\n  __accessCheck(obj, member, \"read from private field\");\n  return getter ? getter.call(obj) : member.get(obj);\n};\nvar __privateAdd = (obj, member, value) => {\n  if (member.has(obj))\n    throw TypeError(\"Cannot add the same private member more than once\");\n  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\n};\nvar __privateSet = (obj, member, value, setter) => {\n  __accessCheck(obj, member, \"write to private field\");\n  setter ? setter.call(obj, value) : member.set(obj, value);\n  return value;\n};\nvar __privateMethod = (obj, member, method) => {\n  __accessCheck(obj, member, \"access private method\");\n  return method;\n};\n\nexport {\n  __privateGet,\n  __privateAdd,\n  __privateSet,\n  __privateMethod\n};\n"], "mappings": ";AAAA,IAAI,gBAAgB,CAAC,KAAK,QAAQ,QAAQ;AACxC,MAAI,CAAC,OAAO,IAAI,GAAG;AACjB,UAAM,UAAU,YAAY,GAAG;AACnC;AACA,IAAI,eAAe,CAAC,KAAK,QAAQ,WAAW;AAC1C,gBAAc,KAAK,QAAQ,yBAAyB;AACpD,SAAO,SAAS,OAAO,KAAK,GAAG,IAAI,OAAO,IAAI,GAAG;AACnD;AACA,IAAI,eAAe,CAAC,KAAK,QAAQ,UAAU;AACzC,MAAI,OAAO,IAAI,GAAG;AAChB,UAAM,UAAU,mDAAmD;AACrE,oBAAkB,UAAU,OAAO,IAAI,GAAG,IAAI,OAAO,IAAI,KAAK,KAAK;AACrE;AACA,IAAI,eAAe,CAAC,KAAK,QAAQ,OAAO,WAAW;AACjD,gBAAc,KAAK,QAAQ,wBAAwB;AACnD,WAAS,OAAO,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,KAAK,KAAK;AACxD,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,KAAK,QAAQ,WAAW;AAC7C,gBAAc,KAAK,QAAQ,uBAAuB;AAClD,SAAO;AACT;", "names": []}