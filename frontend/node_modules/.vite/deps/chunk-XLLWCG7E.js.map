{"version": 3, "sources": ["../../viem/errors/version.ts", "../../viem/errors/utils.ts", "../../viem/errors/base.ts", "../../viem/errors/encoding.ts", "../../viem/utils/data/isHex.ts", "../../viem/utils/data/size.ts", "../../viem/utils/data/trim.ts", "../../viem/errors/data.ts", "../../viem/utils/data/pad.ts", "../../viem/utils/encoding/toBytes.ts", "../../viem/utils/encoding/fromHex.ts", "../../viem/utils/encoding/toHex.ts", "../../viem/utils/formatters/formatter.ts", "../../viem/utils/formatters/transactionRequest.ts", "../../viem/errors/address.ts", "../../viem/errors/chain.ts", "../../viem/constants/unit.ts", "../../viem/utils/unit/formatUnits.ts", "../../viem/utils/unit/formatGwei.ts", "../../viem/errors/node.ts", "../../viem/utils/address/isAddress.ts", "../../viem/utils/data/concat.ts", "../../abitype/src/version.ts", "../../abitype/src/errors.ts", "../../abitype/src/human-readable/errors/abiItem.ts", "../../abitype/src/human-readable/errors/abiParameter.ts", "../../abitype/src/human-readable/errors/signature.ts", "../../abitype/src/human-readable/errors/struct.ts", "../../abitype/src/human-readable/errors/splitParameters.ts", "../../abitype/src/regex.ts", "../../abitype/src/human-readable/runtime/signatures.ts", "../../abitype/src/human-readable/runtime/cache.ts", "../../abitype/src/human-readable/runtime/utils.ts", "../../abitype/src/human-readable/runtime/structs.ts", "../../abitype/src/human-readable/parseAbi.ts", "../../abitype/src/human-readable/formatAbiParameter.ts", "../../abitype/src/human-readable/formatAbiParameters.ts", "../../abitype/src/human-readable/formatAbiItem.ts", "../../abitype/src/human-readable/parseAbiParameter.ts", "../../abitype/src/human-readable/parseAbiParameters.ts", "../../abitype/src/human-readable/parseAbiItem.ts", "../../viem/utils/abi/formatAbiItem.ts", "../../viem/errors/abi.ts", "../../viem/utils/hash/normalizeSignature.ts", "../../viem/utils/hash/getFunctionSignature.ts", "../../viem/utils/hash/getEventSignature.ts", "../../@noble/hashes/src/_u64.ts", "../../@noble/hashes/src/sha3.ts", "../../viem/utils/hash/keccak256.ts", "../../viem/utils/hash/getEventSelector.ts", "../../viem/utils/data/slice.ts", "../../viem/utils/abi/encodeAbiParameters.ts", "../../viem/utils/hash/getFunctionSelector.ts", "../../viem/utils/abi/getAbiItem.ts", "../../viem/utils/abi/encodeFunctionData.ts", "../../viem/utils/address/getAddress.ts", "../../viem/utils/abi/decodeAbiParameters.ts", "../../viem/constants/solidity.ts", "../../viem/utils/abi/decodeErrorResult.ts", "../../viem/utils/stringify.ts", "../../viem/utils/unit/formatEther.ts", "../../viem/errors/transaction.ts", "../../viem/accounts/utils/parseAccount.ts", "../../viem/utils/abi/formatAbiItemWithArgs.ts", "../../viem/errors/contract.ts", "../../viem/errors/request.ts", "../../viem/errors/rpc.ts", "../../viem/utils/transaction/assertRequest.ts", "../../viem/utils/abi/decodeFunctionResult.ts", "../../viem/constants/abis.ts", "../../viem/utils/chain/getChainContractAddress.ts", "../../viem/constants/contract.ts", "../../viem/utils/errors/getNodeError.ts", "../../viem/utils/errors/getCallError.ts", "../../viem/utils/formatters/extract.ts", "../../viem/utils/promise/createBatchScheduler.ts", "../../viem/actions/public/call.ts"], "sourcesContent": ["export const version = '1.21.3'\n", "import type { Address } from 'abitype'\n\nimport { version } from './version.js'\n\nexport type ErrorType<name extends string = 'Error'> = Error & { name: name }\n\nexport const getContractAddress = (address: Address) => address\nexport const getUrl = (url: string) => url\nexport const getVersion = () => `viem@${version}`\n", "import { getVersion } from './utils.js'\n\ntype BaseErrorParameters = {\n  docsPath?: string\n  docsSlug?: string\n  metaMessages?: string[]\n} & (\n  | {\n      cause?: never\n      details?: string\n    }\n  | {\n      cause: BaseError | Error\n      details?: never\n    }\n)\n\nexport type BaseErrorType = BaseError & { name: 'ViemError' }\nexport class BaseError extends Error {\n  details: string\n  docsPath?: string\n  metaMessages?: string[]\n  shortMessage: string\n\n  override name = 'ViemError'\n  version = getVersion()\n\n  constructor(shortMessage: string, args: BaseErrorParameters = {}) {\n    super()\n\n    const details =\n      args.cause instanceof BaseError\n        ? args.cause.details\n        : args.cause?.message\n          ? args.cause.message\n          : args.details!\n    const docsPath =\n      args.cause instanceof BaseError\n        ? args.cause.docsPath || args.docsPath\n        : args.docsPath\n\n    this.message = [\n      shortMessage || 'An error occurred.',\n      '',\n      ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n      ...(docsPath\n        ? [\n            `Docs: https://viem.sh${docsPath}.html${\n              args.docsSlug ? `#${args.docsSlug}` : ''\n            }`,\n          ]\n        : []),\n      ...(details ? [`Details: ${details}`] : []),\n      `Version: ${this.version}`,\n    ].join('\\n')\n\n    if (args.cause) this.cause = args.cause\n    this.details = details\n    this.docsPath = docsPath\n    this.metaMessages = args.metaMessages\n    this.shortMessage = shortMessage\n  }\n\n  walk(): Error\n  walk(fn: (err: unknown) => boolean): Error | null\n  walk(fn?: any): any {\n    return walk(this, fn)\n  }\n}\n\nfunction walk(err: unknown, fn?: (err: unknown) => boolean): unknown {\n  if (fn?.(err)) return err\n  if (err && typeof err === 'object' && 'cause' in err)\n    return walk(err.cause, fn)\n  return fn ? null : err\n}\n", "import type { ByteArray, Hex } from '../types/misc.js'\n\nimport { BaseError } from './base.js'\n\nexport type DataLengthTooLongErrorType = DataLengthTooLongError & {\n  name: 'DataLengthTooLongError'\n}\n/** @deprecated */\nexport class DataLengthTooLongError extends BaseError {\n  override name = 'DataLengthTooLongError'\n  constructor({ consumed, length }: { consumed: number; length: number }) {\n    super(\n      `Consumed bytes (${consumed}) is shorter than data length (${\n        length - 1\n      }).`,\n    )\n  }\n}\n\nexport type DataLengthTooShortErrorType = DataLengthTooShortError & {\n  name: 'DataLengthTooShortError'\n}\n/** @deprecated */\nexport class DataLengthTooShortError extends BaseError {\n  override name = 'DataLengthTooShortError'\n  constructor({ length, dataLength }: { length: number; dataLength: number }) {\n    super(\n      `Data length (${dataLength - 1}) is shorter than consumed bytes length (${\n        length - 1\n      }).`,\n    )\n  }\n}\n\nexport type IntegerOutOfRangeErrorType = IntegerOutOfRangeError & {\n  name: 'IntegerOutOfRangeError'\n}\nexport class IntegerOutOfRangeError extends BaseError {\n  override name = 'IntegerOutOfRangeError'\n  constructor({\n    max,\n    min,\n    signed,\n    size,\n    value,\n  }: {\n    max?: string\n    min: string\n    signed?: boolean\n    size?: number\n    value: string\n  }) {\n    super(\n      `Number \"${value}\" is not in safe ${\n        size ? `${size * 8}-bit ${signed ? 'signed' : 'unsigned'} ` : ''\n      }integer range ${max ? `(${min} to ${max})` : `(above ${min})`}`,\n    )\n  }\n}\n\nexport type InvalidBytesBooleanErrorType = InvalidBytesBooleanError & {\n  name: 'InvalidBytesBooleanError'\n}\nexport class InvalidBytesBooleanError extends BaseError {\n  override name = 'InvalidBytesBooleanError'\n  constructor(bytes: ByteArray) {\n    super(\n      `Bytes value \"${bytes}\" is not a valid boolean. The bytes array must contain a single byte of either a 0 or 1 value.`,\n    )\n  }\n}\n\nexport type InvalidHexBooleanErrorType = InvalidHexBooleanError & {\n  name: 'InvalidHexBooleanError'\n}\nexport class InvalidHexBooleanError extends BaseError {\n  override name = 'InvalidHexBooleanError'\n  constructor(hex: Hex) {\n    super(\n      `Hex value \"${hex}\" is not a valid boolean. The hex value must be \"0x0\" (false) or \"0x1\" (true).`,\n    )\n  }\n}\n\nexport type InvalidHexValueErrorType = InvalidHexValueError & {\n  name: 'InvalidHexValueError'\n}\nexport class InvalidHexValueError extends BaseError {\n  override name = 'InvalidHexValueError'\n  constructor(value: Hex) {\n    super(\n      `Hex value \"${value}\" is an odd length (${value.length}). It must be an even length.`,\n    )\n  }\n}\n\nexport type OffsetOutOfBoundsErrorType = OffsetOutOfBoundsError & {\n  name: 'OffsetOutOfBoundsError'\n}\n/** @deprecated */\nexport class OffsetOutOfBoundsError extends BaseError {\n  override name = 'OffsetOutOfBoundsError' as const\n  constructor({ nextOffset, offset }: { nextOffset: number; offset: number }) {\n    super(\n      `Next offset (${nextOffset}) is greater than previous offset + consumed bytes (${offset})`,\n    )\n  }\n}\n\nexport type SizeOverflowErrorType = SizeOverflowError & {\n  name: 'SizeOverflowError'\n}\nexport class SizeOverflowError extends BaseError {\n  override name = 'SizeOverflowError' as const\n  constructor({ givenSize, maxSize }: { givenSize: number; maxSize: number }) {\n    super(\n      `Size cannot exceed ${maxSize} bytes. Given size: ${givenSize} bytes.`,\n    )\n  }\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\n\nexport type IsHexErrorType = ErrorType\n\nexport function isHex(\n  value: unknown,\n  { strict = true }: { strict?: boolean } = {},\n): value is Hex {\n  if (!value) return false\n  if (typeof value !== 'string') return false\n  return strict ? /^0x[0-9a-fA-F]*$/.test(value) : value.startsWith('0x')\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\nimport { type IsHexErrorType, isHex } from './isHex.js'\n\nexport type SizeErrorType = IsHexErrorType | ErrorType\n\n/**\n * @description Retrieves the size of the value (in bytes).\n *\n * @param value The value (hex or byte array) to retrieve the size of.\n * @returns The size of the value (in bytes).\n */\nexport function size(value: Hex | ByteArray) {\n  if (isHex(value, { strict: false })) return Math.ceil((value.length - 2) / 2)\n  return value.length\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\ntype TrimOptions = {\n  dir?: 'left' | 'right'\n}\nexport type TrimReturnType<TValue extends ByteArray | Hex> = TValue extends Hex\n  ? Hex\n  : ByteArray\n\nexport type TrimErrorType = ErrorType\n\nexport function trim<TValue extends ByteArray | Hex>(\n  hexOrBytes: TValue,\n  { dir = 'left' }: TrimOptions = {},\n): TrimReturnType<TValue> {\n  let data: any =\n    typeof hexOrBytes === 'string' ? hexOrBytes.replace('0x', '') : hexOrBytes\n\n  let sliceLength = 0\n  for (let i = 0; i < data.length - 1; i++) {\n    if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n      sliceLength++\n    else break\n  }\n  data =\n    dir === 'left'\n      ? data.slice(sliceLength)\n      : data.slice(0, data.length - sliceLength)\n\n  if (typeof hexOrBytes === 'string') {\n    if (data.length === 1 && dir === 'right') data = `${data}0`\n    return `0x${\n      data.length % 2 === 1 ? `0${data}` : data\n    }` as TrimReturnType<TValue>\n  }\n  return data as TrimReturnType<TValue>\n}\n", "import { BaseError } from './base.js'\n\nexport type SliceOffsetOutOfBoundsErrorType = SliceOffsetOutOfBoundsError & {\n  name: 'SliceOffsetOutOfBoundsError'\n}\nexport class SliceOffsetOutOfBoundsError extends BaseError {\n  override name = 'SliceOffsetOutOfBoundsError'\n  constructor({\n    offset,\n    position,\n    size,\n  }: { offset: number; position: 'start' | 'end'; size: number }) {\n    super(\n      `Slice ${\n        position === 'start' ? 'starting' : 'ending'\n      } at offset \"${offset}\" is out-of-bounds (size: ${size}).`,\n    )\n  }\n}\n\nexport type SizeExceedsPaddingSizeErrorType = SizeExceedsPaddingSizeError & {\n  name: 'SizeExceedsPaddingSizeError'\n}\nexport class SizeExceedsPaddingSizeError extends BaseError {\n  override name = 'SizeExceedsPaddingSizeError'\n  constructor({\n    size,\n    targetSize,\n    type,\n  }: {\n    size: number\n    targetSize: number\n    type: 'hex' | 'bytes'\n  }) {\n    super(\n      `${type.charAt(0).toUpperCase()}${type\n        .slice(1)\n        .toLowerCase()} size (${size}) exceeds padding size (${targetSize}).`,\n    )\n  }\n}\n", "import {\n  SizeExceedsPaddingSizeError,\n  type SizeExceedsPaddingSizeErrorType,\n} from '../../errors/data.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\ntype PadOptions = {\n  dir?: 'left' | 'right'\n  size?: number | null\n}\nexport type PadReturnType<TValue extends ByteArray | Hex> = TValue extends Hex\n  ? Hex\n  : ByteArray\n\nexport type PadErrorType = PadHexErrorType | PadBytesErrorType | ErrorType\n\nexport function pad<TValue extends ByteArray | Hex>(\n  hexOrBytes: TValue,\n  { dir, size = 32 }: PadOptions = {},\n): PadReturnType<TValue> {\n  if (typeof hexOrBytes === 'string')\n    return padHex(hexOrBytes, { dir, size }) as PadReturnType<TValue>\n  return padBytes(hexOrBytes, { dir, size }) as PadReturnType<TValue>\n}\n\nexport type PadHexErrorType = SizeExceedsPaddingSizeErrorType | ErrorType\n\nexport function padHex(hex_: Hex, { dir, size = 32 }: PadOptions = {}) {\n  if (size === null) return hex_\n  const hex = hex_.replace('0x', '')\n  if (hex.length > size * 2)\n    throw new SizeExceedsPaddingSizeError({\n      size: Math.ceil(hex.length / 2),\n      targetSize: size,\n      type: 'hex',\n    })\n\n  return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](\n    size * 2,\n    '0',\n  )}` as Hex\n}\n\nexport type PadBytesErrorType = SizeExceedsPaddingSizeErrorType | ErrorType\n\nexport function padBytes(\n  bytes: ByteArray,\n  { dir, size = 32 }: PadOptions = {},\n) {\n  if (size === null) return bytes\n  if (bytes.length > size)\n    throw new SizeExceedsPaddingSizeError({\n      size: bytes.length,\n      targetSize: size,\n      type: 'bytes',\n    })\n  const paddedBytes = new Uint8Array(size)\n  for (let i = 0; i < size; i++) {\n    const padEnd = dir === 'right'\n    paddedBytes[padEnd ? i : size - i - 1] =\n      bytes[padEnd ? i : bytes.length - i - 1]\n  }\n  return paddedBytes\n}\n", "import { BaseError } from '../../errors/base.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type IsHexErrorType, isHex } from '../data/isHex.js'\nimport { type PadErrorType, pad } from '../data/pad.js'\n\nimport { type AssertSizeErrorType, assertSize } from './fromHex.js'\nimport {\n  type NumberToHexErrorType,\n  type NumberToHexOpts,\n  numberToHex,\n} from './toHex.js'\n\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\nexport type ToBytesParameters = {\n  /** Size of the output bytes. */\n  size?: number\n}\n\nexport type ToBytesErrorType =\n  | NumberToBytesErrorType\n  | BoolToBytesErrorType\n  | HexToBytesErrorType\n  | StringToBytesErrorType\n  | IsHexErrorType\n  | ErrorType\n\n/**\n * Encodes a UTF-8 string, hex value, bigint, number or boolean to a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes.html\n * - Example: https://viem.sh/docs/utilities/toBytes.html#usage\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes('Hello world')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes(420)\n * // Uint8Array([1, 164])\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes(420, { size: 4 })\n * // Uint8Array([0, 0, 1, 164])\n */\nexport function toBytes(\n  value: string | bigint | number | boolean | Hex,\n  opts: ToBytesParameters = {},\n): ByteArray {\n  if (typeof value === 'number' || typeof value === 'bigint')\n    return numberToBytes(value, opts)\n  if (typeof value === 'boolean') return boolToBytes(value, opts)\n  if (isHex(value)) return hexToBytes(value, opts)\n  return stringToBytes(value, opts)\n}\n\nexport type BoolToBytesOpts = {\n  /** Size of the output bytes. */\n  size?: number\n}\n\nexport type BoolToBytesErrorType =\n  | AssertSizeErrorType\n  | PadErrorType\n  | ErrorType\n\n/**\n * Encodes a boolean into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes.html#booltobytes\n *\n * @param value Boolean value to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { boolToBytes } from 'viem'\n * const data = boolToBytes(true)\n * // Uint8Array([1])\n *\n * @example\n * import { boolToBytes } from 'viem'\n * const data = boolToBytes(true, { size: 32 })\n * // Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n */\nexport function boolToBytes(value: boolean, opts: BoolToBytesOpts = {}) {\n  const bytes = new Uint8Array(1)\n  bytes[0] = Number(value)\n  if (typeof opts.size === 'number') {\n    assertSize(bytes, { size: opts.size })\n    return pad(bytes, { size: opts.size })\n  }\n  return bytes\n}\n\n// We use very optimized technique to convert hex string to byte array\nconst charCodeMap = {\n  zero: 48,\n  nine: 57,\n  A: 65,\n  F: 70,\n  a: 97,\n  f: 102,\n} as const\n\nfunction charCodeToBase16(char: number) {\n  if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n    return char - charCodeMap.zero\n  if (char >= charCodeMap.A && char <= charCodeMap.F)\n    return char - (charCodeMap.A - 10)\n  if (char >= charCodeMap.a && char <= charCodeMap.f)\n    return char - (charCodeMap.a - 10)\n  return undefined\n}\n\nexport type HexToBytesOpts = {\n  /** Size of the output bytes. */\n  size?: number\n}\n\nexport type HexToBytesErrorType = AssertSizeErrorType | PadErrorType | ErrorType\n\n/**\n * Encodes a hex string into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes.html#hextobytes\n *\n * @param hex Hex string to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { hexToBytes } from 'viem'\n * const data = hexToBytes('0x48656c6c6f20776f726c6421')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n *\n * @example\n * import { hexToBytes } from 'viem'\n * const data = hexToBytes('0x48656c6c6f20776f726c6421', { size: 32 })\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n */\nexport function hexToBytes(hex_: Hex, opts: HexToBytesOpts = {}): ByteArray {\n  let hex = hex_\n  if (opts.size) {\n    assertSize(hex, { size: opts.size })\n    hex = pad(hex, { dir: 'right', size: opts.size })\n  }\n\n  let hexString = hex.slice(2) as string\n  if (hexString.length % 2) hexString = `0${hexString}`\n\n  const length = hexString.length / 2\n  const bytes = new Uint8Array(length)\n  for (let index = 0, j = 0; index < length; index++) {\n    const nibbleLeft = charCodeToBase16(hexString.charCodeAt(j++))\n    const nibbleRight = charCodeToBase16(hexString.charCodeAt(j++))\n    if (nibbleLeft === undefined || nibbleRight === undefined) {\n      throw new BaseError(\n        `Invalid byte sequence (\"${hexString[j - 2]}${\n          hexString[j - 1]\n        }\" in \"${hexString}\").`,\n      )\n    }\n    bytes[index] = nibbleLeft * 16 + nibbleRight\n  }\n  return bytes\n}\n\nexport type NumberToBytesErrorType =\n  | NumberToHexErrorType\n  | HexToBytesErrorType\n  | ErrorType\n\n/**\n * Encodes a number into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes.html#numbertobytes\n *\n * @param value Number to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { numberToBytes } from 'viem'\n * const data = numberToBytes(420)\n * // Uint8Array([1, 164])\n *\n * @example\n * import { numberToBytes } from 'viem'\n * const data = numberToBytes(420, { size: 4 })\n * // Uint8Array([0, 0, 1, 164])\n */\nexport function numberToBytes(value: bigint | number, opts?: NumberToHexOpts) {\n  const hex = numberToHex(value, opts)\n  return hexToBytes(hex)\n}\n\nexport type StringToBytesOpts = {\n  /** Size of the output bytes. */\n  size?: number\n}\n\nexport type StringToBytesErrorType =\n  | AssertSizeErrorType\n  | PadErrorType\n  | ErrorType\n\n/**\n * Encodes a UTF-8 string into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes.html#stringtobytes\n *\n * @param value String to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { stringToBytes } from 'viem'\n * const data = stringToBytes('Hello world!')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n *\n * @example\n * import { stringToBytes } from 'viem'\n * const data = stringToBytes('Hello world!', { size: 32 })\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n */\nexport function stringToBytes(\n  value: string,\n  opts: StringToBytesOpts = {},\n): ByteArray {\n  const bytes = encoder.encode(value)\n  if (typeof opts.size === 'number') {\n    assertSize(bytes, { size: opts.size })\n    return pad(bytes, { dir: 'right', size: opts.size })\n  }\n  return bytes\n}\n", "import {\n  InvalidHexBooleanError,\n  type InvalidHexBooleanErrorType,\n  SizeOverflowError,\n  type SizeOverflowErrorType,\n} from '../../errors/encoding.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type SizeErrorType, size as size_ } from '../data/size.js'\nimport { type TrimErrorType, trim } from '../data/trim.js'\n\nimport { type HexToBytesErrorType, hexToBytes } from './toBytes.js'\n\nexport type AssertSizeErrorType =\n  | SizeOverflowErrorType\n  | SizeErrorType\n  | ErrorType\n\nexport function assertSize(\n  hexOrBytes: Hex | ByteArray,\n  { size }: { size: number },\n): void {\n  if (size_(hexOrBytes) > size)\n    throw new SizeOverflowError({\n      givenSize: size_(hexOrBytes),\n      maxSize: size,\n    })\n}\n\nexport type FromHexParameters<\n  TTo extends 'string' | 'bigint' | 'number' | 'bytes' | 'boolean',\n> =\n  | TTo\n  | {\n      /** Size (in bytes) of the hex value. */\n      size?: number\n      /** Type to convert to. */\n      to: TTo\n    }\n\nexport type FromHexReturnType<TTo> = TTo extends 'string'\n  ? string\n  : TTo extends 'bigint'\n    ? bigint\n    : TTo extends 'number'\n      ? number\n      : TTo extends 'bytes'\n        ? ByteArray\n        : TTo extends 'boolean'\n          ? boolean\n          : never\n\nexport type FromHexErrorType =\n  | HexToNumberErrorType\n  | HexToBigIntErrorType\n  | HexToBoolErrorType\n  | HexToStringErrorType\n  | HexToBytesErrorType\n  | ErrorType\n\n/**\n * Decodes a hex string into a string, number, bigint, boolean, or byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex.html\n * - Example: https://viem.sh/docs/utilities/fromHex.html#usage\n *\n * @param hex Hex string to decode.\n * @param toOrOpts Type to convert to or options.\n * @returns Decoded value.\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x1a4', 'number')\n * // 420\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x48656c6c6f20576f726c6421', 'string')\n * // 'Hello world'\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *   size: 32,\n *   to: 'string'\n * })\n * // 'Hello world'\n */\nexport function fromHex<\n  TTo extends 'string' | 'bigint' | 'number' | 'bytes' | 'boolean',\n>(hex: Hex, toOrOpts: FromHexParameters<TTo>): FromHexReturnType<TTo> {\n  const opts = typeof toOrOpts === 'string' ? { to: toOrOpts } : toOrOpts\n  const to = opts.to\n\n  if (to === 'number') return hexToNumber(hex, opts) as FromHexReturnType<TTo>\n  if (to === 'bigint') return hexToBigInt(hex, opts) as FromHexReturnType<TTo>\n  if (to === 'string') return hexToString(hex, opts) as FromHexReturnType<TTo>\n  if (to === 'boolean') return hexToBool(hex, opts) as FromHexReturnType<TTo>\n  return hexToBytes(hex, opts) as FromHexReturnType<TTo>\n}\n\nexport type HexToBigIntOpts = {\n  /** Whether or not the number of a signed representation. */\n  signed?: boolean\n  /** Size (in bytes) of the hex value. */\n  size?: number\n}\n\nexport type HexToBigIntErrorType = AssertSizeErrorType | ErrorType\n\n/**\n * Decodes a hex value into a bigint.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex.html#hextobigint\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns BigInt value.\n *\n * @example\n * import { hexToBigInt } from 'viem'\n * const data = hexToBigInt('0x1a4', { signed: true })\n * // 420n\n *\n * @example\n * import { hexToBigInt } from 'viem'\n * const data = hexToBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // 420n\n */\nexport function hexToBigInt(hex: Hex, opts: HexToBigIntOpts = {}): bigint {\n  const { signed } = opts\n\n  if (opts.size) assertSize(hex, { size: opts.size })\n\n  const value = BigInt(hex)\n  if (!signed) return value\n\n  const size = (hex.length - 2) / 2\n  const max = (1n << (BigInt(size) * 8n - 1n)) - 1n\n  if (value <= max) return value\n\n  return value - BigInt(`0x${'f'.padStart(size * 2, 'f')}`) - 1n\n}\n\nexport type HexToBoolOpts = {\n  /** Size (in bytes) of the hex value. */\n  size?: number\n}\n\nexport type HexToBoolErrorType =\n  | AssertSizeErrorType\n  | InvalidHexBooleanErrorType\n  | TrimErrorType\n  | ErrorType\n\n/**\n * Decodes a hex value into a boolean.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex.html#hextobool\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns Boolean value.\n *\n * @example\n * import { hexToBool } from 'viem'\n * const data = hexToBool('0x01')\n * // true\n *\n * @example\n * import { hexToBool } from 'viem'\n * const data = hexToBool('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // true\n */\nexport function hexToBool(hex_: Hex, opts: HexToBoolOpts = {}): boolean {\n  let hex = hex_\n  if (opts.size) {\n    assertSize(hex, { size: opts.size })\n    hex = trim(hex)\n  }\n  if (trim(hex) === '0x00') return false\n  if (trim(hex) === '0x01') return true\n  throw new InvalidHexBooleanError(hex)\n}\n\nexport type HexToNumberOpts = HexToBigIntOpts\n\nexport type HexToNumberErrorType = HexToBigIntErrorType | ErrorType\n\n/**\n * Decodes a hex string into a number.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex.html#hextonumber\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns Number value.\n *\n * @example\n * import { hexToNumber } from 'viem'\n * const data = hexToNumber('0x1a4')\n * // 420\n *\n * @example\n * import { hexToNumber } from 'viem'\n * const data = hexToBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // 420\n */\nexport function hexToNumber(hex: Hex, opts: HexToNumberOpts = {}): number {\n  return Number(hexToBigInt(hex, opts))\n}\n\nexport type HexToStringOpts = {\n  /** Size (in bytes) of the hex value. */\n  size?: number\n}\n\nexport type HexToStringErrorType =\n  | AssertSizeErrorType\n  | HexToBytesErrorType\n  | TrimErrorType\n  | ErrorType\n\n/**\n * Decodes a hex value into a UTF-8 string.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex.html#hextostring\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns String value.\n *\n * @example\n * import { hexToString } from 'viem'\n * const data = hexToString('0x48656c6c6f20576f726c6421')\n * // 'Hello world!'\n *\n * @example\n * import { hexToString } from 'viem'\n * const data = hexToString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // 'Hello world'\n */\nexport function hexToString(hex: Hex, opts: HexToStringOpts = {}): string {\n  let bytes = hexToBytes(hex)\n  if (opts.size) {\n    assertSize(bytes, { size: opts.size })\n    bytes = trim(bytes, { dir: 'right' })\n  }\n  return new TextDecoder().decode(bytes)\n}\n", "import {\n  IntegerOutOfRangeError,\n  type IntegerOutOfRangeErrorType,\n} from '../../errors/encoding.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type PadErrorType, pad } from '../data/pad.js'\n\nimport { type AssertSizeErrorType, assertSize } from './fromHex.js'\n\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) =>\n  i.toString(16).padStart(2, '0'),\n)\n\nexport type ToHexParameters = {\n  /** The size (in bytes) of the output hex value. */\n  size?: number\n}\n\nexport type ToHexErrorType =\n  | BoolToHexErrorType\n  | BytesToHexErrorType\n  | NumberToHexErrorType\n  | StringToHexErrorType\n  | ErrorType\n\n/**\n * Encodes a string, number, bigint, or ByteArray into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex.html\n * - Example: https://viem.sh/docs/utilities/toHex.html#usage\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex('Hello world')\n * // '0x48656c6c6f20776f726c6421'\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex(420)\n * // '0x1a4'\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex('Hello world', { size: 32 })\n * // '0x48656c6c6f20776f726c64210000000000000000000000000000000000000000'\n */\nexport function toHex(\n  value: string | number | bigint | boolean | ByteArray,\n  opts: ToHexParameters = {},\n): Hex {\n  if (typeof value === 'number' || typeof value === 'bigint')\n    return numberToHex(value, opts)\n  if (typeof value === 'string') {\n    return stringToHex(value, opts)\n  }\n  if (typeof value === 'boolean') return boolToHex(value, opts)\n  return bytesToHex(value, opts)\n}\n\nexport type BoolToHexOpts = {\n  /** The size (in bytes) of the output hex value. */\n  size?: number\n}\n\nexport type BoolToHexErrorType = AssertSizeErrorType | PadErrorType | ErrorType\n\n/**\n * Encodes a boolean into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex.html#booltohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(true)\n * // '0x1'\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(false)\n * // '0x0'\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(true, { size: 32 })\n * // '0x0000000000000000000000000000000000000000000000000000000000000001'\n */\nexport function boolToHex(value: boolean, opts: BoolToHexOpts = {}): Hex {\n  const hex: Hex = `0x${Number(value)}`\n  if (typeof opts.size === 'number') {\n    assertSize(hex, { size: opts.size })\n    return pad(hex, { size: opts.size })\n  }\n  return hex\n}\n\nexport type BytesToHexOpts = {\n  /** The size (in bytes) of the output hex value. */\n  size?: number\n}\n\nexport type BytesToHexErrorType = AssertSizeErrorType | PadErrorType | ErrorType\n\n/**\n * Encodes a bytes array into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex.html#bytestohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { bytesToHex } from 'viem'\n * const data = bytesToHex(Uint8Array.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * // '0x48656c6c6f20576f726c6421'\n *\n * @example\n * import { bytesToHex } from 'viem'\n * const data = bytesToHex(Uint8Array.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]), { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n */\nexport function bytesToHex(value: ByteArray, opts: BytesToHexOpts = {}): Hex {\n  let string = ''\n  for (let i = 0; i < value.length; i++) {\n    string += hexes[value[i]]\n  }\n  const hex = `0x${string}` as const\n\n  if (typeof opts.size === 'number') {\n    assertSize(hex, { size: opts.size })\n    return pad(hex, { dir: 'right', size: opts.size })\n  }\n  return hex\n}\n\nexport type NumberToHexOpts =\n  | {\n      /** Whether or not the number of a signed representation. */\n      signed?: boolean\n      /** The size (in bytes) of the output hex value. */\n      size: number\n    }\n  | {\n      signed?: never\n      /** The size (in bytes) of the output hex value. */\n      size?: number\n    }\n\nexport type NumberToHexErrorType =\n  | IntegerOutOfRangeErrorType\n  | PadErrorType\n  | ErrorType\n\n/**\n * Encodes a number or bigint into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex.html#numbertohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { numberToHex } from 'viem'\n * const data = numberToHex(420)\n * // '0x1a4'\n *\n * @example\n * import { numberToHex } from 'viem'\n * const data = numberToHex(420, { size: 32 })\n * // '0x00000000000000000000000000000000000000000000000000000000000001a4'\n */\nexport function numberToHex(\n  value_: number | bigint,\n  opts: NumberToHexOpts = {},\n): Hex {\n  const { signed, size } = opts\n\n  const value = BigInt(value_)\n\n  let maxValue\n  if (size) {\n    if (signed) maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n\n    else maxValue = 2n ** (BigInt(size) * 8n) - 1n\n  } else if (typeof value_ === 'number') {\n    maxValue = BigInt(Number.MAX_SAFE_INTEGER)\n  }\n\n  const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0\n\n  if ((maxValue && value > maxValue) || value < minValue) {\n    const suffix = typeof value_ === 'bigint' ? 'n' : ''\n    throw new IntegerOutOfRangeError({\n      max: maxValue ? `${maxValue}${suffix}` : undefined,\n      min: `${minValue}${suffix}`,\n      signed,\n      size,\n      value: `${value_}${suffix}`,\n    })\n  }\n\n  const hex = `0x${(signed && value < 0\n    ? (1n << BigInt(size * 8)) + BigInt(value)\n    : value\n  ).toString(16)}` as Hex\n  if (size) return pad(hex, { size }) as Hex\n  return hex\n}\n\nexport type StringToHexOpts = {\n  /** The size (in bytes) of the output hex value. */\n  size?: number\n}\n\nexport type StringToHexErrorType = BytesToHexErrorType | ErrorType\n\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\n/**\n * Encodes a UTF-8 string into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex.html#stringtohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { stringToHex } from 'viem'\n * const data = stringToHex('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * @example\n * import { stringToHex } from 'viem'\n * const data = stringToHex('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n */\nexport function stringToHex(value_: string, opts: StringToHexOpts = {}): Hex {\n  const value = encoder.encode(value_)\n  return bytesToHex(value, opts)\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Assign, Prettify } from '../../types/utils.js'\n\nexport type DefineFormatterErrorType = ErrorType\n\nexport function defineFormatter<TType extends string, TParameters, TReturnType>(\n  type: TType,\n  format: (_: TParameters) => TReturnType,\n) {\n  return <\n    TOverrideParameters,\n    TOverrideReturnType,\n    TExclude extends (keyof TParameters | keyof TOverrideParameters)[] = [],\n  >({\n    exclude,\n    format: overrides,\n  }: {\n    exclude?: TExclude\n    format: (_: TOverrideParameters) => TOverrideReturnType\n  }) => {\n    return {\n      exclude,\n      format: (args: Assign<TParameters, TOverrideParameters>) => {\n        const formatted = format(args as any)\n        if (exclude) {\n          for (const key of exclude) {\n            delete (formatted as any)[key]\n          }\n        }\n        return {\n          ...formatted,\n          ...overrides(args),\n        } as Prettify<Assign<TReturnType, TOverrideReturnType>> & {\n          [_key in TExclude[number]]: never\n        }\n      },\n      type,\n    }\n  }\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type {\n  Chain,\n  ExtractChainFormatterParameters,\n} from '../../types/chain.js'\nimport type { RpcTransactionRequest } from '../../types/rpc.js'\nimport type { TransactionRequest } from '../../types/transaction.js'\nimport { numberToHex } from '../encoding/toHex.js'\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\n\nexport type FormattedTransactionRequest<\n  TChain extends Chain | undefined = Chain | undefined,\n> = ExtractChainFormatterParameters<\n  TChain,\n  'transactionRequest',\n  TransactionRequest\n>\n\nexport const rpcTransactionType = {\n  legacy: '0x0',\n  eip2930: '0x1',\n  eip1559: '0x2',\n} as const\n\nexport type FormatTransactionRequestErrorType = ErrorType\n\nexport function formatTransactionRequest(\n  transactionRequest: Partial<TransactionRequest>,\n) {\n  return {\n    ...transactionRequest,\n    gas:\n      typeof transactionRequest.gas !== 'undefined'\n        ? numberToHex(transactionRequest.gas)\n        : undefined,\n    gasPrice:\n      typeof transactionRequest.gasPrice !== 'undefined'\n        ? numberToHex(transactionRequest.gasPrice)\n        : undefined,\n    maxFeePerGas:\n      typeof transactionRequest.maxFeePerGas !== 'undefined'\n        ? numberToHex(transactionRequest.maxFeePerGas)\n        : undefined,\n    maxPriorityFeePerGas:\n      typeof transactionRequest.maxPriorityFeePerGas !== 'undefined'\n        ? numberToHex(transactionRequest.maxPriorityFeePerGas)\n        : undefined,\n    nonce:\n      typeof transactionRequest.nonce !== 'undefined'\n        ? numberToHex(transactionRequest.nonce)\n        : undefined,\n    type:\n      typeof transactionRequest.type !== 'undefined'\n        ? rpcTransactionType[transactionRequest.type]\n        : undefined,\n    value:\n      typeof transactionRequest.value !== 'undefined'\n        ? numberToHex(transactionRequest.value)\n        : undefined,\n  } as RpcTransactionRequest\n}\n\nexport type DefineTransactionRequestErrorType =\n  | DefineFormatterErrorType\n  | ErrorType\n\nexport const defineTransactionRequest = /*#__PURE__*/ defineFormatter(\n  'transactionRequest',\n  formatTransactionRequest,\n)\n", "import { BaseError } from './base.js'\n\nexport type InvalidAddressErrorType = InvalidAddressError & {\n  name: 'InvalidAddressError'\n}\nexport class InvalidAddressError extends BaseError {\n  override name = 'InvalidAddressError'\n  constructor({ address }: { address: string }) {\n    super(`Address \"${address}\" is invalid.`)\n  }\n}\n", "import type { Chain } from '../types/chain.js'\n\nimport { BaseError } from './base.js'\n\nexport type ChainDoesNotSupportContractErrorType =\n  ChainDoesNotSupportContract & {\n    name: 'ChainDoesNotSupportContract'\n  }\nexport class ChainDoesNotSupportContract extends BaseError {\n  override name = 'ChainDoesNotSupportContract'\n  constructor({\n    blockNumber,\n    chain,\n    contract,\n  }: {\n    blockNumber?: bigint\n    chain: Chain\n    contract: { name: string; blockCreated?: number }\n  }) {\n    super(\n      `Chain \"${chain.name}\" does not support contract \"${contract.name}\".`,\n      {\n        metaMessages: [\n          'This could be due to any of the following:',\n          ...(blockNumber &&\n          contract.blockCreated &&\n          contract.blockCreated > blockNumber\n            ? [\n                `- The contract \"${contract.name}\" was not deployed until block ${contract.blockCreated} (current block ${blockNumber}).`,\n              ]\n            : [\n                `- The chain does not have the contract \"${contract.name}\" configured.`,\n              ]),\n        ],\n      },\n    )\n  }\n}\n\nexport type ChainMismatchErrorType = ChainMismatchError & {\n  name: 'ChainMismatchError'\n}\nexport class ChainMismatchError extends BaseError {\n  override name = 'ChainMismatchError'\n\n  constructor({\n    chain,\n    currentChainId,\n  }: {\n    chain: Chain\n    currentChainId: number\n  }) {\n    super(\n      `The current chain of the wallet (id: ${currentChainId}) does not match the target chain for the transaction (id: ${chain.id} – ${chain.name}).`,\n      {\n        metaMessages: [\n          `Current Chain ID:  ${currentChainId}`,\n          `Expected Chain ID: ${chain.id} – ${chain.name}`,\n        ],\n      },\n    )\n  }\n}\n\nexport type ChainNotFoundErrorType = ChainNotFoundError & {\n  name: 'ChainNotFoundError'\n}\nexport class ChainNotFoundError extends BaseError {\n  override name = 'ChainNotFoundError'\n\n  constructor() {\n    super(\n      [\n        'No chain was provided to the request.',\n        'Please provide a chain with the `chain` argument on the Action, or by supplying a `chain` to WalletClient.',\n      ].join('\\n'),\n    )\n  }\n}\n\nexport type ClientChainNotConfiguredErrorType =\n  ClientChainNotConfiguredError & {\n    name: 'ClientChainNotConfiguredError'\n  }\nexport class ClientChainNotConfiguredError extends BaseError {\n  override name = 'ClientChainNotConfiguredError'\n\n  constructor() {\n    super('No chain was provided to the Client.')\n  }\n}\n\nexport type InvalidChainIdErrorType = InvalidChainIdError & {\n  name: 'InvalidChainIdError'\n}\nexport class InvalidChainIdError extends BaseError {\n  override name = 'InvalidChainIdError'\n\n  constructor({ chainId }: { chainId: number }) {\n    super(`Chain ID \"${chainId}\" is invalid.`)\n  }\n}\n", "export const etherUnits = {\n  gwei: 9,\n  wei: 18,\n}\nexport const gweiUnits = {\n  ether: -9,\n  wei: 9,\n}\nexport const weiUnits = {\n  ether: -18,\n  gwei: -9,\n}\n", "import type { ErrorType } from '../../errors/utils.js'\n\nexport type FormatUnitsErrorType = ErrorType\n\n/**\n *  Divides a number by a given exponent of base 10 (10exponent), and formats it into a string representation of the number..\n *\n * - Docs: https://viem.sh/docs/utilities/formatUnits.html\n *\n * @example\n * import { formatUnits } from 'viem'\n *\n * formatUnits(420000000000n, 9)\n * // '420'\n */\nexport function formatUnits(value: bigint, decimals: number) {\n  let display = value.toString()\n\n  const negative = display.startsWith('-')\n  if (negative) display = display.slice(1)\n\n  display = display.padStart(decimals, '0')\n\n  let [integer, fraction] = [\n    display.slice(0, display.length - decimals),\n    display.slice(display.length - decimals),\n  ]\n  fraction = fraction.replace(/(0+)$/, '')\n  return `${negative ? '-' : ''}${integer || '0'}${\n    fraction ? `.${fraction}` : ''\n  }`\n}\n", "import { gweiUnits } from '../../constants/unit.js'\n\nimport { type FormatUnitsErrorType, formatUnits } from './formatUnits.js'\n\nexport type FormatGweiErrorType = FormatUnitsErrorType\n\n/**\n * Converts numerical wei to a string representation of gwei.\n *\n * - Docs: https://viem.sh/docs/utilities/formatGwei.html\n *\n * @example\n * import { formatGwei } from 'viem'\n *\n * formatGwei(1000000000n)\n * // '1'\n */\nexport function formatGwei(wei: bigint, unit: 'wei' = 'wei') {\n  return formatUnits(wei, gweiUnits[unit])\n}\n", "import { formatGwei } from '../utils/unit/formatGwei.js'\n\nimport { BaseError } from './base.js'\n\n/**\n * geth:    https://github.com/ethereum/go-ethereum/blob/master/core/error.go\n *          https://github.com/ethereum/go-ethereum/blob/master/core/types/transaction.go#L34-L41\n *\n * erigon:  https://github.com/ledgerwatch/erigon/blob/master/core/error.go\n *          https://github.com/ledgerwatch/erigon/blob/master/core/types/transaction.go#L41-L46\n *\n * anvil:   https://github.com/foundry-rs/foundry/blob/master/anvil/src/eth/error.rs#L108\n */\nexport type ExecutionRevertedErrorType = ExecutionRevertedError & {\n  code: 3\n  name: 'ExecutionRevertedError'\n}\nexport class ExecutionRevertedError extends BaseError {\n  static code = 3\n  static nodeMessage = /execution reverted/\n\n  override name = 'ExecutionRevertedError'\n\n  constructor({\n    cause,\n    message,\n  }: { cause?: BaseError; message?: string } = {}) {\n    const reason = message\n      ?.replace('execution reverted: ', '')\n      ?.replace('execution reverted', '')\n    super(\n      `Execution reverted ${\n        reason ? `with reason: ${reason}` : 'for an unknown reason'\n      }.`,\n      {\n        cause,\n      },\n    )\n  }\n}\n\nexport type FeeCapTooHighErrorType = FeeCapTooHighError & {\n  name: 'FeeCapTooHighError'\n}\nexport class FeeCapTooHighError extends BaseError {\n  static nodeMessage =\n    /max fee per gas higher than 2\\^256-1|fee cap higher than 2\\^256-1/\n  override name = 'FeeCapTooHigh'\n  constructor({\n    cause,\n    maxFeePerGas,\n  }: { cause?: BaseError; maxFeePerGas?: bigint } = {}) {\n    super(\n      `The fee cap (\\`maxFeePerGas\\`${\n        maxFeePerGas ? ` = ${formatGwei(maxFeePerGas)} gwei` : ''\n      }) cannot be higher than the maximum allowed value (2^256-1).`,\n      {\n        cause,\n      },\n    )\n  }\n}\n\nexport type FeeCapTooLowErrorType = FeeCapTooLowError & {\n  name: 'FeeCapTooLowError'\n}\nexport class FeeCapTooLowError extends BaseError {\n  static nodeMessage =\n    /max fee per gas less than block base fee|fee cap less than block base fee|transaction is outdated/\n  override name = 'FeeCapTooLow'\n  constructor({\n    cause,\n    maxFeePerGas,\n  }: { cause?: BaseError; maxFeePerGas?: bigint } = {}) {\n    super(\n      `The fee cap (\\`maxFeePerGas\\`${\n        maxFeePerGas ? ` = ${formatGwei(maxFeePerGas)}` : ''\n      } gwei) cannot be lower than the block base fee.`,\n      {\n        cause,\n      },\n    )\n  }\n}\n\nexport type NonceTooHighErrorType = NonceTooHighError & {\n  name: 'NonceTooHighError'\n}\nexport class NonceTooHighError extends BaseError {\n  static nodeMessage = /nonce too high/\n  override name = 'NonceTooHighError'\n  constructor({ cause, nonce }: { cause?: BaseError; nonce?: number } = {}) {\n    super(\n      `Nonce provided for the transaction ${\n        nonce ? `(${nonce}) ` : ''\n      }is higher than the next one expected.`,\n      { cause },\n    )\n  }\n}\n\nexport type NonceTooLowErrorType = NonceTooLowError & {\n  name: 'NonceTooLowError'\n}\nexport class NonceTooLowError extends BaseError {\n  static nodeMessage =\n    /nonce too low|transaction already imported|already known/\n  override name = 'NonceTooLowError'\n  constructor({ cause, nonce }: { cause?: BaseError; nonce?: number } = {}) {\n    super(\n      [\n        `Nonce provided for the transaction ${\n          nonce ? `(${nonce}) ` : ''\n        }is lower than the current nonce of the account.`,\n        'Try increasing the nonce or find the latest nonce with `getTransactionCount`.',\n      ].join('\\n'),\n      { cause },\n    )\n  }\n}\n\nexport type NonceMaxValueErrorType = NonceMaxValueError & {\n  name: 'NonceMaxValueError'\n}\nexport class NonceMaxValueError extends BaseError {\n  static nodeMessage = /nonce has max value/\n  override name = 'NonceMaxValueError'\n  constructor({ cause, nonce }: { cause?: BaseError; nonce?: number } = {}) {\n    super(\n      `Nonce provided for the transaction ${\n        nonce ? `(${nonce}) ` : ''\n      }exceeds the maximum allowed nonce.`,\n      { cause },\n    )\n  }\n}\n\nexport type InsufficientFundsErrorType = InsufficientFundsError & {\n  name: 'InsufficientFundsError'\n}\nexport class InsufficientFundsError extends BaseError {\n  static nodeMessage = /insufficient funds/\n  override name = 'InsufficientFundsError'\n  constructor({ cause }: { cause?: BaseError } = {}) {\n    super(\n      [\n        'The total cost (gas * gas fee + value) of executing this transaction exceeds the balance of the account.',\n      ].join('\\n'),\n      {\n        cause,\n        metaMessages: [\n          'This error could arise when the account does not have enough funds to:',\n          ' - pay for the total gas fee,',\n          ' - pay for the value to send.',\n          ' ',\n          'The cost of the transaction is calculated as `gas * gas fee + value`, where:',\n          ' - `gas` is the amount of gas needed for transaction to execute,',\n          ' - `gas fee` is the gas fee,',\n          ' - `value` is the amount of ether to send to the recipient.',\n        ],\n      },\n    )\n  }\n}\n\nexport type IntrinsicGasTooHighErrorType = IntrinsicGasTooHighError & {\n  name: 'IntrinsicGasTooHighError'\n}\nexport class IntrinsicGasTooHighError extends BaseError {\n  static nodeMessage = /intrinsic gas too high|gas limit reached/\n  override name = 'IntrinsicGasTooHighError'\n  constructor({ cause, gas }: { cause?: BaseError; gas?: bigint } = {}) {\n    super(\n      `The amount of gas ${\n        gas ? `(${gas}) ` : ''\n      }provided for the transaction exceeds the limit allowed for the block.`,\n      {\n        cause,\n      },\n    )\n  }\n}\n\nexport type IntrinsicGasTooLowErrorType = IntrinsicGasTooLowError & {\n  name: 'IntrinsicGasTooLowError'\n}\nexport class IntrinsicGasTooLowError extends BaseError {\n  static nodeMessage = /intrinsic gas too low/\n  override name = 'IntrinsicGasTooLowError'\n  constructor({ cause, gas }: { cause?: BaseError; gas?: bigint } = {}) {\n    super(\n      `The amount of gas ${\n        gas ? `(${gas}) ` : ''\n      }provided for the transaction is too low.`,\n      {\n        cause,\n      },\n    )\n  }\n}\n\nexport type TransactionTypeNotSupportedErrorType =\n  TransactionTypeNotSupportedError & {\n    name: 'TransactionTypeNotSupportedError'\n  }\nexport class TransactionTypeNotSupportedError extends BaseError {\n  static nodeMessage = /transaction type not valid/\n  override name = 'TransactionTypeNotSupportedError'\n  constructor({ cause }: { cause?: BaseError }) {\n    super('The transaction type is not supported for this chain.', {\n      cause,\n    })\n  }\n}\n\nexport type TipAboveFeeCapErrorType = TipAboveFeeCapError & {\n  name: 'TipAboveFeeCapError'\n}\nexport class TipAboveFeeCapError extends BaseError {\n  static nodeMessage =\n    /max priority fee per gas higher than max fee per gas|tip higher than fee cap/\n  override name = 'TipAboveFeeCapError'\n  constructor({\n    cause,\n    maxPriorityFeePerGas,\n    maxFeePerGas,\n  }: {\n    cause?: BaseError\n    maxPriorityFeePerGas?: bigint\n    maxFeePerGas?: bigint\n  } = {}) {\n    super(\n      [\n        `The provided tip (\\`maxPriorityFeePerGas\\`${\n          maxPriorityFeePerGas\n            ? ` = ${formatGwei(maxPriorityFeePerGas)} gwei`\n            : ''\n        }) cannot be higher than the fee cap (\\`maxFeePerGas\\`${\n          maxFeePerGas ? ` = ${formatGwei(maxFeePerGas)} gwei` : ''\n        }).`,\n      ].join('\\n'),\n      {\n        cause,\n      },\n    )\n  }\n}\n\nexport type UnknownNodeErrorType = UnknownNodeError & {\n  name: 'UnknownNodeError'\n}\nexport class UnknownNodeError extends BaseError {\n  override name = 'UnknownNodeError'\n\n  constructor({ cause }: { cause?: BaseError }) {\n    super(`An error occurred while executing: ${cause?.shortMessage}`, {\n      cause,\n    })\n  }\n}\n", "import type { Address } from 'abitype'\nimport type { ErrorType } from '../../errors/utils.js'\n\nconst addressRegex = /^0x[a-fA-F0-9]{40}$/\n\nexport type IsAddressErrorType = ErrorType\n\nexport function isAddress(address: string): address is Address {\n  return addressRegex.test(address)\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\nexport type ConcatReturnType<TValue extends Hex | ByteArray> =\n  TValue extends Hex ? Hex : ByteArray\n\nexport type ConcatErrorType =\n  | ConcatBytesErrorType\n  | ConcatHexErrorType\n  | ErrorType\n\nexport function concat<TValue extends Hex | ByteArray>(\n  values: readonly TValue[],\n): ConcatReturnType<TValue> {\n  if (typeof values[0] === 'string')\n    return concatHex(values as readonly Hex[]) as ConcatReturnType<TValue>\n  return concatBytes(values as readonly ByteArray[]) as ConcatReturnType<TValue>\n}\n\nexport type ConcatBytesErrorType = ErrorType\n\nexport function concatBytes(values: readonly ByteArray[]): ByteArray {\n  let length = 0\n  for (const arr of values) {\n    length += arr.length\n  }\n  const result = new Uint8Array(length)\n  let offset = 0\n  for (const arr of values) {\n    result.set(arr, offset)\n    offset += arr.length\n  }\n  return result\n}\n\nexport type ConcatHexErrorType = ErrorType\n\nexport function concatHex(values: readonly Hex[]): Hex {\n  return `0x${(values as Hex[]).reduce(\n    (acc, x) => acc + x.replace('0x', ''),\n    '',\n  )}`\n}\n", "export const version = '0.9.8'\n", "import type { <PERSON><PERSON><PERSON>, <PERSON> } from './types.js'\nimport { version } from './version.js'\n\ntype BaseErrorArgs = Pretty<\n  {\n    docsPath?: string | undefined\n    metaMessages?: string[] | undefined\n  } & OneOf<{ details?: string | undefined } | { cause?: BaseError | Error }>\n>\n\nexport class BaseError extends Error {\n  details: string\n  docsPath?: string | undefined\n  metaMessages?: string[] | undefined\n  shortMessage: string\n\n  override name = 'AbiTypeError'\n\n  constructor(shortMessage: string, args: BaseErrorArgs = {}) {\n    const details =\n      args.cause instanceof BaseError\n        ? args.cause.details\n        : args.cause?.message\n        ? args.cause.message\n        : args.details!\n    const docsPath =\n      args.cause instanceof BaseError\n        ? args.cause.docsPath || args.docsPath\n        : args.docsPath\n    const message = [\n      shortMessage || 'An error occurred.',\n      '',\n      ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n      ...(docsPath ? [`Docs: https://abitype.dev${docsPath}`] : []),\n      ...(details ? [`Details: ${details}`] : []),\n      `Version: abitype@${version}`,\n    ].join('\\n')\n\n    super(message)\n\n    if (args.cause) this.cause = args.cause\n    this.details = details\n    this.docsPath = docsPath\n    this.metaMessages = args.metaMessages\n    this.shortMessage = shortMessage\n  }\n}\n", "import { BaseError } from '../../errors.js'\n\nexport class InvalidAbiItemError extends BaseError {\n  override name = 'InvalidAbiItemError'\n\n  constructor({ signature }: { signature: string | object }) {\n    super('Failed to parse ABI item.', {\n      details: `parseAbiItem(${JSON.stringify(signature, null, 2)})`,\n      docsPath: '/api/human.html#parseabiitem-1',\n    })\n  }\n}\n\nexport class UnknownTypeError extends BaseError {\n  override name = 'UnknownTypeError'\n\n  constructor({ type }: { type: string }) {\n    super('Unknown type.', {\n      metaMessages: [\n        `Type \"${type}\" is not a valid ABI type. Perhaps you forgot to include a struct signature?`,\n      ],\n    })\n  }\n}\n\nexport class UnknownSolidityTypeError extends BaseError {\n  override name = 'UnknownSolidityTypeError'\n\n  constructor({ type }: { type: string }) {\n    super('Unknown type.', {\n      metaMessages: [`Type \"${type}\" is not a valid ABI type.`],\n    })\n  }\n}\n", "import type { AbiItemType, AbiParameter } from '../../abi.js'\nimport { BaseError } from '../../errors.js'\nimport type { Modifier } from '../types/signatures.js'\n\nexport class InvalidAbiParameterError extends BaseError {\n  override name = 'InvalidAbiParameterError'\n\n  constructor({ param }: { param: string | object }) {\n    super('Failed to parse ABI parameter.', {\n      details: `parseAbiParameter(${JSON.stringify(param, null, 2)})`,\n      docsPath: '/api/human.html#parseabiparameter-1',\n    })\n  }\n}\n\nexport class InvalidAbiParametersError extends BaseError {\n  override name = 'InvalidAbiParametersError'\n\n  constructor({ params }: { params: string | object }) {\n    super('Failed to parse ABI parameters.', {\n      details: `parseAbiParameters(${JSON.stringify(params, null, 2)})`,\n      docsPath: '/api/human.html#parseabiparameters-1',\n    })\n  }\n}\n\nexport class InvalidParameterError extends BaseError {\n  override name = 'InvalidParameterError'\n\n  constructor({ param }: { param: string }) {\n    super('Invalid ABI parameter.', {\n      details: param,\n    })\n  }\n}\n\nexport class SolidityProtectedKeywordError extends BaseError {\n  override name = 'SolidityProtectedKeywordError'\n\n  constructor({ param, name }: { param: string; name: string }) {\n    super('Invalid ABI parameter.', {\n      details: param,\n      metaMessages: [\n        `\"${name}\" is a protected Solidity keyword. More info: https://docs.soliditylang.org/en/latest/cheatsheet.html`,\n      ],\n    })\n  }\n}\n\nexport class InvalidModifierError extends BaseError {\n  override name = 'InvalidModifierError'\n\n  constructor({\n    param,\n    type,\n    modifier,\n  }: {\n    param: string\n    type?: AbiItemType | 'struct' | undefined\n    modifier: Modifier\n  }) {\n    super('Invalid ABI parameter.', {\n      details: param,\n      metaMessages: [\n        `Modifier \"${modifier}\" not allowed${\n          type ? ` in \"${type}\" type` : ''\n        }.`,\n      ],\n    })\n  }\n}\n\nexport class InvalidFunctionModifierError extends BaseError {\n  override name = 'InvalidFunctionModifierError'\n\n  constructor({\n    param,\n    type,\n    modifier,\n  }: {\n    param: string\n    type?: AbiItemType | 'struct' | undefined\n    modifier: Modifier\n  }) {\n    super('Invalid ABI parameter.', {\n      details: param,\n      metaMessages: [\n        `Modifier \"${modifier}\" not allowed${\n          type ? ` in \"${type}\" type` : ''\n        }.`,\n        `Data location can only be specified for array, struct, or mapping types, but \"${modifier}\" was given.`,\n      ],\n    })\n  }\n}\n\nexport class InvalidAbiTypeParameterError extends BaseError {\n  override name = 'InvalidAbiTypeParameterError'\n\n  constructor({\n    abiParameter,\n  }: {\n    abiParameter: AbiParameter & { indexed?: boolean | undefined }\n  }) {\n    super('Invalid ABI parameter.', {\n      details: JSON.stringify(abiParameter, null, 2),\n      metaMessages: ['ABI parameter type is invalid.'],\n    })\n  }\n}\n", "import type { AbiItemType } from '../../abi.js'\nimport { BaseError } from '../../errors.js'\n\nexport class InvalidSignatureError extends BaseError {\n  override name = 'InvalidSignatureError'\n\n  constructor({\n    signature,\n    type,\n  }: {\n    signature: string\n    type: AbiItemType | 'struct'\n  }) {\n    super(`Invalid ${type} signature.`, {\n      details: signature,\n    })\n  }\n}\n\nexport class UnknownSignatureError extends BaseError {\n  override name = 'UnknownSignatureError'\n\n  constructor({ signature }: { signature: string }) {\n    super('Unknown signature.', {\n      details: signature,\n    })\n  }\n}\n\nexport class InvalidStructSignatureError extends BaseError {\n  override name = 'InvalidStructSignatureError'\n\n  constructor({ signature }: { signature: string }) {\n    super('Invalid struct signature.', {\n      details: signature,\n      metaMessages: ['No properties exist.'],\n    })\n  }\n}\n", "import { BaseError } from '../../errors.js'\n\nexport class CircularReferenceError extends BaseError {\n  override name = 'CircularReferenceError'\n\n  constructor({ type }: { type: string }) {\n    super('Circular reference detected.', {\n      metaMessages: [`Struct \"${type}\" is a circular reference.`],\n    })\n  }\n}\n", "import { BaseError } from '../../errors.js'\n\nexport class InvalidParenthesisError extends BaseError {\n  override name = 'InvalidParenthesisError'\n\n  constructor({ current, depth }: { current: string; depth: number }) {\n    super('Unbalanced parentheses.', {\n      metaMessages: [\n        `\"${current.trim()}\" has too many ${\n          depth > 0 ? 'opening' : 'closing'\n        } parentheses.`,\n      ],\n      details: `Depth \"${depth}\"`,\n    })\n  }\n}\n", "// TODO: This looks cool. Need to check the performance of `new RegExp` versus defined inline though.\n// https://twitter.com/GabrielVergnaud/status/1622906834343366657\nexport function execTyped<T>(regex: RegExp, string: string) {\n  const match = regex.exec(string)\n  return match?.groups as T | undefined\n}\n\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nexport const bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/\n\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nexport const integerRegex =\n  /^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/\n\nexport const isTupleRegex = /^\\(.+?\\).*?$/\n", "import type { AbiStateMutability } from '../../abi.js'\nimport { execTyped } from '../../regex.js'\nimport type {\n  EventModifier,\n  FunctionModifier,\n  Modifier,\n} from '../types/signatures.js'\n\n// https://regexr.com/7gmok\nconst errorSignatureRegex =\n  /^error (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/\nexport function isErrorSignature(signature: string) {\n  return errorSignatureRegex.test(signature)\n}\nexport function execErrorSignature(signature: string) {\n  return execTyped<{ name: string; parameters: string }>(\n    errorSignatureRegex,\n    signature,\n  )\n}\n\n// https://regexr.com/7gmoq\nconst eventSignatureRegex =\n  /^event (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/\nexport function isEventSignature(signature: string) {\n  return eventSignatureRegex.test(signature)\n}\nexport function execEventSignature(signature: string) {\n  return execTyped<{ name: string; parameters: string }>(\n    eventSignatureRegex,\n    signature,\n  )\n}\n\n// https://regexr.com/7gmot\nconst functionSignatureRegex =\n  /^function (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)(?: (?<scope>external|public{1}))?(?: (?<stateMutability>pure|view|nonpayable|payable{1}))?(?: returns\\s?\\((?<returns>.*?)\\))?$/\nexport function isFunctionSignature(signature: string) {\n  return functionSignatureRegex.test(signature)\n}\nexport function execFunctionSignature(signature: string) {\n  return execTyped<{\n    name: string\n    parameters: string\n    stateMutability?: AbiStateMutability\n    returns?: string\n  }>(functionSignatureRegex, signature)\n}\n\n// https://regexr.com/7gmp3\nconst structSignatureRegex =\n  /^struct (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*) \\{(?<properties>.*?)\\}$/\nexport function isStructSignature(signature: string) {\n  return structSignatureRegex.test(signature)\n}\nexport function execStructSignature(signature: string) {\n  return execTyped<{ name: string; properties: string }>(\n    structSignatureRegex,\n    signature,\n  )\n}\n\n// https://regexr.com/78u01\nconst constructorSignatureRegex =\n  /^constructor\\((?<parameters>.*?)\\)(?:\\s(?<stateMutability>payable{1}))?$/\nexport function isConstructorSignature(signature: string) {\n  return constructorSignatureRegex.test(signature)\n}\nexport function execConstructorSignature(signature: string) {\n  return execTyped<{\n    parameters: string\n    stateMutability?: Extract<AbiStateMutability, 'payable'>\n  }>(constructorSignatureRegex, signature)\n}\n\n// https://regexr.com/78u18\nconst fallbackSignatureRegex = /^fallback\\(\\)$/\nexport function isFallbackSignature(signature: string) {\n  return fallbackSignatureRegex.test(signature)\n}\n\n// https://regexr.com/78u1k\nconst receiveSignatureRegex = /^receive\\(\\) external payable$/\nexport function isReceiveSignature(signature: string) {\n  return receiveSignatureRegex.test(signature)\n}\n\nexport const modifiers = new Set<Modifier>([\n  'memory',\n  'indexed',\n  'storage',\n  'calldata',\n])\nexport const eventModifiers = new Set<EventModifier>(['indexed'])\nexport const functionModifiers = new Set<FunctionModifier>([\n  'calldata',\n  'memory',\n  'storage',\n])\n", "import type { AbiItemType, AbiParameter } from '../../abi.js'\n\n/**\n * Gets {@link parameterCache} cache key namespaced by {@link type}. This prevents parameters from being accessible to types that don't allow them (e.g. `string indexed foo` not allowed outside of `type: 'event'`).\n * @param param ABI parameter string\n * @param type ABI parameter type\n * @returns Cache key for {@link parameterCache}\n */\nexport function getParameterCacheKey(\n  param: string,\n  type?: AbiItemType | 'struct',\n) {\n  if (type) return `${type}:${param}`\n  return param\n}\n\n/**\n * Basic cache seeded with common ABI parameter strings.\n *\n * **Note: When seeding more parameters, make sure you benchmark performance. The current number is the ideal balance between performance and having an already existing cache.**\n */\nexport const parameterCache = new Map<\n  string,\n  AbiParameter & { indexed?: boolean }\n>([\n  // Unnamed\n  ['address', { type: 'address' }],\n  ['bool', { type: 'bool' }],\n  ['bytes', { type: 'bytes' }],\n  ['bytes32', { type: 'bytes32' }],\n  ['int', { type: 'int256' }],\n  ['int256', { type: 'int256' }],\n  ['string', { type: 'string' }],\n  ['uint', { type: 'uint256' }],\n  ['uint8', { type: 'uint8' }],\n  ['uint16', { type: 'uint16' }],\n  ['uint24', { type: 'uint24' }],\n  ['uint32', { type: 'uint32' }],\n  ['uint64', { type: 'uint64' }],\n  ['uint96', { type: 'uint96' }],\n  ['uint112', { type: 'uint112' }],\n  ['uint160', { type: 'uint160' }],\n  ['uint192', { type: 'uint192' }],\n  ['uint256', { type: 'uint256' }],\n\n  // Named\n  ['address owner', { type: 'address', name: 'owner' }],\n  ['address to', { type: 'address', name: 'to' }],\n  ['bool approved', { type: 'bool', name: 'approved' }],\n  ['bytes _data', { type: 'bytes', name: '_data' }],\n  ['bytes data', { type: 'bytes', name: 'data' }],\n  ['bytes signature', { type: 'bytes', name: 'signature' }],\n  ['bytes32 hash', { type: 'bytes32', name: 'hash' }],\n  ['bytes32 r', { type: 'bytes32', name: 'r' }],\n  ['bytes32 root', { type: 'bytes32', name: 'root' }],\n  ['bytes32 s', { type: 'bytes32', name: 's' }],\n  ['string name', { type: 'string', name: 'name' }],\n  ['string symbol', { type: 'string', name: 'symbol' }],\n  ['string tokenURI', { type: 'string', name: 'tokenURI' }],\n  ['uint tokenId', { type: 'uint256', name: 'tokenId' }],\n  ['uint8 v', { type: 'uint8', name: 'v' }],\n  ['uint256 balance', { type: 'uint256', name: 'balance' }],\n  ['uint256 tokenId', { type: 'uint256', name: 'tokenId' }],\n  ['uint256 value', { type: 'uint256', name: 'value' }],\n\n  // Indexed\n  [\n    'event:address indexed from',\n    { type: 'address', name: 'from', indexed: true },\n  ],\n  ['event:address indexed to', { type: 'address', name: 'to', indexed: true }],\n  [\n    'event:uint indexed tokenId',\n    { type: 'uint256', name: 'tokenId', indexed: true },\n  ],\n  [\n    'event:uint256 indexed tokenId',\n    { type: 'uint256', name: 'tokenId', indexed: true },\n  ],\n])\n", "import type {\n  AbiItemType,\n  AbiType,\n  SolidityArray,\n  SolidityBytes,\n  SolidityString,\n  SolidityTuple,\n} from '../../abi.js'\nimport {\n  bytesRegex,\n  execTyped,\n  integerRegex,\n  isTupleRegex,\n} from '../../regex.js'\nimport { UnknownSolidityTypeError } from '../errors/abiItem.js'\nimport {\n  InvalidFunctionModifierError,\n  InvalidModifierError,\n  InvalidParameterError,\n  SolidityProtectedKeywordError,\n} from '../errors/abiParameter.js'\nimport {\n  InvalidSignatureError,\n  UnknownSignatureError,\n} from '../errors/signature.js'\nimport { InvalidParenthesisError } from '../errors/splitParameters.js'\nimport type { FunctionModifier, Modifier } from '../types/signatures.js'\nimport type { StructLookup } from '../types/structs.js'\nimport { getParameterCacheKey, parameterCache } from './cache.js'\nimport {\n  eventModifiers,\n  execConstructorSignature,\n  execErrorSignature,\n  execEventSignature,\n  execFunctionSignature,\n  functionModifiers,\n  isConstructorSignature,\n  isErrorSignature,\n  isEventSignature,\n  isFallbackSignature,\n  isFunctionSignature,\n  isReceiveSignature,\n} from './signatures.js'\n\nexport function parseSignature(signature: string, structs: StructLookup = {}) {\n  if (isFunctionSignature(signature)) {\n    const match = execFunctionSignature(signature)\n    if (!match) throw new InvalidSignatureError({ signature, type: 'function' })\n\n    const inputParams = splitParameters(match.parameters)\n    const inputs = []\n    const inputLength = inputParams.length\n    for (let i = 0; i < inputLength; i++) {\n      inputs.push(\n        parseAbiParameter(inputParams[i]!, {\n          modifiers: functionModifiers,\n          structs,\n          type: 'function',\n        }),\n      )\n    }\n\n    const outputs = []\n    if (match.returns) {\n      const outputParams = splitParameters(match.returns)\n      const outputLength = outputParams.length\n      for (let i = 0; i < outputLength; i++) {\n        outputs.push(\n          parseAbiParameter(outputParams[i]!, {\n            modifiers: functionModifiers,\n            structs,\n            type: 'function',\n          }),\n        )\n      }\n    }\n\n    return {\n      name: match.name,\n      type: 'function',\n      stateMutability: match.stateMutability ?? 'nonpayable',\n      inputs,\n      outputs,\n    }\n  }\n\n  if (isEventSignature(signature)) {\n    const match = execEventSignature(signature)\n    if (!match) throw new InvalidSignatureError({ signature, type: 'event' })\n\n    const params = splitParameters(match.parameters)\n    const abiParameters = []\n    const length = params.length\n    for (let i = 0; i < length; i++) {\n      abiParameters.push(\n        parseAbiParameter(params[i]!, {\n          modifiers: eventModifiers,\n          structs,\n          type: 'event',\n        }),\n      )\n    }\n    return { name: match.name, type: 'event', inputs: abiParameters }\n  }\n\n  if (isErrorSignature(signature)) {\n    const match = execErrorSignature(signature)\n    if (!match) throw new InvalidSignatureError({ signature, type: 'error' })\n\n    const params = splitParameters(match.parameters)\n    const abiParameters = []\n    const length = params.length\n    for (let i = 0; i < length; i++) {\n      abiParameters.push(\n        parseAbiParameter(params[i]!, { structs, type: 'error' }),\n      )\n    }\n    return { name: match.name, type: 'error', inputs: abiParameters }\n  }\n\n  if (isConstructorSignature(signature)) {\n    const match = execConstructorSignature(signature)\n    if (!match)\n      throw new InvalidSignatureError({ signature, type: 'constructor' })\n\n    const params = splitParameters(match.parameters)\n    const abiParameters = []\n    const length = params.length\n    for (let i = 0; i < length; i++) {\n      abiParameters.push(\n        parseAbiParameter(params[i]!, { structs, type: 'constructor' }),\n      )\n    }\n    return {\n      type: 'constructor',\n      stateMutability: match.stateMutability ?? 'nonpayable',\n      inputs: abiParameters,\n    }\n  }\n\n  if (isFallbackSignature(signature)) return { type: 'fallback' }\n  if (isReceiveSignature(signature))\n    return {\n      type: 'receive',\n      stateMutability: 'payable',\n    }\n\n  throw new UnknownSignatureError({ signature })\n}\n\nconst abiParameterWithoutTupleRegex =\n  /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/\nconst abiParameterWithTupleRegex =\n  /^\\((?<type>.+?)\\)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/\nconst dynamicIntegerRegex = /^u?int$/\n\ntype ParseOptions = {\n  modifiers?: Set<Modifier>\n  structs?: StructLookup\n  type?: AbiItemType | 'struct'\n}\n\nexport function parseAbiParameter(param: string, options?: ParseOptions) {\n  // optional namespace cache by `type`\n  const parameterCacheKey = getParameterCacheKey(param, options?.type)\n  if (parameterCache.has(parameterCacheKey))\n    return parameterCache.get(parameterCacheKey)!\n\n  const isTuple = isTupleRegex.test(param)\n  const match = execTyped<{\n    array?: string\n    modifier?: Modifier\n    name?: string\n    type: string\n  }>(\n    isTuple ? abiParameterWithTupleRegex : abiParameterWithoutTupleRegex,\n    param,\n  )\n  if (!match) throw new InvalidParameterError({ param })\n\n  if (match.name && isSolidityKeyword(match.name))\n    throw new SolidityProtectedKeywordError({ param, name: match.name })\n\n  const name = match.name ? { name: match.name } : {}\n  const indexed = match.modifier === 'indexed' ? { indexed: true } : {}\n  const structs = options?.structs ?? {}\n  let type: string\n  let components = {}\n  if (isTuple) {\n    type = 'tuple'\n    const params = splitParameters(match.type)\n    const components_ = []\n    const length = params.length\n    for (let i = 0; i < length; i++) {\n      // remove `modifiers` from `options` to prevent from being added to tuple components\n      components_.push(parseAbiParameter(params[i]!, { structs }))\n    }\n    components = { components: components_ }\n  } else if (match.type in structs) {\n    type = 'tuple'\n    components = { components: structs[match.type] }\n  } else if (dynamicIntegerRegex.test(match.type)) {\n    type = `${match.type}256`\n  } else {\n    type = match.type\n    if (!(options?.type === 'struct') && !isSolidityType(type))\n      throw new UnknownSolidityTypeError({ type })\n  }\n\n  if (match.modifier) {\n    // Check if modifier exists, but is not allowed (e.g. `indexed` in `functionModifiers`)\n    if (!options?.modifiers?.has?.(match.modifier))\n      throw new InvalidModifierError({\n        param,\n        type: options?.type,\n        modifier: match.modifier,\n      })\n\n    // Check if resolved `type` is valid if there is a function modifier\n    if (\n      functionModifiers.has(match.modifier as FunctionModifier) &&\n      !isValidDataLocation(type, !!match.array)\n    )\n      throw new InvalidFunctionModifierError({\n        param,\n        type: options?.type,\n        modifier: match.modifier,\n      })\n  }\n\n  const abiParameter = {\n    type: `${type}${match.array ?? ''}`,\n    ...name,\n    ...indexed,\n    ...components,\n  }\n  parameterCache.set(parameterCacheKey, abiParameter)\n  return abiParameter\n}\n\n// s/o latika for this\nexport function splitParameters(\n  params: string,\n  result: string[] = [],\n  current = '',\n  depth = 0,\n): readonly string[] {\n  if (params === '') {\n    if (current === '') return result\n    if (depth !== 0) throw new InvalidParenthesisError({ current, depth })\n\n    result.push(current.trim())\n    return result\n  }\n\n  const length = params.length\n  // rome-ignore lint/correctness/noUnreachable: recursive\n  for (let i = 0; i < length; i++) {\n    const char = params[i]\n    const tail = params.slice(i + 1)\n    switch (char) {\n      case ',':\n        return depth === 0\n          ? splitParameters(tail, [...result, current.trim()])\n          : splitParameters(tail, result, `${current}${char}`, depth)\n      case '(':\n        return splitParameters(tail, result, `${current}${char}`, depth + 1)\n      case ')':\n        return splitParameters(tail, result, `${current}${char}`, depth - 1)\n      default:\n        return splitParameters(tail, result, `${current}${char}`, depth)\n    }\n  }\n\n  return []\n}\n\nexport function isSolidityType(\n  type: string,\n): type is Exclude<AbiType, SolidityTuple | SolidityArray> {\n  return (\n    type === 'address' ||\n    type === 'bool' ||\n    type === 'function' ||\n    type === 'string' ||\n    bytesRegex.test(type) ||\n    integerRegex.test(type)\n  )\n}\n\nconst protectedKeywordsRegex =\n  /^(?:after|alias|anonymous|apply|auto|byte|calldata|case|catch|constant|copyof|default|defined|error|event|external|false|final|function|immutable|implements|in|indexed|inline|internal|let|mapping|match|memory|mutable|null|of|override|partial|private|promise|public|pure|reference|relocatable|return|returns|sizeof|static|storage|struct|super|supports|switch|this|true|try|typedef|typeof|var|view|virtual)$/\n\nexport function isSolidityKeyword(name: string) {\n  return (\n    name === 'address' ||\n    name === 'bool' ||\n    name === 'function' ||\n    name === 'string' ||\n    name === 'tuple' ||\n    bytesRegex.test(name) ||\n    integerRegex.test(name) ||\n    protectedKeywordsRegex.test(name)\n  )\n}\n\nexport function isValidDataLocation(\n  type: string,\n  isArray: boolean,\n): type is Exclude<\n  AbiType,\n  SolidityString | Extract<SolidityBytes, 'bytes'> | SolidityArray\n> {\n  return isArray || type === 'bytes' || type === 'string' || type === 'tuple'\n}\n", "import type { AbiParameter } from '../../abi.js'\nimport { execTyped, isTupleRegex } from '../../regex.js'\nimport { UnknownTypeError } from '../errors/abiItem.js'\nimport { InvalidAbiTypeParameterError } from '../errors/abiParameter.js'\nimport {\n  InvalidSignatureError,\n  InvalidStructSignatureError,\n} from '../errors/signature.js'\nimport { CircularReferenceError } from '../errors/struct.js'\nimport type { StructLookup } from '../types/structs.js'\nimport { execStructSignature, isStructSignature } from './signatures.js'\nimport { isSolidityType, parseAbiParameter } from './utils.js'\n\nexport function parseStructs(signatures: readonly string[]) {\n  // Create \"shallow\" version of each struct (and filter out non-structs or invalid structs)\n  const shallowStructs: StructLookup = {}\n  const signaturesLength = signatures.length\n  for (let i = 0; i < signaturesLength; i++) {\n    const signature = signatures[i]!\n    if (!isStructSignature(signature)) continue\n\n    const match = execStructSignature(signature)\n    if (!match) throw new InvalidSignatureError({ signature, type: 'struct' })\n\n    const properties = match.properties.split(';')\n\n    const components: AbiParameter[] = []\n    const propertiesLength = properties.length\n    for (let k = 0; k < propertiesLength; k++) {\n      const property = properties[k]!\n      const trimmed = property.trim()\n      if (!trimmed) continue\n      const abiParameter = parseAbiParameter(trimmed, {\n        type: 'struct',\n      })\n      components.push(abiParameter)\n    }\n\n    if (!components.length) throw new InvalidStructSignatureError({ signature })\n    shallowStructs[match.name] = components\n  }\n\n  // Resolve nested structs inside each parameter\n  const resolvedStructs: StructLookup = {}\n  const entries = Object.entries(shallowStructs)\n  const entriesLength = entries.length\n  for (let i = 0; i < entriesLength; i++) {\n    const [name, parameters] = entries[i]!\n    resolvedStructs[name] = resolveStructs(parameters, shallowStructs)\n  }\n\n  return resolvedStructs\n}\n\nconst typeWithoutTupleRegex =\n  /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?$/\n\nfunction resolveStructs(\n  abiParameters: readonly (AbiParameter & { indexed?: true })[],\n  structs: StructLookup,\n  ancestors = new Set<string>(),\n) {\n  const components: AbiParameter[] = []\n  const length = abiParameters.length\n  for (let i = 0; i < length; i++) {\n    const abiParameter = abiParameters[i]!\n    const isTuple = isTupleRegex.test(abiParameter.type)\n    if (isTuple) components.push(abiParameter)\n    else {\n      const match = execTyped<{ array?: string; type: string }>(\n        typeWithoutTupleRegex,\n        abiParameter.type,\n      )\n      if (!match?.type) throw new InvalidAbiTypeParameterError({ abiParameter })\n\n      const { array, type } = match\n      if (type in structs) {\n        if (ancestors.has(type)) throw new CircularReferenceError({ type })\n\n        components.push({\n          ...abiParameter,\n          type: `tuple${array ?? ''}`,\n          components: resolveStructs(\n            structs[type] ?? [],\n            structs,\n            new Set([...ancestors, type]),\n          ),\n        })\n      } else {\n        if (isSolidityType(type)) components.push(abiParameter)\n        else throw new UnknownTypeError({ type })\n      }\n    }\n  }\n\n  return components\n}\n", "import type { <PERSON><PERSON> } from '../abi.js'\nimport type { <PERSON>rror, Filter } from '../types.js'\nimport { isStructSignature } from './runtime/signatures.js'\nimport { parseStructs } from './runtime/structs.js'\nimport { parseSignature } from './runtime/utils.js'\nimport type { Signatures } from './types/signatures.js'\nimport type { ParseStructs } from './types/structs.js'\nimport type { ParseSignature } from './types/utils.js'\n\n/**\n * Parses human-readable ABI into JSON {@link Abi}\n *\n * @param TSignatures - Human-readable ABI\n * @returns Parsed {@link Abi}\n *\n * @example\n * type Result = ParseAbi<\n *   // ^? type Result = readonly [{ name: \"balanceOf\"; type: \"function\"; stateMutability:...\n *   [\n *     'function balanceOf(address owner) view returns (uint256)',\n *     'event Transfer(address indexed from, address indexed to, uint256 amount)',\n *   ]\n * >\n */\nexport type Parse<PERSON><PERSON><TSignatures extends readonly string[]> =\n  string[] extends TSignatures\n    ? Abi // If `T` was not able to be inferred (e.g. just `string[]`), return `Abi`\n    : TSignatures extends readonly string[]\n    ? TSignatures extends Signatures<TSignatures> // Validate signatures\n      ? ParseStructs<TSignatures> extends infer Structs\n        ? {\n            [K in keyof TSignatures]: TSignatures[K] extends string\n              ? ParseSignature<TSignatures[K], Structs>\n              : never\n          } extends infer Mapped extends readonly unknown[]\n          ? Filter<Mapped, never> extends infer Result\n            ? Result extends readonly []\n              ? never\n              : Result\n            : never\n          : never\n        : never\n      : never\n    : never\n\n/**\n * Parses human-readable ABI into JSON {@link Abi}\n *\n * @param signatures - Human-Readable ABI\n * @returns Parsed {@link Abi}\n *\n * @example\n * const abi = parseAbi([\n *   //  ^? const abi: readonly [{ name: \"balanceOf\"; type: \"function\"; stateMutability:...\n *   'function balanceOf(address owner) view returns (uint256)',\n *   'event Transfer(address indexed from, address indexed to, uint256 amount)',\n * ])\n */\nexport function parseAbi<const TSignatures extends readonly string[]>(\n  signatures: TSignatures['length'] extends 0\n    ? Error<'At least one signature required'>\n    : Signatures<TSignatures> extends TSignatures\n    ? TSignatures\n    : Signatures<TSignatures>,\n): ParseAbi<TSignatures> {\n  const structs = parseStructs(signatures as readonly string[])\n  const abi = []\n  const length = signatures.length as number\n  for (let i = 0; i < length; i++) {\n    const signature = (signatures as readonly string[])[i]!\n    if (isStructSignature(signature)) continue\n    abi.push(parseSignature(signature, structs))\n  }\n  return abi as unknown as ParseAbi<TSignatures>\n}\n", "import type { AbiEventParameter, AbiParameter } from '../abi.js'\nimport { execTyped } from '../regex.js'\nimport type { IsNarrowable, Join } from '../types.js'\nimport type { AssertName } from './types/signatures.js'\n\n/**\n * Formats {@link AbiParameter} to human-readable ABI parameter.\n *\n * @param TAbiParameter - ABI parameter\n * @returns Human-readable ABI parameter\n *\n * @example\n * type Result = FormatAbiParameter<{ type: 'address'; name: 'from'; }>\n * //   ^? type Result = 'address from'\n */\nexport type FormatAbiParameter<\n  TAbiParameter extends AbiParameter | AbiEventParameter,\n> = TAbiParameter extends {\n  name?: infer Name extends string\n  type: `tuple${infer Array}`\n  components: infer Components extends readonly AbiParameter[]\n  indexed?: infer Indexed extends boolean\n}\n  ? FormatAbiParameter<\n      {\n        type: `(${Join<\n          {\n            [K in keyof Components]: FormatAbiParameter<\n              {\n                type: Components[K]['type']\n              } & (IsNarrowable<Components[K]['name'], string> extends true\n                ? { name: Components[K]['name'] }\n                : unknown) &\n                (Components[K] extends { components: readonly AbiParameter[] }\n                  ? { components: Components[K]['components'] }\n                  : unknown)\n            >\n          },\n          ', '\n        >})${Array}`\n      } & (IsNarrowable<Name, string> extends true ? { name: Name } : unknown) &\n        (IsNarrowable<Indexed, boolean> extends true\n          ? { indexed: Indexed }\n          : unknown)\n    >\n  : `${TAbiParameter['type']}${TAbiParameter extends { indexed: true }\n      ? ' indexed'\n      : ''}${TAbiParameter['name'] extends infer Name extends string\n      ? Name extends ''\n        ? ''\n        : ` ${AssertName<Name>}`\n      : ''}`\n\n// https://regexr.com/7f7rv\nconst tupleRegex = /^tuple(?<array>(\\[(\\d*)\\])*)$/\n\n/**\n * Formats {@link AbiParameter} to human-readable ABI parameter.\n *\n * @param abiParameter - ABI parameter\n * @returns Human-readable ABI parameter\n *\n * @example\n * const result = formatAbiParameter({ type: 'address', name: 'from' })\n * //    ^? const result: 'address from'\n */\nexport function formatAbiParameter<\n  const TAbiParameter extends AbiParameter | AbiEventParameter,\n>(abiParameter: TAbiParameter): FormatAbiParameter<TAbiParameter> {\n  type Result = FormatAbiParameter<TAbiParameter>\n\n  let type = abiParameter.type\n  if (tupleRegex.test(abiParameter.type) && 'components' in abiParameter) {\n    type = '('\n    const length = abiParameter.components.length as number\n    for (let i = 0; i < length; i++) {\n      const component = abiParameter.components[i]!\n      type += formatAbiParameter(component)\n      if (i < length - 1) type += ', '\n    }\n    const result = execTyped<{ array?: string }>(tupleRegex, abiParameter.type)\n    type += `)${result?.array ?? ''}`\n    return formatAbiParameter({\n      ...abiParameter,\n      type,\n    }) as Result\n  }\n  // Add `indexed` to type if in `abiParameter`\n  if ('indexed' in abiParameter && abiParameter.indexed)\n    type = `${type} indexed`\n  // Return human-readable ABI parameter\n  if (abiParameter.name) return `${type} ${abiParameter.name}` as Result\n  return type as Result\n}\n", "import type { AbiEventParameter, AbiParameter } from '../abi.js'\nimport type { Join } from '../types.js'\nimport {\n  type FormatAbiParameter,\n  formatAbiParameter,\n} from './formatAbiParameter.js'\n\n/**\n * Formats {@link AbiParameter}s to human-readable ABI parameter.\n *\n * @param TAbiParameters - ABI parameters\n * @returns Human-readable ABI parameters\n *\n * @example\n * type Result = FormatAbiParameters<[\n *   // ^? type Result = 'address from, uint256 tokenId'\n *   { type: 'address'; name: 'from'; },\n *   { type: 'uint256'; name: 'tokenId'; },\n * ]>\n */\nexport type FormatAbiParameters<\n  TAbiParameters extends readonly [\n    AbiParameter | AbiEventParameter,\n    ...(readonly (AbiParameter | AbiEventParameter)[]),\n  ],\n> = Join<\n  {\n    [K in keyof TAbiParameters]: FormatAbiParameter<TAbiParameters[K]>\n  },\n  ', '\n>\n\n/**\n * Formats {@link AbiParameter}s to human-readable ABI parameters.\n *\n * @param abiParameters - ABI parameters\n * @returns Human-readable ABI parameters\n *\n * @example\n * const result = formatAbiParameters([\n *   //  ^? const result: 'address from, uint256 tokenId'\n *   { type: 'address', name: 'from' },\n *   { type: 'uint256', name: 'tokenId' },\n * ])\n */\nexport function formatAbiParameters<\n  const TAbiParameters extends readonly [\n    AbiParameter | AbiEventParameter,\n    ...(readonly (AbiParameter | AbiEventParameter)[]),\n  ],\n>(abiParameters: TAbiParameters): FormatAbiParameters<TAbiParameters> {\n  let params = ''\n  const length = abiParameters.length\n  for (let i = 0; i < length; i++) {\n    const abiParameter = abiParameters[i]!\n    params += formatAbiParameter(abiParameter)\n    if (i !== length - 1) params += ', '\n  }\n  return params as FormatAbiParameters<TAbiParameters>\n}\n", "import type {\n  <PERSON><PERSON>,\n  <PERSON>biConstructor,\n  AbiError,\n  AbiEvent,\n  AbiEventParameter,\n  AbiFallback,\n  AbiFunction,\n  AbiParameter,\n  AbiReceive,\n  AbiStateMutability,\n} from '../abi.js'\nimport {\n  type FormatAbiParameters as FormatAbiParameters_,\n  formatAbiParameters,\n} from './formatAbiParameters.js'\nimport type { AssertName } from './types/signatures.js'\n\n/**\n * Formats ABI item (e.g. error, event, function) into human-readable ABI item\n *\n * @param TAbiItem - ABI item\n * @returns Human-readable ABI item\n */\nexport type FormatAbiItem<TAbiItem extends Abi[number]> =\n  Abi[number] extends TAbiItem\n    ? string\n    :\n        | (TAbiItem extends AbiFunction\n            ? AbiFunction extends TAbiItem\n              ? string\n              : `function ${AssertName<TAbiItem['name']>}(${FormatAbiParameters<\n                  TAbiItem['inputs']\n                >})${TAbiItem['stateMutability'] extends Exclude<\n                  AbiStateMutability,\n                  'nonpayable'\n                >\n                  ? ` ${TAbiItem['stateMutability']}`\n                  : ''}${TAbiItem['outputs']['length'] extends 0\n                  ? ''\n                  : ` returns (${FormatAbiParameters<TAbiItem['outputs']>})`}`\n            : never)\n        | (TAbiItem extends AbiEvent\n            ? AbiEvent extends TAbiItem\n              ? string\n              : `event ${AssertName<TAbiItem['name']>}(${FormatAbiParameters<\n                  TAbiItem['inputs']\n                >})`\n            : never)\n        | (TAbiItem extends AbiError\n            ? AbiError extends TAbiItem\n              ? string\n              : `error ${AssertName<TAbiItem['name']>}(${FormatAbiParameters<\n                  TAbiItem['inputs']\n                >})`\n            : never)\n        | (TAbiItem extends AbiConstructor\n            ? AbiConstructor extends TAbiItem\n              ? string\n              : `constructor(${FormatAbiParameters<\n                  TAbiItem['inputs']\n                >})${TAbiItem['stateMutability'] extends 'payable'\n                  ? ' payable'\n                  : ''}`\n            : never)\n        | (TAbiItem extends AbiFallback\n            ? AbiFallback extends TAbiItem\n              ? string\n              : 'fallback()'\n            : never)\n        | (TAbiItem extends AbiReceive\n            ? AbiReceive extends TAbiItem\n              ? string\n              : 'receive() external payable'\n            : never)\n\ntype FormatAbiParameters<\n  TAbiParameters extends readonly (AbiParameter | AbiEventParameter)[],\n> = TAbiParameters['length'] extends 0\n  ? ''\n  : FormatAbiParameters_<\n      TAbiParameters extends readonly [\n        AbiParameter | AbiEventParameter,\n        ...(readonly (AbiParameter | AbiEventParameter)[]),\n      ]\n        ? TAbiParameters\n        : never\n    >\n\n/**\n * Formats ABI item (e.g. error, event, function) into human-readable ABI item\n *\n * @param abiItem - ABI item\n * @returns Human-readable ABI item\n */\nexport function formatAbiItem<const TAbiItem extends Abi[number]>(\n  abiItem: TAbiItem,\n): FormatAbiItem<TAbiItem> {\n  type Result = FormatAbiItem<TAbiItem>\n  type Params = readonly [\n    AbiParameter | AbiEventParameter,\n    ...(readonly (AbiParameter | AbiEventParameter)[]),\n  ]\n\n  if (abiItem.type === 'function')\n    return `function ${abiItem.name}(${formatAbiParameters(\n      abiItem.inputs as Params,\n    )})${\n      abiItem.stateMutability && abiItem.stateMutability !== 'nonpayable'\n        ? ` ${abiItem.stateMutability}`\n        : ''\n    }${\n      abiItem.outputs.length\n        ? ` returns (${formatAbiParameters(abiItem.outputs as Params)})`\n        : ''\n    }`\n  else if (abiItem.type === 'event')\n    return `event ${abiItem.name}(${formatAbiParameters(\n      abiItem.inputs as Params,\n    )})`\n  else if (abiItem.type === 'error')\n    return `error ${abiItem.name}(${formatAbiParameters(\n      abiItem.inputs as Params,\n    )})`\n  else if (abiItem.type === 'constructor')\n    return `constructor(${formatAbiParameters(abiItem.inputs as Params)})${\n      abiItem.stateMutability === 'payable' ? ' payable' : ''\n    }`\n  else if (abiItem.type === 'fallback') return 'fallback()' as Result\n  return 'receive() external payable' as Result\n}\n", "import type { AbiParameter } from '../abi.js'\nimport { InvalidAbiParameterError } from '../index.js'\nimport type { Narrow } from '../narrow.js'\nimport type { Error, Filter } from '../types.js'\nimport { isStructSignature, modifiers } from './runtime/signatures.js'\nimport { parseStructs } from './runtime/structs.js'\nimport { parseAbiParameter as parseAbiParameter_ } from './runtime/utils.js'\nimport type { IsStructSignature, Modifier } from './types/signatures.js'\nimport type { ParseStructs } from './types/structs.js'\nimport type { ParseAbiParameter as ParseAbiParameter_ } from './types/utils.js'\n\n/**\n * Parses human-readable ABI parameter into {@link AbiParameter}\n *\n * @param TParam - Human-readable ABI parameter\n * @returns Parsed {@link AbiParameter}\n *\n * @example\n * type Result = ParseAbiParameter<'address from'>\n * //   ^? type Result = { type: \"address\"; name: \"from\"; }\n *\n * @example\n * type Result = ParseAbiParameter<\n *   // ^? type Result = { type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   ['Baz bar', 'struct Baz { string name; }']\n * >\n */\nexport type ParseAbiParameter<\n  TParam extends string | readonly string[] | readonly unknown[],\n> =\n  | (TParam extends string\n      ? TParam extends ''\n        ? never\n        : string extends TParam\n        ? AbiParameter\n        : ParseAbiParameter_<TParam, { Modifier: Modifier }>\n      : never)\n  | (TParam extends readonly string[]\n      ? string[] extends TParam\n        ? AbiParameter // Return generic AbiParameter item since type was no inferrable\n        : ParseStructs<TParam> extends infer Structs\n        ? {\n            [K in keyof TParam]: TParam[K] extends string\n              ? IsStructSignature<TParam[K]> extends true\n                ? never\n                : ParseAbiParameter_<\n                    TParam[K],\n                    { Modifier: Modifier; Structs: Structs }\n                  >\n              : never\n          } extends infer Mapped extends readonly unknown[]\n          ? Filter<Mapped, never>[0] extends infer Result\n            ? Result extends undefined\n              ? never\n              : Result\n            : never\n          : never\n        : never\n      : never)\n\n/**\n * Parses human-readable ABI parameter into {@link AbiParameter}\n *\n * @param param - Human-readable ABI parameter\n * @returns Parsed {@link AbiParameter}\n *\n * @example\n * const abiParameter = parseAbiParameter('address from')\n * //    ^? const abiParameter: { type: \"address\"; name: \"from\"; }\n *\n * @example\n * const abiParameter = parseAbiParameter([\n *   //  ^? const abiParameter: { type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   'Baz bar',\n *   'struct Baz { string name; }',\n * ])\n */\nexport function parseAbiParameter<\n  TParam extends string | readonly string[] | readonly unknown[],\n>(\n  param: Narrow<TParam> &\n    (\n      | (TParam extends string\n          ? TParam extends ''\n            ? Error<'Empty string is not allowed.'>\n            : unknown\n          : never)\n      | (TParam extends readonly string[]\n          ? TParam extends readonly [] // empty array\n            ? Error<'At least one parameter required.'>\n            : string[] extends TParam\n            ? unknown\n            : unknown // TODO: Validate param string\n          : never)\n    ),\n): ParseAbiParameter<TParam> {\n  let abiParameter\n  if (typeof param === 'string')\n    abiParameter = parseAbiParameter_(param, {\n      modifiers,\n    }) as ParseAbiParameter<TParam>\n  else {\n    const structs = parseStructs(param as readonly string[])\n    const length = param.length as number\n    for (let i = 0; i < length; i++) {\n      const signature = (param as readonly string[])[i]!\n      if (isStructSignature(signature)) continue\n      abiParameter = parseAbiParameter_(signature, { modifiers, structs })\n      break\n    }\n  }\n\n  if (!abiParameter) throw new InvalidAbiParameterError({ param })\n\n  return abiParameter as ParseAbiParameter<TParam>\n}\n", "import type { AbiParameter } from '../abi.js'\nimport { InvalidAbiParametersError } from '../index.js'\nimport type { Narrow } from '../narrow.js'\nimport type { Error, Filter } from '../types.js'\nimport { isStructSignature, modifiers } from './runtime/signatures.js'\nimport { parseStructs } from './runtime/structs.js'\nimport { splitParameters } from './runtime/utils.js'\nimport { parseAbiParameter as parseAbiParameter_ } from './runtime/utils.js'\nimport type { IsStructSignature, Modifier } from './types/signatures.js'\nimport type { ParseStructs } from './types/structs.js'\nimport type { SplitParameters } from './types/utils.js'\nimport type { ParseAbiParameters as ParseAbiParameters_ } from './types/utils.js'\n\n/**\n * Parses human-readable ABI parameters into {@link AbiParameter}s\n *\n * @param TParams - Human-readable ABI parameters\n * @returns Parsed {@link AbiParameter}s\n *\n * @example\n * type Result = ParseAbiParameters('address from, address to, uint256 amount')\n * //   ^? type Result: [{ type: \"address\"; name: \"from\"; }, { type: \"address\";...\n *\n * @example\n * type Result = ParseAbiParameters<\n *   // ^? type Result: [{ type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   ['Baz bar', 'struct Baz { string name; }']\n * >\n */\nexport type ParseAbiParameters<\n  TParams extends string | readonly string[] | readonly unknown[],\n> =\n  | (TParams extends string\n      ? TParams extends ''\n        ? never\n        : string extends TParams\n        ? readonly AbiParameter[]\n        : ParseAbiParameters_<SplitParameters<TParams>, { Modifier: Modifier }>\n      : never)\n  | (TParams extends readonly string[]\n      ? string[] extends TParams\n        ? AbiParameter // Return generic AbiParameter item since type was no inferrable\n        : ParseStructs<TParams> extends infer Structs\n        ? {\n            [K in keyof TParams]: TParams[K] extends string\n              ? IsStructSignature<TParams[K]> extends true\n                ? never\n                : ParseAbiParameters_<\n                    SplitParameters<TParams[K]>,\n                    { Modifier: Modifier; Structs: Structs }\n                  >\n              : never\n          } extends infer Mapped extends readonly unknown[]\n          ? Filter<Mapped, never>[0] extends infer Result\n            ? Result extends undefined\n              ? never\n              : Result\n            : never\n          : never\n        : never\n      : never)\n\n/**\n * Parses human-readable ABI parameters into {@link AbiParameter}s\n *\n * @param params - Human-readable ABI parameters\n * @returns Parsed {@link AbiParameter}s\n *\n * @example\n * const abiParameters = parseAbiParameters('address from, address to, uint256 amount')\n * //    ^? const abiParameters: [{ type: \"address\"; name: \"from\"; }, { type: \"address\";...\n *\n * @example\n * const abiParameters = parseAbiParameters([\n *   //  ^? const abiParameters: [{ type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   'Baz bar',\n *   'struct Baz { string name; }',\n * ])\n */\nexport function parseAbiParameters<\n  TParams extends string | readonly string[] | readonly unknown[],\n>(\n  params: Narrow<TParams> &\n    (\n      | (TParams extends string\n          ? TParams extends ''\n            ? Error<'Empty string is not allowed.'>\n            : unknown\n          : never)\n      | (TParams extends readonly string[]\n          ? TParams extends readonly [] // empty array\n            ? Error<'At least one parameter required.'>\n            : string[] extends TParams\n            ? unknown\n            : unknown // TODO: Validate param string\n          : never)\n    ),\n): ParseAbiParameters<TParams> {\n  const abiParameters: AbiParameter[] = []\n  if (typeof params === 'string') {\n    const parameters = splitParameters(params)\n    const length = parameters.length\n    for (let i = 0; i < length; i++) {\n      abiParameters.push(parseAbiParameter_(parameters[i]!, { modifiers }))\n    }\n  } else {\n    const structs = parseStructs(params as readonly string[])\n    const length = params.length as number\n    for (let i = 0; i < length; i++) {\n      const signature = (params as readonly string[])[i]!\n      if (isStructSignature(signature)) continue\n      const parameters = splitParameters(signature)\n      const length = parameters.length\n      for (let k = 0; k < length; k++) {\n        abiParameters.push(\n          parseAbiParameter_(parameters[k]!, { modifiers, structs }),\n        )\n      }\n    }\n  }\n\n  if (abiParameters.length === 0)\n    throw new InvalidAbiParametersError({ params })\n\n  return abiParameters as ParseAbiParameters<TParams>\n}\n", "import type { <PERSON><PERSON> } from '../abi.js'\nimport { InvalidAbiItemError } from '../index.js'\nimport type { Narrow } from '../narrow.js'\nimport type { Error, Filter } from '../types.js'\nimport { isStructSignature } from './runtime/signatures.js'\nimport { parseStructs } from './runtime/structs.js'\nimport { parseSignature } from './runtime/utils.js'\nimport type { Signature, Signatures } from './types/signatures.js'\nimport type { ParseStructs } from './types/structs.js'\nimport type { ParseSignature } from './types/utils.js'\n\n/**\n * Parses human-readable ABI item (e.g. error, event, function) into {@link Abi} item\n *\n * @param TSignature - Human-readable ABI item\n * @returns Parsed {@link Abi} item\n *\n * @example\n * type Result = ParseAbiItem<'function balanceOf(address owner) view returns (uint256)'>\n * //   ^? type Result = { name: \"balanceOf\"; type: \"function\"; stateMutability: \"view\";...\n *\n * @example\n * type Result = ParseAbiItem<\n *   // ^? type Result = { name: \"foo\"; type: \"function\"; stateMutability: \"view\"; inputs:...\n *   ['function foo(Baz bar) view returns (string)', 'struct Baz { string name; }']\n * >\n */\nexport type ParseAbiItem<\n  TSignature extends string | readonly string[] | readonly unknown[],\n> =\n  | (TSignature extends string\n      ? string extends TSignature\n        ? Abi[number]\n        : TSignature extends Signature<TSignature> // Validate signature\n        ? ParseSignature<TSignature>\n        : never\n      : never)\n  | (TSignature extends readonly string[]\n      ? string[] extends TSignature\n        ? Abi[number] // Return generic Abi item since type was no inferrable\n        : TSignature extends Signatures<TSignature> // Validate signature\n        ? ParseStructs<TSignature> extends infer Structs\n          ? {\n              [K in keyof TSignature]: ParseSignature<\n                TSignature[K] extends string ? TSignature[K] : never,\n                Structs\n              >\n            } extends infer Mapped extends readonly unknown[]\n            ? // Filter out `never` since those are structs\n              Filter<Mapped, never>[0] extends infer Result\n              ? Result extends undefined // convert `undefined` to `never` (e.g. `ParseAbiItem<['struct Foo { string name; }']>`)\n                ? never\n                : Result\n              : never\n            : never\n          : never\n        : never\n      : never)\n\n/**\n * Parses human-readable ABI item (e.g. error, event, function) into {@link Abi} item\n *\n * @param signature - Human-readable ABI item\n * @returns Parsed {@link Abi} item\n *\n * @example\n * const abiItem = parseAbiItem('function balanceOf(address owner) view returns (uint256)')\n * //    ^? const abiItem: { name: \"balanceOf\"; type: \"function\"; stateMutability: \"view\";...\n *\n * @example\n * const abiItem = parseAbiItem([\n *   //  ^? const abiItem: { name: \"foo\"; type: \"function\"; stateMutability: \"view\"; inputs:...\n *   'function foo(Baz bar) view returns (string)',\n *   'struct Baz { string name; }',\n * ])\n */\nexport function parseAbiItem<\n  TSignature extends string | readonly string[] | readonly unknown[],\n>(\n  signature: Narrow<TSignature> &\n    (\n      | (TSignature extends string\n          ? string extends TSignature\n            ? unknown\n            : Signature<TSignature>\n          : never)\n      | (TSignature extends readonly string[]\n          ? TSignature extends readonly [] // empty array\n            ? Error<'At least one signature required.'>\n            : string[] extends TSignature\n            ? unknown\n            : Signatures<TSignature>\n          : never)\n    ),\n): ParseAbiItem<TSignature> {\n  let abiItem\n  if (typeof signature === 'string')\n    abiItem = parseSignature(signature) as ParseAbiItem<TSignature>\n  else {\n    const structs = parseStructs(signature as readonly string[])\n    const length = signature.length as number\n    for (let i = 0; i < length; i++) {\n      const signature_ = (signature as readonly string[])[i]!\n      if (isStructSignature(signature_)) continue\n      abiItem = parseSignature(signature_, structs)\n      break\n    }\n  }\n\n  if (!abiItem) throw new InvalidAbiItemError({ signature })\n  return abiItem as ParseAbiItem<TSignature>\n}\n", "import type { AbiParameter } from 'abitype'\n\nimport {\n  InvalidDefinitionTypeError,\n  type InvalidDefinitionTypeErrorType,\n} from '../../errors/abi.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { AbiItem } from '../../types/contract.js'\n\nexport type FormatAbiItemErrorType =\n  | FormatAbiParamsErrorType\n  | InvalidDefinitionTypeErrorType\n  | ErrorType\n\nexport function formatAbiItem(\n  abiItem: AbiItem,\n  { includeName = false }: { includeName?: boolean } = {},\n) {\n  if (\n    abiItem.type !== 'function' &&\n    abiItem.type !== 'event' &&\n    abiItem.type !== 'error'\n  )\n    throw new InvalidDefinitionTypeError(abiItem.type)\n\n  return `${abiItem.name}(${formatAbiParams(abiItem.inputs, { includeName })})`\n}\n\nexport type FormatAbiParamsErrorType = ErrorType\n\nexport function formatAbiParams(\n  params: readonly AbiParameter[] | undefined,\n  { includeName = false }: { includeName?: boolean } = {},\n): string {\n  if (!params) return ''\n  return params\n    .map((param) => formatAbiParam(param, { includeName }))\n    .join(includeName ? ', ' : ',')\n}\n\nexport type FormatAbiParamErrorType = ErrorType\n\nfunction formatAbiParam(\n  param: AbiParameter,\n  { includeName }: { includeName: boolean },\n): string {\n  if (param.type.startsWith('tuple')) {\n    return `(${formatAbiParams(\n      (param as unknown as { components: AbiParameter[] }).components,\n      { includeName },\n    )})${param.type.slice('tuple'.length)}`\n  }\n  return param.type + (includeName && param.name ? ` ${param.name}` : '')\n}\n", "import type { <PERSON><PERSON>, <PERSON>biEvent, AbiParameter } from 'abitype'\n\nimport type { Hex } from '../types/misc.js'\nimport { formatAbiItem, formatAbiParams } from '../utils/abi/formatAbiItem.js'\nimport { size } from '../utils/data/size.js'\n\nimport { BaseError } from './base.js'\n\nexport type AbiConstructorNotFoundErrorType = AbiConstructorNotFoundError & {\n  name: 'AbiConstructorNotFoundError'\n}\nexport class AbiConstructorNotFoundError extends BaseError {\n  override name = 'AbiConstructorNotFoundError'\n  constructor({ docsPath }: { docsPath: string }) {\n    super(\n      [\n        'A constructor was not found on the ABI.',\n        'Make sure you are using the correct ABI and that the constructor exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n  }\n}\n\nexport type AbiConstructorParamsNotFoundErrorType =\n  AbiConstructorParamsNotFoundError & {\n    name: 'AbiConstructorParamsNotFoundError'\n  }\n\nexport class AbiConstructorParamsNotFoundError extends BaseError {\n  override name = 'AbiConstructorParamsNotFoundError'\n  constructor({ docsPath }: { docsPath: string }) {\n    super(\n      [\n        'Constructor arguments were provided (`args`), but a constructor parameters (`inputs`) were not found on the ABI.',\n        'Make sure you are using the correct ABI, and that the `inputs` attribute on the constructor exists.',\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n  }\n}\n\nexport type AbiDecodingDataSizeInvalidErrorType =\n  AbiDecodingDataSizeInvalidError & {\n    name: 'AbiDecodingDataSizeInvalidError'\n  }\nexport class AbiDecodingDataSizeInvalidError extends BaseError {\n  override name = 'AbiDecodingDataSizeInvalidError'\n  constructor({ data, size }: { data: Hex; size: number }) {\n    super(\n      [\n        `Data size of ${size} bytes is invalid.`,\n        'Size must be in increments of 32 bytes (size % 32 === 0).',\n      ].join('\\n'),\n      { metaMessages: [`Data: ${data} (${size} bytes)`] },\n    )\n  }\n}\n\nexport type AbiDecodingDataSizeTooSmallErrorType =\n  AbiDecodingDataSizeTooSmallError & {\n    name: 'AbiDecodingDataSizeTooSmallError'\n  }\nexport class AbiDecodingDataSizeTooSmallError extends BaseError {\n  override name = 'AbiDecodingDataSizeTooSmallError'\n\n  data: Hex\n  params: readonly AbiParameter[]\n  size: number\n\n  constructor({\n    data,\n    params,\n    size,\n  }: { data: Hex; params: readonly AbiParameter[]; size: number }) {\n    super(\n      [`Data size of ${size} bytes is too small for given parameters.`].join(\n        '\\n',\n      ),\n      {\n        metaMessages: [\n          `Params: (${formatAbiParams(params, { includeName: true })})`,\n          `Data:   ${data} (${size} bytes)`,\n        ],\n      },\n    )\n\n    this.data = data\n    this.params = params\n    this.size = size\n  }\n}\n\nexport type AbiDecodingZeroDataErrorType = AbiDecodingZeroDataError & {\n  name: 'AbiDecodingZeroDataError'\n}\nexport class AbiDecodingZeroDataError extends BaseError {\n  override name = 'AbiDecodingZeroDataError'\n  constructor() {\n    super('Cannot decode zero data (\"0x\") with ABI parameters.')\n  }\n}\n\nexport type AbiEncodingArrayLengthMismatchErrorType =\n  AbiEncodingArrayLengthMismatchError & {\n    name: 'AbiEncodingArrayLengthMismatchError'\n  }\nexport class AbiEncodingArrayLengthMismatchError extends BaseError {\n  override name = 'AbiEncodingArrayLengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n    type,\n  }: { expectedLength: number; givenLength: number; type: string }) {\n    super(\n      [\n        `ABI encoding array length mismatch for type ${type}.`,\n        `Expected length: ${expectedLength}`,\n        `Given length: ${givenLength}`,\n      ].join('\\n'),\n    )\n  }\n}\n\nexport type AbiEncodingBytesSizeMismatchErrorType =\n  AbiEncodingBytesSizeMismatchError & {\n    name: 'AbiEncodingBytesSizeMismatchError'\n  }\nexport class AbiEncodingBytesSizeMismatchError extends BaseError {\n  override name = 'AbiEncodingBytesSizeMismatchError'\n  constructor({ expectedSize, value }: { expectedSize: number; value: Hex }) {\n    super(\n      `Size of bytes \"${value}\" (bytes${size(\n        value,\n      )}) does not match expected size (bytes${expectedSize}).`,\n    )\n  }\n}\n\nexport type AbiEncodingLengthMismatchErrorType =\n  AbiEncodingLengthMismatchError & {\n    name: 'AbiEncodingLengthMismatchError'\n  }\nexport class AbiEncodingLengthMismatchError extends BaseError {\n  override name = 'AbiEncodingLengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n  }: { expectedLength: number; givenLength: number }) {\n    super(\n      [\n        'ABI encoding params/values length mismatch.',\n        `Expected length (params): ${expectedLength}`,\n        `Given length (values): ${givenLength}`,\n      ].join('\\n'),\n    )\n  }\n}\n\nexport type AbiErrorInputsNotFoundErrorType = AbiErrorInputsNotFoundError & {\n  name: 'AbiErrorInputsNotFoundError'\n}\nexport class AbiErrorInputsNotFoundError extends BaseError {\n  override name = 'AbiErrorInputsNotFoundError'\n  constructor(errorName: string, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Arguments (\\`args\\`) were provided to \"${errorName}\", but \"${errorName}\" on the ABI does not contain any parameters (\\`inputs\\`).`,\n        'Cannot encode error result without knowing what the parameter types are.',\n        'Make sure you are using the correct ABI and that the inputs exist on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n  }\n}\n\nexport type AbiErrorNotFoundErrorType = AbiErrorNotFoundError & {\n  name: 'AbiErrorNotFoundError'\n}\nexport class AbiErrorNotFoundError extends BaseError {\n  override name = 'AbiErrorNotFoundError'\n  constructor(errorName?: string, { docsPath }: { docsPath?: string } = {}) {\n    super(\n      [\n        `Error ${errorName ? `\"${errorName}\" ` : ''}not found on ABI.`,\n        'Make sure you are using the correct ABI and that the error exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n  }\n}\n\nexport type AbiErrorSignatureNotFoundErrorType =\n  AbiErrorSignatureNotFoundError & {\n    name: 'AbiErrorSignatureNotFoundError'\n  }\nexport class AbiErrorSignatureNotFoundError extends BaseError {\n  override name = 'AbiErrorSignatureNotFoundError'\n\n  signature: Hex\n\n  constructor(signature: Hex, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Encoded error signature \"${signature}\" not found on ABI.`,\n        'Make sure you are using the correct ABI and that the error exists on it.',\n        `You can look up the decoded signature here: https://openchain.xyz/signatures?query=${signature}.`,\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n    this.signature = signature\n  }\n}\n\nexport type AbiEventSignatureEmptyTopicsErrorType =\n  AbiEventSignatureEmptyTopicsError & {\n    name: 'AbiEventSignatureEmptyTopicsError'\n  }\nexport class AbiEventSignatureEmptyTopicsError extends BaseError {\n  override name = 'AbiEventSignatureEmptyTopicsError'\n  constructor({ docsPath }: { docsPath: string }) {\n    super('Cannot extract event signature from empty topics.', {\n      docsPath,\n    })\n  }\n}\n\nexport type AbiEventSignatureNotFoundErrorType =\n  AbiEventSignatureNotFoundError & {\n    name: 'AbiEventSignatureNotFoundError'\n  }\nexport class AbiEventSignatureNotFoundError extends BaseError {\n  override name = 'AbiEventSignatureNotFoundError'\n  constructor(signature: Hex, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Encoded event signature \"${signature}\" not found on ABI.`,\n        'Make sure you are using the correct ABI and that the event exists on it.',\n        `You can look up the signature here: https://openchain.xyz/signatures?query=${signature}.`,\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n  }\n}\n\nexport type AbiEventNotFoundErrorType = AbiEventNotFoundError & {\n  name: 'AbiEventNotFoundError'\n}\nexport class AbiEventNotFoundError extends BaseError {\n  override name = 'AbiEventNotFoundError'\n  constructor(eventName?: string, { docsPath }: { docsPath?: string } = {}) {\n    super(\n      [\n        `Event ${eventName ? `\"${eventName}\" ` : ''}not found on ABI.`,\n        'Make sure you are using the correct ABI and that the event exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n  }\n}\n\nexport type AbiFunctionNotFoundErrorType = AbiFunctionNotFoundError & {\n  name: 'AbiFunctionNotFoundError'\n}\nexport class AbiFunctionNotFoundError extends BaseError {\n  override name = 'AbiFunctionNotFoundError'\n  constructor(functionName?: string, { docsPath }: { docsPath?: string } = {}) {\n    super(\n      [\n        `Function ${functionName ? `\"${functionName}\" ` : ''}not found on ABI.`,\n        'Make sure you are using the correct ABI and that the function exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n  }\n}\n\nexport type AbiFunctionOutputsNotFoundErrorType =\n  AbiFunctionOutputsNotFoundError & {\n    name: 'AbiFunctionOutputsNotFoundError'\n  }\nexport class AbiFunctionOutputsNotFoundError extends BaseError {\n  override name = 'AbiFunctionOutputsNotFoundError'\n  constructor(functionName: string, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Function \"${functionName}\" does not contain any \\`outputs\\` on ABI.`,\n        'Cannot decode function result without knowing what the parameter types are.',\n        'Make sure you are using the correct ABI and that the function exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n  }\n}\n\nexport type AbiFunctionSignatureNotFoundErrorType =\n  AbiFunctionSignatureNotFoundError & {\n    name: 'AbiFunctionSignatureNotFoundError'\n  }\nexport class AbiFunctionSignatureNotFoundError extends BaseError {\n  override name = 'AbiFunctionSignatureNotFoundError'\n  constructor(signature: Hex, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Encoded function signature \"${signature}\" not found on ABI.`,\n        'Make sure you are using the correct ABI and that the function exists on it.',\n        `You can look up the signature here: https://openchain.xyz/signatures?query=${signature}.`,\n      ].join('\\n'),\n      {\n        docsPath,\n      },\n    )\n  }\n}\n\nexport type AbiItemAmbiguityErrorType = AbiItemAmbiguityError & {\n  name: 'AbiItemAmbiguityError'\n}\nexport class AbiItemAmbiguityError extends BaseError {\n  override name = 'AbiItemAmbiguityError'\n  constructor(\n    x: { abiItem: Abi[number]; type: string },\n    y: { abiItem: Abi[number]; type: string },\n  ) {\n    super('Found ambiguous types in overloaded ABI items.', {\n      metaMessages: [\n        `\\`${x.type}\\` in \\`${formatAbiItem(x.abiItem)}\\`, and`,\n        `\\`${y.type}\\` in \\`${formatAbiItem(y.abiItem)}\\``,\n        '',\n        'These types encode differently and cannot be distinguished at runtime.',\n        'Remove one of the ambiguous items in the ABI.',\n      ],\n    })\n  }\n}\n\nexport type BytesSizeMismatchErrorType = BytesSizeMismatchError & {\n  name: 'BytesSizeMismatchError'\n}\nexport class BytesSizeMismatchError extends BaseError {\n  override name = 'BytesSizeMismatchError'\n  constructor({\n    expectedSize,\n    givenSize,\n  }: { expectedSize: number; givenSize: number }) {\n    super(`Expected bytes${expectedSize}, got bytes${givenSize}.`)\n  }\n}\n\nexport type DecodeLogDataMismatchErrorType = DecodeLogDataMismatch & {\n  name: 'DecodeLogDataMismatch'\n}\nexport class DecodeLogDataMismatch extends BaseError {\n  override name = 'DecodeLogDataMismatch'\n\n  abiItem: AbiEvent\n  data: Hex\n  params: readonly AbiParameter[]\n  size: number\n\n  constructor({\n    abiItem,\n    data,\n    params,\n    size,\n  }: {\n    abiItem: AbiEvent\n    data: Hex\n    params: readonly AbiParameter[]\n    size: number\n  }) {\n    super(\n      [\n        `Data size of ${size} bytes is too small for non-indexed event parameters.`,\n      ].join('\\n'),\n      {\n        metaMessages: [\n          `Params: (${formatAbiParams(params, { includeName: true })})`,\n          `Data:   ${data} (${size} bytes)`,\n        ],\n      },\n    )\n\n    this.abiItem = abiItem\n    this.data = data\n    this.params = params\n    this.size = size\n  }\n}\n\nexport type DecodeLogTopicsMismatchErrorType = DecodeLogTopicsMismatch & {\n  name: 'DecodeLogTopicsMismatch'\n}\nexport class DecodeLogTopicsMismatch extends BaseError {\n  override name = 'DecodeLogTopicsMismatch'\n\n  abiItem: AbiEvent\n\n  constructor({\n    abiItem,\n    param,\n  }: {\n    abiItem: AbiEvent\n    param: AbiParameter & { indexed: boolean }\n  }) {\n    super(\n      [\n        `Expected a topic for indexed event parameter${\n          param.name ? ` \"${param.name}\"` : ''\n        } on event \"${formatAbiItem(abiItem, { includeName: true })}\".`,\n      ].join('\\n'),\n    )\n\n    this.abiItem = abiItem\n  }\n}\n\nexport type InvalidAbiEncodingTypeErrorType = InvalidAbiEncodingTypeError & {\n  name: 'InvalidAbiEncodingTypeError'\n}\nexport class InvalidAbiEncodingTypeError extends BaseError {\n  override name = 'InvalidAbiEncodingType'\n  constructor(type: string, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Type \"${type}\" is not a valid encoding type.`,\n        'Please provide a valid ABI type.',\n      ].join('\\n'),\n      { docsPath },\n    )\n  }\n}\n\nexport type InvalidAbiDecodingTypeErrorType = InvalidAbiDecodingTypeError & {\n  name: 'InvalidAbiDecodingTypeError'\n}\nexport class InvalidAbiDecodingTypeError extends BaseError {\n  override name = 'InvalidAbiDecodingType'\n  constructor(type: string, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Type \"${type}\" is not a valid decoding type.`,\n        'Please provide a valid ABI type.',\n      ].join('\\n'),\n      { docsPath },\n    )\n  }\n}\n\nexport type InvalidArrayErrorType = InvalidArrayError & {\n  name: 'InvalidArrayError'\n}\nexport class InvalidArrayError extends BaseError {\n  override name = 'InvalidArrayError'\n  constructor(value: unknown) {\n    super([`Value \"${value}\" is not a valid array.`].join('\\n'))\n  }\n}\n\nexport type InvalidDefinitionTypeErrorType = InvalidDefinitionTypeError & {\n  name: 'InvalidDefinitionTypeError'\n}\nexport class InvalidDefinitionTypeError extends BaseError {\n  override name = 'InvalidDefinitionTypeError'\n  constructor(type: string) {\n    super(\n      [\n        `\"${type}\" is not a valid definition type.`,\n        'Valid types: \"function\", \"event\", \"error\"',\n      ].join('\\n'),\n    )\n  }\n}\n\nexport type UnsupportedPackedAbiTypeErrorType = UnsupportedPackedAbiType & {\n  name: 'UnsupportedPackedAbiType'\n}\nexport class UnsupportedPackedAbiType extends BaseError {\n  override name = 'UnsupportedPackedAbiType'\n  constructor(type: unknown) {\n    super(`Type \"${type}\" is not supported for packed encoding.`)\n  }\n}\n", "import { BaseError } from '../../errors/base.js'\nimport type { ErrorType } from '../../errors/utils.js'\n\nexport type NormalizeSignatureParameters = string\nexport type NormalizeSignatureReturnType = string\nexport type NormalizeSignatureErrorType = ErrorType\n\nexport function normalizeSignature(\n  signature: NormalizeSignatureParameters,\n): NormalizeSignatureReturnType {\n  let active = true\n  let current = ''\n  let level = 0\n  let result = ''\n  let valid = false\n\n  for (let i = 0; i < signature.length; i++) {\n    const char = signature[i]\n\n    // If the character is a separator, we want to reactivate.\n    if (['(', ')', ','].includes(char)) active = true\n\n    // If the character is a \"level\" token, we want to increment/decrement.\n    if (char === '(') level++\n    if (char === ')') level--\n\n    // If we aren't active, we don't want to mutate the result.\n    if (!active) continue\n\n    // If level === 0, we are at the definition level.\n    if (level === 0) {\n      if (char === ' ' && ['event', 'function', ''].includes(result))\n        result = ''\n      else {\n        result += char\n\n        // If we are at the end of the definition, we must be finished.\n        if (char === ')') {\n          valid = true\n          break\n        }\n      }\n\n      continue\n    }\n\n    // Ignore spaces\n    if (char === ' ') {\n      // If the previous character is a separator, and the current section isn't empty, we want to deactivate.\n      if (signature[i - 1] !== ',' && current !== ',' && current !== ',(') {\n        current = ''\n        active = false\n      }\n      continue\n    }\n\n    result += char\n    current += char\n  }\n\n  if (!valid) throw new BaseError('Unable to normalize signature.')\n\n  return result\n}\n", "import { type AbiFunction, formatAbiItem } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type NormalizeSignatureErrorType,\n  normalizeSignature,\n} from './normalizeSignature.js'\n\nexport type GetFunctionSignatureErrorType =\n  | NormalizeSignatureErrorType\n  | ErrorType\n\nexport const getFunctionSignature = (fn_: string | AbiFunction) => {\n  const fn = (() => {\n    if (typeof fn_ === 'string') return fn_\n    return formatAbiItem(fn_)\n  })()\n  return normalizeSignature(fn)\n}\n", "import type { AbiEvent, AbiFunction } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type GetFunctionSignatureErrorType,\n  getFunctionSignature,\n} from './getFunctionSignature.js'\n\nexport type GetEventSignatureErrorType =\n  | GetFunctionSignatureErrorType\n  | ErrorType\n\nexport const getEventSignature = (fn: string | AbiEvent) => {\n  return getFunctionSignature(fn as {} as AbiFunction)\n}\n", "const U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n\n// We are not using BigUint64Array, because they are extremely slow as per 2022\nfunction fromBig(n: bigint, le = false) {\n  if (le) return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n  return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\n\nfunction split(lst: bigint[], le = false) {\n  let Ah = new Uint32Array(lst.length);\n  let Al = new Uint32Array(lst.length);\n  for (let i = 0; i < lst.length; i++) {\n    const { h, l } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\n\nconst toBig = (h: number, l: number) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h: number, _l: number, s: number) => h >>> s;\nconst shrSL = (h: number, l: number, s: number) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h: number, l: number, s: number) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h: number, l: number, s: number) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h: number, l: number, s: number) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h: number, l: number, s: number) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h: number, l: number) => l;\nconst rotr32L = (h: number, _l: number) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h: number, l: number, s: number) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h: number, l: number, s: number) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h: number, l: number, s: number) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h: number, l: number, s: number) => (h << (s - 32)) | (l >>> (64 - s));\n\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah: number, Al: number, Bh: number, Bl: number) {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al: number, Bl: number, Cl: number) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low: number, Ah: number, Bh: number, Ch: number) =>\n  (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al: number, Bl: number, Cl: number, Dl: number) =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number) =>\n  (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al: number, Bl: number, Cl: number, Dl: number, El: number) =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number) =>\n  (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n\n// prettier-ignore\nexport {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n// prettier-ignore\nconst u64 = {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n", "import { bytes, exists, number, output } from './_assert.js';\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.js';\nimport {\n  Hash,\n  u32,\n  Input,\n  toBytes,\n  wrapConstructor,\n  wrapXOFConstructorWithOpts,\n  HashXOF,\n} from './utils.js';\n\n// SHA3 (keccak) is based on a new design: basically, the internal state is bigger than output size.\n// It's called a sponge function.\n\n// Various per round constants calculations\nconst [SHA3_PI, SHA3_ROTL, _SHA3_IOTA]: [number[], number[], bigint[]] = [[], [], []];\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nconst _7n = /* @__PURE__ */ BigInt(7);\nconst _256n = /* @__PURE__ */ BigInt(256);\nconst _0x71n = /* @__PURE__ */ BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n    if (R & _2n) t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n  }\n  _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ split(_SHA3_IOTA, true);\n\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h: number, l: number, s: number) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h: number, l: number, s: number) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n\n// Same as keccakf1600, but allows to skip some rounds\nexport function keccakP(s: Uint32Array, rounds: number = 24) {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  B.fill(0);\n}\n\nexport class Keccak extends Hash<Keccak> implements HashXOF<Keccak> {\n  protected state: Uint8Array;\n  protected pos = 0;\n  protected posOut = 0;\n  protected finished = false;\n  protected state32: Uint32Array;\n  protected destroyed = false;\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(\n    public blockLen: number,\n    public suffix: number,\n    public outputLen: number,\n    protected enableXOF = false,\n    protected rounds: number = 24\n  ) {\n    super();\n    // Can be passed from user as dkLen\n    number(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    if (0 >= this.blockLen || this.blockLen >= 200)\n      throw new Error('Sha3 supports only keccak-f1600 function');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n  protected keccak() {\n    keccakP(this.state32, this.rounds);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data: Input) {\n    exists(this);\n    const { blockLen, state } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  protected finish() {\n    if (this.finished) return;\n    this.finished = true;\n    const { state, suffix, pos, blockLen } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  protected writeInto(out: Uint8Array): Uint8Array {\n    exists(this, false);\n    bytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const { blockLen } = this;\n    for (let pos = 0, len = out.length; pos < len; ) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out: Uint8Array): Uint8Array {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes: number): Uint8Array {\n    number(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out: Uint8Array) {\n    output(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest() {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy() {\n    this.destroyed = true;\n    this.state.fill(0);\n  }\n  _cloneInto(to?: Keccak): Keccak {\n    const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n    to ||= new Keccak(blockLen, suffix, outputLen, enableXOF, rounds);\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n}\n\nconst gen = (suffix: number, blockLen: number, outputLen: number) =>\n  wrapConstructor(() => new Keccak(blockLen, suffix, outputLen));\n\nexport const sha3_224 = /* @__PURE__ */ gen(0x06, 144, 224 / 8);\n/**\n * SHA3-256 hash function\n * @param message - that would be hashed\n */\nexport const sha3_256 = /* @__PURE__ */ gen(0x06, 136, 256 / 8);\nexport const sha3_384 = /* @__PURE__ */ gen(0x06, 104, 384 / 8);\nexport const sha3_512 = /* @__PURE__ */ gen(0x06, 72, 512 / 8);\nexport const keccak_224 = /* @__PURE__ */ gen(0x01, 144, 224 / 8);\n/**\n * keccak-256 hash function. Different from SHA3-256.\n * @param message - that would be hashed\n */\nexport const keccak_256 = /* @__PURE__ */ gen(0x01, 136, 256 / 8);\nexport const keccak_384 = /* @__PURE__ */ gen(0x01, 104, 384 / 8);\nexport const keccak_512 = /* @__PURE__ */ gen(0x01, 72, 512 / 8);\n\nexport type ShakeOpts = { dkLen?: number };\n\nconst genShake = (suffix: number, blockLen: number, outputLen: number) =>\n  wrapXOFConstructorWithOpts<HashXOF<Keccak>, ShakeOpts>(\n    (opts: ShakeOpts = {}) =>\n      new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true)\n  );\n\nexport const shake128 = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);\nexport const shake256 = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8);\n", "import { keccak_256 } from '@noble/hashes/sha3'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type IsHexErrorType, isHex } from '../data/isHex.js'\nimport { type ToBytesErrorType, toBytes } from '../encoding/toBytes.js'\nimport { type ToHexErrorType, toHex } from '../encoding/toHex.js'\n\ntype To = 'hex' | 'bytes'\n\nexport type Keccak256Hash<TTo extends To> =\n  | (TTo extends 'bytes' ? ByteArray : never)\n  | (TTo extends 'hex' ? Hex : never)\n\nexport type Keccak256ErrorType =\n  | IsHexErrorType\n  | ToBytesErrorType\n  | ToHexErrorType\n  | ErrorType\n\nexport function keccak256<TTo extends To = 'hex'>(\n  value: Hex | ByteArray,\n  to_?: TTo,\n): Keccak256Hash<TTo> {\n  const to = to_ || 'hex'\n  const bytes = keccak_256(\n    isHex(value, { strict: false }) ? toBytes(value) : value,\n  )\n  if (to === 'bytes') return bytes as Keccak256Hash<TTo>\n  return toHex(bytes) as Keccak256Hash<TTo>\n}\n", "import type { AbiEvent } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport { type ToBytesErrorType, toBytes } from '../encoding/toBytes.js'\nimport { getEventSignature } from './getEventSignature.js'\nimport { type Keccak256ErrorType, keccak256 } from './keccak256.js'\n\nconst hash = (value: string) => keccak256(toBytes(value))\n\nexport type GetEventSelectorErrorType =\n  | Keccak256ErrorType\n  | ToBytesErrorType\n  | ErrorType\n\nexport const getEventSelector = (fn: string | AbiEvent) =>\n  hash(getEventSignature(fn))\n", "import {\n  SliceOffsetOutOfBoundsError,\n  type SliceOffsetOutOfBoundsErrorType,\n} from '../../errors/data.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\nimport { type IsHexErrorType, isHex } from './isHex.js'\nimport { type SizeErrorType, size } from './size.js'\n\nexport type SliceReturnType<TValue extends ByteArray | Hex> = TValue extends Hex\n  ? Hex\n  : ByteArray\n\nexport type SliceErrorType =\n  | IsHexErrorType\n  | SliceBytesErrorType\n  | SliceHexErrorType\n  | ErrorType\n\n/**\n * @description Returns a section of the hex or byte array given a start/end bytes offset.\n *\n * @param value The hex or byte array to slice.\n * @param start The start offset (in bytes).\n * @param end The end offset (in bytes).\n */\nexport function slice<TValue extends ByteArray | Hex>(\n  value: TValue,\n  start?: number,\n  end?: number,\n  { strict }: { strict?: boolean } = {},\n): SliceReturnType<TValue> {\n  if (isHex(value, { strict: false }))\n    return sliceHex(value as Hex, start, end, {\n      strict,\n    }) as SliceReturnType<TValue>\n  return sliceBytes(value as ByteArray, start, end, {\n    strict,\n  }) as SliceReturnType<TValue>\n}\n\nexport type AssertStartOffsetErrorType =\n  | SliceOffsetOutOfBoundsErrorType\n  | SizeErrorType\n  | ErrorType\n\nfunction assertStartOffset(value: Hex | ByteArray, start?: number) {\n  if (typeof start === 'number' && start > 0 && start > size(value) - 1)\n    throw new SliceOffsetOutOfBoundsError({\n      offset: start,\n      position: 'start',\n      size: size(value),\n    })\n}\n\nexport type AssertEndOffsetErrorType =\n  | SliceOffsetOutOfBoundsErrorType\n  | SizeErrorType\n  | ErrorType\n\nfunction assertEndOffset(value: Hex | ByteArray, start?: number, end?: number) {\n  if (\n    typeof start === 'number' &&\n    typeof end === 'number' &&\n    size(value) !== end - start\n  ) {\n    throw new SliceOffsetOutOfBoundsError({\n      offset: end,\n      position: 'end',\n      size: size(value),\n    })\n  }\n}\n\nexport type SliceBytesErrorType =\n  | AssertStartOffsetErrorType\n  | AssertEndOffsetErrorType\n  | ErrorType\n\n/**\n * @description Returns a section of the byte array given a start/end bytes offset.\n *\n * @param value The byte array to slice.\n * @param start The start offset (in bytes).\n * @param end The end offset (in bytes).\n */\nexport function sliceBytes(\n  value_: ByteArray,\n  start?: number,\n  end?: number,\n  { strict }: { strict?: boolean } = {},\n): ByteArray {\n  assertStartOffset(value_, start)\n  const value = value_.slice(start, end)\n  if (strict) assertEndOffset(value, start, end)\n  return value\n}\n\nexport type SliceHexErrorType =\n  | AssertStartOffsetErrorType\n  | AssertEndOffsetErrorType\n  | ErrorType\n\n/**\n * @description Returns a section of the hex value given a start/end bytes offset.\n *\n * @param value The hex value to slice.\n * @param start The start offset (in bytes).\n * @param end The end offset (in bytes).\n */\nexport function sliceHex(\n  value_: Hex,\n  start?: number,\n  end?: number,\n  { strict }: { strict?: boolean } = {},\n): Hex {\n  assertStartOffset(value_, start)\n  const value = `0x${value_\n    .replace('0x', '')\n    .slice((start ?? 0) * 2, (end ?? value_.length) * 2)}` as const\n  if (strict) assertEndOffset(value, start, end)\n  return value\n}\n", "import type {\n  Abi<PERSON>arameter,\n  AbiParameterToPrimitiveType,\n  AbiParametersToPrimitiveTypes,\n} from 'abitype'\n\nimport {\n  AbiEncodingArrayLengthMismatchError,\n  type AbiEncodingArrayLengthMismatchErrorType,\n  AbiEncodingBytesSizeMismatchError,\n  type AbiEncodingBytesSizeMismatchErrorType,\n  AbiEncodingLengthMismatchError,\n  type AbiEncodingLengthMismatchErrorType,\n  InvalidAbiEncodingTypeError,\n  type InvalidAbiEncodingTypeErrorType,\n  InvalidArrayError,\n  type InvalidArrayErrorType,\n} from '../../errors/abi.js'\nimport {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../../errors/address.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport { type IsAddressErrorType, isAddress } from '../address/isAddress.js'\nimport { type ConcatErrorType, concat } from '../data/concat.js'\nimport { type PadHexErrorType, padHex } from '../data/pad.js'\nimport { type SizeErrorType, size } from '../data/size.js'\nimport { type SliceErrorType, slice } from '../data/slice.js'\nimport {\n  type BoolToHexErrorType,\n  type NumberToHexErrorType,\n  type StringToHexErrorType,\n  boolToHex,\n  numberToHex,\n  stringToHex,\n} from '../encoding/toHex.js'\n\nexport type EncodeAbiParametersReturnType = Hex\n\nexport type EncodeAbiParametersErrorType =\n  | AbiEncodingLengthMismatchErrorType\n  | PrepareParamsErrorType\n  | EncodeParamsErrorType\n  | ErrorType\n\n/**\n * @description Encodes a list of primitive values into an ABI-encoded hex value.\n */\nexport function encodeAbiParameters<\n  const TParams extends readonly AbiParameter[] | readonly unknown[],\n>(\n  params: TParams,\n  values: TParams extends readonly AbiParameter[]\n    ? AbiParametersToPrimitiveTypes<TParams>\n    : never,\n): EncodeAbiParametersReturnType {\n  if (params.length !== values.length)\n    throw new AbiEncodingLengthMismatchError({\n      expectedLength: params.length as number,\n      givenLength: values.length,\n    })\n  // Prepare the parameters to determine dynamic types to encode.\n  const preparedParams = prepareParams({\n    params: params as readonly AbiParameter[],\n    values,\n  })\n  const data = encodeParams(preparedParams)\n  if (data.length === 0) return '0x'\n  return data\n}\n\n/////////////////////////////////////////////////////////////////\n\ntype PreparedParam = { dynamic: boolean; encoded: Hex }\n\ntype TupleAbiParameter = AbiParameter & { components: readonly AbiParameter[] }\ntype Tuple = AbiParameterToPrimitiveType<TupleAbiParameter>\n\nexport type PrepareParamsErrorType = ErrorType\n\nfunction prepareParams<const TParams extends readonly AbiParameter[]>({\n  params,\n  values,\n}: {\n  params: TParams\n  values: AbiParametersToPrimitiveTypes<TParams>\n}) {\n  const preparedParams: PreparedParam[] = []\n  for (let i = 0; i < params.length; i++) {\n    preparedParams.push(prepareParam({ param: params[i], value: values[i] }))\n  }\n  return preparedParams\n}\n\nexport type PrepareParamErrorType =\n  | GetArrayComponentsErrorType\n  | InvalidAbiEncodingTypeErrorType\n  | ErrorType\n\nfunction prepareParam<const TParam extends AbiParameter>({\n  param,\n  value,\n}: {\n  param: TParam\n  value: AbiParameterToPrimitiveType<TParam>\n}): PreparedParam {\n  const arrayComponents = getArrayComponents(param.type)\n  if (arrayComponents) {\n    const [length, type] = arrayComponents\n    return encodeArray(value, { length, param: { ...param, type } })\n  }\n  if (param.type === 'tuple') {\n    return encodeTuple(value as unknown as Tuple, {\n      param: param as TupleAbiParameter,\n    })\n  }\n  if (param.type === 'address') {\n    return encodeAddress(value as unknown as Hex)\n  }\n  if (param.type === 'bool') {\n    return encodeBool(value as unknown as boolean)\n  }\n  if (param.type.startsWith('uint') || param.type.startsWith('int')) {\n    const signed = param.type.startsWith('int')\n    return encodeNumber(value as unknown as number, { signed })\n  }\n  if (param.type.startsWith('bytes')) {\n    return encodeBytes(value as unknown as Hex, { param })\n  }\n  if (param.type === 'string') {\n    return encodeString(value as unknown as string)\n  }\n  throw new InvalidAbiEncodingTypeError(param.type, {\n    docsPath: '/docs/contract/encodeAbiParameters',\n  })\n}\n\n/////////////////////////////////////////////////////////////////\n\nexport type EncodeParamsErrorType =\n  | NumberToHexErrorType\n  | SizeErrorType\n  | ErrorType\n\nfunction encodeParams(preparedParams: PreparedParam[]): Hex {\n  // 1. Compute the size of the static part of the parameters.\n  let staticSize = 0\n  for (let i = 0; i < preparedParams.length; i++) {\n    const { dynamic, encoded } = preparedParams[i]\n    if (dynamic) staticSize += 32\n    else staticSize += size(encoded)\n  }\n\n  // 2. Split the parameters into static and dynamic parts.\n  const staticParams: Hex[] = []\n  const dynamicParams: Hex[] = []\n  let dynamicSize = 0\n  for (let i = 0; i < preparedParams.length; i++) {\n    const { dynamic, encoded } = preparedParams[i]\n    if (dynamic) {\n      staticParams.push(numberToHex(staticSize + dynamicSize, { size: 32 }))\n      dynamicParams.push(encoded)\n      dynamicSize += size(encoded)\n    } else {\n      staticParams.push(encoded)\n    }\n  }\n\n  // 3. Concatenate static and dynamic parts.\n  return concat([...staticParams, ...dynamicParams])\n}\n\n/////////////////////////////////////////////////////////////////\n\nexport type EncodeAddressErrorType =\n  | InvalidAddressErrorType\n  | IsAddressErrorType\n  | ErrorType\n\nfunction encodeAddress(value: Hex): PreparedParam {\n  if (!isAddress(value)) throw new InvalidAddressError({ address: value })\n  return { dynamic: false, encoded: padHex(value.toLowerCase() as Hex) }\n}\n\nexport type EncodeArrayErrorType =\n  | AbiEncodingArrayLengthMismatchErrorType\n  | ConcatErrorType\n  | EncodeParamsErrorType\n  | InvalidArrayErrorType\n  | NumberToHexErrorType\n  | PrepareParamErrorType\n  | ErrorType\n\nfunction encodeArray<const TParam extends AbiParameter>(\n  value: AbiParameterToPrimitiveType<TParam>,\n  {\n    length,\n    param,\n  }: {\n    length: number | null\n    param: TParam\n  },\n): PreparedParam {\n  const dynamic = length === null\n\n  if (!Array.isArray(value)) throw new InvalidArrayError(value)\n  if (!dynamic && value.length !== length)\n    throw new AbiEncodingArrayLengthMismatchError({\n      expectedLength: length!,\n      givenLength: value.length,\n      type: `${param.type}[${length}]`,\n    })\n\n  let dynamicChild = false\n  const preparedParams: PreparedParam[] = []\n  for (let i = 0; i < value.length; i++) {\n    const preparedParam = prepareParam({ param, value: value[i] })\n    if (preparedParam.dynamic) dynamicChild = true\n    preparedParams.push(preparedParam)\n  }\n\n  if (dynamic || dynamicChild) {\n    const data = encodeParams(preparedParams)\n    if (dynamic) {\n      const length = numberToHex(preparedParams.length, { size: 32 })\n      return {\n        dynamic: true,\n        encoded: preparedParams.length > 0 ? concat([length, data]) : length,\n      }\n    }\n    if (dynamicChild) return { dynamic: true, encoded: data }\n  }\n  return {\n    dynamic: false,\n    encoded: concat(preparedParams.map(({ encoded }) => encoded)),\n  }\n}\n\nexport type EncodeBytesErrorType =\n  | AbiEncodingBytesSizeMismatchErrorType\n  | ConcatErrorType\n  | PadHexErrorType\n  | NumberToHexErrorType\n  | SizeErrorType\n  | ErrorType\n\nfunction encodeBytes<const TParam extends AbiParameter>(\n  value: Hex,\n  { param }: { param: TParam },\n): PreparedParam {\n  const [, paramSize] = param.type.split('bytes')\n  const bytesSize = size(value)\n  if (!paramSize) {\n    let value_ = value\n    // If the size is not divisible by 32 bytes, pad the end\n    // with empty bytes to the ceiling 32 bytes.\n    if (bytesSize % 32 !== 0)\n      value_ = padHex(value_, {\n        dir: 'right',\n        size: Math.ceil((value.length - 2) / 2 / 32) * 32,\n      })\n    return {\n      dynamic: true,\n      encoded: concat([padHex(numberToHex(bytesSize, { size: 32 })), value_]),\n    }\n  }\n  if (bytesSize !== parseInt(paramSize))\n    throw new AbiEncodingBytesSizeMismatchError({\n      expectedSize: parseInt(paramSize),\n      value,\n    })\n  return { dynamic: false, encoded: padHex(value, { dir: 'right' }) }\n}\n\nexport type EncodeBoolErrorType =\n  | PadHexErrorType\n  | BoolToHexErrorType\n  | ErrorType\n\nfunction encodeBool(value: boolean): PreparedParam {\n  return { dynamic: false, encoded: padHex(boolToHex(value)) }\n}\n\nexport type EncodeNumberErrorType = NumberToHexErrorType | ErrorType\n\nfunction encodeNumber(\n  value: number,\n  { signed }: { signed: boolean },\n): PreparedParam {\n  return {\n    dynamic: false,\n    encoded: numberToHex(value, {\n      size: 32,\n      signed,\n    }),\n  }\n}\n\nexport type EncodeStringErrorType =\n  | ConcatErrorType\n  | NumberToHexErrorType\n  | PadHexErrorType\n  | SizeErrorType\n  | SliceErrorType\n  | StringToHexErrorType\n  | ErrorType\n\nfunction encodeString(value: string): PreparedParam {\n  const hexValue = stringToHex(value)\n  const partsLength = Math.ceil(size(hexValue) / 32)\n  const parts: Hex[] = []\n  for (let i = 0; i < partsLength; i++) {\n    parts.push(\n      padHex(slice(hexValue, i * 32, (i + 1) * 32), {\n        dir: 'right',\n      }),\n    )\n  }\n  return {\n    dynamic: true,\n    encoded: concat([\n      padHex(numberToHex(size(hexValue), { size: 32 })),\n      ...parts,\n    ]),\n  }\n}\n\nexport type EncodeTupleErrorType =\n  | ConcatErrorType\n  | EncodeParamsErrorType\n  | PrepareParamErrorType\n  | ErrorType\n\nfunction encodeTuple<\n  const TParam extends AbiParameter & { components: readonly AbiParameter[] },\n>(\n  value: AbiParameterToPrimitiveType<TParam>,\n  { param }: { param: TParam },\n): PreparedParam {\n  let dynamic = false\n  const preparedParams: PreparedParam[] = []\n  for (let i = 0; i < param.components.length; i++) {\n    const param_ = param.components[i]\n    const index = Array.isArray(value) ? i : param_.name\n    const preparedParam = prepareParam({\n      param: param_,\n      value: (value as any)[index!] as readonly unknown[],\n    })\n    preparedParams.push(preparedParam)\n    if (preparedParam.dynamic) dynamic = true\n  }\n  return {\n    dynamic,\n    encoded: dynamic\n      ? encodeParams(preparedParams)\n      : concat(preparedParams.map(({ encoded }) => encoded)),\n  }\n}\n\nexport type GetArrayComponentsErrorType = ErrorType\n\nexport function getArrayComponents(\n  type: string,\n): [length: number | null, innerType: string] | undefined {\n  const matches = type.match(/^(.*)\\[(\\d+)?\\]$/)\n  return matches\n    ? // Return `null` if the array is dynamic.\n      [matches[2] ? Number(matches[2]) : null, matches[1]]\n    : undefined\n}\n", "import type { AbiFunction } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport { type SliceErrorType, slice } from '../data/slice.js'\nimport { type ToBytesErrorType, toBytes } from '../encoding/toBytes.js'\nimport {\n  type GetFunctionSignatureErrorType,\n  getFunctionSignature,\n} from './getFunctionSignature.js'\nimport { type Keccak256ErrorType, keccak256 } from './keccak256.js'\n\nconst hash = (value: string) => keccak256(toBytes(value))\n\nexport type GetFunctionSelectorErrorType =\n  | GetFunctionSignatureErrorType\n  | Keccak256ErrorType\n  | SliceErrorType\n  | ToBytesErrorType\n  | ErrorType\n\nexport const getFunctionSelector = (fn: string | AbiFunction) =>\n  slice(hash(getFunctionSignature(fn)), 0, 4)\n", "import { type Abi, type AbiParameter, type Address } from 'abitype'\n\nimport { AbiItemAmbiguityError } from '../../errors/abi.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type {\n  AbiItem,\n  GetFunctionArgs,\n  InferItemName,\n} from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\nimport { type IsHexErrorType, isHex } from '../../utils/data/isHex.js'\nimport { getEventSelector } from '../../utils/hash/getEventSelector.js'\nimport {\n  type GetFunctionSelectorErrorType,\n  getFunctionSelector,\n} from '../../utils/hash/getFunctionSelector.js'\nimport { type IsAddressErrorType, isAddress } from '../address/isAddress.js'\n\nexport type GetAbiItemParameters<\n  TAbi extends Abi | readonly unknown[] = Abi,\n  TItemName extends string = string,\n> = {\n  abi: TAbi\n  name: InferItemName<TAbi, TItemName> | Hex\n} & Partial<GetFunctionArgs<TAbi, TItemName>>\n\nexport type GetAbiItemReturnType<\n  TAbi extends Abi | readonly unknown[] = Abi,\n  TItemName extends string = string,\n> = Extract<\n  TAbi[number],\n  {\n    name: TItemName\n  }\n>\n\nexport type GetAbiItemErrorType =\n  | IsArgOfTypeErrorType\n  | IsHexErrorType\n  | GetFunctionSelectorErrorType\n  | ErrorType\n\nexport function getAbiItem<\n  const TAbi extends Abi | readonly unknown[],\n  TItemName extends string,\n>({\n  abi,\n  args = [],\n  name,\n}: GetAbiItemParameters<TAbi, TItemName>): GetAbiItemReturnType<\n  TAbi,\n  TItemName\n> {\n  const isSelector = isHex(name, { strict: false })\n\n  const abiItems = (abi as Abi).filter((abiItem) => {\n    if (isSelector) {\n      if (abiItem.type === 'function')\n        return getFunctionSelector(abiItem) === name\n      if (abiItem.type === 'event') return getEventSelector(abiItem) === name\n      return false\n    }\n    return 'name' in abiItem && abiItem.name === name\n  })\n\n  if (abiItems.length === 0) return undefined as any\n  if (abiItems.length === 1) return abiItems[0] as any\n\n  let matchedAbiItem: AbiItem | undefined = undefined\n  for (const abiItem of abiItems) {\n    if (!('inputs' in abiItem)) continue\n    if (!args || args.length === 0) {\n      if (!abiItem.inputs || abiItem.inputs.length === 0) return abiItem as any\n      continue\n    }\n    if (!abiItem.inputs) continue\n    if (abiItem.inputs.length === 0) continue\n    if (abiItem.inputs.length !== args.length) continue\n    const matched = (args as readonly unknown[]).every((arg, index) => {\n      const abiParameter = 'inputs' in abiItem && abiItem.inputs![index]\n      if (!abiParameter) return false\n      return isArgOfType(arg, abiParameter as AbiParameter)\n    })\n    if (matched) {\n      // Check for ambiguity against already matched parameters (e.g. `address` vs `bytes20`).\n      if (\n        matchedAbiItem &&\n        'inputs' in matchedAbiItem &&\n        matchedAbiItem.inputs\n      ) {\n        const ambiguousTypes = getAmbiguousTypes(\n          abiItem.inputs,\n          matchedAbiItem.inputs,\n          args as readonly unknown[],\n        )\n        if (ambiguousTypes)\n          throw new AbiItemAmbiguityError(\n            {\n              abiItem,\n              type: ambiguousTypes[0],\n            },\n            {\n              abiItem: matchedAbiItem,\n              type: ambiguousTypes[1],\n            },\n          )\n      }\n\n      matchedAbiItem = abiItem\n    }\n  }\n\n  if (matchedAbiItem)\n    return matchedAbiItem as GetAbiItemReturnType<TAbi, TItemName>\n  return abiItems[0] as GetAbiItemReturnType<TAbi, TItemName>\n}\n\nexport type IsArgOfTypeErrorType = IsAddressErrorType | ErrorType\n\nexport function isArgOfType(arg: unknown, abiParameter: AbiParameter): boolean {\n  const argType = typeof arg\n  const abiParameterType = abiParameter.type\n  switch (abiParameterType) {\n    case 'address':\n      return isAddress(arg as Address)\n    case 'bool':\n      return argType === 'boolean'\n    case 'function':\n      return argType === 'string'\n    case 'string':\n      return argType === 'string'\n    default: {\n      if (abiParameterType === 'tuple' && 'components' in abiParameter)\n        return Object.values(abiParameter.components).every(\n          (component, index) => {\n            return isArgOfType(\n              Object.values(arg as unknown[] | Record<string, unknown>)[index],\n              component as AbiParameter,\n            )\n          },\n        )\n\n      // `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n      // https://regexr.com/6v8hp\n      if (\n        /^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/.test(\n          abiParameterType,\n        )\n      )\n        return argType === 'number' || argType === 'bigint'\n\n      // `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n      // https://regexr.com/6va55\n      if (/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/.test(abiParameterType))\n        return argType === 'string' || arg instanceof Uint8Array\n\n      // fixed-length (`<type>[M]`) and dynamic (`<type>[]`) arrays\n      // https://regexr.com/6va6i\n      if (/[a-z]+[1-9]{0,3}(\\[[0-9]{0,}\\])+$/.test(abiParameterType)) {\n        return (\n          Array.isArray(arg) &&\n          arg.every((x: unknown) =>\n            isArgOfType(x, {\n              ...abiParameter,\n              // Pop off `[]` or `[M]` from end of type\n              type: abiParameterType.replace(/(\\[[0-9]{0,}\\])$/, ''),\n            } as AbiParameter),\n          )\n        )\n      }\n\n      return false\n    }\n  }\n}\n\nexport function getAmbiguousTypes(\n  sourceParameters: readonly AbiParameter[],\n  targetParameters: readonly AbiParameter[],\n  args: readonly unknown[],\n): AbiParameter['type'][] | undefined {\n  for (const parameterIndex in sourceParameters) {\n    const sourceParameter = sourceParameters[parameterIndex]\n    const targetParameter = targetParameters[parameterIndex]\n\n    if (\n      sourceParameter.type === 'tuple' &&\n      targetParameter.type === 'tuple' &&\n      'components' in sourceParameter &&\n      'components' in targetParameter\n    )\n      return getAmbiguousTypes(\n        sourceParameter.components,\n        targetParameter.components,\n        (args as any)[parameterIndex],\n      )\n\n    const types = [sourceParameter.type, targetParameter.type]\n\n    const ambiguous = (() => {\n      if (types.includes('address') && types.includes('bytes20')) return true\n      if (types.includes('address') && types.includes('string'))\n        return isAddress(args[parameterIndex] as Address)\n      if (types.includes('address') && types.includes('bytes'))\n        return isAddress(args[parameterIndex] as Address)\n      return false\n    })()\n\n    if (ambiguous) return types\n  }\n\n  return\n}\n", "import type { Abi } from 'abitype'\n\nimport {\n  AbiFunctionNotFoundError,\n  type AbiFunctionNotFoundErrorType,\n} from '../../errors/abi.js'\nimport type {\n  AbiItem,\n  GetFunctionArgs,\n  InferFunctionName,\n} from '../../types/contract.js'\nimport { type ConcatHexErrorType, concatHex } from '../data/concat.js'\nimport {\n  type GetFunctionSelectorErrorType,\n  getFunctionSelector,\n} from '../hash/getFunctionSelector.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type EncodeAbiParametersErrorType,\n  encodeAbiParameters,\n} from './encodeAbiParameters.js'\nimport { type FormatAbiItemErrorType, formatAbiItem } from './formatAbiItem.js'\nimport {\n  type GetAbiItemErrorType,\n  type GetAbiItemParameters,\n  getAbiItem,\n} from './getAbiItem.js'\n\nexport type EncodeFunctionDataParameters<\n  TAbi extends Abi | readonly unknown[] = Abi,\n  TFunctionName extends string | undefined = string,\n  _FunctionName = InferFunctionName<TAbi, TFunctionName>,\n> = {\n  functionName?: _FunctionName\n} & (TFunctionName extends string\n  ? { abi: TAbi } & GetFunctionArgs<TAbi, TFunctionName>\n  : _FunctionName extends string\n    ? { abi: [TAbi[number]] } & GetFunctionArgs<TAbi, _FunctionName>\n    : never)\n\nexport type EncodeFunctionDataErrorType =\n  | AbiFunctionNotFoundErrorType\n  | ConcatHexErrorType\n  | EncodeAbiParametersErrorType\n  | FormatAbiItemErrorType\n  | GetAbiItemErrorType\n  | GetFunctionSelectorErrorType\n  | ErrorType\n\nexport function encodeFunctionData<\n  const TAbi extends Abi | readonly unknown[],\n  TFunctionName extends string | undefined = undefined,\n>({\n  abi,\n  args,\n  functionName,\n}: EncodeFunctionDataParameters<TAbi, TFunctionName>) {\n  let abiItem = abi[0] as AbiItem\n  if (functionName) {\n    abiItem = getAbiItem({\n      abi,\n      args,\n      name: functionName,\n    } as GetAbiItemParameters)\n    if (!abiItem)\n      throw new AbiFunctionNotFoundError(functionName, {\n        docsPath: '/docs/contract/encodeFunctionData',\n      })\n  }\n\n  if (abiItem.type !== 'function')\n    throw new AbiFunctionNotFoundError(undefined, {\n      docsPath: '/docs/contract/encodeFunctionData',\n    })\n\n  const definition = formatAbiItem(abiItem)\n  const signature = getFunctionSelector(definition)\n  const data =\n    'inputs' in abiItem && abiItem.inputs\n      ? encodeAbiParameters(abiItem.inputs, (args ?? []) as readonly unknown[])\n      : undefined\n  return concatHex([signature, data ?? '0x'])\n}\n", "import type { Address } from 'abitype'\n\nimport { InvalidAddressError } from '../../errors/address.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type StringToBytesErrorType,\n  stringToBytes,\n} from '../encoding/toBytes.js'\nimport { type Keccak256ErrorType, keccak256 } from '../hash/keccak256.js'\nimport { type IsAddressErrorType, isAddress } from './isAddress.js'\n\nexport type ChecksumAddressErrorType =\n  | Keccak256ErrorType\n  | StringToBytesErrorType\n  | ErrorType\n\nexport function checksumAddress(address_: Address, chainId?: number): Address {\n  const hexAddress = chainId\n    ? `${chainId}${address_.toLowerCase()}`\n    : address_.substring(2).toLowerCase()\n  const hash = keccak256(stringToBytes(hexAddress), 'bytes')\n\n  const address = (\n    chainId ? hexAddress.substring(`${chainId}0x`.length) : hexAddress\n  ).split('')\n  for (let i = 0; i < 40; i += 2) {\n    if (hash[i >> 1] >> 4 >= 8 && address[i]) {\n      address[i] = address[i].toUpperCase()\n    }\n    if ((hash[i >> 1] & 0x0f) >= 8 && address[i + 1]) {\n      address[i + 1] = address[i + 1].toUpperCase()\n    }\n  }\n\n  return `0x${address.join('')}`\n}\n\nexport type GetAddressErrorType =\n  | ChecksumAddressErrorType\n  | IsAddressErrorType\n  | ErrorType\n\nexport function getAddress(address: string, chainId?: number): Address {\n  if (!isAddress(address)) throw new InvalidAddressError({ address })\n  return checksumAddress(address, chainId)\n}\n", "import type {\n  Abi<PERSON>ara<PERSON>,\n  AbiParameterToPrimitiveType,\n  AbiParametersToPrimitiveTypes,\n} from 'abitype'\n\nimport {\n  AbiDecodingDataSizeTooSmallError,\n  type AbiDecodingDataSizeTooSmallErrorType,\n  AbiDecodingZeroDataError,\n  type AbiDecodingZeroDataErrorType,\n  InvalidAbiDecodingTypeError,\n  type InvalidAbiDecodingTypeErrorType,\n} from '../../errors/abi.js'\nimport type { Hex } from '../../types/misc.js'\nimport {\n  type ChecksumAddressErrorType,\n  checksumAddress,\n} from '../address/getAddress.js'\nimport { type SizeErrorType, size } from '../data/size.js'\nimport { type SliceErrorType, slice } from '../data/slice.js'\nimport { type TrimErrorType, trim } from '../data/trim.js'\nimport {\n  type HexToBigIntErrorType,\n  type HexToBoolErrorType,\n  type HexToNumberErrorType,\n  type HexToStringErrorType,\n  hexToBigInt,\n  hexToBool,\n  hexToNumber,\n  hexToString,\n} from '../encoding/fromHex.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type GetArrayComponentsErrorType,\n  getArrayComponents,\n} from './encodeAbiParameters.js'\n\nexport type DecodeAbiParametersReturnType<\n  TParams extends\n    | readonly AbiParameter[]\n    | readonly unknown[] = readonly AbiParameter[],\n> = AbiParametersToPrimitiveTypes<\n  TParams extends readonly AbiParameter[] ? TParams : AbiParameter[]\n>\n\nexport type DecodeAbiParametersErrorType =\n  | AbiDecodingDataSizeTooSmallErrorType\n  | AbiDecodingZeroDataErrorType\n  | DecodeParamsErrorType\n  | SizeErrorType\n  | ErrorType\n\nexport function decodeAbiParameters<\n  const TParams extends readonly AbiParameter[] | readonly unknown[],\n>(params: TParams, data: Hex): DecodeAbiParametersReturnType<TParams> {\n  if (data === '0x' && (params as unknown[]).length > 0)\n    throw new AbiDecodingZeroDataError()\n  if (size(data) && size(data) < 32)\n    throw new AbiDecodingDataSizeTooSmallError({\n      data,\n      params: params as readonly AbiParameter[],\n      size: size(data),\n    })\n  return decodeParams({\n    data,\n    params: params as readonly AbiParameter[],\n  }) as unknown as DecodeAbiParametersReturnType<TParams>\n}\n\n////////////////////////////////////////////////////////////////////\n\ntype TupleAbiParameter = AbiParameter & { components: readonly AbiParameter[] }\n\ntype DecodeParamsErrorType = DecodeParamErrorType | SizeErrorType | ErrorType\n\nfunction decodeParams<const TParams extends readonly AbiParameter[]>({\n  data,\n  params,\n}: { data: Hex; params: TParams }) {\n  const decodedValues: unknown[] = []\n  let position = 0\n\n  for (let i = 0; i < params.length; i++) {\n    if (position >= size(data))\n      throw new AbiDecodingDataSizeTooSmallError({\n        data,\n        params,\n        size: size(data),\n      })\n\n    const param = params[i]\n    const { consumed, value } = decodeParam({ data, param, position })\n    decodedValues.push(value)\n    // Step across the data by the amount of data consumed by this parameter.\n    position += consumed\n  }\n\n  return decodedValues as unknown as AbiParametersToPrimitiveTypes<TParams>\n}\n\ntype DecodeParamErrorType =\n  | DecodeArrayErrorType\n  | DecodeTupleErrorType\n  | DecodeStringErrorType\n  | DecodeBytesErrorType\n  | DecodeNumberErrorType\n  | DecodeAddressErrorType\n  | DecodeBoolErrorType\n  | InvalidAbiDecodingTypeErrorType\n  | ErrorType\n\nfunction decodeParam({\n  data,\n  param,\n  position,\n}: { data: Hex; param: AbiParameter; position: number }): {\n  consumed: number\n  value: any\n} {\n  const arrayComponents = getArrayComponents(param.type)\n  if (arrayComponents) {\n    const [length, type] = arrayComponents\n    return decodeArray(data, {\n      length,\n      param: { ...param, type: type } as AbiParameter,\n      position,\n    })\n  }\n  if (param.type === 'tuple') {\n    return decodeTuple(data, { param: param as TupleAbiParameter, position })\n  }\n  if (param.type === 'string') {\n    return decodeString(data, { position })\n  }\n  if (param.type.startsWith('bytes')) {\n    return decodeBytes(data, { param, position })\n  }\n\n  const value = slice(data, position, position + 32, { strict: true }) as Hex\n  if (param.type.startsWith('uint') || param.type.startsWith('int')) {\n    return decodeNumber(value, { param })\n  }\n  if (param.type === 'address') {\n    return decodeAddress(value)\n  }\n  if (param.type === 'bool') {\n    return decodeBool(value)\n  }\n  throw new InvalidAbiDecodingTypeError(param.type, {\n    docsPath: '/docs/contract/decodeAbiParameters',\n  })\n}\n\n////////////////////////////////////////////////////////////////////\n\ntype DecodeAddressErrorType =\n  | ChecksumAddressErrorType\n  | SliceErrorType\n  | ErrorType\n\nfunction decodeAddress(value: Hex) {\n  return { consumed: 32, value: checksumAddress(slice(value, -20)) }\n}\n\ntype DecodeArrayErrorType = HexToNumberErrorType | SliceErrorType | ErrorType\n\nfunction decodeArray<const TParam extends AbiParameter>(\n  data: Hex,\n  {\n    param,\n    length,\n    position,\n  }: {\n    param: TParam\n    length: number | null\n    position: number\n  },\n) {\n  // If the length of the array is not known in advance (dynamic array),\n  // we will need to decode the offset of the array data.\n  if (!length) {\n    // Get the offset of the array data.\n    const offset = hexToNumber(\n      slice(data, position, position + 32, { strict: true }),\n    )\n    // Get the length of the array from the offset.\n    const length = hexToNumber(\n      slice(data, offset, offset + 32, { strict: true }),\n    )\n\n    let consumed = 0\n    const value: AbiParameterToPrimitiveType<TParam>[] = []\n    for (let i = 0; i < length; ++i) {\n      const decodedChild = decodeParam({\n        data: slice(data, offset + 32),\n        param,\n        position: consumed,\n      })\n      consumed += decodedChild.consumed\n      value.push(decodedChild.value)\n    }\n    return { value, consumed: 32 }\n  }\n\n  // If the length of the array is known in advance,\n  // and the length of an element deeply nested in the array is not known,\n  // we need to decode the offset of the array data.\n  if (hasDynamicChild(param)) {\n    // Get the child type of the array.\n    const arrayComponents = getArrayComponents(param.type)\n    // If the child type is not known, the array is dynamic.\n    const dynamicChild = !arrayComponents?.[0]\n\n    let consumed = 0\n    const value: AbiParameterToPrimitiveType<TParam>[] = []\n    for (let i = 0; i < length; ++i) {\n      const offset = hexToNumber(\n        slice(data, position, position + 32, { strict: true }),\n      )\n      const decodedChild = decodeParam({\n        data: slice(data, offset),\n        param,\n        position: dynamicChild ? consumed : i * 32,\n      })\n      consumed += decodedChild.consumed\n      value.push(decodedChild.value)\n    }\n    return { value, consumed: 32 }\n  }\n\n  // If the length of the array is known in advance,\n  // and the length of each element in the array is known,\n  // the array data is encoded contiguously after the array.\n  let consumed = 0\n  const value: AbiParameterToPrimitiveType<TParam>[] = []\n  for (let i = 0; i < length; ++i) {\n    const decodedChild = decodeParam({\n      data,\n      param,\n      position: position + consumed,\n    })\n    consumed += decodedChild.consumed\n    value.push(decodedChild.value)\n  }\n  return { value, consumed }\n}\n\ntype DecodeBoolErrorType = HexToBoolErrorType | ErrorType\n\nfunction decodeBool(value: Hex) {\n  return { consumed: 32, value: hexToBool(value) }\n}\n\ntype DecodeBytesErrorType = HexToNumberErrorType | SliceErrorType | ErrorType\n\nfunction decodeBytes<const TParam extends AbiParameter>(\n  data: Hex,\n  { param, position }: { param: TParam; position: number },\n) {\n  const [_, size] = param.type.split('bytes')\n  if (!size) {\n    // If we don't have a size, we're dealing with a dynamic-size array\n    // so we need to read the offset of the data part first.\n    const offset = hexToNumber(\n      slice(data, position, position + 32, { strict: true }),\n    )\n    const length = hexToNumber(\n      slice(data, offset, offset + 32, { strict: true }),\n    )\n    // If there is no length, we have zero data.\n    if (length === 0) return { consumed: 32, value: '0x' }\n    const value = slice(data, offset + 32, offset + 32 + length, {\n      strict: true,\n    })\n    return { consumed: 32, value }\n  }\n\n  const value = slice(data, position, position + parseInt(size), {\n    strict: true,\n  })\n  return { consumed: 32, value }\n}\n\ntype DecodeNumberErrorType =\n  | HexToBigIntErrorType\n  | HexToNumberErrorType\n  | ErrorType\n\nfunction decodeNumber<const TParam extends AbiParameter>(\n  value: Hex,\n  { param }: { param: TParam },\n) {\n  const signed = param.type.startsWith('int')\n  const size = parseInt(param.type.split('int')[1] || '256')\n  return {\n    consumed: 32,\n    value:\n      size > 48\n        ? hexToBigInt(value, { signed })\n        : hexToNumber(value, { signed }),\n  }\n}\n\ntype DecodeStringErrorType =\n  | HexToNumberErrorType\n  | HexToStringErrorType\n  | SliceErrorType\n  | TrimErrorType\n  | ErrorType\n\nfunction decodeString(data: Hex, { position }: { position: number }) {\n  const offset = hexToNumber(\n    slice(data, position, position + 32, { strict: true }),\n  )\n  const length = hexToNumber(slice(data, offset, offset + 32, { strict: true }))\n  // If there is no length, we have zero data (empty string).\n  if (length === 0) return { consumed: 32, value: '' }\n  const value = hexToString(\n    trim(slice(data, offset + 32, offset + 32 + length, { strict: true })),\n  )\n  return { consumed: 32, value }\n}\n\ntype DecodeTupleErrorType =\n  | HasDynamicChildErrorType\n  | HexToNumberErrorType\n  | SliceErrorType\n  | ErrorType\n\nfunction decodeTuple<\n  const TParam extends AbiParameter & { components: readonly AbiParameter[] },\n>(data: Hex, { param, position }: { param: TParam; position: number }) {\n  // Tuples can have unnamed components (i.e. they are arrays), so we must\n  // determine whether the tuple is named or unnamed. In the case of a named\n  // tuple, the value will be an object where each property is the name of the\n  // component. In the case of an unnamed tuple, the value will be an array.\n  const hasUnnamedChild =\n    param.components.length === 0 || param.components.some(({ name }) => !name)\n\n  // Initialize the value to an object or an array, depending on whether the\n  // tuple is named or unnamed.\n  const value: any = hasUnnamedChild ? [] : {}\n  let consumed = 0\n\n  // If the tuple has a dynamic child, we must first decode the offset to the\n  // tuple data.\n  if (hasDynamicChild(param)) {\n    const offset = hexToNumber(\n      slice(data, position, position + 32, { strict: true }),\n    )\n    // Decode each component of the tuple, starting at the offset.\n    for (let i = 0; i < param.components.length; ++i) {\n      const component = param.components[i]\n      const decodedChild = decodeParam({\n        data: slice(data, offset),\n        param: component,\n        position: consumed,\n      })\n      consumed += decodedChild.consumed\n      value[hasUnnamedChild ? i : component?.name!] = decodedChild.value\n    }\n    return { consumed: 32, value }\n  }\n\n  // If the tuple has static children, we can just decode each component\n  // in sequence.\n  for (let i = 0; i < param.components.length; ++i) {\n    const component = param.components[i]\n    const decodedChild = decodeParam({\n      data,\n      param: component,\n      position: position + consumed,\n    })\n    consumed += decodedChild.consumed\n    value[hasUnnamedChild ? i : component?.name!] = decodedChild.value\n  }\n  return { consumed, value }\n}\n\ntype HasDynamicChildErrorType = GetArrayComponentsErrorType | ErrorType\n\nfunction hasDynamicChild(param: AbiParameter) {\n  const { type } = param\n  if (type === 'string') return true\n  if (type === 'bytes') return true\n  if (type.endsWith('[]')) return true\n\n  if (type === 'tuple') return (param as any).components?.some(hasDynamicChild)\n\n  const arrayComponents = getArrayComponents(param.type)\n  if (\n    arrayComponents &&\n    hasDynamicChild({ ...param, type: arrayComponents[1] } as AbiParameter)\n  )\n    return true\n\n  return false\n}\n", "import type { AbiError } from 'abitype'\n\n// https://docs.soliditylang.org/en/v0.8.16/control-structures.html#panic-via-assert-and-error-via-require\nexport const panicReasons = {\n  1: 'An `assert` condition failed.',\n  17: 'Arithmic operation resulted in underflow or overflow.',\n  18: 'Division or modulo by zero (e.g. `5 / 0` or `23 % 0`).',\n  33: 'Attempted to convert to an invalid type.',\n  34: 'Attempted to access a storage byte array that is incorrectly encoded.',\n  49: 'Performed `.pop()` on an empty array',\n  50: 'Array index is out of bounds.',\n  65: 'Allocated too much memory or created an array which is too large.',\n  81: 'Attempted to call a zero-initialized variable of internal function type.',\n} as const\n\nexport const solidityError: AbiError = {\n  inputs: [\n    {\n      name: 'message',\n      type: 'string',\n    },\n  ],\n  name: 'Error',\n  type: 'error',\n}\nexport const solidityPanic: AbiError = {\n  inputs: [\n    {\n      name: 'reason',\n      type: 'uint256',\n    },\n  ],\n  name: 'Panic',\n  type: 'error',\n}\n", "import type { Abi, ExtractAbiError, ExtractAbiErrorNames } from 'abitype'\n\nimport { solidityError, solidityPanic } from '../../constants/solidity.js'\nimport {\n  AbiDecodingZeroDataError,\n  type AbiDecodingZeroDataErrorType,\n  AbiErrorSignatureNotFoundError,\n  type AbiErrorSignatureNotFoundErrorType,\n} from '../../errors/abi.js'\nimport type { AbiItem, GetErrorArgs } from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\nimport { slice } from '../data/slice.js'\nimport {\n  type GetFunctionSelectorErrorType,\n  getFunctionSelector,\n} from '../hash/getFunctionSelector.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type DecodeAbiParametersErrorType,\n  decodeAbiParameters,\n} from './decodeAbiParameters.js'\nimport { type FormatAbiItemErrorType, formatAbiItem } from './formatAbiItem.js'\n\nexport type DecodeErrorResultParameters<\n  TAbi extends Abi | readonly unknown[] = Abi,\n> = { abi?: TAbi; data: Hex }\n\nexport type DecodeErrorResultReturnType<\n  TAbi extends Abi | readonly unknown[] = Abi,\n  _ErrorNames extends string = TAbi extends Abi\n    ? Abi extends TAbi\n      ? string\n      : ExtractAbiErrorNames<TAbi>\n    : string,\n> = {\n  [TName in _ErrorNames]: {\n    abiItem: TAbi extends Abi ? ExtractAbiError<TAbi, TName> : AbiItem\n    args: GetErrorArgs<TAbi, TName>['args']\n    errorName: TName\n  }\n}[_ErrorNames]\n\nexport type DecodeErrorResultErrorType =\n  | AbiDecodingZeroDataErrorType\n  | AbiErrorSignatureNotFoundErrorType\n  | DecodeAbiParametersErrorType\n  | FormatAbiItemErrorType\n  | GetFunctionSelectorErrorType\n  | ErrorType\n\nexport function decodeErrorResult<const TAbi extends Abi | readonly unknown[]>({\n  abi,\n  data,\n}: DecodeErrorResultParameters<TAbi>): DecodeErrorResultReturnType<TAbi> {\n  const signature = slice(data, 0, 4)\n  if (signature === '0x') throw new AbiDecodingZeroDataError()\n\n  const abi_ = [...((abi as Abi) || []), solidityError, solidityPanic]\n  const abiItem = abi_.find(\n    (x) =>\n      x.type === 'error' && signature === getFunctionSelector(formatAbiItem(x)),\n  )\n  if (!abiItem)\n    throw new AbiErrorSignatureNotFoundError(signature, {\n      docsPath: '/docs/contract/decodeErrorResult',\n    })\n  return {\n    abiItem,\n    args: ('inputs' in abiItem && abiItem.inputs && abiItem.inputs.length > 0\n      ? decodeAbiParameters(abiItem.inputs, slice(data, 4))\n      : undefined) as readonly unknown[] | undefined,\n    errorName: (abiItem as { name: string }).name,\n  } as DecodeErrorResultReturnType<TAbi>\n}\n", "import type { ErrorType } from '../errors/utils.js'\n\nexport type StringifyErrorType = ErrorType\n\nexport const stringify: typeof JSON.stringify = (value, replacer, space) =>\n  JSON.stringify(\n    value,\n    (key, value_) => {\n      const value = typeof value_ === 'bigint' ? value_.toString() : value_\n      return typeof replacer === 'function' ? replacer(key, value) : value\n    },\n    space,\n  )\n", "import { etherUnits } from '../../constants/unit.js'\n\nimport { type FormatUnitsErrorType, formatUnits } from './formatUnits.js'\n\nexport type FormatEtherErrorType = FormatUnitsErrorType\n\n/**\n * Converts numerical wei to a string representation of ether.\n *\n * - Docs: https://viem.sh/docs/utilities/formatEther.html\n *\n * @example\n * import { formatEther } from 'viem'\n *\n * formatEther(1000000000000000000n)\n * // '1'\n */\nexport function formatEther(wei: bigint, unit: 'wei' | 'gwei' = 'wei') {\n  return formatUnits(wei, etherUnits[unit])\n}\n", "import type { Account } from '../accounts/types.js'\nimport type { SendTransactionParameters } from '../actions/wallet/sendTransaction.js'\nimport type { BlockTag } from '../types/block.js'\nimport type { Chain } from '../types/chain.js'\nimport type { Hash, Hex } from '../types/misc.js'\nimport type { TransactionType } from '../types/transaction.js'\nimport { formatEther } from '../utils/unit/formatEther.js'\nimport { formatGwei } from '../utils/unit/formatGwei.js'\n\nimport { BaseError } from './base.js'\n\nexport function prettyPrint(\n  args: Record<string, bigint | number | string | undefined | false | unknown>,\n) {\n  const entries = Object.entries(args)\n    .map(([key, value]) => {\n      if (value === undefined || value === false) return null\n      return [key, value]\n    })\n    .filter(Boolean) as [string, string][]\n  const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0)\n  return entries\n    .map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`)\n    .join('\\n')\n}\n\nexport type FeeConflictErrorType = FeeConflictError & {\n  name: 'FeeConflictError'\n}\nexport class FeeConflictError extends BaseError {\n  override name = 'FeeConflictError'\n  constructor() {\n    super(\n      [\n        'Cannot specify both a `gasPrice` and a `maxFeePerGas`/`maxPriorityFeePerGas`.',\n        'Use `maxFeePerGas`/`maxPriorityFeePerGas` for EIP-1559 compatible networks, and `gasPrice` for others.',\n      ].join('\\n'),\n    )\n  }\n}\n\nexport type InvalidLegacyVErrorType = InvalidLegacyVError & {\n  name: 'InvalidLegacyVError'\n}\nexport class InvalidLegacyVError extends BaseError {\n  override name = 'InvalidLegacyVError'\n\n  constructor({ v }: { v: bigint }) {\n    super(`Invalid \\`v\\` value \"${v}\". Expected 27 or 28.`)\n  }\n}\n\nexport type InvalidSerializableTransactionErrorType =\n  InvalidSerializableTransactionError & {\n    name: 'InvalidSerializableTransactionError'\n  }\nexport class InvalidSerializableTransactionError extends BaseError {\n  override name = 'InvalidSerializableTransactionError'\n\n  constructor({ transaction }: { transaction: Record<string, unknown> }) {\n    super('Cannot infer a transaction type from provided transaction.', {\n      metaMessages: [\n        'Provided Transaction:',\n        '{',\n        prettyPrint(transaction),\n        '}',\n        '',\n        'To infer the type, either provide:',\n        '- a `type` to the Transaction, or',\n        '- an EIP-1559 Transaction with `maxFeePerGas`, or',\n        '- an EIP-2930 Transaction with `gasPrice` & `accessList`, or',\n        '- a Legacy Transaction with `gasPrice`',\n      ],\n    })\n  }\n}\n\nexport type InvalidSerializedTransactionTypeErrorType =\n  InvalidSerializedTransactionTypeError & {\n    name: 'InvalidSerializedTransactionTypeError'\n  }\nexport class InvalidSerializedTransactionTypeError extends BaseError {\n  override name = 'InvalidSerializedTransactionType'\n\n  serializedType: Hex\n\n  constructor({ serializedType }: { serializedType: Hex }) {\n    super(`Serialized transaction type \"${serializedType}\" is invalid.`)\n\n    this.serializedType = serializedType\n  }\n}\n\nexport type InvalidSerializedTransactionErrorType =\n  InvalidSerializedTransactionError & {\n    name: 'InvalidSerializedTransactionError'\n  }\nexport class InvalidSerializedTransactionError extends BaseError {\n  override name = 'InvalidSerializedTransactionError'\n\n  serializedTransaction: Hex\n  type: TransactionType\n\n  constructor({\n    attributes,\n    serializedTransaction,\n    type,\n  }: {\n    attributes: Record<string, unknown>\n    serializedTransaction: Hex\n    type: TransactionType\n  }) {\n    const missing = Object.entries(attributes)\n      .map(([key, value]) => (typeof value === 'undefined' ? key : undefined))\n      .filter(Boolean)\n    super(`Invalid serialized transaction of type \"${type}\" was provided.`, {\n      metaMessages: [\n        `Serialized Transaction: \"${serializedTransaction}\"`,\n        missing.length > 0 ? `Missing Attributes: ${missing.join(', ')}` : '',\n      ].filter(Boolean),\n    })\n\n    this.serializedTransaction = serializedTransaction\n    this.type = type\n  }\n}\n\nexport type InvalidStorageKeySizeErrorType = InvalidStorageKeySizeError & {\n  name: 'InvalidStorageKeySizeError'\n}\nexport class InvalidStorageKeySizeError extends BaseError {\n  override name = 'InvalidStorageKeySizeError'\n\n  constructor({ storageKey }: { storageKey: Hex }) {\n    super(\n      `Size for storage key \"${storageKey}\" is invalid. Expected 32 bytes. Got ${Math.floor(\n        (storageKey.length - 2) / 2,\n      )} bytes.`,\n    )\n  }\n}\n\nexport type TransactionExecutionErrorType = TransactionExecutionError & {\n  name: 'TransactionExecutionError'\n}\nexport class TransactionExecutionError extends BaseError {\n  override cause: BaseError\n\n  override name = 'TransactionExecutionError'\n\n  constructor(\n    cause: BaseError,\n    {\n      account,\n      docsPath,\n      chain,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to,\n      value,\n    }: Omit<SendTransactionParameters, 'account' | 'chain'> & {\n      account: Account\n      chain?: Chain\n      docsPath?: string\n    },\n  ) {\n    const prettyArgs = prettyPrint({\n      chain: chain && `${chain?.name} (id: ${chain?.id})`,\n      from: account?.address,\n      to,\n      value:\n        typeof value !== 'undefined' &&\n        `${formatEther(value)} ${chain?.nativeCurrency?.symbol || 'ETH'}`,\n      data,\n      gas,\n      gasPrice:\n        typeof gasPrice !== 'undefined' && `${formatGwei(gasPrice)} gwei`,\n      maxFeePerGas:\n        typeof maxFeePerGas !== 'undefined' &&\n        `${formatGwei(maxFeePerGas)} gwei`,\n      maxPriorityFeePerGas:\n        typeof maxPriorityFeePerGas !== 'undefined' &&\n        `${formatGwei(maxPriorityFeePerGas)} gwei`,\n      nonce,\n    })\n\n    super(cause.shortMessage, {\n      cause,\n      docsPath,\n      metaMessages: [\n        ...(cause.metaMessages ? [...cause.metaMessages, ' '] : []),\n        'Request Arguments:',\n        prettyArgs,\n      ].filter(Boolean) as string[],\n    })\n    this.cause = cause\n  }\n}\n\nexport type TransactionNotFoundErrorType = TransactionNotFoundError & {\n  name: 'TransactionNotFoundError'\n}\nexport class TransactionNotFoundError extends BaseError {\n  override name = 'TransactionNotFoundError'\n  constructor({\n    blockHash,\n    blockNumber,\n    blockTag,\n    hash,\n    index,\n  }: {\n    blockHash?: Hash\n    blockNumber?: bigint\n    blockTag?: BlockTag\n    hash?: Hash\n    index?: number\n  }) {\n    let identifier = 'Transaction'\n    if (blockTag && index !== undefined)\n      identifier = `Transaction at block time \"${blockTag}\" at index \"${index}\"`\n    if (blockHash && index !== undefined)\n      identifier = `Transaction at block hash \"${blockHash}\" at index \"${index}\"`\n    if (blockNumber && index !== undefined)\n      identifier = `Transaction at block number \"${blockNumber}\" at index \"${index}\"`\n    if (hash) identifier = `Transaction with hash \"${hash}\"`\n    super(`${identifier} could not be found.`)\n  }\n}\n\nexport type TransactionReceiptNotFoundErrorType =\n  TransactionReceiptNotFoundError & {\n    name: 'TransactionReceiptNotFoundError'\n  }\nexport class TransactionReceiptNotFoundError extends BaseError {\n  override name = 'TransactionReceiptNotFoundError'\n  constructor({ hash }: { hash: Hash }) {\n    super(\n      `Transaction receipt with hash \"${hash}\" could not be found. The Transaction may not be processed on a block yet.`,\n    )\n  }\n}\n\nexport type WaitForTransactionReceiptTimeoutErrorType =\n  WaitForTransactionReceiptTimeoutError & {\n    name: 'WaitForTransactionReceiptTimeoutError'\n  }\nexport class WaitForTransactionReceiptTimeoutError extends BaseError {\n  override name = 'WaitForTransactionReceiptTimeoutError'\n  constructor({ hash }: { hash: Hash }) {\n    super(\n      `Timed out while waiting for transaction with hash \"${hash}\" to be confirmed.`,\n    )\n  }\n}\n", "import type { Address } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Account } from '../types.js'\n\nexport type ParseAccountErrorType = ErrorType\n\nexport function parseAccount(account: Address | Account): Account {\n  if (typeof account === 'string') return { address: account, type: 'json-rpc' }\n  return account\n}\n", "import type { AbiParameter } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { AbiItem } from '../../types/contract.js'\nimport { stringify } from '../stringify.js'\n\nexport type FormatAbiItemWithArgsErrorType = ErrorType\n\nexport function formatAbiItemWithArgs({\n  abiItem,\n  args,\n  includeFunctionName = true,\n  includeName = false,\n}: {\n  abiItem: AbiItem\n  args: readonly unknown[]\n  includeFunctionName?: boolean\n  includeName?: boolean\n}) {\n  if (!('name' in abiItem)) return\n  if (!('inputs' in abiItem)) return\n  if (!abiItem.inputs) return\n  return `${includeFunctionName ? abiItem.name : ''}(${abiItem.inputs\n    .map(\n      (input: AbiParameter, i: number) =>\n        `${includeName && input.name ? `${input.name}: ` : ''}${\n          typeof args[i] === 'object' ? stringify(args[i]) : args[i]\n        }`,\n    )\n    .join(', ')})`\n}\n", "import type { Abi, Address } from 'abitype'\n\nimport { parseAccount } from '../accounts/utils/parseAccount.js'\nimport type { CallParameters } from '../actions/public/call.js'\nimport { panicReasons } from '../constants/solidity.js'\nimport type { Chain } from '../types/chain.js'\nimport type { Hex } from '../types/misc.js'\nimport {\n  type DecodeErrorResultReturnType,\n  decodeErrorResult,\n} from '../utils/abi/decodeErrorResult.js'\nimport { formatAbiItem } from '../utils/abi/formatAbiItem.js'\nimport { formatAbiItemWithArgs } from '../utils/abi/formatAbiItemWithArgs.js'\nimport { getAbiItem } from '../utils/abi/getAbiItem.js'\nimport { formatEther } from '../utils/unit/formatEther.js'\nimport { formatGwei } from '../utils/unit/formatGwei.js'\n\nimport { AbiErrorSignatureNotFoundError } from './abi.js'\nimport { BaseError } from './base.js'\nimport { prettyPrint } from './transaction.js'\nimport { getContractAddress } from './utils.js'\n\nexport type CallExecutionErrorType = CallExecutionError & {\n  name: 'CallExecutionError'\n}\nexport class CallExecutionError extends BaseError {\n  override cause: BaseError\n\n  override name = 'CallExecutionError'\n\n  constructor(\n    cause: BaseError,\n    {\n      account: account_,\n      docsPath,\n      chain,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to,\n      value,\n    }: CallParameters & { chain?: Chain; docsPath?: string },\n  ) {\n    const account = account_ ? parseAccount(account_) : undefined\n    const prettyArgs = prettyPrint({\n      from: account?.address,\n      to,\n      value:\n        typeof value !== 'undefined' &&\n        `${formatEther(value)} ${chain?.nativeCurrency?.symbol || 'ETH'}`,\n      data,\n      gas,\n      gasPrice:\n        typeof gasPrice !== 'undefined' && `${formatGwei(gasPrice)} gwei`,\n      maxFeePerGas:\n        typeof maxFeePerGas !== 'undefined' &&\n        `${formatGwei(maxFeePerGas)} gwei`,\n      maxPriorityFeePerGas:\n        typeof maxPriorityFeePerGas !== 'undefined' &&\n        `${formatGwei(maxPriorityFeePerGas)} gwei`,\n      nonce,\n    })\n\n    super(cause.shortMessage, {\n      cause,\n      docsPath,\n      metaMessages: [\n        ...(cause.metaMessages ? [...cause.metaMessages, ' '] : []),\n        'Raw Call Arguments:',\n        prettyArgs,\n      ].filter(Boolean) as string[],\n    })\n    this.cause = cause\n  }\n}\n\nexport type ContractFunctionExecutionErrorType =\n  ContractFunctionExecutionError & {\n    name: 'ContractFunctionExecutionError'\n  }\nexport class ContractFunctionExecutionError extends BaseError {\n  abi: Abi\n  args?: unknown[]\n  override cause: BaseError\n  contractAddress?: Address\n  formattedArgs?: string\n  functionName: string\n  sender?: Address\n\n  override name = 'ContractFunctionExecutionError'\n\n  constructor(\n    cause: BaseError,\n    {\n      abi,\n      args,\n      contractAddress,\n      docsPath,\n      functionName,\n      sender,\n    }: {\n      abi: Abi\n      args?: any\n      contractAddress?: Address\n      docsPath?: string\n      functionName: string\n      sender?: Address\n    },\n  ) {\n    const abiItem = getAbiItem({ abi, args, name: functionName })\n    const formattedArgs = abiItem\n      ? formatAbiItemWithArgs({\n          abiItem,\n          args,\n          includeFunctionName: false,\n          includeName: false,\n        })\n      : undefined\n    const functionWithParams = abiItem\n      ? formatAbiItem(abiItem, { includeName: true })\n      : undefined\n\n    const prettyArgs = prettyPrint({\n      address: contractAddress && getContractAddress(contractAddress),\n      function: functionWithParams,\n      args:\n        formattedArgs &&\n        formattedArgs !== '()' &&\n        `${[...Array(functionName?.length ?? 0).keys()]\n          .map(() => ' ')\n          .join('')}${formattedArgs}`,\n      sender,\n    })\n\n    super(\n      cause.shortMessage ||\n        `An unknown error occurred while executing the contract function \"${functionName}\".`,\n      {\n        cause,\n        docsPath,\n        metaMessages: [\n          ...(cause.metaMessages ? [...cause.metaMessages, ' '] : []),\n          'Contract Call:',\n          prettyArgs,\n        ].filter(Boolean) as string[],\n      },\n    )\n    this.abi = abi\n    this.args = args\n    this.cause = cause\n    this.contractAddress = contractAddress\n    this.functionName = functionName\n    this.sender = sender\n  }\n}\n\nexport type ContractFunctionRevertedErrorType =\n  ContractFunctionRevertedError & {\n    name: 'ContractFunctionRevertedError'\n  }\nexport class ContractFunctionRevertedError extends BaseError {\n  override name = 'ContractFunctionRevertedError'\n\n  data?: DecodeErrorResultReturnType\n  reason?: string\n  signature?: Hex\n\n  constructor({\n    abi,\n    data,\n    functionName,\n    message,\n  }: { abi: Abi; data?: Hex; functionName: string; message?: string }) {\n    let cause: Error | undefined\n    let decodedData: DecodeErrorResultReturnType | undefined = undefined\n    let metaMessages\n    let reason\n    if (data && data !== '0x') {\n      try {\n        decodedData = decodeErrorResult({ abi, data })\n        const { abiItem, errorName, args: errorArgs } = decodedData\n        if (errorName === 'Error') {\n          reason = (errorArgs as [string])[0]\n        } else if (errorName === 'Panic') {\n          const [firstArg] = errorArgs as [number]\n          reason = panicReasons[firstArg as keyof typeof panicReasons]\n        } else {\n          const errorWithParams = abiItem\n            ? formatAbiItem(abiItem, { includeName: true })\n            : undefined\n          const formattedArgs =\n            abiItem && errorArgs\n              ? formatAbiItemWithArgs({\n                  abiItem,\n                  args: errorArgs,\n                  includeFunctionName: false,\n                  includeName: false,\n                })\n              : undefined\n\n          metaMessages = [\n            errorWithParams ? `Error: ${errorWithParams}` : '',\n            formattedArgs && formattedArgs !== '()'\n              ? `       ${[...Array(errorName?.length ?? 0).keys()]\n                  .map(() => ' ')\n                  .join('')}${formattedArgs}`\n              : '',\n          ]\n        }\n      } catch (err) {\n        cause = err as Error\n      }\n    } else if (message) reason = message\n\n    let signature: Hex | undefined\n    if (cause instanceof AbiErrorSignatureNotFoundError) {\n      signature = cause.signature\n      metaMessages = [\n        `Unable to decode signature \"${signature}\" as it was not found on the provided ABI.`,\n        'Make sure you are using the correct ABI and that the error exists on it.',\n        `You can look up the decoded signature here: https://openchain.xyz/signatures?query=${signature}.`,\n      ]\n    }\n\n    super(\n      (reason && reason !== 'execution reverted') || signature\n        ? [\n            `The contract function \"${functionName}\" reverted with the following ${\n              signature ? 'signature' : 'reason'\n            }:`,\n            reason || signature,\n          ].join('\\n')\n        : `The contract function \"${functionName}\" reverted.`,\n      {\n        cause,\n        metaMessages,\n      },\n    )\n\n    this.data = decodedData\n    this.reason = reason\n    this.signature = signature\n  }\n}\n\nexport type ContractFunctionZeroDataErrorType =\n  ContractFunctionZeroDataError & {\n    name: 'ContractFunctionZeroDataError'\n  }\nexport class ContractFunctionZeroDataError extends BaseError {\n  override name = 'ContractFunctionZeroDataError'\n  constructor({ functionName }: { functionName: string }) {\n    super(`The contract function \"${functionName}\" returned no data (\"0x\").`, {\n      metaMessages: [\n        'This could be due to any of the following:',\n        `  - The contract does not have the function \"${functionName}\",`,\n        '  - The parameters passed to the contract function may be invalid, or',\n        '  - The address is not a contract.',\n      ],\n    })\n  }\n}\n\nexport type RawContractErrorType = RawContractError & {\n  name: 'RawContractError'\n}\nexport class RawContractError extends BaseError {\n  code = 3\n  override name = 'RawContractError'\n\n  data?: Hex | { data?: Hex }\n\n  constructor({\n    data,\n    message,\n  }: { data?: Hex | { data?: Hex }; message?: string }) {\n    super(message || '')\n    this.data = data\n  }\n}\n", "import { stringify } from '../utils/stringify.js'\n\nimport { BaseError } from './base.js'\nimport { getUrl } from './utils.js'\n\nexport type HttpRequestErrorType = HttpRequestError & {\n  name: 'HttpRequestError'\n}\nexport class HttpRequestError extends BaseError {\n  override name = 'HttpRequestError'\n\n  body?: { [x: string]: unknown } | { [y: string]: unknown }[]\n  headers?: Headers\n  status?: number\n  url: string\n\n  constructor({\n    body,\n    details,\n    headers,\n    status,\n    url,\n  }: {\n    body?: { [x: string]: unknown } | { [y: string]: unknown }[]\n    details?: string\n    headers?: Headers\n    status?: number\n    url: string\n  }) {\n    super('HTTP request failed.', {\n      details,\n      metaMessages: [\n        status && `Status: ${status}`,\n        `URL: ${getUrl(url)}`,\n        body && `Request body: ${stringify(body)}`,\n      ].filter(Boolean) as string[],\n    })\n    this.body = body\n    this.headers = headers\n    this.status = status\n    this.url = url\n  }\n}\n\nexport type WebSocketRequestErrorType = WebSocketRequestError & {\n  name: 'WebSocketRequestError'\n}\nexport class WebSocketRequestError extends BaseError {\n  override name = 'WebSocketRequestError'\n\n  constructor({\n    body,\n    details,\n    url,\n  }: {\n    body: { [key: string]: unknown }\n    details: string\n    url: string\n  }) {\n    super('WebSocket request failed.', {\n      details,\n      metaMessages: [`URL: ${getUrl(url)}`, `Request body: ${stringify(body)}`],\n    })\n  }\n}\n\nexport type RpcRequestErrorType = RpcRequestError & {\n  name: 'RpcRequestError'\n}\nexport class RpcRequestError extends BaseError {\n  override name = 'RpcRequestError'\n\n  code: number\n\n  constructor({\n    body,\n    error,\n    url,\n  }: {\n    body: { [x: string]: unknown } | { [y: string]: unknown }[]\n    error: { code: number; message: string }\n    url: string\n  }) {\n    super('RPC Request failed.', {\n      cause: error as any,\n      details: error.message,\n      metaMessages: [`URL: ${getUrl(url)}`, `Request body: ${stringify(body)}`],\n    })\n    this.code = error.code\n  }\n}\n\nexport type TimeoutErrorType = TimeoutError & {\n  name: 'TimeoutError'\n}\nexport class TimeoutError extends BaseError {\n  override name = 'TimeoutError'\n\n  constructor({\n    body,\n    url,\n  }: {\n    body: { [x: string]: unknown } | { [y: string]: unknown }[]\n    url: string\n  }) {\n    super('The request took too long to respond.', {\n      details: 'The request timed out.',\n      metaMessages: [`URL: ${getUrl(url)}`, `Request body: ${stringify(body)}`],\n    })\n  }\n}\n", "import type { Prettify } from '../types/utils.js'\nimport { BaseError } from './base.js'\nimport { RpcRequestError } from './request.js'\n\nconst unknownErrorCode = -1\n\nexport type RpcErrorCode =\n  | -1\n  | -32700 // Parse error\n  | -32600 // Invalid request\n  | -32601 // Method not found\n  | -32602 // Invalid params\n  | -32603 // Internal error\n  | -32000 // Invalid input\n  | -32001 // Resource not found\n  | -32002 // Resource unavailable\n  | -32003 // Transaction rejected\n  | -32004 // Method not supported\n  | -32005 // Limit exceeded\n  | -32006 // JSON-RPC version not supported\n  | -32042 // Method not found\n\ntype RpcErrorOptions<TCode extends number = RpcErrorCode> = {\n  code?: TCode | (number & {})\n  docsPath?: string\n  metaMessages?: string[]\n  shortMessage: string\n}\n\n/**\n * Error subclass implementing JSON RPC 2.0 errors and Ethereum RPC errors per EIP-1474.\n *\n * - EIP https://eips.ethereum.org/EIPS/eip-1474\n */\nexport type RpcErrorType = RpcError & { name: 'RpcError' }\nexport class RpcError<TCode extends number = RpcErrorCode> extends BaseError {\n  override name = 'RpcError'\n\n  code: TCode | (number & {})\n\n  constructor(\n    cause: Error,\n    { code, docsPath, metaMessages, shortMessage }: RpcErrorOptions<TCode>,\n  ) {\n    super(shortMessage, {\n      cause,\n      docsPath,\n      metaMessages:\n        metaMessages || (cause as { metaMessages?: string[] })?.metaMessages,\n    })\n    this.name = cause.name\n    this.code = (\n      cause instanceof RpcRequestError ? cause.code : code ?? unknownErrorCode\n    ) as TCode\n  }\n}\n\nexport type ProviderRpcErrorCode =\n  | 4001 // User Rejected Request\n  | 4100 // Unauthorized\n  | 4200 // Unsupported Method\n  | 4900 // Disconnected\n  | 4901 // Chain Disconnected\n  | 4902 // Chain Not Recognized\n\n/**\n * Error subclass implementing Ethereum Provider errors per EIP-1193.\n *\n * - EIP https://eips.ethereum.org/EIPS/eip-1193\n */\nexport type ProviderRpcErrorType = ProviderRpcError & {\n  name: 'ProviderRpcError'\n}\nexport class ProviderRpcError<\n  T = undefined,\n> extends RpcError<ProviderRpcErrorCode> {\n  override name = 'ProviderRpcError'\n\n  data?: T\n\n  constructor(\n    cause: Error,\n    options: Prettify<\n      RpcErrorOptions<ProviderRpcErrorCode> & {\n        data?: T\n      }\n    >,\n  ) {\n    super(cause, options)\n\n    this.data = options.data\n  }\n}\n\n/**\n * Subclass for a \"Parse error\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type ParseRpcErrorType = ParseRpcError & {\n  code: -32700\n  name: 'ParseRpcError'\n}\nexport class ParseRpcError extends RpcError {\n  override name = 'ParseRpcError'\n  static code = -32700 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ParseRpcError.code,\n      shortMessage:\n        'Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Invalid request\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type InvalidRequestRpcErrorType = InvalidRequestRpcError & {\n  code: -32600\n  name: 'InvalidRequestRpcError'\n}\nexport class InvalidRequestRpcError extends RpcError {\n  override name = 'InvalidRequestRpcError'\n  static code = -32600 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: InvalidRequestRpcError.code,\n      shortMessage: 'JSON is not a valid request object.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Method not found\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type MethodNotFoundRpcErrorType = MethodNotFoundRpcError & {\n  code: -32601\n  name: 'MethodNotFoundRpcError'\n}\nexport class MethodNotFoundRpcError extends RpcError {\n  override name = 'MethodNotFoundRpcError'\n  static code = -32601 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: MethodNotFoundRpcError.code,\n      shortMessage: 'The method does not exist / is not available.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Invalid params\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type InvalidParamsRpcErrorType = InvalidParamsRpcError & {\n  code: -32602\n  name: 'InvalidParamsRpcError'\n}\nexport class InvalidParamsRpcError extends RpcError {\n  override name = 'InvalidParamsRpcError'\n  static code = -32602 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: InvalidParamsRpcError.code,\n      shortMessage: [\n        'Invalid parameters were provided to the RPC method.',\n        'Double check you have provided the correct parameters.',\n      ].join('\\n'),\n    })\n  }\n}\n\n/**\n * Subclass for an \"Internal error\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type InternalRpcErrorType = InternalRpcError & {\n  code: -32603\n  name: 'InternalRpcError'\n}\nexport class InternalRpcError extends RpcError {\n  override name = 'InternalRpcError'\n  static code = -32603 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: InternalRpcError.code,\n      shortMessage: 'An internal error was received.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Invalid input\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type InvalidInputRpcErrorType = InvalidInputRpcError & {\n  code: -32000\n  name: 'InvalidInputRpcError'\n}\nexport class InvalidInputRpcError extends RpcError {\n  override name = 'InvalidInputRpcError'\n  static code = -32000 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: InvalidInputRpcError.code,\n      shortMessage: [\n        'Missing or invalid parameters.',\n        'Double check you have provided the correct parameters.',\n      ].join('\\n'),\n    })\n  }\n}\n\n/**\n * Subclass for a \"Resource not found\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type ResourceNotFoundRpcErrorType = ResourceNotFoundRpcError & {\n  code: -32001\n  name: 'ResourceNotFoundRpcError'\n}\nexport class ResourceNotFoundRpcError extends RpcError {\n  override name = 'ResourceNotFoundRpcError'\n  static code = -32001 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ResourceNotFoundRpcError.code,\n      shortMessage: 'Requested resource not found.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Resource unavailable\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type ResourceUnavailableRpcErrorType = ResourceUnavailableRpcError & {\n  code: -32002\n  name: 'ResourceUnavailableRpcError'\n}\nexport class ResourceUnavailableRpcError extends RpcError {\n  override name = 'ResourceUnavailableRpcError'\n  static code = -32002 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ResourceUnavailableRpcError.code,\n      shortMessage: 'Requested resource not available.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Transaction rejected\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type TransactionRejectedRpcErrorType = TransactionRejectedRpcError & {\n  code: -32003\n  name: 'TransactionRejectedRpcError'\n}\nexport class TransactionRejectedRpcError extends RpcError {\n  override name = 'TransactionRejectedRpcError'\n  static code = -32003 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: TransactionRejectedRpcError.code,\n      shortMessage: 'Transaction creation failed.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Method not supported\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type MethodNotSupportedRpcErrorType = MethodNotSupportedRpcError & {\n  code: -32004\n  name: 'MethodNotSupportedRpcError'\n}\nexport class MethodNotSupportedRpcError extends RpcError {\n  override name = 'MethodNotSupportedRpcError'\n  static code = -32004 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: MethodNotSupportedRpcError.code,\n      shortMessage: 'Method is not implemented.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Limit exceeded\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type LimitExceededRpcErrorType = LimitExceededRpcError & {\n  code: -32005\n  name: 'LimitExceededRpcError'\n}\nexport class LimitExceededRpcError extends RpcError {\n  override name = 'LimitExceededRpcError'\n  static code = -32005 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: LimitExceededRpcError.code,\n      shortMessage: 'Request exceeds defined limit.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"JSON-RPC version not supported\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type JsonRpcVersionUnsupportedErrorType =\n  JsonRpcVersionUnsupportedError & {\n    code: -32006\n    name: 'JsonRpcVersionUnsupportedError'\n  }\nexport class JsonRpcVersionUnsupportedError extends RpcError {\n  override name = 'JsonRpcVersionUnsupportedError'\n  static code = -32006 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: JsonRpcVersionUnsupportedError.code,\n      shortMessage: 'Version of JSON-RPC protocol is not supported.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"User Rejected Request\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type UserRejectedRequestErrorType = UserRejectedRequestError & {\n  code: 4001\n  name: 'UserRejectedRequestError'\n}\nexport class UserRejectedRequestError extends ProviderRpcError {\n  override name = 'UserRejectedRequestError'\n  static code = 4001 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: UserRejectedRequestError.code,\n      shortMessage: 'User rejected the request.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Unauthorized\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type UnauthorizedProviderErrorType = UnauthorizedProviderError & {\n  code: 4100\n  name: 'UnauthorizedProviderError'\n}\nexport class UnauthorizedProviderError extends ProviderRpcError {\n  override name = 'UnauthorizedProviderError'\n  static code = 4100 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: UnauthorizedProviderError.code,\n      shortMessage:\n        'The requested method and/or account has not been authorized by the user.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Unsupported Method\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type UnsupportedProviderMethodErrorType =\n  UnsupportedProviderMethodError & {\n    code: 4200\n    name: 'UnsupportedProviderMethodError'\n  }\nexport class UnsupportedProviderMethodError extends ProviderRpcError {\n  override name = 'UnsupportedProviderMethodError'\n  static code = 4200 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: UnsupportedProviderMethodError.code,\n      shortMessage: 'The Provider does not support the requested method.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Disconnected\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type ProviderDisconnectedErrorType = ProviderDisconnectedError & {\n  code: 4900\n  name: 'ProviderDisconnectedError'\n}\nexport class ProviderDisconnectedError extends ProviderRpcError {\n  override name = 'ProviderDisconnectedError'\n  static code = 4900 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ProviderDisconnectedError.code,\n      shortMessage: 'The Provider is disconnected from all chains.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Chain Disconnected\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type ChainDisconnectedErrorType = ChainDisconnectedError & {\n  code: 4901\n  name: 'ChainDisconnectedError'\n}\nexport class ChainDisconnectedError extends ProviderRpcError {\n  override name = 'ChainDisconnectedError'\n  static code = 4901 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ChainDisconnectedError.code,\n      shortMessage: 'The Provider is not connected to the requested chain.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Switch Chain\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type SwitchChainErrorType = SwitchChainError & {\n  code: 4902\n  name: 'SwitchChainError'\n}\nexport class SwitchChainError extends ProviderRpcError {\n  override name = 'SwitchChainError'\n  static code = 4902 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: SwitchChainError.code,\n      shortMessage: 'An error occurred when attempting to switch chain.',\n    })\n  }\n}\n\n/**\n * Subclass for an unknown RPC error.\n */\nexport type UnknownRpcErrorType = UnknownRpcError & {\n  name: 'UnknownRpcError'\n}\nexport class UnknownRpcError extends RpcError {\n  override name = 'UnknownRpcError'\n\n  constructor(cause: Error) {\n    super(cause, {\n      shortMessage: 'An unknown RPC error occurred.',\n    })\n  }\n}\n", "import {\n  type ParseAccountErrorType,\n  parseAccount,\n} from '../../accounts/utils/parseAccount.js'\nimport type { SendTransactionParameters } from '../../actions/wallet/sendTransaction.js'\nimport {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../../errors/address.js'\nimport {\n  FeeCapTooHighError,\n  type FeeCapTooHighErrorType,\n  TipAboveFeeCapError,\n  type TipAboveFeeCapErrorType,\n} from '../../errors/node.js'\nimport {\n  FeeConflictError,\n  type FeeConflictErrorType,\n} from '../../errors/transaction.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\nimport { isAddress } from '../address/isAddress.js'\n\nexport type AssertRequestParameters = Partial<SendTransactionParameters<Chain>>\n\nexport type AssertRequestErrorType =\n  | InvalidAddressErrorType\n  | FeeConflictErrorType\n  | FeeCapTooHighErrorType\n  | ParseAccountErrorType\n  | TipAboveFeeCapErrorType\n  | ErrorType\n\nexport function assertRequest(args: AssertRequestParameters) {\n  const {\n    account: account_,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    to,\n  } = args\n  const account = account_ ? parseAccount(account_) : undefined\n  if (account && !isAddress(account.address))\n    throw new InvalidAddressError({ address: account.address })\n  if (to && !isAddress(to)) throw new InvalidAddressError({ address: to })\n  if (\n    typeof gasPrice !== 'undefined' &&\n    (typeof maxFeePerGas !== 'undefined' ||\n      typeof maxPriorityFeePerGas !== 'undefined')\n  )\n    throw new FeeConflictError()\n\n  if (maxFeePerGas && maxFeePerGas > 2n ** 256n - 1n)\n    throw new FeeCapTooHighError({ maxFeePerGas })\n  if (\n    maxPriorityFeePerGas &&\n    maxFeePerGas &&\n    maxPriorityFeePerGas > maxFeePerGas\n  )\n    throw new TipAboveFeeCapError({ maxFeePerGas, maxPriorityFeePerGas })\n}\n", "import type { Abi, ExtractAbiFunctionNames } from 'abitype'\n\nimport {\n  AbiFunctionNotFoundError,\n  type AbiFunctionNotFoundErrorType,\n  AbiFunctionOutputsNotFoundError,\n  type AbiFunctionOutputsNotFoundErrorType,\n} from '../../errors/abi.js'\nimport type {\n  AbiItem,\n  ContractFunctionResult,\n  GetFunctionArgs,\n  InferFunctionName,\n} from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type DecodeAbiParametersErrorType,\n  decodeAbiParameters,\n} from './decodeAbiParameters.js'\nimport {\n  type GetAbiItemErrorType,\n  type GetAbiItemParameters,\n  getAbiItem,\n} from './getAbiItem.js'\n\nconst docsPath = '/docs/contract/decodeFunctionResult'\n\nexport type DecodeFunctionResultParameters<\n  TAbi extends Abi | readonly unknown[] = Abi,\n  TFunctionName extends string | undefined = string,\n  _FunctionName = InferFunctionName<TAbi, TFunctionName>,\n> = {\n  functionName?: _FunctionName\n  data: Hex\n} & (TFunctionName extends string\n  ? { abi: TAbi } & Partial<GetFunctionArgs<TAbi, TFunctionName>>\n  : _FunctionName extends string\n    ? { abi: [TAbi[number]] } & Partial<GetFunctionArgs<TAbi, _FunctionName>>\n    : never)\n\nexport type DecodeFunctionResultReturnType<\n  TAbi extends Abi | readonly unknown[] = Abi,\n  TFunctionName extends string | undefined = string,\n  _FunctionName extends string = TAbi extends Abi\n    ? Abi extends TAbi\n      ? string\n      : ExtractAbiFunctionNames<TAbi>[number]\n    : string,\n> = TFunctionName extends string\n  ? ContractFunctionResult<TAbi, TFunctionName>\n  : ContractFunctionResult<TAbi, _FunctionName>\n\nexport type DecodeFunctionResultErrorType =\n  | AbiFunctionNotFoundErrorType\n  | AbiFunctionOutputsNotFoundErrorType\n  | DecodeAbiParametersErrorType\n  | GetAbiItemErrorType\n  | ErrorType\n\nexport function decodeFunctionResult<\n  const TAbi extends Abi | readonly unknown[],\n  TFunctionName extends string | undefined = undefined,\n>({\n  abi,\n  args,\n  functionName,\n  data,\n}: DecodeFunctionResultParameters<\n  TAbi,\n  TFunctionName\n>): DecodeFunctionResultReturnType<TAbi, TFunctionName> {\n  let abiItem = abi[0] as AbiItem\n  if (functionName) {\n    abiItem = getAbiItem({\n      abi,\n      args,\n      name: functionName,\n    } as GetAbiItemParameters)\n    if (!abiItem) throw new AbiFunctionNotFoundError(functionName, { docsPath })\n  }\n\n  if (abiItem.type !== 'function')\n    throw new AbiFunctionNotFoundError(undefined, { docsPath })\n  if (!abiItem.outputs)\n    throw new AbiFunctionOutputsNotFoundError(abiItem.name, { docsPath })\n\n  const values = decodeAbiParameters(abiItem.outputs, data)\n  if (values && values.length > 1) return values as any\n  if (values && values.length === 1) return values[0] as any\n  return undefined as any\n}\n", "/* [Multicall3](https://github.com/mds1/multicall) */\nexport const multicall3Abi = [\n  {\n    inputs: [\n      {\n        components: [\n          {\n            name: 'target',\n            type: 'address',\n          },\n          {\n            name: 'allowFailure',\n            type: 'bool',\n          },\n          {\n            name: 'callData',\n            type: 'bytes',\n          },\n        ],\n        name: 'calls',\n        type: 'tuple[]',\n      },\n    ],\n    name: 'aggregate3',\n    outputs: [\n      {\n        components: [\n          {\n            name: 'success',\n            type: 'bool',\n          },\n          {\n            name: 'returnData',\n            type: 'bytes',\n          },\n        ],\n        name: 'returnData',\n        type: 'tuple[]',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n] as const\n\nconst universalResolverErrors = [\n  {\n    inputs: [],\n    name: 'ResolverNotFound',\n    type: 'error',\n  },\n  {\n    inputs: [],\n    name: 'ResolverWildcardNotSupported',\n    type: 'error',\n  },\n] as const\n\nexport const universalResolverResolveAbi = [\n  ...universalResolverErrors,\n  {\n    name: 'resolve',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes' },\n      { name: 'data', type: 'bytes' },\n    ],\n    outputs: [\n      { name: '', type: 'bytes' },\n      { name: 'address', type: 'address' },\n    ],\n  },\n] as const\n\nexport const universalResolverReverseAbi = [\n  ...universalResolverErrors,\n  {\n    name: 'reverse',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [{ type: 'bytes', name: 'reverseName' }],\n    outputs: [\n      { type: 'string', name: 'resolvedName' },\n      { type: 'address', name: 'resolvedAddress' },\n      { type: 'address', name: 'reverseResolver' },\n      { type: 'address', name: 'resolver' },\n    ],\n  },\n] as const\n\nexport const textResolverAbi = [\n  {\n    name: 'text',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes32' },\n      { name: 'key', type: 'string' },\n    ],\n    outputs: [{ name: '', type: 'string' }],\n  },\n] as const\n\nexport const addressResolverAbi = [\n  {\n    name: 'addr',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [{ name: 'name', type: 'bytes32' }],\n    outputs: [{ name: '', type: 'address' }],\n  },\n  {\n    name: 'addr',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes32' },\n      { name: 'coinType', type: 'uint256' },\n    ],\n    outputs: [{ name: '', type: 'bytes' }],\n  },\n] as const\n\n// ERC-1271\n// isValidSignature(bytes32 hash, bytes signature) → bytes4 magicValue\nexport const smartAccountAbi = [\n  {\n    name: 'isValidSignature',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'hash', type: 'bytes32' },\n      { name: 'signature', type: 'bytes' },\n    ],\n    outputs: [{ name: '', type: 'bytes4' }],\n  },\n] as const\n\n// ERC-6492 - universal deployless signature validator contract\n// constructor(address _signer, bytes32 _hash, bytes _signature) → bytes4 returnValue\n// returnValue is either 0x1 (valid) or 0x0 (invalid)\nexport const universalSignatureValidatorAbi = [\n  {\n    inputs: [\n      {\n        internalType: 'address',\n        name: '_signer',\n        type: 'address',\n      },\n      {\n        internalType: 'bytes32',\n        name: '_hash',\n        type: 'bytes32',\n      },\n      {\n        internalType: 'bytes',\n        name: '_signature',\n        type: 'bytes',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'constructor',\n  },\n] as const\n", "import {\n  ChainDoesNotSupportContract,\n  type ChainDoesNotSupportContractErrorType,\n} from '../../errors/chain.js'\nimport type { Chain, ChainContract } from '../../types/chain.js'\n\nexport type GetChainContractAddressErrorType =\n  ChainDoesNotSupportContractErrorType\n\nexport function getChainContractAddress({\n  blockNumber,\n  chain,\n  contract: name,\n}: {\n  blockNumber?: bigint\n  chain: Chain\n  contract: string\n}) {\n  const contract = (chain?.contracts as Record<string, ChainContract>)?.[name]\n  if (!contract)\n    throw new ChainDoesNotSupportContract({\n      chain,\n      contract: { name },\n    })\n\n  if (\n    blockNumber &&\n    contract.blockCreated &&\n    contract.blockCreated > blockNumber\n  )\n    throw new ChainDoesNotSupportContract({\n      blockNumber,\n      chain,\n      contract: {\n        name,\n        blockCreated: contract.blockCreated,\n      },\n    })\n\n  return contract.address\n}\n", "export const aggregate3Signature = '0x82ad56cb'\n", "import type { SendTransactionParameters } from '../../actions/wallet/sendTransaction.js'\nimport { BaseError } from '../../errors/base.js'\nimport {\n  ExecutionRevertedError,\n  type ExecutionRevertedErrorType,\n  FeeCapTooHighError,\n  type FeeCapTooHighErrorType,\n  FeeCapTooLowError,\n  type FeeCapTooLowErrorType,\n  InsufficientFundsError,\n  type InsufficientFundsErrorType,\n  IntrinsicGasTooHighError,\n  type IntrinsicGasTooHighErrorType,\n  IntrinsicGasTooLowError,\n  type IntrinsicGasTooLowErrorType,\n  NonceMaxValueError,\n  type NonceMaxValueErrorType,\n  NonceTooHighError,\n  type NonceTooHighErrorType,\n  NonceTooLowError,\n  type NonceTooLowErrorType,\n  TipAboveFeeCapError,\n  type TipAboveFeeCapErrorType,\n  TransactionTypeNotSupportedError,\n  type TransactionTypeNotSupportedErrorType,\n  UnknownNodeError,\n  type UnknownNodeErrorType,\n} from '../../errors/node.js'\nimport { RpcRequestError } from '../../errors/request.js'\nimport {\n  InvalidInputRpcError,\n  TransactionRejectedRpcError,\n} from '../../errors/rpc.js'\n\nexport function containsNodeError(err: BaseError) {\n  return (\n    err instanceof TransactionRejectedRpcError ||\n    err instanceof InvalidInputRpcError ||\n    (err instanceof RpcRequestError && err.code === ExecutionRevertedError.code)\n  )\n}\n\nexport type GetNodeErrorParameters = Partial<SendTransactionParameters<any>>\n\nexport type GetNodeErrorReturnType =\n  | ExecutionRevertedErrorType\n  | FeeCapTooHighErrorType\n  | FeeCapTooLowErrorType\n  | NonceTooHighErrorType\n  | NonceTooLowErrorType\n  | NonceMaxValueErrorType\n  | InsufficientFundsErrorType\n  | IntrinsicGasTooHighErrorType\n  | IntrinsicGasTooLowErrorType\n  | TransactionTypeNotSupportedErrorType\n  | TipAboveFeeCapErrorType\n  | UnknownNodeErrorType\n\nexport function getNodeError(\n  err: BaseError,\n  args: GetNodeErrorParameters,\n): GetNodeErrorReturnType {\n  const message = (err.details || '').toLowerCase()\n\n  const executionRevertedError = err.walk(\n    (e) => (e as { code: number }).code === ExecutionRevertedError.code,\n  )\n  if (executionRevertedError instanceof BaseError) {\n    return new ExecutionRevertedError({\n      cause: err,\n      message: executionRevertedError.details,\n    }) as any\n  }\n  if (ExecutionRevertedError.nodeMessage.test(message))\n    return new ExecutionRevertedError({\n      cause: err,\n      message: err.details,\n    }) as any\n  if (FeeCapTooHighError.nodeMessage.test(message))\n    return new FeeCapTooHighError({\n      cause: err,\n      maxFeePerGas: args?.maxFeePerGas,\n    }) as any\n  if (FeeCapTooLowError.nodeMessage.test(message))\n    return new FeeCapTooLowError({\n      cause: err,\n      maxFeePerGas: args?.maxFeePerGas,\n    }) as any\n  if (NonceTooHighError.nodeMessage.test(message))\n    return new NonceTooHighError({ cause: err, nonce: args?.nonce }) as any\n  if (NonceTooLowError.nodeMessage.test(message))\n    return new NonceTooLowError({ cause: err, nonce: args?.nonce }) as any\n  if (NonceMaxValueError.nodeMessage.test(message))\n    return new NonceMaxValueError({ cause: err, nonce: args?.nonce }) as any\n  if (InsufficientFundsError.nodeMessage.test(message))\n    return new InsufficientFundsError({ cause: err }) as any\n  if (IntrinsicGasTooHighError.nodeMessage.test(message))\n    return new IntrinsicGasTooHighError({ cause: err, gas: args?.gas }) as any\n  if (IntrinsicGasTooLowError.nodeMessage.test(message))\n    return new IntrinsicGasTooLowError({ cause: err, gas: args?.gas }) as any\n  if (TransactionTypeNotSupportedError.nodeMessage.test(message))\n    return new TransactionTypeNotSupportedError({ cause: err }) as any\n  if (TipAboveFeeCapError.nodeMessage.test(message))\n    return new TipAboveFeeCapError({\n      cause: err,\n      maxFeePerGas: args?.maxFeePerGas,\n      maxPriorityFeePerGas: args?.maxPriorityFeePerGas,\n    }) as any\n  return new UnknownNodeError({\n    cause: err,\n  }) as any\n}\n", "import type { CallParameters } from '../../actions/public/call.js'\nimport type { BaseError } from '../../errors/base.js'\nimport {\n  CallExecutionError,\n  type CallExecutionErrorType,\n} from '../../errors/contract.js'\nimport { UnknownNodeError } from '../../errors/node.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\n\nimport {\n  type GetNodeErrorParameters,\n  type GetNodeErrorReturnType,\n  getNodeError,\n} from './getNodeError.js'\n\nexport type GetCallErrorReturnType<cause = ErrorType> = Omit<\n  CallExecutionErrorType,\n  'cause'\n> & {\n  cause: cause | GetNodeErrorReturnType\n}\n\nexport function getCallError<err extends ErrorType<string>>(\n  err: err,\n  {\n    docsPath,\n    ...args\n  }: CallParameters & {\n    chain?: Chain\n    docsPath?: string\n  },\n): GetCallErrorReturnType<err> {\n  const cause = (() => {\n    const cause = getNodeError(\n      err as {} as BaseError,\n      args as GetNodeErrorParameters,\n    )\n    if (cause instanceof UnknownNodeError) return err as {} as BaseError\n    return cause\n  })()\n  return new CallExecutionError(cause, {\n    docsPath,\n    ...args,\n  }) as GetCallErrorReturnType<err>\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ChainFormatter } from '../../types/chain.js'\n\nexport type ExtractErrorType = ErrorType\n\n/**\n * @description Picks out the keys from `value` that exist in the formatter..\n */\nexport function extract(\n  value_: Record<string, unknown>,\n  { format }: { format?: ChainFormatter['format'] },\n) {\n  if (!format) return {}\n\n  const value: Record<string, unknown> = {}\n  function extract_(formatted: Record<string, any>) {\n    const keys = Object.keys(formatted)\n    for (const key of keys) {\n      if (key in value_) value[key] = value_[key]\n      if (\n        formatted[key] &&\n        typeof formatted[key] === 'object' &&\n        !Array.isArray(formatted[key])\n      )\n        extract_(formatted[key])\n    }\n  }\n\n  const formatted = format(value_ || {})\n  extract_(formatted)\n\n  return value\n}\n", "import type { ErrorType } from '../../errors/utils.js'\n\ntype Resolved<TReturnType extends readonly unknown[] = any> = [\n  result: TReturnType[number],\n  results: TReturnType,\n]\n\ntype PendingPromise<TReturnType extends readonly unknown[] = any> = {\n  resolve?: (data: Resolved<TReturnType>) => void\n  reject?: (reason?: unknown) => void\n}\n\ntype SchedulerItem = { args: unknown; pendingPromise: PendingPromise }\n\ntype BatchResultsCompareFn<TResult = unknown> = (\n  a: TResult,\n  b: TResult,\n) => number\n\nexport type CreateBatchSchedulerArguments<\n  TParameters = unknown,\n  TReturnType extends readonly unknown[] = readonly unknown[],\n> = {\n  fn: (args: TParameters[]) => Promise<TReturnType>\n  id: number | string\n  shouldSplitBatch?: (args: TParameters[]) => boolean\n  wait?: number\n  sort?: BatchResultsCompareFn<TReturnType[number]>\n}\n\nexport type CreateBatchSchedulerReturnType<\n  TParameters = unknown,\n  TReturnType extends readonly unknown[] = readonly unknown[],\n> = {\n  flush: () => void\n  schedule: TParameters extends undefined\n    ? (args?: TParameters) => Promise<Resolved<TReturnType>>\n    : (args: TParameters) => Promise<Resolved<TReturnType>>\n}\n\nexport type CreateBatchSchedulerErrorType = ErrorType\n\nconst schedulerCache = /*#__PURE__*/ new Map<number | string, SchedulerItem[]>()\n\nexport function createBatchScheduler<\n  TParameters,\n  TReturnType extends readonly unknown[],\n>({\n  fn,\n  id,\n  shouldSplitBatch,\n  wait = 0,\n  sort,\n}: CreateBatchSchedulerArguments<\n  TParameters,\n  TReturnType\n>): CreateBatchSchedulerReturnType<TParameters, TReturnType> {\n  const exec = async () => {\n    const scheduler = getScheduler()\n    flush()\n\n    const args = scheduler.map(({ args }) => args)\n\n    if (args.length === 0) return\n\n    fn(args as TParameters[])\n      .then((data) => {\n        if (sort && Array.isArray(data)) data.sort(sort)\n        for (let i = 0; i < scheduler.length; i++) {\n          const { pendingPromise } = scheduler[i]\n          pendingPromise.resolve?.([data[i], data])\n        }\n      })\n      .catch((err) => {\n        for (let i = 0; i < scheduler.length; i++) {\n          const { pendingPromise } = scheduler[i]\n          pendingPromise.reject?.(err)\n        }\n      })\n  }\n\n  const flush = () => schedulerCache.delete(id)\n\n  const getBatchedArgs = () =>\n    getScheduler().map(({ args }) => args) as TParameters[]\n\n  const getScheduler = () => schedulerCache.get(id) || []\n\n  const setScheduler = (item: SchedulerItem) =>\n    schedulerCache.set(id, [...getScheduler(), item])\n\n  return {\n    flush,\n    async schedule(args: TParameters) {\n      const pendingPromise: PendingPromise<TReturnType> = {}\n      const promise = new Promise<Resolved<TReturnType>>((resolve, reject) => {\n        pendingPromise.resolve = resolve\n        pendingPromise.reject = reject\n      })\n\n      const split = shouldSplitBatch?.([...getBatchedArgs(), args])\n\n      if (split) exec()\n\n      const hasActiveScheduler = getScheduler().length > 0\n      if (hasActiveScheduler) {\n        setScheduler({ args, pendingPromise })\n        return promise\n      }\n\n      setScheduler({ args, pendingPromise })\n      setTimeout(exec, wait)\n      return promise\n    },\n  } as unknown as CreateBatchSchedulerReturnType<TParameters, TReturnType>\n}\n", "import type { Address } from 'abitype'\n\nimport type { Account } from '../../accounts/types.js'\nimport {\n  type ParseAccountErrorType,\n  parseAccount,\n} from '../../accounts/utils/parseAccount.js'\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport { multicall3Abi } from '../../constants/abis.js'\nimport { aggregate3Signature } from '../../constants/contract.js'\nimport { BaseError } from '../../errors/base.js'\nimport {\n  ChainDoesNotSupportContract,\n  ClientChainNotConfiguredError,\n} from '../../errors/chain.js'\nimport {\n  RawContractError,\n  type RawContractErrorType,\n} from '../../errors/contract.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { RpcTransactionRequest } from '../../types/rpc.js'\nimport type { TransactionRequest } from '../../types/transaction.js'\nimport type { UnionOmit } from '../../types/utils.js'\nimport {\n  type DecodeFunctionResultErrorType,\n  decodeFunctionResult,\n} from '../../utils/abi/decodeFunctionResult.js'\nimport {\n  type EncodeFunctionDataErrorType,\n  encodeFunctionData,\n} from '../../utils/abi/encodeFunctionData.js'\nimport type { RequestErrorType } from '../../utils/buildRequest.js'\nimport {\n  type GetChainContractAddressErrorType,\n  getChainContractAddress,\n} from '../../utils/chain/getChainContractAddress.js'\nimport {\n  type NumberToHexErrorType,\n  numberToHex,\n} from '../../utils/encoding/toHex.js'\nimport {\n  type GetCallErrorReturnType,\n  getCallError,\n} from '../../utils/errors/getCallError.js'\nimport { extract } from '../../utils/formatters/extract.js'\nimport {\n  type FormatTransactionRequestErrorType,\n  type FormattedTransactionRequest,\n  formatTransactionRequest,\n} from '../../utils/formatters/transactionRequest.js'\nimport {\n  type CreateBatchSchedulerErrorType,\n  createBatchScheduler,\n} from '../../utils/promise/createBatchScheduler.js'\nimport { assertRequest } from '../../utils/transaction/assertRequest.js'\nimport type {\n  AssertRequestErrorType,\n  AssertRequestParameters,\n} from '../../utils/transaction/assertRequest.js'\n\nexport type FormattedCall<\n  TChain extends Chain | undefined = Chain | undefined,\n> = FormattedTransactionRequest<TChain>\n\nexport type CallParameters<\n  TChain extends Chain | undefined = Chain | undefined,\n> = UnionOmit<FormattedCall<TChain>, 'from'> & {\n  account?: Account | Address\n  batch?: boolean\n} & (\n    | {\n        /** The balance of the account at a block number. */\n        blockNumber?: bigint\n        blockTag?: never\n      }\n    | {\n        blockNumber?: never\n        /**\n         * The balance of the account at a block tag.\n         * @default 'latest'\n         */\n        blockTag?: BlockTag\n      }\n  )\n\nexport type CallReturnType = { data: Hex | undefined }\n\nexport type CallErrorType = GetCallErrorReturnType<\n  | ParseAccountErrorType\n  | AssertRequestErrorType\n  | NumberToHexErrorType\n  | FormatTransactionRequestErrorType\n  | ScheduleMulticallErrorType\n  | RequestErrorType\n>\n\n/**\n * Executes a new message call immediately without submitting a transaction to the network.\n *\n * - Docs: https://viem.sh/docs/actions/public/call.html\n * - JSON-RPC Methods: [`eth_call`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_call)\n *\n * @param client - Client to use\n * @param parameters - {@link CallParameters}\n * @returns The call data. {@link CallReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { call } from 'viem/public'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const data = await call(client, {\n *   account: '******************************************',\n *   data: '******************************************',\n *   to: '******************************************',\n * })\n */\nexport async function call<TChain extends Chain | undefined>(\n  client: Client<Transport, TChain>,\n  args: CallParameters<TChain>,\n): Promise<CallReturnType> {\n  const {\n    account: account_ = client.account,\n    batch = Boolean(client.batch?.multicall),\n    blockNumber,\n    blockTag = 'latest',\n    accessList,\n    data,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    to,\n    value,\n    ...rest\n  } = args\n  const account = account_ ? parseAccount(account_) : undefined\n\n  try {\n    assertRequest(args as AssertRequestParameters)\n\n    const blockNumberHex = blockNumber ? numberToHex(blockNumber) : undefined\n    const block = blockNumberHex || blockTag\n\n    const chainFormat = client.chain?.formatters?.transactionRequest?.format\n    const format = chainFormat || formatTransactionRequest\n\n    const request = format({\n      // Pick out extra data that might exist on the chain's transaction request type.\n      ...extract(rest, { format: chainFormat }),\n      from: account?.address,\n      accessList,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to,\n      value,\n    } as TransactionRequest) as TransactionRequest\n\n    if (batch && shouldPerformMulticall({ request })) {\n      try {\n        return await scheduleMulticall(client, {\n          ...request,\n          blockNumber,\n          blockTag,\n        } as unknown as ScheduleMulticallParameters<TChain>)\n      } catch (err) {\n        if (\n          !(err instanceof ClientChainNotConfiguredError) &&\n          !(err instanceof ChainDoesNotSupportContract)\n        )\n          throw err\n      }\n    }\n\n    const response = await client.request({\n      method: 'eth_call',\n      params: block\n        ? [request as Partial<RpcTransactionRequest>, block]\n        : [request as Partial<RpcTransactionRequest>],\n    })\n    if (response === '0x') return { data: undefined }\n    return { data: response }\n  } catch (err) {\n    const data = getRevertErrorData(err)\n    const { offchainLookup, offchainLookupSignature } = await import(\n      '../../utils/ccip.js'\n    )\n    if (data?.slice(0, 10) === offchainLookupSignature && to) {\n      return { data: await offchainLookup(client, { data, to }) }\n    }\n    throw getCallError(err as ErrorType, {\n      ...args,\n      account,\n      chain: client.chain,\n    })\n  }\n}\n\n// We only want to perform a scheduled multicall if:\n// - The request has calldata,\n// - The request has a target address,\n// - The target address is not already the aggregate3 signature,\n// - The request has no other properties (`nonce`, `gas`, etc cannot be sent with a multicall).\nfunction shouldPerformMulticall({ request }: { request: TransactionRequest }) {\n  const { data, to, ...request_ } = request\n  if (!data) return false\n  if (data.startsWith(aggregate3Signature)) return false\n  if (!to) return false\n  if (\n    Object.values(request_).filter((x) => typeof x !== 'undefined').length > 0\n  )\n    return false\n  return true\n}\n\ntype ScheduleMulticallParameters<TChain extends Chain | undefined> = Pick<\n  CallParameters<TChain>,\n  'blockNumber' | 'blockTag'\n> & {\n  data: Hex\n  multicallAddress?: Address\n  to: Address\n}\n\nexport type ScheduleMulticallErrorType =\n  | GetChainContractAddressErrorType\n  | NumberToHexErrorType\n  | CreateBatchSchedulerErrorType\n  | EncodeFunctionDataErrorType\n  | DecodeFunctionResultErrorType\n  | RawContractErrorType\n  | ErrorType\n\nasync function scheduleMulticall<TChain extends Chain | undefined>(\n  client: Client<Transport>,\n  args: ScheduleMulticallParameters<TChain>,\n) {\n  const { batchSize = 1024, wait = 0 } =\n    typeof client.batch?.multicall === 'object' ? client.batch.multicall : {}\n  const {\n    blockNumber,\n    blockTag = 'latest',\n    data,\n    multicallAddress: multicallAddress_,\n    to,\n  } = args\n\n  let multicallAddress = multicallAddress_\n  if (!multicallAddress) {\n    if (!client.chain) throw new ClientChainNotConfiguredError()\n\n    multicallAddress = getChainContractAddress({\n      blockNumber,\n      chain: client.chain,\n      contract: 'multicall3',\n    })\n  }\n\n  const blockNumberHex = blockNumber ? numberToHex(blockNumber) : undefined\n  const block = blockNumberHex || blockTag\n\n  const { schedule } = createBatchScheduler({\n    id: `${client.uid}.${block}`,\n    wait,\n    shouldSplitBatch(args) {\n      const size = args.reduce((size, { data }) => size + (data.length - 2), 0)\n      return size > batchSize * 2\n    },\n    fn: async (\n      requests: {\n        data: Hex\n        to: Address\n      }[],\n    ) => {\n      const calls = requests.map((request) => ({\n        allowFailure: true,\n        callData: request.data,\n        target: request.to,\n      }))\n\n      const calldata = encodeFunctionData({\n        abi: multicall3Abi,\n        args: [calls],\n        functionName: 'aggregate3',\n      })\n\n      const data = await client.request({\n        method: 'eth_call',\n        params: [\n          {\n            data: calldata,\n            to: multicallAddress,\n          },\n          block,\n        ],\n      })\n\n      return decodeFunctionResult({\n        abi: multicall3Abi,\n        args: [calls],\n        functionName: 'aggregate3',\n        data: data || '0x',\n      })\n    },\n  })\n\n  const [{ returnData, success }] = await schedule({ data, to })\n\n  if (!success) throw new RawContractError({ data: returnData })\n  if (returnData === '0x') return { data: undefined }\n  return { data: returnData }\n}\n\nexport type GetRevertErrorDataErrorType = ErrorType\n\nexport function getRevertErrorData(err: unknown) {\n  if (!(err instanceof BaseError)) return undefined\n  const error = err.walk() as RawContractError\n  return typeof error.data === 'object' ? error.data.data : error.data\n}\n"], "mappings": ";;;;;;;;;;;;;AAAO,IAAM,UAAU;;;ACMhB,IAAM,qBAAqB,CAAC,YAAqB;AACjD,IAAM,SAAS,CAAC,QAAgB;AAChC,IAAM,aAAa,MAAM,QAAQ,OAAO;;;ACUzC,IAAO,YAAP,MAAO,mBAAkB,MAAK;EASlC,YAAY,cAAsB,OAA4B,CAAA,GAAE;AA3BlE;AA4BI,UAAK;AATP,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAES,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAChB,WAAA,eAAA,MAAA,WAAA;;;;aAAU,WAAU;;AAKlB,UAAM,UACJ,KAAK,iBAAiB,aAClB,KAAK,MAAM,YACX,UAAK,UAAL,mBAAY,WACV,KAAK,MAAM,UACX,KAAK;AACb,UAAMA,YACJ,KAAK,iBAAiB,aAClB,KAAK,MAAM,YAAY,KAAK,WAC5B,KAAK;AAEX,SAAK,UAAU;MACb,gBAAgB;MAChB;MACA,GAAI,KAAK,eAAe,CAAC,GAAG,KAAK,cAAc,EAAE,IAAI,CAAA;MACrD,GAAIA,YACA;QACE,wBAAwBA,SAAQ,QAC9B,KAAK,WAAW,IAAI,KAAK,QAAQ,KAAK,EACxC;UAEF,CAAA;MACJ,GAAI,UAAU,CAAC,YAAY,OAAO,EAAE,IAAI,CAAA;MACxC,YAAY,KAAK,OAAO;MACxB,KAAK,IAAI;AAEX,QAAI,KAAK;AAAO,WAAK,QAAQ,KAAK;AAClC,SAAK,UAAU;AACf,SAAK,WAAWA;AAChB,SAAK,eAAe,KAAK;AACzB,SAAK,eAAe;EACtB;EAIA,KAAK,IAAQ;AACX,WAAO,KAAK,MAAM,EAAE;EACtB;;AAGF,SAAS,KAAK,KAAc,IAA8B;AACxD,MAAI,yBAAK;AAAM,WAAO;AACtB,MAAI,OAAO,OAAO,QAAQ,YAAY,WAAW;AAC/C,WAAO,KAAK,IAAI,OAAO,EAAE;AAC3B,SAAO,KAAK,OAAO;AACrB;;;ACnEM,IAAO,yBAAP,cAAsC,UAAS;EAEnD,YAAY,EAAE,UAAU,OAAM,GAAwC;AACpE,UACE,mBAAmB,QAAQ,kCACzB,SAAS,CACX,IAAI;AALC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;AAOI,IAAO,0BAAP,cAAuC,UAAS;EAEpD,YAAY,EAAE,QAAQ,WAAU,GAA0C;AACxE,UACE,gBAAgB,aAAa,CAAC,4CAC5B,SAAS,CACX,IAAI;AALC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;AAMI,IAAO,yBAAP,cAAsC,UAAS;EAEnD,YAAY,EACV,KACA,KACA,QACA,MAAAC,OACA,MAAK,GAON;AACC,UACE,WAAW,KAAK,oBACdA,QAAO,GAAGA,QAAO,CAAC,QAAQ,SAAS,WAAW,UAAU,MAAM,EAChE,iBAAiB,MAAM,IAAI,GAAG,OAAO,GAAG,MAAM,UAAU,GAAG,GAAG,EAAE;AAjB3D,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAmBhB;;AAMI,IAAO,2BAAP,cAAwC,UAAS;EAErD,YAAYC,QAAgB;AAC1B,UACE,gBAAgBA,MAAK,gGAAgG;AAHhH,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;AAMI,IAAO,yBAAP,cAAsC,UAAS;EAEnD,YAAY,KAAQ;AAClB,UACE,cAAc,GAAG,gFAAgF;AAH5F,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;AAMI,IAAO,uBAAP,cAAoC,UAAS;EAEjD,YAAY,OAAU;AACpB,UACE,cAAc,KAAK,uBAAuB,MAAM,MAAM,+BAA+B;AAHhF,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;AAOI,IAAO,yBAAP,cAAsC,UAAS;EAEnD,YAAY,EAAE,YAAY,OAAM,GAA0C;AACxE,UACE,gBAAgB,UAAU,uDAAuD,MAAM,GAAG;AAHrF,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;AAMI,IAAO,oBAAP,cAAiC,UAAS;EAE9C,YAAY,EAAE,WAAW,QAAO,GAA0C;AACxE,UACE,sBAAsB,OAAO,uBAAuB,SAAS,SAAS;AAHjE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;;;ACjHI,SAAU,MACd,OACA,EAAE,SAAS,KAAI,IAA2B,CAAA,GAAE;AAE5C,MAAI,CAAC;AAAO,WAAO;AACnB,MAAI,OAAO,UAAU;AAAU,WAAO;AACtC,SAAO,SAAS,mBAAmB,KAAK,KAAK,IAAI,MAAM,WAAW,IAAI;AACxE;;;ACCM,SAAU,KAAK,OAAsB;AACzC,MAAI,MAAM,OAAO,EAAE,QAAQ,MAAK,CAAE;AAAG,WAAO,KAAK,MAAM,MAAM,SAAS,KAAK,CAAC;AAC5E,SAAO,MAAM;AACf;;;ACJM,SAAU,KACd,YACA,EAAE,MAAM,OAAM,IAAkB,CAAA,GAAE;AAElC,MAAI,OACF,OAAO,eAAe,WAAW,WAAW,QAAQ,MAAM,EAAE,IAAI;AAElE,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,QAAI,KAAK,QAAQ,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,SAAQ,MAAO;AAChE;;AACG;;AAEP,SACE,QAAQ,SACJ,KAAK,MAAM,WAAW,IACtB,KAAK,MAAM,GAAG,KAAK,SAAS,WAAW;AAE7C,MAAI,OAAO,eAAe,UAAU;AAClC,QAAI,KAAK,WAAW,KAAK,QAAQ;AAAS,aAAO,GAAG,IAAI;AACxD,WAAO,KACL,KAAK,SAAS,MAAM,IAAI,IAAI,IAAI,KAAK,IACvC;;AAEF,SAAO;AACT;;;AChCM,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,EACV,QACA,UACA,MAAAC,MAAI,GACwD;AAC5D,UACE,SACE,aAAa,UAAU,aAAa,QACtC,eAAe,MAAM,6BAA6BA,KAAI,IAAI;AATrD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,EACV,MAAAA,OACA,YACA,KAAI,GAKL;AACC,UACE,GAAG,KAAK,OAAO,CAAC,EAAE,YAAW,CAAE,GAAG,KAC/B,MAAM,CAAC,EACP,YAAW,CAAE,UAAUA,KAAI,2BAA2B,UAAU,IAAI;AAblE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAehB;;;;ACtBI,SAAU,IACd,YACA,EAAE,KAAK,MAAAC,QAAO,GAAE,IAAiB,CAAA,GAAE;AAEnC,MAAI,OAAO,eAAe;AACxB,WAAO,OAAO,YAAY,EAAE,KAAK,MAAAA,MAAI,CAAE;AACzC,SAAO,SAAS,YAAY,EAAE,KAAK,MAAAA,MAAI,CAAE;AAC3C;AAIM,SAAU,OAAO,MAAW,EAAE,KAAK,MAAAA,QAAO,GAAE,IAAiB,CAAA,GAAE;AACnE,MAAIA,UAAS;AAAM,WAAO;AAC1B,QAAM,MAAM,KAAK,QAAQ,MAAM,EAAE;AACjC,MAAI,IAAI,SAASA,QAAO;AACtB,UAAM,IAAI,4BAA4B;MACpC,MAAM,KAAK,KAAK,IAAI,SAAS,CAAC;MAC9B,YAAYA;MACZ,MAAM;KACP;AAEH,SAAO,KAAK,IAAI,QAAQ,UAAU,WAAW,UAAU,EACrDA,QAAO,GACP,GAAG,CACJ;AACH;AAIM,SAAU,SACdC,QACA,EAAE,KAAK,MAAAD,QAAO,GAAE,IAAiB,CAAA,GAAE;AAEnC,MAAIA,UAAS;AAAM,WAAOC;AAC1B,MAAIA,OAAM,SAASD;AACjB,UAAM,IAAI,4BAA4B;MACpC,MAAMC,OAAM;MACZ,YAAYD;MACZ,MAAM;KACP;AACH,QAAM,cAAc,IAAI,WAAWA,KAAI;AACvC,WAAS,IAAI,GAAG,IAAIA,OAAM,KAAK;AAC7B,UAAM,SAAS,QAAQ;AACvB,gBAAY,SAAS,IAAIA,QAAO,IAAI,CAAC,IACnCC,OAAM,SAAS,IAAIA,OAAM,SAAS,IAAI,CAAC;;AAE3C,SAAO;AACT;;;ACnDA,IAAM,UAAwB,IAAI,YAAW;AAwCvC,SAAUC,SACd,OACA,OAA0B,CAAA,GAAE;AAE5B,MAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAChD,WAAO,cAAc,OAAO,IAAI;AAClC,MAAI,OAAO,UAAU;AAAW,WAAO,YAAY,OAAO,IAAI;AAC9D,MAAI,MAAM,KAAK;AAAG,WAAO,WAAW,OAAO,IAAI;AAC/C,SAAO,cAAc,OAAO,IAAI;AAClC;AA+BM,SAAU,YAAY,OAAgB,OAAwB,CAAA,GAAE;AACpE,QAAMC,SAAQ,IAAI,WAAW,CAAC;AAC9B,EAAAA,OAAM,CAAC,IAAI,OAAO,KAAK;AACvB,MAAI,OAAO,KAAK,SAAS,UAAU;AACjC,eAAWA,QAAO,EAAE,MAAM,KAAK,KAAI,CAAE;AACrC,WAAO,IAAIA,QAAO,EAAE,MAAM,KAAK,KAAI,CAAE;;AAEvC,SAAOA;AACT;AAGA,IAAM,cAAc;EAClB,MAAM;EACN,MAAM;EACN,GAAG;EACH,GAAG;EACH,GAAG;EACH,GAAG;;AAGL,SAAS,iBAAiB,MAAY;AACpC,MAAI,QAAQ,YAAY,QAAQ,QAAQ,YAAY;AAClD,WAAO,OAAO,YAAY;AAC5B,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,SAAO;AACT;AA4BM,SAAU,WAAW,MAAW,OAAuB,CAAA,GAAE;AAC7D,MAAI,MAAM;AACV,MAAI,KAAK,MAAM;AACb,eAAW,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;AACnC,UAAM,IAAI,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAI,CAAE;;AAGlD,MAAI,YAAY,IAAI,MAAM,CAAC;AAC3B,MAAI,UAAU,SAAS;AAAG,gBAAY,IAAI,SAAS;AAEnD,QAAM,SAAS,UAAU,SAAS;AAClC,QAAMA,SAAQ,IAAI,WAAW,MAAM;AACnC,WAAS,QAAQ,GAAG,IAAI,GAAG,QAAQ,QAAQ,SAAS;AAClD,UAAM,aAAa,iBAAiB,UAAU,WAAW,GAAG,CAAC;AAC7D,UAAM,cAAc,iBAAiB,UAAU,WAAW,GAAG,CAAC;AAC9D,QAAI,eAAe,UAAa,gBAAgB,QAAW;AACzD,YAAM,IAAI,UACR,2BAA2B,UAAU,IAAI,CAAC,CAAC,GACzC,UAAU,IAAI,CAAC,CACjB,SAAS,SAAS,KAAK;;AAG3B,IAAAA,OAAM,KAAK,IAAI,aAAa,KAAK;;AAEnC,SAAOA;AACT;AA0BM,SAAU,cAAc,OAAwB,MAAsB;AAC1E,QAAM,MAAM,YAAY,OAAO,IAAI;AACnC,SAAO,WAAW,GAAG;AACvB;AA+BM,SAAU,cACd,OACA,OAA0B,CAAA,GAAE;AAE5B,QAAMA,SAAQ,QAAQ,OAAO,KAAK;AAClC,MAAI,OAAO,KAAK,SAAS,UAAU;AACjC,eAAWA,QAAO,EAAE,MAAM,KAAK,KAAI,CAAE;AACrC,WAAO,IAAIA,QAAO,EAAE,KAAK,SAAS,MAAM,KAAK,KAAI,CAAE;;AAErD,SAAOA;AACT;;;AClOM,SAAU,WACd,YACA,EAAE,MAAAC,MAAI,GAAoB;AAE1B,MAAI,KAAM,UAAU,IAAIA;AACtB,UAAM,IAAI,kBAAkB;MAC1B,WAAW,KAAM,UAAU;MAC3B,SAASA;KACV;AACL;AA6DM,SAAU,QAEd,KAAU,UAAgC;AAC1C,QAAM,OAAO,OAAO,aAAa,WAAW,EAAE,IAAI,SAAQ,IAAK;AAC/D,QAAM,KAAK,KAAK;AAEhB,MAAI,OAAO;AAAU,WAAO,YAAY,KAAK,IAAI;AACjD,MAAI,OAAO;AAAU,WAAO,YAAY,KAAK,IAAI;AACjD,MAAI,OAAO;AAAU,WAAO,YAAY,KAAK,IAAI;AACjD,MAAI,OAAO;AAAW,WAAO,UAAU,KAAK,IAAI;AAChD,SAAO,WAAW,KAAK,IAAI;AAC7B;AA8BM,SAAU,YAAY,KAAU,OAAwB,CAAA,GAAE;AAC9D,QAAM,EAAE,OAAM,IAAK;AAEnB,MAAI,KAAK;AAAM,eAAW,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;AAElD,QAAM,QAAQ,OAAO,GAAG;AACxB,MAAI,CAAC;AAAQ,WAAO;AAEpB,QAAMA,SAAQ,IAAI,SAAS,KAAK;AAChC,QAAM,OAAO,MAAO,OAAOA,KAAI,IAAI,KAAK,MAAO;AAC/C,MAAI,SAAS;AAAK,WAAO;AAEzB,SAAO,QAAQ,OAAO,KAAK,IAAI,SAASA,QAAO,GAAG,GAAG,CAAC,EAAE,IAAI;AAC9D;AAgCM,SAAU,UAAU,MAAW,OAAsB,CAAA,GAAE;AAC3D,MAAI,MAAM;AACV,MAAI,KAAK,MAAM;AACb,eAAW,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;AACnC,UAAM,KAAK,GAAG;;AAEhB,MAAI,KAAK,GAAG,MAAM;AAAQ,WAAO;AACjC,MAAI,KAAK,GAAG,MAAM;AAAQ,WAAO;AACjC,QAAM,IAAI,uBAAuB,GAAG;AACtC;AAyBM,SAAU,YAAY,KAAU,OAAwB,CAAA,GAAE;AAC9D,SAAO,OAAO,YAAY,KAAK,IAAI,CAAC;AACtC;AAkCM,SAAU,YAAY,KAAU,OAAwB,CAAA,GAAE;AAC9D,MAAIC,SAAQ,WAAW,GAAG;AAC1B,MAAI,KAAK,MAAM;AACb,eAAWA,QAAO,EAAE,MAAM,KAAK,KAAI,CAAE;AACrC,IAAAA,SAAQ,KAAKA,QAAO,EAAE,KAAK,QAAO,CAAE;;AAEtC,SAAO,IAAI,YAAW,EAAG,OAAOA,MAAK;AACvC;;;ACjPA,IAAM,QAAsB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,IAAI,MAC3D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAwC3B,SAAU,MACd,OACA,OAAwB,CAAA,GAAE;AAE1B,MAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAChD,WAAO,YAAY,OAAO,IAAI;AAChC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,YAAY,OAAO,IAAI;;AAEhC,MAAI,OAAO,UAAU;AAAW,WAAO,UAAU,OAAO,IAAI;AAC5D,SAAO,WAAW,OAAO,IAAI;AAC/B;AAiCM,SAAU,UAAU,OAAgB,OAAsB,CAAA,GAAE;AAChE,QAAM,MAAW,KAAK,OAAO,KAAK,CAAC;AACnC,MAAI,OAAO,KAAK,SAAS,UAAU;AACjC,eAAW,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;AACnC,WAAO,IAAI,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;;AAErC,SAAO;AACT;AA4BM,SAAU,WAAW,OAAkB,OAAuB,CAAA,GAAE;AACpE,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAU,MAAM,MAAM,CAAC,CAAC;;AAE1B,QAAM,MAAM,KAAK,MAAM;AAEvB,MAAI,OAAO,KAAK,SAAS,UAAU;AACjC,eAAW,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;AACnC,WAAO,IAAI,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAI,CAAE;;AAEnD,SAAO;AACT;AAuCM,SAAU,YACd,QACA,OAAwB,CAAA,GAAE;AAE1B,QAAM,EAAE,QAAQ,MAAAC,MAAI,IAAK;AAEzB,QAAM,QAAQ,OAAO,MAAM;AAE3B,MAAI;AACJ,MAAIA,OAAM;AACR,QAAI;AAAQ,kBAAY,MAAO,OAAOA,KAAI,IAAI,KAAK,MAAO;;AACrD,iBAAW,OAAO,OAAOA,KAAI,IAAI,MAAM;aACnC,OAAO,WAAW,UAAU;AACrC,eAAW,OAAO,OAAO,gBAAgB;;AAG3C,QAAM,WAAW,OAAO,aAAa,YAAY,SAAS,CAAC,WAAW,KAAK;AAE3E,MAAK,YAAY,QAAQ,YAAa,QAAQ,UAAU;AACtD,UAAM,SAAS,OAAO,WAAW,WAAW,MAAM;AAClD,UAAM,IAAI,uBAAuB;MAC/B,KAAK,WAAW,GAAG,QAAQ,GAAG,MAAM,KAAK;MACzC,KAAK,GAAG,QAAQ,GAAG,MAAM;MACzB;MACA,MAAAA;MACA,OAAO,GAAG,MAAM,GAAG,MAAM;KAC1B;;AAGH,QAAM,MAAM,MAAM,UAAU,QAAQ,KAC/B,MAAM,OAAOA,QAAO,CAAC,KAAK,OAAO,KAAK,IACvC,OACF,SAAS,EAAE,CAAC;AACd,MAAIA;AAAM,WAAO,IAAI,KAAK,EAAE,MAAAA,MAAI,CAAE;AAClC,SAAO;AACT;AASA,IAAMC,WAAwB,IAAI,YAAW;AAqBvC,SAAU,YAAY,QAAgB,OAAwB,CAAA,GAAE;AACpE,QAAM,QAAQA,SAAQ,OAAO,MAAM;AACnC,SAAO,WAAW,OAAO,IAAI;AAC/B;;;ACpPM,SAAU,gBACd,MACA,QAAuC;AAEvC,SAAO,CAIL,EACA,SACA,QAAQ,UAAS,MAId;AACH,WAAO;MACL;MACA,QAAQ,CAAC,SAAkD;AACzD,cAAM,YAAY,OAAO,IAAW;AACpC,YAAI,SAAS;AACX,qBAAW,OAAO,SAAS;AACzB,mBAAQ,UAAkB,GAAG;;;AAGjC,eAAO;UACL,GAAG;UACH,GAAG,UAAU,IAAI;;MAIrB;MACA;;EAEJ;AACF;;;ACrBO,IAAM,qBAAqB;EAChC,QAAQ;EACR,SAAS;EACT,SAAS;;AAKL,SAAU,yBACd,oBAA+C;AAE/C,SAAO;IACL,GAAG;IACH,KACE,OAAO,mBAAmB,QAAQ,cAC9B,YAAY,mBAAmB,GAAG,IAClC;IACN,UACE,OAAO,mBAAmB,aAAa,cACnC,YAAY,mBAAmB,QAAQ,IACvC;IACN,cACE,OAAO,mBAAmB,iBAAiB,cACvC,YAAY,mBAAmB,YAAY,IAC3C;IACN,sBACE,OAAO,mBAAmB,yBAAyB,cAC/C,YAAY,mBAAmB,oBAAoB,IACnD;IACN,OACE,OAAO,mBAAmB,UAAU,cAChC,YAAY,mBAAmB,KAAK,IACpC;IACN,MACE,OAAO,mBAAmB,SAAS,cAC/B,mBAAmB,mBAAmB,IAAI,IAC1C;IACN,OACE,OAAO,mBAAmB,UAAU,cAChC,YAAY,mBAAmB,KAAK,IACpC;;AAEV;AAMO,IAAM,2BAAyC,gBACpD,sBACA,wBAAwB;;;AC/DpB,IAAO,sBAAP,cAAmC,UAAS;EAEhD,YAAY,EAAE,QAAO,GAAuB;AAC1C,UAAM,YAAY,OAAO,eAAe;AAFjC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;;;ACDI,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,EACV,aACA,OACA,SAAQ,GAKT;AACC,UACE,UAAU,MAAM,IAAI,gCAAgC,SAAS,IAAI,MACjE;MACE,cAAc;QACZ;QACA,GAAI,eACJ,SAAS,gBACT,SAAS,eAAe,cACpB;UACE,mBAAmB,SAAS,IAAI,kCAAkC,SAAS,YAAY,mBAAmB,WAAW;YAEvH;UACE,2CAA2C,SAAS,IAAI;;;KAGjE;AAzBI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EA2BhB;;AAMI,IAAO,qBAAP,cAAkC,UAAS;EAG/C,YAAY,EACV,OACA,eAAc,GAIf;AACC,UACE,wCAAwC,cAAc,8DAA8D,MAAM,EAAE,MAAM,MAAM,IAAI,MAC5I;MACE,cAAc;QACZ,sBAAsB,cAAc;QACpC,sBAAsB,MAAM,EAAE,MAAM,MAAM,IAAI;;KAEjD;AAhBI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAkBhB;;AAMI,IAAO,qBAAP,cAAkC,UAAS;EAG/C,cAAA;AACE,UACE;MACE;MACA;MACA,KAAK,IAAI,CAAC;AAPP,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AAOI,IAAO,gCAAP,cAA6C,UAAS;EAG1D,cAAA;AACE,UAAM,sCAAsC;AAHrC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIhB;;AAMI,IAAO,sBAAP,cAAmC,UAAS;EAGhD,YAAY,EAAE,QAAO,GAAuB;AAC1C,UAAM,aAAa,OAAO,eAAe;AAHlC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIhB;;;;ACpGK,IAAM,aAAa;EACxB,MAAM;EACN,KAAK;;AAEA,IAAM,YAAY;EACvB,OAAO;EACP,KAAK;;AAEA,IAAM,WAAW;EACtB,OAAO;EACP,MAAM;;;;ACKF,SAAU,YAAY,OAAe,UAAgB;AACzD,MAAI,UAAU,MAAM,SAAQ;AAE5B,QAAM,WAAW,QAAQ,WAAW,GAAG;AACvC,MAAI;AAAU,cAAU,QAAQ,MAAM,CAAC;AAEvC,YAAU,QAAQ,SAAS,UAAU,GAAG;AAExC,MAAI,CAAC,SAAS,QAAQ,IAAI;IACxB,QAAQ,MAAM,GAAG,QAAQ,SAAS,QAAQ;IAC1C,QAAQ,MAAM,QAAQ,SAAS,QAAQ;;AAEzC,aAAW,SAAS,QAAQ,SAAS,EAAE;AACvC,SAAO,GAAG,WAAW,MAAM,EAAE,GAAG,WAAW,GAAG,GAC5C,WAAW,IAAI,QAAQ,KAAK,EAC9B;AACF;;;ACdM,SAAU,WAAW,KAAa,OAAc,OAAK;AACzD,SAAO,YAAY,KAAK,UAAU,IAAI,CAAC;AACzC;;;ACFA,IAAa,yBAAb,cAA4C,UAAS;EAMnD,YAAY,EACV,OACA,QAAO,IACoC,CAAA,GAAE;AA1BjD;AA2BI,UAAM,UAAS,wCACX,QAAQ,wBAAwB,QADrB,mBAEX,QAAQ,sBAAsB;AAClC,UACE,sBACE,SAAS,gBAAgB,MAAM,KAAK,uBACtC,KACA;MACE;KACD;AAfI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAiBhB;;AApBO,OAAA,eAAA,wBAAA,QAAA;;;;SAAO;;AACP,OAAA,eAAA,wBAAA,eAAA;;;;SAAc;;AAyBvB,IAAa,qBAAb,cAAwC,UAAS;EAI/C,YAAY,EACV,OACA,aAAY,IACoC,CAAA,GAAE;AAClD,UACE,gCACE,eAAe,MAAM,WAAW,YAAY,CAAC,UAAU,EACzD,gEACA;MACE;KACD;AAXI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAahB;;AAfO,OAAA,eAAA,oBAAA,eAAA;;;;SACL;;AAoBJ,IAAa,oBAAb,cAAuC,UAAS;EAI9C,YAAY,EACV,OACA,aAAY,IACoC,CAAA,GAAE;AAClD,UACE,gCACE,eAAe,MAAM,WAAW,YAAY,CAAC,KAAK,EACpD,mDACA;MACE;KACD;AAXI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAahB;;AAfO,OAAA,eAAA,mBAAA,eAAA;;;;SACL;;AAoBJ,IAAa,oBAAb,cAAuC,UAAS;EAG9C,YAAY,EAAE,OAAO,MAAK,IAA4C,CAAA,GAAE;AACtE,UACE,sCACE,QAAQ,IAAI,KAAK,OAAO,EAC1B,yCACA,EAAE,MAAK,CAAE;AANJ,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AATO,OAAA,eAAA,mBAAA,eAAA;;;;SAAc;;AAevB,IAAa,mBAAb,cAAsC,UAAS;EAI7C,YAAY,EAAE,OAAO,MAAK,IAA4C,CAAA,GAAE;AACtE,UACE;MACE,sCACE,QAAQ,IAAI,KAAK,OAAO,EAC1B;MACA;MACA,KAAK,IAAI,GACX,EAAE,MAAK,CAAE;AATJ,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;AAbO,OAAA,eAAA,kBAAA,eAAA;;;;SACL;;AAkBJ,IAAa,qBAAb,cAAwC,UAAS;EAG/C,YAAY,EAAE,OAAO,MAAK,IAA4C,CAAA,GAAE;AACtE,UACE,sCACE,QAAQ,IAAI,KAAK,OAAO,EAC1B,sCACA,EAAE,MAAK,CAAE;AANJ,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AATO,OAAA,eAAA,oBAAA,eAAA;;;;SAAc;;AAevB,IAAa,yBAAb,cAA4C,UAAS;EAGnD,YAAY,EAAE,MAAK,IAA4B,CAAA,GAAE;AAC/C,UACE;MACE;MACA,KAAK,IAAI,GACX;MACE;MACA,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;KAEH;AAlBI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAoBhB;;AArBO,OAAA,eAAA,wBAAA,eAAA;;;;SAAc;;AA2BvB,IAAa,2BAAb,cAA8C,UAAS;EAGrD,YAAY,EAAE,OAAO,IAAG,IAA0C,CAAA,GAAE;AAClE,UACE,qBACE,MAAM,IAAI,GAAG,OAAO,EACtB,yEACA;MACE;KACD;AARI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAUhB;;AAXO,OAAA,eAAA,0BAAA,eAAA;;;;SAAc;;AAiBvB,IAAa,0BAAb,cAA6C,UAAS;EAGpD,YAAY,EAAE,OAAO,IAAG,IAA0C,CAAA,GAAE;AAClE,UACE,qBACE,MAAM,IAAI,GAAG,OAAO,EACtB,4CACA;MACE;KACD;AARI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAUhB;;AAXO,OAAA,eAAA,yBAAA,eAAA;;;;SAAc;;AAkBvB,IAAa,mCAAb,cAAsD,UAAS;EAG7D,YAAY,EAAE,MAAK,GAAyB;AAC1C,UAAM,yDAAyD;MAC7D;KACD;AAJM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;AANO,OAAA,eAAA,kCAAA,eAAA;;;;SAAc;;AAYvB,IAAa,sBAAb,cAAyC,UAAS;EAIhD,YAAY,EACV,OACA,sBACA,aAAY,IAKV,CAAA,GAAE;AACJ,UACE;MACE,6CACE,uBACI,MAAM,WAAW,oBAAoB,CAAC,UACtC,EACN,wDACE,eAAe,MAAM,WAAW,YAAY,CAAC,UAAU,EACzD;MACA,KAAK,IAAI,GACX;MACE;KACD;AAtBI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAwBhB;;AA1BO,OAAA,eAAA,qBAAA,eAAA;;;;SACL;;AA+BE,IAAO,mBAAP,cAAgC,UAAS;EAG7C,YAAY,EAAE,MAAK,GAAyB;AAC1C,UAAM,sCAAsC,+BAAO,YAAY,IAAI;MACjE;KACD;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;;;AC/PF,IAAM,eAAe;AAIf,SAAU,UAAU,SAAe;AACvC,SAAO,aAAa,KAAK,OAAO;AAClC;;;ACEM,SAAU,OACd,QAAyB;AAEzB,MAAI,OAAO,OAAO,CAAC,MAAM;AACvB,WAAO,UAAU,MAAwB;AAC3C,SAAO,YAAY,MAA8B;AACnD;AAIM,SAAU,YAAY,QAA4B;AACtD,MAAI,SAAS;AACb,aAAW,OAAO,QAAQ;AACxB,cAAU,IAAI;;AAEhB,QAAM,SAAS,IAAI,WAAW,MAAM;AACpC,MAAI,SAAS;AACb,aAAW,OAAO,QAAQ;AACxB,WAAO,IAAI,KAAK,MAAM;AACtB,cAAU,IAAI;;AAEhB,SAAO;AACT;AAIM,SAAU,UAAU,QAAsB;AAC9C,SAAO,KAAM,OAAiB,OAC5B,CAAC,KAAK,MAAM,MAAM,EAAE,QAAQ,MAAM,EAAE,GACpC,EAAE,CACH;AACH;;;AC1CO,IAAMC,WAAU;;;ACUjB,IAAOC,aAAP,MAAO,mBAAkB,MAAK;EAQlC,YAAY,cAAsB,OAAsB,CAAA,GAAE;AAjB5D;AAkBI,UAAM,UACJ,KAAK,iBAAiB,aAClB,KAAK,MAAM,YACX,UAAK,UAAL,mBAAY,WACZ,KAAK,MAAM,UACX,KAAK;AACX,UAAMC,YACJ,KAAK,iBAAiB,aAClB,KAAK,MAAM,YAAY,KAAK,WAC5B,KAAK;AACX,UAAM,UAAU;MACd,gBAAgB;MAChB;MACA,GAAI,KAAK,eAAe,CAAC,GAAG,KAAK,cAAc,EAAE,IAAI,CAAA;MACrD,GAAIA,YAAW,CAAC,4BAA4BA,SAAQ,EAAE,IAAI,CAAA;MAC1D,GAAI,UAAU,CAAC,YAAY,OAAO,EAAE,IAAI,CAAA;MACxC,oBAAoBC,QAAO;MAC3B,KAAK,IAAI;AAEX,UAAM,OAAO;AA3Bf,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAES,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAwBd,QAAI,KAAK;AAAO,WAAK,QAAQ,KAAK;AAClC,SAAK,UAAU;AACf,SAAK,WAAWD;AAChB,SAAK,eAAe,KAAK;AACzB,SAAK,eAAe;EACtB;;;;AC3CI,IAAO,sBAAP,cAAmCE,WAAS;EAGhD,YAAY,EAAE,UAAS,GAAkC;AACvD,UAAM,6BAA6B;MACjC,SAAS,gBAAgB,KAAK,UAAU,WAAW,MAAM,CAAC,CAAC;MAC3D,UAAU;KACX;AANM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;AAGI,IAAO,mBAAP,cAAgCA,WAAS;EAG7C,YAAY,EAAE,KAAI,GAAoB;AACpC,UAAM,iBAAiB;MACrB,cAAc;QACZ,SAAS,IAAI;;KAEhB;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAGI,IAAO,2BAAP,cAAwCA,WAAS;EAGrD,YAAY,EAAE,KAAI,GAAoB;AACpC,UAAM,iBAAiB;MACrB,cAAc,CAAC,SAAS,IAAI,4BAA4B;KACzD;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;;;AC5BI,IAAO,2BAAP,cAAwCC,WAAS;EAGrD,YAAY,EAAE,MAAK,GAA8B;AAC/C,UAAM,kCAAkC;MACtC,SAAS,qBAAqB,KAAK,UAAU,OAAO,MAAM,CAAC,CAAC;MAC5D,UAAU;KACX;AANM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;AAGI,IAAO,4BAAP,cAAyCA,WAAS;EAGtD,YAAY,EAAE,OAAM,GAA+B;AACjD,UAAM,mCAAmC;MACvC,SAAS,sBAAsB,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;MAC9D,UAAU;KACX;AANM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;AAGI,IAAO,wBAAP,cAAqCA,WAAS;EAGlD,YAAY,EAAE,MAAK,GAAqB;AACtC,UAAM,0BAA0B;MAC9B,SAAS;KACV;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;AAGI,IAAO,gCAAP,cAA6CA,WAAS;EAG1D,YAAY,EAAE,OAAO,KAAI,GAAmC;AAC1D,UAAM,0BAA0B;MAC9B,SAAS;MACT,cAAc;QACZ,IAAI,IAAI;;KAEX;AARM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AAGI,IAAO,uBAAP,cAAoCA,WAAS;EAGjD,YAAY,EACV,OACA,MACA,SAAQ,GAKT;AACC,UAAM,0BAA0B;MAC9B,SAAS;MACT,cAAc;QACZ,aAAa,QAAQ,gBACnB,OAAO,QAAQ,IAAI,WAAW,EAChC;;KAEH;AAlBM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAmBhB;;AAGI,IAAO,+BAAP,cAA4CA,WAAS;EAGzD,YAAY,EACV,OACA,MACA,SAAQ,GAKT;AACC,UAAM,0BAA0B;MAC9B,SAAS;MACT,cAAc;QACZ,aAAa,QAAQ,gBACnB,OAAO,QAAQ,IAAI,WAAW,EAChC;QACA,iFAAiF,QAAQ;;KAE5F;AAnBM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAoBhB;;AAGI,IAAO,+BAAP,cAA4CA,WAAS;EAGzD,YAAY,EACV,aAAY,GAGb;AACC,UAAM,0BAA0B;MAC9B,SAAS,KAAK,UAAU,cAAc,MAAM,CAAC;MAC7C,cAAc,CAAC,gCAAgC;KAChD;AAVM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;;;ACzGI,IAAO,wBAAP,cAAqCC,WAAS;EAGlD,YAAY,EACV,WACA,KAAI,GAIL;AACC,UAAM,WAAW,IAAI,eAAe;MAClC,SAAS;KACV;AAXM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYhB;;AAGI,IAAO,wBAAP,cAAqCA,WAAS;EAGlD,YAAY,EAAE,UAAS,GAAyB;AAC9C,UAAM,sBAAsB;MAC1B,SAAS;KACV;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;AAGI,IAAO,8BAAP,cAA2CA,WAAS;EAGxD,YAAY,EAAE,UAAS,GAAyB;AAC9C,UAAM,6BAA6B;MACjC,SAAS;MACT,cAAc,CAAC,sBAAsB;KACtC;AANM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;;;ACnCI,IAAO,yBAAP,cAAsCC,WAAS;EAGnD,YAAY,EAAE,KAAI,GAAoB;AACpC,UAAM,gCAAgC;MACpC,cAAc,CAAC,WAAW,IAAI,4BAA4B;KAC3D;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;;;ACPI,IAAO,0BAAP,cAAuCC,WAAS;EAGpD,YAAY,EAAE,SAAS,MAAK,GAAsC;AAChE,UAAM,2BAA2B;MAC/B,cAAc;QACZ,IAAI,QAAQ,KAAI,CAAE,kBAChB,QAAQ,IAAI,YAAY,SAC1B;;MAEF,SAAS,UAAU,KAAK;KACzB;AAVM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;;;ACZI,SAAU,UAAa,OAAe,QAAc;AACxD,QAAM,QAAQ,MAAM,KAAK,MAAM;AAC/B,SAAO,+BAAO;AAChB;AAIO,IAAM,aAAa;AAInB,IAAM,eACX;AAEK,IAAM,eAAe;;;ACP5B,IAAM,sBACJ;AACI,SAAU,iBAAiB,WAAiB;AAChD,SAAO,oBAAoB,KAAK,SAAS;AAC3C;AACM,SAAU,mBAAmB,WAAiB;AAClD,SAAO,UACL,qBACA,SAAS;AAEb;AAGA,IAAM,sBACJ;AACI,SAAU,iBAAiB,WAAiB;AAChD,SAAO,oBAAoB,KAAK,SAAS;AAC3C;AACM,SAAU,mBAAmB,WAAiB;AAClD,SAAO,UACL,qBACA,SAAS;AAEb;AAGA,IAAM,yBACJ;AACI,SAAU,oBAAoB,WAAiB;AACnD,SAAO,uBAAuB,KAAK,SAAS;AAC9C;AACM,SAAU,sBAAsB,WAAiB;AACrD,SAAO,UAKJ,wBAAwB,SAAS;AACtC;AAGA,IAAM,uBACJ;AACI,SAAU,kBAAkB,WAAiB;AACjD,SAAO,qBAAqB,KAAK,SAAS;AAC5C;AACM,SAAU,oBAAoB,WAAiB;AACnD,SAAO,UACL,sBACA,SAAS;AAEb;AAGA,IAAM,4BACJ;AACI,SAAU,uBAAuB,WAAiB;AACtD,SAAO,0BAA0B,KAAK,SAAS;AACjD;AACM,SAAU,yBAAyB,WAAiB;AACxD,SAAO,UAGJ,2BAA2B,SAAS;AACzC;AAGA,IAAM,yBAAyB;AACzB,SAAU,oBAAoB,WAAiB;AACnD,SAAO,uBAAuB,KAAK,SAAS;AAC9C;AAGA,IAAM,wBAAwB;AACxB,SAAU,mBAAmB,WAAiB;AAClD,SAAO,sBAAsB,KAAK,SAAS;AAC7C;AAEO,IAAM,YAAY,oBAAI,IAAc;EACzC;EACA;EACA;EACA;CACD;AACM,IAAM,iBAAiB,oBAAI,IAAmB,CAAC,SAAS,CAAC;AACzD,IAAM,oBAAoB,oBAAI,IAAsB;EACzD;EACA;EACA;CACD;;;AC1FK,SAAU,qBACd,OACA,MAA6B;AAE7B,MAAI;AAAM,WAAO,GAAG,IAAI,IAAI,KAAK;AACjC,SAAO;AACT;AAOO,IAAM,iBAAiB,oBAAI,IAGhC;EAEA,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,QAAQ,EAAE,MAAM,OAAM,CAAE;EACzB,CAAC,SAAS,EAAE,MAAM,QAAO,CAAE;EAC3B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,OAAO,EAAE,MAAM,SAAQ,CAAE;EAC1B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,QAAQ,EAAE,MAAM,UAAS,CAAE;EAC5B,CAAC,SAAS,EAAE,MAAM,QAAO,CAAE;EAC3B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAG/B,CAAC,iBAAiB,EAAE,MAAM,WAAW,MAAM,QAAO,CAAE;EACpD,CAAC,cAAc,EAAE,MAAM,WAAW,MAAM,KAAI,CAAE;EAC9C,CAAC,iBAAiB,EAAE,MAAM,QAAQ,MAAM,WAAU,CAAE;EACpD,CAAC,eAAe,EAAE,MAAM,SAAS,MAAM,QAAO,CAAE;EAChD,CAAC,cAAc,EAAE,MAAM,SAAS,MAAM,OAAM,CAAE;EAC9C,CAAC,mBAAmB,EAAE,MAAM,SAAS,MAAM,YAAW,CAAE;EACxD,CAAC,gBAAgB,EAAE,MAAM,WAAW,MAAM,OAAM,CAAE;EAClD,CAAC,aAAa,EAAE,MAAM,WAAW,MAAM,IAAG,CAAE;EAC5C,CAAC,gBAAgB,EAAE,MAAM,WAAW,MAAM,OAAM,CAAE;EAClD,CAAC,aAAa,EAAE,MAAM,WAAW,MAAM,IAAG,CAAE;EAC5C,CAAC,eAAe,EAAE,MAAM,UAAU,MAAM,OAAM,CAAE;EAChD,CAAC,iBAAiB,EAAE,MAAM,UAAU,MAAM,SAAQ,CAAE;EACpD,CAAC,mBAAmB,EAAE,MAAM,UAAU,MAAM,WAAU,CAAE;EACxD,CAAC,gBAAgB,EAAE,MAAM,WAAW,MAAM,UAAS,CAAE;EACrD,CAAC,WAAW,EAAE,MAAM,SAAS,MAAM,IAAG,CAAE;EACxC,CAAC,mBAAmB,EAAE,MAAM,WAAW,MAAM,UAAS,CAAE;EACxD,CAAC,mBAAmB,EAAE,MAAM,WAAW,MAAM,UAAS,CAAE;EACxD,CAAC,iBAAiB,EAAE,MAAM,WAAW,MAAM,QAAO,CAAE;EAGpD;IACE;IACA,EAAE,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAI;;EAEhD,CAAC,4BAA4B,EAAE,MAAM,WAAW,MAAM,MAAM,SAAS,KAAI,CAAE;EAC3E;IACE;IACA,EAAE,MAAM,WAAW,MAAM,WAAW,SAAS,KAAI;;EAEnD;IACE;IACA,EAAE,MAAM,WAAW,MAAM,WAAW,SAAS,KAAI;;CAEpD;;;ACnCK,SAAU,eAAe,WAAmB,UAAwB,CAAA,GAAE;AAC1E,MAAI,oBAAoB,SAAS,GAAG;AAClC,UAAM,QAAQ,sBAAsB,SAAS;AAC7C,QAAI,CAAC;AAAO,YAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,WAAU,CAAE;AAE3E,UAAM,cAAc,gBAAgB,MAAM,UAAU;AACpD,UAAM,SAAS,CAAA;AACf,UAAM,cAAc,YAAY;AAChC,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,aAAO,KACL,kBAAkB,YAAY,CAAC,GAAI;QACjC,WAAW;QACX;QACA,MAAM;OACP,CAAC;;AAIN,UAAM,UAAU,CAAA;AAChB,QAAI,MAAM,SAAS;AACjB,YAAM,eAAe,gBAAgB,MAAM,OAAO;AAClD,YAAM,eAAe,aAAa;AAClC,eAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,gBAAQ,KACN,kBAAkB,aAAa,CAAC,GAAI;UAClC,WAAW;UACX;UACA,MAAM;SACP,CAAC;;;AAKR,WAAO;MACL,MAAM,MAAM;MACZ,MAAM;MACN,iBAAiB,MAAM,mBAAmB;MAC1C;MACA;;;AAIJ,MAAI,iBAAiB,SAAS,GAAG;AAC/B,UAAM,QAAQ,mBAAmB,SAAS;AAC1C,QAAI,CAAC;AAAO,YAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,QAAO,CAAE;AAExE,UAAM,SAAS,gBAAgB,MAAM,UAAU;AAC/C,UAAM,gBAAgB,CAAA;AACtB,UAAM,SAAS,OAAO;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,oBAAc,KACZ,kBAAkB,OAAO,CAAC,GAAI;QAC5B,WAAW;QACX;QACA,MAAM;OACP,CAAC;;AAGN,WAAO,EAAE,MAAM,MAAM,MAAM,MAAM,SAAS,QAAQ,cAAa;;AAGjE,MAAI,iBAAiB,SAAS,GAAG;AAC/B,UAAM,QAAQ,mBAAmB,SAAS;AAC1C,QAAI,CAAC;AAAO,YAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,QAAO,CAAE;AAExE,UAAM,SAAS,gBAAgB,MAAM,UAAU;AAC/C,UAAM,gBAAgB,CAAA;AACtB,UAAM,SAAS,OAAO;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,oBAAc,KACZ,kBAAkB,OAAO,CAAC,GAAI,EAAE,SAAS,MAAM,QAAO,CAAE,CAAC;;AAG7D,WAAO,EAAE,MAAM,MAAM,MAAM,MAAM,SAAS,QAAQ,cAAa;;AAGjE,MAAI,uBAAuB,SAAS,GAAG;AACrC,UAAM,QAAQ,yBAAyB,SAAS;AAChD,QAAI,CAAC;AACH,YAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,cAAa,CAAE;AAEpE,UAAM,SAAS,gBAAgB,MAAM,UAAU;AAC/C,UAAM,gBAAgB,CAAA;AACtB,UAAM,SAAS,OAAO;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,oBAAc,KACZ,kBAAkB,OAAO,CAAC,GAAI,EAAE,SAAS,MAAM,cAAa,CAAE,CAAC;;AAGnE,WAAO;MACL,MAAM;MACN,iBAAiB,MAAM,mBAAmB;MAC1C,QAAQ;;;AAIZ,MAAI,oBAAoB,SAAS;AAAG,WAAO,EAAE,MAAM,WAAU;AAC7D,MAAI,mBAAmB,SAAS;AAC9B,WAAO;MACL,MAAM;MACN,iBAAiB;;AAGrB,QAAM,IAAI,sBAAsB,EAAE,UAAS,CAAE;AAC/C;AAEA,IAAM,gCACJ;AACF,IAAM,6BACJ;AACF,IAAM,sBAAsB;AAQtB,SAAU,kBAAkB,OAAe,SAAsB;AA1JvE;AA4JE,QAAM,oBAAoB,qBAAqB,OAAO,mCAAS,IAAI;AACnE,MAAI,eAAe,IAAI,iBAAiB;AACtC,WAAO,eAAe,IAAI,iBAAiB;AAE7C,QAAM,UAAU,aAAa,KAAK,KAAK;AACvC,QAAM,QAAQ,UAMZ,UAAU,6BAA6B,+BACvC,KAAK;AAEP,MAAI,CAAC;AAAO,UAAM,IAAI,sBAAsB,EAAE,MAAK,CAAE;AAErD,MAAI,MAAM,QAAQ,kBAAkB,MAAM,IAAI;AAC5C,UAAM,IAAI,8BAA8B,EAAE,OAAO,MAAM,MAAM,KAAI,CAAE;AAErE,QAAM,OAAO,MAAM,OAAO,EAAE,MAAM,MAAM,KAAI,IAAK,CAAA;AACjD,QAAM,UAAU,MAAM,aAAa,YAAY,EAAE,SAAS,KAAI,IAAK,CAAA;AACnE,QAAM,WAAU,mCAAS,YAAW,CAAA;AACpC,MAAI;AACJ,MAAI,aAAa,CAAA;AACjB,MAAI,SAAS;AACX,WAAO;AACP,UAAM,SAAS,gBAAgB,MAAM,IAAI;AACzC,UAAM,cAAc,CAAA;AACpB,UAAM,SAAS,OAAO;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAE/B,kBAAY,KAAK,kBAAkB,OAAO,CAAC,GAAI,EAAE,QAAO,CAAE,CAAC;;AAE7D,iBAAa,EAAE,YAAY,YAAW;aAC7B,MAAM,QAAQ,SAAS;AAChC,WAAO;AACP,iBAAa,EAAE,YAAY,QAAQ,MAAM,IAAI,EAAC;aACrC,oBAAoB,KAAK,MAAM,IAAI,GAAG;AAC/C,WAAO,GAAG,MAAM,IAAI;SACf;AACL,WAAO,MAAM;AACb,QAAI,GAAE,mCAAS,UAAS,aAAa,CAAC,eAAe,IAAI;AACvD,YAAM,IAAI,yBAAyB,EAAE,KAAI,CAAE;;AAG/C,MAAI,MAAM,UAAU;AAElB,QAAI,GAAC,8CAAS,cAAT,mBAAoB,QAApB,4BAA0B,MAAM;AACnC,YAAM,IAAI,qBAAqB;QAC7B;QACA,MAAM,mCAAS;QACf,UAAU,MAAM;OACjB;AAGH,QACE,kBAAkB,IAAI,MAAM,QAA4B,KACxD,CAAC,oBAAoB,MAAM,CAAC,CAAC,MAAM,KAAK;AAExC,YAAM,IAAI,6BAA6B;QACrC;QACA,MAAM,mCAAS;QACf,UAAU,MAAM;OACjB;;AAGL,QAAM,eAAe;IACnB,MAAM,GAAG,IAAI,GAAG,MAAM,SAAS,EAAE;IACjC,GAAG;IACH,GAAG;IACH,GAAG;;AAEL,iBAAe,IAAI,mBAAmB,YAAY;AAClD,SAAO;AACT;AAGM,SAAU,gBACd,QACA,SAAmB,CAAA,GACnB,UAAU,IACV,QAAQ,GAAC;AAET,MAAI,WAAW,IAAI;AACjB,QAAI,YAAY;AAAI,aAAO;AAC3B,QAAI,UAAU;AAAG,YAAM,IAAI,wBAAwB,EAAE,SAAS,MAAK,CAAE;AAErE,WAAO,KAAK,QAAQ,KAAI,CAAE;AAC1B,WAAO;;AAGT,QAAM,SAAS,OAAO;AAEtB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,OAAO,OAAO,MAAM,IAAI,CAAC;AAC/B,YAAQ,MAAM;MACZ,KAAK;AACH,eAAO,UAAU,IACb,gBAAgB,MAAM,CAAC,GAAG,QAAQ,QAAQ,KAAI,CAAE,CAAC,IACjD,gBAAgB,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,IAAI,KAAK;MAC9D,KAAK;AACH,eAAO,gBAAgB,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,IAAI,QAAQ,CAAC;MACrE,KAAK;AACH,eAAO,gBAAgB,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,IAAI,QAAQ,CAAC;MACrE;AACE,eAAO,gBAAgB,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,IAAI,KAAK;;;AAIrE,SAAO,CAAA;AACT;AAEM,SAAU,eACd,MAAY;AAEZ,SACE,SAAS,aACT,SAAS,UACT,SAAS,cACT,SAAS,YACT,WAAW,KAAK,IAAI,KACpB,aAAa,KAAK,IAAI;AAE1B;AAEA,IAAM,yBACJ;AAEI,SAAU,kBAAkB,MAAY;AAC5C,SACE,SAAS,aACT,SAAS,UACT,SAAS,cACT,SAAS,YACT,SAAS,WACT,WAAW,KAAK,IAAI,KACpB,aAAa,KAAK,IAAI,KACtB,uBAAuB,KAAK,IAAI;AAEpC;AAEM,SAAU,oBACd,MACA,SAAgB;AAKhB,SAAO,WAAW,SAAS,WAAW,SAAS,YAAY,SAAS;AACtE;;;AC7SM,SAAU,aAAa,YAA6B;AAExD,QAAM,iBAA+B,CAAA;AACrC,QAAM,mBAAmB,WAAW;AACpC,WAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,UAAM,YAAY,WAAW,CAAC;AAC9B,QAAI,CAAC,kBAAkB,SAAS;AAAG;AAEnC,UAAM,QAAQ,oBAAoB,SAAS;AAC3C,QAAI,CAAC;AAAO,YAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,SAAQ,CAAE;AAEzE,UAAM,aAAa,MAAM,WAAW,MAAM,GAAG;AAE7C,UAAM,aAA6B,CAAA;AACnC,UAAM,mBAAmB,WAAW;AACpC,aAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,YAAM,WAAW,WAAW,CAAC;AAC7B,YAAM,UAAU,SAAS,KAAI;AAC7B,UAAI,CAAC;AAAS;AACd,YAAM,eAAe,kBAAkB,SAAS;QAC9C,MAAM;OACP;AACD,iBAAW,KAAK,YAAY;;AAG9B,QAAI,CAAC,WAAW;AAAQ,YAAM,IAAI,4BAA4B,EAAE,UAAS,CAAE;AAC3E,mBAAe,MAAM,IAAI,IAAI;;AAI/B,QAAM,kBAAgC,CAAA;AACtC,QAAM,UAAU,OAAO,QAAQ,cAAc;AAC7C,QAAM,gBAAgB,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,UAAM,CAAC,MAAM,UAAU,IAAI,QAAQ,CAAC;AACpC,oBAAgB,IAAI,IAAI,eAAe,YAAY,cAAc;;AAGnE,SAAO;AACT;AAEA,IAAM,wBACJ;AAEF,SAAS,eACP,eACA,SACA,YAAY,oBAAI,IAAG,GAAU;AAE7B,QAAM,aAA6B,CAAA;AACnC,QAAM,SAAS,cAAc;AAC7B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,eAAe,cAAc,CAAC;AACpC,UAAM,UAAU,aAAa,KAAK,aAAa,IAAI;AACnD,QAAI;AAAS,iBAAW,KAAK,YAAY;SACpC;AACH,YAAM,QAAQ,UACZ,uBACA,aAAa,IAAI;AAEnB,UAAI,EAAC,+BAAO;AAAM,cAAM,IAAI,6BAA6B,EAAE,aAAY,CAAE;AAEzE,YAAM,EAAE,OAAO,KAAI,IAAK;AACxB,UAAI,QAAQ,SAAS;AACnB,YAAI,UAAU,IAAI,IAAI;AAAG,gBAAM,IAAI,uBAAuB,EAAE,KAAI,CAAE;AAElE,mBAAW,KAAK;UACd,GAAG;UACH,MAAM,QAAQ,SAAS,EAAE;UACzB,YAAY,eACV,QAAQ,IAAI,KAAK,CAAA,GACjB,SACA,oBAAI,IAAI,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC;SAEhC;aACI;AACL,YAAI,eAAe,IAAI;AAAG,qBAAW,KAAK,YAAY;;AACjD,gBAAM,IAAI,iBAAiB,EAAE,KAAI,CAAE;;;;AAK9C,SAAO;AACT;;;ACtCM,SAAU,SACd,YAI2B;AAE3B,QAAM,UAAU,aAAa,UAA+B;AAC5D,QAAM,MAAM,CAAA;AACZ,QAAM,SAAS,WAAW;AAC1B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,YAAa,WAAiC,CAAC;AACrD,QAAI,kBAAkB,SAAS;AAAG;AAClC,QAAI,KAAK,eAAe,WAAW,OAAO,CAAC;;AAE7C,SAAO;AACT;;;ACpBA,IAAM,aAAa;AAYb,SAAU,mBAEd,cAA2B;AAG3B,MAAI,OAAO,aAAa;AACxB,MAAI,WAAW,KAAK,aAAa,IAAI,KAAK,gBAAgB,cAAc;AACtE,WAAO;AACP,UAAM,SAAS,aAAa,WAAW;AACvC,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,YAAY,aAAa,WAAW,CAAC;AAC3C,cAAQ,mBAAmB,SAAS;AACpC,UAAI,IAAI,SAAS;AAAG,gBAAQ;;AAE9B,UAAM,SAAS,UAA8B,YAAY,aAAa,IAAI;AAC1E,YAAQ,KAAI,iCAAQ,UAAS,EAAE;AAC/B,WAAO,mBAAmB;MACxB,GAAG;MACH;KACD;;AAGH,MAAI,aAAa,gBAAgB,aAAa;AAC5C,WAAO,GAAG,IAAI;AAEhB,MAAI,aAAa;AAAM,WAAO,GAAG,IAAI,IAAI,aAAa,IAAI;AAC1D,SAAO;AACT;;;AChDM,SAAU,oBAKd,eAA6B;AAC7B,MAAI,SAAS;AACb,QAAM,SAAS,cAAc;AAC7B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,eAAe,cAAc,CAAC;AACpC,cAAU,mBAAmB,YAAY;AACzC,QAAI,MAAM,SAAS;AAAG,gBAAU;;AAElC,SAAO;AACT;;;ACoCM,SAAU,cACd,SAAiB;AAQjB,MAAI,QAAQ,SAAS;AACnB,WAAO,YAAY,QAAQ,IAAI,IAAI,oBACjC,QAAQ,MAAgB,CACzB,IACC,QAAQ,mBAAmB,QAAQ,oBAAoB,eACnD,IAAI,QAAQ,eAAe,KAC3B,EACN,GACE,QAAQ,QAAQ,SACZ,aAAa,oBAAoB,QAAQ,OAAiB,CAAC,MAC3D,EACN;WACO,QAAQ,SAAS;AACxB,WAAO,SAAS,QAAQ,IAAI,IAAI,oBAC9B,QAAQ,MAAgB,CACzB;WACM,QAAQ,SAAS;AACxB,WAAO,SAAS,QAAQ,IAAI,IAAI,oBAC9B,QAAQ,MAAgB,CACzB;WACM,QAAQ,SAAS;AACxB,WAAO,eAAe,oBAAoB,QAAQ,MAAgB,CAAC,IACjE,QAAQ,oBAAoB,YAAY,aAAa,EACvD;WACO,QAAQ,SAAS;AAAY,WAAO;AAC7C,SAAO;AACT;;;ACrDM,SAAUC,mBAGd,OAcG;AAEH,MAAI;AACJ,MAAI,OAAO,UAAU;AACnB,mBAAe,kBAAmB,OAAO;MACvC;KACD;OACE;AACH,UAAM,UAAU,aAAa,KAA0B;AACvD,UAAM,SAAS,MAAM;AACrB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,YAAa,MAA4B,CAAC;AAChD,UAAI,kBAAkB,SAAS;AAAG;AAClC,qBAAe,kBAAmB,WAAW,EAAE,WAAW,QAAO,CAAE;AACnE;;;AAIJ,MAAI,CAAC;AAAc,UAAM,IAAI,yBAAyB,EAAE,MAAK,CAAE;AAE/D,SAAO;AACT;;;ACpCM,SAAU,mBAGd,QAcG;AAEH,QAAM,gBAAgC,CAAA;AACtC,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,aAAa,gBAAgB,MAAM;AACzC,UAAM,SAAS,WAAW;AAC1B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,oBAAc,KAAK,kBAAmB,WAAW,CAAC,GAAI,EAAE,UAAS,CAAE,CAAC;;SAEjE;AACL,UAAM,UAAU,aAAa,MAA2B;AACxD,UAAM,SAAS,OAAO;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,YAAa,OAA6B,CAAC;AACjD,UAAI,kBAAkB,SAAS;AAAG;AAClC,YAAM,aAAa,gBAAgB,SAAS;AAC5C,YAAMC,UAAS,WAAW;AAC1B,eAAS,IAAI,GAAG,IAAIA,SAAQ,KAAK;AAC/B,sBAAc,KACZ,kBAAmB,WAAW,CAAC,GAAI,EAAE,WAAW,QAAO,CAAE,CAAC;;;;AAMlE,MAAI,cAAc,WAAW;AAC3B,UAAM,IAAI,0BAA0B,EAAE,OAAM,CAAE;AAEhD,SAAO;AACT;;;ACjDM,SAAU,aAGd,WAcG;AAEH,MAAI;AACJ,MAAI,OAAO,cAAc;AACvB,cAAU,eAAe,SAAS;OAC/B;AACH,UAAM,UAAU,aAAa,SAA8B;AAC3D,UAAM,SAAS,UAAU;AACzB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,aAAc,UAAgC,CAAC;AACrD,UAAI,kBAAkB,UAAU;AAAG;AACnC,gBAAU,eAAe,YAAY,OAAO;AAC5C;;;AAIJ,MAAI,CAAC;AAAS,UAAM,IAAI,oBAAoB,EAAE,UAAS,CAAE;AACzD,SAAO;AACT;;;ACjGM,SAAUC,eACd,SACA,EAAE,cAAc,MAAK,IAAgC,CAAA,GAAE;AAEvD,MACE,QAAQ,SAAS,cACjB,QAAQ,SAAS,WACjB,QAAQ,SAAS;AAEjB,UAAM,IAAI,2BAA2B,QAAQ,IAAI;AAEnD,SAAO,GAAG,QAAQ,IAAI,IAAI,gBAAgB,QAAQ,QAAQ,EAAE,YAAW,CAAE,CAAC;AAC5E;AAIM,SAAU,gBACd,QACA,EAAE,cAAc,MAAK,IAAgC,CAAA,GAAE;AAEvD,MAAI,CAAC;AAAQ,WAAO;AACpB,SAAO,OACJ,IAAI,CAAC,UAAU,eAAe,OAAO,EAAE,YAAW,CAAE,CAAC,EACrD,KAAK,cAAc,OAAO,GAAG;AAClC;AAIA,SAAS,eACP,OACA,EAAE,YAAW,GAA4B;AAEzC,MAAI,MAAM,KAAK,WAAW,OAAO,GAAG;AAClC,WAAO,IAAI,gBACR,MAAoD,YACrD,EAAE,YAAW,CAAE,CAChB,IAAI,MAAM,KAAK,MAAM,QAAQ,MAAM,CAAC;;AAEvC,SAAO,MAAM,QAAQ,eAAe,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK;AACtE;;;AC1CM,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,EAAE,UAAAC,UAAQ,GAAwB;AAC5C,UACE;MACE;MACA;MACA,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AATI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;AAQI,IAAO,oCAAP,cAAiD,UAAS;EAE9D,YAAY,EAAE,UAAAA,UAAQ,GAAwB;AAC5C,UACE;MACE;MACA;MACA,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AATI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;AAOI,IAAO,kCAAP,cAA+C,UAAS;EAE5D,YAAY,EAAE,MAAM,MAAAC,MAAI,GAA+B;AACrD,UACE;MACE,gBAAgBA,KAAI;MACpB;MACA,KAAK,IAAI,GACX,EAAE,cAAc,CAAC,SAAS,IAAI,KAAKA,KAAI,SAAS,EAAC,CAAE;AAP9C,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AAOI,IAAO,mCAAP,cAAgD,UAAS;EAO7D,YAAY,EACV,MACA,QACA,MAAAA,MAAI,GACyD;AAC7D,UACE,CAAC,gBAAgBA,KAAI,2CAA2C,EAAE,KAChE,IAAI,GAEN;MACE,cAAc;QACZ,YAAY,gBAAgB,QAAQ,EAAE,aAAa,KAAI,CAAE,CAAC;QAC1D,WAAW,IAAI,KAAKA,KAAI;;KAE3B;AApBI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AAmBE,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAOA;EACd;;AAMI,IAAO,2BAAP,cAAwC,UAAS;EAErD,cAAA;AACE,UAAM,qDAAqD;AAFpD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAOI,IAAO,sCAAP,cAAmD,UAAS;EAEhE,YAAY,EACV,gBACA,aACA,KAAI,GAC0D;AAC9D,UACE;MACE,+CAA+C,IAAI;MACnD,oBAAoB,cAAc;MAClC,iBAAiB,WAAW;MAC5B,KAAK,IAAI,CAAC;AAXP,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAahB;;AAOI,IAAO,oCAAP,cAAiD,UAAS;EAE9D,YAAY,EAAE,cAAc,MAAK,GAAwC;AACvE,UACE,kBAAkB,KAAK,WAAW,KAChC,KAAK,CACN,wCAAwC,YAAY,IAAI;AALpD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;AAOI,IAAO,iCAAP,cAA8C,UAAS;EAE3D,YAAY,EACV,gBACA,YAAW,GACqC;AAChD,UACE;MACE;MACA,6BAA6B,cAAc;MAC3C,0BAA0B,WAAW;MACrC,KAAK,IAAI,CAAC;AAVP,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYhB;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,WAAmB,EAAE,UAAAD,UAAQ,GAAwB;AAC/D,UACE;MACE,0CAA0C,SAAS,WAAW,SAAS;MACvE;MACA;MACA,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AAVI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYhB;;AAMI,IAAO,wBAAP,cAAqC,UAAS;EAElD,YAAY,WAAoB,EAAE,UAAAA,UAAQ,IAA4B,CAAA,GAAE;AACtE,UACE;MACE,SAAS,YAAY,IAAI,SAAS,OAAO,EAAE;MAC3C;MACA,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AATI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;AAOI,IAAO,iCAAP,cAA8C,UAAS;EAK3D,YAAY,WAAgB,EAAE,UAAAA,UAAQ,GAAwB;AAC5D,UACE;MACE,4BAA4B,SAAS;MACrC;MACA,sFAAsF,SAAS;MAC/F,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AAbI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,aAAA;;;;;;AAaE,SAAK,YAAY;EACnB;;AAOI,IAAO,oCAAP,cAAiD,UAAS;EAE9D,YAAY,EAAE,UAAAA,UAAQ,GAAwB;AAC5C,UAAM,qDAAqD;MACzD,UAAAA;KACD;AAJM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;AAOI,IAAO,iCAAP,cAA8C,UAAS;EAE3D,YAAY,WAAgB,EAAE,UAAAA,UAAQ,GAAwB;AAC5D,UACE;MACE,4BAA4B,SAAS;MACrC;MACA,8EAA8E,SAAS;MACvF,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AAVI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYhB;;AAMI,IAAO,wBAAP,cAAqC,UAAS;EAElD,YAAY,WAAoB,EAAE,UAAAA,UAAQ,IAA4B,CAAA,GAAE;AACtE,UACE;MACE,SAAS,YAAY,IAAI,SAAS,OAAO,EAAE;MAC3C;MACA,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AATI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;AAMI,IAAO,2BAAP,cAAwC,UAAS;EAErD,YAAY,cAAuB,EAAE,UAAAA,UAAQ,IAA4B,CAAA,GAAE;AACzE,UACE;MACE,YAAY,eAAe,IAAI,YAAY,OAAO,EAAE;MACpD;MACA,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AATI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;AAOI,IAAO,kCAAP,cAA+C,UAAS;EAE5D,YAAY,cAAsB,EAAE,UAAAA,UAAQ,GAAwB;AAClE,UACE;MACE,aAAa,YAAY;MACzB;MACA;MACA,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AAVI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYhB;;AAOI,IAAO,oCAAP,cAAiD,UAAS;EAE9D,YAAY,WAAgB,EAAE,UAAAA,UAAQ,GAAwB;AAC5D,UACE;MACE,+BAA+B,SAAS;MACxC;MACA,8EAA8E,SAAS;MACvF,KAAK,IAAI,GACX;MACE,UAAAA;KACD;AAVI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYhB;;AAMI,IAAO,wBAAP,cAAqC,UAAS;EAElD,YACE,GACA,GAAyC;AAEzC,UAAM,kDAAkD;MACtD,cAAc;QACZ,KAAK,EAAE,IAAI,WAAWE,eAAc,EAAE,OAAO,CAAC;QAC9C,KAAK,EAAE,IAAI,WAAWA,eAAc,EAAE,OAAO,CAAC;QAC9C;QACA;QACA;;KAEH;AAbM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAchB;;AAMI,IAAO,yBAAP,cAAsC,UAAS;EAEnD,YAAY,EACV,cACA,UAAS,GACmC;AAC5C,UAAM,iBAAiB,YAAY,cAAc,SAAS,GAAG;AALtD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;AAMI,IAAO,wBAAP,cAAqC,UAAS;EAQlD,YAAY,EACV,SACA,MACA,QACA,MAAAD,MAAI,GAML;AACC,UACE;MACE,gBAAgBA,KAAI;MACpB,KAAK,IAAI,GACX;MACE,cAAc;QACZ,YAAY,gBAAgB,QAAQ,EAAE,aAAa,KAAI,CAAE,CAAC;QAC1D,WAAW,IAAI,KAAKA,KAAI;;KAE3B;AA3BI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AAyBE,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAOA;EACd;;AAMI,IAAO,0BAAP,cAAuC,UAAS;EAKpD,YAAY,EACV,SACA,MAAK,GAIN;AACC,UACE;MACE,+CACE,MAAM,OAAO,KAAK,MAAM,IAAI,MAAM,EACpC,cAAcC,eAAc,SAAS,EAAE,aAAa,KAAI,CAAE,CAAC;MAC3D,KAAK,IAAI,CAAC;AAhBP,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,WAAA;;;;;;AAiBE,SAAK,UAAU;EACjB;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,MAAc,EAAE,UAAAF,UAAQ,GAAwB;AAC1D,UACE;MACE,SAAS,IAAI;MACb;MACA,KAAK,IAAI,GACX,EAAE,UAAAA,UAAQ,CAAE;AAPP,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,MAAc,EAAE,UAAAA,UAAQ,GAAwB;AAC1D,UACE;MACE,SAAS,IAAI;MACb;MACA,KAAK,IAAI,GACX,EAAE,UAAAA,UAAQ,CAAE;AAPP,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AAMI,IAAO,oBAAP,cAAiC,UAAS;EAE9C,YAAY,OAAc;AACxB,UAAM,CAAC,UAAU,KAAK,yBAAyB,EAAE,KAAK,IAAI,CAAC;AAFpD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAMI,IAAO,6BAAP,cAA0C,UAAS;EAEvD,YAAY,MAAY;AACtB,UACE;MACE,IAAI,IAAI;MACR;MACA,KAAK,IAAI,CAAC;AANP,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAMI,IAAO,2BAAP,cAAwC,UAAS;EAErD,YAAY,MAAa;AACvB,UAAM,SAAS,IAAI,yCAAyC;AAFrD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;;;AC5eI,SAAU,mBACd,WAAuC;AAEvC,MAAI,SAAS;AACb,MAAI,UAAU;AACd,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,QAAQ;AAEZ,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAM,OAAO,UAAU,CAAC;AAGxB,QAAI,CAAC,KAAK,KAAK,GAAG,EAAE,SAAS,IAAI;AAAG,eAAS;AAG7C,QAAI,SAAS;AAAK;AAClB,QAAI,SAAS;AAAK;AAGlB,QAAI,CAAC;AAAQ;AAGb,QAAI,UAAU,GAAG;AACf,UAAI,SAAS,OAAO,CAAC,SAAS,YAAY,EAAE,EAAE,SAAS,MAAM;AAC3D,iBAAS;WACN;AACH,kBAAU;AAGV,YAAI,SAAS,KAAK;AAChB,kBAAQ;AACR;;;AAIJ;;AAIF,QAAI,SAAS,KAAK;AAEhB,UAAI,UAAU,IAAI,CAAC,MAAM,OAAO,YAAY,OAAO,YAAY,MAAM;AACnE,kBAAU;AACV,iBAAS;;AAEX;;AAGF,cAAU;AACV,eAAW;;AAGb,MAAI,CAAC;AAAO,UAAM,IAAI,UAAU,gCAAgC;AAEhE,SAAO;AACT;;;ACnDO,IAAM,uBAAuB,CAAC,QAA6B;AAChE,QAAM,MAAM,MAAK;AACf,QAAI,OAAO,QAAQ;AAAU,aAAO;AACpC,WAAO,cAAc,GAAG;EAC1B,GAAE;AACF,SAAO,mBAAmB,EAAE;AAC9B;;;ACNO,IAAM,oBAAoB,CAAC,OAAyB;AACzD,SAAO,qBAAqB,EAAuB;AACrD;;;ACdA,IAAM,aAA6B,OAAO,KAAK,KAAK,CAAC;AACrD,IAAM,OAAuB,OAAO,EAAE;AAGtC,SAAS,QAAQ,GAAW,KAAK,OAAK;AACpC,MAAI;AAAI,WAAO,EAAE,GAAG,OAAO,IAAI,UAAU,GAAG,GAAG,OAAQ,KAAK,OAAQ,UAAU,EAAC;AAC/E,SAAO,EAAE,GAAG,OAAQ,KAAK,OAAQ,UAAU,IAAI,GAAG,GAAG,OAAO,IAAI,UAAU,IAAI,EAAC;AACjF;AAEA,SAAS,MAAM,KAAe,KAAK,OAAK;AACtC,MAAI,KAAK,IAAI,YAAY,IAAI,MAAM;AACnC,MAAI,KAAK,IAAI,YAAY,IAAI,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAM,EAAE,GAAG,EAAC,IAAK,QAAQ,IAAI,CAAC,GAAG,EAAE;AACnC,KAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;;AAExB,SAAO,CAAC,IAAI,EAAE;AAChB;AAgBA,IAAM,SAAS,CAAC,GAAW,GAAW,MAAe,KAAK,IAAM,MAAO,KAAK;AAC5E,IAAM,SAAS,CAAC,GAAW,GAAW,MAAe,KAAK,IAAM,MAAO,KAAK;AAE5E,IAAM,SAAS,CAAC,GAAW,GAAW,MAAe,KAAM,IAAI,KAAQ,MAAO,KAAK;AACnF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAe,KAAM,IAAI,KAAQ,MAAO,KAAK;;;ACrBnF,IAAM,CAAC,SAAS,WAAW,UAAU,IAAoC,CAAC,CAAA,GAAI,CAAA,GAAI,CAAA,CAAE;AACpF,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,QAAwB,OAAO,GAAG;AACxC,IAAM,SAAyB,OAAO,GAAI;AAC1C,SAAS,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,SAAS;AAE9D,GAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC;AAChC,UAAQ,KAAK,KAAK,IAAI,IAAI,EAAE;AAE5B,YAAU,MAAQ,QAAQ,MAAM,QAAQ,KAAM,IAAK,EAAE;AAErD,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,SAAM,KAAK,OAAS,KAAK,OAAO,UAAW;AAC3C,QAAI,IAAI;AAAK,WAAK,QAAS,OAAuB,OAAO,CAAC,KAAK;;AAEjE,aAAW,KAAK,CAAC;;AAEnB,IAAM,CAAC,aAAa,WAAW,IAAoB,MAAM,YAAY,IAAI;AAGzE,IAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,KAAK,OAAO,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC;AAC7F,IAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,KAAK,OAAO,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC;AAGvF,SAAU,QAAQ,GAAgB,SAAiB,IAAE;AACzD,QAAM,IAAI,IAAI,YAAY,IAAI,CAAC;AAE/B,WAAS,QAAQ,KAAK,QAAQ,QAAQ,IAAI,SAAS;AAEjD,aAAS,IAAI,GAAG,IAAI,IAAI;AAAK,QAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACvF,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,YAAM,QAAQ,IAAI,KAAK;AACvB,YAAM,QAAQ,IAAI,KAAK;AACvB,YAAM,KAAK,EAAE,IAAI;AACjB,YAAM,KAAK,EAAE,OAAO,CAAC;AACrB,YAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI;AACpC,YAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACxC,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,UAAE,IAAI,CAAC,KAAK;AACZ,UAAE,IAAI,IAAI,CAAC,KAAK;;;AAIpB,QAAI,OAAO,EAAE,CAAC;AACd,QAAI,OAAO,EAAE,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAM,QAAQ,UAAU,CAAC;AACzB,YAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,YAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,YAAM,KAAK,QAAQ,CAAC;AACpB,aAAO,EAAE,EAAE;AACX,aAAO,EAAE,KAAK,CAAC;AACf,QAAE,EAAE,IAAI;AACR,QAAE,KAAK,CAAC,IAAI;;AAGd,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,eAAS,IAAI,GAAG,IAAI,IAAI;AAAK,UAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAC3C,eAAS,IAAI,GAAG,IAAI,IAAI;AAAK,UAAE,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,KAAK,EAAE;;AAG5E,MAAE,CAAC,KAAK,YAAY,KAAK;AACzB,MAAE,CAAC,KAAK,YAAY,KAAK;;AAE3B,IAAE,KAAK,CAAC;AACV;AAEM,IAAO,SAAP,MAAO,gBAAe,KAAY;;EAQtC,YACS,UACA,QACA,WACG,YAAY,OACZ,SAAiB,IAAE;AAE7B,UAAK;AANE,SAAA,WAAA;AACA,SAAA,SAAA;AACA,SAAA,YAAA;AACG,SAAA,YAAA;AACA,SAAA,SAAA;AAXF,SAAA,MAAM;AACN,SAAA,SAAS;AACT,SAAA,WAAW;AAEX,SAAA,YAAY;AAWpB,WAAO,SAAS;AAEhB,QAAI,KAAK,KAAK,YAAY,KAAK,YAAY;AACzC,YAAM,IAAI,MAAM,0CAA0C;AAC5D,SAAK,QAAQ,IAAI,WAAW,GAAG;AAC/B,SAAK,UAAU,IAAI,KAAK,KAAK;EAC/B;EACU,SAAM;AACd,YAAQ,KAAK,SAAS,KAAK,MAAM;AACjC,SAAK,SAAS;AACd,SAAK,MAAM;EACb;EACA,OAAO,MAAW;AAChB,WAAO,IAAI;AACX,UAAM,EAAE,UAAU,MAAK,IAAK;AAC5B,WAAO,QAAQ,IAAI;AACnB,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AACpD,eAAS,IAAI,GAAG,IAAI,MAAM;AAAK,cAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAC9D,UAAI,KAAK,QAAQ;AAAU,aAAK,OAAM;;AAExC,WAAO;EACT;EACU,SAAM;AACd,QAAI,KAAK;AAAU;AACnB,SAAK,WAAW;AAChB,UAAM,EAAE,OAAO,QAAQ,KAAK,SAAQ,IAAK;AAEzC,UAAM,GAAG,KAAK;AACd,SAAK,SAAS,SAAU,KAAK,QAAQ,WAAW;AAAG,WAAK,OAAM;AAC9D,UAAM,WAAW,CAAC,KAAK;AACvB,SAAK,OAAM;EACb;EACU,UAAU,KAAe;AACjC,WAAO,MAAM,KAAK;AAClB,UAAM,GAAG;AACT,SAAK,OAAM;AACX,UAAM,YAAY,KAAK;AACvB,UAAM,EAAE,SAAQ,IAAK;AACrB,aAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,MAAM,OAAO;AAC/C,UAAI,KAAK,UAAU;AAAU,aAAK,OAAM;AACxC,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,QAAQ,MAAM,GAAG;AACvD,UAAI,IAAI,UAAU,SAAS,KAAK,QAAQ,KAAK,SAAS,IAAI,GAAG,GAAG;AAChE,WAAK,UAAU;AACf,aAAO;;AAET,WAAO;EACT;EACA,QAAQ,KAAe;AAErB,QAAI,CAAC,KAAK;AAAW,YAAM,IAAI,MAAM,uCAAuC;AAC5E,WAAO,KAAK,UAAU,GAAG;EAC3B;EACA,IAAIG,QAAa;AACf,WAAOA,MAAK;AACZ,WAAO,KAAK,QAAQ,IAAI,WAAWA,MAAK,CAAC;EAC3C;EACA,WAAW,KAAe;AACxB,WAAO,KAAK,IAAI;AAChB,QAAI,KAAK;AAAU,YAAM,IAAI,MAAM,6BAA6B;AAChE,SAAK,UAAU,GAAG;AAClB,SAAK,QAAO;AACZ,WAAO;EACT;EACA,SAAM;AACJ,WAAO,KAAK,WAAW,IAAI,WAAW,KAAK,SAAS,CAAC;EACvD;EACA,UAAO;AACL,SAAK,YAAY;AACjB,SAAK,MAAM,KAAK,CAAC;EACnB;EACA,WAAW,IAAW;AACpB,UAAM,EAAE,UAAU,QAAQ,WAAW,QAAQ,UAAS,IAAK;AAC3D,WAAA,KAAO,IAAI,QAAO,UAAU,QAAQ,WAAW,WAAW,MAAM;AAChE,OAAG,QAAQ,IAAI,KAAK,OAAO;AAC3B,OAAG,MAAM,KAAK;AACd,OAAG,SAAS,KAAK;AACjB,OAAG,WAAW,KAAK;AACnB,OAAG,SAAS;AAEZ,OAAG,SAAS;AACZ,OAAG,YAAY;AACf,OAAG,YAAY;AACf,OAAG,YAAY,KAAK;AACpB,WAAO;EACT;;AAGF,IAAM,MAAM,CAAC,QAAgB,UAAkB,cAC7C,gBAAgB,MAAM,IAAI,OAAO,UAAU,QAAQ,SAAS,CAAC;AAExD,IAAM,WAA2B,IAAI,GAAM,KAAK,MAAM,CAAC;AAKvD,IAAM,WAA2B,IAAI,GAAM,KAAK,MAAM,CAAC;AACvD,IAAM,WAA2B,IAAI,GAAM,KAAK,MAAM,CAAC;AACvD,IAAM,WAA2B,IAAI,GAAM,IAAI,MAAM,CAAC;AACtD,IAAM,aAA6B,IAAI,GAAM,KAAK,MAAM,CAAC;AAKzD,IAAM,aAA6B,IAAI,GAAM,KAAK,MAAM,CAAC;AACzD,IAAM,aAA6B,IAAI,GAAM,KAAK,MAAM,CAAC;AACzD,IAAM,aAA6B,IAAI,GAAM,IAAI,MAAM,CAAC;AAI/D,IAAM,WAAW,CAAC,QAAgB,UAAkB,cAClD,2BACE,CAAC,OAAkB,CAAA,MACjB,IAAI,OAAO,UAAU,QAAQ,KAAK,UAAU,SAAY,YAAY,KAAK,OAAO,IAAI,CAAC;AAGpF,IAAM,WAA2B,SAAS,IAAM,KAAK,MAAM,CAAC;AAC5D,IAAM,WAA2B,SAAS,IAAM,KAAK,MAAM,CAAC;;;AC1M7D,SAAU,UACd,OACA,KAAS;AAET,QAAM,KAAK,OAAO;AAClB,QAAMC,SAAQ,WACZ,MAAM,OAAO,EAAE,QAAQ,MAAK,CAAE,IAAIC,SAAQ,KAAK,IAAI,KAAK;AAE1D,MAAI,OAAO;AAAS,WAAOD;AAC3B,SAAO,MAAMA,MAAK;AACpB;;;ACvBA,IAAM,OAAO,CAAC,UAAkB,UAAUE,SAAQ,KAAK,CAAC;AAOjD,IAAM,mBAAmB,CAAC,OAC/B,KAAK,kBAAkB,EAAE,CAAC;;;ACYtB,SAAU,MACd,OACA,OACA,KACA,EAAE,OAAM,IAA2B,CAAA,GAAE;AAErC,MAAI,MAAM,OAAO,EAAE,QAAQ,MAAK,CAAE;AAChC,WAAO,SAAS,OAAc,OAAO,KAAK;MACxC;KACD;AACH,SAAO,WAAW,OAAoB,OAAO,KAAK;IAChD;GACD;AACH;AAOA,SAAS,kBAAkB,OAAwB,OAAc;AAC/D,MAAI,OAAO,UAAU,YAAY,QAAQ,KAAK,QAAQ,KAAK,KAAK,IAAI;AAClE,UAAM,IAAI,4BAA4B;MACpC,QAAQ;MACR,UAAU;MACV,MAAM,KAAK,KAAK;KACjB;AACL;AAOA,SAAS,gBAAgB,OAAwB,OAAgB,KAAY;AAC3E,MACE,OAAO,UAAU,YACjB,OAAO,QAAQ,YACf,KAAK,KAAK,MAAM,MAAM,OACtB;AACA,UAAM,IAAI,4BAA4B;MACpC,QAAQ;MACR,UAAU;MACV,MAAM,KAAK,KAAK;KACjB;;AAEL;AAcM,SAAU,WACd,QACA,OACA,KACA,EAAE,OAAM,IAA2B,CAAA,GAAE;AAErC,oBAAkB,QAAQ,KAAK;AAC/B,QAAM,QAAQ,OAAO,MAAM,OAAO,GAAG;AACrC,MAAI;AAAQ,oBAAgB,OAAO,OAAO,GAAG;AAC7C,SAAO;AACT;AAcM,SAAU,SACd,QACA,OACA,KACA,EAAE,OAAM,IAA2B,CAAA,GAAE;AAErC,oBAAkB,QAAQ,KAAK;AAC/B,QAAM,QAAQ,KAAK,OAChB,QAAQ,MAAM,EAAE,EAChB,OAAO,SAAS,KAAK,IAAI,OAAO,OAAO,UAAU,CAAC,CAAC;AACtD,MAAI;AAAQ,oBAAgB,OAAO,OAAO,GAAG;AAC7C,SAAO;AACT;;;AC1EM,SAAU,oBAGd,QACA,QAES;AAET,MAAI,OAAO,WAAW,OAAO;AAC3B,UAAM,IAAI,+BAA+B;MACvC,gBAAgB,OAAO;MACvB,aAAa,OAAO;KACrB;AAEH,QAAM,iBAAiB,cAAc;IACnC;IACA;GACD;AACD,QAAM,OAAO,aAAa,cAAc;AACxC,MAAI,KAAK,WAAW;AAAG,WAAO;AAC9B,SAAO;AACT;AAWA,SAAS,cAA6D,EACpE,QACA,OAAM,GAIP;AACC,QAAM,iBAAkC,CAAA;AACxC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,mBAAe,KAAK,aAAa,EAAE,OAAO,OAAO,CAAC,GAAG,OAAO,OAAO,CAAC,EAAC,CAAE,CAAC;;AAE1E,SAAO;AACT;AAOA,SAAS,aAAgD,EACvD,OACA,MAAK,GAIN;AACC,QAAM,kBAAkB,mBAAmB,MAAM,IAAI;AACrD,MAAI,iBAAiB;AACnB,UAAM,CAAC,QAAQ,IAAI,IAAI;AACvB,WAAO,YAAY,OAAO,EAAE,QAAQ,OAAO,EAAE,GAAG,OAAO,KAAI,EAAE,CAAE;;AAEjE,MAAI,MAAM,SAAS,SAAS;AAC1B,WAAO,YAAY,OAA2B;MAC5C;KACD;;AAEH,MAAI,MAAM,SAAS,WAAW;AAC5B,WAAO,cAAc,KAAuB;;AAE9C,MAAI,MAAM,SAAS,QAAQ;AACzB,WAAO,WAAW,KAA2B;;AAE/C,MAAI,MAAM,KAAK,WAAW,MAAM,KAAK,MAAM,KAAK,WAAW,KAAK,GAAG;AACjE,UAAM,SAAS,MAAM,KAAK,WAAW,KAAK;AAC1C,WAAO,aAAa,OAA4B,EAAE,OAAM,CAAE;;AAE5D,MAAI,MAAM,KAAK,WAAW,OAAO,GAAG;AAClC,WAAO,YAAY,OAAyB,EAAE,MAAK,CAAE;;AAEvD,MAAI,MAAM,SAAS,UAAU;AAC3B,WAAO,aAAa,KAA0B;;AAEhD,QAAM,IAAI,4BAA4B,MAAM,MAAM;IAChD,UAAU;GACX;AACH;AASA,SAAS,aAAa,gBAA+B;AAEnD,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,UAAM,EAAE,SAAS,QAAO,IAAK,eAAe,CAAC;AAC7C,QAAI;AAAS,oBAAc;;AACtB,oBAAc,KAAK,OAAO;;AAIjC,QAAM,eAAsB,CAAA;AAC5B,QAAM,gBAAuB,CAAA;AAC7B,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,UAAM,EAAE,SAAS,QAAO,IAAK,eAAe,CAAC;AAC7C,QAAI,SAAS;AACX,mBAAa,KAAK,YAAY,aAAa,aAAa,EAAE,MAAM,GAAE,CAAE,CAAC;AACrE,oBAAc,KAAK,OAAO;AAC1B,qBAAe,KAAK,OAAO;WACtB;AACL,mBAAa,KAAK,OAAO;;;AAK7B,SAAO,OAAO,CAAC,GAAG,cAAc,GAAG,aAAa,CAAC;AACnD;AASA,SAAS,cAAc,OAAU;AAC/B,MAAI,CAAC,UAAU,KAAK;AAAG,UAAM,IAAI,oBAAoB,EAAE,SAAS,MAAK,CAAE;AACvE,SAAO,EAAE,SAAS,OAAO,SAAS,OAAO,MAAM,YAAW,CAAS,EAAC;AACtE;AAWA,SAAS,YACP,OACA,EACE,QACA,MAAK,GAIN;AAED,QAAM,UAAU,WAAW;AAE3B,MAAI,CAAC,MAAM,QAAQ,KAAK;AAAG,UAAM,IAAI,kBAAkB,KAAK;AAC5D,MAAI,CAAC,WAAW,MAAM,WAAW;AAC/B,UAAM,IAAI,oCAAoC;MAC5C,gBAAgB;MAChB,aAAa,MAAM;MACnB,MAAM,GAAG,MAAM,IAAI,IAAI,MAAM;KAC9B;AAEH,MAAI,eAAe;AACnB,QAAM,iBAAkC,CAAA;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,gBAAgB,aAAa,EAAE,OAAO,OAAO,MAAM,CAAC,EAAC,CAAE;AAC7D,QAAI,cAAc;AAAS,qBAAe;AAC1C,mBAAe,KAAK,aAAa;;AAGnC,MAAI,WAAW,cAAc;AAC3B,UAAM,OAAO,aAAa,cAAc;AACxC,QAAI,SAAS;AACX,YAAMC,UAAS,YAAY,eAAe,QAAQ,EAAE,MAAM,GAAE,CAAE;AAC9D,aAAO;QACL,SAAS;QACT,SAAS,eAAe,SAAS,IAAI,OAAO,CAACA,SAAQ,IAAI,CAAC,IAAIA;;;AAGlE,QAAI;AAAc,aAAO,EAAE,SAAS,MAAM,SAAS,KAAI;;AAEzD,SAAO;IACL,SAAS;IACT,SAAS,OAAO,eAAe,IAAI,CAAC,EAAE,QAAO,MAAO,OAAO,CAAC;;AAEhE;AAUA,SAAS,YACP,OACA,EAAE,MAAK,GAAqB;AAE5B,QAAM,CAAC,EAAE,SAAS,IAAI,MAAM,KAAK,MAAM,OAAO;AAC9C,QAAM,YAAY,KAAK,KAAK;AAC5B,MAAI,CAAC,WAAW;AACd,QAAI,SAAS;AAGb,QAAI,YAAY,OAAO;AACrB,eAAS,OAAO,QAAQ;QACtB,KAAK;QACL,MAAM,KAAK,MAAM,MAAM,SAAS,KAAK,IAAI,EAAE,IAAI;OAChD;AACH,WAAO;MACL,SAAS;MACT,SAAS,OAAO,CAAC,OAAO,YAAY,WAAW,EAAE,MAAM,GAAE,CAAE,CAAC,GAAG,MAAM,CAAC;;;AAG1E,MAAI,cAAc,SAAS,SAAS;AAClC,UAAM,IAAI,kCAAkC;MAC1C,cAAc,SAAS,SAAS;MAChC;KACD;AACH,SAAO,EAAE,SAAS,OAAO,SAAS,OAAO,OAAO,EAAE,KAAK,QAAO,CAAE,EAAC;AACnE;AAOA,SAAS,WAAW,OAAc;AAChC,SAAO,EAAE,SAAS,OAAO,SAAS,OAAO,UAAU,KAAK,CAAC,EAAC;AAC5D;AAIA,SAAS,aACP,OACA,EAAE,OAAM,GAAuB;AAE/B,SAAO;IACL,SAAS;IACT,SAAS,YAAY,OAAO;MAC1B,MAAM;MACN;KACD;;AAEL;AAWA,SAAS,aAAa,OAAa;AACjC,QAAM,WAAW,YAAY,KAAK;AAClC,QAAM,cAAc,KAAK,KAAK,KAAK,QAAQ,IAAI,EAAE;AACjD,QAAM,QAAe,CAAA;AACrB,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,UAAM,KACJ,OAAO,MAAM,UAAU,IAAI,KAAK,IAAI,KAAK,EAAE,GAAG;MAC5C,KAAK;KACN,CAAC;;AAGN,SAAO;IACL,SAAS;IACT,SAAS,OAAO;MACd,OAAO,YAAY,KAAK,QAAQ,GAAG,EAAE,MAAM,GAAE,CAAE,CAAC;MAChD,GAAG;KACJ;;AAEL;AAQA,SAAS,YAGP,OACA,EAAE,MAAK,GAAqB;AAE5B,MAAI,UAAU;AACd,QAAM,iBAAkC,CAAA;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,KAAK;AAChD,UAAM,SAAS,MAAM,WAAW,CAAC;AACjC,UAAM,QAAQ,MAAM,QAAQ,KAAK,IAAI,IAAI,OAAO;AAChD,UAAM,gBAAgB,aAAa;MACjC,OAAO;MACP,OAAQ,MAAc,KAAM;KAC7B;AACD,mBAAe,KAAK,aAAa;AACjC,QAAI,cAAc;AAAS,gBAAU;;AAEvC,SAAO;IACL;IACA,SAAS,UACL,aAAa,cAAc,IAC3B,OAAO,eAAe,IAAI,CAAC,EAAE,QAAO,MAAO,OAAO,CAAC;;AAE3D;AAIM,SAAU,mBACd,MAAY;AAEZ,QAAM,UAAU,KAAK,MAAM,kBAAkB;AAC7C,SAAO;;IAEH,CAAC,QAAQ,CAAC,IAAI,OAAO,QAAQ,CAAC,CAAC,IAAI,MAAM,QAAQ,CAAC,CAAC;MACnD;AACN;;;ACvWA,IAAMC,QAAO,CAAC,UAAkB,UAAUC,SAAQ,KAAK,CAAC;AASjD,IAAM,sBAAsB,CAAC,OAClC,MAAMD,MAAK,qBAAqB,EAAE,CAAC,GAAG,GAAG,CAAC;;;ACqBtC,SAAU,WAGd,EACA,KACA,OAAO,CAAA,GACP,KAAI,GACkC;AAItC,QAAM,aAAa,MAAM,MAAM,EAAE,QAAQ,MAAK,CAAE;AAEhD,QAAM,WAAY,IAAY,OAAO,CAAC,YAAW;AAC/C,QAAI,YAAY;AACd,UAAI,QAAQ,SAAS;AACnB,eAAO,oBAAoB,OAAO,MAAM;AAC1C,UAAI,QAAQ,SAAS;AAAS,eAAO,iBAAiB,OAAO,MAAM;AACnE,aAAO;;AAET,WAAO,UAAU,WAAW,QAAQ,SAAS;EAC/C,CAAC;AAED,MAAI,SAAS,WAAW;AAAG,WAAO;AAClC,MAAI,SAAS,WAAW;AAAG,WAAO,SAAS,CAAC;AAE5C,MAAI,iBAAsC;AAC1C,aAAW,WAAW,UAAU;AAC9B,QAAI,EAAE,YAAY;AAAU;AAC5B,QAAI,CAAC,QAAQ,KAAK,WAAW,GAAG;AAC9B,UAAI,CAAC,QAAQ,UAAU,QAAQ,OAAO,WAAW;AAAG,eAAO;AAC3D;;AAEF,QAAI,CAAC,QAAQ;AAAQ;AACrB,QAAI,QAAQ,OAAO,WAAW;AAAG;AACjC,QAAI,QAAQ,OAAO,WAAW,KAAK;AAAQ;AAC3C,UAAM,UAAW,KAA4B,MAAM,CAAC,KAAK,UAAS;AAChE,YAAM,eAAe,YAAY,WAAW,QAAQ,OAAQ,KAAK;AACjE,UAAI,CAAC;AAAc,eAAO;AAC1B,aAAO,YAAY,KAAK,YAA4B;IACtD,CAAC;AACD,QAAI,SAAS;AAEX,UACE,kBACA,YAAY,kBACZ,eAAe,QACf;AACA,cAAM,iBAAiB,kBACrB,QAAQ,QACR,eAAe,QACf,IAA0B;AAE5B,YAAI;AACF,gBAAM,IAAI,sBACR;YACE;YACA,MAAM,eAAe,CAAC;aAExB;YACE,SAAS;YACT,MAAM,eAAe,CAAC;WACvB;;AAIP,uBAAiB;;;AAIrB,MAAI;AACF,WAAO;AACT,SAAO,SAAS,CAAC;AACnB;AAIM,SAAU,YAAY,KAAc,cAA0B;AAClE,QAAM,UAAU,OAAO;AACvB,QAAM,mBAAmB,aAAa;AACtC,UAAQ,kBAAkB;IACxB,KAAK;AACH,aAAO,UAAU,GAAc;IACjC,KAAK;AACH,aAAO,YAAY;IACrB,KAAK;AACH,aAAO,YAAY;IACrB,KAAK;AACH,aAAO,YAAY;IACrB,SAAS;AACP,UAAI,qBAAqB,WAAW,gBAAgB;AAClD,eAAO,OAAO,OAAO,aAAa,UAAU,EAAE,MAC5C,CAAC,WAAW,UAAS;AACnB,iBAAO,YACL,OAAO,OAAO,GAA0C,EAAE,KAAK,GAC/D,SAAyB;QAE7B,CAAC;AAKL,UACE,+HAA+H,KAC7H,gBAAgB;AAGlB,eAAO,YAAY,YAAY,YAAY;AAI7C,UAAI,uCAAuC,KAAK,gBAAgB;AAC9D,eAAO,YAAY,YAAY,eAAe;AAIhD,UAAI,oCAAoC,KAAK,gBAAgB,GAAG;AAC9D,eACE,MAAM,QAAQ,GAAG,KACjB,IAAI,MAAM,CAAC,MACT,YAAY,GAAG;UACb,GAAG;;UAEH,MAAM,iBAAiB,QAAQ,oBAAoB,EAAE;SACtC,CAAC;;AAKxB,aAAO;;;AAGb;AAEM,SAAU,kBACd,kBACA,kBACA,MAAwB;AAExB,aAAW,kBAAkB,kBAAkB;AAC7C,UAAM,kBAAkB,iBAAiB,cAAc;AACvD,UAAM,kBAAkB,iBAAiB,cAAc;AAEvD,QACE,gBAAgB,SAAS,WACzB,gBAAgB,SAAS,WACzB,gBAAgB,mBAChB,gBAAgB;AAEhB,aAAO,kBACL,gBAAgB,YAChB,gBAAgB,YACf,KAAa,cAAc,CAAC;AAGjC,UAAM,QAAQ,CAAC,gBAAgB,MAAM,gBAAgB,IAAI;AAEzD,UAAM,aAAa,MAAK;AACtB,UAAI,MAAM,SAAS,SAAS,KAAK,MAAM,SAAS,SAAS;AAAG,eAAO;AACnE,UAAI,MAAM,SAAS,SAAS,KAAK,MAAM,SAAS,QAAQ;AACtD,eAAO,UAAU,KAAK,cAAc,CAAY;AAClD,UAAI,MAAM,SAAS,SAAS,KAAK,MAAM,SAAS,OAAO;AACrD,eAAO,UAAU,KAAK,cAAc,CAAY;AAClD,aAAO;IACT,GAAE;AAEF,QAAI;AAAW,aAAO;;AAGxB;AACF;;;AClKM,SAAU,mBAGd,EACA,KACA,MACA,aAAY,GACsC;AAClD,MAAI,UAAU,IAAI,CAAC;AACnB,MAAI,cAAc;AAChB,cAAU,WAAW;MACnB;MACA;MACA,MAAM;KACiB;AACzB,QAAI,CAAC;AACH,YAAM,IAAI,yBAAyB,cAAc;QAC/C,UAAU;OACX;;AAGL,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,yBAAyB,QAAW;MAC5C,UAAU;KACX;AAEH,QAAM,aAAaE,eAAc,OAAO;AACxC,QAAM,YAAY,oBAAoB,UAAU;AAChD,QAAM,OACJ,YAAY,WAAW,QAAQ,SAC3B,oBAAoB,QAAQ,QAAS,QAAQ,CAAA,CAAyB,IACtE;AACN,SAAO,UAAU,CAAC,WAAW,QAAQ,IAAI,CAAC;AAC5C;;;ACnEM,SAAU,gBAAgB,UAAmB,SAAgB;AACjE,QAAM,aAAa,UACf,GAAG,OAAO,GAAG,SAAS,YAAW,CAAE,KACnC,SAAS,UAAU,CAAC,EAAE,YAAW;AACrC,QAAMC,QAAO,UAAU,cAAc,UAAU,GAAG,OAAO;AAEzD,QAAM,WACJ,UAAU,WAAW,UAAU,GAAG,OAAO,KAAK,MAAM,IAAI,YACxD,MAAM,EAAE;AACV,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,QAAIA,MAAK,KAAK,CAAC,KAAK,KAAK,KAAK,QAAQ,CAAC,GAAG;AACxC,cAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,YAAW;;AAErC,SAAKA,MAAK,KAAK,CAAC,IAAI,OAAS,KAAK,QAAQ,IAAI,CAAC,GAAG;AAChD,cAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE,YAAW;;;AAI/C,SAAO,KAAK,QAAQ,KAAK,EAAE,CAAC;AAC9B;AAOM,SAAU,WAAW,SAAiB,SAAgB;AAC1D,MAAI,CAAC,UAAU,OAAO;AAAG,UAAM,IAAI,oBAAoB,EAAE,QAAO,CAAE;AAClE,SAAO,gBAAgB,SAAS,OAAO;AACzC;;;ACSM,SAAU,oBAEd,QAAiB,MAAS;AAC1B,MAAI,SAAS,QAAS,OAAqB,SAAS;AAClD,UAAM,IAAI,yBAAwB;AACpC,MAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI;AAC7B,UAAM,IAAI,iCAAiC;MACzC;MACA;MACA,MAAM,KAAK,IAAI;KAChB;AACH,SAAO,aAAa;IAClB;IACA;GACD;AACH;AAQA,SAAS,aAA4D,EACnE,MACA,OAAM,GACyB;AAC/B,QAAM,gBAA2B,CAAA;AACjC,MAAI,WAAW;AAEf,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,YAAY,KAAK,IAAI;AACvB,YAAM,IAAI,iCAAiC;QACzC;QACA;QACA,MAAM,KAAK,IAAI;OAChB;AAEH,UAAM,QAAQ,OAAO,CAAC;AACtB,UAAM,EAAE,UAAU,MAAK,IAAK,YAAY,EAAE,MAAM,OAAO,SAAQ,CAAE;AACjE,kBAAc,KAAK,KAAK;AAExB,gBAAY;;AAGd,SAAO;AACT;AAaA,SAAS,YAAY,EACnB,MACA,OACA,SAAQ,GAC6C;AAIrD,QAAM,kBAAkB,mBAAmB,MAAM,IAAI;AACrD,MAAI,iBAAiB;AACnB,UAAM,CAAC,QAAQ,IAAI,IAAI;AACvB,WAAO,YAAY,MAAM;MACvB;MACA,OAAO,EAAE,GAAG,OAAO,KAAU;MAC7B;KACD;;AAEH,MAAI,MAAM,SAAS,SAAS;AAC1B,WAAO,YAAY,MAAM,EAAE,OAAmC,SAAQ,CAAE;;AAE1E,MAAI,MAAM,SAAS,UAAU;AAC3B,WAAO,aAAa,MAAM,EAAE,SAAQ,CAAE;;AAExC,MAAI,MAAM,KAAK,WAAW,OAAO,GAAG;AAClC,WAAO,YAAY,MAAM,EAAE,OAAO,SAAQ,CAAE;;AAG9C,QAAM,QAAQ,MAAM,MAAM,UAAU,WAAW,IAAI,EAAE,QAAQ,KAAI,CAAE;AACnE,MAAI,MAAM,KAAK,WAAW,MAAM,KAAK,MAAM,KAAK,WAAW,KAAK,GAAG;AACjE,WAAO,aAAa,OAAO,EAAE,MAAK,CAAE;;AAEtC,MAAI,MAAM,SAAS,WAAW;AAC5B,WAAO,cAAc,KAAK;;AAE5B,MAAI,MAAM,SAAS,QAAQ;AACzB,WAAO,WAAW,KAAK;;AAEzB,QAAM,IAAI,4BAA4B,MAAM,MAAM;IAChD,UAAU;GACX;AACH;AASA,SAAS,cAAc,OAAU;AAC/B,SAAO,EAAE,UAAU,IAAI,OAAO,gBAAgB,MAAM,OAAO,GAAG,CAAC,EAAC;AAClE;AAIA,SAAS,YACP,MACA,EACE,OACA,QACA,SAAQ,GAKT;AAID,MAAI,CAAC,QAAQ;AAEX,UAAM,SAAS,YACb,MAAM,MAAM,UAAU,WAAW,IAAI,EAAE,QAAQ,KAAI,CAAE,CAAC;AAGxD,UAAMC,UAAS,YACb,MAAM,MAAM,QAAQ,SAAS,IAAI,EAAE,QAAQ,KAAI,CAAE,CAAC;AAGpD,QAAIC,YAAW;AACf,UAAMC,SAA+C,CAAA;AACrD,aAAS,IAAI,GAAG,IAAIF,SAAQ,EAAE,GAAG;AAC/B,YAAM,eAAe,YAAY;QAC/B,MAAM,MAAM,MAAM,SAAS,EAAE;QAC7B;QACA,UAAUC;OACX;AACD,MAAAA,aAAY,aAAa;AACzB,MAAAC,OAAM,KAAK,aAAa,KAAK;;AAE/B,WAAO,EAAE,OAAAA,QAAO,UAAU,GAAE;;AAM9B,MAAI,gBAAgB,KAAK,GAAG;AAE1B,UAAM,kBAAkB,mBAAmB,MAAM,IAAI;AAErD,UAAM,eAAe,EAAC,mDAAkB;AAExC,QAAID,YAAW;AACf,UAAMC,SAA+C,CAAA;AACrD,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,YAAM,SAAS,YACb,MAAM,MAAM,UAAU,WAAW,IAAI,EAAE,QAAQ,KAAI,CAAE,CAAC;AAExD,YAAM,eAAe,YAAY;QAC/B,MAAM,MAAM,MAAM,MAAM;QACxB;QACA,UAAU,eAAeD,YAAW,IAAI;OACzC;AACD,MAAAA,aAAY,aAAa;AACzB,MAAAC,OAAM,KAAK,aAAa,KAAK;;AAE/B,WAAO,EAAE,OAAAA,QAAO,UAAU,GAAE;;AAM9B,MAAI,WAAW;AACf,QAAM,QAA+C,CAAA;AACrD,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,UAAM,eAAe,YAAY;MAC/B;MACA;MACA,UAAU,WAAW;KACtB;AACD,gBAAY,aAAa;AACzB,UAAM,KAAK,aAAa,KAAK;;AAE/B,SAAO,EAAE,OAAO,SAAQ;AAC1B;AAIA,SAAS,WAAW,OAAU;AAC5B,SAAO,EAAE,UAAU,IAAI,OAAO,UAAU,KAAK,EAAC;AAChD;AAIA,SAAS,YACP,MACA,EAAE,OAAO,SAAQ,GAAuC;AAExD,QAAM,CAAC,GAAGC,KAAI,IAAI,MAAM,KAAK,MAAM,OAAO;AAC1C,MAAI,CAACA,OAAM;AAGT,UAAM,SAAS,YACb,MAAM,MAAM,UAAU,WAAW,IAAI,EAAE,QAAQ,KAAI,CAAE,CAAC;AAExD,UAAM,SAAS,YACb,MAAM,MAAM,QAAQ,SAAS,IAAI,EAAE,QAAQ,KAAI,CAAE,CAAC;AAGpD,QAAI,WAAW;AAAG,aAAO,EAAE,UAAU,IAAI,OAAO,KAAI;AACpD,UAAMD,SAAQ,MAAM,MAAM,SAAS,IAAI,SAAS,KAAK,QAAQ;MAC3D,QAAQ;KACT;AACD,WAAO,EAAE,UAAU,IAAI,OAAAA,OAAK;;AAG9B,QAAM,QAAQ,MAAM,MAAM,UAAU,WAAW,SAASC,KAAI,GAAG;IAC7D,QAAQ;GACT;AACD,SAAO,EAAE,UAAU,IAAI,MAAK;AAC9B;AAOA,SAAS,aACP,OACA,EAAE,MAAK,GAAqB;AAE5B,QAAM,SAAS,MAAM,KAAK,WAAW,KAAK;AAC1C,QAAMA,QAAO,SAAS,MAAM,KAAK,MAAM,KAAK,EAAE,CAAC,KAAK,KAAK;AACzD,SAAO;IACL,UAAU;IACV,OACEA,QAAO,KACH,YAAY,OAAO,EAAE,OAAM,CAAE,IAC7B,YAAY,OAAO,EAAE,OAAM,CAAE;;AAEvC;AASA,SAAS,aAAa,MAAW,EAAE,SAAQ,GAAwB;AACjE,QAAM,SAAS,YACb,MAAM,MAAM,UAAU,WAAW,IAAI,EAAE,QAAQ,KAAI,CAAE,CAAC;AAExD,QAAM,SAAS,YAAY,MAAM,MAAM,QAAQ,SAAS,IAAI,EAAE,QAAQ,KAAI,CAAE,CAAC;AAE7E,MAAI,WAAW;AAAG,WAAO,EAAE,UAAU,IAAI,OAAO,GAAE;AAClD,QAAM,QAAQ,YACZ,KAAK,MAAM,MAAM,SAAS,IAAI,SAAS,KAAK,QAAQ,EAAE,QAAQ,KAAI,CAAE,CAAC,CAAC;AAExE,SAAO,EAAE,UAAU,IAAI,MAAK;AAC9B;AAQA,SAAS,YAEP,MAAW,EAAE,OAAO,SAAQ,GAAuC;AAKnE,QAAM,kBACJ,MAAM,WAAW,WAAW,KAAK,MAAM,WAAW,KAAK,CAAC,EAAE,KAAI,MAAO,CAAC,IAAI;AAI5E,QAAM,QAAa,kBAAkB,CAAA,IAAK,CAAA;AAC1C,MAAI,WAAW;AAIf,MAAI,gBAAgB,KAAK,GAAG;AAC1B,UAAM,SAAS,YACb,MAAM,MAAM,UAAU,WAAW,IAAI,EAAE,QAAQ,KAAI,CAAE,CAAC;AAGxD,aAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,YAAM,YAAY,MAAM,WAAW,CAAC;AACpC,YAAM,eAAe,YAAY;QAC/B,MAAM,MAAM,MAAM,MAAM;QACxB,OAAO;QACP,UAAU;OACX;AACD,kBAAY,aAAa;AACzB,YAAM,kBAAkB,IAAI,uCAAW,IAAK,IAAI,aAAa;;AAE/D,WAAO,EAAE,UAAU,IAAI,MAAK;;AAK9B,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,UAAM,YAAY,MAAM,WAAW,CAAC;AACpC,UAAM,eAAe,YAAY;MAC/B;MACA,OAAO;MACP,UAAU,WAAW;KACtB;AACD,gBAAY,aAAa;AACzB,UAAM,kBAAkB,IAAI,uCAAW,IAAK,IAAI,aAAa;;AAE/D,SAAO,EAAE,UAAU,MAAK;AAC1B;AAIA,SAAS,gBAAgB,OAAmB;AAzX5C;AA0XE,QAAM,EAAE,KAAI,IAAK;AACjB,MAAI,SAAS;AAAU,WAAO;AAC9B,MAAI,SAAS;AAAS,WAAO;AAC7B,MAAI,KAAK,SAAS,IAAI;AAAG,WAAO;AAEhC,MAAI,SAAS;AAAS,YAAQ,WAAc,eAAd,mBAA0B,KAAK;AAE7D,QAAM,kBAAkB,mBAAmB,MAAM,IAAI;AACrD,MACE,mBACA,gBAAgB,EAAE,GAAG,OAAO,MAAM,gBAAgB,CAAC,EAAC,CAAkB;AAEtE,WAAO;AAET,SAAO;AACT;;;AC5YO,IAAM,eAAe;EAC1B,GAAG;EACH,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;;AAGC,IAAM,gBAA0B;EACrC,QAAQ;IACN;MACE,MAAM;MACN,MAAM;;;EAGV,MAAM;EACN,MAAM;;AAED,IAAM,gBAA0B;EACrC,QAAQ;IACN;MACE,MAAM;MACN,MAAM;;;EAGV,MAAM;EACN,MAAM;;;;ACkBF,SAAU,kBAA+D,EAC7E,KACA,KAAI,GAC8B;AAClC,QAAM,YAAY,MAAM,MAAM,GAAG,CAAC;AAClC,MAAI,cAAc;AAAM,UAAM,IAAI,yBAAwB;AAE1D,QAAM,OAAO,CAAC,GAAK,OAAe,CAAA,GAAK,eAAe,aAAa;AACnE,QAAM,UAAU,KAAK,KACnB,CAAC,MACC,EAAE,SAAS,WAAW,cAAc,oBAAoBC,eAAc,CAAC,CAAC,CAAC;AAE7E,MAAI,CAAC;AACH,UAAM,IAAI,+BAA+B,WAAW;MAClD,UAAU;KACX;AACH,SAAO;IACL;IACA,MAAO,YAAY,WAAW,QAAQ,UAAU,QAAQ,OAAO,SAAS,IACpE,oBAAoB,QAAQ,QAAQ,MAAM,MAAM,CAAC,CAAC,IAClD;IACJ,WAAY,QAA6B;;AAE7C;;;ACtEO,IAAM,YAAmC,CAAC,OAAO,UAAU,UAChE,KAAK,UACH,OACA,CAAC,KAAK,WAAU;AACd,QAAMC,SAAQ,OAAO,WAAW,WAAW,OAAO,SAAQ,IAAK;AAC/D,SAAO,OAAO,aAAa,aAAa,SAAS,KAAKA,MAAK,IAAIA;AACjE,GACA,KAAK;;;ACMH,SAAU,YAAY,KAAa,OAAuB,OAAK;AACnE,SAAO,YAAY,KAAK,WAAW,IAAI,CAAC;AAC1C;;;ACRM,SAAU,YACd,MAA4E;AAE5E,QAAM,UAAU,OAAO,QAAQ,IAAI,EAChC,IAAI,CAAC,CAAC,KAAK,KAAK,MAAK;AACpB,QAAI,UAAU,UAAa,UAAU;AAAO,aAAO;AACnD,WAAO,CAAC,KAAK,KAAK;EACpB,CAAC,EACA,OAAO,OAAO;AACjB,QAAM,YAAY,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC;AAC7E,SAAO,QACJ,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,KAAK,GAAG,GAAG,IAAI,OAAO,YAAY,CAAC,CAAC,KAAK,KAAK,EAAE,EACtE,KAAK,IAAI;AACd;AAKM,IAAO,mBAAP,cAAgC,UAAS;EAE7C,cAAA;AACE,UACE;MACE;MACA;MACA,KAAK,IAAI,CAAC;AANP,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAMI,IAAO,sBAAP,cAAmC,UAAS;EAGhD,YAAY,EAAE,EAAC,GAAiB;AAC9B,UAAM,wBAAwB,CAAC,uBAAuB;AAH/C,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIhB;;AAOI,IAAO,sCAAP,cAAmD,UAAS;EAGhE,YAAY,EAAE,YAAW,GAA4C;AACnE,UAAM,8DAA8D;MAClE,cAAc;QACZ;QACA;QACA,YAAY,WAAW;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;;KAEH;AAhBM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAiBhB;;AAOI,IAAO,wCAAP,cAAqD,UAAS;EAKlE,YAAY,EAAE,eAAc,GAA2B;AACrD,UAAM,gCAAgC,cAAc,eAAe;AAL5D,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,kBAAA;;;;;;AAKE,SAAK,iBAAiB;EACxB;;AAOI,IAAO,oCAAP,cAAiD,UAAS;EAM9D,YAAY,EACV,YACA,uBACA,KAAI,GAKL;AACC,UAAM,UAAU,OAAO,QAAQ,UAAU,EACtC,IAAI,CAAC,CAAC,KAAK,KAAK,MAAO,OAAO,UAAU,cAAc,MAAM,MAAU,EACtE,OAAO,OAAO;AACjB,UAAM,2CAA2C,IAAI,mBAAmB;MACtE,cAAc;QACZ,4BAA4B,qBAAqB;QACjD,QAAQ,SAAS,IAAI,uBAAuB,QAAQ,KAAK,IAAI,CAAC,KAAK;QACnE,OAAO,OAAO;KACjB;AAtBM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,yBAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AAqBE,SAAK,wBAAwB;AAC7B,SAAK,OAAO;EACd;;AAMI,IAAO,6BAAP,cAA0C,UAAS;EAGvD,YAAY,EAAE,WAAU,GAAuB;AAC7C,UACE,yBAAyB,UAAU,wCAAwC,KAAK,OAC7E,WAAW,SAAS,KAAK,CAAC,CAC5B,SAAS;AANL,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAMI,IAAO,4BAAP,cAAyC,UAAS;EAKtD,YACE,OACA,EACE,SACA,UAAAC,WACA,OACA,MACA,KACA,UACA,cACA,sBACA,OACA,IACA,MAAK,GAKN;AAlKL;AAoKI,UAAM,aAAa,YAAY;MAC7B,OAAO,SAAS,GAAG,+BAAO,IAAI,SAAS,+BAAO,EAAE;MAChD,MAAM,mCAAS;MACf;MACA,OACE,OAAO,UAAU,eACjB,GAAG,YAAY,KAAK,CAAC,MAAI,oCAAO,mBAAP,mBAAuB,WAAU,KAAK;MACjE;MACA;MACA,UACE,OAAO,aAAa,eAAe,GAAG,WAAW,QAAQ,CAAC;MAC5D,cACE,OAAO,iBAAiB,eACxB,GAAG,WAAW,YAAY,CAAC;MAC7B,sBACE,OAAO,yBAAyB,eAChC,GAAG,WAAW,oBAAoB,CAAC;MACrC;KACD;AAED,UAAM,MAAM,cAAc;MACxB;MACA,UAAAA;MACA,cAAc;QACZ,GAAI,MAAM,eAAe,CAAC,GAAG,MAAM,cAAc,GAAG,IAAI,CAAA;QACxD;QACA;QACA,OAAO,OAAO;KACjB;AApDM,WAAA,eAAA,MAAA,SAAA;;;;;;AAEA,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAmDd,SAAK,QAAQ;EACf;;AAMI,IAAO,2BAAP,cAAwC,UAAS;EAErD,YAAY,EACV,WACA,aACA,UACA,MAAAC,OACA,MAAK,GAON;AACC,QAAI,aAAa;AACjB,QAAI,YAAY,UAAU;AACxB,mBAAa,8BAA8B,QAAQ,eAAe,KAAK;AACzE,QAAI,aAAa,UAAU;AACzB,mBAAa,8BAA8B,SAAS,eAAe,KAAK;AAC1E,QAAI,eAAe,UAAU;AAC3B,mBAAa,gCAAgC,WAAW,eAAe,KAAK;AAC9E,QAAIA;AAAM,mBAAa,0BAA0BA,KAAI;AACrD,UAAM,GAAG,UAAU,sBAAsB;AAtBlC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAuBhB;;AAOI,IAAO,kCAAP,cAA+C,UAAS;EAE5D,YAAY,EAAE,MAAAA,MAAI,GAAkB;AAClC,UACE,kCAAkCA,KAAI,4EAA4E;AAH7G,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;AAOI,IAAO,wCAAP,cAAqD,UAAS;EAElE,YAAY,EAAE,MAAAA,MAAI,GAAkB;AAClC,UACE,sDAAsDA,KAAI,oBAAoB;AAHzE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;;;ACzPI,SAAU,aAAa,SAA0B;AACrD,MAAI,OAAO,YAAY;AAAU,WAAO,EAAE,SAAS,SAAS,MAAM,WAAU;AAC5E,SAAO;AACT;;;ACFM,SAAU,sBAAsB,EACpC,SACA,MACA,sBAAsB,MACtB,cAAc,MAAK,GAMpB;AACC,MAAI,EAAE,UAAU;AAAU;AAC1B,MAAI,EAAE,YAAY;AAAU;AAC5B,MAAI,CAAC,QAAQ;AAAQ;AACrB,SAAO,GAAG,sBAAsB,QAAQ,OAAO,EAAE,IAAI,QAAQ,OAC1D,IACC,CAAC,OAAqB,MACpB,GAAG,eAAe,MAAM,OAAO,GAAG,MAAM,IAAI,OAAO,EAAE,GACnD,OAAO,KAAK,CAAC,MAAM,WAAW,UAAU,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAC3D,EAAE,EAEL,KAAK,IAAI,CAAC;AACf;;;ACLM,IAAO,qBAAP,cAAkC,UAAS;EAK/C,YACE,OACA,EACE,SAAS,UACT,UAAAC,WACA,OACA,MACA,KACA,UACA,cACA,sBACA,OACA,IACA,MAAK,GACiD;AA1C5D;AA4CI,UAAM,UAAU,WAAW,aAAa,QAAQ,IAAI;AACpD,UAAM,aAAa,YAAY;MAC7B,MAAM,mCAAS;MACf;MACA,OACE,OAAO,UAAU,eACjB,GAAG,YAAY,KAAK,CAAC,MAAI,oCAAO,mBAAP,mBAAuB,WAAU,KAAK;MACjE;MACA;MACA,UACE,OAAO,aAAa,eAAe,GAAG,WAAW,QAAQ,CAAC;MAC5D,cACE,OAAO,iBAAiB,eACxB,GAAG,WAAW,YAAY,CAAC;MAC7B,sBACE,OAAO,yBAAyB,eAChC,GAAG,WAAW,oBAAoB,CAAC;MACrC;KACD;AAED,UAAM,MAAM,cAAc;MACxB;MACA,UAAAA;MACA,cAAc;QACZ,GAAI,MAAM,eAAe,CAAC,GAAG,MAAM,cAAc,GAAG,IAAI,CAAA;QACxD;QACA;QACA,OAAO,OAAO;KACjB;AAhDM,WAAA,eAAA,MAAA,SAAA;;;;;;AAEA,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AA+Cd,SAAK,QAAQ;EACf;;AAOI,IAAO,iCAAP,cAA8C,UAAS;EAW3D,YACE,OACA,EACE,KACA,MACA,iBACA,UAAAA,WACA,cACA,OAAM,GAQP;AAED,UAAM,UAAU,WAAW,EAAE,KAAK,MAAM,MAAM,aAAY,CAAE;AAC5D,UAAM,gBAAgB,UAClB,sBAAsB;MACpB;MACA;MACA,qBAAqB;MACrB,aAAa;KACd,IACD;AACJ,UAAM,qBAAqB,UACvBC,eAAc,SAAS,EAAE,aAAa,KAAI,CAAE,IAC5C;AAEJ,UAAM,aAAa,YAAY;MAC7B,SAAS,mBAAmB,mBAAmB,eAAe;MAC9D,UAAU;MACV,MACE,iBACA,kBAAkB,QAClB,GAAG,CAAC,GAAG,OAAM,6CAAc,WAAU,CAAC,EAAE,KAAI,CAAE,EAC3C,IAAI,MAAM,GAAG,EACb,KAAK,EAAE,CAAC,GAAG,aAAa;MAC7B;KACD;AAED,UACE,MAAM,gBACJ,oEAAoE,YAAY,MAClF;MACE;MACA,UAAAD;MACA,cAAc;QACZ,GAAI,MAAM,eAAe,CAAC,GAAG,MAAM,cAAc,GAAG,IAAI,CAAA;QACxD;QACA;QACA,OAAO,OAAO;KACjB;AAhEL,WAAA,eAAA,MAAA,OAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AACS,WAAA,eAAA,MAAA,SAAA;;;;;;AACT,WAAA,eAAA,MAAA,mBAAA;;;;;;AACA,WAAA,eAAA,MAAA,iBAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,UAAA;;;;;;AAES,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AA0Dd,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,SAAS;EAChB;;AAOI,IAAO,gCAAP,cAA6C,UAAS;EAO1D,YAAY,EACV,KACA,MACA,cACA,QAAO,GAC0D;AACjE,QAAI;AACJ,QAAI,cAAuD;AAC3D,QAAI;AACJ,QAAI;AACJ,QAAI,QAAQ,SAAS,MAAM;AACzB,UAAI;AACF,sBAAc,kBAAkB,EAAE,KAAK,KAAI,CAAE;AAC7C,cAAM,EAAE,SAAS,WAAW,MAAM,UAAS,IAAK;AAChD,YAAI,cAAc,SAAS;AACzB,mBAAU,UAAuB,CAAC;mBACzB,cAAc,SAAS;AAChC,gBAAM,CAAC,QAAQ,IAAI;AACnB,mBAAS,aAAa,QAAqC;eACtD;AACL,gBAAM,kBAAkB,UACpBC,eAAc,SAAS,EAAE,aAAa,KAAI,CAAE,IAC5C;AACJ,gBAAM,gBACJ,WAAW,YACP,sBAAsB;YACpB;YACA,MAAM;YACN,qBAAqB;YACrB,aAAa;WACd,IACD;AAEN,yBAAe;YACb,kBAAkB,UAAU,eAAe,KAAK;YAChD,iBAAiB,kBAAkB,OAC/B,UAAU,CAAC,GAAG,OAAM,uCAAW,WAAU,CAAC,EAAE,KAAI,CAAE,EAC/C,IAAI,MAAM,GAAG,EACb,KAAK,EAAE,CAAC,GAAG,aAAa,KAC3B;;;eAGD,KAAK;AACZ,gBAAQ;;eAED;AAAS,eAAS;AAE7B,QAAI;AACJ,QAAI,iBAAiB,gCAAgC;AACnD,kBAAY,MAAM;AAClB,qBAAe;QACb,+BAA+B,SAAS;QACxC;QACA,sFAAsF,SAAS;;;AAInG,UACG,UAAU,WAAW,wBAAyB,YAC3C;MACE,0BAA0B,YAAY,iCACpC,YAAY,cAAc,QAC5B;MACA,UAAU;MACV,KAAK,IAAI,IACX,0BAA0B,YAAY,eAC1C;MACE;MACA;KACD;AA3EI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,aAAA;;;;;;AA0EE,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,YAAY;EACnB;;AAOI,IAAO,gCAAP,cAA6C,UAAS;EAE1D,YAAY,EAAE,aAAY,GAA4B;AACpD,UAAM,0BAA0B,YAAY,8BAA8B;MACxE,cAAc;QACZ;QACA,gDAAgD,YAAY;QAC5D;QACA;;KAEH;AATM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAUhB;;AAMI,IAAO,mBAAP,cAAgC,UAAS;EAM7C,YAAY,EACV,MACA,QAAO,GAC2C;AAClD,UAAM,WAAW,EAAE;AATrB,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AACE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,QAAA;;;;;;AAOE,SAAK,OAAO;EACd;;;;ACjRI,IAAO,mBAAP,cAAgC,UAAS;EAQ7C,YAAY,EACV,MACA,SACA,SACA,QACA,IAAG,GAOJ;AACC,UAAM,wBAAwB;MAC5B;MACA,cAAc;QACZ,UAAU,WAAW,MAAM;QAC3B,QAAQ,OAAO,GAAG,CAAC;QACnB,QAAQ,iBAAiB,UAAU,IAAI,CAAC;QACxC,OAAO,OAAO;KACjB;AA3BM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,OAAA;;;;;;AAuBE,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,MAAM;EACb;;AAMI,IAAO,wBAAP,cAAqC,UAAS;EAGlD,YAAY,EACV,MACA,SACA,IAAG,GAKJ;AACC,UAAM,6BAA6B;MACjC;MACA,cAAc,CAAC,QAAQ,OAAO,GAAG,CAAC,IAAI,iBAAiB,UAAU,IAAI,CAAC,EAAE;KACzE;AAdM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAehB;;AAMI,IAAO,kBAAP,cAA+B,UAAS;EAK5C,YAAY,EACV,MACA,OACA,IAAG,GAKJ;AACC,UAAM,uBAAuB;MAC3B,OAAO;MACP,SAAS,MAAM;MACf,cAAc,CAAC,QAAQ,OAAO,GAAG,CAAC,IAAI,iBAAiB,UAAU,IAAI,CAAC,EAAE;KACzE;AAjBM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,QAAA;;;;;;AAgBE,SAAK,OAAO,MAAM;EACpB;;AAMI,IAAO,eAAP,cAA4B,UAAS;EAGzC,YAAY,EACV,MACA,IAAG,GAIJ;AACC,UAAM,yCAAyC;MAC7C,SAAS;MACT,cAAc,CAAC,QAAQ,OAAO,GAAG,CAAC,IAAI,iBAAiB,UAAU,IAAI,CAAC,EAAE;KACzE;AAZM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAahB;;;;ACzGF,IAAM,mBAAmB;AA+BnB,IAAO,WAAP,cAA6D,UAAS;EAK1E,YACE,OACA,EAAE,MAAM,UAAAC,WAAU,cAAc,aAAY,GAA0B;AAEtE,UAAM,cAAc;MAClB;MACA,UAAAA;MACA,cACE,iBAAiB,+BAAuC;KAC3D;AAbM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,QAAA;;;;;;AAYE,SAAK,OAAO,MAAM;AAClB,SAAK,OACH,iBAAiB,kBAAkB,MAAM,OAAO,QAAQ;EAE5D;;AAmBI,IAAO,mBAAP,cAEI,SAA8B;EAKtC,YACE,OACA,SAIC;AAED,UAAM,OAAO,OAAO;AAZb,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEhB,WAAA,eAAA,MAAA,QAAA;;;;;;AAYE,SAAK,OAAO,QAAQ;EACtB;;AAYF,IAAa,gBAAb,MAAa,uBAAsB,SAAQ;EAIzC,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,eAAc;MACpB,cACE;KACH;AARM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AARO,OAAA,eAAA,eAAA,QAAA;;;;SAAO;;AAoBhB,IAAa,yBAAb,MAAa,gCAA+B,SAAQ;EAIlD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,wBAAuB;MAC7B,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,wBAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,yBAAb,MAAa,gCAA+B,SAAQ;EAIlD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,wBAAuB;MAC7B,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,wBAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,wBAAb,MAAa,+BAA8B,SAAQ;EAIjD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,uBAAsB;MAC5B,cAAc;QACZ;QACA;QACA,KAAK,IAAI;KACZ;AAVM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;AAVO,OAAA,eAAA,uBAAA,QAAA;;;;SAAO;;AAsBhB,IAAa,mBAAb,MAAa,0BAAyB,SAAQ;EAI5C,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,kBAAiB;MACvB,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,kBAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,uBAAb,MAAa,8BAA6B,SAAQ;EAIhD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,sBAAqB;MAC3B,cAAc;QACZ;QACA;QACA,KAAK,IAAI;KACZ;AAVM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;AAVO,OAAA,eAAA,sBAAA,QAAA;;;;SAAO;;AAsBhB,IAAa,2BAAb,MAAa,kCAAiC,SAAQ;EAIpD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,0BAAyB;MAC/B,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,0BAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,8BAAb,MAAa,qCAAoC,SAAQ;EAIvD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,6BAA4B;MAClC,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,6BAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,8BAAb,MAAa,qCAAoC,SAAQ;EAIvD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,6BAA4B;MAClC,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,6BAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,6BAAb,MAAa,oCAAmC,SAAQ;EAItD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,4BAA2B;MACjC,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,4BAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,wBAAb,MAAa,+BAA8B,SAAQ;EAIjD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,uBAAsB;MAC5B,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,uBAAA,QAAA;;;;SAAO;;AAoBhB,IAAa,iCAAb,MAAa,wCAAuC,SAAQ;EAI1D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,gCAA+B;MACrC,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,gCAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,2BAAb,MAAa,kCAAiC,iBAAgB;EAI5D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,0BAAyB;MAC/B,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,0BAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,4BAAb,MAAa,mCAAkC,iBAAgB;EAI7D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,2BAA0B;MAChC,cACE;KACH;AARM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AARO,OAAA,eAAA,2BAAA,QAAA;;;;SAAO;;AAqBhB,IAAa,iCAAb,MAAa,wCAAuC,iBAAgB;EAIlE,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,gCAA+B;MACrC,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,gCAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,4BAAb,MAAa,mCAAkC,iBAAgB;EAI7D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,2BAA0B;MAChC,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,2BAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,yBAAb,MAAa,gCAA+B,iBAAgB;EAI1D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,wBAAuB;MAC7B,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,wBAAA,QAAA;;;;SAAO;;AAmBhB,IAAa,mBAAb,MAAa,0BAAyB,iBAAgB;EAIpD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,kBAAiB;MACvB,cAAc;KACf;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAPO,OAAA,eAAA,kBAAA,QAAA;;;;SAAO;;AAgBV,IAAO,kBAAP,cAA+B,SAAQ;EAG3C,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,cAAc;KACf;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;;;AC9cI,SAAU,cAAc,MAA6B;AACzD,QAAM,EACJ,SAAS,UACT,UACA,cACA,sBACA,GAAE,IACA;AACJ,QAAM,UAAU,WAAW,aAAa,QAAQ,IAAI;AACpD,MAAI,WAAW,CAAC,UAAU,QAAQ,OAAO;AACvC,UAAM,IAAI,oBAAoB,EAAE,SAAS,QAAQ,QAAO,CAAE;AAC5D,MAAI,MAAM,CAAC,UAAU,EAAE;AAAG,UAAM,IAAI,oBAAoB,EAAE,SAAS,GAAE,CAAE;AACvE,MACE,OAAO,aAAa,gBACnB,OAAO,iBAAiB,eACvB,OAAO,yBAAyB;AAElC,UAAM,IAAI,iBAAgB;AAE5B,MAAI,gBAAgB,eAAe,MAAM,OAAO;AAC9C,UAAM,IAAI,mBAAmB,EAAE,aAAY,CAAE;AAC/C,MACE,wBACA,gBACA,uBAAuB;AAEvB,UAAM,IAAI,oBAAoB,EAAE,cAAc,qBAAoB,CAAE;AACxE;;;ACjCA,IAAM,WAAW;AAkCX,SAAU,qBAGd,EACA,KACA,MACA,cACA,KAAI,GAIL;AACC,MAAI,UAAU,IAAI,CAAC;AACnB,MAAI,cAAc;AAChB,cAAU,WAAW;MACnB;MACA;MACA,MAAM;KACiB;AACzB,QAAI,CAAC;AAAS,YAAM,IAAI,yBAAyB,cAAc,EAAE,SAAQ,CAAE;;AAG7E,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,yBAAyB,QAAW,EAAE,SAAQ,CAAE;AAC5D,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,gCAAgC,QAAQ,MAAM,EAAE,SAAQ,CAAE;AAEtE,QAAM,SAAS,oBAAoB,QAAQ,SAAS,IAAI;AACxD,MAAI,UAAU,OAAO,SAAS;AAAG,WAAO;AACxC,MAAI,UAAU,OAAO,WAAW;AAAG,WAAO,OAAO,CAAC;AAClD,SAAO;AACT;;;AC3FO,IAAM,gBAAgB;EAC3B;IACE,QAAQ;MACN;QACE,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;;QAGV,MAAM;QACN,MAAM;;;IAGV,MAAM;IACN,SAAS;MACP;QACE,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;;QAGV,MAAM;QACN,MAAM;;;IAGV,iBAAiB;IACjB,MAAM;;;AAIV,IAAM,0BAA0B;EAC9B;IACE,QAAQ,CAAA;IACR,MAAM;IACN,MAAM;;EAER;IACE,QAAQ,CAAA;IACR,MAAM;IACN,MAAM;;;AAIH,IAAM,8BAA8B;EACzC,GAAG;EACH;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,QAAQ,MAAM,QAAO;MAC7B,EAAE,MAAM,QAAQ,MAAM,QAAO;;IAE/B,SAAS;MACP,EAAE,MAAM,IAAI,MAAM,QAAO;MACzB,EAAE,MAAM,WAAW,MAAM,UAAS;;;;AAKjC,IAAM,8BAA8B;EACzC,GAAG;EACH;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ,CAAC,EAAE,MAAM,SAAS,MAAM,cAAa,CAAE;IAC/C,SAAS;MACP,EAAE,MAAM,UAAU,MAAM,eAAc;MACtC,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,WAAU;;;;AAKlC,IAAM,kBAAkB;EAC7B;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,QAAQ,MAAM,UAAS;MAC/B,EAAE,MAAM,OAAO,MAAM,SAAQ;;IAE/B,SAAS,CAAC,EAAE,MAAM,IAAI,MAAM,SAAQ,CAAE;;;AAInC,IAAM,qBAAqB;EAChC;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ,CAAC,EAAE,MAAM,QAAQ,MAAM,UAAS,CAAE;IAC1C,SAAS,CAAC,EAAE,MAAM,IAAI,MAAM,UAAS,CAAE;;EAEzC;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,QAAQ,MAAM,UAAS;MAC/B,EAAE,MAAM,YAAY,MAAM,UAAS;;IAErC,SAAS,CAAC,EAAE,MAAM,IAAI,MAAM,QAAO,CAAE;;;AAsBlC,IAAM,iCAAiC;EAC5C;IACE,QAAQ;MACN;QACE,cAAc;QACd,MAAM;QACN,MAAM;;MAER;QACE,cAAc;QACd,MAAM;QACN,MAAM;;MAER;QACE,cAAc;QACd,MAAM;QACN,MAAM;;;IAGV,iBAAiB;IACjB,MAAM;;;;;ACzJJ,SAAU,wBAAwB,EACtC,aACA,OACA,UAAU,KAAI,GAKf;AAjBD;AAkBE,QAAM,YAAY,oCAAO,cAAP,mBAAqD;AACvE,MAAI,CAAC;AACH,UAAM,IAAI,4BAA4B;MACpC;MACA,UAAU,EAAE,KAAI;KACjB;AAEH,MACE,eACA,SAAS,gBACT,SAAS,eAAe;AAExB,UAAM,IAAI,4BAA4B;MACpC;MACA;MACA,UAAU;QACR;QACA,cAAc,SAAS;;KAE1B;AAEH,SAAO,SAAS;AAClB;;;ACxCO,IAAM,sBAAsB;;;AC0D7B,SAAU,aACd,KACA,MAA4B;AAE5B,QAAM,WAAW,IAAI,WAAW,IAAI,YAAW;AAE/C,QAAM,yBAAyB,IAAI,KACjC,CAAC,MAAO,EAAuB,SAAS,uBAAuB,IAAI;AAErE,MAAI,kCAAkC,WAAW;AAC/C,WAAO,IAAI,uBAAuB;MAChC,OAAO;MACP,SAAS,uBAAuB;KACjC;;AAEH,MAAI,uBAAuB,YAAY,KAAK,OAAO;AACjD,WAAO,IAAI,uBAAuB;MAChC,OAAO;MACP,SAAS,IAAI;KACd;AACH,MAAI,mBAAmB,YAAY,KAAK,OAAO;AAC7C,WAAO,IAAI,mBAAmB;MAC5B,OAAO;MACP,cAAc,6BAAM;KACrB;AACH,MAAI,kBAAkB,YAAY,KAAK,OAAO;AAC5C,WAAO,IAAI,kBAAkB;MAC3B,OAAO;MACP,cAAc,6BAAM;KACrB;AACH,MAAI,kBAAkB,YAAY,KAAK,OAAO;AAC5C,WAAO,IAAI,kBAAkB,EAAE,OAAO,KAAK,OAAO,6BAAM,MAAK,CAAE;AACjE,MAAI,iBAAiB,YAAY,KAAK,OAAO;AAC3C,WAAO,IAAI,iBAAiB,EAAE,OAAO,KAAK,OAAO,6BAAM,MAAK,CAAE;AAChE,MAAI,mBAAmB,YAAY,KAAK,OAAO;AAC7C,WAAO,IAAI,mBAAmB,EAAE,OAAO,KAAK,OAAO,6BAAM,MAAK,CAAE;AAClE,MAAI,uBAAuB,YAAY,KAAK,OAAO;AACjD,WAAO,IAAI,uBAAuB,EAAE,OAAO,IAAG,CAAE;AAClD,MAAI,yBAAyB,YAAY,KAAK,OAAO;AACnD,WAAO,IAAI,yBAAyB,EAAE,OAAO,KAAK,KAAK,6BAAM,IAAG,CAAE;AACpE,MAAI,wBAAwB,YAAY,KAAK,OAAO;AAClD,WAAO,IAAI,wBAAwB,EAAE,OAAO,KAAK,KAAK,6BAAM,IAAG,CAAE;AACnE,MAAI,iCAAiC,YAAY,KAAK,OAAO;AAC3D,WAAO,IAAI,iCAAiC,EAAE,OAAO,IAAG,CAAE;AAC5D,MAAI,oBAAoB,YAAY,KAAK,OAAO;AAC9C,WAAO,IAAI,oBAAoB;MAC7B,OAAO;MACP,cAAc,6BAAM;MACpB,sBAAsB,6BAAM;KAC7B;AACH,SAAO,IAAI,iBAAiB;IAC1B,OAAO;GACR;AACH;;;ACxFM,SAAU,aACd,KACA,EACE,UAAAC,WACA,GAAG,KAAI,GAIR;AAED,QAAM,SAAS,MAAK;AAClB,UAAMC,SAAQ,aACZ,KACA,IAA8B;AAEhC,QAAIA,kBAAiB;AAAkB,aAAO;AAC9C,WAAOA;EACT,GAAE;AACF,SAAO,IAAI,mBAAmB,OAAO;IACnC,UAAAD;IACA,GAAG;GACJ;AACH;;;ACrCM,SAAU,QACd,QACA,EAAE,OAAM,GAAyC;AAEjD,MAAI,CAAC;AAAQ,WAAO,CAAA;AAEpB,QAAM,QAAiC,CAAA;AACvC,WAAS,SAASE,YAA8B;AAC9C,UAAM,OAAO,OAAO,KAAKA,UAAS;AAClC,eAAW,OAAO,MAAM;AACtB,UAAI,OAAO;AAAQ,cAAM,GAAG,IAAI,OAAO,GAAG;AAC1C,UACEA,WAAU,GAAG,KACb,OAAOA,WAAU,GAAG,MAAM,YAC1B,CAAC,MAAM,QAAQA,WAAU,GAAG,CAAC;AAE7B,iBAASA,WAAU,GAAG,CAAC;;EAE7B;AAEA,QAAM,YAAY,OAAO,UAAU,CAAA,CAAE;AACrC,WAAS,SAAS;AAElB,SAAO;AACT;;;ACUA,IAAM,iBAA+B,oBAAI,IAAG;AAEtC,SAAU,qBAGd,EACA,IACA,IACA,kBACA,OAAO,GACP,KAAI,GAIL;AACC,QAAM,OAAO,YAAW;AACtB,UAAM,YAAY,aAAY;AAC9B,UAAK;AAEL,UAAM,OAAO,UAAU,IAAI,CAAC,EAAE,MAAAC,MAAI,MAAOA,KAAI;AAE7C,QAAI,KAAK,WAAW;AAAG;AAEvB,OAAG,IAAqB,EACrB,KAAK,CAAC,SAAQ;AAxBrB;AAyBQ,UAAI,QAAQ,MAAM,QAAQ,IAAI;AAAG,aAAK,KAAK,IAAI;AAC/C,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,EAAE,eAAc,IAAK,UAAU,CAAC;AACtC,6BAAe,YAAf,wCAAyB,CAAC,KAAK,CAAC,GAAG,IAAI;;IAE3C,CAAC,EACA,MAAM,CAAC,QAAO;AA/BrB;AAgCQ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,EAAE,eAAc,IAAK,UAAU,CAAC;AACtC,6BAAe,WAAf,wCAAwB;;IAE5B,CAAC;EACL;AAEA,QAAM,QAAQ,MAAM,eAAe,OAAO,EAAE;AAE5C,QAAM,iBAAiB,MACrB,aAAY,EAAG,IAAI,CAAC,EAAE,KAAI,MAAO,IAAI;AAEvC,QAAM,eAAe,MAAM,eAAe,IAAI,EAAE,KAAK,CAAA;AAErD,QAAM,eAAe,CAAC,SACpB,eAAe,IAAI,IAAI,CAAC,GAAG,aAAY,GAAI,IAAI,CAAC;AAElD,SAAO;IACL;IACA,MAAM,SAAS,MAAiB;AAC9B,YAAM,iBAA8C,CAAA;AACpD,YAAM,UAAU,IAAI,QAA+B,CAAC,SAAS,WAAU;AACrE,uBAAe,UAAU;AACzB,uBAAe,SAAS;MAC1B,CAAC;AAED,YAAMC,SAAQ,qDAAmB,CAAC,GAAG,eAAc,GAAI,IAAI;AAE3D,UAAIA;AAAO,aAAI;AAEf,YAAM,qBAAqB,aAAY,EAAG,SAAS;AACnD,UAAI,oBAAoB;AACtB,qBAAa,EAAE,MAAM,eAAc,CAAE;AACrC,eAAO;;AAGT,mBAAa,EAAE,MAAM,eAAc,CAAE;AACrC,iBAAW,MAAM,IAAI;AACrB,aAAO;IACT;;AAEJ;;;ACUA,eAAsB,KACpB,QACA,MAA4B;AA5H9B;AA8HE,QAAM,EACJ,SAAS,WAAW,OAAO,SAC3B,QAAQ,SAAQ,YAAO,UAAP,mBAAc,SAAS,GACvC,aACA,WAAW,UACX,YACA,MACA,KACA,UACA,cACA,sBACA,OACA,IACA,OACA,GAAG,KAAI,IACL;AACJ,QAAM,UAAU,WAAW,aAAa,QAAQ,IAAI;AAEpD,MAAI;AACF,kBAAc,IAA+B;AAE7C,UAAM,iBAAiB,cAAc,YAAY,WAAW,IAAI;AAChE,UAAM,QAAQ,kBAAkB;AAEhC,UAAM,eAAc,wBAAO,UAAP,mBAAc,eAAd,mBAA0B,uBAA1B,mBAA8C;AAClE,UAAM,SAAS,eAAe;AAE9B,UAAM,UAAU,OAAO;;MAErB,GAAG,QAAQ,MAAM,EAAE,QAAQ,YAAW,CAAE;MACxC,MAAM,mCAAS;MACf;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACqB;AAEvB,QAAI,SAAS,uBAAuB,EAAE,QAAO,CAAE,GAAG;AAChD,UAAI;AACF,eAAO,MAAM,kBAAkB,QAAQ;UACrC,GAAG;UACH;UACA;SACiD;eAC5C,KAAK;AACZ,YACE,EAAE,eAAe,kCACjB,EAAE,eAAe;AAEjB,gBAAM;;;AAIZ,UAAM,WAAW,MAAM,OAAO,QAAQ;MACpC,QAAQ;MACR,QAAQ,QACJ,CAAC,SAA2C,KAAK,IACjD,CAAC,OAAyC;KAC/C;AACD,QAAI,aAAa;AAAM,aAAO,EAAE,MAAM,OAAS;AAC/C,WAAO,EAAE,MAAM,SAAQ;WAChB,KAAK;AACZ,UAAMC,QAAO,mBAAmB,GAAG;AACnC,UAAM,EAAE,gBAAgB,wBAAuB,IAAK,MAAM,OACxD,oBAAqB;AAEvB,SAAIA,SAAA,gBAAAA,MAAM,MAAM,GAAG,SAAQ,2BAA2B,IAAI;AACxD,aAAO,EAAE,MAAM,MAAM,eAAe,QAAQ,EAAE,MAAAA,OAAM,GAAE,CAAE,EAAC;;AAE3D,UAAM,aAAa,KAAkB;MACnC,GAAG;MACH;MACA,OAAO,OAAO;KACf;;AAEL;AAOA,SAAS,uBAAuB,EAAE,QAAO,GAAmC;AAC1E,QAAM,EAAE,MAAM,IAAI,GAAG,SAAQ,IAAK;AAClC,MAAI,CAAC;AAAM,WAAO;AAClB,MAAI,KAAK,WAAW,mBAAmB;AAAG,WAAO;AACjD,MAAI,CAAC;AAAI,WAAO;AAChB,MACE,OAAO,OAAO,QAAQ,EAAE,OAAO,CAAC,MAAM,OAAO,MAAM,WAAW,EAAE,SAAS;AAEzE,WAAO;AACT,SAAO;AACT;AAoBA,eAAe,kBACb,QACA,MAAyC;AArP3C;AAuPE,QAAM,EAAE,YAAY,MAAM,OAAO,EAAC,IAChC,SAAO,YAAO,UAAP,mBAAc,eAAc,WAAW,OAAO,MAAM,YAAY,CAAA;AACzE,QAAM,EACJ,aACA,WAAW,UACX,MACA,kBAAkB,mBAClB,GAAE,IACA;AAEJ,MAAI,mBAAmB;AACvB,MAAI,CAAC,kBAAkB;AACrB,QAAI,CAAC,OAAO;AAAO,YAAM,IAAI,8BAA6B;AAE1D,uBAAmB,wBAAwB;MACzC;MACA,OAAO,OAAO;MACd,UAAU;KACX;;AAGH,QAAM,iBAAiB,cAAc,YAAY,WAAW,IAAI;AAChE,QAAM,QAAQ,kBAAkB;AAEhC,QAAM,EAAE,SAAQ,IAAK,qBAAqB;IACxC,IAAI,GAAG,OAAO,GAAG,IAAI,KAAK;IAC1B;IACA,iBAAiBC,OAAI;AACnB,YAAMC,QAAOD,MAAK,OAAO,CAACC,OAAM,EAAE,MAAAF,MAAI,MAAOE,SAAQF,MAAK,SAAS,IAAI,CAAC;AACxE,aAAOE,QAAO,YAAY;IAC5B;IACA,IAAI,OACF,aAIE;AACF,YAAM,QAAQ,SAAS,IAAI,CAAC,aAAa;QACvC,cAAc;QACd,UAAU,QAAQ;QAClB,QAAQ,QAAQ;QAChB;AAEF,YAAM,WAAW,mBAAmB;QAClC,KAAK;QACL,MAAM,CAAC,KAAK;QACZ,cAAc;OACf;AAED,YAAMF,QAAO,MAAM,OAAO,QAAQ;QAChC,QAAQ;QACR,QAAQ;UACN;YACE,MAAM;YACN,IAAI;;UAEN;;OAEH;AAED,aAAO,qBAAqB;QAC1B,KAAK;QACL,MAAM,CAAC,KAAK;QACZ,cAAc;QACd,MAAMA,SAAQ;OACf;IACH;GACD;AAED,QAAM,CAAC,EAAE,YAAY,QAAO,CAAE,IAAI,MAAM,SAAS,EAAE,MAAM,GAAE,CAAE;AAE7D,MAAI,CAAC;AAAS,UAAM,IAAI,iBAAiB,EAAE,MAAM,WAAU,CAAE;AAC7D,MAAI,eAAe;AAAM,WAAO,EAAE,MAAM,OAAS;AACjD,SAAO,EAAE,MAAM,WAAU;AAC3B;AAIM,SAAU,mBAAmB,KAAY;AAC7C,MAAI,EAAE,eAAe;AAAY,WAAO;AACxC,QAAM,QAAQ,IAAI,KAAI;AACtB,SAAO,OAAO,MAAM,SAAS,WAAW,MAAM,KAAK,OAAO,MAAM;AAClE;", "names": ["docs<PERSON><PERSON>", "size", "bytes", "size", "size", "bytes", "toBytes", "bytes", "size", "bytes", "size", "encoder", "version", "BaseError", "docs<PERSON><PERSON>", "version", "BaseError", "BaseError", "BaseError", "BaseError", "BaseError", "parseAbiParameter", "length", "formatAbiItem", "docs<PERSON><PERSON>", "size", "formatAbiItem", "bytes", "bytes", "toBytes", "toBytes", "length", "hash", "toBytes", "formatAbiItem", "hash", "length", "consumed", "value", "size", "formatAbiItem", "value", "docs<PERSON><PERSON>", "hash", "docs<PERSON><PERSON>", "formatAbiItem", "docs<PERSON><PERSON>", "docs<PERSON><PERSON>", "cause", "formatted", "args", "split", "data", "args", "size"]}