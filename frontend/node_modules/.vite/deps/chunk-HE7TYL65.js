import {
  goerli,
  mainnet
} from "./chunk-5EBGCFML.js";
import {
  createPublicClient,
  createWalletClient,
  custom,
  fallback,
  http,
  parseGwei,
  webSocket
} from "./chunk-JDCTZMVO.js";
import {
  __privateAdd,
  __privateGet,
  __privateMethod,
  __privateSet
} from "./chunk-L3LPOETZ.js";
import {
  ContractFunctionExecutionError,
  ResourceUnavailableRpcError,
  SwitchChainError,
  UserRejectedRequestError,
  formatUnits,
  getAddress,
  hexToString,
  isAddress,
  numberToHex,
  trim,
  weiUnits
} from "./chunk-XLLWCG7E.js";
import {
  __commonJS,
  __toESM
} from "./chunk-W7S2ME4R.js";

// node_modules/eventemitter3/index.js
var require_eventemitter3 = __commonJS({
  "node_modules/eventemitter3/index.js"(exports, module) {
    "use strict";
    var has = Object.prototype.hasOwnProperty;
    var prefix = "~";
    function Events() {
    }
    if (Object.create) {
      Events.prototype = /* @__PURE__ */ Object.create(null);
      if (!new Events().__proto__)
        prefix = false;
    }
    function EE(fn, context, once) {
      this.fn = fn;
      this.context = context;
      this.once = once || false;
    }
    function addListener(emitter, event, fn, context, once) {
      if (typeof fn !== "function") {
        throw new TypeError("The listener must be a function");
      }
      var listener = new EE(fn, context || emitter, once), evt = prefix ? prefix + event : event;
      if (!emitter._events[evt])
        emitter._events[evt] = listener, emitter._eventsCount++;
      else if (!emitter._events[evt].fn)
        emitter._events[evt].push(listener);
      else
        emitter._events[evt] = [emitter._events[evt], listener];
      return emitter;
    }
    function clearEvent(emitter, evt) {
      if (--emitter._eventsCount === 0)
        emitter._events = new Events();
      else
        delete emitter._events[evt];
    }
    function EventEmitter2() {
      this._events = new Events();
      this._eventsCount = 0;
    }
    EventEmitter2.prototype.eventNames = function eventNames() {
      var names = [], events, name;
      if (this._eventsCount === 0)
        return names;
      for (name in events = this._events) {
        if (has.call(events, name))
          names.push(prefix ? name.slice(1) : name);
      }
      if (Object.getOwnPropertySymbols) {
        return names.concat(Object.getOwnPropertySymbols(events));
      }
      return names;
    };
    EventEmitter2.prototype.listeners = function listeners(event) {
      var evt = prefix ? prefix + event : event, handlers = this._events[evt];
      if (!handlers)
        return [];
      if (handlers.fn)
        return [handlers.fn];
      for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {
        ee[i] = handlers[i].fn;
      }
      return ee;
    };
    EventEmitter2.prototype.listenerCount = function listenerCount(event) {
      var evt = prefix ? prefix + event : event, listeners = this._events[evt];
      if (!listeners)
        return 0;
      if (listeners.fn)
        return 1;
      return listeners.length;
    };
    EventEmitter2.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt])
        return false;
      var listeners = this._events[evt], len = arguments.length, args, i;
      if (listeners.fn) {
        if (listeners.once)
          this.removeListener(event, listeners.fn, void 0, true);
        switch (len) {
          case 1:
            return listeners.fn.call(listeners.context), true;
          case 2:
            return listeners.fn.call(listeners.context, a1), true;
          case 3:
            return listeners.fn.call(listeners.context, a1, a2), true;
          case 4:
            return listeners.fn.call(listeners.context, a1, a2, a3), true;
          case 5:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;
          case 6:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;
        }
        for (i = 1, args = new Array(len - 1); i < len; i++) {
          args[i - 1] = arguments[i];
        }
        listeners.fn.apply(listeners.context, args);
      } else {
        var length = listeners.length, j;
        for (i = 0; i < length; i++) {
          if (listeners[i].once)
            this.removeListener(event, listeners[i].fn, void 0, true);
          switch (len) {
            case 1:
              listeners[i].fn.call(listeners[i].context);
              break;
            case 2:
              listeners[i].fn.call(listeners[i].context, a1);
              break;
            case 3:
              listeners[i].fn.call(listeners[i].context, a1, a2);
              break;
            case 4:
              listeners[i].fn.call(listeners[i].context, a1, a2, a3);
              break;
            default:
              if (!args)
                for (j = 1, args = new Array(len - 1); j < len; j++) {
                  args[j - 1] = arguments[j];
                }
              listeners[i].fn.apply(listeners[i].context, args);
          }
        }
      }
      return true;
    };
    EventEmitter2.prototype.on = function on(event, fn, context) {
      return addListener(this, event, fn, context, false);
    };
    EventEmitter2.prototype.once = function once(event, fn, context) {
      return addListener(this, event, fn, context, true);
    };
    EventEmitter2.prototype.removeListener = function removeListener(event, fn, context, once) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt])
        return this;
      if (!fn) {
        clearEvent(this, evt);
        return this;
      }
      var listeners = this._events[evt];
      if (listeners.fn) {
        if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {
          clearEvent(this, evt);
        }
      } else {
        for (var i = 0, events = [], length = listeners.length; i < length; i++) {
          if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {
            events.push(listeners[i]);
          }
        }
        if (events.length)
          this._events[evt] = events.length === 1 ? events[0] : events;
        else
          clearEvent(this, evt);
      }
      return this;
    };
    EventEmitter2.prototype.removeAllListeners = function removeAllListeners(event) {
      var evt;
      if (event) {
        evt = prefix ? prefix + event : event;
        if (this._events[evt])
          clearEvent(this, evt);
      } else {
        this._events = new Events();
        this._eventsCount = 0;
      }
      return this;
    };
    EventEmitter2.prototype.off = EventEmitter2.prototype.removeListener;
    EventEmitter2.prototype.addListener = EventEmitter2.prototype.on;
    EventEmitter2.prefixed = prefix;
    EventEmitter2.EventEmitter = EventEmitter2;
    if ("undefined" !== typeof module) {
      module.exports = EventEmitter2;
    }
  }
});

// node_modules/@wagmi/connectors/dist/chunk-UGBGYVBH.js
var ChainNotConfiguredForConnectorError = class extends Error {
  constructor({
    chainId,
    connectorId
  }) {
    super(`Chain "${chainId}" not configured for connector "${connectorId}".`);
    this.name = "ChainNotConfiguredForConnectorError";
  }
};
var ConnectorNotFoundError = class extends Error {
  constructor() {
    super(...arguments);
    this.name = "ConnectorNotFoundError";
    this.message = "Connector not found";
  }
};

// node_modules/@wagmi/connectors/dist/chunk-W65LBPLT.js
var import_eventemitter3 = __toESM(require_eventemitter3(), 1);
var __accessCheck = (obj, member, msg) => {
  if (!member.has(obj))
    throw TypeError("Cannot " + msg);
};
var __privateGet2 = (obj, member, getter) => {
  __accessCheck(obj, member, "read from private field");
  return getter ? getter.call(obj) : member.get(obj);
};
var __privateAdd2 = (obj, member, value) => {
  if (member.has(obj))
    throw TypeError("Cannot add the same private member more than once");
  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
};
var __privateSet2 = (obj, member, value, setter) => {
  __accessCheck(obj, member, "write to private field");
  setter ? setter.call(obj, value) : member.set(obj, value);
  return value;
};
var __privateMethod2 = (obj, member, method) => {
  __accessCheck(obj, member, "access private method");
  return method;
};
var Connector = class extends import_eventemitter3.default {
  constructor({
    chains = [mainnet, goerli],
    options
  }) {
    super();
    this.chains = chains;
    this.options = options;
  }
  getBlockExplorerUrls(chain) {
    const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};
    if (blockExplorer)
      return [
        blockExplorer.url,
        ...Object.values(blockExplorers).map((x) => x.url)
      ];
  }
  isChainUnsupported(chainId) {
    return !this.chains.some((x) => x.id === chainId);
  }
  setStorage(storage) {
    this.storage = storage;
  }
};

// node_modules/@wagmi/connectors/dist/chunk-OQILYQDO.js
function normalizeChainId(chainId) {
  if (typeof chainId === "string")
    return Number.parseInt(
      chainId,
      chainId.trim().substring(0, 2) === "0x" ? 16 : 10
    );
  if (typeof chainId === "bigint")
    return Number(chainId);
  return chainId;
}

// node_modules/@wagmi/connectors/dist/chunk-P4NRLOY7.js
function getInjectedName(ethereum) {
  var _a;
  if (!ethereum)
    return "Injected";
  const getName = (provider) => {
    if (provider.isApexWallet)
      return "Apex Wallet";
    if (provider.isAvalanche)
      return "Core Wallet";
    if (provider.isBackpack)
      return "Backpack";
    if (provider.isBifrost)
      return "Bifrost Wallet";
    if (provider.isBitKeep)
      return "BitKeep";
    if (provider.isBitski)
      return "Bitski";
    if (provider.isBlockWallet)
      return "BlockWallet";
    if (provider.isBraveWallet)
      return "Brave Wallet";
    if (provider.isCoin98)
      return "Coin98 Wallet";
    if (provider.isCoinbaseWallet)
      return "Coinbase Wallet";
    if (provider.isDawn)
      return "Dawn Wallet";
    if (provider.isDefiant)
      return "Defiant";
    if (provider.isDesig)
      return "Desig Wallet";
    if (provider.isEnkrypt)
      return "Enkrypt";
    if (provider.isExodus)
      return "Exodus";
    if (provider.isFordefi)
      return "Fordefi";
    if (provider.isFrame)
      return "Frame";
    if (provider.isFrontier)
      return "Frontier Wallet";
    if (provider.isGamestop)
      return "GameStop Wallet";
    if (provider.isHaqqWallet)
      return "HAQQ Wallet";
    if (provider.isHyperPay)
      return "HyperPay Wallet";
    if (provider.isImToken)
      return "ImToken";
    if (provider.isHaloWallet)
      return "Halo Wallet";
    if (provider.isKuCoinWallet)
      return "KuCoin Wallet";
    if (provider.isMathWallet)
      return "MathWallet";
    if (provider.isNovaWallet)
      return "Nova Wallet";
    if (provider.isOkxWallet || provider.isOKExWallet)
      return "OKX Wallet";
    if (provider.isOktoWallet)
      return "Okto Wallet";
    if (provider.isOneInchIOSWallet || provider.isOneInchAndroidWallet)
      return "1inch Wallet";
    if (provider.isOneKey)
      return "OneKey Wallet";
    if (provider.isOpera)
      return "Opera";
    if (provider.isPhantom)
      return "Phantom";
    if (provider.isPortal)
      return "Ripio Portal";
    if (provider.isRabby)
      return "Rabby Wallet";
    if (provider.isRainbow)
      return "Rainbow";
    if (provider.isSafePal)
      return "SafePal Wallet";
    if (provider.isStatus)
      return "Status";
    if (provider.isSubWallet)
      return "SubWallet";
    if (provider.isTalisman)
      return "Talisman";
    if (provider.isTally)
      return "Taho";
    if (provider.isTokenPocket)
      return "TokenPocket";
    if (provider.isTokenary)
      return "Tokenary";
    if (provider.isTrust || provider.isTrustWallet)
      return "Trust Wallet";
    if (provider.isTTWallet)
      return "TTWallet";
    if (provider.isXDEFI)
      return "XDEFI Wallet";
    if (provider.isZeal)
      return "Zeal";
    if (provider.isZerion)
      return "Zerion";
    if (provider.isMetaMask)
      return "MetaMask";
  };
  if ((_a = ethereum.providers) == null ? void 0 : _a.length) {
    const nameSet = /* @__PURE__ */ new Set();
    let unknownCount = 1;
    for (const provider of ethereum.providers) {
      let name = getName(provider);
      if (!name) {
        name = `Unknown Wallet #${unknownCount}`;
        unknownCount += 1;
      }
      nameSet.add(name);
    }
    const names = [...nameSet];
    if (names.length)
      return names;
    return names[0] ?? "Injected";
  }
  return getName(ethereum) ?? "Injected";
}
var _provider;
var InjectedConnector = class extends Connector {
  constructor({
    chains,
    options: options_
  } = {}) {
    const options = {
      shimDisconnect: true,
      getProvider() {
        if (typeof window === "undefined")
          return;
        const ethereum = window.ethereum;
        if ((ethereum == null ? void 0 : ethereum.providers) && ethereum.providers.length > 0)
          return ethereum.providers[0];
        return ethereum;
      },
      ...options_
    };
    super({ chains, options });
    this.id = "injected";
    __privateAdd2(this, _provider, void 0);
    this.shimDisconnectKey = `${this.id}.shimDisconnect`;
    this.onAccountsChanged = (accounts) => {
      if (accounts.length === 0)
        this.emit("disconnect");
      else
        this.emit("change", {
          account: getAddress(accounts[0])
        });
    };
    this.onChainChanged = (chainId) => {
      const id = normalizeChainId(chainId);
      const unsupported = this.isChainUnsupported(id);
      this.emit("change", { chain: { id, unsupported } });
    };
    this.onDisconnect = async (error) => {
      var _a;
      if (error.code === 1013) {
        const provider2 = await this.getProvider();
        if (provider2) {
          const isAuthorized = await this.getAccount();
          if (isAuthorized)
            return;
        }
      }
      this.emit("disconnect");
      if (this.options.shimDisconnect)
        (_a = this.storage) == null ? void 0 : _a.removeItem(this.shimDisconnectKey);
    };
    const provider = options.getProvider();
    if (typeof options.name === "string")
      this.name = options.name;
    else if (provider) {
      const detectedName = getInjectedName(provider);
      if (options.name)
        this.name = options.name(detectedName);
      else {
        if (typeof detectedName === "string")
          this.name = detectedName;
        else
          this.name = detectedName[0];
      }
    } else
      this.name = "Injected";
    this.ready = !!provider;
  }
  async connect({ chainId } = {}) {
    var _a;
    try {
      const provider = await this.getProvider();
      if (!provider)
        throw new ConnectorNotFoundError();
      if (provider.on) {
        provider.on("accountsChanged", this.onAccountsChanged);
        provider.on("chainChanged", this.onChainChanged);
        provider.on("disconnect", this.onDisconnect);
      }
      this.emit("message", { type: "connecting" });
      const accounts = await provider.request({
        method: "eth_requestAccounts"
      });
      const account = getAddress(accounts[0]);
      let id = await this.getChainId();
      let unsupported = this.isChainUnsupported(id);
      if (chainId && id !== chainId) {
        const chain = await this.switchChain(chainId);
        id = chain.id;
        unsupported = this.isChainUnsupported(id);
      }
      if (this.options.shimDisconnect)
        (_a = this.storage) == null ? void 0 : _a.setItem(this.shimDisconnectKey, true);
      return { account, chain: { id, unsupported } };
    } catch (error) {
      if (this.isUserRejectedRequestError(error))
        throw new UserRejectedRequestError(error);
      if (error.code === -32002)
        throw new ResourceUnavailableRpcError(error);
      throw error;
    }
  }
  async disconnect() {
    var _a;
    const provider = await this.getProvider();
    if (!(provider == null ? void 0 : provider.removeListener))
      return;
    provider.removeListener("accountsChanged", this.onAccountsChanged);
    provider.removeListener("chainChanged", this.onChainChanged);
    provider.removeListener("disconnect", this.onDisconnect);
    if (this.options.shimDisconnect)
      (_a = this.storage) == null ? void 0 : _a.removeItem(this.shimDisconnectKey);
  }
  async getAccount() {
    const provider = await this.getProvider();
    if (!provider)
      throw new ConnectorNotFoundError();
    const accounts = await provider.request({
      method: "eth_accounts"
    });
    return getAddress(accounts[0]);
  }
  async getChainId() {
    const provider = await this.getProvider();
    if (!provider)
      throw new ConnectorNotFoundError();
    return provider.request({ method: "eth_chainId" }).then(normalizeChainId);
  }
  async getProvider() {
    const provider = this.options.getProvider();
    if (provider)
      __privateSet2(this, _provider, provider);
    return __privateGet2(this, _provider);
  }
  async getWalletClient({
    chainId
  } = {}) {
    const [provider, account] = await Promise.all([
      this.getProvider(),
      this.getAccount()
    ]);
    const chain = this.chains.find((x) => x.id === chainId);
    if (!provider)
      throw new Error("provider is required.");
    return createWalletClient({
      account,
      chain,
      transport: custom(provider)
    });
  }
  async isAuthorized() {
    var _a;
    try {
      if (this.options.shimDisconnect && !((_a = this.storage) == null ? void 0 : _a.getItem(this.shimDisconnectKey)))
        return false;
      const provider = await this.getProvider();
      if (!provider)
        throw new ConnectorNotFoundError();
      const account = await this.getAccount();
      return !!account;
    } catch {
      return false;
    }
  }
  async switchChain(chainId) {
    var _a, _b, _c;
    const provider = await this.getProvider();
    if (!provider)
      throw new ConnectorNotFoundError();
    const id = numberToHex(chainId);
    try {
      await Promise.all([
        provider.request({
          method: "wallet_switchEthereumChain",
          params: [{ chainId: id }]
        }),
        new Promise(
          (res) => this.on("change", ({ chain }) => {
            if ((chain == null ? void 0 : chain.id) === chainId)
              res();
          })
        )
      ]);
      return this.chains.find((x) => x.id === chainId) ?? {
        id: chainId,
        name: `Chain ${id}`,
        network: `${id}`,
        nativeCurrency: { name: "Ether", decimals: 18, symbol: "ETH" },
        rpcUrls: { default: { http: [""] }, public: { http: [""] } }
      };
    } catch (error) {
      const chain = this.chains.find((x) => x.id === chainId);
      if (!chain)
        throw new ChainNotConfiguredForConnectorError({
          chainId,
          connectorId: this.id
        });
      if (error.code === 4902 || ((_b = (_a = error == null ? void 0 : error.data) == null ? void 0 : _a.originalError) == null ? void 0 : _b.code) === 4902) {
        try {
          await provider.request({
            method: "wallet_addEthereumChain",
            params: [
              {
                chainId: id,
                chainName: chain.name,
                nativeCurrency: chain.nativeCurrency,
                rpcUrls: [((_c = chain.rpcUrls.public) == null ? void 0 : _c.http[0]) ?? ""],
                blockExplorerUrls: this.getBlockExplorerUrls(chain)
              }
            ]
          });
          const currentChainId = await this.getChainId();
          if (currentChainId !== chainId)
            throw new UserRejectedRequestError(
              new Error("User rejected switch after adding network.")
            );
          return chain;
        } catch (error2) {
          throw new UserRejectedRequestError(error2);
        }
      }
      if (this.isUserRejectedRequestError(error))
        throw new UserRejectedRequestError(error);
      throw new SwitchChainError(error);
    }
  }
  async watchAsset({
    address,
    decimals = 18,
    image,
    symbol
  }) {
    const provider = await this.getProvider();
    if (!provider)
      throw new ConnectorNotFoundError();
    return provider.request({
      method: "wallet_watchAsset",
      params: {
        type: "ERC20",
        options: {
          address,
          decimals,
          image,
          symbol
        }
      }
    });
  }
  isUserRejectedRequestError(error) {
    return error.code === 4001;
  }
};
_provider = /* @__PURE__ */ new WeakMap();

// node_modules/zustand/esm/middleware.mjs
var subscribeWithSelectorImpl = (fn) => (set, get, api) => {
  const origSubscribe = api.subscribe;
  api.subscribe = (selector, optListener, options) => {
    let listener = selector;
    if (optListener) {
      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;
      let currentSlice = selector(api.getState());
      listener = (state) => {
        const nextSlice = selector(state);
        if (!equalityFn(currentSlice, nextSlice)) {
          const previousSlice = currentSlice;
          optListener(currentSlice = nextSlice, previousSlice);
        }
      };
      if (options == null ? void 0 : options.fireImmediately) {
        optListener(currentSlice, currentSlice);
      }
    }
    return origSubscribe(listener);
  };
  const initialState = fn(set, get, api);
  return initialState;
};
var subscribeWithSelector = subscribeWithSelectorImpl;
function createJSONStorage(getStorage, options) {
  let storage;
  try {
    storage = getStorage();
  } catch (_e) {
    return;
  }
  const persistStorage = {
    getItem: (name) => {
      var _a;
      const parse = (str2) => {
        if (str2 === null) {
          return null;
        }
        return JSON.parse(str2, options == null ? void 0 : options.reviver);
      };
      const str = (_a = storage.getItem(name)) != null ? _a : null;
      if (str instanceof Promise) {
        return str.then(parse);
      }
      return parse(str);
    },
    setItem: (name, newValue) => storage.setItem(
      name,
      JSON.stringify(newValue, options == null ? void 0 : options.replacer)
    ),
    removeItem: (name) => storage.removeItem(name)
  };
  return persistStorage;
}
var toThenable = (fn) => (input) => {
  try {
    const result = fn(input);
    if (result instanceof Promise) {
      return result;
    }
    return {
      then(onFulfilled) {
        return toThenable(onFulfilled)(result);
      },
      catch(_onRejected) {
        return this;
      }
    };
  } catch (e) {
    return {
      then(_onFulfilled) {
        return this;
      },
      catch(onRejected) {
        return toThenable(onRejected)(e);
      }
    };
  }
};
var oldImpl = (config2, baseOptions) => (set, get, api) => {
  let options = {
    getStorage: () => localStorage,
    serialize: JSON.stringify,
    deserialize: JSON.parse,
    partialize: (state) => state,
    version: 0,
    merge: (persistedState, currentState) => ({
      ...currentState,
      ...persistedState
    }),
    ...baseOptions
  };
  let hasHydrated = false;
  const hydrationListeners = /* @__PURE__ */ new Set();
  const finishHydrationListeners = /* @__PURE__ */ new Set();
  let storage;
  try {
    storage = options.getStorage();
  } catch (_e) {
  }
  if (!storage) {
    return config2(
      (...args) => {
        console.warn(
          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`
        );
        set(...args);
      },
      get,
      api
    );
  }
  const thenableSerialize = toThenable(options.serialize);
  const setItem = () => {
    const state = options.partialize({ ...get() });
    let errorInSync;
    const thenable = thenableSerialize({ state, version: options.version }).then(
      (serializedValue) => storage.setItem(options.name, serializedValue)
    ).catch((e) => {
      errorInSync = e;
    });
    if (errorInSync) {
      throw errorInSync;
    }
    return thenable;
  };
  const savedSetState = api.setState;
  api.setState = (state, replace) => {
    savedSetState(state, replace);
    void setItem();
  };
  const configResult = config2(
    (...args) => {
      set(...args);
      void setItem();
    },
    get,
    api
  );
  let stateFromStorage;
  const hydrate = () => {
    var _a;
    if (!storage)
      return;
    hasHydrated = false;
    hydrationListeners.forEach((cb) => cb(get()));
    const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;
    return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue) => {
      if (storageValue) {
        return options.deserialize(storageValue);
      }
    }).then((deserializedStorageValue) => {
      if (deserializedStorageValue) {
        if (typeof deserializedStorageValue.version === "number" && deserializedStorageValue.version !== options.version) {
          if (options.migrate) {
            return options.migrate(
              deserializedStorageValue.state,
              deserializedStorageValue.version
            );
          }
          console.error(
            `State loaded from storage couldn't be migrated since no migrate function was provided`
          );
        } else {
          return deserializedStorageValue.state;
        }
      }
    }).then((migratedState) => {
      var _a2;
      stateFromStorage = options.merge(
        migratedState,
        (_a2 = get()) != null ? _a2 : configResult
      );
      set(stateFromStorage, true);
      return setItem();
    }).then(() => {
      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);
      hasHydrated = true;
      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));
    }).catch((e) => {
      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);
    });
  };
  api.persist = {
    setOptions: (newOptions) => {
      options = {
        ...options,
        ...newOptions
      };
      if (newOptions.getStorage) {
        storage = newOptions.getStorage();
      }
    },
    clearStorage: () => {
      storage == null ? void 0 : storage.removeItem(options.name);
    },
    getOptions: () => options,
    rehydrate: () => hydrate(),
    hasHydrated: () => hasHydrated,
    onHydrate: (cb) => {
      hydrationListeners.add(cb);
      return () => {
        hydrationListeners.delete(cb);
      };
    },
    onFinishHydration: (cb) => {
      finishHydrationListeners.add(cb);
      return () => {
        finishHydrationListeners.delete(cb);
      };
    }
  };
  hydrate();
  return stateFromStorage || configResult;
};
var newImpl = (config2, baseOptions) => (set, get, api) => {
  let options = {
    storage: createJSONStorage(() => localStorage),
    partialize: (state) => state,
    version: 0,
    merge: (persistedState, currentState) => ({
      ...currentState,
      ...persistedState
    }),
    ...baseOptions
  };
  let hasHydrated = false;
  const hydrationListeners = /* @__PURE__ */ new Set();
  const finishHydrationListeners = /* @__PURE__ */ new Set();
  let storage = options.storage;
  if (!storage) {
    return config2(
      (...args) => {
        console.warn(
          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`
        );
        set(...args);
      },
      get,
      api
    );
  }
  const setItem = () => {
    const state = options.partialize({ ...get() });
    return storage.setItem(options.name, {
      state,
      version: options.version
    });
  };
  const savedSetState = api.setState;
  api.setState = (state, replace) => {
    savedSetState(state, replace);
    void setItem();
  };
  const configResult = config2(
    (...args) => {
      set(...args);
      void setItem();
    },
    get,
    api
  );
  api.getInitialState = () => configResult;
  let stateFromStorage;
  const hydrate = () => {
    var _a, _b;
    if (!storage)
      return;
    hasHydrated = false;
    hydrationListeners.forEach((cb) => {
      var _a2;
      return cb((_a2 = get()) != null ? _a2 : configResult);
    });
    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;
    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {
      if (deserializedStorageValue) {
        if (typeof deserializedStorageValue.version === "number" && deserializedStorageValue.version !== options.version) {
          if (options.migrate) {
            return [
              true,
              options.migrate(
                deserializedStorageValue.state,
                deserializedStorageValue.version
              )
            ];
          }
          console.error(
            `State loaded from storage couldn't be migrated since no migrate function was provided`
          );
        } else {
          return [false, deserializedStorageValue.state];
        }
      }
      return [false, void 0];
    }).then((migrationResult) => {
      var _a2;
      const [migrated, migratedState] = migrationResult;
      stateFromStorage = options.merge(
        migratedState,
        (_a2 = get()) != null ? _a2 : configResult
      );
      set(stateFromStorage, true);
      if (migrated) {
        return setItem();
      }
    }).then(() => {
      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);
      stateFromStorage = get();
      hasHydrated = true;
      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));
    }).catch((e) => {
      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);
    });
  };
  api.persist = {
    setOptions: (newOptions) => {
      options = {
        ...options,
        ...newOptions
      };
      if (newOptions.storage) {
        storage = newOptions.storage;
      }
    },
    clearStorage: () => {
      storage == null ? void 0 : storage.removeItem(options.name);
    },
    getOptions: () => options,
    rehydrate: () => hydrate(),
    hasHydrated: () => hasHydrated,
    onHydrate: (cb) => {
      hydrationListeners.add(cb);
      return () => {
        hydrationListeners.delete(cb);
      };
    },
    onFinishHydration: (cb) => {
      finishHydrationListeners.add(cb);
      return () => {
        finishHydrationListeners.delete(cb);
      };
    }
  };
  if (!options.skipHydration) {
    hydrate();
  }
  return stateFromStorage || configResult;
};
var persistImpl = (config2, baseOptions) => {
  if ("getStorage" in baseOptions || "serialize" in baseOptions || "deserialize" in baseOptions) {
    if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production") {
      console.warn(
        "[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."
      );
    }
    return oldImpl(config2, baseOptions);
  }
  return newImpl(config2, baseOptions);
};
var persist = persistImpl;

// node_modules/zustand/esm/vanilla.mjs
var createStoreImpl = (createState) => {
  let state;
  const listeners = /* @__PURE__ */ new Set();
  const setState = (partial, replace) => {
    const nextState = typeof partial === "function" ? partial(state) : partial;
    if (!Object.is(nextState, state)) {
      const previousState = state;
      state = (replace != null ? replace : typeof nextState !== "object" || nextState === null) ? nextState : Object.assign({}, state, nextState);
      listeners.forEach((listener) => listener(state, previousState));
    }
  };
  const getState = () => state;
  const getInitialState = () => initialState;
  const subscribe = (listener) => {
    listeners.add(listener);
    return () => listeners.delete(listener);
  };
  const destroy = () => {
    if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production") {
      console.warn(
        "[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."
      );
    }
    listeners.clear();
  };
  const api = { setState, getState, getInitialState, subscribe, destroy };
  const initialState = state = createState(setState, getState, api);
  return api;
};
var createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;

// node_modules/zustand/esm/shallow.mjs
function shallow$1(objA, objB) {
  if (Object.is(objA, objB)) {
    return true;
  }
  if (typeof objA !== "object" || objA === null || typeof objB !== "object" || objB === null) {
    return false;
  }
  if (objA instanceof Map && objB instanceof Map) {
    if (objA.size !== objB.size)
      return false;
    for (const [key, value] of objA) {
      if (!Object.is(value, objB.get(key))) {
        return false;
      }
    }
    return true;
  }
  if (objA instanceof Set && objB instanceof Set) {
    if (objA.size !== objB.size)
      return false;
    for (const value of objA) {
      if (!objB.has(value)) {
        return false;
      }
    }
    return true;
  }
  const keysA = Object.keys(objA);
  if (keysA.length !== Object.keys(objB).length) {
    return false;
  }
  for (const keyA of keysA) {
    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {
      return false;
    }
  }
  return true;
}

// node_modules/@wagmi/core/dist/chunk-TSH6VVF4.js
function configureChains(defaultChains, providers, {
  batch = { multicall: { wait: 32 } },
  pollingInterval = 4e3,
  rank,
  retryCount,
  retryDelay,
  stallTimeout
} = {}) {
  if (!defaultChains.length)
    throw new Error("must have at least one chain");
  let chains = [];
  const httpUrls = {};
  const wsUrls = {};
  for (const chain of defaultChains) {
    let configExists = false;
    for (const provider of providers) {
      const apiConfig = provider(chain);
      if (!apiConfig)
        continue;
      configExists = true;
      if (!chains.some(({ id }) => id === chain.id)) {
        chains = [...chains, apiConfig.chain];
      }
      httpUrls[chain.id] = [
        ...httpUrls[chain.id] || [],
        ...apiConfig.rpcUrls.http
      ];
      if (apiConfig.rpcUrls.webSocket) {
        wsUrls[chain.id] = [
          ...wsUrls[chain.id] || [],
          ...apiConfig.rpcUrls.webSocket
        ];
      }
    }
    if (!configExists) {
      throw new Error(
        [
          `Could not find valid provider configuration for chain "${chain.name}".
`,
          "You may need to add `jsonRpcProvider` to `configureChains` with the chain's RPC URLs.",
          "Read more: https://wagmi.sh/core/providers/jsonRpc"
        ].join("\n")
      );
    }
  }
  return {
    chains,
    publicClient: ({ chainId }) => {
      const activeChain = chains.find((x) => x.id === chainId) ?? defaultChains[0];
      const chainHttpUrls = httpUrls[activeChain.id];
      if (!chainHttpUrls || !chainHttpUrls[0])
        throw new Error(`No providers configured for chain "${activeChain.id}"`);
      const publicClient = createPublicClient({
        batch,
        chain: activeChain,
        transport: fallback(
          chainHttpUrls.map((url) => http(url, { timeout: stallTimeout })),
          { rank, retryCount, retryDelay }
        ),
        pollingInterval
      });
      return Object.assign(publicClient, {
        chains
      });
    },
    webSocketPublicClient: ({ chainId }) => {
      const activeChain = chains.find((x) => x.id === chainId) ?? defaultChains[0];
      const chainWsUrls = wsUrls[activeChain.id];
      if (!chainWsUrls || !chainWsUrls[0])
        return void 0;
      const publicClient = createPublicClient({
        batch,
        chain: activeChain,
        transport: fallback(
          chainWsUrls.map((url) => webSocket(url, { timeout: stallTimeout })),
          { rank, retryCount, retryDelay }
        ),
        pollingInterval
      });
      return Object.assign(publicClient, {
        chains
      });
    }
  };
}
var ChainMismatchError = class extends Error {
  constructor({
    activeChain,
    targetChain
  }) {
    super(
      `Chain mismatch: Expected "${targetChain}", received "${activeChain}".`
    );
    this.name = "ChainMismatchError";
  }
};
var ChainNotConfiguredError = class extends Error {
  constructor({
    chainId,
    connectorId
  }) {
    super(
      `Chain "${chainId}" not configured${connectorId ? ` for connector "${connectorId}"` : ""}.`
    );
    this.name = "ChainNotConfigured";
  }
};
var ConnectorAlreadyConnectedError = class extends Error {
  constructor() {
    super(...arguments);
    this.name = "ConnectorAlreadyConnectedError";
    this.message = "Connector already connected";
  }
};
var ConfigChainsNotFound = class extends Error {
  constructor() {
    super(...arguments);
    this.name = "ConfigChainsNotFound";
    this.message = "No chains were found on the wagmi config. Some functions that require a chain may not work.";
  }
};
var SwitchChainNotSupportedError = class extends Error {
  constructor({ connector }) {
    super(`"${connector.name}" does not support programmatic chain switching.`);
    this.name = "SwitchChainNotSupportedError";
  }
};
function deepEqual(a, b) {
  if (a === b)
    return true;
  if (a && b && typeof a === "object" && typeof b === "object") {
    if (a.constructor !== b.constructor)
      return false;
    let length;
    let i;
    if (Array.isArray(a) && Array.isArray(b)) {
      length = a.length;
      if (length != b.length)
        return false;
      for (i = length; i-- !== 0; )
        if (!deepEqual(a[i], b[i]))
          return false;
      return true;
    }
    if (a.valueOf !== Object.prototype.valueOf)
      return a.valueOf() === b.valueOf();
    if (a.toString !== Object.prototype.toString)
      return a.toString() === b.toString();
    const keys = Object.keys(a);
    length = keys.length;
    if (length !== Object.keys(b).length)
      return false;
    for (i = length; i-- !== 0; )
      if (!Object.prototype.hasOwnProperty.call(b, keys[i]))
        return false;
    for (i = length; i-- !== 0; ) {
      const key = keys[i];
      if (key && !deepEqual(a[key], b[key]))
        return false;
    }
    return true;
  }
  return a !== a && b !== b;
}
var findAndReplace = (cacheRef, {
  find,
  replace
}) => {
  if (cacheRef && find(cacheRef)) {
    return replace(cacheRef);
  }
  if (typeof cacheRef !== "object") {
    return cacheRef;
  }
  if (Array.isArray(cacheRef)) {
    return cacheRef.map((item) => findAndReplace(item, { find, replace }));
  }
  if (cacheRef instanceof Object) {
    return Object.entries(cacheRef).reduce(
      (curr, [key, value]) => ({
        ...curr,
        [key]: findAndReplace(value, { find, replace })
      }),
      {}
    );
  }
  return cacheRef;
};
function deserialize(cachedString) {
  const cache = JSON.parse(cachedString);
  const deserializedCacheWithBigInts = findAndReplace(cache, {
    find: (data) => typeof data === "string" && data.startsWith("#bigint."),
    replace: (data) => BigInt(data.replace("#bigint.", ""))
  });
  return deserializedCacheWithBigInts;
}
function getCallParameters(args) {
  return {
    accessList: args.accessList,
    account: args.account,
    blockNumber: args.blockNumber,
    blockTag: args.blockTag,
    data: args.data,
    gas: args.gas,
    gasPrice: args.gasPrice,
    maxFeePerGas: args.maxFeePerGas,
    maxPriorityFeePerGas: args.maxPriorityFeePerGas,
    nonce: args.nonce,
    to: args.to,
    value: args.value
  };
}
function getSendTransactionParameters(args) {
  return {
    accessList: args.accessList,
    account: args.account,
    data: args.data,
    gas: args.gas,
    gasPrice: args.gasPrice,
    maxFeePerGas: args.maxFeePerGas,
    maxPriorityFeePerGas: args.maxPriorityFeePerGas,
    nonce: args.nonce,
    to: args.to,
    value: args.value
  };
}
function getUnit(unit) {
  if (typeof unit === "number")
    return unit;
  if (unit === "wei")
    return 0;
  return Math.abs(weiUnits[unit]);
}
function getReferenceKey(keys, cutoff) {
  return keys.slice(0, cutoff).join(".") || ".";
}
function getCutoff(array, value) {
  const { length } = array;
  for (let index = 0; index < length; ++index) {
    if (array[index] === value) {
      return index + 1;
    }
  }
  return 0;
}
function createReplacer(replacer, circularReplacer) {
  const hasReplacer = typeof replacer === "function";
  const hasCircularReplacer = typeof circularReplacer === "function";
  const cache = [];
  const keys = [];
  return function replace(key, value) {
    if (typeof value === "object") {
      if (cache.length) {
        const thisCutoff = getCutoff(cache, this);
        if (thisCutoff === 0) {
          cache[cache.length] = this;
        } else {
          cache.splice(thisCutoff);
          keys.splice(thisCutoff);
        }
        keys[keys.length] = key;
        const valueCutoff = getCutoff(cache, value);
        if (valueCutoff !== 0) {
          return hasCircularReplacer ? circularReplacer.call(
            this,
            key,
            value,
            getReferenceKey(keys, valueCutoff)
          ) : `[ref=${getReferenceKey(keys, valueCutoff)}]`;
        }
      } else {
        cache[0] = value;
        keys[0] = key;
      }
    }
    return hasReplacer ? replacer.call(this, key, value) : value;
  };
}
function serialize(value, replacer, indent, circularReplacer) {
  return JSON.stringify(
    value,
    createReplacer((key, value_) => {
      const value2 = typeof value_ === "bigint" ? `#bigint.${value_.toString()}` : value_;
      return (replacer == null ? void 0 : replacer(key, value2)) || value2;
    }, circularReplacer),
    indent ?? void 0
  );
}
var noopStorage = {
  getItem: (_key) => "",
  setItem: (_key, _value) => null,
  removeItem: (_key) => null
};
function createStorage({
  deserialize: deserialize2 = deserialize,
  key: prefix = "wagmi",
  serialize: serialize2 = serialize,
  storage
}) {
  return {
    ...storage,
    getItem: (key, defaultState = null) => {
      const value = storage.getItem(`${prefix}.${key}`);
      try {
        return value ? deserialize2(value) : defaultState;
      } catch (error) {
        console.warn(error);
        return defaultState;
      }
    },
    setItem: (key, value) => {
      if (value === null) {
        storage.removeItem(`${prefix}.${key}`);
      } else {
        try {
          storage.setItem(`${prefix}.${key}`, serialize2(value));
        } catch (err) {
          console.error(err);
        }
      }
    },
    removeItem: (key) => storage.removeItem(`${prefix}.${key}`)
  };
}
var storeKey = "store";
var _isAutoConnecting;
var _lastUsedConnector;
var _addEffects;
var addEffects_fn;
var Config = class {
  constructor({
    autoConnect = false,
    connectors = [new InjectedConnector()],
    publicClient,
    storage = createStorage({
      storage: typeof window !== "undefined" ? window.localStorage : noopStorage
    }),
    logger = {
      warn: console.warn
    },
    webSocketPublicClient
  }) {
    var _a, _b;
    __privateAdd(this, _addEffects);
    this.publicClients = /* @__PURE__ */ new Map();
    this.webSocketPublicClients = /* @__PURE__ */ new Map();
    __privateAdd(this, _isAutoConnecting, void 0);
    __privateAdd(this, _lastUsedConnector, void 0);
    this.args = {
      autoConnect,
      connectors,
      logger,
      publicClient,
      storage,
      webSocketPublicClient
    };
    let status = "disconnected";
    let chainId;
    if (autoConnect) {
      try {
        const rawState = storage.getItem(storeKey);
        const data = (_a = rawState == null ? void 0 : rawState.state) == null ? void 0 : _a.data;
        status = (data == null ? void 0 : data.account) ? "reconnecting" : "connecting";
        chainId = (_b = data == null ? void 0 : data.chain) == null ? void 0 : _b.id;
      } catch (_error) {
      }
    }
    const connectors_ = typeof connectors === "function" ? connectors() : connectors;
    connectors_.forEach((connector) => connector.setStorage(storage));
    this.store = createStore(
      subscribeWithSelector(
        persist(
          () => ({
            connectors: connectors_,
            publicClient: this.getPublicClient({ chainId }),
            status,
            webSocketPublicClient: this.getWebSocketPublicClient({ chainId })
          }),
          {
            name: storeKey,
            storage,
            partialize: (state) => {
              var _a2, _b2;
              return {
                ...autoConnect && {
                  data: {
                    account: (_a2 = state == null ? void 0 : state.data) == null ? void 0 : _a2.account,
                    chain: (_b2 = state == null ? void 0 : state.data) == null ? void 0 : _b2.chain
                  }
                },
                chains: state == null ? void 0 : state.chains
              };
            },
            version: 2
          }
        )
      )
    );
    this.storage = storage;
    __privateSet(this, _lastUsedConnector, storage == null ? void 0 : storage.getItem("wallet"));
    __privateMethod(this, _addEffects, addEffects_fn).call(this);
    if (autoConnect && typeof window !== "undefined")
      setTimeout(async () => await this.autoConnect(), 0);
  }
  get chains() {
    return this.store.getState().chains;
  }
  get connectors() {
    return this.store.getState().connectors;
  }
  get connector() {
    return this.store.getState().connector;
  }
  get data() {
    return this.store.getState().data;
  }
  get error() {
    return this.store.getState().error;
  }
  get lastUsedChainId() {
    var _a, _b;
    return (_b = (_a = this.data) == null ? void 0 : _a.chain) == null ? void 0 : _b.id;
  }
  get publicClient() {
    return this.store.getState().publicClient;
  }
  get status() {
    return this.store.getState().status;
  }
  get subscribe() {
    return this.store.subscribe;
  }
  get webSocketPublicClient() {
    return this.store.getState().webSocketPublicClient;
  }
  setState(updater) {
    const newState = typeof updater === "function" ? updater(this.store.getState()) : updater;
    this.store.setState(newState, true);
  }
  clearState() {
    this.setState((x) => ({
      ...x,
      chains: void 0,
      connector: void 0,
      data: void 0,
      error: void 0,
      status: "disconnected"
    }));
  }
  async destroy() {
    var _a, _b;
    if (this.connector)
      await ((_b = (_a = this.connector).disconnect) == null ? void 0 : _b.call(_a));
    __privateSet(this, _isAutoConnecting, false);
    this.clearState();
    this.store.destroy();
  }
  async autoConnect() {
    if (__privateGet(this, _isAutoConnecting))
      return;
    __privateSet(this, _isAutoConnecting, true);
    this.setState((x) => {
      var _a;
      return {
        ...x,
        status: ((_a = x.data) == null ? void 0 : _a.account) ? "reconnecting" : "connecting"
      };
    });
    const sorted = __privateGet(this, _lastUsedConnector) ? [...this.connectors].sort(
      (x) => x.id === __privateGet(this, _lastUsedConnector) ? -1 : 1
    ) : this.connectors;
    let connected = false;
    for (const connector of sorted) {
      if (!connector.ready || !connector.isAuthorized)
        continue;
      const isAuthorized = await connector.isAuthorized();
      if (!isAuthorized)
        continue;
      const data = await connector.connect();
      this.setState((x) => ({
        ...x,
        connector,
        chains: connector == null ? void 0 : connector.chains,
        data,
        status: "connected"
      }));
      connected = true;
      break;
    }
    if (!connected)
      this.setState((x) => ({
        ...x,
        data: void 0,
        status: "disconnected"
      }));
    __privateSet(this, _isAutoConnecting, false);
    return this.data;
  }
  setConnectors(connectors) {
    this.args = {
      ...this.args,
      connectors
    };
    const connectors_ = typeof connectors === "function" ? connectors() : connectors;
    connectors_.forEach((connector) => connector.setStorage(this.args.storage));
    this.setState((x) => ({
      ...x,
      connectors: connectors_
    }));
  }
  getPublicClient({ chainId } = {}) {
    let publicClient_ = this.publicClients.get(-1);
    if (publicClient_ && (publicClient_ == null ? void 0 : publicClient_.chain.id) === chainId)
      return publicClient_;
    publicClient_ = this.publicClients.get(chainId ?? -1);
    if (publicClient_)
      return publicClient_;
    const { publicClient } = this.args;
    publicClient_ = typeof publicClient === "function" ? publicClient({ chainId }) : publicClient;
    this.publicClients.set(chainId ?? -1, publicClient_);
    return publicClient_;
  }
  setPublicClient(publicClient) {
    var _a, _b;
    const chainId = (_b = (_a = this.data) == null ? void 0 : _a.chain) == null ? void 0 : _b.id;
    this.args = {
      ...this.args,
      publicClient
    };
    this.publicClients.clear();
    this.setState((x) => ({
      ...x,
      publicClient: this.getPublicClient({ chainId })
    }));
  }
  getWebSocketPublicClient({ chainId } = {}) {
    let webSocketPublicClient_ = this.webSocketPublicClients.get(-1);
    if (webSocketPublicClient_ && (webSocketPublicClient_ == null ? void 0 : webSocketPublicClient_.chain.id) === chainId)
      return webSocketPublicClient_;
    webSocketPublicClient_ = this.webSocketPublicClients.get(chainId ?? -1);
    if (webSocketPublicClient_)
      return webSocketPublicClient_;
    const { webSocketPublicClient } = this.args;
    webSocketPublicClient_ = typeof webSocketPublicClient === "function" ? webSocketPublicClient({ chainId }) : webSocketPublicClient;
    if (webSocketPublicClient_)
      this.webSocketPublicClients.set(chainId ?? -1, webSocketPublicClient_);
    return webSocketPublicClient_;
  }
  setWebSocketPublicClient(webSocketPublicClient) {
    var _a, _b;
    const chainId = (_b = (_a = this.data) == null ? void 0 : _a.chain) == null ? void 0 : _b.id;
    this.args = {
      ...this.args,
      webSocketPublicClient
    };
    this.webSocketPublicClients.clear();
    this.setState((x) => ({
      ...x,
      webSocketPublicClient: this.getWebSocketPublicClient({
        chainId
      })
    }));
  }
  setLastUsedConnector(lastUsedConnector = null) {
    var _a;
    (_a = this.storage) == null ? void 0 : _a.setItem("wallet", lastUsedConnector);
  }
};
_isAutoConnecting = /* @__PURE__ */ new WeakMap();
_lastUsedConnector = /* @__PURE__ */ new WeakMap();
_addEffects = /* @__PURE__ */ new WeakSet();
addEffects_fn = function() {
  const onChange = (data) => {
    this.setState((x) => ({
      ...x,
      data: { ...x.data, ...data }
    }));
  };
  const onDisconnect = () => {
    this.clearState();
  };
  const onError = (error) => {
    this.setState((x) => ({ ...x, error }));
  };
  this.store.subscribe(
    ({ connector }) => connector,
    (connector, prevConnector) => {
      var _a, _b, _c, _d, _e, _f;
      (_a = prevConnector == null ? void 0 : prevConnector.off) == null ? void 0 : _a.call(prevConnector, "change", onChange);
      (_b = prevConnector == null ? void 0 : prevConnector.off) == null ? void 0 : _b.call(prevConnector, "disconnect", onDisconnect);
      (_c = prevConnector == null ? void 0 : prevConnector.off) == null ? void 0 : _c.call(prevConnector, "error", onError);
      if (!connector)
        return;
      (_d = connector.on) == null ? void 0 : _d.call(connector, "change", onChange);
      (_e = connector.on) == null ? void 0 : _e.call(connector, "disconnect", onDisconnect);
      (_f = connector.on) == null ? void 0 : _f.call(connector, "error", onError);
    }
  );
  const { publicClient, webSocketPublicClient } = this.args;
  const subscribePublicClient = typeof publicClient === "function";
  const subscribeWebSocketPublicClient = typeof webSocketPublicClient === "function";
  if (subscribePublicClient || subscribeWebSocketPublicClient)
    this.store.subscribe(
      ({ data }) => {
        var _a;
        return (_a = data == null ? void 0 : data.chain) == null ? void 0 : _a.id;
      },
      (chainId) => {
        this.setState((x) => ({
          ...x,
          publicClient: this.getPublicClient({ chainId }),
          webSocketPublicClient: this.getWebSocketPublicClient({
            chainId
          })
        }));
      }
    );
};
var config;
function createConfig(args) {
  const config_ = new Config(args);
  config = config_;
  return config_;
}
function getConfig() {
  if (!config) {
    throw new Error(
      "No wagmi config found. Ensure you have set up a config: https://wagmi.sh/react/config"
    );
  }
  return config;
}
async function connect({ chainId, connector }) {
  const config2 = getConfig();
  const activeConnector = config2.connector;
  if (activeConnector && connector.id === activeConnector.id)
    throw new ConnectorAlreadyConnectedError();
  try {
    config2.setState((x) => ({ ...x, status: "connecting" }));
    const data = await connector.connect({ chainId });
    config2.setLastUsedConnector(connector.id);
    config2.setState((x) => ({
      ...x,
      connector,
      chains: connector == null ? void 0 : connector.chains,
      data,
      status: "connected"
    }));
    config2.storage.setItem("connected", true);
    return { ...data, connector };
  } catch (err) {
    config2.setState((x) => {
      return {
        ...x,
        status: x.connector ? "connected" : "disconnected"
      };
    });
    throw err;
  }
}
async function disconnect() {
  const config2 = getConfig();
  if (config2.connector)
    await config2.connector.disconnect();
  config2.clearState();
  config2.storage.removeItem("connected");
}
var erc20ABI = [
  {
    type: "event",
    name: "Approval",
    inputs: [
      {
        indexed: true,
        name: "owner",
        type: "address"
      },
      {
        indexed: true,
        name: "spender",
        type: "address"
      },
      {
        indexed: false,
        name: "value",
        type: "uint256"
      }
    ]
  },
  {
    type: "event",
    name: "Transfer",
    inputs: [
      {
        indexed: true,
        name: "from",
        type: "address"
      },
      {
        indexed: true,
        name: "to",
        type: "address"
      },
      {
        indexed: false,
        name: "value",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "allowance",
    stateMutability: "view",
    inputs: [
      {
        name: "owner",
        type: "address"
      },
      {
        name: "spender",
        type: "address"
      }
    ],
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "approve",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "spender",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  },
  {
    type: "function",
    name: "balanceOf",
    stateMutability: "view",
    inputs: [
      {
        name: "account",
        type: "address"
      }
    ],
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "decimals",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "uint8"
      }
    ]
  },
  {
    type: "function",
    name: "name",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "string"
      }
    ]
  },
  {
    type: "function",
    name: "symbol",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "string"
      }
    ]
  },
  {
    type: "function",
    name: "totalSupply",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "transfer",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "recipient",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  },
  {
    type: "function",
    name: "transferFrom",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "sender",
        type: "address"
      },
      {
        name: "recipient",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  }
];
var erc20ABI_bytes32 = [
  {
    type: "event",
    name: "Approval",
    inputs: [
      {
        indexed: true,
        name: "owner",
        type: "address"
      },
      {
        indexed: true,
        name: "spender",
        type: "address"
      },
      {
        indexed: false,
        name: "value",
        type: "uint256"
      }
    ]
  },
  {
    type: "event",
    name: "Transfer",
    inputs: [
      {
        indexed: true,
        name: "from",
        type: "address"
      },
      {
        indexed: true,
        name: "to",
        type: "address"
      },
      {
        indexed: false,
        name: "value",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "allowance",
    stateMutability: "view",
    inputs: [
      {
        name: "owner",
        type: "address"
      },
      {
        name: "spender",
        type: "address"
      }
    ],
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "approve",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "spender",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  },
  {
    type: "function",
    name: "balanceOf",
    stateMutability: "view",
    inputs: [
      {
        name: "account",
        type: "address"
      }
    ],
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "decimals",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "uint8"
      }
    ]
  },
  {
    type: "function",
    name: "name",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "bytes32"
      }
    ]
  },
  {
    type: "function",
    name: "symbol",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "bytes32"
      }
    ]
  },
  {
    type: "function",
    name: "totalSupply",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "transfer",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "recipient",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  },
  {
    type: "function",
    name: "transferFrom",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "sender",
        type: "address"
      },
      {
        name: "recipient",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  }
];
var erc721ABI = [
  {
    type: "event",
    name: "Approval",
    inputs: [
      {
        indexed: true,
        name: "owner",
        type: "address"
      },
      {
        indexed: true,
        name: "spender",
        type: "address"
      },
      {
        indexed: true,
        name: "tokenId",
        type: "uint256"
      }
    ]
  },
  {
    type: "event",
    name: "ApprovalForAll",
    inputs: [
      {
        indexed: true,
        name: "owner",
        type: "address"
      },
      {
        indexed: true,
        name: "operator",
        type: "address"
      },
      {
        indexed: false,
        name: "approved",
        type: "bool"
      }
    ]
  },
  {
    type: "event",
    name: "Transfer",
    inputs: [
      {
        indexed: true,
        name: "from",
        type: "address"
      },
      {
        indexed: true,
        name: "to",
        type: "address"
      },
      {
        indexed: true,
        name: "tokenId",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "approve",
    stateMutability: "payable",
    inputs: [
      {
        name: "spender",
        type: "address"
      },
      {
        name: "tokenId",
        type: "uint256"
      }
    ],
    outputs: []
  },
  {
    type: "function",
    name: "balanceOf",
    stateMutability: "view",
    inputs: [
      {
        name: "account",
        type: "address"
      }
    ],
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "getApproved",
    stateMutability: "view",
    inputs: [
      {
        name: "tokenId",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "address"
      }
    ]
  },
  {
    type: "function",
    name: "isApprovedForAll",
    stateMutability: "view",
    inputs: [
      {
        name: "owner",
        type: "address"
      },
      {
        name: "operator",
        type: "address"
      }
    ],
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ]
  },
  {
    type: "function",
    name: "name",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "string"
      }
    ]
  },
  {
    type: "function",
    name: "ownerOf",
    stateMutability: "view",
    inputs: [
      {
        name: "tokenId",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "owner",
        type: "address"
      }
    ]
  },
  {
    type: "function",
    name: "safeTransferFrom",
    stateMutability: "payable",
    inputs: [
      {
        name: "from",
        type: "address"
      },
      {
        name: "to",
        type: "address"
      },
      {
        name: "tokenId",
        type: "uint256"
      }
    ],
    outputs: []
  },
  {
    type: "function",
    name: "safeTransferFrom",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "from",
        type: "address"
      },
      {
        name: "to",
        type: "address"
      },
      {
        name: "id",
        type: "uint256"
      },
      {
        name: "data",
        type: "bytes"
      }
    ],
    outputs: []
  },
  {
    type: "function",
    name: "setApprovalForAll",
    stateMutability: "nonpayable",
    inputs: [
      {
        name: "operator",
        type: "address"
      },
      {
        name: "approved",
        type: "bool"
      }
    ],
    outputs: []
  },
  {
    type: "function",
    name: "symbol",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "string"
      }
    ]
  },
  {
    type: "function",
    name: "tokenByIndex",
    stateMutability: "view",
    inputs: [
      {
        name: "index",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "tokenByIndex",
    stateMutability: "view",
    inputs: [
      {
        name: "owner",
        type: "address"
      },
      {
        name: "index",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "tokenId",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "tokenURI",
    stateMutability: "view",
    inputs: [
      {
        name: "tokenId",
        type: "uint256"
      }
    ],
    outputs: [
      {
        name: "",
        type: "string"
      }
    ]
  },
  {
    type: "function",
    name: "totalSupply",
    stateMutability: "view",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ]
  },
  {
    type: "function",
    name: "transferFrom",
    stateMutability: "payable",
    inputs: [
      {
        name: "sender",
        type: "address"
      },
      {
        name: "recipient",
        type: "address"
      },
      {
        name: "tokeId",
        type: "uint256"
      }
    ],
    outputs: []
  }
];
var erc4626ABI = [
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "owner",
        type: "address"
      },
      {
        indexed: true,
        name: "spender",
        type: "address"
      },
      {
        indexed: false,
        name: "value",
        type: "uint256"
      }
    ],
    name: "Approval",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "sender",
        type: "address"
      },
      {
        indexed: true,
        name: "receiver",
        type: "address"
      },
      {
        indexed: false,
        name: "assets",
        type: "uint256"
      },
      {
        indexed: false,
        name: "shares",
        type: "uint256"
      }
    ],
    name: "Deposit",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "from",
        type: "address"
      },
      {
        indexed: true,
        name: "to",
        type: "address"
      },
      {
        indexed: false,
        name: "value",
        type: "uint256"
      }
    ],
    name: "Transfer",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "sender",
        type: "address"
      },
      {
        indexed: true,
        name: "receiver",
        type: "address"
      },
      {
        indexed: true,
        name: "owner",
        type: "address"
      },
      {
        indexed: false,
        name: "assets",
        type: "uint256"
      },
      {
        indexed: false,
        name: "shares",
        type: "uint256"
      }
    ],
    name: "Withdraw",
    type: "event"
  },
  {
    inputs: [
      {
        name: "owner",
        type: "address"
      },
      {
        name: "spender",
        type: "address"
      }
    ],
    name: "allowance",
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "spender",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    name: "approve",
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ],
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    inputs: [],
    name: "asset",
    outputs: [
      {
        name: "assetTokenAddress",
        type: "address"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "account",
        type: "address"
      }
    ],
    name: "balanceOf",
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "shares",
        type: "uint256"
      }
    ],
    name: "convertToAssets",
    outputs: [
      {
        name: "assets",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "assets",
        type: "uint256"
      }
    ],
    name: "convertToShares",
    outputs: [
      {
        name: "shares",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "assets",
        type: "uint256"
      },
      {
        name: "receiver",
        type: "address"
      }
    ],
    name: "deposit",
    outputs: [
      {
        name: "shares",
        type: "uint256"
      }
    ],
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    inputs: [
      {
        name: "caller",
        type: "address"
      }
    ],
    name: "maxDeposit",
    outputs: [
      {
        name: "maxAssets",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "caller",
        type: "address"
      }
    ],
    name: "maxMint",
    outputs: [
      {
        name: "maxShares",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "owner",
        type: "address"
      }
    ],
    name: "maxRedeem",
    outputs: [
      {
        name: "maxShares",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "owner",
        type: "address"
      }
    ],
    name: "maxWithdraw",
    outputs: [
      {
        name: "maxAssets",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "shares",
        type: "uint256"
      },
      {
        name: "receiver",
        type: "address"
      }
    ],
    name: "mint",
    outputs: [
      {
        name: "assets",
        type: "uint256"
      }
    ],
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    inputs: [
      {
        name: "assets",
        type: "uint256"
      }
    ],
    name: "previewDeposit",
    outputs: [
      {
        name: "shares",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "shares",
        type: "uint256"
      }
    ],
    name: "previewMint",
    outputs: [
      {
        name: "assets",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "shares",
        type: "uint256"
      }
    ],
    name: "previewRedeem",
    outputs: [
      {
        name: "assets",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "assets",
        type: "uint256"
      }
    ],
    name: "previewWithdraw",
    outputs: [
      {
        name: "shares",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "shares",
        type: "uint256"
      },
      {
        name: "receiver",
        type: "address"
      },
      {
        name: "owner",
        type: "address"
      }
    ],
    name: "redeem",
    outputs: [
      {
        name: "assets",
        type: "uint256"
      }
    ],
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    inputs: [],
    name: "totalAssets",
    outputs: [
      {
        name: "totalManagedAssets",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [],
    name: "totalSupply",
    outputs: [
      {
        name: "",
        type: "uint256"
      }
    ],
    stateMutability: "view",
    type: "function"
  },
  {
    inputs: [
      {
        name: "to",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    name: "transfer",
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ],
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    inputs: [
      {
        name: "from",
        type: "address"
      },
      {
        name: "to",
        type: "address"
      },
      {
        name: "amount",
        type: "uint256"
      }
    ],
    name: "transferFrom",
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ],
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    inputs: [
      {
        name: "assets",
        type: "uint256"
      },
      {
        name: "receiver",
        type: "address"
      },
      {
        name: "owner",
        type: "address"
      }
    ],
    name: "withdraw",
    outputs: [
      {
        name: "shares",
        type: "uint256"
      }
    ],
    stateMutability: "nonpayable",
    type: "function"
  }
];
async function fetchToken({
  address,
  chainId,
  formatUnits: unit = 18
}) {
  async function fetchToken_({ abi }) {
    const erc20Config = { address, abi, chainId };
    const [decimals, name, symbol, totalSupply] = await readContracts({
      allowFailure: false,
      contracts: [
        { ...erc20Config, functionName: "decimals" },
        { ...erc20Config, functionName: "name" },
        { ...erc20Config, functionName: "symbol" },
        { ...erc20Config, functionName: "totalSupply" }
      ]
    });
    return {
      address,
      decimals,
      name,
      symbol,
      totalSupply: {
        formatted: formatUnits(totalSupply, getUnit(unit)),
        value: totalSupply
      }
    };
  }
  try {
    return await fetchToken_({ abi: erc20ABI });
  } catch (err) {
    if (err instanceof ContractFunctionExecutionError) {
      const { name, symbol, ...rest } = await fetchToken_({
        abi: erc20ABI_bytes32
      });
      return {
        name: hexToString(trim(name, { dir: "right" })),
        symbol: hexToString(trim(symbol, { dir: "right" })),
        ...rest
      };
    }
    throw err;
  }
}
function getPublicClient({ chainId } = {}) {
  const config2 = getConfig();
  if (chainId)
    return config2.getPublicClient({ chainId }) || config2.publicClient;
  return config2.publicClient;
}
async function getWalletClient({
  chainId
} = {}) {
  var _a, _b;
  const config2 = getConfig();
  const walletClient = await ((_b = (_a = config2.connector) == null ? void 0 : _a.getWalletClient) == null ? void 0 : _b.call(_a, { chainId })) || null;
  return walletClient;
}
function getWebSocketPublicClient({
  chainId
} = {}) {
  const config2 = getConfig();
  if (chainId)
    return config2.getWebSocketPublicClient({ chainId }) || config2.webSocketPublicClient;
  return config2.webSocketPublicClient;
}
function watchPublicClient(args, callback) {
  const config2 = getConfig();
  const handleChange = async () => callback(getPublicClient(args));
  const unsubscribe = config2.subscribe(
    ({ publicClient }) => publicClient,
    handleChange
  );
  return unsubscribe;
}
function watchWalletClient({ chainId }, callback) {
  const config2 = getConfig();
  const handleChange = async ({ chainId: chainId_ }) => {
    if (chainId && chainId_ && chainId !== chainId_)
      return;
    const walletClient = await getWalletClient({ chainId });
    if (!getConfig().connector)
      return callback(null);
    return callback(walletClient);
  };
  const unsubscribe = config2.subscribe(
    ({ data, connector }) => {
      var _a;
      return {
        account: data == null ? void 0 : data.account,
        chainId: (_a = data == null ? void 0 : data.chain) == null ? void 0 : _a.id,
        connector
      };
    },
    handleChange,
    {
      equalityFn: shallow$1
    }
  );
  return unsubscribe;
}
function watchWebSocketPublicClient(args, callback) {
  const config2 = getConfig();
  const handleChange = async () => callback(getWebSocketPublicClient(args));
  const unsubscribe = config2.subscribe(
    ({ webSocketPublicClient }) => webSocketPublicClient,
    handleChange
  );
  return unsubscribe;
}
async function prepareWriteContract({
  abi,
  address,
  args,
  chainId,
  dataSuffix,
  functionName,
  walletClient: walletClient_,
  ...config2
}) {
  const publicClient = getPublicClient({ chainId });
  const walletClient = walletClient_ ?? await getWalletClient({ chainId });
  if (!walletClient)
    throw new ConnectorNotFoundError();
  if (chainId)
    assertActiveChain({ chainId });
  const {
    account,
    accessList,
    blockNumber,
    blockTag,
    gas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    nonce,
    value
  } = getCallParameters(config2);
  const { result, request } = await publicClient.simulateContract({
    abi,
    address,
    functionName,
    args,
    account: account || walletClient.account,
    accessList,
    blockNumber,
    blockTag,
    dataSuffix,
    gas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    nonce,
    value
  });
  const minimizedAbi = abi.filter(
    (abiItem) => "name" in abiItem && abiItem.name === functionName
  );
  return {
    mode: "prepared",
    request: {
      ...request,
      abi: minimizedAbi,
      chainId
    },
    result
  };
}
async function multicall({
  chainId,
  contracts,
  blockNumber,
  blockTag,
  ...args
}) {
  const publicClient = getPublicClient({ chainId });
  if (!publicClient.chains)
    throw new ConfigChainsNotFound();
  if (chainId && publicClient.chain.id !== chainId)
    throw new ChainNotConfiguredError({ chainId });
  return publicClient.multicall({
    allowFailure: args.allowFailure ?? true,
    blockNumber,
    blockTag,
    contracts
  });
}
async function readContract({
  address,
  account,
  chainId,
  abi,
  args,
  functionName,
  blockNumber,
  blockTag
}) {
  const publicClient = getPublicClient({ chainId });
  return publicClient.readContract({
    abi,
    address,
    account,
    functionName,
    args,
    blockNumber,
    blockTag
  });
}
async function readContracts({
  contracts,
  blockNumber,
  blockTag,
  ...args
}) {
  const { allowFailure = true } = args;
  try {
    const publicClient = getPublicClient();
    const contractsByChainId = contracts.reduce((contracts2, contract, index) => {
      const chainId = contract.chainId ?? publicClient.chain.id;
      return {
        ...contracts2,
        [chainId]: [...contracts2[chainId] || [], { contract, index }]
      };
    }, {});
    const promises = () => Object.entries(contractsByChainId).map(
      ([chainId, contracts2]) => multicall({
        allowFailure,
        chainId: parseInt(chainId),
        contracts: contracts2.map(
          ({ contract }) => contract
        ),
        blockNumber,
        blockTag
      })
    );
    const multicallResults = (await Promise.all(promises())).flat();
    const resultIndexes = Object.values(contractsByChainId).flatMap(
      (contracts2) => contracts2.map(({ index }) => index)
    );
    return multicallResults.reduce((results, result, index) => {
      if (results)
        results[resultIndexes[index]] = result;
      return results;
    }, []);
  } catch (err) {
    if (err instanceof ContractFunctionExecutionError)
      throw err;
    const promises = () => contracts.map(
      (contract) => readContract({ ...contract, blockNumber, blockTag })
    );
    if (allowFailure)
      return (await Promise.allSettled(promises())).map((result) => {
        if (result.status === "fulfilled")
          return { result: result.value, status: "success" };
        return { error: result.reason, result: void 0, status: "failure" };
      });
    return await Promise.all(promises());
  }
}
async function writeContract(config2) {
  const walletClient = await getWalletClient({ chainId: config2.chainId });
  if (!walletClient)
    throw new ConnectorNotFoundError();
  if (config2.chainId)
    assertActiveChain({ chainId: config2.chainId });
  let request;
  if (config2.mode === "prepared") {
    request = config2.request;
  } else {
    const { chainId: _, mode: __, ...args } = config2;
    const res = await prepareWriteContract(args);
    request = res.request;
  }
  const hash = await walletClient.writeContract({
    ...request,
    chain: config2.chainId ? { id: config2.chainId } : null
  });
  return { hash };
}
async function fetchBalance({
  address,
  chainId,
  formatUnits: unit,
  token
}) {
  const config2 = getConfig();
  const publicClient = getPublicClient({ chainId });
  if (token) {
    const fetchContractBalance = async ({ abi }) => {
      const erc20Config = { abi, address: token, chainId };
      const [value2, decimals, symbol] = await readContracts({
        allowFailure: false,
        contracts: [
          {
            ...erc20Config,
            functionName: "balanceOf",
            args: [address]
          },
          { ...erc20Config, functionName: "decimals" },
          { ...erc20Config, functionName: "symbol" }
        ]
      });
      return {
        decimals,
        formatted: formatUnits(value2 ?? "0", getUnit(unit ?? decimals)),
        symbol,
        value: value2
      };
    };
    try {
      return await fetchContractBalance({ abi: erc20ABI });
    } catch (err) {
      if (err instanceof ContractFunctionExecutionError) {
        const { symbol, ...rest } = await fetchContractBalance({
          abi: erc20ABI_bytes32
        });
        return {
          symbol: hexToString(trim(symbol, { dir: "right" })),
          ...rest
        };
      }
      throw err;
    }
  }
  const chains = [
    ...config2.publicClient.chains || [],
    ...config2.chains ?? []
  ];
  const value = await publicClient.getBalance({ address });
  const chain = chains.find((x) => x.id === publicClient.chain.id);
  return {
    decimals: (chain == null ? void 0 : chain.nativeCurrency.decimals) ?? 18,
    formatted: formatUnits(value ?? "0", getUnit(unit ?? 18)),
    symbol: (chain == null ? void 0 : chain.nativeCurrency.symbol) ?? "ETH",
    value
  };
}
function getAccount() {
  const { data, connector, status } = getConfig();
  switch (status) {
    case "connected":
      return {
        address: data == null ? void 0 : data.account,
        connector,
        isConnected: true,
        isConnecting: false,
        isDisconnected: false,
        isReconnecting: false,
        status
      };
    case "reconnecting":
      return {
        address: data == null ? void 0 : data.account,
        connector,
        isConnected: !!(data == null ? void 0 : data.account),
        isConnecting: false,
        isDisconnected: false,
        isReconnecting: true,
        status
      };
    case "connecting":
      return {
        address: data == null ? void 0 : data.account,
        connector,
        isConnected: false,
        isConnecting: true,
        isDisconnected: false,
        isReconnecting: false,
        status
      };
    case "disconnected":
      return {
        address: void 0,
        connector: void 0,
        isConnected: false,
        isConnecting: false,
        isDisconnected: true,
        isReconnecting: false,
        status
      };
  }
}
function getNetwork() {
  var _a, _b, _c, _d;
  const config2 = getConfig();
  const chainId = (_b = (_a = config2.data) == null ? void 0 : _a.chain) == null ? void 0 : _b.id;
  const activeChains = config2.chains ?? [];
  const activeChain = [
    ...((_c = config2.publicClient) == null ? void 0 : _c.chains) || [],
    ...activeChains
  ].find((x) => x.id === chainId) ?? {
    id: chainId,
    name: `Chain ${chainId}`,
    network: `${chainId}`,
    nativeCurrency: { name: "Ether", decimals: 18, symbol: "ETH" },
    rpcUrls: {
      default: { http: [""] },
      public: { http: [""] }
    }
  };
  return {
    chain: chainId ? {
      ...activeChain,
      ...(_d = config2.data) == null ? void 0 : _d.chain,
      id: chainId
    } : void 0,
    chains: activeChains
  };
}
async function signMessage(args) {
  const walletClient = await getWalletClient();
  if (!walletClient)
    throw new ConnectorNotFoundError();
  return await walletClient.signMessage({
    message: args.message
  });
}
async function signTypedData({
  domain,
  message,
  primaryType,
  types
}) {
  const walletClient = await getWalletClient();
  if (!walletClient)
    throw new ConnectorNotFoundError();
  const { chainId } = domain;
  if (chainId)
    assertActiveChain({ chainId });
  return walletClient.signTypedData({
    message,
    primaryType,
    types,
    domain
  });
}
async function switchNetwork({
  chainId
}) {
  const { connector } = getConfig();
  if (!connector)
    throw new ConnectorNotFoundError();
  if (!connector.switchChain)
    throw new SwitchChainNotSupportedError({
      connector
    });
  return connector.switchChain(chainId);
}
function watchAccount(callback, { selector = (x) => x } = {}) {
  const config2 = getConfig();
  const handleChange = () => callback(getAccount());
  const unsubscribe = config2.subscribe(
    ({ data, connector, status }) => selector({
      address: data == null ? void 0 : data.account,
      connector,
      status
    }),
    handleChange,
    {
      equalityFn: shallow$1
    }
  );
  return unsubscribe;
}
function watchNetwork(callback, { selector = (x) => x } = {}) {
  const config2 = getConfig();
  const handleChange = () => callback(getNetwork());
  const unsubscribe = config2.subscribe(
    ({ data, chains }) => {
      var _a;
      return selector({ chainId: (_a = data == null ? void 0 : data.chain) == null ? void 0 : _a.id, chains });
    },
    handleChange,
    {
      equalityFn: shallow$1
    }
  );
  return unsubscribe;
}
async function fetchEnsAddress({
  chainId,
  name
}) {
  const { normalize } = await import("./ens-CYIXZ44E.js");
  const publicClient = getPublicClient({ chainId });
  const address = await publicClient.getEnsAddress({
    name: normalize(name)
  });
  try {
    if (address === "0x0000000000000000000000000000000000000000")
      return null;
    return address ? getAddress(address) : null;
  } catch (_error) {
    return null;
  }
}
async function fetchEnsAvatar({
  name,
  chainId
}) {
  const { normalize } = await import("./ens-CYIXZ44E.js");
  const publicClient = getPublicClient({ chainId });
  const avatar = await publicClient.getEnsAvatar({ name: normalize(name) });
  return avatar;
}
async function fetchEnsName({
  address,
  chainId
}) {
  const publicClient = getPublicClient({ chainId });
  return publicClient.getEnsName({
    address: getAddress(address)
  });
}
async function fetchEnsResolver({
  chainId,
  name
}) {
  const { normalize } = await import("./ens-CYIXZ44E.js");
  const publicClient = getPublicClient({ chainId });
  const resolver = await publicClient.getEnsResolver({ name: normalize(name) });
  return resolver;
}
async function fetchBlockNumber({
  chainId
} = {}) {
  const publicClient = getPublicClient({ chainId });
  const blockNumber = await publicClient.getBlockNumber();
  return blockNumber;
}
async function fetchFeeData({
  chainId,
  formatUnits: units = "gwei"
} = {}) {
  const publicClient = getPublicClient({ chainId });
  const block = await publicClient.getBlock();
  let gasPrice = null;
  try {
    gasPrice = await publicClient.getGasPrice();
  } catch {
  }
  let lastBaseFeePerGas = null;
  let maxFeePerGas = null;
  let maxPriorityFeePerGas = null;
  if (block == null ? void 0 : block.baseFeePerGas) {
    lastBaseFeePerGas = block.baseFeePerGas;
    maxPriorityFeePerGas = parseGwei("1");
    maxFeePerGas = block.baseFeePerGas * 2n + maxPriorityFeePerGas;
  }
  const unit = getUnit(units);
  const formatted = {
    gasPrice: gasPrice ? formatUnits(gasPrice, unit) : null,
    maxFeePerGas: maxFeePerGas ? formatUnits(maxFeePerGas, unit) : null,
    maxPriorityFeePerGas: maxPriorityFeePerGas ? formatUnits(maxPriorityFeePerGas, unit) : null
  };
  return {
    lastBaseFeePerGas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    formatted
  };
}
async function fetchTransaction({
  chainId,
  hash
}) {
  const publicClient = getPublicClient({ chainId });
  return publicClient.getTransaction({ hash });
}
async function prepareSendTransaction({
  accessList,
  account,
  chainId,
  data,
  gas: gas_,
  gasPrice,
  maxFeePerGas,
  maxPriorityFeePerGas,
  nonce,
  to: to_,
  value,
  walletClient: walletClient_
}) {
  const publicClient = getPublicClient({ chainId });
  const walletClient = walletClient_ ?? await getWalletClient({ chainId });
  if (!walletClient)
    throw new ConnectorNotFoundError();
  if (chainId)
    assertActiveChain({ chainId });
  const to = (to_ && !isAddress(to_) ? await fetchEnsAddress({ name: to_ }) : to_) || void 0;
  if (to && !isAddress(to))
    throw new Error("Invalid address");
  const gas = typeof gas_ === "undefined" ? await publicClient.estimateGas({
    accessList,
    account: walletClient.account,
    data,
    gas: gas_ ?? void 0,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    nonce,
    to,
    value
  }) : gas_ || void 0;
  return {
    accessList,
    account,
    data,
    gas,
    gasPrice,
    maxFeePerGas,
    maxPriorityFeePerGas,
    mode: "prepared",
    nonce,
    to,
    value,
    ...chainId ? { chainId } : {}
  };
}
async function sendTransaction({
  accessList,
  account,
  chainId,
  data,
  gas,
  gasPrice,
  maxFeePerGas,
  maxPriorityFeePerGas,
  mode,
  nonce,
  to,
  value
}) {
  const walletClient = await getWalletClient({ chainId });
  if (!walletClient)
    throw new ConnectorNotFoundError();
  if (chainId)
    assertActiveChain({ chainId });
  let args;
  if (mode === "prepared") {
    args = {
      account,
      accessList,
      chain: null,
      data,
      gas,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      to,
      value
    };
  } else {
    args = await prepareSendTransaction({
      accessList,
      account,
      chainId,
      data,
      gas: gas || null,
      gasPrice,
      maxFeePerGas,
      maxPriorityFeePerGas,
      nonce,
      to,
      value
    });
  }
  const hash = await walletClient.sendTransaction({
    ...args,
    chain: chainId ? { id: chainId } : null
  });
  return { hash };
}
async function waitForTransaction({
  chainId,
  confirmations = 1,
  hash,
  onReplaced,
  timeout = 0
}) {
  const publicClient = getPublicClient({ chainId });
  const receipt = await publicClient.waitForTransactionReceipt({
    hash,
    confirmations,
    onReplaced,
    timeout
  });
  if (receipt.status === "reverted") {
    const txn = await publicClient.getTransaction({
      hash: receipt.transactionHash
    });
    const code = await publicClient.call({
      ...txn,
      gasPrice: txn.type !== "eip1559" ? txn.gasPrice : void 0,
      maxFeePerGas: txn.type === "eip1559" ? txn.maxFeePerGas : void 0,
      maxPriorityFeePerGas: txn.type === "eip1559" ? txn.maxPriorityFeePerGas : void 0
    });
    const reason = hexToString(`0x${code.substring(138)}`);
    throw new Error(reason);
  }
  return receipt;
}
function assertActiveChain({ chainId }) {
  var _a, _b;
  const { chain: activeChain, chains } = getNetwork();
  const activeChainId = activeChain == null ? void 0 : activeChain.id;
  if (activeChainId && chainId !== activeChainId) {
    throw new ChainMismatchError({
      activeChain: ((_a = chains.find((x) => x.id === activeChainId)) == null ? void 0 : _a.name) ?? `Chain ${activeChainId}`,
      targetChain: ((_b = chains.find((x) => x.id === chainId)) == null ? void 0 : _b.name) ?? `Chain ${chainId}`
    });
  }
}

export {
  ChainNotConfiguredForConnectorError,
  ConnectorNotFoundError,
  normalizeChainId,
  __privateGet2 as __privateGet,
  __privateAdd2 as __privateAdd,
  __privateSet2 as __privateSet,
  __privateMethod2 as __privateMethod,
  Connector,
  InjectedConnector,
  configureChains,
  ChainMismatchError,
  ChainNotConfiguredError,
  ConnectorAlreadyConnectedError,
  ConfigChainsNotFound,
  SwitchChainNotSupportedError,
  deepEqual,
  deserialize,
  getCallParameters,
  getSendTransactionParameters,
  serialize,
  noopStorage,
  createStorage,
  createConfig,
  connect,
  disconnect,
  erc20ABI,
  erc721ABI,
  erc4626ABI,
  fetchToken,
  getPublicClient,
  getWalletClient,
  getWebSocketPublicClient,
  watchPublicClient,
  watchWalletClient,
  watchWebSocketPublicClient,
  prepareWriteContract,
  readContract,
  readContracts,
  writeContract,
  fetchBalance,
  getAccount,
  getNetwork,
  signMessage,
  signTypedData,
  switchNetwork,
  watchAccount,
  watchNetwork,
  fetchEnsAddress,
  fetchEnsAvatar,
  fetchEnsName,
  fetchEnsResolver,
  fetchBlockNumber,
  fetchFeeData,
  fetchTransaction,
  prepareSendTransaction,
  sendTransaction,
  waitForTransaction
};
//# sourceMappingURL=chunk-HE7TYL65.js.map
