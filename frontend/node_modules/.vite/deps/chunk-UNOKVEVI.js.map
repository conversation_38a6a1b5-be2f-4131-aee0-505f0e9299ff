{"version": 3, "sources": ["../../proxy-compare/src/index.ts", "../../valtio/esm/vanilla.mjs", "../../@walletconnect/modal-core/src/controllers/RouterCtrl.ts", "../../@walletconnect/modal-core/src/utils/CoreUtil.ts", "../../@walletconnect/modal-core/src/controllers/EventsCtrl.ts", "../../@walletconnect/modal-core/src/controllers/OptionsCtrl.ts", "../../@walletconnect/modal-core/src/controllers/ConfigCtrl.ts", "../../@walletconnect/modal-core/src/utils/ExplorerUtil.ts", "../../@walletconnect/modal-core/src/controllers/ExplorerCtrl.ts", "../../@walletconnect/modal-core/src/controllers/ModalCtrl.ts", "../../@walletconnect/modal-core/src/controllers/ThemeCtrl.ts", "../../@walletconnect/modal-core/src/controllers/ToastCtrl.ts"], "sourcesContent": ["// symbols\nconst TRACK_MEMO_SYMBOL = Symbol();\nconst GET_ORIGINAL_SYMBOL = Symbol();\n\n// properties\nconst AFFECTED_PROPERTY = 'a';\nconst IS_TARGET_COPIED_PROPERTY = 'f';\nconst PROXY_PROPERTY = 'p';\nconst PROXY_CACHE_PROPERTY = 'c';\nconst TARGET_CACHE_PROPERTY = 't';\nconst NEXT_OBJECT_PROPERTY = 'n';\nconst CHANGED_PROPERTY = 'g';\nconst HAS_KEY_PROPERTY = 'h';\nconst ALL_OWN_KEYS_PROPERTY = 'w';\nconst HAS_OWN_KEY_PROPERTY = 'o';\nconst KEYS_PROPERTY = 'k';\n\n// function to create a new bare proxy\nlet newProxy = <T extends object>(\n  target: T,\n  handler: ProxyHandler<T>,\n) => new Proxy(target, handler);\n\n// get object prototype\nconst getProto = Object.getPrototypeOf;\n\nconst objectsToTrack = new WeakMap<object, boolean>();\n\n// check if obj is a plain object or an array\nconst isObjectToTrack = <T>(obj: T): obj is T extends object ? T : never => (\n  obj && (objectsToTrack.has(obj as unknown as object)\n    ? objectsToTrack.get(obj as unknown as object) as boolean\n    : (getProto(obj) === Object.prototype || getProto(obj) === Array.prototype)\n  )\n);\n\n// check if it is object\nconst isObject = (x: unknown): x is object => (\n  typeof x === 'object' && x !== null\n);\n\n// Properties that are both non-configurable and non-writable will break\n// the proxy get trap when we try to return a recursive/child compare proxy\n// from them. We can avoid this by making a copy of the target object with\n// all descriptors marked as configurable, see `copyTargetObject`.\n// See: https://github.com/dai-shi/proxy-compare/pull/8\nconst needsToCopyTargetObject = (obj: object) => (\n  Object.values(Object.getOwnPropertyDescriptors(obj)).some(\n    (descriptor) => !descriptor.configurable && !descriptor.writable,\n  )\n);\n\n// Make a copy with all descriptors marked as configurable.\nconst copyTargetObject = <T extends object>(obj: T): T => {\n  if (Array.isArray(obj)) {\n    // Arrays need a special way to copy\n    return Array.from(obj) as T;\n  }\n  // For non-array objects, we create a new object keeping the prototype\n  // with changing all configurable options (otherwise, proxies will complain)\n  const descriptors = Object.getOwnPropertyDescriptors(obj);\n  Object.values(descriptors).forEach((desc) => { desc.configurable = true; });\n  return Object.create(getProto(obj), descriptors);\n};\n\ntype HasKeySet = Set<string | symbol>\ntype HasOwnKeySet = Set<string | symbol>\ntype KeysSet = Set<string | symbol>\ntype Used = {\n  [HAS_KEY_PROPERTY]?: HasKeySet;\n  [ALL_OWN_KEYS_PROPERTY]?: true;\n  [HAS_OWN_KEY_PROPERTY]?: HasOwnKeySet;\n  [KEYS_PROPERTY]?: KeysSet;\n};\ntype Affected = WeakMap<object, Used>;\ntype ProxyHandlerState<T extends object> = {\n  readonly [IS_TARGET_COPIED_PROPERTY]: boolean;\n  [PROXY_PROPERTY]?: T;\n  [PROXY_CACHE_PROPERTY]?: ProxyCache<object> | undefined;\n  [TARGET_CACHE_PROPERTY]?: TargetCache<object> | undefined;\n  [AFFECTED_PROPERTY]?: Affected;\n}\ntype ProxyCache<T extends object> = WeakMap<\n  object,\n  readonly [ProxyHandler<T>, ProxyHandlerState<T>]\n>;\ntype TargetCache<T extends object> = WeakMap<\n  object,\n  readonly [target: T, copiedTarget?: T]\n>;\n\nconst createProxyHandler = <T extends object>(origObj: T, isTargetCopied: boolean) => {\n  const state: ProxyHandlerState<T> = {\n    [IS_TARGET_COPIED_PROPERTY]: isTargetCopied,\n  };\n  let trackObject = false; // for trackMemo\n  const recordUsage = (\n    type:\n      | typeof HAS_KEY_PROPERTY\n      | typeof ALL_OWN_KEYS_PROPERTY\n      | typeof HAS_OWN_KEY_PROPERTY\n      | typeof KEYS_PROPERTY,\n    key?: string | symbol,\n  ) => {\n    if (!trackObject) {\n      let used = (state[AFFECTED_PROPERTY] as Affected).get(origObj);\n      if (!used) {\n        used = {};\n        (state[AFFECTED_PROPERTY] as Affected).set(origObj, used);\n      }\n      if (type === ALL_OWN_KEYS_PROPERTY) {\n        used[ALL_OWN_KEYS_PROPERTY] = true;\n      } else {\n        let set = used[type];\n        if (!set) {\n          set = new Set();\n          used[type] = set;\n        }\n        set.add(key as string | symbol);\n      }\n    }\n  };\n  const recordObjectAsUsed = () => {\n    trackObject = true;\n    (state[AFFECTED_PROPERTY] as Affected).delete(origObj);\n  };\n  const handler: ProxyHandler<T> = {\n    get(target, key) {\n      if (key === GET_ORIGINAL_SYMBOL) {\n        return origObj;\n      }\n      recordUsage(KEYS_PROPERTY, key);\n      return createProxy(\n        Reflect.get(target, key),\n        (state[AFFECTED_PROPERTY] as Affected),\n        state[PROXY_CACHE_PROPERTY],\n        state[TARGET_CACHE_PROPERTY],\n      );\n    },\n    has(target, key) {\n      if (key === TRACK_MEMO_SYMBOL) {\n        recordObjectAsUsed();\n        return true;\n      }\n      recordUsage(HAS_KEY_PROPERTY, key);\n      return Reflect.has(target, key);\n    },\n    getOwnPropertyDescriptor(target, key) {\n      recordUsage(HAS_OWN_KEY_PROPERTY, key);\n      return Reflect.getOwnPropertyDescriptor(target, key);\n    },\n    ownKeys(target) {\n      recordUsage(ALL_OWN_KEYS_PROPERTY);\n      return Reflect.ownKeys(target);\n    },\n  };\n  if (isTargetCopied) {\n    handler.set = handler.deleteProperty = () => false;\n  }\n  return [handler, state] as const;\n};\n\nconst getOriginalObject = <T extends object>(obj: T) => (\n  // unwrap proxy\n  (obj as { [GET_ORIGINAL_SYMBOL]?: typeof obj })[GET_ORIGINAL_SYMBOL]\n  // otherwise\n  || obj\n);\n\n/**\n * Create a proxy.\n *\n * This function will create a proxy at top level and proxy nested objects as you access them,\n * in order to keep track of which properties were accessed via get/has proxy handlers:\n *\n * NOTE: Printing of WeakMap is hard to inspect and not very readable\n * for this purpose you can use the `affectedToPathList` helper.\n *\n * @param {object} obj - Object that will be wrapped on the proxy.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that will hold the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [proxyCache] -\n * WeakMap that will help keep referential identity for proxies.\n * @returns {Proxy<object>} - Object wrapped in a proxy.\n *\n * @example\n * import { createProxy } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n * const proxy = createProxy(original, affected);\n *\n * proxy.a // Will mark as used and track its value.\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"a\"\n *\n * proxy.d // Will mark \"d\" as accessed to track and proxy itself ({ e: \"3\" }).\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"d\"\n */\nexport const createProxy = <T>(\n  obj: T,\n  affected: WeakMap<object, unknown>,\n  proxyCache?: WeakMap<object, unknown>,\n  targetCache?: WeakMap<object, unknown>,\n): T => {\n  if (!isObjectToTrack(obj)) return obj;\n  let targetAndCopied = (\n    targetCache && (targetCache as TargetCache<typeof obj>).get(obj)\n  );\n  if (!targetAndCopied) {\n    const target = getOriginalObject(obj);\n    if (needsToCopyTargetObject(target)) {\n      targetAndCopied = [target, copyTargetObject(target)];\n    } else {\n      targetAndCopied = [target];\n    }\n    targetCache?.set(obj, targetAndCopied);\n  }\n  const [target, copiedTarget] = targetAndCopied;\n  let handlerAndState = (\n    proxyCache && (proxyCache as ProxyCache<typeof target>).get(target)\n  );\n  if (\n    !handlerAndState\n    || handlerAndState[1][IS_TARGET_COPIED_PROPERTY] !== !!copiedTarget\n  ) {\n    handlerAndState = createProxyHandler<typeof target>(target, !!copiedTarget);\n    handlerAndState[1][PROXY_PROPERTY] = newProxy(\n      copiedTarget || target,\n      handlerAndState[0],\n    );\n    if (proxyCache) {\n      proxyCache.set(target, handlerAndState);\n    }\n  }\n  handlerAndState[1][AFFECTED_PROPERTY] = affected as Affected;\n  handlerAndState[1][PROXY_CACHE_PROPERTY] = proxyCache as ProxyCache<object> | undefined;\n  handlerAndState[1][TARGET_CACHE_PROPERTY] = targetCache as TargetCache<object> | undefined;\n  return handlerAndState[1][PROXY_PROPERTY] as typeof target;\n};\n\nconst isAllOwnKeysChanged = (prevObj: object, nextObj: object) => {\n  const prevKeys = Reflect.ownKeys(prevObj);\n  const nextKeys = Reflect.ownKeys(nextObj);\n  return prevKeys.length !== nextKeys.length\n    || prevKeys.some((k, i) => k !== nextKeys[i]);\n};\n\ntype ChangedCache = WeakMap<object, {\n  [NEXT_OBJECT_PROPERTY]: object;\n  [CHANGED_PROPERTY]: boolean;\n}>;\n\n/**\n * Compare changes on objects.\n *\n * This will compare the affected properties on tracked objects inside the proxy\n * to check if there were any changes made to it,\n * by default if no property was accessed on the proxy it will attempt to do a\n * reference equality check for the objects provided (Object.is(a, b)). If you access a property\n * on the proxy, then isChanged will only compare the affected properties.\n *\n * @param {object} prevObj - The previous object to compare.\n * @param {object} nextObj - Object to compare with the previous one.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that holds the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [cache] -\n * WeakMap that holds a cache of the comparisons for better performance with repetitive comparisons,\n * and to avoid infinite loop with circular structures.\n * @returns {boolean} - Boolean indicating if the affected property on the object has changed.\n *\n * @example\n * import { createProxy, isChanged } from 'proxy-compare';\n *\n * const obj = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(obj, affected);\n *\n * proxy.a\n *\n * isChanged(obj, { a: \"1\" }, affected) // false\n *\n * proxy.a = \"2\"\n *\n * isChanged(obj, { a: \"1\" }, affected) // true\n */\n\nexport const isChanged = (\n  prevObj: unknown,\n  nextObj: unknown,\n  affected: WeakMap<object, unknown>,\n  cache?: WeakMap<object, unknown>,\n): boolean => {\n  if (Object.is(prevObj, nextObj)) {\n    return false;\n  }\n  if (!isObject(prevObj) || !isObject(nextObj)) return true;\n  const used = (affected as Affected).get(getOriginalObject(prevObj));\n  if (!used) return true;\n  if (cache) {\n    const hit = (cache as ChangedCache).get(prevObj);\n    if (hit && hit[NEXT_OBJECT_PROPERTY] === nextObj) {\n      return hit[CHANGED_PROPERTY];\n    }\n    // for object with cycles\n    (cache as ChangedCache).set(prevObj, {\n      [NEXT_OBJECT_PROPERTY]: nextObj,\n      [CHANGED_PROPERTY]: false,\n    });\n  }\n  let changed: boolean | null = null;\n  try {\n    for (const key of used[HAS_KEY_PROPERTY] || []) {\n      changed = Reflect.has(prevObj, key) !== Reflect.has(nextObj, key);\n      if (changed) return changed;\n    }\n    if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n      changed = isAllOwnKeysChanged(prevObj, nextObj);\n      if (changed) return changed;\n    } else {\n      for (const key of used[HAS_OWN_KEY_PROPERTY] || []) {\n        const hasPrev = !!Reflect.getOwnPropertyDescriptor(prevObj, key);\n        const hasNext = !!Reflect.getOwnPropertyDescriptor(nextObj, key);\n        changed = hasPrev !== hasNext;\n        if (changed) return changed;\n      }\n    }\n    for (const key of used[KEYS_PROPERTY] || []) {\n      changed = isChanged(\n        (prevObj as any)[key],\n        (nextObj as any)[key],\n        affected,\n        cache,\n      );\n      if (changed) return changed;\n    }\n    if (changed === null) changed = true;\n    return changed;\n  } finally {\n    if (cache) {\n      cache.set(prevObj, {\n        [NEXT_OBJECT_PROPERTY]: nextObj,\n        [CHANGED_PROPERTY]: changed,\n      });\n    }\n  }\n};\n\n// explicitly track object with memo\nexport const trackMemo = (obj: unknown) => {\n  if (isObjectToTrack(obj)) {\n    return TRACK_MEMO_SYMBOL in obj;\n  }\n  return false;\n};\n\n/**\n * Unwrap proxy to get the original object.\n *\n * Used to retrieve the original object used to create the proxy instance with `createProxy`.\n *\n * @param {Proxy<object>} obj -  The proxy wrapper of the originial object.\n * @returns {object | null} - Return either the unwrapped object if exists.\n *\n * @example\n * import { createProxy, getUntracked } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n * const originalFromProxy = getUntracked(proxy)\n *\n * Object.is(original, originalFromProxy) // true\n * isChanged(original, originalFromProxy, affected) // false\n */\nexport const getUntracked = <T>(obj: T): T | null => {\n  if (isObjectToTrack(obj)) {\n    return (obj as { [GET_ORIGINAL_SYMBOL]?: T })[GET_ORIGINAL_SYMBOL] || null;\n  }\n  return null;\n};\n\n/**\n * Mark object to be tracked.\n *\n * This function marks an object that will be passed into `createProxy`\n * as marked to track or not. By default only Array and Object are marked to track,\n * so this is useful for example to mark a class instance to track or to mark a object\n * to be untracked when creating your proxy.\n *\n * @param obj - Object to mark as tracked or not.\n * @param mark - Boolean indicating whether you want to track this object or not.\n * @returns - No return.\n *\n * @example\n * import { createProxy, markToTrack, isChanged } from 'proxy-compare';\n *\n * const nested = { e: \"3\" }\n *\n * markToTrack(nested, false)\n *\n * const original = { a: \"1\", c: \"2\", d: nested };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n *\n * proxy.d.e\n *\n * isChanged(original, { d: { e: \"3\" } }, affected) // true\n */\nexport const markToTrack = (obj: object, mark = true) => {\n  objectsToTrack.set(obj, mark);\n};\n\n/**\n * Convert `affected` to path list\n *\n * `affected` is a weak map which is not printable.\n * This function is can convert it to printable path list.\n * It's for debugging purpose.\n *\n * @param obj - An object that is used with `createProxy`.\n * @param affected - A weak map that is used with `createProxy`.\n * @param onlyWithValues - An optional boolean to exclude object getters.\n * @returns - An array of paths.\n */\nexport const affectedToPathList = (\n  obj: unknown,\n  affected: WeakMap<object, unknown>,\n  onlyWithValues?: boolean,\n) => {\n  const list: (string | symbol)[][] = [];\n  const seen = new WeakSet();\n  const walk = (x: unknown, path?: (string | symbol)[]) => {\n    if (seen.has(x as object)) {\n      // for object with cycles\n      return;\n    }\n    if (isObject(x)) {\n      seen.add(x);\n    }\n    const used = isObject(x) && (affected as Affected).get(getOriginalObject(x));\n    if (used) {\n      used[HAS_KEY_PROPERTY]?.forEach((key) => {\n        const segment = `:has(${String(key)})`;\n        list.push(path ? [...path, segment] : [segment]);\n      });\n      if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n        const segment = ':ownKeys';\n        list.push(path ? [...path, segment] : [segment]);\n      } else {\n        used[HAS_OWN_KEY_PROPERTY]?.forEach((key) => {\n          const segment = `:hasOwn(${String(key)})`;\n          list.push(path ? [...path, segment] : [segment]);\n        });\n      }\n      used[KEYS_PROPERTY]?.forEach((key) => {\n        if (!onlyWithValues || 'value' in (Object.getOwnPropertyDescriptor(x, key) || {})) {\n          walk((x as any)[key], path ? [...path, key] : [key]);\n        }\n      });\n    } else if (path) {\n      list.push(path);\n    }\n  };\n  walk(obj);\n  return list;\n};\n\n/**\n * replace newProxy function.\n *\n * This can be used if you want to use proxy-polyfill.\n * Note that proxy-polyfill can't polyfill everything.\n * Use it at your own risk.\n */\nexport const replaceNewProxy = (fn: typeof newProxy) => {\n  newProxy = fn;\n};\n", "import { markToTrack, getUntracked } from 'proxy-compare';\n\nconst isObject = (x) => typeof x === \"object\" && x !== null;\nconst proxyStateMap = /* @__PURE__ */ new WeakMap();\nconst refSet = /* @__PURE__ */ new WeakSet();\nconst buildProxyFunction = (objectIs = Object.is, newProxy = (target, handler) => new Proxy(target, handler), canProxy = (x) => isObject(x) && !refSet.has(x) && (Array.isArray(x) || !(Symbol.iterator in x)) && !(x instanceof WeakMap) && !(x instanceof WeakSet) && !(x instanceof Error) && !(x instanceof Number) && !(x instanceof Date) && !(x instanceof String) && !(x instanceof RegExp) && !(x instanceof ArrayBuffer), defaultHandlePromise = (promise) => {\n  switch (promise.status) {\n    case \"fulfilled\":\n      return promise.value;\n    case \"rejected\":\n      throw promise.reason;\n    default:\n      throw promise;\n  }\n}, snapCache = /* @__PURE__ */ new WeakMap(), createSnapshot = (target, version, handlePromise = defaultHandlePromise) => {\n  const cache = snapCache.get(target);\n  if ((cache == null ? void 0 : cache[0]) === version) {\n    return cache[1];\n  }\n  const snap = Array.isArray(target) ? [] : Object.create(Object.getPrototypeOf(target));\n  markToTrack(snap, true);\n  snapCache.set(target, [version, snap]);\n  Reflect.ownKeys(target).forEach((key) => {\n    if (Object.getOwnPropertyDescriptor(snap, key)) {\n      return;\n    }\n    const value = Reflect.get(target, key);\n    const desc = {\n      value,\n      enumerable: true,\n      // This is intentional to avoid copying with proxy-compare.\n      // It's still non-writable, so it avoids assigning a value.\n      configurable: true\n    };\n    if (refSet.has(value)) {\n      markToTrack(value, false);\n    } else if (value instanceof Promise) {\n      delete desc.value;\n      desc.get = () => handlePromise(value);\n    } else if (proxyStateMap.has(value)) {\n      const [target2, ensureVersion] = proxyStateMap.get(\n        value\n      );\n      desc.value = createSnapshot(\n        target2,\n        ensureVersion(),\n        handlePromise\n      );\n    }\n    Object.defineProperty(snap, key, desc);\n  });\n  return Object.preventExtensions(snap);\n}, proxyCache = /* @__PURE__ */ new WeakMap(), versionHolder = [1, 1], proxyFunction = (initialObject) => {\n  if (!isObject(initialObject)) {\n    throw new Error(\"object required\");\n  }\n  const found = proxyCache.get(initialObject);\n  if (found) {\n    return found;\n  }\n  let version = versionHolder[0];\n  const listeners = /* @__PURE__ */ new Set();\n  const notifyUpdate = (op, nextVersion = ++versionHolder[0]) => {\n    if (version !== nextVersion) {\n      version = nextVersion;\n      listeners.forEach((listener) => listener(op, nextVersion));\n    }\n  };\n  let checkVersion = versionHolder[1];\n  const ensureVersion = (nextCheckVersion = ++versionHolder[1]) => {\n    if (checkVersion !== nextCheckVersion && !listeners.size) {\n      checkVersion = nextCheckVersion;\n      propProxyStates.forEach(([propProxyState]) => {\n        const propVersion = propProxyState[1](nextCheckVersion);\n        if (propVersion > version) {\n          version = propVersion;\n        }\n      });\n    }\n    return version;\n  };\n  const createPropListener = (prop) => (op, nextVersion) => {\n    const newOp = [...op];\n    newOp[1] = [prop, ...newOp[1]];\n    notifyUpdate(newOp, nextVersion);\n  };\n  const propProxyStates = /* @__PURE__ */ new Map();\n  const addPropListener = (prop, propProxyState) => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && propProxyStates.has(prop)) {\n      throw new Error(\"prop listener already exists\");\n    }\n    if (listeners.size) {\n      const remove = propProxyState[3](createPropListener(prop));\n      propProxyStates.set(prop, [propProxyState, remove]);\n    } else {\n      propProxyStates.set(prop, [propProxyState]);\n    }\n  };\n  const removePropListener = (prop) => {\n    var _a;\n    const entry = propProxyStates.get(prop);\n    if (entry) {\n      propProxyStates.delete(prop);\n      (_a = entry[1]) == null ? void 0 : _a.call(entry);\n    }\n  };\n  const addListener = (listener) => {\n    listeners.add(listener);\n    if (listeners.size === 1) {\n      propProxyStates.forEach(([propProxyState, prevRemove], prop) => {\n        if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && prevRemove) {\n          throw new Error(\"remove already exists\");\n        }\n        const remove = propProxyState[3](createPropListener(prop));\n        propProxyStates.set(prop, [propProxyState, remove]);\n      });\n    }\n    const removeListener = () => {\n      listeners.delete(listener);\n      if (listeners.size === 0) {\n        propProxyStates.forEach(([propProxyState, remove], prop) => {\n          if (remove) {\n            remove();\n            propProxyStates.set(prop, [propProxyState]);\n          }\n        });\n      }\n    };\n    return removeListener;\n  };\n  const baseObject = Array.isArray(initialObject) ? [] : Object.create(Object.getPrototypeOf(initialObject));\n  const handler = {\n    deleteProperty(target, prop) {\n      const prevValue = Reflect.get(target, prop);\n      removePropListener(prop);\n      const deleted = Reflect.deleteProperty(target, prop);\n      if (deleted) {\n        notifyUpdate([\"delete\", [prop], prevValue]);\n      }\n      return deleted;\n    },\n    set(target, prop, value, receiver) {\n      const hasPrevValue = Reflect.has(target, prop);\n      const prevValue = Reflect.get(target, prop, receiver);\n      if (hasPrevValue && (objectIs(prevValue, value) || proxyCache.has(value) && objectIs(prevValue, proxyCache.get(value)))) {\n        return true;\n      }\n      removePropListener(prop);\n      if (isObject(value)) {\n        value = getUntracked(value) || value;\n      }\n      let nextValue = value;\n      if (value instanceof Promise) {\n        value.then((v) => {\n          value.status = \"fulfilled\";\n          value.value = v;\n          notifyUpdate([\"resolve\", [prop], v]);\n        }).catch((e) => {\n          value.status = \"rejected\";\n          value.reason = e;\n          notifyUpdate([\"reject\", [prop], e]);\n        });\n      } else {\n        if (!proxyStateMap.has(value) && canProxy(value)) {\n          nextValue = proxyFunction(value);\n        }\n        const childProxyState = !refSet.has(nextValue) && proxyStateMap.get(nextValue);\n        if (childProxyState) {\n          addPropListener(prop, childProxyState);\n        }\n      }\n      Reflect.set(target, prop, nextValue, receiver);\n      notifyUpdate([\"set\", [prop], value, prevValue]);\n      return true;\n    }\n  };\n  const proxyObject = newProxy(baseObject, handler);\n  proxyCache.set(initialObject, proxyObject);\n  const proxyState = [\n    baseObject,\n    ensureVersion,\n    createSnapshot,\n    addListener\n  ];\n  proxyStateMap.set(proxyObject, proxyState);\n  Reflect.ownKeys(initialObject).forEach((key) => {\n    const desc = Object.getOwnPropertyDescriptor(\n      initialObject,\n      key\n    );\n    if (\"value\" in desc) {\n      proxyObject[key] = initialObject[key];\n      delete desc.value;\n      delete desc.writable;\n    }\n    Object.defineProperty(baseObject, key, desc);\n  });\n  return proxyObject;\n}) => [\n  // public functions\n  proxyFunction,\n  // shared state\n  proxyStateMap,\n  refSet,\n  // internal things\n  objectIs,\n  newProxy,\n  canProxy,\n  defaultHandlePromise,\n  snapCache,\n  createSnapshot,\n  proxyCache,\n  versionHolder\n];\nconst [defaultProxyFunction] = buildProxyFunction();\nfunction proxy(initialObject = {}) {\n  return defaultProxyFunction(initialObject);\n}\nfunction getVersion(proxyObject) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  return proxyState == null ? void 0 : proxyState[1]();\n}\nfunction subscribe(proxyObject, callback, notifyInSync) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  let promise;\n  const ops = [];\n  const addListener = proxyState[3];\n  let isListenerActive = false;\n  const listener = (op) => {\n    ops.push(op);\n    if (notifyInSync) {\n      callback(ops.splice(0));\n      return;\n    }\n    if (!promise) {\n      promise = Promise.resolve().then(() => {\n        promise = void 0;\n        if (isListenerActive) {\n          callback(ops.splice(0));\n        }\n      });\n    }\n  };\n  const removeListener = addListener(listener);\n  isListenerActive = true;\n  return () => {\n    isListenerActive = false;\n    removeListener();\n  };\n}\nfunction snapshot(proxyObject, handlePromise) {\n  const proxyState = proxyStateMap.get(proxyObject);\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !proxyState) {\n    console.warn(\"Please use proxy object\");\n  }\n  const [target, ensureVersion, createSnapshot] = proxyState;\n  return createSnapshot(target, ensureVersion(), handlePromise);\n}\nfunction ref(obj) {\n  refSet.add(obj);\n  return obj;\n}\nconst unstable_buildProxyFunction = buildProxyFunction;\n\nexport { getVersion, proxy, ref, snapshot, subscribe, unstable_buildProxyFunction };\n", "import { proxy, subscribe as valtioSub } from 'valtio/vanilla'\nimport type { RouterCtrlState } from '../types/controllerTypes'\n\n// -- initial state ------------------------------------------------ //\nconst state = proxy<RouterCtrlState>({\n  history: ['ConnectWallet'],\n  view: 'ConnectWallet',\n  data: undefined\n})\n\n// -- controller --------------------------------------------------- //\nexport const RouterCtrl = {\n  state,\n\n  subscribe(callback: (newState: RouterCtrlState) => void) {\n    return valtioSub(state, () => callback(state))\n  },\n\n  push(view: RouterCtrlState['view'], data?: RouterCtrlState['data']) {\n    if (view !== state.view) {\n      state.view = view\n      if (data) {\n        state.data = data\n      }\n      state.history.push(view)\n    }\n  },\n\n  reset(view: RouterCtrlState['view']) {\n    state.view = view\n    state.history = [view]\n  },\n\n  replace(view: RouterCtrlState['view']) {\n    if (state.history.length > 1) {\n      state.history[state.history.length - 1] = view\n      state.view = view\n    }\n  },\n\n  goBack() {\n    if (state.history.length > 1) {\n      state.history.pop()\n      const [last] = state.history.slice(-1)\n      state.view = last\n    }\n  },\n\n  setData(data: RouterCtrlState['data']) {\n    state.data = data\n  }\n}\n", "import { RouterCtrl } from '../controllers/RouterCtrl'\n\nexport const CoreUtil = {\n  WALLETCONNECT_DEEPLINK_CHOICE: 'WALLETCONNECT_DEEPLINK_CHOICE',\n\n  WCM_VERSION: 'WCM_VERSION',\n\n  RECOMMENDED_WALLET_AMOUNT: 9,\n\n  isMobile() {\n    if (typeof window !== 'undefined') {\n      return Boolean(\n        window.matchMedia('(pointer:coarse)').matches ||\n          /Android|webOS|iPhone|iPad|iPod|BlackBerry|Opera Mini/u.test(navigator.userAgent)\n      )\n    }\n\n    return false\n  },\n\n  isAndroid() {\n    return CoreUtil.isMobile() && navigator.userAgent.toLowerCase().includes('android')\n  },\n\n  isIos() {\n    const ua = navigator.userAgent.toLowerCase()\n\n    return CoreUtil.isMobile() && (ua.includes('iphone') || ua.includes('ipad'))\n  },\n\n  isHttpUrl(url: string) {\n    return url.startsWith('http://') || url.startsWith('https://')\n  },\n\n  isArray<T>(data?: T | T[]): data is T[] {\n    return Array.isArray(data) && data.length > 0\n  },\n\n  formatNativeUrl(appUrl: string, wcUri: string, name: string): string {\n    if (CoreUtil.isHttpUrl(appUrl)) {\n      return this.formatUniversalUrl(appUrl, wcUri, name)\n    }\n    let safeAppUrl = appUrl\n    if (!safeAppUrl.includes('://')) {\n      safeAppUrl = appUrl.replaceAll('/', '').replaceAll(':', '')\n      safeAppUrl = `${safeAppUrl}://`\n    }\n    if (!safeAppUrl.endsWith('/')) {\n      safeAppUrl = `${safeAppUrl}/`\n    }\n    this.setWalletConnectDeepLink(safeAppUrl, name)\n    const encodedWcUrl = encodeURIComponent(wcUri)\n\n    return `${safeAppUrl}wc?uri=${encodedWcUrl}`\n  },\n\n  formatUniversalUrl(appUrl: string, wcUri: string, name: string): string {\n    if (!CoreUtil.isHttpUrl(appUrl)) {\n      return this.formatNativeUrl(appUrl, wcUri, name)\n    }\n    let safeAppUrl = appUrl\n    if (!safeAppUrl.endsWith('/')) {\n      safeAppUrl = `${safeAppUrl}/`\n    }\n    this.setWalletConnectDeepLink(safeAppUrl, name)\n    const encodedWcUrl = encodeURIComponent(wcUri)\n\n    return `${safeAppUrl}wc?uri=${encodedWcUrl}`\n  },\n\n  async wait(miliseconds: number) {\n    return new Promise(resolve => {\n      setTimeout(resolve, miliseconds)\n    })\n  },\n\n  openHref(href: string, target: '_blank' | '_self') {\n    window.open(href, target, 'noreferrer noopener')\n  },\n\n  setWalletConnectDeepLink(href: string, name: string) {\n    try {\n      localStorage.setItem(CoreUtil.WALLETCONNECT_DEEPLINK_CHOICE, JSON.stringify({ href, name }))\n    } catch {\n      console.info('Unable to set WalletConnect deep link')\n    }\n  },\n\n  setWalletConnectAndroidDeepLink(wcUri: string) {\n    try {\n      const [href] = wcUri.split('?')\n      localStorage.setItem(\n        CoreUtil.WALLETCONNECT_DEEPLINK_CHOICE,\n        JSON.stringify({ href, name: 'Android' })\n      )\n    } catch {\n      console.info('Unable to set WalletConnect android deep link')\n    }\n  },\n\n  removeWalletConnectDeepLink() {\n    try {\n      localStorage.removeItem(CoreUtil.WALLETCONNECT_DEEPLINK_CHOICE)\n    } catch {\n      console.info('Unable to remove WalletConnect deep link')\n    }\n  },\n\n  setModalVersionInStorage() {\n    try {\n      if (typeof localStorage !== 'undefined') {\n        localStorage.setItem(CoreUtil.WCM_VERSION, process.env.ROLLUP_WCM_VERSION ?? 'UNKNOWN')\n      }\n    } catch {\n      console.info('Unable to set Web3Modal version in storage')\n    }\n  },\n\n  getWalletRouterData() {\n    const routerData = RouterCtrl.state.data?.Wallet\n    if (!routerData) {\n      throw new Error('Missing \"Wallet\" view data')\n    }\n\n    return routerData\n  }\n}\n", "import { proxy, snapshot, subscribe as valtioSub } from 'valtio/vanilla'\nimport type { EventsCtrlState, ModalEvent, ModalEventData } from '../types/controllerTypes'\n\n// -- helpers ------------------------------------------------------ //\nconst isEnabled =\n  typeof location !== 'undefined' &&\n  (location.hostname.includes('localhost') || location.protocol.includes('https'))\n\n// -- initial state ------------------------------------------------ //\nconst state = proxy<EventsCtrlState>({\n  enabled: isEnabled,\n  userSessionId: '',\n  events: [],\n  connectedWalletId: undefined\n})\n\n// -- controller --------------------------------------------------- //\nexport const EventsCtrl = {\n  state,\n\n  subscribe(callback: (newEvent: ModalEvent) => void) {\n    return valtioSub(state.events, () => callback(snapshot(state.events[state.events.length - 1])))\n  },\n\n  initialize() {\n    if (state.enabled && typeof crypto?.randomUUID !== 'undefined') {\n      state.userSessionId = crypto.randomUUID()\n    }\n  },\n\n  setConnectedWalletId(connectedWalletId: EventsCtrlState['connectedWalletId']) {\n    state.connectedWalletId = connectedWalletId\n  },\n\n  click(data: ModalEventData) {\n    if (state.enabled) {\n      const event = {\n        type: 'CLICK' as const,\n        name: data.name,\n        userSessionId: state.userSessionId,\n        timestamp: Date.now(),\n        data\n      }\n      state.events.push(event)\n    }\n  },\n\n  track(data: ModalEventData) {\n    if (state.enabled) {\n      const event = {\n        type: 'TRACK' as const,\n        name: data.name,\n        userSessionId: state.userSessionId,\n        timestamp: Date.now(),\n        data\n      }\n      state.events.push(event)\n    }\n  },\n\n  view(data: ModalEventData) {\n    if (state.enabled) {\n      const event = {\n        type: 'VIEW' as const,\n        name: data.name,\n        userSessionId: state.userSessionId,\n        timestamp: Date.now(),\n        data\n      }\n      state.events.push(event)\n    }\n  }\n}\n", "import { proxy, subscribe as valtioSub } from 'valtio/vanilla'\nimport type { OptionsCtrlState } from '../types/controllerTypes'\n\n// -- initial state ------------------------------------------------ //\nconst state = proxy<OptionsCtrlState>({\n  chains: undefined,\n  walletConnectUri: undefined,\n  isAuth: false,\n  isCustomDesktop: false,\n  isCustomMobile: false,\n  isDataLoaded: false,\n  isUiLoaded: false\n})\n\n// -- controller --------------------------------------------------- //\nexport const OptionsCtrl = {\n  state,\n\n  subscribe(callback: (newState: OptionsCtrlState) => void) {\n    return valtioSub(state, () => callback(state))\n  },\n\n  setChains(chains: OptionsCtrlState['chains']) {\n    state.chains = chains\n  },\n\n  setWalletConnectUri(walletConnectUri: OptionsCtrlState['walletConnectUri']) {\n    state.walletConnectUri = walletConnectUri\n  },\n\n  setIsCustomDesktop(isCustomDesktop: OptionsCtrlState['isCustomDesktop']) {\n    state.isCustomDesktop = isCustomDesktop\n  },\n\n  setIsCustomMobile(isCustomMobile: OptionsCtrlState['isCustomMobile']) {\n    state.isCustomMobile = isCustomMobile\n  },\n\n  setIsDataLoaded(isDataLoaded: OptionsCtrlState['isDataLoaded']) {\n    state.isDataLoaded = isDataLoaded\n  },\n\n  setIsUiLoaded(isUiLoaded: OptionsCtrlState['isUiLoaded']) {\n    state.isUiLoaded = isUiLoaded\n  },\n\n  setIsAuth(isAuth: OptionsCtrlState['isAuth']) {\n    state.isAuth = isAuth\n  }\n}\n", "import { proxy, subscribe as valtioSub } from 'valtio/vanilla'\nimport type { ConfigCtrlState } from '../types/controllerTypes'\nimport { CoreUtil } from '../utils/CoreUtil'\nimport { EventsCtrl } from './EventsCtrl'\nimport { OptionsCtrl } from './OptionsCtrl'\n\nconst state = proxy<ConfigCtrlState>({\n  projectId: '',\n  mobileWallets: undefined,\n  desktopWallets: undefined,\n  walletImages: undefined,\n  chains: undefined,\n  enableAuthMode: false,\n  enableExplorer: true,\n  explorerExcludedWalletIds: undefined,\n  explorerRecommendedWalletIds: undefined,\n  termsOfServiceUrl: undefined,\n  privacyPolicyUrl: undefined\n})\n\n// -- controller --------------------------------------------------- //\nexport const ConfigCtrl = {\n  state,\n\n  subscribe(callback: (newState: ConfigCtrlState) => void) {\n    return valtioSub(state, () => callback(state))\n  },\n\n  setConfig(config: ConfigCtrlState) {\n    EventsCtrl.initialize()\n    OptionsCtrl.setChains(config.chains)\n    OptionsCtrl.setIsAuth(Boolean(config.enableAuthMode))\n    OptionsCtrl.setIsCustomMobile(Boolean(config.mobileWallets?.length))\n    OptionsCtrl.setIsCustomDesktop(Boolean(config.desktopWallets?.length))\n\n    CoreUtil.setModalVersionInStorage()\n\n    Object.assign(state, config)\n  }\n}\n", "import { ConfigCtrl } from '../controllers/ConfigCtrl'\nimport type { ListingParams, ListingResponse } from '../types/controllerTypes'\n\n// -- Helpers -------------------------------------------------------\nconst W3M_API = 'https://explorer-api.walletconnect.com'\nconst SDK_TYPE = 'wcm'\nconst SDK_VERSION = `js-${process.env.ROLLUP_WCM_VERSION}`\n\nasync function fetchListings(endpoint: string, params: ListingParams) {\n  const allParams = { sdkType: SDK_TYPE, sdkVersion: SDK_VERSION, ...params }\n  const url = new URL(endpoint, W3M_API)\n  url.searchParams.append('projectId', ConfigCtrl.state.projectId)\n  Object.entries(allParams).forEach(([key, value]) => {\n    if (value) {\n      url.searchParams.append(key, String(value))\n    }\n  })\n  const request = await fetch(url)\n\n  return request.json() as Promise<ListingResponse>\n}\n\n// -- Utility -------------------------------------------------------\nexport const ExplorerUtil = {\n  async getDesktopListings(params: ListingParams) {\n    return fetchListings('/w3m/v1/getDesktopListings', params)\n  },\n\n  async getMobileListings(params: ListingParams) {\n    return fetchListings('/w3m/v1/getMobileListings', params)\n  },\n\n  async getInjectedListings(params: ListingParams) {\n    return fetchListings('/w3m/v1/getInjectedListings', params)\n  },\n\n  async getAllListings(params: ListingParams) {\n    return fetchListings('/w3m/v1/getAllListings', params)\n  },\n\n  getWalletImageUrl(imageId: string) {\n    return `${W3M_API}/w3m/v1/getWalletImage/${imageId}?projectId=${ConfigCtrl.state.projectId}&sdkType=${SDK_TYPE}&sdkVersion=${SDK_VERSION}`\n  },\n\n  getAssetImageUrl(imageId: string) {\n    return `${W3M_API}/w3m/v1/getAssetImage/${imageId}?projectId=${ConfigCtrl.state.projectId}&sdkType=${SDK_TYPE}&sdkVersion=${SDK_VERSION}`\n  }\n}\n", "import { proxy } from 'valtio/vanilla'\nimport type { ExplorerCtrlState, ListingParams } from '../types/controllerTypes'\nimport { CoreUtil } from '../utils/CoreUtil'\nimport { ExplorerUtil } from '../utils/ExplorerUtil'\nimport { ConfigCtrl } from './ConfigCtrl'\nimport { OptionsCtrl } from './OptionsCtrl'\n\nconst isMobile = CoreUtil.isMobile()\n\n// -- initial state ------------------------------------------------ //\nconst state = proxy<ExplorerCtrlState>({\n  wallets: { listings: [], total: 0, page: 1 },\n  search: { listings: [], total: 0, page: 1 },\n  recomendedWallets: []\n})\n\n// -- controller --------------------------------------------------- //\nexport const ExplorerCtrl = {\n  state,\n\n  async getRecomendedWallets() {\n    const { explorerRecommendedWalletIds, explorerExcludedWalletIds } = ConfigCtrl.state\n\n    // Don't fetch recomended wallets\n    if (\n      explorerRecommendedWalletIds === 'NONE' ||\n      (explorerExcludedWalletIds === 'ALL' && !explorerRecommendedWalletIds)\n    ) {\n      return state.recomendedWallets\n    }\n\n    // Fetch only recomended wallets defined in config\n    if (CoreUtil.isArray(explorerRecommendedWalletIds)) {\n      const recommendedIds = explorerRecommendedWalletIds.join(',')\n      const params = { recommendedIds }\n      const { listings } = await ExplorerUtil.getAllListings(params)\n      const listingsArr = Object.values(listings)\n      listingsArr.sort((a, b) => {\n        const aIndex = explorerRecommendedWalletIds.indexOf(a.id)\n        const bIndex = explorerRecommendedWalletIds.indexOf(b.id)\n\n        return aIndex - bIndex\n      })\n      state.recomendedWallets = listingsArr\n    }\n\n    // Fetch default recomended wallets based on user's device, options and excluded config\n    else {\n      const { chains, isAuth } = OptionsCtrl.state\n      const chainsFilter = chains?.join(',')\n      const isExcluded = CoreUtil.isArray(explorerExcludedWalletIds)\n      const params = {\n        page: 1,\n        sdks: isAuth ? 'auth_v1' : undefined,\n        entries: CoreUtil.RECOMMENDED_WALLET_AMOUNT,\n        chains: chainsFilter,\n        version: 2,\n        excludedIds: isExcluded ? explorerExcludedWalletIds.join(',') : undefined\n      }\n      const { listings } = isMobile\n        ? await ExplorerUtil.getMobileListings(params)\n        : await ExplorerUtil.getDesktopListings(params)\n      state.recomendedWallets = Object.values(listings)\n    }\n\n    return state.recomendedWallets\n  },\n\n  async getWallets(params: ListingParams) {\n    const extendedParams: ListingParams = { ...params }\n    const { explorerRecommendedWalletIds, explorerExcludedWalletIds } = ConfigCtrl.state\n    const { recomendedWallets } = state\n\n    // Don't fetch any wallets if all are excluded\n    if (explorerExcludedWalletIds === 'ALL') {\n      return state.wallets\n    }\n\n    // Don't fetch recomended wallets, as we already have these\n    if (recomendedWallets.length) {\n      extendedParams.excludedIds = recomendedWallets.map(wallet => wallet.id).join(',')\n    } else if (CoreUtil.isArray(explorerRecommendedWalletIds)) {\n      extendedParams.excludedIds = explorerRecommendedWalletIds.join(',')\n    }\n\n    // Don't fetch user defined excluded wallets & recomended wallets\n    if (CoreUtil.isArray(explorerExcludedWalletIds)) {\n      extendedParams.excludedIds = [extendedParams.excludedIds, explorerExcludedWalletIds]\n        .filter(Boolean)\n        .join(',')\n    }\n\n    // Fetch only auth wallets\n    if (OptionsCtrl.state.isAuth) {\n      extendedParams.sdks = 'auth_v1'\n    }\n\n    const { page, search } = params\n    const { listings: listingsObj, total } = isMobile\n      ? await ExplorerUtil.getMobileListings(extendedParams)\n      : await ExplorerUtil.getDesktopListings(extendedParams)\n    const listings = Object.values(listingsObj)\n    const type = search ? 'search' : 'wallets'\n    state[type] = {\n      listings: [...state[type].listings, ...listings],\n      total,\n      page: page ?? 1\n    }\n\n    return { listings, total }\n  },\n\n  getWalletImageUrl(imageId: string) {\n    return ExplorerUtil.getWalletImageUrl(imageId)\n  },\n\n  getAssetImageUrl(imageId: string) {\n    return ExplorerUtil.getAssetImageUrl(imageId)\n  },\n\n  resetSearch() {\n    state.search = { listings: [], total: 0, page: 1 }\n  }\n}\n", "import { proxy, subscribe as valtioSub } from 'valtio/vanilla'\nimport type { ModalCtrlState } from '../types/controllerTypes'\nimport { CoreUtil } from '../utils/CoreUtil'\nimport { OptionsCtrl } from './OptionsCtrl'\nimport { RouterCtrl } from './RouterCtrl'\n\n// -- types -------------------------------------------------------- //\nexport interface OpenOptions {\n  uri: string\n  chains?: string[]\n}\n\n// -- initial state ------------------------------------------------ //\nconst state = proxy<ModalCtrlState>({\n  open: false\n})\n\n// -- controller --------------------------------------------------- //\nexport const ModalCtrl = {\n  state,\n\n  subscribe(callback: (newState: ModalCtrlState) => void) {\n    return valtioSub(state, () => callback(state))\n  },\n\n  async open(options?: OpenOptions) {\n    return new Promise<void>(resolve => {\n      const { isUiLoaded, isDataLoaded } = OptionsCtrl.state\n      CoreUtil.removeWalletConnectDeepLink()\n\n      OptionsCtrl.setWalletConnectUri(options?.uri)\n      OptionsCtrl.setChains(options?.chains)\n      RouterCtrl.reset('ConnectWallet')\n\n      // Open modal if essential async data is ready\n      if (isUiLoaded && isDataLoaded) {\n        state.open = true\n        resolve()\n      }\n      // Otherwise (slow network) re-attempt open checks\n      else {\n        const interval = setInterval(() => {\n          const opts = OptionsCtrl.state\n          if (opts.isUiLoaded && opts.isDataLoaded) {\n            clearInterval(interval)\n            state.open = true\n            resolve()\n          }\n        }, 200)\n      }\n    })\n  },\n\n  close() {\n    state.open = false\n  }\n}\n", "import { proxy, subscribe as valtioSub } from 'valtio/vanilla'\nimport type { ThemeCtrlState } from '../types/controllerTypes'\n\n// -- initial state ------------------------------------------------ //\nfunction isDarkMode() {\n  return typeof matchMedia !== 'undefined' && matchMedia('(prefers-color-scheme: dark)').matches\n}\n\nconst state = proxy<ThemeCtrlState>({\n  themeMode: isDarkMode() ? 'dark' : 'light'\n})\n\n// -- controller --------------------------------------------------- //\nexport const ThemeCtrl = {\n  state,\n\n  subscribe(callback: (newState: ThemeCtrlState) => void) {\n    return valtioSub(state, () => callback(state))\n  },\n\n  setThemeConfig(theme: ThemeCtrlState) {\n    const { themeMode, themeVariables } = theme\n\n    if (themeMode) {\n      state.themeMode = themeMode\n    }\n\n    if (themeVariables) {\n      state.themeVariables = { ...themeVariables }\n    }\n  }\n}\n", "import { proxy, subscribe as valtioSub } from 'valtio/vanilla'\nimport type { ToastCtrlState } from '../types/controllerTypes'\n\n// -- initial state ------------------------------------------------ //\nconst state = proxy<ToastCtrlState>({\n  open: false,\n  message: '',\n  variant: 'success'\n})\n\n// -- controller --------------------------------------------------- //\nexport const ToastCtrl = {\n  state,\n\n  subscribe(callback: (newState: ToastCtrlState) => void) {\n    return valtioSub(state, () => callback(state))\n  },\n\n  openToast(message: ToastCtrlState['message'], variant: ToastCtrlState['variant']) {\n    state.open = true\n    state.message = message\n    state.variant = variant\n  },\n\n  closeToast() {\n    state.open = false\n  }\n}\n"], "mappings": ";AACA,IAAMA,IAAoBC,OAAAA;AAA1B,IACMC,IAAsBD,OAAAA;AAsB5B,IAAME,IAAWC,OAAOC;AAAxB,IAEMC,IAAiB,oBAAIC;AAF3B,IAKMC,IAAsBC,CAAAA,OAC1BA,OAAQH,EAAeI,IAAID,EAAAA,IACvBH,EAAeK,IAAIF,EAAAA,IAClBN,EAASM,EAAAA,MAASL,OAAOQ,aAAaT,EAASM,EAAAA,MAASI,MAAMD;AARrE,IAkWaE,IAAmBC,CAAAA,OAC1BC,EAAgBD,EAAAA,KACVA,GAAsCE,CAAAA,KAGlD;AAvWA,IAqYaC,IAAcA,CAACH,IAAaI,KAAAA,SAAO;AAC9CC,IAAeC,IAAIN,IAAKI,EAAAA;AAAI;;;AC5Z9B,IAAM,WAAW,CAAC,MAAM,OAAO,MAAM,YAAY,MAAM;AACvD,IAAM,gBAAgC,oBAAI,QAAQ;AAClD,IAAM,SAAyB,oBAAI,QAAQ;AAC3C,IAAM,qBAAqB,CAAC,WAAW,OAAO,IAAI,WAAW,CAAC,QAAQ,YAAY,IAAI,MAAM,QAAQ,OAAO,GAAG,WAAW,CAAC,MAAM,SAAS,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,MAAM,QAAQ,CAAC,KAAK,EAAE,OAAO,YAAY,OAAO,EAAE,aAAa,YAAY,EAAE,aAAa,YAAY,EAAE,aAAa,UAAU,EAAE,aAAa,WAAW,EAAE,aAAa,SAAS,EAAE,aAAa,WAAW,EAAE,aAAa,WAAW,EAAE,aAAa,cAAc,uBAAuB,CAAC,YAAY;AACtc,UAAQ,QAAQ,QAAQ;AAAA,IACtB,KAAK;AACH,aAAO,QAAQ;AAAA,IACjB,KAAK;AACH,YAAM,QAAQ;AAAA,IAChB;AACE,YAAM;AAAA,EACV;AACF,GAAG,YAA4B,oBAAI,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,SAAS,gBAAgB,yBAAyB;AACxH,QAAM,QAAQ,UAAU,IAAI,MAAM;AAClC,OAAK,SAAS,OAAO,SAAS,MAAM,CAAC,OAAO,SAAS;AACnD,WAAO,MAAM,CAAC;AAAA,EAChB;AACA,QAAM,OAAO,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,OAAO,OAAO,OAAO,eAAe,MAAM,CAAC;AACrF,IAAY,MAAM,IAAI;AACtB,YAAU,IAAI,QAAQ,CAAC,SAAS,IAAI,CAAC;AACrC,UAAQ,QAAQ,MAAM,EAAE,QAAQ,CAAC,QAAQ;AACvC,QAAI,OAAO,yBAAyB,MAAM,GAAG,GAAG;AAC9C;AAAA,IACF;AACA,UAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AACrC,UAAM,OAAO;AAAA,MACX;AAAA,MACA,YAAY;AAAA;AAAA;AAAA,MAGZ,cAAc;AAAA,IAChB;AACA,QAAI,OAAO,IAAI,KAAK,GAAG;AACrB,QAAY,OAAO,KAAK;AAAA,IAC1B,WAAW,iBAAiB,SAAS;AACnC,aAAO,KAAK;AACZ,WAAK,MAAM,MAAM,cAAc,KAAK;AAAA,IACtC,WAAW,cAAc,IAAI,KAAK,GAAG;AACnC,YAAM,CAAC,SAAS,aAAa,IAAI,cAAc;AAAA,QAC7C;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,QACX;AAAA,QACA,cAAc;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,WAAO,eAAe,MAAM,KAAK,IAAI;AAAA,EACvC,CAAC;AACD,SAAO,OAAO,kBAAkB,IAAI;AACtC,GAAG,aAA6B,oBAAI,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,kBAAkB;AACxG,MAAI,CAAC,SAAS,aAAa,GAAG;AAC5B,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACnC;AACA,QAAM,QAAQ,WAAW,IAAI,aAAa;AAC1C,MAAI,OAAO;AACT,WAAO;AAAA,EACT;AACA,MAAI,UAAU,cAAc,CAAC;AAC7B,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,eAAe,CAAC,IAAI,cAAc,EAAE,cAAc,CAAC,MAAM;AAC7D,QAAI,YAAY,aAAa;AAC3B,gBAAU;AACV,gBAAU,QAAQ,CAAC,aAAa,SAAS,IAAI,WAAW,CAAC;AAAA,IAC3D;AAAA,EACF;AACA,MAAI,eAAe,cAAc,CAAC;AAClC,QAAM,gBAAgB,CAAC,mBAAmB,EAAE,cAAc,CAAC,MAAM;AAC/D,QAAI,iBAAiB,oBAAoB,CAAC,UAAU,MAAM;AACxD,qBAAe;AACf,sBAAgB,QAAQ,CAAC,CAAC,cAAc,MAAM;AAC5C,cAAM,cAAc,eAAe,CAAC,EAAE,gBAAgB;AACtD,YAAI,cAAc,SAAS;AACzB,oBAAU;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,QAAM,qBAAqB,CAAC,SAAS,CAAC,IAAI,gBAAgB;AACxD,UAAM,QAAQ,CAAC,GAAG,EAAE;AACpB,UAAM,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;AAC7B,iBAAa,OAAO,WAAW;AAAA,EACjC;AACA,QAAM,kBAAkC,oBAAI,IAAI;AAChD,QAAM,kBAAkB,CAAC,MAAM,mBAAmB;AAChD,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,gBAAgB,IAAI,IAAI,GAAG;AACnG,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAChD;AACA,QAAI,UAAU,MAAM;AAClB,YAAM,SAAS,eAAe,CAAC,EAAE,mBAAmB,IAAI,CAAC;AACzD,sBAAgB,IAAI,MAAM,CAAC,gBAAgB,MAAM,CAAC;AAAA,IACpD,OAAO;AACL,sBAAgB,IAAI,MAAM,CAAC,cAAc,CAAC;AAAA,IAC5C;AAAA,EACF;AACA,QAAM,qBAAqB,CAAC,SAAS;AACnC,QAAI;AACJ,UAAM,QAAQ,gBAAgB,IAAI,IAAI;AACtC,QAAI,OAAO;AACT,sBAAgB,OAAO,IAAI;AAC3B,OAAC,KAAK,MAAM,CAAC,MAAM,OAAO,SAAS,GAAG,KAAK,KAAK;AAAA,IAClD;AAAA,EACF;AACA,QAAM,cAAc,CAAC,aAAa;AAChC,cAAU,IAAI,QAAQ;AACtB,QAAI,UAAU,SAAS,GAAG;AACxB,sBAAgB,QAAQ,CAAC,CAAC,gBAAgB,UAAU,GAAG,SAAS;AAC9D,aAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,YAAY;AACpF,gBAAM,IAAI,MAAM,uBAAuB;AAAA,QACzC;AACA,cAAM,SAAS,eAAe,CAAC,EAAE,mBAAmB,IAAI,CAAC;AACzD,wBAAgB,IAAI,MAAM,CAAC,gBAAgB,MAAM,CAAC;AAAA,MACpD,CAAC;AAAA,IACH;AACA,UAAM,iBAAiB,MAAM;AAC3B,gBAAU,OAAO,QAAQ;AACzB,UAAI,UAAU,SAAS,GAAG;AACxB,wBAAgB,QAAQ,CAAC,CAAC,gBAAgB,MAAM,GAAG,SAAS;AAC1D,cAAI,QAAQ;AACV,mBAAO;AACP,4BAAgB,IAAI,MAAM,CAAC,cAAc,CAAC;AAAA,UAC5C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa,MAAM,QAAQ,aAAa,IAAI,CAAC,IAAI,OAAO,OAAO,OAAO,eAAe,aAAa,CAAC;AACzG,QAAM,UAAU;AAAA,IACd,eAAe,QAAQ,MAAM;AAC3B,YAAM,YAAY,QAAQ,IAAI,QAAQ,IAAI;AAC1C,yBAAmB,IAAI;AACvB,YAAM,UAAU,QAAQ,eAAe,QAAQ,IAAI;AACnD,UAAI,SAAS;AACX,qBAAa,CAAC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACT;AAAA,IACA,IAAI,QAAQ,MAAM,OAAO,UAAU;AACjC,YAAM,eAAe,QAAQ,IAAI,QAAQ,IAAI;AAC7C,YAAM,YAAY,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AACpD,UAAI,iBAAiB,SAAS,WAAW,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,SAAS,WAAW,WAAW,IAAI,KAAK,CAAC,IAAI;AACvH,eAAO;AAAA,MACT;AACA,yBAAmB,IAAI;AACvB,UAAI,SAAS,KAAK,GAAG;AACnB,gBAAQ,EAAa,KAAK,KAAK;AAAA,MACjC;AACA,UAAI,YAAY;AAChB,UAAI,iBAAiB,SAAS;AAC5B,cAAM,KAAK,CAAC,MAAM;AAChB,gBAAM,SAAS;AACf,gBAAM,QAAQ;AACd,uBAAa,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;AAAA,QACrC,CAAC,EAAE,MAAM,CAACG,OAAM;AACd,gBAAM,SAAS;AACf,gBAAM,SAASA;AACf,uBAAa,CAAC,UAAU,CAAC,IAAI,GAAGA,EAAC,CAAC;AAAA,QACpC,CAAC;AAAA,MACH,OAAO;AACL,YAAI,CAAC,cAAc,IAAI,KAAK,KAAK,SAAS,KAAK,GAAG;AAChD,sBAAY,cAAc,KAAK;AAAA,QACjC;AACA,cAAM,kBAAkB,CAAC,OAAO,IAAI,SAAS,KAAK,cAAc,IAAI,SAAS;AAC7E,YAAI,iBAAiB;AACnB,0BAAgB,MAAM,eAAe;AAAA,QACvC;AAAA,MACF;AACA,cAAQ,IAAI,QAAQ,MAAM,WAAW,QAAQ;AAC7C,mBAAa,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,SAAS,CAAC;AAC9C,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,cAAc,SAAS,YAAY,OAAO;AAChD,aAAW,IAAI,eAAe,WAAW;AACzC,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,gBAAc,IAAI,aAAa,UAAU;AACzC,UAAQ,QAAQ,aAAa,EAAE,QAAQ,CAAC,QAAQ;AAC9C,UAAM,OAAO,OAAO;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AACA,QAAI,WAAW,MAAM;AACnB,kBAAY,GAAG,IAAI,cAAc,GAAG;AACpC,aAAO,KAAK;AACZ,aAAO,KAAK;AAAA,IACd;AACA,WAAO,eAAe,YAAY,KAAK,IAAI;AAAA,EAC7C,CAAC;AACD,SAAO;AACT,MAAM;AAAA;AAAA,EAEJ;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,CAAC,oBAAoB,IAAI,mBAAmB;AAClD,SAAS,MAAM,gBAAgB,CAAC,GAAG;AACjC,SAAO,qBAAqB,aAAa;AAC3C;AAKA,SAAS,UAAU,aAAa,UAAU,cAAc;AACtD,QAAM,aAAa,cAAc,IAAI,WAAW;AAChD,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,CAAC,YAAY;AACrF,YAAQ,KAAK,yBAAyB;AAAA,EACxC;AACA,MAAI;AACJ,QAAM,MAAM,CAAC;AACb,QAAM,cAAc,WAAW,CAAC;AAChC,MAAI,mBAAmB;AACvB,QAAM,WAAW,CAAC,OAAO;AACvB,QAAI,KAAK,EAAE;AACX,QAAI,cAAc;AAChB,eAAS,IAAI,OAAO,CAAC,CAAC;AACtB;AAAA,IACF;AACA,QAAI,CAAC,SAAS;AACZ,gBAAU,QAAQ,QAAQ,EAAE,KAAK,MAAM;AACrC,kBAAU;AACV,YAAI,kBAAkB;AACpB,mBAAS,IAAI,OAAO,CAAC,CAAC;AAAA,QACxB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,iBAAiB,YAAY,QAAQ;AAC3C,qBAAmB;AACnB,SAAO,MAAM;AACX,uBAAmB;AACnB,mBAAe;AAAA,EACjB;AACF;AACA,SAAS,SAAS,aAAa,eAAe;AAC5C,QAAM,aAAa,cAAc,IAAI,WAAW;AAChD,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,CAAC,YAAY;AACrF,YAAQ,KAAK,yBAAyB;AAAA,EACxC;AACA,QAAM,CAAC,QAAQ,eAAe,cAAc,IAAI;AAChD,SAAO,eAAe,QAAQ,cAAc,GAAG,aAAa;AAC9D;;;AChQA,IAAMC,IAAQC,MAAuB,EACnC,SAAS,CAAC,eAAe,GACzB,MAAM,iBACN,MAAM,OACR,CAAC;AAJD,IAOaC,IAAa,EACxB,OAAAF,GAEA,UAAUG,IAA+C;AACvD,SAAOC,UAAUJ,GAAO,MAAMG,GAASH,CAAK,CAAC;AAC/C,GAEA,KAAKK,IAA+BC,IAAgC;AAC9DD,EAAAA,OAASL,EAAM,SACjBA,EAAM,OAAOK,IACTC,OACFN,EAAM,OAAOM,KAEfN,EAAM,QAAQ,KAAKK,EAAI;AAE3B,GAEA,MAAMA,IAA+B;AACnCL,IAAM,OAAOK,IACbL,EAAM,UAAU,CAACK,EAAI;AACvB,GAEA,QAAQA,IAA+B;AACjCL,IAAM,QAAQ,SAAS,MACzBA,EAAM,QAAQA,EAAM,QAAQ,SAAS,CAAC,IAAIK,IAC1CL,EAAM,OAAOK;AAEjB,GAEA,SAAS;AACP,MAAIL,EAAM,QAAQ,SAAS,GAAG;AAC5BA,MAAM,QAAQ,IACd;AAAA,UAAM,CAACO,EAAI,IAAIP,EAAM,QAAQ,MAAM,EAAE;AACrCA,MAAM,OAAOO;EAAAA;AAEjB,GAEA,QAAQD,IAA+B;AACrCN,IAAM,OAAOM;AACf,EACF;AA/CA,ICFaE,IAAW,EACtB,+BAA+B,iCAE/B,aAAa,eAEb,2BAA2B,GAE3B,WAAW;AACT,SAAI,OAAO,SAAW,MACb,QACL,OAAO,WAAW,kBAAkB,EAAE,WACpC,wDAAwD,KAAK,UAAU,SAAS,CACpF,IAGK;AACT,GAEA,YAAY;AACV,SAAOA,EAAS,SAAA,KAAc,UAAU,UAAU,YAAY,EAAE,SAAS,SAAS;AACpF,GAEA,QAAQ;AACN,QAAMC,KAAK,UAAU,UAAU,YAAA;AAE/B,SAAOD,EAAS,SAAS,MAAMC,GAAG,SAAS,QAAQ,KAAKA,GAAG,SAAS,MAAM;AAC5E,GAEA,UAAUC,IAAa;AACrB,SAAOA,GAAI,WAAW,SAAS,KAAKA,GAAI,WAAW,UAAU;AAC/D,GAEA,QAAWJ,IAA6B;AACtC,SAAO,MAAM,QAAQA,EAAI,KAAKA,GAAK,SAAS;AAC9C,GAEA,gBAAgBK,IAAgBC,IAAeC,IAAsB;AACnE,MAAIL,EAAS,UAAUG,EAAM;AAC3B,WAAO,KAAK,mBAAmBA,IAAQC,IAAOC,EAAI;AAEpD,MAAIC,IAAaH;AACZG,IAAW,SAAS,KAAK,MAC5BA,IAAaH,GAAO,WAAW,KAAK,EAAE,EAAE,WAAW,KAAK,EAAE,GAC1DG,IAAa,GAAGA,CAAAA,QAEbA,EAAW,SAAS,GAAG,MAC1BA,IAAa,GAAGA,CAAAA,MAElB,KAAK,yBAAyBA,GAAYD,EAAI;AAC9C,QAAME,IAAe,mBAAmBH,EAAK;AAE7C,SAAO,GAAGE,CAAAA,UAAoBC,CAAAA;AAChC,GAEA,mBAAmBJ,IAAgBC,IAAeC,IAAsB;AACtE,MAAI,CAACL,EAAS,UAAUG,EAAM;AAC5B,WAAO,KAAK,gBAAgBA,IAAQC,IAAOC,EAAI;AAEjD,MAAIC,IAAaH;AACZG,IAAW,SAAS,GAAG,MAC1BA,IAAa,GAAGA,CAAAA,MAElB,KAAK,yBAAyBA,GAAYD,EAAI;AAC9C,QAAME,IAAe,mBAAmBH,EAAK;AAE7C,SAAO,GAAGE,CAAAA,UAAoBC,CAAAA;AAChC,GAEA,MAAM,KAAKC,IAAqB;AAC9B,SAAO,IAAI,QAAQC,CAAAA,OAAW;AAC5B,eAAWA,IAASD,EAAW;EACjC,CAAC;AACH,GAEA,SAASE,IAAcC,IAA4B;AACjD,SAAO,KAAKD,IAAMC,IAAQ,qBAAqB;AACjD,GAEA,yBAAyBD,IAAcL,IAAc;AACnD,MAAI;AACF,iBAAa,QAAQL,EAAS,+BAA+B,KAAK,UAAU,EAAE,MAAAU,IAAM,MAAAL,GAAK,CAAC,CAAC;EAC7F,QAAA;AACE,YAAQ,KAAK,uCAAuC;EACtD;AACF,GAEA,gCAAgCD,IAAe;AAC7C,MAAI;AACF,UAAM,CAACM,EAAI,IAAIN,GAAM,MAAM,GAAG;AAC9B,iBAAa,QACXJ,EAAS,+BACT,KAAK,UAAU,EAAE,MAAAU,IAAM,MAAM,UAAU,CAAC,CAC1C;EACF,QAAA;AACE,YAAQ,KAAK,+CAA+C;EAC9D;AACF,GAEA,8BAA8B;AAC5B,MAAI;AACF,iBAAa,WAAWV,EAAS,6BAA6B;EAChE,QAAQ;AACN,YAAQ,KAAK,0CAA0C;EACzD;AACF,GAEA,2BAA2B;AACzB,MAAI;AACE,WAAO,eAAiB,OAC1B,aAAa,QAAQA,EAAS,aAAa,OAA2C;EAE1F,QAAQ;AACN,YAAQ,KAAK,4CAA4C;EAC3D;AACF,GAEA,sBAAsB;AAtHxB,MAAAY;AAuHI,QAAMC,MAAaD,KAAAlB,EAAW,MAAM,SAAjB,OAAA,SAAAkB,GAAuB;AAC1C,MAAI,CAACC;AACH,UAAM,IAAI,MAAM,4BAA4B;AAG9C,SAAOA;AACT,EACF;AD1HA,IEAMC,IACJ,OAAO,WAAa,QACnB,SAAS,SAAS,SAAS,WAAW,KAAK,SAAS,SAAS,SAAS,OAAO;AFFhF,IEKMtB,IAAQC,MAAuB,EACnC,SAASqB,GACT,eAAe,IACf,QAAQ,CAAA,GACR,mBAAmB,OACrB,CAAC;AFVD,IEaaC,IAAa,EACxB,OAAAvB,GAEA,UAAUG,IAA0C;AAClD,SAAOC,UAAUJ,EAAM,QAAQ,MAAMG,GAASqB,SAASxB,EAAM,OAAOA,EAAM,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;AAChG,GAEA,aAAa;AACPA,IAAM,WAAW,QAAO,UAAQ,OAAA,SAAA,OAAA,cAAe,QACjDA,EAAM,gBAAgB,OAAO,WAAA;AAEjC,GAEA,qBAAqByB,IAAyD;AAC5EzB,IAAM,oBAAoByB;AAC5B,GAEA,MAAMnB,IAAsB;AAC1B,MAAIN,EAAM,SAAS;AACjB,UAAM0B,KAAQ,EACZ,MAAM,SACN,MAAMpB,GAAK,MACX,eAAeN,EAAM,eACrB,WAAW,KAAK,IAChB,GAAA,MAAAM,GACF;AACAN,MAAM,OAAO,KAAK0B,EAAK;EAAA;AAE3B,GAEA,MAAMpB,IAAsB;AAC1B,MAAIN,EAAM,SAAS;AACjB,UAAM0B,KAAQ,EACZ,MAAM,SACN,MAAMpB,GAAK,MACX,eAAeN,EAAM,eACrB,WAAW,KAAK,IAChB,GAAA,MAAAM,GACF;AACAN,MAAM,OAAO,KAAK0B,EAAK;EAAA;AAE3B,GAEA,KAAKpB,IAAsB;AACzB,MAAIN,EAAM,SAAS;AACjB,UAAM0B,KAAQ,EACZ,MAAM,QACN,MAAMpB,GAAK,MACX,eAAeN,EAAM,eACrB,WAAW,KAAK,IAAA,GAChB,MAAAM,GACF;AACAN,MAAM,OAAO,KAAK0B,EAAK;EAAA;AAE3B,EACF;AFpEA,IGAM1B,KAAQC,MAAwB,EACpC,QAAQ,QACR,kBAAkB,QAClB,QAAQ,OACR,iBAAiB,OACjB,gBAAgB,OAChB,cAAc,OACd,YAAY,MACd,CAAC;AHRD,IGWa0B,IAAc,EACzB,OAAA3B,IAEA,UAAUG,IAAgD;AACxD,SAAOC,UAAUJ,IAAO,MAAMG,GAASH,EAAK,CAAC;AAC/C,GAEA,UAAU4B,IAAoC;AAC5C5B,EAAAA,GAAM,SAAS4B;AACjB,GAEA,oBAAoBC,IAAwD;AAC1E7B,EAAAA,GAAM,mBAAmB6B;AAC3B,GAEA,mBAAmBC,IAAsD;AACvE9B,EAAAA,GAAM,kBAAkB8B;AAC1B,GAEA,kBAAkBC,IAAoD;AACpE/B,EAAAA,GAAM,iBAAiB+B;AACzB,GAEA,gBAAgBC,IAAgD;AAC9DhC,EAAAA,GAAM,eAAegC;AACvB,GAEA,cAAcC,IAA4C;AACxDjC,EAAAA,GAAM,aAAaiC;AACrB,GAEA,UAAUC,IAAoC;AAC5ClC,EAAAA,GAAM,SAASkC;AACjB,EACF;AH7CA,IIEMlC,IAAQC,MAAuB,EACnC,WAAW,IACX,eAAe,QACf,gBAAgB,QAChB,cAAc,QACd,QAAQ,QACR,gBAAgB,OAChB,gBAAgB,MAChB,2BAA2B,QAC3B,8BAA8B,QAC9B,mBAAmB,QACnB,kBAAkB,OACpB,CAAC;AJdD,IIiBakC,KAAa,EACxB,OAAAnC,GAEA,UAAUG,IAA+C;AACvD,SAAOC,UAAUJ,GAAO,MAAMG,GAASH,CAAK,CAAC;AAC/C,GAEA,UAAUoC,IAAyB;AA5BrC,MAAAhB,IAAAiB;AA6BId,IAAW,WAAA,GACXI,EAAY,UAAUS,GAAO,MAAM,GACnCT,EAAY,UAAU,QAAQS,GAAO,cAAc,CAAC,GACpDT,EAAY,kBAAkB,SAAQP,KAAAgB,GAAO,kBAAP,OAAA,SAAAhB,GAAsB,MAAM,CAAC,GACnEO,EAAY,mBAAmB,SAAQU,KAAAD,GAAO,mBAAP,OAAA,SAAAC,GAAuB,MAAM,CAAC,GAErE7B,EAAS,yBAAA,GAET,OAAO,OAAOR,GAAOoC,EAAM;AAC7B,EACF;ACvCA,IAAAE,IAAA,OAAA;AAAA,IAAAC,IAAA,OAAA;AAAA,IAAAC,IAAA,OAAA,UAAA;AAAA,IAAAC,IAAA,OAAA,UAAA;AAAA,IAAAC,IAAA,CAAAC,IAAAC,IAAAC,OAAAD,MAAAD,KAAAL,EAAAK,IAAAC,IAAA,EAAA,YAAA,MAAA,cAAA,MAAA,UAAA,MAAA,OAAAC,GAAA,CAAA,IAAAF,GAAAC,EAAA,IAAAC;AAAA,IAAAC,IAAA,CAAAH,IAAAC,OAAA;AAAA,WAAAC,MAAAD,OAAAA,KAAA,CAAA;AAAAJ,MAAA,KAAAI,IAAAC,EAAA,KAAAH,EAAAC,IAAAE,IAAAD,GAAAC,EAAA,CAAA;AAAA,MAAAN;AAAA,aAAAM,MAAAN,EAAAK,EAAA;AAAAH,QAAA,KAAAG,IAAAC,EAAA,KAAAH,EAAAC,IAAAE,IAAAD,GAAAC,EAAA,CAAA;AAAA,SAAAF;AAAA;AAIA,IAAMI,IAAU;AAAhB,IACMC,IAAW;AADjB,IAEMC,IAAc;AAEpB,eAAeC,EAAcC,IAAkBC,IAAuB;AACpE,QAAMC,KAAYC,EAAA,EAAE,SAASN,GAAU,YAAYC,EAAgBG,GAAAA,EAAAA,GAC7D1C,IAAM,IAAI,IAAIyC,IAAUJ,CAAO;AACrC,SAAArC,EAAI,aAAa,OAAO,aAAayB,GAAW,MAAM,SAAS,GAC/D,OAAO,QAAQkB,EAAS,EAAE,QAAQ,CAAC,CAACE,GAAKC,EAAK,MAAM;AAC9CA,IAAAA,MACF9C,EAAI,aAAa,OAAO6C,GAAK,OAAOC,EAAK,CAAC;EAE9C,CAAC,IACe,MAAM,MAAM9C,CAAG,GAEhB,KAAA;AACjB;AAGa,IAAA+C,IAAe,EAC1B,MAAM,mBAAmBL,IAAuB;AAC9C,SAAOF,EAAc,8BAA8BE,EAAM;AAC3D,GAEA,MAAM,kBAAkBA,IAAuB;AAC7C,SAAOF,EAAc,6BAA6BE,EAAM;AAC1D,GAEA,MAAM,oBAAoBA,IAAuB;AAC/C,SAAOF,EAAc,+BAA+BE,EAAM;AAC5D,GAEA,MAAM,eAAeA,IAAuB;AAC1C,SAAOF,EAAc,0BAA0BE,EAAM;AACvD,GAEA,kBAAkBM,IAAiB;AACjC,SAAO,GAAGX,CAAAA,0BAAiCW,EAAAA,cAAqBvB,GAAW,MAAM,SAAA,YAAqBa,CAAAA,eAAuBC,CAAAA;AAC/H,GAEA,iBAAiBS,IAAiB;AAChC,SAAO,GAAGX,CAAAA,yBAAgCW,EAAAA,cAAqBvB,GAAW,MAAM,SAAA,YAAqBa,CAAAA,eAAuBC,CAAAA;AAC9H,EACF;AC/CA,IAAAU,IAAA,OAAA;AAAA,IAAAC,IAAA,OAAA;AAAA,IAAAC,IAAA,OAAA,UAAA;AAAA,IAAAC,IAAA,OAAA,UAAA;AAAA,IAAAC,IAAA,CAAApB,IAAAE,IAAAD,OAAAC,MAAAF,KAAAgB,EAAAhB,IAAAE,IAAA,EAAA,YAAA,MAAA,cAAA,MAAA,UAAA,MAAA,OAAAD,GAAA,CAAA,IAAAD,GAAAE,EAAA,IAAAD;AAAA,IAAAoB,IAAA,CAAArB,IAAAE,OAAA;AAAA,WAAAD,MAAAC,OAAAA,KAAA,CAAA;AAAAgB,MAAA,KAAAhB,IAAAD,EAAA,KAAAmB,EAAApB,IAAAC,IAAAC,GAAAD,EAAA,CAAA;AAAA,MAAAgB;AAAA,aAAAhB,MAAAgB,EAAAf,EAAA;AAAAiB,QAAA,KAAAjB,IAAAD,EAAA,KAAAmB,EAAApB,IAAAC,IAAAC,GAAAD,EAAA,CAAA;AAAA,SAAAD;AAAA;AAOA,IAAMsB,IAAWzD,EAAS,SAAS;AAAnC,IAGMR,IAAQC,MAAyB,EACrC,SAAS,EAAE,UAAU,CAAI,GAAA,OAAO,GAAG,MAAM,EAAE,GAC3C,QAAQ,EAAE,UAAU,CAAA,GAAI,OAAO,GAAG,MAAM,EAAE,GAC1C,mBAAmB,CACrB,EAAA,CAAC;AAPD,IAUaiE,KAAe,EAC1B,OAAAlE,GAEA,MAAM,uBAAuB;AAC3B,QAAM,EAAE,8BAAAmE,IAA8B,2BAAAC,GAA0B,IAAIjC,GAAW;AAG/E,MACEgC,OAAiC,UAChCC,OAA8B,SAAS,CAACD;AAEzC,WAAOnE,EAAM;AAIf,MAAIQ,EAAS,QAAQ2D,EAA4B,GAAG;AAElD,UAAMf,KAAS,EAAE,gBADMe,GAA6B,KAAK,GAAG,EAC5B,GAC1B,EAAE,UAAAE,EAAS,IAAI,MAAMZ,EAAa,eAAeL,EAAM,GACvDkB,IAAc,OAAO,OAAOD,CAAQ;AAC1CC,MAAY,KAAK,CAACC,IAAGC,MAAM;AACzB,YAAMC,IAASN,GAA6B,QAAQI,GAAE,EAAE,GAClDG,IAASP,GAA6B,QAAQK,EAAE,EAAE;AAExD,aAAOC,IAASC;IAClB,CAAC,GACD1E,EAAM,oBAAoBsE;EAAAA,OAIvB;AACH,UAAM,EAAE,QAAA1C,IAAQ,QAAAM,EAAO,IAAIP,EAAY,OACjCgD,IAAe/C,MAAAA,gBAAAA,GAAQ,KAAK,MAC5BgD,KAAapE,EAAS,QAAQ4D,EAAyB,GACvDhB,IAAS,EACb,MAAM,GACN,MAAMlB,IAAS,YAAY,QAC3B,SAAS1B,EAAS,2BAClB,QAAQmE,GACR,SAAS,GACT,aAAaC,KAAaR,GAA0B,KAAK,GAAG,IAAI,OAClE,GACM,EAAE,UAAAC,EAAS,IAAIJ,IACjB,MAAMR,EAAa,kBAAkBL,CAAM,IAC3C,MAAMK,EAAa,mBAAmBL,CAAM;AAChDpD,MAAM,oBAAoB,OAAO,OAAOqE,CAAQ;EAAA;AAGlD,SAAOrE,EAAM;AACf,GAEA,MAAM,WAAWoD,IAAuB;AACtC,QAAMyB,KAAgCvB,EAAA,CAAA,GAAKF,EAAAA,GACrC,EAAE,8BAAAe,IAA8B,2BAAAC,EAA0B,IAAIjC,GAAW,OACzE,EAAE,mBAAA2C,EAAkB,IAAI9E;AAG9B,MAAIoE,MAA8B;AAChC,WAAOpE,EAAM;AAIX8E,IAAkB,SACpBD,GAAe,cAAcC,EAAkB,IAAIC,OAAUA,EAAO,EAAE,EAAE,KAAK,GAAG,IACvEvE,EAAS,QAAQ2D,EAA4B,MACtDU,GAAe,cAAcV,GAA6B,KAAK,GAAG,IAIhE3D,EAAS,QAAQ4D,CAAyB,MAC5CS,GAAe,cAAc,CAACA,GAAe,aAAaT,CAAyB,EAChF,OAAO,OAAO,EACd,KAAK,GAAG,IAITzC,EAAY,MAAM,WACpBkD,GAAe,OAAO;AAGxB,QAAM,EAAE,MAAAG,IAAM,QAAAC,EAAO,IAAI7B,IACnB,EAAE,UAAU8B,GAAa,OAAAC,EAAM,IAAIlB,IACrC,MAAMR,EAAa,kBAAkBoB,EAAc,IACnD,MAAMpB,EAAa,mBAAmBoB,EAAc,GAClDR,IAAW,OAAO,OAAOa,CAAW,GACpCE,IAAOH,IAAS,WAAW;AACjC,SAAAjF,EAAMoF,CAAI,IAAI,EACZ,UAAU,CAAC,GAAGpF,EAAMoF,CAAI,EAAE,UAAU,GAAGf,CAAQ,GAC/C,OAAAc,GACA,MAAMH,MAAQ,EAChB,GAEO,EAAE,UAAAX,GAAU,OAAAc,EAAM;AAC3B,GAEA,kBAAkBzB,IAAiB;AACjC,SAAOD,EAAa,kBAAkBC,EAAO;AAC/C,GAEA,iBAAiBA,IAAiB;AAChC,SAAOD,EAAa,iBAAiBC,EAAO;AAC9C,GAEA,cAAc;AACZ1D,IAAM,SAAS,EAAE,UAAU,CAAA,GAAI,OAAO,GAAG,MAAM,EAAE;AACnD,EACF;AApHA,ICMMA,IAAQC,MAAsB,EAClC,MAAM,MACR,CAAC;ADRD,ICWaoF,KAAY,EACvB,OAAArF,GAEA,UAAUG,IAA8C;AACtD,SAAOC,UAAUJ,GAAO,MAAMG,GAASH,CAAK,CAAC;AAC/C,GAEA,MAAM,KAAKsF,IAAuB;AAChC,SAAO,IAAI,QAAcrE,CAAAA,OAAW;AAClC,UAAM,EAAE,YAAAgB,IAAY,cAAAD,EAAa,IAAIL,EAAY;AAQjD,QAPAnB,EAAS,4BAAA,GAETmB,EAAY,oBAAoB2D,MAAAA,gBAAAA,GAAS,GAAG,GAC5C3D,EAAY,UAAU2D,MAAAA,gBAAAA,GAAS,MAAM,GACrCpF,EAAW,MAAM,eAAe,GAG5B+B,MAAcD;AAChBhC,QAAM,OAAO,MACbiB,GAGG;SAAA;AACH,YAAMsE,IAAW,YAAY,MAAM;AACjC,cAAMC,KAAO7D,EAAY;AACrB6D,QAAAA,GAAK,cAAcA,GAAK,iBAC1B,cAAcD,CAAQ,GACtBvF,EAAM,OAAO,MACbiB,GAAAA;MAEJ,GAAG,GAAG;IAAA;EAEV,CAAC;AACH,GAEA,QAAQ;AACNjB,IAAM,OAAO;AACf,EACF;ACxDA,IAAAyF,IAAA,OAAA;AAAA,IAAAlB,IAAA,OAAA;AAAA,IAAAV,IAAA,OAAA,UAAA;AAAA,IAAA6B,IAAA,OAAA,UAAA;AAAA,IAAAhD,IAAA,CAAAiD,IAAA9C,IAAAF,OAAAE,MAAA8C,KAAAF,EAAAE,IAAA9C,IAAA,EAAA,YAAA,MAAA,cAAA,MAAA,UAAA,MAAA,OAAAF,GAAA,CAAA,IAAAgD,GAAA9C,EAAA,IAAAF;AAAA,IAAAC,IAAA,CAAA+C,IAAA9C,OAAA;AAAA,WAAAF,MAAAE,OAAAA,KAAA,CAAA;AAAAgB,MAAA,KAAAhB,IAAAF,EAAA,KAAAD,EAAAiD,IAAAhD,IAAAE,GAAAF,EAAA,CAAA;AAAA,MAAA4B;AAAA,aAAA5B,MAAA4B,EAAA1B,EAAA;AAAA6C,QAAA,KAAA7C,IAAAF,EAAA,KAAAD,EAAAiD,IAAAhD,IAAAE,GAAAF,EAAA,CAAA;AAAA,SAAAgD;AAAA;AAIA,SAASC,IAAa;AACpB,SAAO,OAAO,aAAe,OAAe,WAAW,8BAA8B,EAAE;AACzF;AAEA,IAAM5F,IAAQC,MAAsB,EAClC,WAAW2F,EAAW,IAAI,SAAS,QACrC,CAAC;AAFD,IAKaC,KAAY,EACvB,OAAA7F,GAEA,UAAUG,IAA8C;AACtD,SAAOC,UAAUJ,GAAO,MAAMG,GAASH,CAAK,CAAC;AAC/C,GAEA,eAAe8F,IAAuB;AACpC,QAAM,EAAE,WAAAC,IAAW,gBAAAC,GAAe,IAAIF;AAElCC,EAAAA,OACF/F,EAAM,YAAY+F,KAGhBC,OACFhG,EAAM,iBAAiBsD,EAAA,CAAA,GAAK0C,EAAAA;AAEhC,EACF;AAvBA,ICJMhG,IAAQC,MAAsB,EAClC,MAAM,OACN,SAAS,IACT,SAAS,UACX,CAAC;ADAD,ICGagG,KAAY,EACvB,OAAAjG,GAEA,UAAUG,IAA8C;AACtD,SAAOC,UAAUJ,GAAO,MAAMG,GAASH,CAAK,CAAC;AAC/C,GAEA,UAAUkG,IAAoCC,IAAoC;AAChFnG,IAAM,OAAO,MACbA,EAAM,UAAUkG,IAChBlG,EAAM,UAAUmG;AAClB,GAEA,aAAa;AACXnG,IAAM,OAAO;AACf,EACF;", "names": ["TRACK_MEMO_SYMBOL", "Symbol", "GET_ORIGINAL_SYMBOL", "getProto", "Object", "getPrototypeOf", "objectsToTrack", "WeakMap", "isObjectToTrack", "obj", "has", "get", "prototype", "Array", "getUntracked", "obj", "isObjectToTrack", "GET_ORIGINAL_SYMBOL", "markToTrack", "mark", "objectsToTrack", "set", "e", "state", "proxy", "RouterCtrl", "callback", "valtioSub", "view", "data", "last", "CoreUtil", "ua", "url", "appUrl", "wc<PERSON><PERSON>", "name", "safeAppUrl", "encodedWcUrl", "mi<PERSON><PERSON><PERSON><PERSON>", "resolve", "href", "target", "_a", "routerData", "isEnabled", "EventsCtrl", "snapshot", "connectedWalletId", "event", "OptionsCtrl", "chains", "walletConnectUri", "isCustomDesktop", "isCustomMobile", "isDataLoaded", "isUiLoaded", "isAuth", "ConfigCtrl", "config", "_b", "d", "p", "I", "P", "m", "t", "s", "e", "l", "W3M_API", "SDK_TYPE", "SDK_VERSION", "fetchListings", "endpoint", "params", "allParams", "__spreadValues", "key", "value", "ExplorerUtil", "imageId", "E", "u", "h", "y", "x", "f", "isMobile", "ExplorerCtrl", "explorerRecommendedWalletIds", "explorerExcludedWalletIds", "listings", "listingsArr", "a", "b", "aIndex", "bIndex", "chainsFilter", "isExcluded", "extendedParams", "recomendedWallets", "wallet", "page", "search", "listingsObj", "total", "type", "ModalCtrl", "options", "interval", "opts", "i", "c", "r", "isDarkMode", "ThemeCtrl", "theme", "themeMode", "themeVariables", "ToastCtrl", "message", "variant"]}