"use client";
import {
  CoinbaseWalletConnector,
  MetaMaskConnector,
  SafeConnector,
  WalletConnectConnector,
  WalletConnectLegacyConnector
} from "./chunk-DI232KXC.js";
import "./chunk-P5AQC5DX.js";
import "./chunk-YUA5JKFY.js";
import "./chunk-HK67DIC5.js";
import {
  InjectedConnector
} from "./chunk-HE7TYL65.js";
import "./chunk-5EBGCFML.js";
import "./chunk-JDCTZMVO.js";
import "./chunk-Z2Q63RMA.js";
import "./chunk-L3LPOETZ.js";
import "./chunk-P4NPJVRO.js";
import "./chunk-XLLWCG7E.js";
import "./chunk-W6I35MAG.js";
import "./chunk-2B3V2GUC.js";
import "./chunk-W7S2ME4R.js";

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-ZOLACFTK.js
function isAndroid() {
  return typeof navigator !== "undefined" && /android/i.test(navigator.userAgent);
}
function isSmallIOS() {
  return typeof navigator !== "undefined" && /iPhone|iPod/.test(navigator.userAgent);
}
function isLargeIOS() {
  return typeof navigator !== "undefined" && (/iPad/.test(navigator.userAgent) || navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
}
function isIOS() {
  return isSmallIOS() || isLargeIOS();
}
function isMobile() {
  return isAndroid() || isIOS();
}

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-7IPLF2TT.js
async function getWalletConnectUri(connector, version) {
  const provider = await connector.getProvider();
  return version === "2" ? new Promise((resolve) => provider.once("display_uri", resolve)) : provider.connector.uri;
}
var sharedConnectors = /* @__PURE__ */ new Map();
function createConnector(version, config) {
  const connector = version === "1" ? new WalletConnectLegacyConnector(config) : new WalletConnectConnector(config);
  sharedConnectors.set(JSON.stringify(config), connector);
  return connector;
}
function getWalletConnectConnector({
  chains,
  options = {},
  projectId,
  version = "2"
}) {
  const exampleProjectId = "21fef48091f12692cad574a6f7753643";
  if (version === "2") {
    if (!projectId || projectId === "")
      throw new Error("No projectId found. Every dApp must now provide a WalletConnect Cloud projectId to enable WalletConnect v2 https://www.rainbowkit.com/docs/installation#configure");
    if (projectId === "YOUR_PROJECT_ID" || projectId === exampleProjectId)
      console.warn("Invalid projectId. Please create a unique WalletConnect Cloud projectId for your dApp https://www.rainbowkit.com/docs/installation#configure");
  }
  const config = {
    chains,
    options: version === "1" ? {
      qrcode: false,
      ...options
    } : {
      projectId: projectId === "YOUR_PROJECT_ID" ? exampleProjectId : projectId,
      showQrModal: false,
      ...options
    }
  };
  const serializedConfig = JSON.stringify(config);
  const sharedConnector = sharedConnectors.get(serializedConfig);
  return sharedConnector != null ? sharedConnector : createConnector(version, config);
}

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-EBWTDDFA.js
var zerionWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isZerionInjected = typeof window !== "undefined" && (typeof window.ethereum !== "undefined" && window.ethereum.isZerion || typeof window.zerionWallet !== "undefined");
  const shouldUseWalletConnect = !isZerionInjected;
  return {
    id: "zerion",
    name: "Zerion",
    iconUrl: async () => (await import("./zerionWallet-35GMAYN4-ZC34N7UG.js")).default,
    iconAccent: "#2962ef",
    iconBackground: "#2962ef",
    installed: !shouldUseWalletConnect ? isZerionInjected : void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=io.zerion.android",
      ios: "https://apps.apple.com/app/apple-store/id1456732565",
      mobile: "https://link.zerion.io/pt3gdRP0njb",
      qrCode: "https://link.zerion.io/pt3gdRP0njb",
      chrome: "https://chrome.google.com/webstore/detail/klghhnkeealcohjjanjjdaeeggmfmlpl",
      browserExtension: "https://zerion.io/extension"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : new InjectedConnector({
        chains,
        options: {
          getProvider: () => typeof window !== "undefined" ? window.zerionWallet || window.ethereum : void 0,
          ...options
        }
      });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return isIOS() ? `zerion://wc?uri=${encodeURIComponent(uri)}` : uri;
      };
      return {
        connector,
        mobile: {
          getUri: shouldUseWalletConnect ? getUri : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri,
          instructions: {
            learnMoreUrl: "https://zerion.io/blog/announcing-the-zerion-smart-wallet/",
            steps: [
              {
                description: "wallet_connectors.zerion.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.zerion.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.zerion.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.zerion.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.zerion.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.zerion.qr_code.step3.title"
              }
            ]
          }
        } : void 0,
        extension: {
          instructions: {
            learnMoreUrl: "https://help.zerion.io/en/",
            steps: [
              {
                description: "wallet_connectors.zerion.extension.step1.description",
                step: "install",
                title: "wallet_connectors.zerion.extension.step1.title"
              },
              {
                description: "wallet_connectors.zerion.extension.step2.description",
                step: "create",
                title: "wallet_connectors.zerion.extension.step2.title"
              },
              {
                description: "wallet_connectors.zerion.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.zerion.extension.step3.title"
              }
            ]
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-MQYCNKY3.js
function getExplicitInjectedProvider(flag) {
  if (typeof window === "undefined" || typeof window.ethereum === "undefined")
    return;
  const providers = window.ethereum.providers;
  return providers ? providers.find((provider) => provider[flag]) : window.ethereum[flag] ? window.ethereum : void 0;
}
function hasInjectedProvider(flag) {
  return Boolean(getExplicitInjectedProvider(flag));
}
function getInjectedProvider(flag) {
  if (typeof window === "undefined" || typeof window.ethereum === "undefined")
    return;
  const providers = window.ethereum.providers;
  const provider = getExplicitInjectedProvider(flag);
  if (provider)
    return provider;
  if (typeof providers !== "undefined" && providers.length > 0)
    return providers[0];
  return window.ethereum;
}
function getInjectedConnector({
  chains,
  flag,
  options
}) {
  return new InjectedConnector({
    chains,
    options: {
      getProvider: () => getInjectedProvider(flag),
      ...options
    }
  });
}

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-LJ6A4ZAF.js
var zealWallet = ({
  chains,
  ...options
}) => ({
  id: "zeal",
  name: "Zeal",
  iconUrl: async () => (await import("./zealWallet-K7JBLVKT-MFNTCYC5.js")).default,
  iconBackground: "#fff0",
  installed: hasInjectedProvider("isZeal"),
  downloadUrls: {
    browserExtension: "https://zeal.app"
  },
  createConnector: () => ({
    connector: new InjectedConnector({
      chains,
      options
    }),
    extension: {
      instructions: {
        learnMoreUrl: "https://zeal.app/",
        steps: [
          {
            description: "wallet_connectors.zeal.extension.step1.description",
            step: "install",
            title: "wallet_connectors.zeal.extension.step1.title"
          },
          {
            description: "wallet_connectors.zeal.extension.step2.description",
            step: "create",
            title: "wallet_connectors.zeal.extension.step2.title"
          },
          {
            description: "wallet_connectors.zeal.extension.step3.description",
            step: "refresh",
            title: "wallet_connectors.zeal.extension.step3.title"
          }
        ]
      }
    }
  })
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-WG6TOL3W.js
var tahoWallet = ({
  chains,
  ...options
}) => ({
  id: "taho",
  name: "Taho",
  iconBackground: "#d08d57",
  iconUrl: async () => (await import("./tahoWallet-BYONWLHD-V66IOGOV.js")).default,
  downloadUrls: {
    chrome: "https://chrome.google.com/webstore/detail/taho/eajafomhmkipbjmfmhebemolkcicgfmd",
    browserExtension: "https://taho.xyz"
  },
  installed: typeof window !== "undefined" && typeof window.tally !== "undefined" && window["tally"] ? true : void 0,
  createConnector: () => {
    return {
      connector: new InjectedConnector({
        chains,
        options: {
          getProvider: () => {
            const getTaho = (tally) => (tally == null ? void 0 : tally.isTally) ? tally : void 0;
            if (typeof window === "undefined")
              return;
            return getTaho(window.tally);
          },
          ...options
        }
      }),
      extension: {
        instructions: {
          learnMoreUrl: "https://tahowallet.notion.site/Taho-Knowledge-Base-4d95ed5439c64d6db3d3d27abf1fdae5",
          steps: [
            {
              description: "wallet_connectors.taho.extension.step1.description",
              step: "install",
              title: "wallet_connectors.taho.extension.step1.title"
            },
            {
              description: "wallet_connectors.taho.extension.step2.description",
              step: "create",
              title: "wallet_connectors.taho.extension.step2.title"
            },
            {
              description: "wallet_connectors.taho.extension.step3.description",
              step: "refresh",
              title: "wallet_connectors.taho.extension.step3.title"
            }
          ]
        }
      }
    };
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-TPZGT45G.js
var talismanWallet = ({
  chains,
  ...options
}) => ({
  id: "talisman",
  name: "Talisman",
  iconUrl: async () => (await import("./talismanWallet-W5EQ26N7-3KKPUTMN.js")).default,
  iconBackground: "#fff",
  installed: typeof window !== "undefined" && typeof window.talismanEth !== "undefined" && window.talismanEth.isTalisman === true,
  downloadUrls: {
    chrome: "https://chrome.google.com/webstore/detail/talisman-polkadot-wallet/fijngjgcjhjmmpcmkeiomlglpeiijkld",
    firefox: "https://addons.mozilla.org/en-US/firefox/addon/talisman-wallet-extension/",
    browserExtension: "https://talisman.xyz/download"
  },
  createConnector: () => ({
    connector: new InjectedConnector({
      chains,
      options: {
        getProvider: () => {
          if (typeof window === "undefined")
            return;
          return window.talismanEth;
        },
        ...options
      }
    }),
    extension: {
      instructions: {
        learnMoreUrl: "https://talisman.xyz/",
        steps: [
          {
            description: "wallet_connectors.talisman.extension.step1.description",
            step: "install",
            title: "wallet_connectors.talisman.extension.step1.title"
          },
          {
            description: "wallet_connectors.talisman.extension.step2.description",
            step: "create",
            title: "wallet_connectors.talisman.extension.step2.title"
          },
          {
            description: "wallet_connectors.talisman.extension.step3.description",
            step: "refresh",
            title: "wallet_connectors.talisman.extension.step3.title"
          }
        ]
      }
    }
  })
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-V5NFDUUI.js
function isSafari() {
  return typeof navigator !== "undefined" && /Version\/([0-9._]+).*Safari/.test(navigator.userAgent);
}
var tokenaryWallet = ({
  chains,
  ...options
}) => ({
  id: "tokenary",
  name: "Tokenary",
  iconUrl: async () => (await import("./tokenaryWallet-FZ7BMUTO-IAND55IT.js")).default,
  iconBackground: "#ffffff",
  installed: typeof window !== "undefined" && typeof window.ethereum !== "undefined" && window.ethereum.isTokenary,
  hidden: () => !isSafari(),
  downloadUrls: {
    ios: "https://tokenary.io/get",
    mobile: "https://tokenary.io",
    qrCode: "https://tokenary.io/get",
    safari: "https://tokenary.io/get",
    browserExtension: "https://tokenary.io/get"
  },
  createConnector: () => ({
    connector: new InjectedConnector({
      chains,
      options
    })
  })
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-2TLRAFPK.js
var tokenPocketWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2"
}) => {
  var _a;
  const isTokenPocketInjected = typeof window !== "undefined" && ((_a = window.ethereum) == null ? void 0 : _a.isTokenPocket) === true;
  const shouldUseWalletConnect = !isTokenPocketInjected;
  return {
    id: "tokenPocket",
    name: "TokenPocket",
    iconUrl: async () => (await import("./tokenPocketWallet-UYD66DEM-POHCTZC2.js")).default,
    iconBackground: "#2980FE",
    installed: !shouldUseWalletConnect ? isTokenPocketInjected : void 0,
    downloadUrls: {
      chrome: "https://chrome.google.com/webstore/detail/tokenpocket/mfgccjchihfkkindfppnaooecgfneiii",
      browserExtension: "https://extension.tokenpocket.pro/",
      android: "https://play.google.com/store/apps/details?id=vip.mytokenpocket",
      ios: "https://apps.apple.com/us/app/tp-global-wallet/id6444625622",
      qrCode: "https://tokenpocket.pro/en/download/app",
      mobile: "https://tokenpocket.pro/en/download/app"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        chains,
        projectId,
        options: walletConnectOptions,
        version: walletConnectVersion
      }) : new InjectedConnector({ chains });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return isMobile() ? `tpoutside://wc?uri=${encodeURIComponent(uri)}` : uri;
      };
      return {
        connector,
        mobile: {
          getUri: shouldUseWalletConnect ? getUri : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri,
          instructions: {
            learnMoreUrl: "https://help.tokenpocket.pro/en/",
            steps: [
              {
                description: "wallet_connectors.token_pocket.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.token_pocket.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.token_pocket.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.token_pocket.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.token_pocket.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.token_pocket.qr_code.step3.title"
              }
            ]
          }
        } : void 0,
        extension: {
          instructions: {
            learnMoreUrl: "https://help.tokenpocket.pro/en/extension-wallet/faq/installation-tutorial",
            steps: [
              {
                description: "wallet_connectors.token_pocket.extension.step1.description",
                step: "install",
                title: "wallet_connectors.token_pocket.extension.step1.title"
              },
              {
                description: "wallet_connectors.token_pocket.extension.step2.description",
                step: "create",
                title: "wallet_connectors.token_pocket.extension.step2.title"
              },
              {
                description: "wallet_connectors.token_pocket.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.token_pocket.extension.step3.title"
              }
            ]
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-KRB7QT2L.js
var trustWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isTrustWalletInjected = isMobile() ? hasInjectedProvider("isTrust") : hasInjectedProvider("isTrustWallet");
  const shouldUseWalletConnect = !isTrustWalletInjected;
  return {
    id: "trust",
    name: "Trust Wallet",
    iconUrl: async () => (await import("./trustWallet-FST5ID2K-2M77WFAA.js")).default,
    installed: isTrustWalletInjected || void 0,
    iconAccent: "#3375BB",
    iconBackground: "#fff",
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=com.wallet.crypto.trustapp",
      ios: "https://apps.apple.com/us/app/trust-crypto-bitcoin-wallet/id1288339409",
      mobile: "https://trustwallet.com/download",
      qrCode: "https://trustwallet.com/download",
      chrome: "https://chrome.google.com/webstore/detail/trust-wallet/egjidjbpglichdcondbcbdnbeeppgdph",
      browserExtension: "https://trustwallet.com/browser-extension"
    },
    createConnector: () => {
      const getUriMobile = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return `trust://wc?uri=${encodeURIComponent(uri)}`;
      };
      const getUriQR = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return uri;
      };
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : isMobile() ? getInjectedConnector({ flag: "isTrust", chains, options }) : getInjectedConnector({ flag: "isTrustWallet", chains, options });
      const mobileConnector = {
        getUri: shouldUseWalletConnect ? getUriMobile : void 0
      };
      let qrConnector = void 0;
      if (shouldUseWalletConnect) {
        qrConnector = {
          getUri: getUriQR,
          instructions: {
            learnMoreUrl: "https://trustwallet.com/",
            steps: [
              {
                description: "wallet_connectors.trust.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.trust.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.trust.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.trust.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.trust.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.trust.qr_code.step3.title"
              }
            ]
          }
        };
      }
      const extensionConnector = {
        instructions: {
          learnMoreUrl: "https://trustwallet.com/browser-extension",
          steps: [
            {
              description: "wallet_connectors.trust.extension.step1.description",
              step: "install",
              title: "wallet_connectors.trust.extension.step1.title"
            },
            {
              description: "wallet_connectors.trust.extension.step2.description",
              step: "create",
              title: "wallet_connectors.trust.extension.step2.title"
            },
            {
              description: "wallet_connectors.trust.extension.step3.description",
              step: "refresh",
              title: "wallet_connectors.trust.extension.step3.title"
            }
          ]
        }
      };
      return {
        connector,
        mobile: mobileConnector,
        qrCode: qrConnector,
        extension: extensionConnector
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-GEJE7HDQ.js
var uniswapWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2"
}) => ({
  id: "uniswap",
  name: "Uniswap Wallet",
  iconUrl: async () => (await import("./uniswapWallet-JYAMZDQK-PWFJ4A6S.js")).default,
  iconBackground: "#FFD8EA",
  downloadUrls: {
    ios: "https://apps.apple.com/app/apple-store/id6443944476",
    mobile: "https://wallet.uniswap.org/",
    qrCode: "https://wallet.uniswap.org/"
  },
  createConnector: () => {
    const connector = getWalletConnectConnector({
      projectId,
      chains,
      version: walletConnectVersion,
      options: walletConnectOptions
    });
    return {
      connector,
      mobile: {
        getUri: async () => {
          const uri = await getWalletConnectUri(connector, walletConnectVersion);
          return `uniswap://wc?uri=${encodeURIComponent(uri)}`;
        }
      },
      qrCode: {
        getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
        instructions: {
          learnMoreUrl: "https://wallet.uniswap.org/",
          steps: [
            {
              description: "wallet_connectors.uniswap.qr_code.step1.description",
              step: "install",
              title: "wallet_connectors.uniswap.qr_code.step1.title"
            },
            {
              description: "wallet_connectors.uniswap.qr_code.step2.description",
              step: "create",
              title: "wallet_connectors.uniswap.qr_code.step2.title"
            },
            {
              description: "wallet_connectors.uniswap.qr_code.step3.description",
              step: "scan",
              title: "wallet_connectors.uniswap.qr_code.step3.title"
            }
          ]
        }
      }
    };
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-RWVPLIAV.js
var xdefiWallet = ({
  chains,
  ...options
}) => {
  const isInstalled = typeof window !== "undefined" && typeof (window == null ? void 0 : window.xfi) !== "undefined";
  return {
    id: "xdefi",
    name: "XDEFI Wallet",
    installed: isInstalled,
    iconUrl: async () => (await import("./xdefiWallet-QL7LCYNI-NRUJS7U6.js")).default,
    iconBackground: "#fff",
    downloadUrls: {
      chrome: "https://chrome.google.com/webstore/detail/xdefi-wallet/hmeobnfnfcmdkdcmlblgagmfpfboieaf",
      browserExtension: "https://xdefi.io"
    },
    createConnector: () => ({
      connector: new InjectedConnector({
        chains,
        options: {
          getProvider: () => {
            var _a;
            return isInstalled ? (_a = window.xfi) == null ? void 0 : _a.ethereum : void 0;
          },
          ...options
        }
      }),
      extension: {
        instructions: {
          learnMoreUrl: "https://xdefi.io/support-categories/xdefi-wallet/",
          steps: [
            {
              description: "wallet_connectors.xdefi.extension.step1.description",
              step: "install",
              title: "wallet_connectors.xdefi.extension.step1.title"
            },
            {
              description: "wallet_connectors.xdefi.extension.step2.description",
              step: "create",
              title: "wallet_connectors.xdefi.extension.step2.title"
            },
            {
              description: "wallet_connectors.xdefi.extension.step3.description",
              step: "refresh",
              title: "wallet_connectors.xdefi.extension.step3.title"
            }
          ]
        }
      }
    })
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-AFQDNMHR.js
var walletConnectWallet = ({
  chains,
  options,
  projectId,
  version = "2"
}) => ({
  id: "walletConnect",
  name: "WalletConnect",
  iconUrl: async () => (await import("./walletConnectWallet-D6ZADJM7-EU3PXOXH.js")).default,
  iconBackground: "#3b99fc",
  createConnector: () => {
    const ios = isIOS();
    const connector = version === "1" ? getWalletConnectConnector({
      version: "1",
      chains,
      options: {
        qrcode: ios,
        ...options
      }
    }) : getWalletConnectConnector({
      version: "2",
      chains,
      projectId,
      options: {
        showQrModal: ios,
        ...options
      }
    });
    const getUri = async () => getWalletConnectUri(connector, version);
    return {
      connector,
      ...ios ? {} : {
        mobile: { getUri },
        qrCode: { getUri }
      }
    };
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-BBJKQTN3.js
var oneKeyWallet = ({ chains }) => {
  var _a;
  const provider = typeof window !== "undefined" && ((_a = window["$onekey"]) == null ? void 0 : _a.ethereum);
  const isOnekeyInjected = Boolean(provider);
  return {
    createConnector: () => {
      const connector = new InjectedConnector({
        chains,
        options: {
          getProvider: () => provider
        }
      });
      return {
        connector,
        extension: {
          instructions: {
            learnMoreUrl: "https://help.onekey.so/hc/en-us/categories/360000170236",
            steps: [
              {
                description: "wallet_connectors.one_key.extension.step1.description",
                step: "install",
                title: "wallet_connectors.one_key.extension.step1.title"
              },
              {
                description: "wallet_connectors.one_key.extension.step2.description",
                step: "create",
                title: "wallet_connectors.one_key.extension.step2.title"
              },
              {
                description: "wallet_connectors.one_key.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.one_key.extension.step3.title"
              }
            ]
          }
        }
      };
    },
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=so.onekey.app.wallet",
      browserExtension: "https://www.onekey.so/download/",
      chrome: "https://chrome.google.com/webstore/detail/onekey/jnmbobjmhlngoefaiojfljckilhhlhcj",
      edge: "https://microsoftedge.microsoft.com/addons/detail/onekey/obffkkagpmohennipjokmpllocnlndac",
      ios: "https://apps.apple.com/us/app/onekey-open-source-wallet/id1609559473",
      mobile: "https://www.onekey.so/download/",
      qrCode: "https://www.onekey.so/download/"
    },
    iconAccent: "#00B812",
    iconBackground: "#fff",
    iconUrl: async () => (await import("./oneKeyWallet-FEYKOAOJ-LSRB36OJ.js")).default,
    id: "onekey",
    installed: isOnekeyInjected,
    name: "OneKey"
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-XOGNDGKZ.js
var phantomWallet = ({
  chains,
  ...options
}) => {
  var _a;
  return {
    id: "phantom",
    name: "Phantom",
    iconUrl: async () => (await import("./phantomWallet-OLG36S4X-UZXX5VFU.js")).default,
    iconBackground: "#9A8AEE",
    installed: typeof window !== "undefined" && !!((_a = window.phantom) == null ? void 0 : _a.ethereum) || void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=app.phantom",
      ios: "https://apps.apple.com/app/phantom-solana-wallet/**********",
      mobile: "https://phantom.app/download",
      qrCode: "https://phantom.app/download",
      chrome: "https://chrome.google.com/webstore/detail/phantom/bfnaelmomeimhlpmgjnjophhpkkoljpa",
      firefox: "https://addons.mozilla.org/firefox/addon/phantom-app/",
      browserExtension: "https://phantom.app/download"
    },
    createConnector: () => {
      const getProvider = () => {
        var _a2;
        return typeof window !== "undefined" ? (_a2 = window.phantom) == null ? void 0 : _a2.ethereum : void 0;
      };
      const connector = new InjectedConnector({
        chains,
        options: { getProvider, ...options }
      });
      return {
        connector,
        extension: {
          instructions: {
            steps: [
              {
                description: "wallet_connectors.phantom.extension.step1.description",
                step: "install",
                title: "wallet_connectors.phantom.extension.step1.title"
              },
              {
                description: "wallet_connectors.phantom.extension.step2.description",
                step: "create",
                title: "wallet_connectors.phantom.extension.step2.title"
              },
              {
                description: "wallet_connectors.phantom.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.phantom.extension.step3.title"
              }
            ],
            learnMoreUrl: "https://help.phantom.app"
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-TNC2B7LX.js
var rabbyWallet = ({
  chains,
  ...options
}) => ({
  id: "rabby",
  name: "Rabby Wallet",
  iconUrl: async () => (await import("./rabbyWallet-22VWIFCE-OTFH64JJ.js")).default,
  iconBackground: "#8697FF",
  installed: typeof window !== "undefined" && typeof window.ethereum !== "undefined" && window.ethereum.isRabby === true,
  downloadUrls: {
    chrome: "https://chrome.google.com/webstore/detail/rabby-wallet/acmacodkjbdgmoleebolmdjonilkdbch",
    browserExtension: "https://rabby.io"
  },
  createConnector: () => ({
    connector: new InjectedConnector({
      chains,
      options
    }),
    extension: {
      instructions: {
        learnMoreUrl: "https://rabby.io/",
        steps: [
          {
            description: "wallet_connectors.rabby.extension.step1.description",
            step: "install",
            title: "wallet_connectors.rabby.extension.step1.title"
          },
          {
            description: "wallet_connectors.rabby.extension.step2.description",
            step: "create",
            title: "wallet_connectors.rabby.extension.step2.title"
          },
          {
            description: "wallet_connectors.rabby.extension.step3.description",
            step: "refresh",
            title: "wallet_connectors.rabby.extension.step3.title"
          }
        ]
      }
    }
  })
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-NXTGMMKC.js
var rainbowWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isRainbowInjected = hasInjectedProvider("isRainbow");
  const shouldUseWalletConnect = !isRainbowInjected;
  return {
    id: "rainbow",
    name: "Rainbow",
    iconUrl: async () => (await import("./rainbowWallet-GGU64QEI-MQCVWEBM.js")).default,
    iconBackground: "#0c2f78",
    installed: !shouldUseWalletConnect ? isRainbowInjected : void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=me.rainbow&referrer=utm_source%3Drainbowkit&utm_source=rainbowkit",
      ios: "https://apps.apple.com/app/apple-store/id1457119021?pt=119997837&ct=rainbowkit&mt=8",
      mobile: "https://rainbow.download?utm_source=rainbowkit",
      qrCode: "https://rainbow.download?utm_source=rainbowkit&utm_medium=qrcode",
      browserExtension: "https://rainbow.me/extension?utm_source=rainbowkit"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : getInjectedConnector({ flag: "isRainbow", chains, options });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return isAndroid() ? uri : isIOS() ? `rainbow://wc?uri=${encodeURIComponent(uri)}&connector=rainbowkit` : `https://rnbwapp.com/wc?uri=${encodeURIComponent(uri)}&connector=rainbowkit`;
      };
      return {
        connector,
        mobile: { getUri: shouldUseWalletConnect ? getUri : void 0 },
        qrCode: shouldUseWalletConnect ? {
          getUri,
          instructions: {
            learnMoreUrl: "https://learn.rainbow.me/connect-to-a-website-or-app?utm_source=rainbowkit&utm_medium=connector&utm_campaign=learnmore",
            steps: [
              {
                description: "wallet_connectors.rainbow.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.rainbow.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.rainbow.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.rainbow.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.rainbow.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.rainbow.qr_code.step3.title"
              }
            ]
          }
        } : void 0
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-A6WSD4AS.js
var safeWallet = ({
  chains,
  ...options
}) => ({
  id: "safe",
  name: "Safe",
  iconAccent: "#12ff80",
  iconBackground: "#fff",
  iconUrl: async () => (await import("./safeWallet-DFMLSLCR-N7CB6SES.js")).default,
  installed: !(typeof window === "undefined") && (window == null ? void 0 : window.parent) !== window,
  downloadUrls: {},
  createConnector: () => ({
    connector: new SafeConnector({ chains, options })
  })
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-7WTKIVCW.js
var safeheronWallet = ({
  chains,
  ...options
}) => ({
  id: "safeheron",
  name: "Safeheron",
  installed: typeof window !== "undefined" && typeof window.safeheron !== "undefined" && window.safeheron.isSafeheron === true,
  iconUrl: async () => (await import("./safeheronWallet-YBMFXEUH-QPNQORWP.js")).default,
  iconBackground: "#fff",
  downloadUrls: {
    chrome: "https://chrome.google.com/webstore/detail/safeheron/aiaghdjafpiofpainifbgfgjfpclngoh",
    browserExtension: "https://www.safeheron.com/"
  },
  createConnector: () => ({
    connector: new InjectedConnector({
      chains,
      options: {
        getProvider: () => typeof window !== "undefined" ? window.safeheron : void 0,
        ...options
      }
    }),
    extension: {
      instructions: {
        learnMoreUrl: "https://www.safeheron.com/",
        steps: [
          {
            description: "wallet_connectors.safeheron.extension.step1.description",
            step: "install",
            title: "wallet_connectors.safeheron.extension.step1.title"
          },
          {
            description: "wallet_connectors.safeheron.extension.step2.description",
            step: "create",
            title: "wallet_connectors.safeheron.extension.step2.title"
          },
          {
            description: "wallet_connectors.safeheron.extension.step3.description",
            step: "refresh",
            title: "wallet_connectors.safeheron.extension.step3.title"
          }
        ]
      }
    }
  })
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-6GAQUU2I.js
function getSafepalWalletInjectedProvider() {
  var _a;
  const isSafePalWallet = (ethereum) => {
    const safepalWallet2 = !!ethereum.isSafePal;
    return safepalWallet2;
  };
  const injectedProviderExist = typeof window !== "undefined" && typeof window.ethereum !== "undefined";
  if (!injectedProviderExist) {
    return;
  }
  if (window["safepalProvider"]) {
    return window["safepalProvider"];
  }
  if (isSafePalWallet(window.ethereum)) {
    return window.ethereum;
  }
  if ((_a = window.ethereum) == null ? void 0 : _a.providers) {
    return window.ethereum.providers.find(isSafePalWallet);
  }
}
var safepalWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isSafePalWalletInjected = Boolean(getSafepalWalletInjectedProvider());
  const shouldUseWalletConnect = !isSafePalWalletInjected;
  return {
    id: "safepal",
    name: "SafePal Wallet",
    iconUrl: async () => (await import("./safepalWallet-FDJRNZUU-GAPOXYZK.js")).default,
    installed: isSafePalWalletInjected || void 0,
    iconAccent: "#3375BB",
    iconBackground: "#fff",
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=io.safepal.wallet&referrer=utm_source%3Drainbowkit%26utm_medium%3Ddisplay%26utm_campaign%3Ddownload",
      ios: "https://apps.apple.com/app/apple-store/id1548297139?pt=122504219&ct=rainbowkit&mt=8",
      mobile: "https://www.safepal.com/en/download",
      qrCode: "https://www.safepal.com/en/download",
      chrome: "https://chrome.google.com/webstore/detail/safepal-extension-wallet/lgmpcpglpngdoalbgeoldeajfclnhafa",
      browserExtension: "https://www.safepal.com/download?product=2"
    },
    createConnector: () => {
      const getUriMobile = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return `safepalwallet://wc?uri=${encodeURIComponent(uri)}`;
      };
      const getUriQR = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return uri;
      };
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : new InjectedConnector({
        chains,
        options: {
          getProvider: getSafepalWalletInjectedProvider,
          ...options
        }
      });
      const mobileConnector = {
        getUri: shouldUseWalletConnect ? getUriMobile : void 0
      };
      let qrConnector = void 0;
      if (shouldUseWalletConnect) {
        qrConnector = {
          getUri: getUriQR,
          instructions: {
            learnMoreUrl: "https://safepal.com/",
            steps: [
              {
                description: "wallet_connectors.safepal.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.safepal.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.safepal.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.safepal.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.safepal.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.safepal.qr_code.step3.title"
              }
            ]
          }
        };
      }
      const extensionConnector = {
        instructions: {
          learnMoreUrl: "https://www.safepal.com/download?product=2",
          steps: [
            {
              description: "wallet_connectors.safepal.extension.step1.description",
              step: "install",
              title: "wallet_connectors.safepal.extension.step1.title"
            },
            {
              description: "wallet_connectors.safepal.extension.step2.description",
              step: "create",
              title: "wallet_connectors.safepal.extension.step2.title"
            },
            {
              description: "wallet_connectors.safepal.extension.step3.description",
              step: "refresh",
              title: "wallet_connectors.safepal.extension.step3.title"
            }
          ]
        }
      };
      return {
        connector,
        mobile: mobileConnector,
        qrCode: qrConnector,
        extension: extensionConnector
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-6UZUEWNI.js
var getSubWalletInjectedProvider = () => {
  if (typeof window === "undefined")
    return;
  return window.SubWallet;
};
var subWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isSubWalletInjected = Boolean(getSubWalletInjectedProvider());
  const shouldUseWalletConnect = !isSubWalletInjected;
  return {
    id: "subwallet",
    name: "SubWallet",
    iconUrl: async () => (await import("./subWallet-ELA2UJOS-OT3RISX3.js")).default,
    iconBackground: "#fff",
    installed: isSubWalletInjected || void 0,
    downloadUrls: {
      browserExtension: "https://www.subwallet.app/download",
      chrome: "https://chrome.google.com/webstore/detail/subwallet-polkadot-wallet/onhogfjeacnfoofkfgppdlbmlmnplgbn",
      firefox: "https://addons.mozilla.org/en-US/firefox/addon/subwallet/",
      edge: "https://chrome.google.com/webstore/detail/subwallet-polkadot-wallet/onhogfjeacnfoofkfgppdlbmlmnplgbn",
      mobile: "https://www.subwallet.app/download",
      android: "https://play.google.com/store/apps/details?id=app.subwallet.mobile",
      ios: "https://apps.apple.com/us/app/subwallet-polkadot-wallet/id1633050285",
      qrCode: "https://www.subwallet.app/download"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : new InjectedConnector({
        chains,
        options: {
          getProvider: getSubWalletInjectedProvider,
          ...options
        }
      });
      const getUriMobile = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return `subwallet://wc?uri=${encodeURIComponent(uri)}`;
      };
      const getUriQR = async () => {
        return await getWalletConnectUri(connector, walletConnectVersion);
      };
      const mobileConnector = {
        getUri: shouldUseWalletConnect ? getUriMobile : void 0
      };
      let qrConnector = void 0;
      if (shouldUseWalletConnect) {
        qrConnector = {
          getUri: getUriQR,
          instructions: {
            learnMoreUrl: "https://www.subwallet.app/",
            steps: [
              {
                description: "wallet_connectors.subwallet.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.subwallet.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.subwallet.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.subwallet.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.subwallet.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.subwallet.qr_code.step3.title"
              }
            ]
          }
        };
      }
      const extensionConnector = {
        instructions: {
          learnMoreUrl: "https://www.subwallet.app/",
          steps: [
            {
              description: "wallet_connectors.subwallet.extension.step1.description",
              step: "install",
              title: "wallet_connectors.subwallet.extension.step1.title"
            },
            {
              description: "wallet_connectors.subwallet.extension.step2.description",
              step: "create",
              title: "wallet_connectors.subwallet.extension.step2.title"
            },
            {
              description: "wallet_connectors.subwallet.extension.step3.description",
              step: "refresh",
              title: "wallet_connectors.subwallet.extension.step3.title"
            }
          ]
        }
      };
      return {
        connector,
        mobile: mobileConnector,
        qrCode: qrConnector,
        extension: extensionConnector
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-NP7YNZFK.js
var imTokenWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2"
}) => ({
  id: "imToken",
  name: "imToken",
  iconUrl: async () => (await import("./imTokenWallet-DMDOIZDQ-E5EYMSXS.js")).default,
  iconBackground: "#098de6",
  downloadUrls: {
    android: "https://play.google.com/store/apps/details?id=im.token.app",
    ios: "https://itunes.apple.com/us/app/imtoken2/id1384798940",
    mobile: "https://token.im/download",
    qrCode: "https://token.im/download"
  },
  createConnector: () => {
    const connector = getWalletConnectConnector({
      projectId,
      chains,
      version: walletConnectVersion,
      options: walletConnectOptions
    });
    return {
      connector,
      mobile: {
        getUri: async () => {
          const uri = await getWalletConnectUri(connector, walletConnectVersion);
          return `imtokenv2://wc?uri=${encodeURIComponent(uri)}`;
        }
      },
      qrCode: {
        getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
        instructions: {
          learnMoreUrl: typeof window !== "undefined" && window.navigator.language.includes("zh") ? "https://support.token.im/hc/zh-cn/categories/360000925393" : "https://support.token.im/hc/en-us/categories/360000925393",
          steps: [
            {
              description: "wallet_connectors.im_token.qr_code.step1.description",
              step: "install",
              title: "wallet_connectors.im_token.qr_code.step1.title"
            },
            {
              description: "wallet_connectors.im_token.qr_code.step2.description",
              step: "create",
              title: "wallet_connectors.im_token.qr_code.step2.title"
            },
            {
              description: "wallet_connectors.im_token.qr_code.step3.description",
              step: "scan",
              title: "wallet_connectors.im_token.qr_code.step3.title"
            }
          ]
        }
      }
    };
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-7TYS3UTW.js
var injectedWallet = ({
  chains,
  ...options
}) => ({
  id: "injected",
  name: "Browser Wallet",
  iconUrl: async () => (await import("./injectedWallet-EUKDEAIU-6IPRRUK6.js")).default,
  iconBackground: "#fff",
  hidden: ({ wallets }) => wallets.some((wallet) => wallet.installed && wallet.name === wallet.connector.name && (wallet.connector instanceof InjectedConnector || wallet.id === "coinbase")),
  createConnector: () => ({
    connector: new InjectedConnector({
      chains,
      options
    })
  })
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-O6GPXB7E.js
var ledgerWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2"
}) => ({
  id: "ledger",
  iconBackground: "#000",
  iconAccent: "#000",
  name: "Ledger",
  iconUrl: async () => (await import("./ledgerWallet-DIS4VM6H-2UGSZQTY.js")).default,
  downloadUrls: {
    android: "https://play.google.com/store/apps/details?id=com.ledger.live",
    ios: "https://apps.apple.com/us/app/ledger-live-web3-wallet/id1361671700",
    mobile: "https://www.ledger.com/ledger-live",
    qrCode: "https://r354.adj.st/?adj_t=t2esmlk",
    windows: "https://www.ledger.com/ledger-live/download",
    macos: "https://www.ledger.com/ledger-live/download",
    linux: "https://www.ledger.com/ledger-live/download",
    desktop: "https://www.ledger.com/ledger-live"
  },
  createConnector: () => {
    const connector = getWalletConnectConnector({
      projectId,
      chains,
      version: walletConnectVersion,
      options: walletConnectOptions
    });
    return {
      connector,
      mobile: {
        getUri: async () => {
          const uri = await getWalletConnectUri(connector, walletConnectVersion);
          return isAndroid() ? uri : `ledgerlive://wc?uri=${encodeURIComponent(uri)}`;
        }
      },
      desktop: {
        getUri: async () => {
          const uri = await getWalletConnectUri(connector, walletConnectVersion);
          return `ledgerlive://wc?uri=${encodeURIComponent(uri)}`;
        },
        instructions: {
          learnMoreUrl: "https://support.ledger.com/hc/en-us/articles/4404389503889-Getting-started-with-Ledger-Live",
          steps: [
            {
              description: "wallet_connectors.ledger.desktop.step1.description",
              step: "install",
              title: "wallet_connectors.ledger.desktop.step1.title"
            },
            {
              description: "wallet_connectors.ledger.desktop.step2.description",
              step: "create",
              title: "wallet_connectors.ledger.desktop.step2.title"
            },
            {
              description: "wallet_connectors.ledger.desktop.step3.description",
              step: "connect",
              title: "wallet_connectors.ledger.desktop.step3.title"
            }
          ]
        }
      },
      qrCode: {
        getUri: async () => {
          const uri = await getWalletConnectUri(connector, walletConnectVersion);
          return `ledgerlive://wc?uri=${encodeURIComponent(uri)}`;
        },
        instructions: {
          learnMoreUrl: "https://support.ledger.com/hc/en-us/articles/4404389503889-Getting-started-with-Ledger-Live",
          steps: [
            {
              description: "wallet_connectors.ledger.qr_code.step1.description",
              step: "install",
              title: "wallet_connectors.ledger.qr_code.step1.title"
            },
            {
              description: "wallet_connectors.ledger.qr_code.step2.description",
              step: "create",
              title: "wallet_connectors.ledger.qr_code.step2.title"
            },
            {
              description: "wallet_connectors.ledger.qr_code.step3.description",
              step: "scan",
              title: "wallet_connectors.ledger.qr_code.step3.title"
            }
          ]
        }
      }
    };
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-YY63ARSJ.js
function isMetaMask(ethereum) {
  if (!(ethereum == null ? void 0 : ethereum.isMetaMask))
    return false;
  if (ethereum.isBraveWallet && !ethereum._events && !ethereum._state)
    return false;
  if (ethereum.isApexWallet)
    return false;
  if (ethereum.isAvalanche)
    return false;
  if (ethereum.isBackpack)
    return false;
  if (ethereum.isBifrost)
    return false;
  if (ethereum.isBitKeep)
    return false;
  if (ethereum.isBitski)
    return false;
  if (ethereum.isBlockWallet)
    return false;
  if (ethereum.isCoinbaseWallet)
    return false;
  if (ethereum.isDawn)
    return false;
  if (ethereum.isEnkrypt)
    return false;
  if (ethereum.isExodus)
    return false;
  if (ethereum.isFrame)
    return false;
  if (ethereum.isFrontier)
    return false;
  if (ethereum.isGamestop)
    return false;
  if (ethereum.isHyperPay)
    return false;
  if (ethereum.isImToken)
    return false;
  if (ethereum.isKuCoinWallet)
    return false;
  if (ethereum.isMathWallet)
    return false;
  if (ethereum.isOkxWallet || ethereum.isOKExWallet)
    return false;
  if (ethereum.isOneInchIOSWallet || ethereum.isOneInchAndroidWallet)
    return false;
  if (ethereum.isOpera)
    return false;
  if (ethereum.isPhantom)
    return false;
  if (ethereum.isPortal)
    return false;
  if (ethereum.isRabby)
    return false;
  if (ethereum.isRainbow)
    return false;
  if (ethereum.isStatus)
    return false;
  if (ethereum.isTalisman)
    return false;
  if (ethereum.isTally)
    return false;
  if (ethereum.isTokenPocket)
    return false;
  if (ethereum.isTokenary)
    return false;
  if (ethereum.isTrust || ethereum.isTrustWallet)
    return false;
  if (ethereum.isXDEFI)
    return false;
  if (ethereum.isZeal)
    return false;
  if (ethereum.isZerion)
    return false;
  return true;
}
var metaMaskWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  var _a, _b;
  const providers = typeof window !== "undefined" && ((_a = window.ethereum) == null ? void 0 : _a.providers);
  const isMetaMaskInjected = typeof window !== "undefined" && typeof window.ethereum !== "undefined" && (((_b = window.ethereum.providers) == null ? void 0 : _b.some(isMetaMask)) || window.ethereum.isMetaMask);
  const shouldUseWalletConnect = !isMetaMaskInjected;
  return {
    id: "metaMask",
    name: "MetaMask",
    iconUrl: async () => (await import("./metaMaskWallet-ORHUNQRP-LCXG7JSF.js")).default,
    iconAccent: "#f6851a",
    iconBackground: "#fff",
    installed: !shouldUseWalletConnect ? isMetaMaskInjected : void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=io.metamask",
      ios: "https://apps.apple.com/us/app/metamask/id1438144202",
      mobile: "https://metamask.io/download",
      qrCode: "https://metamask.io/download",
      chrome: "https://chrome.google.com/webstore/detail/metamask/nkbihfbeogaeaoehlefnkodbefgpgknn",
      edge: "https://microsoftedge.microsoft.com/addons/detail/metamask/ejbalbakoplchlghecdalmeeeajnimhm",
      firefox: "https://addons.mozilla.org/firefox/addon/ether-metamask",
      opera: "https://addons.opera.com/extensions/details/metamask-10",
      browserExtension: "https://metamask.io/download"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : new MetaMaskConnector({
        chains,
        options: {
          getProvider: () => Array.isArray(providers) && providers.find(isMetaMask) || (typeof window !== "undefined" ? window.ethereum : void 0),
          ...options
        }
      });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return isAndroid() ? uri : isIOS() ? `metamask://wc?uri=${encodeURIComponent(uri)}` : `https://metamask.app.link/wc?uri=${encodeURIComponent(uri)}`;
      };
      return {
        connector,
        mobile: {
          getUri: shouldUseWalletConnect ? getUri : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri,
          instructions: {
            learnMoreUrl: "https://metamask.io/faqs/",
            steps: [
              {
                description: "wallet_connectors.metamask.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.metamask.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.metamask.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.metamask.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.metamask.qr_code.step3.description",
                step: "refresh",
                title: "wallet_connectors.metamask.qr_code.step3.title"
              }
            ]
          }
        } : void 0,
        extension: {
          instructions: {
            learnMoreUrl: "https://metamask.io/faqs/",
            steps: [
              {
                description: "wallet_connectors.metamask.extension.step1.description",
                step: "install",
                title: "wallet_connectors.metamask.extension.step1.title"
              },
              {
                description: "wallet_connectors.metamask.extension.step2.description",
                step: "create",
                title: "wallet_connectors.metamask.extension.step2.title"
              },
              {
                description: "wallet_connectors.metamask.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.metamask.extension.step3.title"
              }
            ]
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-PLQVPRFW.js
var oktoWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2"
}) => ({
  id: "Okto",
  name: "Okto",
  iconUrl: async () => (await import("./oktoWallet-3LTNTBG3-4OQH5D2T.js")).default,
  iconBackground: "#fff",
  downloadUrls: {
    android: "https://play.google.com/store/apps/details?id=im.okto.contractwalletclient",
    ios: "https://apps.apple.com/in/app/okto-wallet/id6450688229",
    mobile: "https://okto.tech/",
    qrCode: "https://okto.tech/"
  },
  createConnector: () => {
    const connector = getWalletConnectConnector({
      projectId,
      chains,
      version: walletConnectVersion,
      options: walletConnectOptions
    });
    return {
      connector,
      mobile: {
        getUri: async () => {
          const uri = await getWalletConnectUri(connector, walletConnectVersion);
          return isAndroid() ? uri : `okto://wc?uri=${encodeURIComponent(uri)}`;
        }
      },
      qrCode: {
        getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
        instructions: {
          learnMoreUrl: "https://okto.tech/",
          steps: [
            {
              description: "wallet_connectors.okto.qr_code.step1.description",
              step: "install",
              title: "wallet_connectors.okto.qr_code.step1.title"
            },
            {
              description: "wallet_connectors.okto.qr_code.step2.description",
              step: "create",
              title: "wallet_connectors.okto.qr_code.step2.title"
            },
            {
              description: "wallet_connectors.okto.qr_code.step3.description",
              step: "scan",
              title: "wallet_connectors.okto.qr_code.step3.title"
            }
          ]
        }
      }
    };
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-MG4RCX4W.js
var mewWallet = ({
  chains,
  ...options
}) => {
  var _a;
  const isMewWalletInjected = typeof window !== "undefined" && Boolean((_a = window.ethereum) == null ? void 0 : _a.isMEWwallet);
  return {
    id: "mew",
    name: "MEW wallet",
    iconUrl: async () => (await import("./mewWallet-4ZVF6HCJ-DIRD76UJ.js")).default,
    iconBackground: "#fff",
    installed: isMewWalletInjected,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=com.myetherwallet.mewwallet&referrer=utm_source%3Drainbow",
      ios: "https://apps.apple.com/app/apple-store/id1464614025?pt=118781877&mt=8&ct=rainbow",
      mobile: "https://mewwallet.com",
      qrCode: "https://mewwallet.com"
    },
    createConnector: () => {
      return {
        connector: new InjectedConnector({
          chains,
          options
        })
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-QBOG4TU6.js
var okxWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isOKXInjected = typeof window !== "undefined" && typeof window.okxwallet !== "undefined";
  const shouldUseWalletConnect = !isOKXInjected;
  return {
    id: "okx",
    name: "OKX Wallet",
    iconUrl: async () => (await import("./okxWallet-GKYMI2XW-2JJZDT43.js")).default,
    iconAccent: "#000",
    iconBackground: "#000",
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=com.okinc.okex.gp",
      ios: "https://itunes.apple.com/app/id1327268470?mt=8",
      mobile: "https://okx.com/download",
      qrCode: "https://okx.com/download",
      chrome: "https://chrome.google.com/webstore/detail/okx-wallet/mcohilncbfahbmgdjkbpemcciiolgcge",
      edge: "https://microsoftedge.microsoft.com/addons/detail/okx-wallet/pbpjkcldjiffchgbbndmhojiacbgflha",
      firefox: "https://addons.mozilla.org/firefox/addon/okexwallet/",
      browserExtension: "https://okx.com/download"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : new InjectedConnector({
        chains,
        options: {
          getProvider: () => window.okxwallet,
          ...options
        }
      });
      return {
        connector,
        mobile: {
          getUri: shouldUseWalletConnect ? async () => {
            const uri = await getWalletConnectUri(connector, walletConnectVersion);
            return isAndroid() ? uri : `okex://main/wc?uri=${encodeURIComponent(uri)}`;
          } : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
          instructions: {
            learnMoreUrl: "https://okx.com/web3/",
            steps: [
              {
                description: "wallet_connectors.okx.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.okx.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.okx.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.okx.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.okx.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.okx.qr_code.step3.title"
              }
            ]
          }
        } : void 0,
        extension: {
          instructions: {
            learnMoreUrl: "https://okx.com/web3/",
            steps: [
              {
                description: "wallet_connectors.okx.extension.step1.description",
                step: "install",
                title: "wallet_connectors.okx.extension.step1.title"
              },
              {
                description: "wallet_connectors.okx.extension.step2.description",
                step: "create",
                title: "wallet_connectors.okx.extension.step2.title"
              },
              {
                description: "wallet_connectors.okx.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.okx.extension.step3.title"
              }
            ]
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-7VAS62IJ.js
var omniWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2"
}) => ({
  id: "omni",
  name: "Omni",
  iconUrl: async () => (await import("./omniWallet-VF54LPLK-66432FWW.js")).default,
  iconBackground: "#000",
  downloadUrls: {
    android: "https://play.google.com/store/apps/details?id=fi.steakwallet.app",
    ios: "https://itunes.apple.com/us/app/id1569375204",
    mobile: "https://omniwallet.app.link",
    qrCode: "https://omniwallet.app.link"
  },
  createConnector: () => {
    const connector = getWalletConnectConnector({
      projectId,
      chains,
      version: walletConnectVersion,
      options: walletConnectOptions
    });
    return {
      connector,
      mobile: {
        getUri: async () => {
          const uri = await getWalletConnectUri(connector, walletConnectVersion);
          return isAndroid() ? uri : `omni://wc?uri=${encodeURIComponent(uri)}`;
        }
      },
      qrCode: {
        getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
        instructions: {
          learnMoreUrl: "https://omni.app/support",
          steps: [
            {
              description: "wallet_connectors.omni.qr_code.step1.description",
              step: "install",
              title: "wallet_connectors.omni.qr_code.step1.title"
            },
            {
              description: "wallet_connectors.omni.qr_code.step2.description",
              step: "create",
              title: "wallet_connectors.omni.qr_code.step2.title"
            },
            {
              description: "wallet_connectors.omni.qr_code.step3.description",
              step: "scan",
              title: "wallet_connectors.omni.qr_code.step3.title"
            }
          ]
        }
      }
    };
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-ILKXCMW2.js
var bitgetWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isBitKeepInjected = typeof window !== "undefined" && window.bitkeep !== void 0 && window.bitkeep.ethereum !== void 0 && window.bitkeep.ethereum.isBitKeep === true;
  const shouldUseWalletConnect = !isBitKeepInjected;
  return {
    id: "bitget",
    name: "Bitget Wallet",
    iconUrl: async () => (await import("./bitgetWallet-JVNCB4EB-W6KLT5BM.js")).default,
    iconAccent: "#f6851a",
    iconBackground: "#fff",
    installed: !shouldUseWalletConnect ? isBitKeepInjected : void 0,
    downloadUrls: {
      android: "https://web3.bitget.com/en/wallet-download?type=0",
      ios: "https://apps.apple.com/app/bitkeep/id1395301115",
      mobile: "https://web3.bitget.com/en/wallet-download?type=2",
      qrCode: "https://web3.bitget.com/en/wallet-download",
      chrome: "https://chrome.google.com/webstore/detail/bitkeep-crypto-nft-wallet/jiidiaalihmmhddjgbnbgdfflelocpak",
      browserExtension: "https://web3.bitget.com/en/wallet-download"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        chains,
        options: walletConnectOptions,
        projectId,
        version: walletConnectVersion
      }) : new InjectedConnector({
        chains,
        options: {
          getProvider: () => window.bitkeep.ethereum,
          ...options
        }
      });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return isAndroid() ? uri : `bitkeep://wc?uri=${encodeURIComponent(uri)}`;
      };
      return {
        connector,
        extension: {
          instructions: {
            learnMoreUrl: "https://web3.bitget.com/en/academy",
            steps: [
              {
                description: "wallet_connectors.bitget.extension.step1.description",
                step: "install",
                title: "wallet_connectors.bitget.extension.step1.title"
              },
              {
                description: "wallet_connectors.bitget.extension.step2.description",
                step: "create",
                title: "wallet_connectors.bitget.extension.step2.title"
              },
              {
                description: "wallet_connectors.bitget.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.bitget.extension.step3.description"
              }
            ]
          }
        },
        mobile: {
          getUri: shouldUseWalletConnect ? getUri : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
          instructions: {
            learnMoreUrl: "https://web3.bitget.com/en/academy",
            steps: [
              {
                description: "wallet_connectors.bitget.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.bitget.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.bitget.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.bitget.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.bitget.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.bitget.qr_code.step3.title"
              }
            ]
          }
        } : void 0
      };
    }
  };
};
var bitKeepWallet = bitgetWallet;

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-JGG2V4XA.js
var desigWallet = ({
  chains,
  ...options
}) => {
  var _a;
  return {
    id: "desig",
    name: "Desig Wallet",
    iconUrl: async () => (await import("./desigWallet-YZ4ZDEYW-W6NTZRJZ.js")).default,
    iconBackground: "#ffffff",
    installed: typeof window !== "undefined" && !!((_a = window == null ? void 0 : window.desig) == null ? void 0 : _a.ethereum) || void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=io.desig.app",
      ios: "https://apps.apple.com/app/desig-wallet/id6450106028",
      qrCode: "https://desig.io",
      mobile: "https://desig.io",
      browserExtension: "https://chrome.google.com/webstore/detail/desig-wallet/panpgppehdchfphcigocleabcmcgfoca"
    },
    createConnector: () => {
      const getProvider = () => {
        var _a2;
        return typeof window !== "undefined" ? (_a2 = window.desig) == null ? void 0 : _a2.ethereum : void 0;
      };
      const connector = new InjectedConnector({
        chains,
        options: { getProvider, ...options }
      });
      return {
        connector,
        extension: {
          instructions: {
            steps: [
              {
                description: "wallet_connectors.desig.extension.step1.description",
                step: "install",
                title: "wallet_connectors.desig.extension.step1.title"
              },
              {
                description: "wallet_connectors.desig.extension.step2.description",
                step: "create",
                title: "wallet_connectors.desig.extension.step2.title"
              },
              {
                description: "wallet_connectors.desig.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.desig.extension.step3.title"
              }
            ],
            learnMoreUrl: "https://desig.io"
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-ZAV235NL.js
var bitskiWallet = ({
  chains,
  ...options
}) => {
  var _a;
  return {
    id: "bitski",
    name: "Bitski",
    installed: typeof window !== "undefined" && typeof window.ethereum !== "undefined" && (window.ethereum.isBitski === true || !!((_a = window.ethereum.providers) == null ? void 0 : _a.find((p) => p.isBitski === true))),
    iconUrl: async () => (await import("./bitskiWallet-Y4QTLQPQ-WTKDCWMG.js")).default,
    iconBackground: "#fff",
    downloadUrls: {
      chrome: "https://chrome.google.com/webstore/detail/bitski/feejiigddaafeojfddjjlmfkabimkell",
      browserExtension: "https://bitski.com"
    },
    createConnector: () => ({
      connector: new InjectedConnector({
        chains,
        options
      }),
      extension: {
        instructions: {
          learnMoreUrl: "https://bitski.zendesk.com/hc/articles/12803972818836-How-to-install-the-Bitski-browser-extension",
          steps: [
            {
              description: "wallet_connectors.bitski.extension.step1.description",
              step: "install",
              title: "wallet_connectors.bitski.extension.step1.title"
            },
            {
              description: "wallet_connectors.bitski.extension.step2.description",
              step: "create",
              title: "wallet_connectors.bitski.extension.step2.title"
            },
            {
              description: "wallet_connectors.bitski.extension.step3.description",
              step: "refresh",
              title: "wallet_connectors.bitski.extension.step3.title"
            }
          ]
        }
      }
    })
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-3ZFYANJH.js
var enkryptWallet = ({
  chains,
  ...options
}) => {
  var _a, _b;
  const isEnkryptInjected = typeof window !== "undefined" && typeof window.enkrypt !== "undefined" && ((_b = (_a = window == null ? void 0 : window.enkrypt) == null ? void 0 : _a.providers) == null ? void 0 : _b.ethereum);
  return {
    id: "enkrypt",
    name: "Enkrypt Wallet",
    installed: isEnkryptInjected ? true : void 0,
    iconUrl: async () => (await import("./enkryptWallet-LVMJVNXI-67JND4CN.js")).default,
    iconBackground: "#FFFFFF",
    downloadUrls: {
      qrCode: "https://www.enkrypt.com",
      chrome: "https://chrome.google.com/webstore/detail/enkrypt-ethereum-polkadot/kkpllkodjeloidieedojogacfhpaihoh",
      browserExtension: "https://www.enkrypt.com/",
      edge: "https://microsoftedge.microsoft.com/addons/detail/enkrypt-ethereum-polkad/gfenajajnjjmmdojhdjmnngomkhlnfjl",
      firefox: "https://addons.mozilla.org/en-US/firefox/addon/enkrypt/",
      opera: "https://addons.opera.com/en/extensions/details/enkrypt/",
      safari: "https://apps.apple.com/app/enkrypt-web3-wallet/id1640164309"
    },
    createConnector: () => {
      return {
        connector: new InjectedConnector({
          chains,
          options: {
            getProvider: () => {
              var _a2, _b2;
              return isEnkryptInjected ? (_b2 = (_a2 = window == null ? void 0 : window.enkrypt) == null ? void 0 : _a2.providers) == null ? void 0 : _b2.ethereum : void 0;
            },
            ...options
          }
        }),
        extension: {
          instructions: {
            learnMoreUrl: "https://blog.enkrypt.com/what-is-a-web3-wallet/",
            steps: [
              {
                description: "wallet_connectors.enkrypt.extension.step1.description",
                step: "install",
                title: "wallet_connectors.enkrypt.extension.step1.title"
              },
              {
                description: "wallet_connectors.enkrypt.extension.step2.description",
                step: "create",
                title: "wallet_connectors.enkrypt.extension.step2.title"
              },
              {
                description: "wallet_connectors.enkrypt.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.enkrypt.extension.step3.title"
              }
            ]
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-QJNS6IE4.js
var dawnWallet = ({
  chains,
  ...options
}) => ({
  id: "dawn",
  name: "Dawn",
  iconUrl: async () => (await import("./dawnWallet-MN7QMTX3-HZNRBGSM.js")).default,
  iconBackground: "#000000",
  installed: typeof window !== "undefined" && typeof window.ethereum !== "undefined" && window.ethereum.isDawn,
  hidden: () => !isIOS(),
  downloadUrls: {
    ios: "https://apps.apple.com/us/app/dawn-ethereum-wallet/id1673143782",
    mobile: "https://dawnwallet.xyz"
  },
  createConnector: () => ({
    connector: new InjectedConnector({
      chains,
      options
    })
  })
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-ARXBG5HI.js
var foxWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isFoxInjected = typeof window !== "undefined" && typeof window.foxwallet !== "undefined";
  const shouldUseWalletConnect = !isFoxInjected;
  return {
    id: "foxwallet",
    name: "FoxWallet",
    iconUrl: async () => (await import("./foxWallet-RFPGZZOK-XHHNWSAK.js")).default,
    iconBackground: "#fff",
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=com.foxwallet.play",
      ios: "https://apps.apple.com/app/foxwallet-crypto-web3/id1590983231",
      qrCode: "https://foxwallet.com/download"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : new InjectedConnector({
        chains,
        options: {
          getProvider: () => window.foxwallet.ethereum,
          ...options
        }
      });
      return {
        connector,
        mobile: {
          getUri: shouldUseWalletConnect ? async () => {
            const uri = await getWalletConnectUri(connector, walletConnectVersion);
            return `foxwallet://wc?uri=${encodeURIComponent(uri)}`;
          } : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
          instructions: {
            learnMoreUrl: "https://foxwallet.com",
            steps: [
              {
                description: "wallet_connectors.fox.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.fox.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.fox.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.fox.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.fox.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.fox.qr_code.step3.title"
              }
            ]
          }
        } : void 0
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-PWJ32OLJ.js
var frameWallet = ({
  chains,
  ...options
}) => {
  var _a;
  return {
    id: "frame",
    name: "Frame",
    installed: typeof window !== "undefined" && typeof window.ethereum !== "undefined" && (window.ethereum.isFrame === true || !!((_a = window.ethereum.providers) == null ? void 0 : _a.find((p) => p.isFrame === true))),
    iconUrl: async () => (await import("./frameWallet-J2WUL2NQ-MVFMPOUB.js")).default,
    iconBackground: "#121C20",
    downloadUrls: {
      browserExtension: "https://frame.sh/"
    },
    createConnector: () => ({
      connector: new InjectedConnector({
        chains,
        options
      }),
      extension: {
        instructions: {
          learnMoreUrl: "https://docs.frame.sh/docs/Getting%20Started/Installation/",
          steps: [
            {
              description: "wallet_connectors.frame.extension.step1.description",
              step: "install",
              title: "wallet_connectors.frame.extension.step1.title"
            },
            {
              description: "wallet_connectors.frame.extension.step2.description",
              step: "create",
              title: "wallet_connectors.frame.extension.step2.title"
            },
            {
              description: "wallet_connectors.frame.extension.step3.description",
              step: "refresh",
              title: "wallet_connectors.frame.extension.step3.title"
            }
          ]
        }
      }
    })
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-SXH7BZQ3.js
var frontierWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  var _a, _b, _c, _d;
  const isFrontierInjected = typeof window !== "undefined" && typeof window.frontier !== "undefined" && ((_b = (_a = window == null ? void 0 : window.frontier) == null ? void 0 : _a.ethereum) == null ? void 0 : _b.isFrontier);
  return {
    id: "frontier",
    name: "Frontier Wallet",
    installed: typeof window !== "undefined" && typeof window.frontier !== "undefined" && ((_d = (_c = window == null ? void 0 : window.frontier) == null ? void 0 : _c.ethereum) == null ? void 0 : _d.isFrontier) ? true : void 0,
    iconUrl: async () => (await import("./frontierWallet-3CNZ2ST5-BUUI6NVJ.js")).default,
    iconBackground: "#CC703C",
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=com.frontierwallet",
      ios: "https://apps.apple.com/us/app/frontier-crypto-defi-wallet/id1482380988",
      qrCode: "https://www.frontier.xyz/download",
      chrome: "https://chrome.google.com/webstore/detail/frontier-wallet/kppfdiipphfccemcignhifpjkapfbihd",
      browserExtension: "https://www.frontier.xyz/download"
    },
    createConnector: () => {
      const shouldUseWalletConnect = !isFrontierInjected;
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        chains,
        projectId,
        options: walletConnectOptions,
        version: walletConnectVersion
      }) : new InjectedConnector({ chains });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return isAndroid() ? `frontier://wc?uri=${encodeURIComponent(uri)}` : uri;
      };
      return {
        connector: new InjectedConnector({
          chains,
          options: {
            getProvider: () => {
              const getFront = (frontier) => (frontier == null ? void 0 : frontier.ethereum) ? frontier == null ? void 0 : frontier.ethereum : void 0;
              if (typeof window === "undefined")
                return;
              return getFront(window.frontier);
            },
            ...options
          }
        }),
        mobile: {
          getUri: shouldUseWalletConnect ? getUri : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri,
          instructions: {
            learnMoreUrl: "https://help.frontier.xyz/en/",
            steps: [
              {
                description: "wallet_connectors.im_token.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.im_token.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.im_token.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.im_token.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.im_token.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.im_token.qr_code.step3.title"
              }
            ]
          }
        } : void 0,
        extension: {
          instructions: {
            learnMoreUrl: "https://help.frontier.xyz/en/articles/6967236-setting-up-frontier-on-your-device",
            steps: [
              {
                description: "wallet_connectors.frontier.extension.step1.description",
                step: "install",
                title: "wallet_connectors.frontier.extension.step1.title"
              },
              {
                description: "wallet_connectors.frontier.extension.step2.description",
                step: "create",
                title: "wallet_connectors.frontier.extension.step2.title"
              },
              {
                description: "wallet_connectors.frontier.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.frontier.extension.step3.title"
              }
            ]
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-6D5IWTK5.js
var argentWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2"
}) => ({
  id: "argent",
  name: "Argent",
  iconUrl: async () => (await import("./argentWallet-5OEFC7BD-7IOB4RZF.js")).default,
  iconBackground: "#fff",
  downloadUrls: {
    android: "https://play.google.com/store/apps/details?id=im.argent.contractwalletclient",
    ios: "https://apps.apple.com/us/app/argent/id1358741926",
    mobile: "https://argent.xyz/download-argent",
    qrCode: "https://argent.link/app"
  },
  createConnector: () => {
    const connector = getWalletConnectConnector({
      projectId,
      chains,
      version: walletConnectVersion,
      options: walletConnectOptions
    });
    return {
      connector,
      mobile: {
        getUri: async () => {
          const uri = await getWalletConnectUri(connector, walletConnectVersion);
          return isAndroid() ? uri : `argent://app/wc?uri=${encodeURIComponent(uri)}`;
        }
      },
      qrCode: {
        getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
        instructions: {
          learnMoreUrl: "https://argent.xyz/learn/what-is-a-crypto-wallet/",
          steps: [
            {
              description: "wallet_connectors.argent.qr_code.step1.description",
              step: "install",
              title: "wallet_connectors.argent.qr_code.step1.title"
            },
            {
              description: "wallet_connectors.argent.qr_code.step2.description",
              step: "create",
              title: "wallet_connectors.argent.qr_code.step2.title"
            },
            {
              description: "wallet_connectors.argent.qr_code.step3.description",
              step: "scan",
              title: "wallet_connectors.argent.qr_code.step3.title"
            }
          ]
        }
      }
    };
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-FJC3HGLW.js
var clvWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2"
}) => {
  const provider = typeof window !== "undefined" && window["clover"];
  const isCLVInjected = Boolean(provider);
  const shouldUseWalletConnect = !isCLVInjected;
  return {
    id: "clv",
    name: "CLV",
    iconUrl: async () => (await import("./clvWallet-RM4V57ZB-X5WYRB63.js")).default,
    iconBackground: "#fff",
    iconAccent: "#BDFDE2",
    installed: !shouldUseWalletConnect ? isCLVInjected : void 0,
    downloadUrls: {
      chrome: "https://chrome.google.com/webstore/detail/clv-wallet/nhnkbkgjikgcigadomkphalanndcapjk",
      ios: "https://apps.apple.com/app/clover-wallet/id1570072858",
      mobile: "https://apps.apple.com/app/clover-wallet/id1570072858",
      qrCode: "https://clv.org/"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        chains,
        options: walletConnectOptions,
        projectId,
        version: walletConnectVersion
      }) : new InjectedConnector({
        chains,
        options: {
          getProvider: () => provider
        }
      });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, "2");
        return uri;
      };
      return {
        connector,
        extension: {
          instructions: {
            learnMoreUrl: "https://clv.org/",
            steps: [
              {
                description: "wallet_connectors.clv.extension.step1.description",
                step: "install",
                title: "wallet_connectors.clv.extension.step1.title"
              },
              {
                description: "wallet_connectors.clv.extension.step2.description",
                step: "create",
                title: "wallet_connectors.clv.extension.step2.title"
              },
              {
                description: "wallet_connectors.clv.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.clv.extension.step3.title"
              }
            ]
          }
        },
        mobile: {
          getUri: shouldUseWalletConnect ? getUri : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
          instructions: {
            learnMoreUrl: "https://clv.org/",
            steps: [
              {
                description: "wallet_connectors.clv.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.clv.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.clv.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.clv.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.clv.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.clv.qr_code.step3.title"
              }
            ]
          }
        } : void 0
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-HXHIVLJJ.js
var bifrostWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isBifrostInjected = typeof window !== "undefined" && typeof window.ethereum !== "undefined" && window.ethereum.isBifrost;
  const shouldUseWalletConnect = !isBifrostInjected;
  return {
    id: "bifrostWallet",
    name: "Bifrost Wallet",
    iconUrl: async () => (await import("./bifrostWallet-5VPKXMCJ-XY56WZQE.js")).default,
    iconBackground: "#fff",
    installed: !shouldUseWalletConnect ? isBifrostInjected : void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=com.bifrostwallet.app",
      ios: "https://apps.apple.com/us/app/bifrost-wallet/id1577198351",
      qrCode: "https://bifrostwallet.com/#download-app"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        chains,
        projectId,
        options: walletConnectOptions,
        version: walletConnectVersion
      }) : new InjectedConnector({
        chains,
        options
      });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return isAndroid() ? uri : `https://app.bifrostwallet.com/wc?uri=${encodeURIComponent(uri)}`;
      };
      return {
        connector,
        mobile: {
          getUri: shouldUseWalletConnect ? getUri : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri: async () => getWalletConnectUri(connector, walletConnectVersion),
          instructions: {
            learnMoreUrl: "https://support.bifrostwallet.com/en/articles/6886814-how-to-use-walletconnect",
            steps: [
              {
                description: "wallet_connectors.bifrost.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.bifrost.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.bifrost.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.bifrost.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.bifrost.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.bifrost.qr_code.step3.title"
              }
            ]
          }
        } : void 0
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-I5IK77LU.js
var braveWallet = ({
  chains,
  ...options
}) => {
  var _a;
  return {
    id: "brave",
    name: "Brave Wallet",
    iconUrl: async () => (await import("./braveWallet-BTBH4MDN-UOR55DBD.js")).default,
    iconBackground: "#fff",
    installed: typeof window !== "undefined" && ((_a = window.ethereum) == null ? void 0 : _a.isBraveWallet) === true,
    downloadUrls: {},
    createConnector: () => ({
      connector: new InjectedConnector({
        chains,
        options
      })
    })
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-SUTSTKXS.js
function getCoin98WalletInjectedProvider() {
  var _a;
  const isCoin98Wallet = (ethereum) => {
    const coin98Wallet2 = !!ethereum.isCoin98;
    return coin98Wallet2;
  };
  const injectedProviderExist = typeof window !== "undefined" && typeof window.ethereum !== "undefined";
  if (!injectedProviderExist) {
    return;
  }
  if (window["coin98Wallet"]) {
    return window["coin98Wallet"];
  }
  if (isCoin98Wallet(window.ethereum)) {
    return window.ethereum;
  }
  if ((_a = window.ethereum) == null ? void 0 : _a.providers) {
    return window.ethereum.providers.find(isCoin98Wallet);
  }
}
var coin98Wallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isCoin98WalletInjected = Boolean(getCoin98WalletInjectedProvider());
  const shouldUseWalletConnect = !isCoin98WalletInjected;
  return {
    id: "coin98",
    name: "Coin98 Wallet",
    iconUrl: async () => (await import("./coin98Wallet-7Q4WNBWR-DY5SXTXP.js")).default,
    installed: !shouldUseWalletConnect ? isCoin98WalletInjected : void 0,
    iconAccent: "#CDA349",
    iconBackground: "#fff",
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=coin98.crypto.finance.media",
      ios: "https://apps.apple.com/vn/app/coin98-super-app/id1561969966",
      mobile: "https://coin98.com/wallet",
      qrCode: "https://coin98.com/wallet",
      chrome: "https://chrome.google.com/webstore/detail/coin98-wallet/aeachknmefphepccionboohckonoeemg",
      browserExtension: "https://coin98.com/wallet"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        options: walletConnectOptions,
        version: walletConnectVersion
      }) : new InjectedConnector({
        chains,
        options: {
          name: "Coin98 Wallet",
          getProvider: getCoin98WalletInjectedProvider,
          ...options
        }
      });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return uri;
      };
      return {
        connector,
        mobile: { getUri: shouldUseWalletConnect ? getUri : void 0 },
        qrCode: shouldUseWalletConnect ? {
          getUri,
          instructions: {
            learnMoreUrl: "https://coin98.com/wallet",
            steps: [
              {
                description: "wallet_connectors.coin98.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.coin98.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.coin98.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.coin98.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.coin98.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.coin98.qr_code.step3.title"
              }
            ]
          }
        } : void 0,
        extension: {
          instructions: {
            learnMoreUrl: "https://coin98.com/wallet",
            steps: [
              {
                description: "wallet_connectors.coin98.extension.step1.description",
                step: "install",
                title: "wallet_connectors.coin98.extension.step1.title"
              },
              {
                description: "wallet_connectors.coin98.extension.step2.description",
                step: "create",
                title: "wallet_connectors.coin98.extension.step2.title"
              },
              {
                description: "wallet_connectors.coin98.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.coin98.extension.step3.title"
              }
            ]
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-XDNOHDO2.js
var coinbaseWallet = ({
  appName,
  chains,
  ...options
}) => {
  const isCoinbaseWalletInjected = hasInjectedProvider("isCoinbaseWallet");
  return {
    id: "coinbase",
    name: "Coinbase Wallet",
    shortName: "Coinbase",
    iconUrl: async () => (await import("./coinbaseWallet-2OUR5TUP-ZQGNX7YR.js")).default,
    iconAccent: "#2c5ff6",
    iconBackground: "#2c5ff6",
    installed: isCoinbaseWalletInjected || void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=org.toshi",
      ios: "https://apps.apple.com/us/app/coinbase-wallet-store-crypto/id1278383455",
      mobile: "https://coinbase.com/wallet/downloads",
      qrCode: "https://coinbase-wallet.onelink.me/q5Sx/fdb9b250",
      chrome: "https://chrome.google.com/webstore/detail/coinbase-wallet-extension/hnfanknocfeofbddgcijnmhnfnkdnaad",
      browserExtension: "https://coinbase.com/wallet"
    },
    createConnector: () => {
      const ios = isIOS();
      const connector = new CoinbaseWalletConnector({
        chains,
        options: {
          appName,
          headlessMode: true,
          ...options
        }
      });
      const getUri = async () => (await connector.getProvider()).qrUrl;
      return {
        connector,
        ...ios ? {} : {
          qrCode: {
            getUri,
            instructions: {
              learnMoreUrl: "https://coinbase.com/wallet/articles/getting-started-mobile",
              steps: [
                {
                  description: "wallet_connectors.coinbase.qr_code.step1.description",
                  step: "install",
                  title: "wallet_connectors.coinbase.qr_code.step1.title"
                },
                {
                  description: "wallet_connectors.coinbase.qr_code.step2.description",
                  step: "create",
                  title: "wallet_connectors.coinbase.qr_code.step2.title"
                },
                {
                  description: "wallet_connectors.coinbase.qr_code.step3.description",
                  step: "scan",
                  title: "wallet_connectors.coinbase.qr_code.step3.title"
                }
              ]
            }
          },
          extension: {
            instructions: {
              learnMoreUrl: "https://coinbase.com/wallet/articles/getting-started-extension",
              steps: [
                {
                  description: "wallet_connectors.coinbase.extension.step1.description",
                  step: "install",
                  title: "wallet_connectors.coinbase.extension.step1.title"
                },
                {
                  description: "wallet_connectors.coinbase.extension.step2.description",
                  step: "create",
                  title: "wallet_connectors.coinbase.extension.step2.title"
                },
                {
                  description: "wallet_connectors.coinbase.extension.step3.description",
                  step: "refresh",
                  title: "wallet_connectors.coinbase.extension.step3.title"
                }
              ]
            }
          }
        }
      };
    }
  };
};

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/chunk-VNY6Z7PN.js
function getCoreWalletInjectedProvider() {
  var _a, _b;
  const injectedProviderExist = typeof window !== "undefined" && typeof window.ethereum !== "undefined";
  if (!injectedProviderExist) {
    return;
  }
  if ((_a = window["evmproviders"]) == null ? void 0 : _a["core"]) {
    return (_b = window["evmproviders"]) == null ? void 0 : _b["core"];
  }
  if (window.avalanche) {
    return window.avalanche;
  }
  if (typeof window !== "undefined" && typeof window.ethereum !== "undefined" && window.ethereum.isAvalanche === true) {
    return window.ethereum;
  }
}
var coreWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isCoreInjected = Boolean(getCoreWalletInjectedProvider());
  const shouldUseWalletConnect = !isCoreInjected;
  return {
    id: "core",
    name: "Core",
    iconUrl: async () => (await import("./coreWallet-HRVLR2XS-A5ZI2RZN.js")).default,
    iconBackground: "#1A1A1C",
    installed: !shouldUseWalletConnect ? isCoreInjected : void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=com.avaxwallet",
      ios: "https://apps.apple.com/us/app/core-wallet/id6443685999",
      mobile: "https://core.app/?downloadCoreMobile=1",
      qrCode: "https://core.app/?downloadCoreMobile=1",
      chrome: "https://chrome.google.com/webstore/detail/core-crypto-wallet-nft-ex/agoakfejjabomempkjlepdflaleeobhb",
      browserExtension: "https://extension.core.app/"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        options: walletConnectOptions,
        version: walletConnectVersion
      }) : new InjectedConnector({
        chains,
        options: {
          getProvider: getCoreWalletInjectedProvider,
          ...options
        }
      });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return uri;
      };
      return {
        connector,
        mobile: { getUri: shouldUseWalletConnect ? getUri : void 0 },
        qrCode: shouldUseWalletConnect ? {
          getUri,
          instructions: {
            learnMoreUrl: "https://support.avax.network/en/articles/6115608-core-mobile-how-to-add-the-core-mobile-to-my-phone",
            steps: [
              {
                description: "wallet_connectors.core.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.core.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.core.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.core.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.core.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.core.qr_code.step3.title"
              }
            ]
          }
        } : void 0,
        extension: {
          instructions: {
            learnMoreUrl: "https://extension.core.app/",
            steps: [
              {
                description: "wallet_connectors.core.extension.step1.description",
                step: "install",
                title: "wallet_connectors.core.extension.step1.title"
              },
              {
                description: "wallet_connectors.core.extension.step2.description",
                step: "create",
                title: "wallet_connectors.core.extension.step2.title"
              },
              {
                description: "wallet_connectors.core.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.core.extension.step3.title"
              }
            ]
          }
        }
      };
    }
  };
};
export {
  argentWallet,
  bifrostWallet,
  bitKeepWallet,
  bitgetWallet,
  bitskiWallet,
  braveWallet,
  clvWallet,
  coin98Wallet,
  coinbaseWallet,
  coreWallet,
  dawnWallet,
  desigWallet,
  enkryptWallet,
  foxWallet,
  frameWallet,
  frontierWallet,
  imTokenWallet,
  injectedWallet,
  ledgerWallet,
  metaMaskWallet,
  mewWallet,
  oktoWallet,
  okxWallet,
  omniWallet,
  oneKeyWallet,
  phantomWallet,
  rabbyWallet,
  rainbowWallet,
  safeWallet,
  safeheronWallet,
  safepalWallet,
  subWallet,
  tahoWallet,
  talismanWallet,
  tokenPocketWallet,
  tokenaryWallet,
  trustWallet,
  uniswapWallet,
  walletConnectWallet,
  xdefiWallet,
  zealWallet,
  zerionWallet
};
//# sourceMappingURL=@rainbow-me_rainbowkit_wallets.js.map
