{"version": 3, "sources": ["../../@wagmi/core/dist/providers/public.js"], "sourcesContent": ["import \"../chunk-MQXBDTVK.js\";\n\n// src/providers/public.ts\nfunction publicProvider() {\n  return function(chain) {\n    if (!chain.rpcUrls.public.http[0])\n      return null;\n    return {\n      chain,\n      rpcUrls: chain.rpcUrls.public\n    };\n  };\n}\nexport {\n  publicProvider\n};\n"], "mappings": ";;;;;AAGA,SAAS,iBAAiB;AACxB,SAAO,SAAS,OAAO;AACrB,QAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,CAAC;AAC9B,aAAO;AACT,WAAO;AAAA,MACL;AAAA,MACA,SAAS,MAAM,QAAQ;AAAA,IACzB;AAAA,EACF;AACF;", "names": []}