"use client";
import "./chunk-W7S2ME4R.js";

// node_modules/@rainbow-me/rainbowkit/dist/pt_BR-JDDVMLRA.js
var pt_BR_default = '{\n  "connect_wallet": {\n    "label": "Conectar Carteira",\n    "wrong_network": {\n      "label": "Rede incorreta"\n    }\n  },\n  "intro": {\n    "title": "O que é uma Carteira?",\n    "description": "Uma carteira é usada para enviar, receber, armazenar e exibir ativos digitais. Também é uma nova forma de se conectar, sem precisar criar novas contas e senhas em todo site.",\n    "digital_asset": {\n      "title": "Um lar para seus ativos digitais",\n      "description": "Carteiras são usadas para enviar, receber, armazenar e exibir ativos digitais como Ethereum e NFTs."\n    },\n    "login": {\n      "title": "Uma nova maneira de fazer login",\n      "description": "Em vez de criar novas contas e senhas em todos os sites, basta conectar sua carteira."\n    },\n    "get": {\n      "label": "Obter uma Carteira"\n    },\n    "learn_more": {\n      "label": "Saiba mais"\n    }\n  },\n  "sign_in": {\n    "label": "Verifique sua conta",\n    "description": "Para concluir a conexão, você deve assinar uma mensagem em sua carteira para confirmar que você é o proprietário desta conta.",\n    "message": {\n      "send": "Enviar mensagem",\n      "preparing": "Preparando mensagem...",\n      "cancel": "Cancelar",\n      "preparing_error": "Erro ao preparar a mensagem, tente novamente!"\n    },\n    "signature": {\n      "waiting": "Aguardando assinatura...",\n      "verifying": "Verificando assinatura...",\n      "signing_error": "Erro ao assinar a mensagem, tente novamente!",\n      "verifying_error": "Erro ao verificar assinatura, tente novamente!",\n      "oops_error": "Ops, algo deu errado!"\n    }\n  },\n  "connect": {\n    "label": "Conectar",\n    "title": "Conectar uma Carteira",\n    "new_to_ethereum": {\n      "description": "Novo nas carteiras Ethereum?",\n      "learn_more": {\n        "label": "Saiba mais"\n      }\n    },\n    "learn_more": {\n      "label": "Saiba mais"\n    },\n    "recent": "Recente",\n    "status": {\n      "opening": "Abrindo %{wallet}...",\n      "connecting": "Conectando",\n      "connect_mobile": "Continue em %{wallet}",\n      "not_installed": "%{wallet} não está instalado",\n      "not_available": "%{wallet} não está disponível",\n      "confirm": "Confirme a conexão na extensão",\n      "confirm_mobile": "Aceite o pedido de conexão na carteira"\n    },\n    "secondary_action": {\n      "get": {\n        "description": "Não tem %{wallet}?",\n        "label": "OBTER"\n      },\n      "install": {\n        "label": "INSTALAR"\n      },\n      "retry": {\n        "label": "TENTAR DE NOVO"\n      }\n    },\n    "walletconnect": {\n      "description": {\n        "full": "Precisa do modal oficial do WalletConnect?",\n        "compact": "Precisa do modal WalletConnect?"\n      },\n      "open": {\n        "label": "ABRIR"\n      }\n    }\n  },\n  "connect_scan": {\n    "title": "Digitalize com %{wallet}",\n    "fallback_title": "Digitalize com o seu telefone"\n  },\n  "connector_group": {\n    "recommended": "Recomendado",\n    "other": "Outro",\n    "popular": "Popular",\n    "more": "Mais",\n    "others": "Outros"\n  },\n  "get": {\n    "title": "Obter uma Carteira",\n    "action": {\n      "label": "OBTER"\n    },\n    "mobile": {\n      "description": "Carteira Móvel"\n    },\n    "extension": {\n      "description": "Extensão do Navegador"\n    },\n    "mobile_and_extension": {\n      "description": "Carteira Móvel e Extensão"\n    },\n    "mobile_and_desktop": {\n      "description": "Carteira para Mobile e Desktop"\n    },\n    "looking_for": {\n      "title": "Não é o que você está procurando?",\n      "mobile": {\n        "description": "Selecione uma carteira na tela principal para começar com um provedor de carteira diferente."\n      },\n      "desktop": {\n        "compact_description": "Selecione uma carteira na tela principal para começar com um provedor de carteira diferente.",\n        "wide_description": "Selecione uma carteira à esquerda para começar com um provedor de carteira diferente."\n      }\n    }\n  },\n  "get_options": {\n    "title": "Comece com %{wallet}",\n    "short_title": "Obtenha %{wallet}",\n    "mobile": {\n      "title": "%{wallet} para Móvel",\n      "description": "Use a carteira móvel para explorar o mundo do Ethereum.",\n      "download": {\n        "label": "Baixe o aplicativo"\n      }\n    },\n    "extension": {\n      "title": "%{wallet} para %{browser}",\n      "description": "Acesse sua carteira diretamente do seu navegador web favorito.",\n      "download": {\n        "label": "Adicionar ao %{browser}"\n      }\n    },\n    "desktop": {\n      "title": "%{wallet} para %{platform}",\n      "description": "Acesse sua carteira nativamente do seu desktop poderoso.",\n      "download": {\n        "label": "Adicionar ao %{platform}"\n      }\n    }\n  },\n  "get_mobile": {\n    "title": "Instale %{wallet}",\n    "description": "Escaneie com seu celular para baixar no iOS ou Android",\n    "continue": {\n      "label": "Continuar"\n    }\n  },\n  "get_instructions": {\n    "mobile": {\n      "connect": {\n        "label": "Conectar"\n      },\n      "learn_more": {\n        "label": "Saiba mais"\n      }\n    },\n    "extension": {\n      "refresh": {\n        "label": "Atualizar"\n      },\n      "learn_more": {\n        "label": "Saiba mais"\n      }\n    },\n    "desktop": {\n      "connect": {\n        "label": "Conectar"\n      },\n      "learn_more": {\n        "label": "Saiba mais"\n      }\n    }\n  },\n  "chains": {\n    "title": "Mudar Redes",\n    "wrong_network": "Rede errada detectada, mude ou desconecte para continuar.",\n    "confirm": "Confirme na Carteira",\n    "switching_not_supported": "Sua carteira não suporta a mudança de redes de %{appName}. Tente mudar de redes dentro da sua carteira.",\n    "switching_not_supported_fallback": "Sua carteira não suporta a troca de redes a partir deste aplicativo. Tente trocar de rede dentro de sua carteira.",\n    "disconnect": "Desconectar",\n    "connected": "Conectado"\n  },\n  "profile": {\n    "disconnect": {\n      "label": "Desconectar"\n    },\n    "copy_address": {\n      "label": "Copiar Endereço",\n      "copied": "Copiado!"\n    },\n    "explorer": {\n      "label": "Veja mais no explorador"\n    },\n    "transactions": {\n      "description": "%{appName} transações aparecerão aqui...",\n      "description_fallback": "Suas transações aparecerão aqui...",\n      "recent": {\n        "title": "Transações Recentes"\n      },\n      "clear": {\n        "label": "Limpar Tudo"\n      }\n    }\n  },\n  "wallet_connectors": {\n    "argent": {\n      "qr_code": {\n        "step1": {\n          "description": "Coloque o Argent na tela inicial para um acesso mais rápido à sua carteira.",\n          "title": "Abra o aplicativo Argent"\n        },\n        "step2": {\n          "description": "Crie uma carteira e nome de usuário, ou importe uma carteira existente.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois que você escanear, um prompt de conexão aparecerá para você conectar sua carteira.",\n          "title": "Toque no botão Scan QR"\n        }\n      }\n    },\n    "bifrost": {\n      "qr_code": {\n        "step1": {\n          "description": "Recomendamos colocar a Bifrost Wallet na sua tela inicial para um acesso mais rápido.",\n          "title": "Abra o aplicativo Bifrost Wallet"\n        },\n        "step2": {\n          "description": "Crie ou importe uma carteira usando sua frase de recuperação.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Após você escanear, um prompt de conexão aparecerá para você conectar sua carteira.",\n          "title": "Toque no botão de escanear"\n        }\n      }\n    },\n    "bitget": {\n      "qr_code": {\n        "step1": {\n          "description": "Recomendamos colocar a Bitget Wallet na sua tela inicial para um acesso mais rápido.",\n          "title": "Abra o aplicativo Bitget Wallet"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de escanear, um prompt de conexão aparecerá para você conectar sua carteira.",\n          "title": "Toque no botão de escaneamento"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Recomendamos fixar a Bitget Wallet na sua barra de tarefas para um acesso mais rápido à sua carteira.",\n          "title": "Instale a extensão da Carteira Bitget"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer o backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão.",\n          "title": "Atualize seu navegador"\n        }\n      }\n    },\n    "bitski": {\n      "extension": {\n        "step1": {\n          "description": "Recomendamos fixar o Bitski na sua barra de tarefas para um acesso mais rápido à sua carteira.",\n          "title": "Instale a extensão Bitski"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão.",\n          "title": "Atualize seu navegador"\n        }\n      }\n    },\n    "coin98": {\n      "qr_code": {\n        "step1": {\n          "description": "Recomendamos colocar a Carteira Coin98 na tela inicial para um acesso mais rápido à sua carteira.",\n          "title": "Abra o aplicativo Carteira Coin98"\n        },\n        "step2": {\n          "description": "Você pode facilmente fazer backup de sua carteira usando nosso recurso de backup em seu telefone.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de escanear, uma solicitação de conexão aparecerá para você conectar sua carteira.",\n          "title": "Toque no botão WalletConnect"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Clique no canto superior direito do seu navegador e fixe a Carteira Coin98 para fácil acesso.",\n          "title": "Instale a extensão da Carteira Coin98"\n        },\n        "step2": {\n          "description": "Crie uma nova carteira ou importe uma existente.",\n          "title": "Criar ou Importar uma carteira"\n        },\n        "step3": {\n          "description": "Depois de configurar a Carteira Coin98, clique abaixo para atualizar o navegador e carregar a extensão.",\n          "title": "Atualize seu navegador"\n        }\n      }\n    },\n    "coinbase": {\n      "qr_code": {\n        "step1": {\n          "description": "Recomendamos colocar a Carteira Coinbase na tela inicial para um acesso mais rápido.",\n          "title": "Abra o aplicativo Coinbase Wallet"\n        },\n        "step2": {\n          "description": "Você pode fazer backup da sua carteira facilmente usando o recurso de backup na nuvem.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de escanear, um prompt de conexão aparecerá para que você conecte sua carteira.",\n          "title": "Toque no botão de escanear"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Recomendamos fixar o Coinbase Wallet na sua barra de tarefas para um acesso mais rápido à sua carteira.",\n          "title": "Instale a extensão Coinbase Wallet"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Uma vez que você configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extensão.",\n          "title": "Atualize seu navegador"\n        }\n      }\n    },\n    "core": {\n      "qr_code": {\n        "step1": {\n          "description": "Recomendamos colocar o Core na tela inicial para um acesso mais rápido à sua carteira.",\n          "title": "Abra o aplicativo Core"\n        },\n        "step2": {\n          "description": "Você pode facilmente salvar sua carteira usando nosso recurso de backup no seu celular.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de escanear, um prompt de conexão aparecerá para você conectar sua carteira.",\n          "title": "Toque no botão WalletConnect"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Recomendamos fixar o Core na sua barra de tarefas para um acesso mais rápido à sua carteira.",\n          "title": "Instale a extensão Core"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão.",\n          "title": "Atualize seu navegador"\n        }\n      }\n    },\n    "fox": {\n      "qr_code": {\n        "step1": {\n          "description": "Recomendamos colocar o FoxWallet na tela inicial para um acesso mais rápido.",\n          "title": "Abra o aplicativo FoxWallet"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de escanear, uma solicitação de conexão aparecerá para você conectar sua carteira.",\n          "title": "Toque no botão de escaneamento"\n        }\n      }\n    },\n    "frontier": {\n      "qr_code": {\n        "step1": {\n          "description": "Recomendamos colocar o Frontier Wallet na tela inicial para um acesso mais rápido.",\n          "title": "Abra o aplicativo Frontier Wallet"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de escanear, aparecerá um prompt de conexão para você conectar sua carteira.",\n          "title": "Toque no botão de varredura"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "Recomendamos fixar a Carteira Frontier na sua barra de tarefas para um acesso mais rápido à sua carteira.",\n          "title": "Instale a extensão da Carteira Frontier"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão.",\n          "title": "Atualize seu navegador"\n        }\n      }\n    },\n    "im_token": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo imToken",\n          "description": "Coloque o aplicativo imToken na tela inicial para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Crie uma nova carteira ou importe uma existente."\n        },\n        "step3": {\n          "title": "Toque no ícone do Scanner no canto superior direito",\n          "description": "Escolha Nova Conexão, em seguida, escaneie o código QR e confirme o prompt para conectar."\n        }\n      }\n    },\n    "metamask": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo MetaMask",\n          "description": "Recomendamos colocar o MetaMask na tela inicial para um acesso mais rápido."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Toque no botão escanear",\n          "description": "Depois de escanear, aparecerá um prompt de conexão para você conectar sua carteira."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão MetaMask",\n          "description": "Recomendamos fixar o MetaMask na barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize o seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "okx": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo da Carteira OKX",\n          "description": "Recomendamos colocar a Carteira OKX na tela inicial para um acesso mais rápido."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer o backup da sua carteira utilizando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Toque no botão de digitalização",\n          "description": "Depois de escanear, aparecerá um prompt de conexão para você conectar sua carteira."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão OKX Wallet",\n          "description": "Recomendamos fixar a OKX Wallet na sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer o backup da sua carteira utilizando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize o seu navegador",\n          "description": "Uma vez que você configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "omni": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo Omni",\n          "description": "Adicione o Omni à sua tela inicial para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Crie uma nova carteira ou importe uma existente."\n        },\n        "step3": {\n          "title": "Toque no ícone do QR e escaneie",\n          "description": "Toque no ícone QR na tela inicial, escaneie o código e confirme o prompt para conectar."\n        }\n      }\n    },\n    "token_pocket": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo TokenPocket",\n          "description": "Recomendamos colocar o TokenPocket na tela inicial para um acesso mais rápido."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Toque no botão de digitalização",\n          "description": "Depois de escanear, aparecerá um prompt de conexão para você conectar sua carteira."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão TokenPocket",\n          "description": "Recomendamos fixar o TokenPocket em sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Uma vez que você configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "trust": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo Trust Wallet",\n          "description": "Coloque o Trust Wallet na tela inicial para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Crie uma nova carteira ou importe uma existente."\n        },\n        "step3": {\n          "title": "Toque em WalletConnect nas Configurações",\n          "description": "Escolha Nova Conexão, depois escaneie o QR code e confirme o prompt para se conectar."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão Trust Wallet",\n          "description": "Clique no canto superior direito do seu navegador e marque Trust Wallet para fácil acesso."\n        },\n        "step2": {\n          "title": "Crie ou Importe uma carteira",\n          "description": "Crie uma nova carteira ou importe uma existente."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois que configurar a Trust Wallet, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "uniswap": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo Uniswap",\n          "description": "Adicione a Carteira Uniswap à sua tela inicial para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Crie uma nova carteira ou importe uma existente."\n        },\n        "step3": {\n          "title": "Toque no ícone QR e escaneie",\n          "description": "Toque no ícone QR na sua tela inicial, escaneie o código e confirme o prompt para conectar."\n        }\n      }\n    },\n    "zerion": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo Zerion",\n          "description": "Recomendamos colocar o Zerion na sua tela inicial para um acesso mais rápido."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Toque no botão de digitalização",\n          "description": "Depois de digitalizar, um prompt de conexão aparecerá para que você possa conectar sua carteira."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão Zerion",\n          "description": "Recomendamos fixar o Zerion na sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "rainbow": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo Rainbow",\n          "description": "Recomendamos colocar o Rainbow na tela inicial para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Você pode facilmente fazer backup da sua carteira usando nosso recurso de backup no seu telefone."\n        },\n        "step3": {\n          "title": "Toque no botão de digitalizar",\n          "description": "Depois de escanear, uma solicitação de conexão aparecerá para você conectar sua carteira."\n        }\n      }\n    },\n    "enkrypt": {\n      "extension": {\n        "step1": {\n          "description": "Recomendamos fixar a Carteira Enkrypt na sua barra de tarefas para um acesso mais rápido à sua carteira.",\n          "title": "Instale a extensão da Carteira Enkrypt"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão.",\n          "title": "Atualize o seu navegador"\n        }\n      }\n    },\n    "frame": {\n      "extension": {\n        "step1": {\n          "description": "Recomendamos fixar o Frame na sua barra de tarefas para um acesso mais rápido à sua carteira.",\n          "title": "Instale o Frame e a extensão complementar"\n        },\n        "step2": {\n          "description": "Certifique-se de fazer backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém.",\n          "title": "Criar ou Importar uma Carteira"\n        },\n        "step3": {\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão.",\n          "title": "Atualize seu navegador"\n        }\n      }\n    },\n    "one_key": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão OneKey Wallet",\n          "description": "Recomendamos fixar a OneKey Wallet na sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Uma vez que você configurou sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "phantom": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão Phantom",\n          "description": "Recomendamos fixar o Phantom na sua barra de tarefas para facilitar o acesso à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta de recuperação com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "rabby": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão Rabby",\n          "description": "Recomendamos fixar Rabby na sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "safeheron": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão Core",\n          "description": "Recomendamos fixar Safeheron na sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer o backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "taho": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão Taho",\n          "description": "Recomendamos fixar o Taho na sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer o backup da sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "talisman": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão Talisman",\n          "description": "Recomendamos fixar o Talisman na sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Crie ou Importe uma Carteira Ethereum",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase de recuperação com ninguém."\n        },\n        "step3": {\n          "title": "Atualize o seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "xdefi": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão XDEFI Wallet",\n          "description": "Recomendamos fixar a Carteira XDEFI na sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "zeal": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão Zeal",\n          "description": "Recomendamos fixar o Zeal na sua barra de tarefas para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "safepal": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão da Carteira SafePal",\n          "description": "Clique no canto superior direito do seu navegador e fixe a Carteira SafePal para fácil acesso."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma carteira",\n          "description": "Crie uma nova carteira ou importe uma existente."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar a Carteira SafePal, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo Carteira SafePal",\n          "description": "Coloque a Carteira SafePal na tela inicial para um acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Crie uma nova carteira ou importe uma existente."\n        },\n        "step3": {\n          "title": "Toque em WalletConnect nas Configurações",\n          "description": "Escolha Nova Conexão, em seguida, escaneie o código QR e confirme o prompt para conectar."\n        }\n      }\n    },\n    "desig": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão Desig",\n          "description": "Recomendamos fixar Desig na sua barra de tarefas para facilitar o acesso à sua carteira."\n        },\n        "step2": {\n          "title": "Criar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      }\n    },\n    "subwallet": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão SubWallet",\n          "description": "Recomendamos fixar SubWallet na sua barra de tarefas para acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase de recuperação com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo SubWallet",\n          "description": "Recomendamos colocar SubWallet na tela inicial para acesso mais rápido."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Toque no botão de escanear",\n          "description": "Depois que você escanear, um prompt de conexão aparecerá para você conectar sua carteira."\n        }\n      }\n    },\n    "clv": {\n      "extension": {\n        "step1": {\n          "title": "Instale a extensão CLV Wallet",\n          "description": "Recomendamos fixar CLV Wallet na sua barra de tarefas para acesso mais rápido à sua carteira."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Atualize seu navegador",\n          "description": "Depois de configurar sua carteira, clique abaixo para atualizar o navegador e carregar a extensão."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo da carteira CLV",\n          "description": "Recomendamos colocar a Carteira CLV na tela inicial para acesso mais rápido."\n        },\n        "step2": {\n          "title": "Criar ou Importar uma Carteira",\n          "description": "Certifique-se de fazer backup de sua carteira usando um método seguro. Nunca compartilhe sua frase secreta com ninguém."\n        },\n        "step3": {\n          "title": "Toque no botão de escanear",\n          "description": "Depois que você escanear, um prompt de conexão aparecerá para você conectar sua carteira."\n        }\n      }\n    },\n    "okto": {\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo Okto",\n          "description": "Adicione Okto à sua tela inicial para acesso rápido"\n        },\n        "step2": {\n          "title": "Crie uma carteira MPC",\n          "description": "Crie uma conta e gere uma carteira"\n        },\n        "step3": {\n          "title": "Toque em WalletConnect nas Configurações",\n          "description": "Toque no ícone Scan QR no canto superior direito e confirme o prompt para conectar."\n        }\n      }\n    },\n    "ledger": {\n      "desktop": {\n        "step1": {\n          "title": "Abra o aplicativo Ledger Live",\n          "description": "Recomendamos colocar o Ledger Live na tela inicial para um acesso mais rápido."\n        },\n        "step2": {\n          "title": "Configure seu Ledger",\n          "description": "Configure um novo Ledger ou conecte-se a um já existente."\n        },\n        "step3": {\n          "title": "Conectar",\n          "description": "Depois de escanear, aparecerá um prompt de conexão para você conectar sua carteira."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "Abra o aplicativo Ledger Live",\n          "description": "Recomendamos colocar o Ledger Live na tela inicial para um acesso mais rápido."\n        },\n        "step2": {\n          "title": "Configure seu Ledger",\n          "description": "Você pode sincronizar com o aplicativo de desktop ou conectar seu Ledger."\n        },\n        "step3": {\n          "title": "Escanear o código",\n          "description": "Toque em WalletConnect e em seguida mude para Scanner. Depois de escanear, aparecerá um prompt de conexão para você conectar sua carteira."\n        }\n      }\n    }\n  }\n}\n';
export {
  pt_BR_default as default
};
//# sourceMappingURL=pt_BR-JDDVMLRA-2KTUD4TY.js.map
