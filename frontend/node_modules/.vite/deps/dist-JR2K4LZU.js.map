{"version": 3, "sources": ["../../@lit/reactive-element/src/css-tag.ts", "../../@lit/reactive-element/src/reactive-element.ts", "../../lit-html/src/lit-html.ts", "../../lit-element/src/lit-element.ts", "../../@lit/reactive-element/src/decorators/custom-element.ts", "../../@lit/reactive-element/src/decorators/property.ts", "../../@lit/reactive-element/src/decorators/state.ts", "../../@lit/reactive-element/src/decorators/query-assigned-elements.ts", "../../lit-html/src/directive.ts", "../../lit-html/src/directives/class-map.ts", "../../@motionone/utils/dist/array.es.js", "../../@motionone/utils/dist/clamp.es.js", "../../@motionone/utils/dist/defaults.es.js", "../../@motionone/utils/dist/is-number.es.js", "../../@motionone/utils/dist/is-easing-list.es.js", "../../@motionone/utils/dist/wrap.es.js", "../../@motionone/utils/dist/easing.es.js", "../../@motionone/utils/dist/mix.es.js", "../../@motionone/utils/dist/noop.es.js", "../../@motionone/utils/dist/progress.es.js", "../../@motionone/utils/dist/offset.es.js", "../../@motionone/utils/dist/interpolate.es.js", "../../@motionone/utils/dist/is-cubic-bezier.es.js", "../../@motionone/utils/dist/is-easing-generator.es.js", "../../@motionone/utils/dist/is-function.es.js", "../../@motionone/utils/dist/is-string.es.js", "../../@motionone/utils/dist/time.es.js", "../../@motionone/utils/dist/velocity.es.js", "../../@motionone/easing/dist/cubic-bezier.es.js", "../../@motionone/easing/dist/steps.es.js", "../../@motionone/animation/dist/utils/easing.es.js", "../../@motionone/animation/dist/Animation.es.js", "../../hey-listen/dist/hey-listen.es.js", "../../@motionone/types/dist/MotionValue.es.js", "../../@motionone/dom/dist/animate/data.es.js", "../../@motionone/dom/dist/animate/utils/transforms.es.js", "../../@motionone/dom/dist/animate/utils/css-var.es.js", "../../@motionone/dom/dist/animate/utils/feature-detection.es.js", "../../@motionone/dom/dist/animate/utils/easing.es.js", "../../@motionone/dom/dist/animate/utils/keyframes.es.js", "../../@motionone/dom/dist/animate/utils/get-style-name.es.js", "../../@motionone/dom/dist/animate/style.es.js", "../../@motionone/dom/dist/animate/utils/stop-animation.es.js", "../../@motionone/dom/dist/animate/utils/get-unit.es.js", "../../@motionone/dom/dist/animate/animate-style.es.js", "../../@motionone/dom/dist/animate/utils/options.es.js", "../../@motionone/dom/dist/utils/resolve-elements.es.js", "../../@motionone/dom/dist/animate/utils/controls.es.js", "../../@motionone/dom/dist/utils/stagger.es.js", "../../@motionone/dom/dist/animate/create-animate.es.js", "../../@motionone/dom/dist/animate/index.es.js", "../../@motionone/dom/dist/timeline/index.es.js", "../../@motionone/generators/dist/utils/velocity.es.js", "../../@motionone/generators/dist/spring/defaults.es.js", "../../@motionone/generators/dist/spring/utils.es.js", "../../@motionone/generators/dist/utils/has-reached-target.es.js", "../../@motionone/generators/dist/spring/index.es.js", "../../@motionone/generators/dist/glide/index.es.js", "../../@motionone/generators/dist/utils/pregenerate-keyframes.es.js", "../../@motionone/dom/dist/easing/create-generator-easing.es.js", "../../@motionone/dom/dist/easing/spring/index.es.js", "../../@motionone/dom/dist/easing/glide/index.es.js", "../../@motionone/dom/dist/gestures/in-view.es.js", "../../@motionone/dom/dist/gestures/scroll/index.es.js", "../../@motionone/dom/dist/state/index.es.js", "../../@motionone/dom/dist/state/gestures/in-view.es.js", "../../@motionone/dom/dist/state/utils/events.es.js", "../../@motionone/dom/dist/state/gestures/hover.es.js", "../../@motionone/dom/dist/state/gestures/press.es.js", "../../motion/dist/animate.es.js", "../../lit-html/src/directives/if-defined.ts", "../../@walletconnect/modal-ui/src/utils/ThemeUtil.ts", "../../@walletconnect/modal-ui/src/components/wcm-button/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-button-big/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-info-footer/index.ts", "../../@walletconnect/modal-ui/src/utils/SvgUtil.ts", "../../@walletconnect/modal-ui/src/components/wcm-modal-backcard/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-modal-content/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-modal-footer/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-modal-header/index.ts", "../../@walletconnect/modal-ui/src/utils/UiUtil.ts", "../../@walletconnect/modal-ui/src/components/wcm-modal-router/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-modal-toast/index.ts", "../../@walletconnect/modal-ui/src/utils/QrCode.ts", "../../@walletconnect/modal-ui/src/components/wcm-qrcode/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-search-input/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-spinner/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-text/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-wallet-button/index.ts", "../../@walletconnect/modal-ui/src/components/wcm-wallet-image/index.ts", "../../@walletconnect/modal-ui/src/contexts/wcm-explorer-context.ts", "../../@walletconnect/modal-ui/src/contexts/wcm-theme-context.ts", "../../@walletconnect/modal-ui/src/partials/wcm-android-wallet-selection/index.ts", "../../@walletconnect/modal-ui/src/partials/wcm-connector-waiting/index.ts", "../../@walletconnect/modal-ui/src/utils/DataUtil.ts", "../../@walletconnect/modal-ui/src/utils/TemplateUtil.ts", "../../@walletconnect/modal-ui/src/partials/wcm-desktop-wallet-selection/index.ts", "../../@walletconnect/modal-ui/src/partials/wcm-legal-notice/index.ts", "../../@walletconnect/modal-ui/src/partials/wcm-mobile-wallet-selection/index.ts", "../../@walletconnect/modal-ui/src/partials/wcm-modal/index.ts", "../../@walletconnect/modal-ui/src/partials/wcm-platform-selection/index.ts", "../../@walletconnect/modal-ui/src/partials/wcm-view-all-wallets-button/index.ts", "../../@walletconnect/modal-ui/src/partials/wcm-walletconnect-qr/index.ts", "../../@walletconnect/modal-ui/src/views/wcm-connect-wallet-view/index.ts", "../../@walletconnect/modal-ui/src/views/wcm-desktop-connecting-view/index.ts", "../../@walletconnect/modal-ui/src/views/wcm-install-wallet-view/index.ts", "../../@walletconnect/modal-ui/src/views/wcm-mobile-connecting-view/index.ts", "../../@walletconnect/modal-ui/src/views/wcm-mobile-qr-connecting-view/index.ts", "../../@walletconnect/modal-ui/src/views/wcm-qrcode-view/index.ts", "../../@walletconnect/modal-ui/src/views/wcm-wallet-explorer-view/index.ts", "../../@walletconnect/modal-ui/src/views/wcm-web-connecting-view/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n\n/**\n * Whether the current browser supports `adoptedStyleSheets`.\n */\nexport const supportsAdoptingStyleSheets: boolean =\n  global.ShadowRoot &&\n  (global.ShadyCSS === undefined || global.ShadyCSS.nativeShadow) &&\n  'adoptedStyleSheets' in Document.prototype &&\n  'replace' in CSSStyleSheet.prototype;\n\n/**\n * A CSSResult or native CSSStyleSheet.\n *\n * In browsers that support constructible CSS style sheets, CSSStyleSheet\n * object can be used for styling along side CSSResult from the `css`\n * template tag.\n */\nexport type CSSResultOrNative = CSSResult | CSSStyleSheet;\n\nexport type CSSResultArray = Array<CSSResultOrNative | CSSResultArray>;\n\n/**\n * A single CSSResult, CSSStyleSheet, or an array or nested arrays of those.\n */\nexport type CSSResultGroup = CSSResultOrNative | CSSResultArray;\n\nconst constructionToken = Symbol();\n\nconst cssTagCache = new WeakMap<TemplateStringsArray, CSSStyleSheet>();\n\n/**\n * A container for a string of CSS text, that may be used to create a CSSStyleSheet.\n *\n * CSSResult is the return value of `css`-tagged template literals and\n * `unsafeCSS()`. In order to ensure that CSSResults are only created via the\n * `css` tag and `unsafeCSS()`, CSSResult cannot be constructed directly.\n */\nexport class CSSResult {\n  // This property needs to remain unminified.\n  ['_$cssResult$'] = true;\n  readonly cssText: string;\n  private _styleSheet?: CSSStyleSheet;\n  private _strings: TemplateStringsArray | undefined;\n\n  private constructor(\n    cssText: string,\n    strings: TemplateStringsArray | undefined,\n    safeToken: symbol\n  ) {\n    if (safeToken !== constructionToken) {\n      throw new Error(\n        'CSSResult is not constructable. Use `unsafeCSS` or `css` instead.'\n      );\n    }\n    this.cssText = cssText;\n    this._strings = strings;\n  }\n\n  // This is a getter so that it's lazy. In practice, this means stylesheets\n  // are not created until the first element instance is made.\n  get styleSheet(): CSSStyleSheet | undefined {\n    // If `supportsAdoptingStyleSheets` is true then we assume CSSStyleSheet is\n    // constructable.\n    let styleSheet = this._styleSheet;\n    const strings = this._strings;\n    if (supportsAdoptingStyleSheets && styleSheet === undefined) {\n      const cacheable = strings !== undefined && strings.length === 1;\n      if (cacheable) {\n        styleSheet = cssTagCache.get(strings);\n      }\n      if (styleSheet === undefined) {\n        (this._styleSheet = styleSheet = new CSSStyleSheet()).replaceSync(\n          this.cssText\n        );\n        if (cacheable) {\n          cssTagCache.set(strings, styleSheet);\n        }\n      }\n    }\n    return styleSheet;\n  }\n\n  toString(): string {\n    return this.cssText;\n  }\n}\n\ntype ConstructableCSSResult = CSSResult & {\n  new (\n    cssText: string,\n    strings: TemplateStringsArray | undefined,\n    safeToken: symbol\n  ): CSSResult;\n};\n\nconst textFromCSSResult = (value: CSSResultGroup | number) => {\n  // This property needs to remain unminified.\n  if ((value as CSSResult)['_$cssResult$'] === true) {\n    return (value as CSSResult).cssText;\n  } else if (typeof value === 'number') {\n    return value;\n  } else {\n    throw new Error(\n      `Value passed to 'css' function must be a 'css' function result: ` +\n        `${value}. Use 'unsafeCSS' to pass non-literal values, but take care ` +\n        `to ensure page security.`\n    );\n  }\n};\n\n/**\n * Wrap a value for interpolation in a {@linkcode css} tagged template literal.\n *\n * This is unsafe because untrusted CSS text can be used to phone home\n * or exfiltrate data to an attacker controlled site. Take care to only use\n * this with trusted input.\n */\nexport const unsafeCSS = (value: unknown) =>\n  new (CSSResult as ConstructableCSSResult)(\n    typeof value === 'string' ? value : String(value),\n    undefined,\n    constructionToken\n  );\n\n/**\n * A template literal tag which can be used with LitElement's\n * {@linkcode LitElement.styles} property to set element styles.\n *\n * For security reasons, only literal string values and number may be used in\n * embedded expressions. To incorporate non-literal values {@linkcode unsafeCSS}\n * may be used inside an expression.\n */\nexport const css = (\n  strings: TemplateStringsArray,\n  ...values: (CSSResultGroup | number)[]\n): CSSResult => {\n  const cssText =\n    strings.length === 1\n      ? strings[0]\n      : values.reduce(\n          (acc, v, idx) => acc + textFromCSSResult(v) + strings[idx + 1],\n          strings[0]\n        );\n  return new (CSSResult as ConstructableCSSResult)(\n    cssText,\n    strings,\n    constructionToken\n  );\n};\n\n/**\n * Applies the given styles to a `shadowRoot`. When Shadow DOM is\n * available but `adoptedStyleSheets` is not, styles are appended to the\n * `shadowRoot` to [mimic spec behavior](https://wicg.github.io/construct-stylesheets/#using-constructed-stylesheets).\n * Note, when shimming is used, any styles that are subsequently placed into\n * the shadowRoot should be placed *before* any shimmed adopted styles. This\n * will match spec behavior that gives adopted sheets precedence over styles in\n * shadowRoot.\n */\nexport const adoptStyles = (\n  renderRoot: ShadowRoot,\n  styles: Array<CSSResultOrNative>\n) => {\n  if (supportsAdoptingStyleSheets) {\n    (renderRoot as ShadowRoot).adoptedStyleSheets = styles.map((s) =>\n      s instanceof CSSStyleSheet ? s : s.styleSheet!\n    );\n  } else {\n    styles.forEach((s) => {\n      const style = document.createElement('style');\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const nonce = (global as any)['litNonce'];\n      if (nonce !== undefined) {\n        style.setAttribute('nonce', nonce);\n      }\n      style.textContent = (s as CSSResult).cssText;\n      renderRoot.appendChild(style);\n    });\n  }\n};\n\nconst cssResultFromStyleSheet = (sheet: CSSStyleSheet) => {\n  let cssText = '';\n  for (const rule of sheet.cssRules) {\n    cssText += rule.cssText;\n  }\n  return unsafeCSS(cssText);\n};\n\nexport const getCompatibleStyle =\n  supportsAdoptingStyleSheets ||\n  (NODE_MODE && global.CSSStyleSheet === undefined)\n    ? (s: CSSResultOrNative) => s\n    : (s: CSSResultOrNative) =>\n        s instanceof CSSStyleSheet ? cssResultFromStyleSheet(s) : s;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Use this module if you want to create your own base class extending\n * {@link ReactiveElement}.\n * @packageDocumentation\n */\n\nimport {\n  getCompatibleStyle,\n  adoptStyles,\n  CSSResultGroup,\n  CSSResultOrNative,\n} from './css-tag.js';\nimport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\n// In the Node build, this import will be injected by Rollup:\n// import {HTMLElement, customElements} from '@lit-labs/ssr-dom-shim';\n\nexport * from './css-tag.js';\nexport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n\nif (NODE_MODE) {\n  global.customElements ??= customElements;\n}\n\nconst DEV_MODE = true;\n\nlet requestUpdateThenable: (name: string) => {\n  then: (\n    onfulfilled?: (value: boolean) => void,\n    _onrejected?: () => void\n  ) => void;\n};\n\nlet issueWarning: (code: string, warning: string) => void;\n\nconst trustedTypes = (global as unknown as {trustedTypes?: {emptyScript: ''}})\n  .trustedTypes;\n\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n  ? (trustedTypes.emptyScript as unknown as '')\n  : '';\n\nconst polyfillSupport = DEV_MODE\n  ? global.reactiveElementPolyfillSupportDevMode\n  : global.reactiveElementPolyfillSupport;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  const issuedWarnings: Set<string | undefined> = (global.litIssuedWarnings ??=\n    new Set());\n\n  // Issue a warning, if we haven't already.\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (!issuedWarnings.has(warning)) {\n      console.warn(warning);\n      issuedWarnings.add(warning);\n    }\n  };\n\n  issueWarning(\n    'dev-mode',\n    `Lit is in dev mode. Not recommended for production!`\n  );\n\n  // Issue polyfill support warning.\n  if (global.ShadyDOM?.inUse && polyfillSupport === undefined) {\n    issueWarning(\n      'polyfill-support-missing',\n      `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` +\n        `the \\`polyfill-support\\` module has not been loaded.`\n    );\n  }\n\n  requestUpdateThenable = (name) => ({\n    then: (\n      onfulfilled?: (value: boolean) => void,\n      _onrejected?: () => void\n    ) => {\n      issueWarning(\n        'request-update-promise',\n        `The \\`requestUpdate\\` method should no longer return a Promise but ` +\n          `does so on \\`${name}\\`. Use \\`updateComplete\\` instead.`\n      );\n      if (onfulfilled !== undefined) {\n        onfulfilled(false);\n      }\n    },\n  });\n}\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace ReactiveUnstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry = Update;\n    export interface Update {\n      kind: 'update';\n    }\n  }\n}\n\ninterface DebugLoggingWindow {\n  // Even in dev mode, we generally don't want to emit these events, as that's\n  // another level of cost, so only emit them when DEV_MODE is true _and_ when\n  // window.emitLitDebugEvents is true.\n  emitLitDebugLogEvents?: boolean;\n}\n\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n  ? (event: ReactiveUnstable.DebugLog.Entry) => {\n      const shouldEmit = (global as unknown as DebugLoggingWindow)\n        .emitLitDebugLogEvents;\n      if (!shouldEmit) {\n        return;\n      }\n      global.dispatchEvent(\n        new CustomEvent<ReactiveUnstable.DebugLog.Entry>('lit-debug', {\n          detail: event,\n        })\n      );\n    }\n  : undefined;\n\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = <P extends PropertyKey>(\n  prop: P,\n  _obj: unknown\n): P => prop;\n\n/**\n * Converts property values to and from attribute values.\n */\nexport interface ComplexAttributeConverter<Type = unknown, TypeHint = unknown> {\n  /**\n   * Called to convert an attribute value to a property\n   * value.\n   */\n  fromAttribute?(value: string | null, type?: TypeHint): Type;\n\n  /**\n   * Called to convert a property value to an attribute\n   * value.\n   *\n   * It returns unknown instead of string, to be compatible with\n   * https://github.com/WICG/trusted-types (and similar efforts).\n   */\n  toAttribute?(value: Type, type?: TypeHint): unknown;\n}\n\ntype AttributeConverter<Type = unknown, TypeHint = unknown> =\n  | ComplexAttributeConverter<Type>\n  | ((value: string | null, type?: TypeHint) => Type);\n\n/**\n * Defines options for a property accessor.\n */\nexport interface PropertyDeclaration<Type = unknown, TypeHint = unknown> {\n  /**\n   * When set to `true`, indicates the property is internal private state. The\n   * property should not be set by users. When using TypeScript, this property\n   * should be marked as `private` or `protected`, and it is also a common\n   * practice to use a leading `_` in the name. The property is not added to\n   * `observedAttributes`.\n   */\n  readonly state?: boolean;\n\n  /**\n   * Indicates how and whether the property becomes an observed attribute.\n   * If the value is `false`, the property is not added to `observedAttributes`.\n   * If true or absent, the lowercased property name is observed (e.g. `fooBar`\n   * becomes `foobar`). If a string, the string value is observed (e.g\n   * `attribute: 'foo-bar'`).\n   */\n  readonly attribute?: boolean | string;\n\n  /**\n   * Indicates the type of the property. This is used only as a hint for the\n   * `converter` to determine how to convert the attribute\n   * to/from a property.\n   */\n  readonly type?: TypeHint;\n\n  /**\n   * Indicates how to convert the attribute to/from a property. If this value\n   * is a function, it is used to convert the attribute value a the property\n   * value. If it's an object, it can have keys for `fromAttribute` and\n   * `toAttribute`. If no `toAttribute` function is provided and\n   * `reflect` is set to `true`, the property value is set directly to the\n   * attribute. A default `converter` is used if none is provided; it supports\n   * `Boolean`, `String`, `Number`, `Object`, and `Array`. Note,\n   * when a property changes and the converter is used to update the attribute,\n   * the property is never updated again as a result of the attribute changing,\n   * and vice versa.\n   */\n  readonly converter?: AttributeConverter<Type, TypeHint>;\n\n  /**\n   * Indicates if the property should reflect to an attribute.\n   * If `true`, when the property is set, the attribute is set using the\n   * attribute name determined according to the rules for the `attribute`\n   * property option and the value of the property converted using the rules\n   * from the `converter` property option.\n   */\n  readonly reflect?: boolean;\n\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n\n  /**\n   * Indicates whether an accessor will be created for this property. By\n   * default, an accessor will be generated for this property that requests an\n   * update when set. If this flag is `true`, no accessor will be created, and\n   * it will be the user's responsibility to call\n   * `this.requestUpdate(propertyName, oldValue)` to request an update when\n   * the property changes.\n   */\n  readonly noAccessor?: boolean;\n}\n\n/**\n * Map of properties to PropertyDeclaration options. For each property an\n * accessor is made, and the property is processed according to the\n * PropertyDeclaration options.\n */\nexport interface PropertyDeclarations {\n  readonly [key: string]: PropertyDeclaration;\n}\n\ntype PropertyDeclarationMap = Map<PropertyKey, PropertyDeclaration>;\n\ntype AttributeMap = Map<string, PropertyKey>;\n\n/**\n * A Map of property keys to values.\n *\n * Takes an optional type parameter T, which when specified as a non-any,\n * non-unknown type, will make the Map more strongly-typed, associating the map\n * keys with their corresponding value type on T.\n *\n * Use `PropertyValues<this>` when overriding ReactiveElement.update() and\n * other lifecycle methods in order to get stronger type-checking on keys\n * and values.\n */\n// This type is conditional so that if the parameter T is not specified, or\n// is `any`, the type will include `Map<PropertyKey, unknown>`. Since T is not\n// given in the uses of PropertyValues in this file, all uses here fallback to\n// meaning `Map<PropertyKey, unknown>`, but if a developer uses\n// `PropertyValues<this>` (or any other value for T) they will get a\n// strongly-typed Map type.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type PropertyValues<T = any> = T extends object\n  ? PropertyValueMap<T>\n  : Map<PropertyKey, unknown>;\n\n/**\n * Do not use, instead prefer {@linkcode PropertyValues}.\n */\n// This type must be exported such that JavaScript generated by the Google\n// Closure Compiler can import a type reference.\nexport interface PropertyValueMap<T> extends Map<PropertyKey, unknown> {\n  get<K extends keyof T>(k: K): T[K];\n  set<K extends keyof T>(key: K, value: T[K]): this;\n  has<K extends keyof T>(k: K): boolean;\n  delete<K extends keyof T>(k: K): boolean;\n}\n\nexport const defaultConverter: ComplexAttributeConverter = {\n  toAttribute(value: unknown, type?: unknown): unknown {\n    switch (type) {\n      case Boolean:\n        value = value ? emptyStringForBooleanAttribute : null;\n        break;\n      case Object:\n      case Array:\n        // if the value is `null` or `undefined` pass this through\n        // to allow removing/no change behavior.\n        value = value == null ? value : JSON.stringify(value);\n        break;\n    }\n    return value;\n  },\n\n  fromAttribute(value: string | null, type?: unknown) {\n    let fromValue: unknown = value;\n    switch (type) {\n      case Boolean:\n        fromValue = value !== null;\n        break;\n      case Number:\n        fromValue = value === null ? null : Number(value);\n        break;\n      case Object:\n      case Array:\n        // Do *not* generate exception when invalid JSON is set as elements\n        // don't normally complain on being mis-configured.\n        // TODO(sorvell): Do generate exception in *dev mode*.\n        try {\n          // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n          fromValue = JSON.parse(value!) as unknown;\n        } catch (e) {\n          fromValue = null;\n        }\n        break;\n    }\n    return fromValue;\n  },\n};\n\nexport interface HasChanged {\n  (value: unknown, old: unknown): boolean;\n}\n\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nexport const notEqual: HasChanged = (value: unknown, old: unknown): boolean => {\n  // This ensures (old==NaN, value==NaN) always returns false\n  return old !== value && (old === old || value === value);\n};\n\nconst defaultPropertyDeclaration: PropertyDeclaration = {\n  attribute: true,\n  type: String,\n  converter: defaultConverter,\n  reflect: false,\n  hasChanged: notEqual,\n};\n\n/**\n * The Closure JS Compiler doesn't currently have good support for static\n * property semantics where \"this\" is dynamic (e.g.\n * https://github.com/google/closure-compiler/issues/3177 and others) so we use\n * this hack to bypass any rewriting by the compiler.\n */\nconst finalized = 'finalized';\n\n/**\n * A string representing one of the supported dev mode warning categories.\n */\nexport type WarningKind = 'change-in-update' | 'migration';\n\nexport type Initializer = (element: ReactiveElement) => void;\n\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclassers to render updates as desired.\n * @noInheritDoc\n */\nexport abstract class ReactiveElement\n  // In the Node build, this `extends` clause will be substituted with\n  // `(globalThis.HTMLElement ?? HTMLElement)`.\n  //\n  // This way, we will first prefer any global `HTMLElement` polyfill that the\n  // user has assigned, and then fall back to the `HTMLElement` shim which has\n  // been imported (see note at the top of this file about how this import is\n  // generated by Rollup). Note that the `HTMLElement` variable has been\n  // shadowed by this import, so it no longer refers to the global.\n  extends HTMLElement\n  implements ReactiveControllerHost\n{\n  // Note: these are patched in only in DEV_MODE.\n  /**\n   * Read or set all the enabled warning categories for this class.\n   *\n   * This property is only used in development builds.\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enabledWarnings?: WarningKind[];\n\n  /**\n   * Enable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Enable for all ReactiveElement subclasses\n   * ReactiveElement.enableWarning?.('migration');\n   *\n   * // Enable for only MyElement and subclasses\n   * MyElement.enableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Disable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Disable for all ReactiveElement subclasses\n   * ReactiveElement.disableWarning?.('migration');\n   *\n   * // Disable for only MyElement and subclasses\n   * MyElement.disableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static disableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Adds an initializer function to the class that is called during instance\n   * construction.\n   *\n   * This is useful for code that runs against a `ReactiveElement`\n   * subclass, such as a decorator, that needs to do work for each\n   * instance, such as setting up a `ReactiveController`.\n   *\n   * ```ts\n   * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n   *   target.addInitializer((instance: ReactiveElement) => {\n   *     // This is run during construction of the element\n   *     new MyController(instance);\n   *   });\n   * }\n   * ```\n   *\n   * Decorating a field will then cause each instance to run an initializer\n   * that adds a controller:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   @myDecorator foo;\n   * }\n   * ```\n   *\n   * Initializers are stored per-constructor. Adding an initializer to a\n   * subclass does not add it to a superclass. Since initializers are run in\n   * constructors, initializers will run in order of the class hierarchy,\n   * starting with superclasses and progressing to the instance's class.\n   *\n   * @nocollapse\n   */\n  static addInitializer(initializer: Initializer) {\n    this.finalize();\n    (this._initializers ??= []).push(initializer);\n  }\n\n  static _initializers?: Initializer[];\n\n  /*\n   * Due to closure compiler ES6 compilation bugs, @nocollapse is required on\n   * all static methods and properties with initializers.  Reference:\n   * - https://github.com/google/closure-compiler/issues/1776\n   */\n\n  /**\n   * Maps attribute names to properties; for example `foobar` attribute to\n   * `fooBar` property. Created lazily on user subclasses when finalizing the\n   * class.\n   * @nocollapse\n   */\n  private static __attributeToPropertyMap: AttributeMap;\n\n  /**\n   * Marks class as having finished creating properties.\n   */\n  protected static [finalized] = true;\n\n  /**\n   * Memoized list of all element properties, including any superclass properties.\n   * Created lazily on user subclasses when finalizing the class.\n   * @nocollapse\n   * @category properties\n   */\n  static elementProperties: PropertyDeclarationMap = new Map();\n\n  /**\n   * User-supplied object that maps property names to `PropertyDeclaration`\n   * objects containing options for configuring reactive properties. When\n   * a reactive property is set the element will update and render.\n   *\n   * By default properties are public fields, and as such, they should be\n   * considered as primarily settable by element users, either via attribute or\n   * the property itself.\n   *\n   * Generally, properties that are changed by the element should be private or\n   * protected fields and should use the `state: true` option. Properties\n   * marked as `state` do not reflect from the corresponding attribute\n   *\n   * However, sometimes element code does need to set a public property. This\n   * should typically only be done in response to user interaction, and an event\n   * should be fired informing the user; for example, a checkbox sets its\n   * `checked` property when clicked and fires a `changed` event. Mutating\n   * public properties should typically not be done for non-primitive (object or\n   * array) properties. In other cases when an element needs to manage state, a\n   * private property set with the `state: true` option should be used. When\n   * needed, state properties can be initialized via public properties to\n   * facilitate complex interactions.\n   * @nocollapse\n   * @category properties\n   */\n  static properties: PropertyDeclarations;\n\n  /**\n   * Memoized list of all element styles.\n   * Created lazily on user subclasses when finalizing the class.\n   * @nocollapse\n   * @category styles\n   */\n  static elementStyles: Array<CSSResultOrNative> = [];\n\n  /**\n   * Array of styles to apply to the element. The styles should be defined\n   * using the {@linkcode css} tag function, via constructible stylesheets, or\n   * imported from native CSS module scripts.\n   *\n   * Note on Content Security Policy:\n   *\n   * Element styles are implemented with `<style>` tags when the browser doesn't\n   * support adopted StyleSheets. To use such `<style>` tags with the style-src\n   * CSP directive, the style-src value must either include 'unsafe-inline' or\n   * `nonce-<base64-value>` with `<base64-value>` replaced be a server-generated\n   * nonce.\n   *\n   * To provide a nonce to use on generated `<style>` elements, set\n   * `window.litNonce` to a server-generated nonce in your page's HTML, before\n   * loading application code:\n   *\n   * ```html\n   * <script>\n   *   // Generated and unique per request:\n   *   window.litNonce = 'a1b2c3d4';\n   * </script>\n   * ```\n   * @nocollapse\n   * @category styles\n   */\n  static styles?: CSSResultGroup;\n\n  /**\n   * The set of properties defined by this class that caused an accessor to be\n   * added during `createProperty`.\n   * @nocollapse\n   */\n  private static __reactivePropertyKeys?: Set<PropertyKey>;\n\n  /**\n   * Returns a list of attributes corresponding to the registered properties.\n   * @nocollapse\n   * @category attributes\n   */\n  static get observedAttributes() {\n    // note: piggy backing on this to ensure we're finalized.\n    this.finalize();\n    const attributes: string[] = [];\n    // Use forEach so this works even if for/of loops are compiled to for loops\n    // expecting arrays\n    this.elementProperties.forEach((v, p) => {\n      const attr = this.__attributeNameForProperty(p, v);\n      if (attr !== undefined) {\n        this.__attributeToPropertyMap.set(attr, p);\n        attributes.push(attr);\n      }\n    });\n    return attributes;\n  }\n\n  /**\n   * Creates a property accessor on the element prototype if one does not exist\n   * and stores a {@linkcode PropertyDeclaration} for the property with the\n   * given options. The property setter calls the property's `hasChanged`\n   * property option or uses a strict identity check to determine whether or not\n   * to request an update.\n   *\n   * This method may be overridden to customize properties; however,\n   * when doing so, it's important to call `super.createProperty` to ensure\n   * the property is setup correctly. This method calls\n   * `getPropertyDescriptor` internally to get a descriptor to install.\n   * To customize what properties do when they are get or set, override\n   * `getPropertyDescriptor`. To customize the options for a property,\n   * implement `createProperty` like this:\n   *\n   * ```ts\n   * static createProperty(name, options) {\n   *   options = Object.assign(options, {myOption: true});\n   *   super.createProperty(name, options);\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  static createProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration = defaultPropertyDeclaration\n  ) {\n    // if this is a state property, force the attribute to false.\n    if (options.state) {\n      // Cast as any since this is readonly.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (options as any).attribute = false;\n    }\n    // Note, since this can be called by the `@property` decorator which\n    // is called before `finalize`, we ensure finalization has been kicked off.\n    this.finalize();\n    this.elementProperties.set(name, options);\n    // Do not generate an accessor if the prototype already has one, since\n    // it would be lost otherwise and that would never be the user's intention;\n    // Instead, we expect users to call `requestUpdate` themselves from\n    // user-defined accessors. Note that if the super has an accessor we will\n    // still overwrite it\n    if (!options.noAccessor && !this.prototype.hasOwnProperty(name)) {\n      const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n      const descriptor = this.getPropertyDescriptor(name, key, options);\n      if (descriptor !== undefined) {\n        Object.defineProperty(this.prototype, name, descriptor);\n        if (DEV_MODE) {\n          // If this class doesn't have its own set, create one and initialize\n          // with the values in the set from the nearest ancestor class, if any.\n          if (!this.hasOwnProperty('__reactivePropertyKeys')) {\n            this.__reactivePropertyKeys = new Set(\n              this.__reactivePropertyKeys ?? []\n            );\n          }\n          this.__reactivePropertyKeys!.add(name);\n        }\n      }\n    }\n  }\n\n  /**\n   * Returns a property descriptor to be defined on the given named property.\n   * If no descriptor is returned, the property will not become an accessor.\n   * For example,\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   static getPropertyDescriptor(name, key, options) {\n   *     const defaultDescriptor =\n   *         super.getPropertyDescriptor(name, key, options);\n   *     const setter = defaultDescriptor.set;\n   *     return {\n   *       get: defaultDescriptor.get,\n   *       set(value) {\n   *         setter.call(this, value);\n   *         // custom action.\n   *       },\n   *       configurable: true,\n   *       enumerable: true\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  protected static getPropertyDescriptor(\n    name: PropertyKey,\n    key: string | symbol,\n    options: PropertyDeclaration\n  ): PropertyDescriptor | undefined {\n    return {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      get(): any {\n        return (this as {[key: string]: unknown})[key as string];\n      },\n      set(this: ReactiveElement, value: unknown) {\n        const oldValue = (this as {} as {[key: string]: unknown})[\n          name as string\n        ];\n        (this as {} as {[key: string]: unknown})[key as string] = value;\n        (this as unknown as ReactiveElement).requestUpdate(\n          name,\n          oldValue,\n          options\n        );\n      },\n      configurable: true,\n      enumerable: true,\n    };\n  }\n\n  /**\n   * Returns the property options associated with the given property.\n   * These options are defined with a `PropertyDeclaration` via the `properties`\n   * object or the `@property` decorator and are registered in\n   * `createProperty(...)`.\n   *\n   * Note, this method should be considered \"final\" and not overridden. To\n   * customize the options for a given property, override\n   * {@linkcode createProperty}.\n   *\n   * @nocollapse\n   * @final\n   * @category properties\n   */\n  static getPropertyOptions(name: PropertyKey) {\n    return this.elementProperties.get(name) || defaultPropertyDeclaration;\n  }\n\n  /**\n   * Creates property accessors for registered properties, sets up element\n   * styling, and ensures any superclasses are also finalized. Returns true if\n   * the element was finalized.\n   * @nocollapse\n   */\n  protected static finalize() {\n    if (this.hasOwnProperty(finalized)) {\n      return false;\n    }\n    this[finalized] = true;\n    // finalize any superclasses\n    const superCtor = Object.getPrototypeOf(this) as typeof ReactiveElement;\n    superCtor.finalize();\n    // Create own set of initializers for this class if any exist on the\n    // superclass and copy them down. Note, for a small perf boost, avoid\n    // creating initializers unless needed.\n    if (superCtor._initializers !== undefined) {\n      this._initializers = [...superCtor._initializers];\n    }\n    this.elementProperties = new Map(superCtor.elementProperties);\n    // initialize Map populated in observedAttributes\n    this.__attributeToPropertyMap = new Map();\n    // make any properties\n    // Note, only process \"own\" properties since this element will inherit\n    // any properties defined on the superClass, and finalization ensures\n    // the entire prototype chain is finalized.\n    if (this.hasOwnProperty(JSCompiler_renameProperty('properties', this))) {\n      const props = this.properties;\n      // support symbols in properties (IE11 does not support this)\n      const propKeys = [\n        ...Object.getOwnPropertyNames(props),\n        ...Object.getOwnPropertySymbols(props),\n      ];\n      // This for/of is ok because propKeys is an array\n      for (const p of propKeys) {\n        // note, use of `any` is due to TypeScript lack of support for symbol in\n        // index types\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this.createProperty(p, (props as any)[p]);\n      }\n    }\n    this.elementStyles = this.finalizeStyles(this.styles);\n    // DEV mode warnings\n    if (DEV_MODE) {\n      const warnRemovedOrRenamed = (name: string, renamed = false) => {\n        if (this.prototype.hasOwnProperty(name)) {\n          issueWarning(\n            renamed ? 'renamed-api' : 'removed-api',\n            `\\`${name}\\` is implemented on class ${this.name}. It ` +\n              `has been ${renamed ? 'renamed' : 'removed'} ` +\n              `in this version of LitElement.`\n          );\n        }\n      };\n      warnRemovedOrRenamed('initialize');\n      warnRemovedOrRenamed('requestUpdateInternal');\n      warnRemovedOrRenamed('_getUpdateComplete', true);\n    }\n    return true;\n  }\n\n  /**\n   * Options used when calling `attachShadow`. Set this property to customize\n   * the options for the shadowRoot; for example, to create a closed\n   * shadowRoot: `{mode: 'closed'}`.\n   *\n   * Note, these options are used in `createRenderRoot`. If this method\n   * is customized, options should be respected if possible.\n   * @nocollapse\n   * @category rendering\n   */\n  static shadowRootOptions: ShadowRootInit = {mode: 'open'};\n\n  /**\n   * Takes the styles the user supplied via the `static styles` property and\n   * returns the array of styles to apply to the element.\n   * Override this method to integrate into a style management system.\n   *\n   * Styles are deduplicated preserving the _last_ instance in the list. This\n   * is a performance optimization to avoid duplicated styles that can occur\n   * especially when composing via subclassing. The last item is kept to try\n   * to preserve the cascade order with the assumption that it's most important\n   * that last added styles override previous styles.\n   *\n   * @nocollapse\n   * @category styles\n   */\n  protected static finalizeStyles(\n    styles?: CSSResultGroup\n  ): Array<CSSResultOrNative> {\n    const elementStyles = [];\n    if (Array.isArray(styles)) {\n      // Dedupe the flattened array in reverse order to preserve the last items.\n      // Casting to Array<unknown> works around TS error that\n      // appears to come from trying to flatten a type CSSResultArray.\n      const set = new Set((styles as Array<unknown>).flat(Infinity).reverse());\n      // Then preserve original order by adding the set items in reverse order.\n      for (const s of set) {\n        elementStyles.unshift(getCompatibleStyle(s as CSSResultOrNative));\n      }\n    } else if (styles !== undefined) {\n      elementStyles.push(getCompatibleStyle(styles));\n    }\n    return elementStyles;\n  }\n\n  /**\n   * Node or ShadowRoot into which element DOM should be rendered. Defaults\n   * to an open shadowRoot.\n   * @category rendering\n   */\n  readonly renderRoot!: HTMLElement | ShadowRoot;\n\n  /**\n   * Returns the property name for the given attribute `name`.\n   * @nocollapse\n   */\n  private static __attributeNameForProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration\n  ) {\n    const attribute = options.attribute;\n    return attribute === false\n      ? undefined\n      : typeof attribute === 'string'\n      ? attribute\n      : typeof name === 'string'\n      ? name.toLowerCase()\n      : undefined;\n  }\n\n  private __instanceProperties?: PropertyValues = new Map();\n  // Initialize to an unresolved Promise so we can make sure the element has\n  // connected before first update.\n  private __updatePromise!: Promise<boolean>;\n\n  /**\n   * True if there is a pending update as a result of calling `requestUpdate()`.\n   * Should only be read.\n   * @category updates\n   */\n  isUpdatePending = false;\n\n  /**\n   * Is set to `true` after the first update. The element code cannot assume\n   * that `renderRoot` exists before the element `hasUpdated`.\n   * @category updates\n   */\n  hasUpdated = false;\n\n  /**\n   * Map with keys for any properties that have changed since the last\n   * update cycle with previous values.\n   *\n   * @internal\n   */\n  _$changedProperties!: PropertyValues;\n\n  /**\n   * Map with keys of properties that should be reflected when updated.\n   */\n  private __reflectingProperties?: Map<PropertyKey, PropertyDeclaration>;\n\n  /**\n   * Name of currently reflecting property\n   */\n  private __reflectingProperty: PropertyKey | null = null;\n\n  /**\n   * Set of controllers.\n   */\n  private __controllers?: ReactiveController[];\n\n  constructor() {\n    super();\n    this.__initialize();\n  }\n\n  /**\n   * Internal only override point for customizing work done when elements\n   * are constructed.\n   */\n  private __initialize() {\n    this.__updatePromise = new Promise<boolean>(\n      (res) => (this.enableUpdating = res)\n    );\n    this._$changedProperties = new Map();\n    this.__saveInstanceProperties();\n    // ensures first update will be caught by an early access of\n    // `updateComplete`\n    this.requestUpdate();\n    (this.constructor as typeof ReactiveElement)._initializers?.forEach((i) =>\n      i(this)\n    );\n  }\n\n  /**\n   * Registers a `ReactiveController` to participate in the element's reactive\n   * update cycle. The element automatically calls into any registered\n   * controllers during its lifecycle callbacks.\n   *\n   * If the element is connected when `addController()` is called, the\n   * controller's `hostConnected()` callback will be immediately called.\n   * @category controllers\n   */\n  addController(controller: ReactiveController) {\n    (this.__controllers ??= []).push(controller);\n    // If a controller is added after the element has been connected,\n    // call hostConnected. Note, re-using existence of `renderRoot` here\n    // (which is set in connectedCallback) to avoid the need to track a\n    // first connected state.\n    if (this.renderRoot !== undefined && this.isConnected) {\n      controller.hostConnected?.();\n    }\n  }\n\n  /**\n   * Removes a `ReactiveController` from the element.\n   * @category controllers\n   */\n  removeController(controller: ReactiveController) {\n    // Note, if the indexOf is -1, the >>> will flip the sign which makes the\n    // splice do nothing.\n    this.__controllers?.splice(this.__controllers.indexOf(controller) >>> 0, 1);\n  }\n\n  /**\n   * Fixes any properties set on the instance before upgrade time.\n   * Otherwise these would shadow the accessor and break these properties.\n   * The properties are stored in a Map which is played back after the\n   * constructor runs. Note, on very old versions of Safari (<=9) or Chrome\n   * (<=41), properties created for native platform properties like (`id` or\n   * `name`) may not have default values set in the element constructor. On\n   * these browsers native properties appear on instances and therefore their\n   * default value will overwrite any element default (e.g. if the element sets\n   * this.id = 'id' in the constructor, the 'id' will become '' since this is\n   * the native platform default).\n   */\n  private __saveInstanceProperties() {\n    // Use forEach so this works even if for/of loops are compiled to for loops\n    // expecting arrays\n    (this.constructor as typeof ReactiveElement).elementProperties.forEach(\n      (_v, p) => {\n        if (this.hasOwnProperty(p)) {\n          this.__instanceProperties!.set(p, this[p as keyof this]);\n          delete this[p as keyof this];\n        }\n      }\n    );\n  }\n\n  /**\n   * Returns the node into which the element should render and by default\n   * creates and returns an open shadowRoot. Implement to customize where the\n   * element's DOM is rendered. For example, to render into the element's\n   * childNodes, return `this`.\n   *\n   * @return Returns a node into which to render.\n   * @category rendering\n   */\n  protected createRenderRoot(): Element | ShadowRoot {\n    const renderRoot =\n      this.shadowRoot ??\n      this.attachShadow(\n        (this.constructor as typeof ReactiveElement).shadowRootOptions\n      );\n    adoptStyles(\n      renderRoot,\n      (this.constructor as typeof ReactiveElement).elementStyles\n    );\n    return renderRoot;\n  }\n\n  /**\n   * On first connection, creates the element's renderRoot, sets up\n   * element styling, and enables updating.\n   * @category lifecycle\n   */\n  connectedCallback() {\n    // create renderRoot before first update.\n    if (this.renderRoot === undefined) {\n      (\n        this as {\n          renderRoot: Element | DocumentFragment;\n        }\n      ).renderRoot = this.createRenderRoot();\n    }\n    this.enableUpdating(true);\n    this.__controllers?.forEach((c) => c.hostConnected?.());\n  }\n\n  /**\n   * Note, this method should be considered final and not overridden. It is\n   * overridden on the element instance with a function that triggers the first\n   * update.\n   * @category updates\n   */\n  protected enableUpdating(_requestedUpdate: boolean) {}\n\n  /**\n   * Allows for `super.disconnectedCallback()` in extensions while\n   * reserving the possibility of making non-breaking feature additions\n   * when disconnecting at some point in the future.\n   * @category lifecycle\n   */\n  disconnectedCallback() {\n    this.__controllers?.forEach((c) => c.hostDisconnected?.());\n  }\n\n  /**\n   * Synchronizes property values when attributes change.\n   *\n   * Specifically, when an attribute is set, the corresponding property is set.\n   * You should rarely need to implement this callback. If this method is\n   * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n   * called.\n   *\n   * See [using the lifecycle callbacks](https://developer.mozilla.org/en-US/docs/Web/Web_Components/Using_custom_elements#using_the_lifecycle_callbacks)\n   * on MDN for more information about the `attributeChangedCallback`.\n   * @category attributes\n   */\n  attributeChangedCallback(\n    name: string,\n    _old: string | null,\n    value: string | null\n  ) {\n    this._$attributeToProperty(name, value);\n  }\n\n  private __propertyToAttribute(\n    name: PropertyKey,\n    value: unknown,\n    options: PropertyDeclaration = defaultPropertyDeclaration\n  ) {\n    const attr = (\n      this.constructor as typeof ReactiveElement\n    ).__attributeNameForProperty(name, options);\n    if (attr !== undefined && options.reflect === true) {\n      const converter =\n        (options.converter as ComplexAttributeConverter)?.toAttribute !==\n        undefined\n          ? (options.converter as ComplexAttributeConverter)\n          : defaultConverter;\n      const attrValue = converter.toAttribute!(value, options.type);\n      if (\n        DEV_MODE &&\n        (this.constructor as typeof ReactiveElement).enabledWarnings!.indexOf(\n          'migration'\n        ) >= 0 &&\n        attrValue === undefined\n      ) {\n        issueWarning(\n          'undefined-attribute-value',\n          `The attribute value for the ${name as string} property is ` +\n            `undefined on element ${this.localName}. The attribute will be ` +\n            `removed, but in the previous version of \\`ReactiveElement\\`, ` +\n            `the attribute would not have changed.`\n        );\n      }\n      // Track if the property is being reflected to avoid\n      // setting the property again via `attributeChangedCallback`. Note:\n      // 1. this takes advantage of the fact that the callback is synchronous.\n      // 2. will behave incorrectly if multiple attributes are in the reaction\n      // stack at time of calling. However, since we process attributes\n      // in `update` this should not be possible (or an extreme corner case\n      // that we'd like to discover).\n      // mark state reflecting\n      this.__reflectingProperty = name;\n      if (attrValue == null) {\n        this.removeAttribute(attr);\n      } else {\n        this.setAttribute(attr, attrValue as string);\n      }\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /** @internal */\n  _$attributeToProperty(name: string, value: string | null) {\n    const ctor = this.constructor as typeof ReactiveElement;\n    // Note, hint this as an `AttributeMap` so closure clearly understands\n    // the type; it has issues with tracking types through statics\n    const propName = (ctor.__attributeToPropertyMap as AttributeMap).get(name);\n    // Use tracking info to avoid reflecting a property value to an attribute\n    // if it was just set because the attribute changed.\n    if (propName !== undefined && this.__reflectingProperty !== propName) {\n      const options = ctor.getPropertyOptions(propName);\n      const converter =\n        typeof options.converter === 'function'\n          ? {fromAttribute: options.converter}\n          : options.converter?.fromAttribute !== undefined\n          ? options.converter\n          : defaultConverter;\n      // mark state reflecting\n      this.__reflectingProperty = propName;\n      this[propName as keyof this] = converter.fromAttribute!(\n        value,\n        options.type\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      ) as any;\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /**\n   * Requests an update which is processed asynchronously. This should be called\n   * when an element should update based on some state not triggered by setting\n   * a reactive property. In this case, pass no arguments. It should also be\n   * called when manually implementing a property setter. In this case, pass the\n   * property `name` and `oldValue` to ensure that any configured property\n   * options are honored.\n   *\n   * @param name name of requesting property\n   * @param oldValue old value of requesting property\n   * @param options property options to use instead of the previously\n   *     configured options\n   * @category updates\n   */\n  requestUpdate(\n    name?: PropertyKey,\n    oldValue?: unknown,\n    options?: PropertyDeclaration\n  ): void {\n    let shouldRequestUpdate = true;\n    // If we have a property key, perform property update steps.\n    if (name !== undefined) {\n      options =\n        options ||\n        (this.constructor as typeof ReactiveElement).getPropertyOptions(name);\n      const hasChanged = options.hasChanged || notEqual;\n      if (hasChanged(this[name as keyof this], oldValue)) {\n        if (!this._$changedProperties.has(name)) {\n          this._$changedProperties.set(name, oldValue);\n        }\n        // Add to reflecting properties set.\n        // Note, it's important that every change has a chance to add the\n        // property to `_reflectingProperties`. This ensures setting\n        // attribute + property reflects correctly.\n        if (options.reflect === true && this.__reflectingProperty !== name) {\n          if (this.__reflectingProperties === undefined) {\n            this.__reflectingProperties = new Map();\n          }\n          this.__reflectingProperties.set(name, options);\n        }\n      } else {\n        // Abort the request if the property should not be considered changed.\n        shouldRequestUpdate = false;\n      }\n    }\n    if (!this.isUpdatePending && shouldRequestUpdate) {\n      this.__updatePromise = this.__enqueueUpdate();\n    }\n    // Note, since this no longer returns a promise, in dev mode we return a\n    // thenable which warns if it's called.\n    return DEV_MODE\n      ? (requestUpdateThenable(this.localName) as unknown as void)\n      : undefined;\n  }\n\n  /**\n   * Sets up the element to asynchronously update.\n   */\n  private async __enqueueUpdate() {\n    this.isUpdatePending = true;\n    try {\n      // Ensure any previous update has resolved before updating.\n      // This `await` also ensures that property changes are batched.\n      await this.__updatePromise;\n    } catch (e) {\n      // Refire any previous errors async so they do not disrupt the update\n      // cycle. Errors are refired so developers have a chance to observe\n      // them, and this can be done by implementing\n      // `window.onunhandledrejection`.\n      Promise.reject(e);\n    }\n    const result = this.scheduleUpdate();\n    // If `scheduleUpdate` returns a Promise, we await it. This is done to\n    // enable coordinating updates with a scheduler. Note, the result is\n    // checked to avoid delaying an additional microtask unless we need to.\n    if (result != null) {\n      await result;\n    }\n    return !this.isUpdatePending;\n  }\n\n  /**\n   * Schedules an element update. You can override this method to change the\n   * timing of updates by returning a Promise. The update will await the\n   * returned Promise, and you should resolve the Promise to allow the update\n   * to proceed. If this method is overridden, `super.scheduleUpdate()`\n   * must be called.\n   *\n   * For instance, to schedule updates to occur just before the next frame:\n   *\n   * ```ts\n   * override protected async scheduleUpdate(): Promise<unknown> {\n   *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n   *   super.scheduleUpdate();\n   * }\n   * ```\n   * @category updates\n   */\n  protected scheduleUpdate(): void | Promise<unknown> {\n    return this.performUpdate();\n  }\n\n  /**\n   * Performs an element update. Note, if an exception is thrown during the\n   * update, `firstUpdated` and `updated` will not be called.\n   *\n   * Call `performUpdate()` to immediately process a pending update. This should\n   * generally not be needed, but it can be done in rare cases when you need to\n   * update synchronously.\n   *\n   * Note: To ensure `performUpdate()` synchronously completes a pending update,\n   * it should not be overridden. In LitElement 2.x it was suggested to override\n   * `performUpdate()` to also customizing update scheduling. Instead, you should now\n   * override `scheduleUpdate()`. For backwards compatibility with LitElement 2.x,\n   * scheduling updates via `performUpdate()` continues to work, but will make\n   * also calling `performUpdate()` to synchronously process updates difficult.\n   *\n   * @category updates\n   */\n  protected performUpdate(): void | Promise<unknown> {\n    // Abort any update if one is not pending when this is called.\n    // This can happen if `performUpdate` is called early to \"flush\"\n    // the update.\n    if (!this.isUpdatePending) {\n      return;\n    }\n    debugLogEvent?.({kind: 'update'});\n    // create renderRoot before first update.\n    if (!this.hasUpdated) {\n      // Produce warning if any class properties are shadowed by class fields\n      if (DEV_MODE) {\n        const shadowedProperties: string[] = [];\n        (\n          this.constructor as typeof ReactiveElement\n        ).__reactivePropertyKeys?.forEach((p) => {\n          if (this.hasOwnProperty(p) && !this.__instanceProperties?.has(p)) {\n            shadowedProperties.push(p as string);\n          }\n        });\n        if (shadowedProperties.length) {\n          throw new Error(\n            `The following properties on element ${this.localName} will not ` +\n              `trigger updates as expected because they are set using class ` +\n              `fields: ${shadowedProperties.join(', ')}. ` +\n              `Native class fields and some compiled output will overwrite ` +\n              `accessors used for detecting changes. See ` +\n              `https://lit.dev/msg/class-field-shadowing ` +\n              `for more information.`\n          );\n        }\n      }\n    }\n    // Mixin instance properties once, if they exist.\n    if (this.__instanceProperties) {\n      // Use forEach so this works even if for/of loops are compiled to for loops\n      // expecting arrays\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      this.__instanceProperties!.forEach((v, p) => ((this as any)[p] = v));\n      this.__instanceProperties = undefined;\n    }\n    let shouldUpdate = false;\n    const changedProperties = this._$changedProperties;\n    try {\n      shouldUpdate = this.shouldUpdate(changedProperties);\n      if (shouldUpdate) {\n        this.willUpdate(changedProperties);\n        this.__controllers?.forEach((c) => c.hostUpdate?.());\n        this.update(changedProperties);\n      } else {\n        this.__markUpdated();\n      }\n    } catch (e) {\n      // Prevent `firstUpdated` and `updated` from running when there's an\n      // update exception.\n      shouldUpdate = false;\n      // Ensure element can accept additional updates after an exception.\n      this.__markUpdated();\n      throw e;\n    }\n    // The update is no longer considered pending and further updates are now allowed.\n    if (shouldUpdate) {\n      this._$didUpdate(changedProperties);\n    }\n  }\n\n  /**\n   * Invoked before `update()` to compute values needed during the update.\n   *\n   * Implement `willUpdate` to compute property values that depend on other\n   * properties and are used in the rest of the update process.\n   *\n   * ```ts\n   * willUpdate(changedProperties) {\n   *   // only need to check changed properties for an expensive computation.\n   *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n   *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n   *   }\n   * }\n   *\n   * render() {\n   *   return html`SHA: ${this.sha}`;\n   * }\n   * ```\n   *\n   * @category updates\n   */\n  protected willUpdate(_changedProperties: PropertyValues): void {}\n\n  // Note, this is an override point for polyfill-support.\n  // @internal\n  _$didUpdate(changedProperties: PropertyValues) {\n    this.__controllers?.forEach((c) => c.hostUpdated?.());\n    if (!this.hasUpdated) {\n      this.hasUpdated = true;\n      this.firstUpdated(changedProperties);\n    }\n    this.updated(changedProperties);\n    if (\n      DEV_MODE &&\n      this.isUpdatePending &&\n      (this.constructor as typeof ReactiveElement).enabledWarnings!.indexOf(\n        'change-in-update'\n      ) >= 0\n    ) {\n      issueWarning(\n        'change-in-update',\n        `Element ${this.localName} scheduled an update ` +\n          `(generally because a property was set) ` +\n          `after an update completed, causing a new update to be scheduled. ` +\n          `This is inefficient and should be avoided unless the next update ` +\n          `can only be scheduled as a side effect of the previous update.`\n      );\n    }\n  }\n\n  private __markUpdated() {\n    this._$changedProperties = new Map();\n    this.isUpdatePending = false;\n  }\n\n  /**\n   * Returns a Promise that resolves when the element has completed updating.\n   * The Promise value is a boolean that is `true` if the element completed the\n   * update without triggering another update. The Promise result is `false` if\n   * a property was set inside `updated()`. If the Promise is rejected, an\n   * exception was thrown during the update.\n   *\n   * To await additional asynchronous work, override the `getUpdateComplete`\n   * method. For example, it is sometimes useful to await a rendered element\n   * before fulfilling this Promise. To do this, first await\n   * `super.getUpdateComplete()`, then any subsequent state.\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  get updateComplete(): Promise<boolean> {\n    return this.getUpdateComplete();\n  }\n\n  /**\n   * Override point for the `updateComplete` promise.\n   *\n   * It is not safe to override the `updateComplete` getter directly due to a\n   * limitation in TypeScript which means it is not possible to call a\n   * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n   * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n   * This method should be overridden instead. For example:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   override async getUpdateComplete() {\n   *     const result = await super.getUpdateComplete();\n   *     await this._myChild.updateComplete;\n   *     return result;\n   *   }\n   * }\n   * ```\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  protected getUpdateComplete(): Promise<boolean> {\n    return this.__updatePromise;\n  }\n\n  /**\n   * Controls whether or not `update()` should be called when the element requests\n   * an update. By default, this method always returns `true`, but this can be\n   * customized to control when to update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected shouldUpdate(_changedProperties: PropertyValues): boolean {\n    return true;\n  }\n\n  /**\n   * Updates the element. This method reflects property values to attributes.\n   * It can be overridden to render and keep updated element DOM.\n   * Setting properties inside this method will *not* trigger\n   * another update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected update(_changedProperties: PropertyValues) {\n    if (this.__reflectingProperties !== undefined) {\n      // Use forEach so this works even if for/of loops are compiled to for\n      // loops expecting arrays\n      this.__reflectingProperties.forEach((v, k) =>\n        this.__propertyToAttribute(k, this[k as keyof this], v)\n      );\n      this.__reflectingProperties = undefined;\n    }\n    this.__markUpdated();\n  }\n\n  /**\n   * Invoked whenever the element is updated. Implement to perform\n   * post-updating tasks via DOM APIs, for example, focusing an element.\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected updated(_changedProperties: PropertyValues) {}\n\n  /**\n   * Invoked when the element is first updated. Implement to perform one time\n   * work on the element after update.\n   *\n   * ```ts\n   * firstUpdated() {\n   *   this.renderRoot.getElementById('my-text-area').focus();\n   * }\n   * ```\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected firstUpdated(_changedProperties: PropertyValues) {}\n}\n\n// Apply polyfills if available\npolyfillSupport?.({ReactiveElement});\n\n// Dev mode warnings...\nif (DEV_MODE) {\n  // Default warning set.\n  ReactiveElement.enabledWarnings = ['change-in-update'];\n  const ensureOwnWarnings = function (ctor: typeof ReactiveElement) {\n    if (\n      !ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings', ctor))\n    ) {\n      ctor.enabledWarnings = ctor.enabledWarnings!.slice();\n    }\n  };\n  ReactiveElement.enableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    if (this.enabledWarnings!.indexOf(warning) < 0) {\n      this.enabledWarnings!.push(warning);\n    }\n  };\n  ReactiveElement.disableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    const i = this.enabledWarnings!.indexOf(warning);\n    if (i >= 0) {\n      this.enabledWarnings!.splice(i, 1);\n    }\n  };\n}\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n(global.reactiveElementVersions ??= []).push('1.6.3');\nif (DEV_MODE && global.reactiveElementVersions.length > 1) {\n  issueWarning!(\n    'multiple-versions',\n    `Multiple versions of Lit loaded. Loading multiple versions ` +\n      `is not recommended.`\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// IMPORTANT: these imports must be type-only\nimport type {Directive, DirectiveResult, PartInfo} from './directive.js';\n\nconst DEV_MODE = true;\nconst ENABLE_EXTRA_SECURITY_HOOKS = true;\nconst ENABLE_SHADYDOM_NOPATCH = true;\nconst NODE_MODE = false;\n// Use window for browser builds because IE11 doesn't have globalThis.\nconst global = NODE_MODE ? globalThis : window;\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace LitUnstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry =\n      | TemplatePrep\n      | TemplateInstantiated\n      | TemplateInstantiatedAndUpdated\n      | TemplateUpdating\n      | BeginRender\n      | EndRender\n      | CommitPartEntry\n      | SetPartValue;\n    export interface TemplatePrep {\n      kind: 'template prep';\n      template: Template;\n      strings: TemplateStringsArray;\n      clonableTemplate: HTMLTemplateElement;\n      parts: TemplatePart[];\n    }\n    export interface BeginRender {\n      kind: 'begin render';\n      id: number;\n      value: unknown;\n      container: HTMLElement | DocumentFragment;\n      options: RenderOptions | undefined;\n      part: ChildPart | undefined;\n    }\n    export interface EndRender {\n      kind: 'end render';\n      id: number;\n      value: unknown;\n      container: HTMLElement | DocumentFragment;\n      options: RenderOptions | undefined;\n      part: ChildPart;\n    }\n    export interface TemplateInstantiated {\n      kind: 'template instantiated';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      fragment: Node;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface TemplateInstantiatedAndUpdated {\n      kind: 'template instantiated and updated';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      fragment: Node;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface TemplateUpdating {\n      kind: 'template updating';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface SetPartValue {\n      kind: 'set part';\n      part: Part;\n      value: unknown;\n      valueIndex: number;\n      values: unknown[];\n      templateInstance: TemplateInstance;\n    }\n\n    export type CommitPartEntry =\n      | CommitNothingToChildEntry\n      | CommitText\n      | CommitNode\n      | CommitAttribute\n      | CommitProperty\n      | CommitBooleanAttribute\n      | CommitEventListener\n      | CommitToElementBinding;\n\n    export interface CommitNothingToChildEntry {\n      kind: 'commit nothing to child';\n      start: ChildNode;\n      end: ChildNode | null;\n      parent: Disconnectable | undefined;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitText {\n      kind: 'commit text';\n      node: Text;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitNode {\n      kind: 'commit node';\n      start: Node;\n      parent: Disconnectable | undefined;\n      value: Node;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitAttribute {\n      kind: 'commit attribute';\n      element: Element;\n      name: string;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitProperty {\n      kind: 'commit property';\n      element: Element;\n      name: string;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitBooleanAttribute {\n      kind: 'commit boolean attribute';\n      element: Element;\n      name: string;\n      value: boolean;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitEventListener {\n      kind: 'commit event listener';\n      element: Element;\n      name: string;\n      value: unknown;\n      oldListener: unknown;\n      options: RenderOptions | undefined;\n      // True if we're removing the old event listener (e.g. because settings changed, or value is nothing)\n      removeListener: boolean;\n      // True if we're adding a new event listener (e.g. because first render, or settings changed)\n      addListener: boolean;\n    }\n\n    export interface CommitToElementBinding {\n      kind: 'commit to element binding';\n      element: Element;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n  }\n}\n\ninterface DebugLoggingWindow {\n  // Even in dev mode, we generally don't want to emit these events, as that's\n  // another level of cost, so only emit them when DEV_MODE is true _and_ when\n  // window.emitLitDebugEvents is true.\n  emitLitDebugLogEvents?: boolean;\n}\n\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n  ? (event: LitUnstable.DebugLog.Entry) => {\n      const shouldEmit = (global as unknown as DebugLoggingWindow)\n        .emitLitDebugLogEvents;\n      if (!shouldEmit) {\n        return;\n      }\n      global.dispatchEvent(\n        new CustomEvent<LitUnstable.DebugLog.Entry>('lit-debug', {\n          detail: event,\n        })\n      );\n    }\n  : undefined;\n// Used for connecting beginRender and endRender events when there are nested\n// renders when errors are thrown preventing an endRender event from being\n// called.\nlet debugLogRenderId = 0;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  global.litIssuedWarnings ??= new Set();\n\n  // Issue a warning, if we haven't already.\n  issueWarning = (code: string, warning: string) => {\n    warning += code\n      ? ` See https://lit.dev/msg/${code} for more information.`\n      : '';\n    if (!global.litIssuedWarnings!.has(warning)) {\n      console.warn(warning);\n      global.litIssuedWarnings!.add(warning);\n    }\n  };\n\n  issueWarning(\n    'dev-mode',\n    `Lit is in dev mode. Not recommended for production!`\n  );\n}\n\nconst wrap =\n  ENABLE_SHADYDOM_NOPATCH &&\n  global.ShadyDOM?.inUse &&\n  global.ShadyDOM?.noPatch === true\n    ? global.ShadyDOM!.wrap\n    : (node: Node) => node;\n\nconst trustedTypes = (global as unknown as Partial<Window>).trustedTypes;\n\n/**\n * Our TrustedTypePolicy for HTML which is declared using the html template\n * tag function.\n *\n * That HTML is a developer-authored constant, and is parsed with innerHTML\n * before any untrusted expressions have been mixed in. Therefor it is\n * considered safe by construction.\n */\nconst policy = trustedTypes\n  ? trustedTypes.createPolicy('lit-html', {\n      createHTML: (s) => s,\n    })\n  : undefined;\n\n/**\n * Used to sanitize any value before it is written into the DOM. This can be\n * used to implement a security policy of allowed and disallowed values in\n * order to prevent XSS attacks.\n *\n * One way of using this callback would be to check attributes and properties\n * against a list of high risk fields, and require that values written to such\n * fields be instances of a class which is safe by construction. Closure's Safe\n * HTML Types is one implementation of this technique (\n * https://github.com/google/safe-html-types/blob/master/doc/safehtml-types.md).\n * The TrustedTypes polyfill in API-only mode could also be used as a basis\n * for this technique (https://github.com/WICG/trusted-types).\n *\n * @param node The HTML node (usually either a #text node or an Element) that\n *     is being written to. Note that this is just an exemplar node, the write\n *     may take place against another instance of the same class of node.\n * @param name The name of an attribute or property (for example, 'href').\n * @param type Indicates whether the write that's about to be performed will\n *     be to a property or a node.\n * @return A function that will sanitize this class of writes.\n */\nexport type SanitizerFactory = (\n  node: Node,\n  name: string,\n  type: 'property' | 'attribute'\n) => ValueSanitizer;\n\n/**\n * A function which can sanitize values that will be written to a specific kind\n * of DOM sink.\n *\n * See SanitizerFactory.\n *\n * @param value The value to sanitize. Will be the actual value passed into\n *     the lit-html template literal, so this could be of any type.\n * @return The value to write to the DOM. Usually the same as the input value,\n *     unless sanitization is needed.\n */\nexport type ValueSanitizer = (value: unknown) => unknown;\n\nconst identityFunction: ValueSanitizer = (value: unknown) => value;\nconst noopSanitizer: SanitizerFactory = (\n  _node: Node,\n  _name: string,\n  _type: 'property' | 'attribute'\n) => identityFunction;\n\n/** Sets the global sanitizer factory. */\nconst setSanitizer = (newSanitizer: SanitizerFactory) => {\n  if (!ENABLE_EXTRA_SECURITY_HOOKS) {\n    return;\n  }\n  if (sanitizerFactoryInternal !== noopSanitizer) {\n    throw new Error(\n      `Attempted to overwrite existing lit-html security policy.` +\n        ` setSanitizeDOMValueFactory should be called at most once.`\n    );\n  }\n  sanitizerFactoryInternal = newSanitizer;\n};\n\n/**\n * Only used in internal tests, not a part of the public API.\n */\nconst _testOnlyClearSanitizerFactoryDoNotCallOrElse = () => {\n  sanitizerFactoryInternal = noopSanitizer;\n};\n\nconst createSanitizer: SanitizerFactory = (node, name, type) => {\n  return sanitizerFactoryInternal(node, name, type);\n};\n\n// Added to an attribute name to mark the attribute as bound so we can find\n// it easily.\nconst boundAttributeSuffix = '$lit$';\n\n// This marker is used in many syntactic positions in HTML, so it must be\n// a valid element name and attribute name. We don't support dynamic names (yet)\n// but this at least ensures that the parse tree is closer to the template\n// intention.\nconst marker = `lit$${String(Math.random()).slice(9)}$`;\n\n// String used to tell if a comment is a marker comment\nconst markerMatch = '?' + marker;\n\n// Text used to insert a comment marker node. We use processing instruction\n// syntax because it's slightly smaller, but parses as a comment node.\nconst nodeMarker = `<${markerMatch}>`;\n\nconst d =\n  NODE_MODE && global.document === undefined\n    ? ({\n        createTreeWalker() {\n          return {};\n        },\n      } as unknown as Document)\n    : document;\n\n// Creates a dynamic marker. We never have to search for these in the DOM.\nconst createMarker = () => d.createComment('');\n\n// https://tc39.github.io/ecma262/#sec-typeof-operator\ntype Primitive = null | undefined | boolean | number | string | symbol | bigint;\nconst isPrimitive = (value: unknown): value is Primitive =>\n  value === null || (typeof value != 'object' && typeof value != 'function');\nconst isArray = Array.isArray;\nconst isIterable = (value: unknown): value is Iterable<unknown> =>\n  isArray(value) ||\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  typeof (value as any)?.[Symbol.iterator] === 'function';\n\nconst SPACE_CHAR = `[ \\t\\n\\f\\r]`;\nconst ATTR_VALUE_CHAR = `[^ \\t\\n\\f\\r\"'\\`<>=]`;\nconst NAME_CHAR = `[^\\\\s\"'>=/]`;\n\n// These regexes represent the five parsing states that we care about in the\n// Template's HTML scanner. They match the *end* of the state they're named\n// after.\n// Depending on the match, we transition to a new state. If there's no match,\n// we stay in the same state.\n// Note that the regexes are stateful. We utilize lastIndex and sync it\n// across the multiple regexes used. In addition to the five regexes below\n// we also dynamically create a regex to find the matching end tags for raw\n// text elements.\n\n/**\n * End of text is: `<` followed by:\n *   (comment start) or (tag) or (dynamic tag binding)\n */\nconst textEndRegex = /<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g;\nconst COMMENT_START = 1;\nconst TAG_NAME = 2;\nconst DYNAMIC_TAG_NAME = 3;\n\nconst commentEndRegex = /-->/g;\n/**\n * Comments not started with <!--, like </{, can be ended by a single `>`\n */\nconst comment2EndRegex = />/g;\n\n/**\n * The tagEnd regex matches the end of the \"inside an opening\" tag syntax\n * position. It either matches a `>`, an attribute-like sequence, or the end\n * of the string after a space (attribute-name position ending).\n *\n * See attributes in the HTML spec:\n * https://www.w3.org/TR/html5/syntax.html#elements-attributes\n *\n * \" \\t\\n\\f\\r\" are HTML space characters:\n * https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * So an attribute is:\n *  * The name: any character except a whitespace character, (\"), ('), \">\",\n *    \"=\", or \"/\". Note: this is different from the HTML spec which also excludes control characters.\n *  * Followed by zero or more space characters\n *  * Followed by \"=\"\n *  * Followed by zero or more space characters\n *  * Followed by:\n *    * Any character except space, ('), (\"), \"<\", \">\", \"=\", (`), or\n *    * (\") then any non-(\"), or\n *    * (') then any non-(')\n */\nconst tagEndRegex = new RegExp(\n  `>|${SPACE_CHAR}(?:(${NAME_CHAR}+)(${SPACE_CHAR}*=${SPACE_CHAR}*(?:${ATTR_VALUE_CHAR}|(\"|')|))|$)`,\n  'g'\n);\nconst ENTIRE_MATCH = 0;\nconst ATTRIBUTE_NAME = 1;\nconst SPACES_AND_EQUALS = 2;\nconst QUOTE_CHAR = 3;\n\nconst singleQuoteAttrEndRegex = /'/g;\nconst doubleQuoteAttrEndRegex = /\"/g;\n/**\n * Matches the raw text elements.\n *\n * Comments are not parsed within raw text elements, so we need to search their\n * text content for marker strings.\n */\nconst rawTextElement = /^(?:script|style|textarea|title)$/i;\n\n/** TemplateResult types */\nconst HTML_RESULT = 1;\nconst SVG_RESULT = 2;\n\ntype ResultType = typeof HTML_RESULT | typeof SVG_RESULT;\n\n// TemplatePart types\n// IMPORTANT: these must match the values in PartType\nconst ATTRIBUTE_PART = 1;\nconst CHILD_PART = 2;\nconst PROPERTY_PART = 3;\nconst BOOLEAN_ATTRIBUTE_PART = 4;\nconst EVENT_PART = 5;\nconst ELEMENT_PART = 6;\nconst COMMENT_PART = 7;\n\n/**\n * The return type of the template tag functions, {@linkcode html} and\n * {@linkcode svg}.\n *\n * A `TemplateResult` object holds all the information about a template\n * expression required to render it: the template strings, expression values,\n * and type of template (html or svg).\n *\n * `TemplateResult` objects do not create any DOM on their own. To create or\n * update DOM you need to render the `TemplateResult`. See\n * [Rendering](https://lit.dev/docs/components/rendering) for more information.\n *\n */\nexport type TemplateResult<T extends ResultType = ResultType> = {\n  // This property needs to remain unminified.\n  ['_$litType$']: T;\n  strings: TemplateStringsArray;\n  values: unknown[];\n};\n\nexport type HTMLTemplateResult = TemplateResult<typeof HTML_RESULT>;\n\nexport type SVGTemplateResult = TemplateResult<typeof SVG_RESULT>;\n\nexport interface CompiledTemplateResult {\n  // This is a factory in order to make template initialization lazy\n  // and allow ShadyRenderOptions scope to be passed in.\n  // This property needs to remain unminified.\n  ['_$litType$']: CompiledTemplate;\n  values: unknown[];\n}\n\nexport interface CompiledTemplate extends Omit<Template, 'el'> {\n  // el is overridden to be optional. We initialize it on first render\n  el?: HTMLTemplateElement;\n\n  // The prepared HTML string to create a template element from.\n  // The type is a TemplateStringsArray to guarantee that the value came from\n  // source code, preventing a JSON injection attack.\n  h: TemplateStringsArray;\n}\n\n/**\n * Generates a template literal tag function that returns a TemplateResult with\n * the given result type.\n */\nconst tag =\n  <T extends ResultType>(type: T) =>\n  (strings: TemplateStringsArray, ...values: unknown[]): TemplateResult<T> => {\n    // Warn against templates octal escape sequences\n    // We do this here rather than in render so that the warning is closer to the\n    // template definition.\n    if (DEV_MODE && strings.some((s) => s === undefined)) {\n      console.warn(\n        'Some template strings are undefined.\\n' +\n          'This is probably caused by illegal octal escape sequences.'\n      );\n    }\n    return {\n      // This property needs to remain unminified.\n      ['_$litType$']: type,\n      strings,\n      values,\n    };\n  };\n\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const header = (title: string) => html`<h1>${title}</h1>`;\n * ```\n *\n * The `html` tag returns a description of the DOM to render as a value. It is\n * lazy, meaning no work is done until the template is rendered. When rendering,\n * if a template comes from the same expression as a previously rendered result,\n * it's efficiently updated instead of replaced.\n */\nexport const html = tag(HTML_RESULT);\n\n/**\n * Interprets a template literal as an SVG fragment that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const rect = svg`<rect width=\"10\" height=\"10\"></rect>`;\n *\n * const myImage = html`\n *   <svg viewBox=\"0 0 10 10\" xmlns=\"http://www.w3.org/2000/svg\">\n *     ${rect}\n *   </svg>`;\n * ```\n *\n * The `svg` *tag function* should only be used for SVG fragments, or elements\n * that would be contained **inside** an `<svg>` HTML element. A common error is\n * placing an `<svg>` *element* in a template tagged with the `svg` tag\n * function. The `<svg>` element is an HTML element and should be used within a\n * template tagged with the {@linkcode html} tag function.\n *\n * In LitElement usage, it's invalid to return an SVG fragment from the\n * `render()` method, as the SVG fragment will be contained within the element's\n * shadow root and thus cannot be used within an `<svg>` HTML element.\n */\nexport const svg = tag(SVG_RESULT);\n\n/**\n * A sentinel value that signals that a value was handled by a directive and\n * should not be written to the DOM.\n */\nexport const noChange = Symbol.for('lit-noChange');\n\n/**\n * A sentinel value that signals a ChildPart to fully clear its content.\n *\n * ```ts\n * const button = html`${\n *  user.isAdmin\n *    ? html`<button>DELETE</button>`\n *    : nothing\n * }`;\n * ```\n *\n * Prefer using `nothing` over other falsy values as it provides a consistent\n * behavior between various expression binding contexts.\n *\n * In child expressions, `undefined`, `null`, `''`, and `nothing` all behave the\n * same and render no nodes. In attribute expressions, `nothing` _removes_ the\n * attribute, while `undefined` and `null` will render an empty string. In\n * property expressions `nothing` becomes `undefined`.\n */\nexport const nothing = Symbol.for('lit-nothing');\n\n/**\n * The cache of prepared templates, keyed by the tagged TemplateStringsArray\n * and _not_ accounting for the specific template tag used. This means that\n * template tags cannot be dynamic - the must statically be one of html, svg,\n * or attr. This restriction simplifies the cache lookup, which is on the hot\n * path for rendering.\n */\nconst templateCache = new WeakMap<TemplateStringsArray, Template>();\n\n/**\n * Object specifying options for controlling lit-html rendering. Note that\n * while `render` may be called multiple times on the same `container` (and\n * `renderBefore` reference node) to efficiently update the rendered content,\n * only the options passed in during the first render are respected during\n * the lifetime of renders to that unique `container` + `renderBefore`\n * combination.\n */\nexport interface RenderOptions {\n  /**\n   * An object to use as the `this` value for event listeners. It's often\n   * useful to set this to the host component rendering a template.\n   */\n  host?: object;\n  /**\n   * A DOM node before which to render content in the container.\n   */\n  renderBefore?: ChildNode | null;\n  /**\n   * Node used for cloning the template (`importNode` will be called on this\n   * node). This controls the `ownerDocument` of the rendered DOM, along with\n   * any inherited context. Defaults to the global `document`.\n   */\n  creationScope?: {importNode(node: Node, deep?: boolean): Node};\n  /**\n   * The initial connected state for the top-level part being rendered. If no\n   * `isConnected` option is set, `AsyncDirective`s will be connected by\n   * default. Set to `false` if the initial render occurs in a disconnected tree\n   * and `AsyncDirective`s should see `isConnected === false` for their initial\n   * render. The `part.setConnected()` method must be used subsequent to initial\n   * render to change the connected state of the part.\n   */\n  isConnected?: boolean;\n}\n\nconst walker = d.createTreeWalker(\n  d,\n  129 /* NodeFilter.SHOW_{ELEMENT|COMMENT} */,\n  null,\n  false\n);\n\nlet sanitizerFactoryInternal: SanitizerFactory = noopSanitizer;\n\n//\n// Classes only below here, const variable declarations only above here...\n//\n// Keeping variable declarations and classes together improves minification.\n// Interfaces and type aliases can be interleaved freely.\n//\n\n// Type for classes that have a `_directive` or `_directives[]` field, used by\n// `resolveDirective`\nexport interface DirectiveParent {\n  _$parent?: DirectiveParent;\n  _$isConnected: boolean;\n  __directive?: Directive;\n  __directives?: Array<Directive | undefined>;\n}\n\nfunction trustFromTemplateString(\n  tsa: TemplateStringsArray,\n  stringFromTSA: string\n): TrustedHTML {\n  // A security check to prevent spoofing of Lit template results.\n  // In the future, we may be able to replace this with Array.isTemplateObject,\n  // though we might need to make that check inside of the html and svg\n  // functions, because precompiled templates don't come in as\n  // TemplateStringArray objects.\n  if (!Array.isArray(tsa) || !tsa.hasOwnProperty('raw')) {\n    let message = 'invalid template strings array';\n    if (DEV_MODE) {\n      message = `\n          Internal Error: expected template strings to be an array\n          with a 'raw' field. Faking a template strings array by\n          calling html or svg like an ordinary function is effectively\n          the same as calling unsafeHtml and can lead to major security\n          issues, e.g. opening your code up to XSS attacks.\n          If you're using the html or svg tagged template functions normally\n          and still seeing this error, please file a bug at\n          https://github.com/lit/lit/issues/new?template=bug_report.md\n          and include information about your build tooling, if any.\n        `\n        .trim()\n        .replace(/\\n */g, '\\n');\n    }\n    throw new Error(message);\n  }\n  return policy !== undefined\n    ? policy.createHTML(stringFromTSA)\n    : (stringFromTSA as unknown as TrustedHTML);\n}\n\n/**\n * Returns an HTML string for the given TemplateStringsArray and result type\n * (HTML or SVG), along with the case-sensitive bound attribute names in\n * template order. The HTML contains comment markers denoting the `ChildPart`s\n * and suffixes on bound attributes denoting the `AttributeParts`.\n *\n * @param strings template strings array\n * @param type HTML or SVG\n * @return Array containing `[html, attrNames]` (array returned for terseness,\n *     to avoid object fields since this code is shared with non-minified SSR\n *     code)\n */\nconst getTemplateHtml = (\n  strings: TemplateStringsArray,\n  type: ResultType\n): [TrustedHTML, Array<string | undefined>] => {\n  // Insert makers into the template HTML to represent the position of\n  // bindings. The following code scans the template strings to determine the\n  // syntactic position of the bindings. They can be in text position, where\n  // we insert an HTML comment, attribute value position, where we insert a\n  // sentinel string and re-write the attribute name, or inside a tag where\n  // we insert the sentinel string.\n  const l = strings.length - 1;\n  // Stores the case-sensitive bound attribute names in the order of their\n  // parts. ElementParts are also reflected in this array as undefined\n  // rather than a string, to disambiguate from attribute bindings.\n  const attrNames: Array<string | undefined> = [];\n  let html = type === SVG_RESULT ? '<svg>' : '';\n\n  // When we're inside a raw text tag (not it's text content), the regex\n  // will still be tagRegex so we can find attributes, but will switch to\n  // this regex when the tag ends.\n  let rawTextEndRegex: RegExp | undefined;\n\n  // The current parsing state, represented as a reference to one of the\n  // regexes\n  let regex = textEndRegex;\n\n  for (let i = 0; i < l; i++) {\n    const s = strings[i];\n    // The index of the end of the last attribute name. When this is\n    // positive at end of a string, it means we're in an attribute value\n    // position and need to rewrite the attribute name.\n    // We also use a special value of -2 to indicate that we encountered\n    // the end of a string in attribute name position.\n    let attrNameEndIndex = -1;\n    let attrName: string | undefined;\n    let lastIndex = 0;\n    let match!: RegExpExecArray | null;\n\n    // The conditions in this loop handle the current parse state, and the\n    // assignments to the `regex` variable are the state transitions.\n    while (lastIndex < s.length) {\n      // Make sure we start searching from where we previously left off\n      regex.lastIndex = lastIndex;\n      match = regex.exec(s);\n      if (match === null) {\n        break;\n      }\n      lastIndex = regex.lastIndex;\n      if (regex === textEndRegex) {\n        if (match[COMMENT_START] === '!--') {\n          regex = commentEndRegex;\n        } else if (match[COMMENT_START] !== undefined) {\n          // We started a weird comment, like </{\n          regex = comment2EndRegex;\n        } else if (match[TAG_NAME] !== undefined) {\n          if (rawTextElement.test(match[TAG_NAME])) {\n            // Record if we encounter a raw-text element. We'll switch to\n            // this regex at the end of the tag.\n            rawTextEndRegex = new RegExp(`</${match[TAG_NAME]}`, 'g');\n          }\n          regex = tagEndRegex;\n        } else if (match[DYNAMIC_TAG_NAME] !== undefined) {\n          if (DEV_MODE) {\n            throw new Error(\n              'Bindings in tag names are not supported. Please use static templates instead. ' +\n                'See https://lit.dev/docs/templates/expressions/#static-expressions'\n            );\n          }\n          regex = tagEndRegex;\n        }\n      } else if (regex === tagEndRegex) {\n        if (match[ENTIRE_MATCH] === '>') {\n          // End of a tag. If we had started a raw-text element, use that\n          // regex\n          regex = rawTextEndRegex ?? textEndRegex;\n          // We may be ending an unquoted attribute value, so make sure we\n          // clear any pending attrNameEndIndex\n          attrNameEndIndex = -1;\n        } else if (match[ATTRIBUTE_NAME] === undefined) {\n          // Attribute name position\n          attrNameEndIndex = -2;\n        } else {\n          attrNameEndIndex = regex.lastIndex - match[SPACES_AND_EQUALS].length;\n          attrName = match[ATTRIBUTE_NAME];\n          regex =\n            match[QUOTE_CHAR] === undefined\n              ? tagEndRegex\n              : match[QUOTE_CHAR] === '\"'\n              ? doubleQuoteAttrEndRegex\n              : singleQuoteAttrEndRegex;\n        }\n      } else if (\n        regex === doubleQuoteAttrEndRegex ||\n        regex === singleQuoteAttrEndRegex\n      ) {\n        regex = tagEndRegex;\n      } else if (regex === commentEndRegex || regex === comment2EndRegex) {\n        regex = textEndRegex;\n      } else {\n        // Not one of the five state regexes, so it must be the dynamically\n        // created raw text regex and we're at the close of that element.\n        regex = tagEndRegex;\n        rawTextEndRegex = undefined;\n      }\n    }\n\n    if (DEV_MODE) {\n      // If we have a attrNameEndIndex, which indicates that we should\n      // rewrite the attribute name, assert that we're in a valid attribute\n      // position - either in a tag, or a quoted attribute value.\n      console.assert(\n        attrNameEndIndex === -1 ||\n          regex === tagEndRegex ||\n          regex === singleQuoteAttrEndRegex ||\n          regex === doubleQuoteAttrEndRegex,\n        'unexpected parse state B'\n      );\n    }\n\n    // We have four cases:\n    //  1. We're in text position, and not in a raw text element\n    //     (regex === textEndRegex): insert a comment marker.\n    //  2. We have a non-negative attrNameEndIndex which means we need to\n    //     rewrite the attribute name to add a bound attribute suffix.\n    //  3. We're at the non-first binding in a multi-binding attribute, use a\n    //     plain marker.\n    //  4. We're somewhere else inside the tag. If we're in attribute name\n    //     position (attrNameEndIndex === -2), add a sequential suffix to\n    //     generate a unique attribute name.\n\n    // Detect a binding next to self-closing tag end and insert a space to\n    // separate the marker from the tag end:\n    const end =\n      regex === tagEndRegex && strings[i + 1].startsWith('/>') ? ' ' : '';\n    html +=\n      regex === textEndRegex\n        ? s + nodeMarker\n        : attrNameEndIndex >= 0\n        ? (attrNames.push(attrName!),\n          s.slice(0, attrNameEndIndex) +\n            boundAttributeSuffix +\n            s.slice(attrNameEndIndex)) +\n          marker +\n          end\n        : s +\n          marker +\n          (attrNameEndIndex === -2 ? (attrNames.push(undefined), i) : end);\n  }\n\n  const htmlResult: string | TrustedHTML =\n    html + (strings[l] || '<?>') + (type === SVG_RESULT ? '</svg>' : '');\n\n  // Returned as an array for terseness\n  return [trustFromTemplateString(strings, htmlResult), attrNames];\n};\n\n/** @internal */\nexport type {Template};\nclass Template {\n  /** @internal */\n  el!: HTMLTemplateElement;\n\n  parts: Array<TemplatePart> = [];\n\n  constructor(\n    // This property needs to remain unminified.\n    {strings, ['_$litType$']: type}: TemplateResult,\n    options?: RenderOptions\n  ) {\n    let node: Node | null;\n    let nodeIndex = 0;\n    let attrNameIndex = 0;\n    const partCount = strings.length - 1;\n    const parts = this.parts;\n\n    // Create template element\n    const [html, attrNames] = getTemplateHtml(strings, type);\n    this.el = Template.createElement(html, options);\n    walker.currentNode = this.el.content;\n\n    // Reparent SVG nodes into template root\n    if (type === SVG_RESULT) {\n      const content = this.el.content;\n      const svgElement = content.firstChild!;\n      svgElement.remove();\n      content.append(...svgElement.childNodes);\n    }\n\n    // Walk the template to find binding markers and create TemplateParts\n    while ((node = walker.nextNode()) !== null && parts.length < partCount) {\n      if (node.nodeType === 1) {\n        if (DEV_MODE) {\n          const tag = (node as Element).localName;\n          // Warn if `textarea` includes an expression and throw if `template`\n          // does since these are not supported. We do this by checking\n          // innerHTML for anything that looks like a marker. This catches\n          // cases like bindings in textarea there markers turn into text nodes.\n          if (\n            /^(?:textarea|template)$/i!.test(tag) &&\n            (node as Element).innerHTML.includes(marker)\n          ) {\n            const m =\n              `Expressions are not supported inside \\`${tag}\\` ` +\n              `elements. See https://lit.dev/msg/expression-in-${tag} for more ` +\n              `information.`;\n            if (tag === 'template') {\n              throw new Error(m);\n            } else issueWarning('', m);\n          }\n        }\n        // TODO (justinfagnani): for attempted dynamic tag names, we don't\n        // increment the bindingIndex, and it'll be off by 1 in the element\n        // and off by two after it.\n        if ((node as Element).hasAttributes()) {\n          // We defer removing bound attributes because on IE we might not be\n          // iterating attributes in their template order, and would sometimes\n          // remove an attribute that we still need to create a part for.\n          const attrsToRemove = [];\n          for (const name of (node as Element).getAttributeNames()) {\n            // `name` is the name of the attribute we're iterating over, but not\n            // _necessarily_ the name of the attribute we will create a part\n            // for. They can be different in browsers that don't iterate on\n            // attributes in source order. In that case the attrNames array\n            // contains the attribute name we'll process next. We only need the\n            // attribute name here to know if we should process a bound attribute\n            // on this element.\n            if (\n              name.endsWith(boundAttributeSuffix) ||\n              name.startsWith(marker)\n            ) {\n              const realName = attrNames[attrNameIndex++];\n              attrsToRemove.push(name);\n              if (realName !== undefined) {\n                // Lowercase for case-sensitive SVG attributes like viewBox\n                const value = (node as Element).getAttribute(\n                  realName.toLowerCase() + boundAttributeSuffix\n                )!;\n                const statics = value.split(marker);\n                const m = /([.?@])?(.*)/.exec(realName)!;\n                parts.push({\n                  type: ATTRIBUTE_PART,\n                  index: nodeIndex,\n                  name: m[2],\n                  strings: statics,\n                  ctor:\n                    m[1] === '.'\n                      ? PropertyPart\n                      : m[1] === '?'\n                      ? BooleanAttributePart\n                      : m[1] === '@'\n                      ? EventPart\n                      : AttributePart,\n                });\n              } else {\n                parts.push({\n                  type: ELEMENT_PART,\n                  index: nodeIndex,\n                });\n              }\n            }\n          }\n          for (const name of attrsToRemove) {\n            (node as Element).removeAttribute(name);\n          }\n        }\n        // TODO (justinfagnani): benchmark the regex against testing for each\n        // of the 3 raw text element names.\n        if (rawTextElement.test((node as Element).tagName)) {\n          // For raw text elements we need to split the text content on\n          // markers, create a Text node for each segment, and create\n          // a TemplatePart for each marker.\n          const strings = (node as Element).textContent!.split(marker);\n          const lastIndex = strings.length - 1;\n          if (lastIndex > 0) {\n            (node as Element).textContent = trustedTypes\n              ? (trustedTypes.emptyScript as unknown as '')\n              : '';\n            // Generate a new text node for each literal section\n            // These nodes are also used as the markers for node parts\n            // We can't use empty text nodes as markers because they're\n            // normalized when cloning in IE (could simplify when\n            // IE is no longer supported)\n            for (let i = 0; i < lastIndex; i++) {\n              (node as Element).append(strings[i], createMarker());\n              // Walk past the marker node we just added\n              walker.nextNode();\n              parts.push({type: CHILD_PART, index: ++nodeIndex});\n            }\n            // Note because this marker is added after the walker's current\n            // node, it will be walked to in the outer loop (and ignored), so\n            // we don't need to adjust nodeIndex here\n            (node as Element).append(strings[lastIndex], createMarker());\n          }\n        }\n      } else if (node.nodeType === 8) {\n        const data = (node as Comment).data;\n        if (data === markerMatch) {\n          parts.push({type: CHILD_PART, index: nodeIndex});\n        } else {\n          let i = -1;\n          while ((i = (node as Comment).data.indexOf(marker, i + 1)) !== -1) {\n            // Comment node has a binding marker inside, make an inactive part\n            // The binding won't work, but subsequent bindings will\n            parts.push({type: COMMENT_PART, index: nodeIndex});\n            // Move to the end of the match\n            i += marker.length - 1;\n          }\n        }\n      }\n      nodeIndex++;\n    }\n    // We could set walker.currentNode to another node here to prevent a memory\n    // leak, but every time we prepare a template, we immediately render it\n    // and re-use the walker in new TemplateInstance._clone().\n    debugLogEvent?.({\n      kind: 'template prep',\n      template: this,\n      clonableTemplate: this.el,\n      parts: this.parts,\n      strings,\n    });\n  }\n\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @nocollapse */\n  static createElement(html: TrustedHTML, _options?: RenderOptions) {\n    const el = d.createElement('template');\n    el.innerHTML = html as unknown as string;\n    return el;\n  }\n}\n\nexport interface Disconnectable {\n  _$parent?: Disconnectable;\n  _$disconnectableChildren?: Set<Disconnectable>;\n  // Rather than hold connection state on instances, Disconnectables recursively\n  // fetch the connection state from the RootPart they are connected in via\n  // getters up the Disconnectable tree via _$parent references. This pushes the\n  // cost of tracking the isConnected state to `AsyncDirectives`, and avoids\n  // needing to pass all Disconnectables (parts, template instances, and\n  // directives) their connection state each time it changes, which would be\n  // costly for trees that have no AsyncDirectives.\n  _$isConnected: boolean;\n}\n\nfunction resolveDirective(\n  part: ChildPart | AttributePart | ElementPart,\n  value: unknown,\n  parent: DirectiveParent = part,\n  attributeIndex?: number\n): unknown {\n  // Bail early if the value is explicitly noChange. Note, this means any\n  // nested directive is still attached and is not run.\n  if (value === noChange) {\n    return value;\n  }\n  let currentDirective =\n    attributeIndex !== undefined\n      ? (parent as AttributePart).__directives?.[attributeIndex]\n      : (parent as ChildPart | ElementPart | Directive).__directive;\n  const nextDirectiveConstructor = isPrimitive(value)\n    ? undefined\n    : // This property needs to remain unminified.\n      (value as DirectiveResult)['_$litDirective$'];\n  if (currentDirective?.constructor !== nextDirectiveConstructor) {\n    // This property needs to remain unminified.\n    currentDirective?.['_$notifyDirectiveConnectionChanged']?.(false);\n    if (nextDirectiveConstructor === undefined) {\n      currentDirective = undefined;\n    } else {\n      currentDirective = new nextDirectiveConstructor(part as PartInfo);\n      currentDirective._$initialize(part, parent, attributeIndex);\n    }\n    if (attributeIndex !== undefined) {\n      ((parent as AttributePart).__directives ??= [])[attributeIndex] =\n        currentDirective;\n    } else {\n      (parent as ChildPart | Directive).__directive = currentDirective;\n    }\n  }\n  if (currentDirective !== undefined) {\n    value = resolveDirective(\n      part,\n      currentDirective._$resolve(part, (value as DirectiveResult).values),\n      currentDirective,\n      attributeIndex\n    );\n  }\n  return value;\n}\n\nexport type {TemplateInstance};\n/**\n * An updateable instance of a Template. Holds references to the Parts used to\n * update the template instance.\n */\nclass TemplateInstance implements Disconnectable {\n  _$template: Template;\n  _$parts: Array<Part | undefined> = [];\n\n  /** @internal */\n  _$parent: ChildPart;\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  constructor(template: Template, parent: ChildPart) {\n    this._$template = template;\n    this._$parent = parent;\n  }\n\n  // Called by ChildPart parentNode getter\n  get parentNode() {\n    return this._$parent.parentNode;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  // This method is separate from the constructor because we need to return a\n  // DocumentFragment and we don't want to hold onto it with an instance field.\n  _clone(options: RenderOptions | undefined) {\n    const {\n      el: {content},\n      parts: parts,\n    } = this._$template;\n    const fragment = (options?.creationScope ?? d).importNode(content, true);\n    walker.currentNode = fragment;\n\n    let node = walker.nextNode()!;\n    let nodeIndex = 0;\n    let partIndex = 0;\n    let templatePart = parts[0];\n\n    while (templatePart !== undefined) {\n      if (nodeIndex === templatePart.index) {\n        let part: Part | undefined;\n        if (templatePart.type === CHILD_PART) {\n          part = new ChildPart(\n            node as HTMLElement,\n            node.nextSibling,\n            this,\n            options\n          );\n        } else if (templatePart.type === ATTRIBUTE_PART) {\n          part = new templatePart.ctor(\n            node as HTMLElement,\n            templatePart.name,\n            templatePart.strings,\n            this,\n            options\n          );\n        } else if (templatePart.type === ELEMENT_PART) {\n          part = new ElementPart(node as HTMLElement, this, options);\n        }\n        this._$parts.push(part);\n        templatePart = parts[++partIndex];\n      }\n      if (nodeIndex !== templatePart?.index) {\n        node = walker.nextNode()!;\n        nodeIndex++;\n      }\n    }\n    // We need to set the currentNode away from the cloned tree so that we\n    // don't hold onto the tree even if the tree is detached and should be\n    // freed.\n    walker.currentNode = d;\n    return fragment;\n  }\n\n  _update(values: Array<unknown>) {\n    let i = 0;\n    for (const part of this._$parts) {\n      if (part !== undefined) {\n        debugLogEvent?.({\n          kind: 'set part',\n          part,\n          value: values[i],\n          valueIndex: i,\n          values,\n          templateInstance: this,\n        });\n        if ((part as AttributePart).strings !== undefined) {\n          (part as AttributePart)._$setValue(values, part as AttributePart, i);\n          // The number of values the part consumes is part.strings.length - 1\n          // since values are in between template spans. We increment i by 1\n          // later in the loop, so increment it by part.strings.length - 2 here\n          i += (part as AttributePart).strings!.length - 2;\n        } else {\n          part._$setValue(values[i]);\n        }\n      }\n      i++;\n    }\n  }\n}\n\n/*\n * Parts\n */\ntype AttributeTemplatePart = {\n  readonly type: typeof ATTRIBUTE_PART;\n  readonly index: number;\n  readonly name: string;\n  readonly ctor: typeof AttributePart;\n  readonly strings: ReadonlyArray<string>;\n};\ntype ChildTemplatePart = {\n  readonly type: typeof CHILD_PART;\n  readonly index: number;\n};\ntype ElementTemplatePart = {\n  readonly type: typeof ELEMENT_PART;\n  readonly index: number;\n};\ntype CommentTemplatePart = {\n  readonly type: typeof COMMENT_PART;\n  readonly index: number;\n};\n\n/**\n * A TemplatePart represents a dynamic part in a template, before the template\n * is instantiated. When a template is instantiated Parts are created from\n * TemplateParts.\n */\ntype TemplatePart =\n  | ChildTemplatePart\n  | AttributeTemplatePart\n  | ElementTemplatePart\n  | CommentTemplatePart;\n\nexport type Part =\n  | ChildPart\n  | AttributePart\n  | PropertyPart\n  | BooleanAttributePart\n  | ElementPart\n  | EventPart;\n\nexport type {ChildPart};\nclass ChildPart implements Disconnectable {\n  readonly type = CHILD_PART;\n  readonly options: RenderOptions | undefined;\n  _$committedValue: unknown = nothing;\n  /** @internal */\n  __directive?: Directive;\n  /** @internal */\n  _$startNode: ChildNode;\n  /** @internal */\n  _$endNode: ChildNode | null;\n  private _textSanitizer: ValueSanitizer | undefined;\n  /** @internal */\n  _$parent: Disconnectable | undefined;\n  /**\n   * Connection state for RootParts only (i.e. ChildPart without _$parent\n   * returned from top-level `render`). This field is unsed otherwise. The\n   * intention would clearer if we made `RootPart` a subclass of `ChildPart`\n   * with this field (and a different _$isConnected getter), but the subclass\n   * caused a perf regression, possibly due to making call sites polymorphic.\n   * @internal\n   */\n  __isConnected: boolean;\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    // ChildParts that are not at the root should always be created with a\n    // parent; only RootChildNode's won't, so they return the local isConnected\n    // state\n    return this._$parent?._$isConnected ?? this.__isConnected;\n  }\n\n  // The following fields will be patched onto ChildParts when required by\n  // AsyncDirective\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n  /** @internal */\n  _$notifyConnectionChanged?(\n    isConnected: boolean,\n    removeFromParent?: boolean,\n    from?: number\n  ): void;\n  /** @internal */\n  _$reparentDisconnectables?(parent: Disconnectable): void;\n\n  constructor(\n    startNode: ChildNode,\n    endNode: ChildNode | null,\n    parent: TemplateInstance | ChildPart | undefined,\n    options: RenderOptions | undefined\n  ) {\n    this._$startNode = startNode;\n    this._$endNode = endNode;\n    this._$parent = parent;\n    this.options = options;\n    // Note __isConnected is only ever accessed on RootParts (i.e. when there is\n    // no _$parent); the value on a non-root-part is \"don't care\", but checking\n    // for parent would be more code\n    this.__isConnected = options?.isConnected ?? true;\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      // Explicitly initialize for consistent class shape.\n      this._textSanitizer = undefined;\n    }\n  }\n\n  /**\n   * The parent node into which the part renders its content.\n   *\n   * A ChildPart's content consists of a range of adjacent child nodes of\n   * `.parentNode`, possibly bordered by 'marker nodes' (`.startNode` and\n   * `.endNode`).\n   *\n   * - If both `.startNode` and `.endNode` are non-null, then the part's content\n   * consists of all siblings between `.startNode` and `.endNode`, exclusively.\n   *\n   * - If `.startNode` is non-null but `.endNode` is null, then the part's\n   * content consists of all siblings following `.startNode`, up to and\n   * including the last child of `.parentNode`. If `.endNode` is non-null, then\n   * `.startNode` will always be non-null.\n   *\n   * - If both `.endNode` and `.startNode` are null, then the part's content\n   * consists of all child nodes of `.parentNode`.\n   */\n  get parentNode(): Node {\n    let parentNode: Node = wrap(this._$startNode).parentNode!;\n    const parent = this._$parent;\n    if (\n      parent !== undefined &&\n      parentNode?.nodeType === 11 /* Node.DOCUMENT_FRAGMENT */\n    ) {\n      // If the parentNode is a DocumentFragment, it may be because the DOM is\n      // still in the cloned fragment during initial render; if so, get the real\n      // parentNode the part will be committed into by asking the parent.\n      parentNode = (parent as ChildPart | TemplateInstance).parentNode;\n    }\n    return parentNode;\n  }\n\n  /**\n   * The part's leading marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get startNode(): Node | null {\n    return this._$startNode;\n  }\n\n  /**\n   * The part's trailing marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get endNode(): Node | null {\n    return this._$endNode;\n  }\n\n  _$setValue(value: unknown, directiveParent: DirectiveParent = this): void {\n    if (DEV_MODE && this.parentNode === null) {\n      throw new Error(\n        `This \\`ChildPart\\` has no \\`parentNode\\` and therefore cannot accept a value. This likely means the element containing the part was manipulated in an unsupported way outside of Lit's control such that the part's marker nodes were ejected from DOM. For example, setting the element's \\`innerHTML\\` or \\`textContent\\` can do this.`\n      );\n    }\n    value = resolveDirective(this, value, directiveParent);\n    if (isPrimitive(value)) {\n      // Non-rendering child values. It's important that these do not render\n      // empty text nodes to avoid issues with preventing default <slot>\n      // fallback content.\n      if (value === nothing || value == null || value === '') {\n        if (this._$committedValue !== nothing) {\n          debugLogEvent?.({\n            kind: 'commit nothing to child',\n            start: this._$startNode,\n            end: this._$endNode,\n            parent: this._$parent,\n            options: this.options,\n          });\n          this._$clear();\n        }\n        this._$committedValue = nothing;\n      } else if (value !== this._$committedValue && value !== noChange) {\n        this._commitText(value);\n      }\n      // This property needs to remain unminified.\n    } else if ((value as TemplateResult)['_$litType$'] !== undefined) {\n      this._commitTemplateResult(value as TemplateResult);\n    } else if ((value as Node).nodeType !== undefined) {\n      if (DEV_MODE && this.options?.host === value) {\n        this._commitText(\n          `[probable mistake: rendered a template's host in itself ` +\n            `(commonly caused by writing \\${this} in a template]`\n        );\n        console.warn(\n          `Attempted to render the template host`,\n          value,\n          `inside itself. This is almost always a mistake, and in dev mode `,\n          `we render some warning text. In production however, we'll `,\n          `render it, which will usually result in an error, and sometimes `,\n          `in the element disappearing from the DOM.`\n        );\n        return;\n      }\n      this._commitNode(value as Node);\n    } else if (isIterable(value)) {\n      this._commitIterable(value);\n    } else {\n      // Fallback, will render the string representation\n      this._commitText(value);\n    }\n  }\n\n  private _insert<T extends Node>(node: T) {\n    return wrap(wrap(this._$startNode).parentNode!).insertBefore(\n      node,\n      this._$endNode\n    );\n  }\n\n  private _commitNode(value: Node): void {\n    if (this._$committedValue !== value) {\n      this._$clear();\n      if (\n        ENABLE_EXTRA_SECURITY_HOOKS &&\n        sanitizerFactoryInternal !== noopSanitizer\n      ) {\n        const parentNodeName = this._$startNode.parentNode?.nodeName;\n        if (parentNodeName === 'STYLE' || parentNodeName === 'SCRIPT') {\n          let message = 'Forbidden';\n          if (DEV_MODE) {\n            if (parentNodeName === 'STYLE') {\n              message =\n                `Lit does not support binding inside style nodes. ` +\n                `This is a security risk, as style injection attacks can ` +\n                `exfiltrate data and spoof UIs. ` +\n                `Consider instead using css\\`...\\` literals ` +\n                `to compose styles, and make do dynamic styling with ` +\n                `css custom properties, ::parts, <slot>s, ` +\n                `and by mutating the DOM rather than stylesheets.`;\n            } else {\n              message =\n                `Lit does not support binding inside script nodes. ` +\n                `This is a security risk, as it could allow arbitrary ` +\n                `code execution.`;\n            }\n          }\n          throw new Error(message);\n        }\n      }\n      debugLogEvent?.({\n        kind: 'commit node',\n        start: this._$startNode,\n        parent: this._$parent,\n        value: value,\n        options: this.options,\n      });\n      this._$committedValue = this._insert(value);\n    }\n  }\n\n  private _commitText(value: unknown): void {\n    // If the committed value is a primitive it means we called _commitText on\n    // the previous render, and we know that this._$startNode.nextSibling is a\n    // Text node. We can now just replace the text content (.data) of the node.\n    if (\n      this._$committedValue !== nothing &&\n      isPrimitive(this._$committedValue)\n    ) {\n      const node = wrap(this._$startNode).nextSibling as Text;\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(node, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n      }\n      debugLogEvent?.({\n        kind: 'commit text',\n        node,\n        value,\n        options: this.options,\n      });\n      (node as Text).data = value as string;\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        const textNode = d.createTextNode('');\n        this._commitNode(textNode);\n        // When setting text content, for security purposes it matters a lot\n        // what the parent is. For example, <style> and <script> need to be\n        // handled with care, while <span> does not. So first we need to put a\n        // text node into the document, then we can sanitize its content.\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(textNode, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n        debugLogEvent?.({\n          kind: 'commit text',\n          node: textNode,\n          value,\n          options: this.options,\n        });\n        textNode.data = value as string;\n      } else {\n        this._commitNode(d.createTextNode(value as string));\n        debugLogEvent?.({\n          kind: 'commit text',\n          node: wrap(this._$startNode).nextSibling as Text,\n          value,\n          options: this.options,\n        });\n      }\n    }\n    this._$committedValue = value;\n  }\n\n  private _commitTemplateResult(\n    result: TemplateResult | CompiledTemplateResult\n  ): void {\n    // This property needs to remain unminified.\n    const {values, ['_$litType$']: type} = result;\n    // If $litType$ is a number, result is a plain TemplateResult and we get\n    // the template from the template cache. If not, result is a\n    // CompiledTemplateResult and _$litType$ is a CompiledTemplate and we need\n    // to create the <template> element the first time we see it.\n    const template: Template | CompiledTemplate =\n      typeof type === 'number'\n        ? this._$getTemplate(result as TemplateResult)\n        : (type.el === undefined &&\n            (type.el = Template.createElement(\n              trustFromTemplateString(type.h, type.h[0]),\n              this.options\n            )),\n          type);\n\n    if ((this._$committedValue as TemplateInstance)?._$template === template) {\n      debugLogEvent?.({\n        kind: 'template updating',\n        template,\n        instance: this._$committedValue as TemplateInstance,\n        parts: (this._$committedValue as TemplateInstance)._$parts,\n        options: this.options,\n        values,\n      });\n      (this._$committedValue as TemplateInstance)._update(values);\n    } else {\n      const instance = new TemplateInstance(template as Template, this);\n      const fragment = instance._clone(this.options);\n      debugLogEvent?.({\n        kind: 'template instantiated',\n        template,\n        instance,\n        parts: instance._$parts,\n        options: this.options,\n        fragment,\n        values,\n      });\n      instance._update(values);\n      debugLogEvent?.({\n        kind: 'template instantiated and updated',\n        template,\n        instance,\n        parts: instance._$parts,\n        options: this.options,\n        fragment,\n        values,\n      });\n      this._commitNode(fragment);\n      this._$committedValue = instance;\n    }\n  }\n\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @internal */\n  _$getTemplate(result: TemplateResult) {\n    let template = templateCache.get(result.strings);\n    if (template === undefined) {\n      templateCache.set(result.strings, (template = new Template(result)));\n    }\n    return template;\n  }\n\n  private _commitIterable(value: Iterable<unknown>): void {\n    // For an Iterable, we create a new InstancePart per item, then set its\n    // value to the item. This is a little bit of overhead for every item in\n    // an Iterable, but it lets us recurse easily and efficiently update Arrays\n    // of TemplateResults that will be commonly returned from expressions like:\n    // array.map((i) => html`${i}`), by reusing existing TemplateInstances.\n\n    // If value is an array, then the previous render was of an\n    // iterable and value will contain the ChildParts from the previous\n    // render. If value is not an array, clear this part and make a new\n    // array for ChildParts.\n    if (!isArray(this._$committedValue)) {\n      this._$committedValue = [];\n      this._$clear();\n    }\n\n    // Lets us keep track of how many items we stamped so we can clear leftover\n    // items from a previous render\n    const itemParts = this._$committedValue as ChildPart[];\n    let partIndex = 0;\n    let itemPart: ChildPart | undefined;\n\n    for (const item of value) {\n      if (partIndex === itemParts.length) {\n        // If no existing part, create a new one\n        // TODO (justinfagnani): test perf impact of always creating two parts\n        // instead of sharing parts between nodes\n        // https://github.com/lit/lit/issues/1266\n        itemParts.push(\n          (itemPart = new ChildPart(\n            this._insert(createMarker()),\n            this._insert(createMarker()),\n            this,\n            this.options\n          ))\n        );\n      } else {\n        // Reuse an existing part\n        itemPart = itemParts[partIndex];\n      }\n      itemPart._$setValue(item);\n      partIndex++;\n    }\n\n    if (partIndex < itemParts.length) {\n      // itemParts always have end nodes\n      this._$clear(\n        itemPart && wrap(itemPart._$endNode!).nextSibling,\n        partIndex\n      );\n      // Truncate the parts array so _value reflects the current state\n      itemParts.length = partIndex;\n    }\n  }\n\n  /**\n   * Removes the nodes contained within this Part from the DOM.\n   *\n   * @param start Start node to clear from, for clearing a subset of the part's\n   *     DOM (used when truncating iterables)\n   * @param from  When `start` is specified, the index within the iterable from\n   *     which ChildParts are being removed, used for disconnecting directives in\n   *     those Parts.\n   *\n   * @internal\n   */\n  _$clear(\n    start: ChildNode | null = wrap(this._$startNode).nextSibling,\n    from?: number\n  ) {\n    this._$notifyConnectionChanged?.(false, true, from);\n    while (start && start !== this._$endNode) {\n      const n = wrap(start!).nextSibling;\n      (wrap(start!) as Element).remove();\n      start = n;\n    }\n  }\n  /**\n   * Implementation of RootPart's `isConnected`. Note that this metod\n   * should only be called on `RootPart`s (the `ChildPart` returned from a\n   * top-level `render()` call). It has no effect on non-root ChildParts.\n   * @param isConnected Whether to set\n   * @internal\n   */\n  setConnected(isConnected: boolean) {\n    if (this._$parent === undefined) {\n      this.__isConnected = isConnected;\n      this._$notifyConnectionChanged?.(isConnected);\n    } else if (DEV_MODE) {\n      throw new Error(\n        'part.setConnected() may only be called on a ' +\n          'RootPart returned from render().'\n      );\n    }\n  }\n}\n\n/**\n * A top-level `ChildPart` returned from `render` that manages the connected\n * state of `AsyncDirective`s created throughout the tree below it.\n */\nexport interface RootPart extends ChildPart {\n  /**\n   * Sets the connection state for `AsyncDirective`s contained within this root\n   * ChildPart.\n   *\n   * lit-html does not automatically monitor the connectedness of DOM rendered;\n   * as such, it is the responsibility of the caller to `render` to ensure that\n   * `part.setConnected(false)` is called before the part object is potentially\n   * discarded, to ensure that `AsyncDirective`s have a chance to dispose of\n   * any resources being held. If a `RootPart` that was previously\n   * disconnected is subsequently re-connected (and its `AsyncDirective`s should\n   * re-connect), `setConnected(true)` should be called.\n   *\n   * @param isConnected Whether directives within this tree should be connected\n   * or not\n   */\n  setConnected(isConnected: boolean): void;\n}\n\nexport type {AttributePart};\nclass AttributePart implements Disconnectable {\n  readonly type = ATTRIBUTE_PART as\n    | typeof ATTRIBUTE_PART\n    | typeof PROPERTY_PART\n    | typeof BOOLEAN_ATTRIBUTE_PART\n    | typeof EVENT_PART;\n  readonly element: HTMLElement;\n  readonly name: string;\n  readonly options: RenderOptions | undefined;\n\n  /**\n   * If this attribute part represents an interpolation, this contains the\n   * static strings of the interpolation. For single-value, complete bindings,\n   * this is undefined.\n   */\n  readonly strings?: ReadonlyArray<string>;\n  /** @internal */\n  _$committedValue: unknown | Array<unknown> = nothing;\n  /** @internal */\n  __directives?: Array<Directive | undefined>;\n  /** @internal */\n  _$parent: Disconnectable;\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  protected _sanitizer: ValueSanitizer | undefined;\n\n  get tagName() {\n    return this.element.tagName;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  constructor(\n    element: HTMLElement,\n    name: string,\n    strings: ReadonlyArray<string>,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    this.element = element;\n    this.name = name;\n    this._$parent = parent;\n    this.options = options;\n    if (strings.length > 2 || strings[0] !== '' || strings[1] !== '') {\n      this._$committedValue = new Array(strings.length - 1).fill(new String());\n      this.strings = strings;\n    } else {\n      this._$committedValue = nothing;\n    }\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      this._sanitizer = undefined;\n    }\n  }\n\n  /**\n   * Sets the value of this part by resolving the value from possibly multiple\n   * values and static strings and committing it to the DOM.\n   * If this part is single-valued, `this._strings` will be undefined, and the\n   * method will be called with a single value argument. If this part is\n   * multi-value, `this._strings` will be defined, and the method is called\n   * with the value array of the part's owning TemplateInstance, and an offset\n   * into the value array from which the values should be read.\n   * This method is overloaded this way to eliminate short-lived array slices\n   * of the template instance values, and allow a fast-path for single-valued\n   * parts.\n   *\n   * @param value The part value, or an array of values for multi-valued parts\n   * @param valueIndex the index to start reading values from. `undefined` for\n   *   single-valued parts\n   * @param noCommit causes the part to not commit its value to the DOM. Used\n   *   in hydration to prime attribute parts with their first-rendered value,\n   *   but not set the attribute, and in SSR to no-op the DOM operation and\n   *   capture the value for serialization.\n   *\n   * @internal\n   */\n  _$setValue(\n    value: unknown | Array<unknown>,\n    directiveParent: DirectiveParent = this,\n    valueIndex?: number,\n    noCommit?: boolean\n  ) {\n    const strings = this.strings;\n\n    // Whether any of the values has changed, for dirty-checking\n    let change = false;\n\n    if (strings === undefined) {\n      // Single-value binding case\n      value = resolveDirective(this, value, directiveParent, 0);\n      change =\n        !isPrimitive(value) ||\n        (value !== this._$committedValue && value !== noChange);\n      if (change) {\n        this._$committedValue = value;\n      }\n    } else {\n      // Interpolation case\n      const values = value as Array<unknown>;\n      value = strings[0];\n\n      let i, v;\n      for (i = 0; i < strings.length - 1; i++) {\n        v = resolveDirective(this, values[valueIndex! + i], directiveParent, i);\n\n        if (v === noChange) {\n          // If the user-provided value is `noChange`, use the previous value\n          v = (this._$committedValue as Array<unknown>)[i];\n        }\n        change ||=\n          !isPrimitive(v) || v !== (this._$committedValue as Array<unknown>)[i];\n        if (v === nothing) {\n          value = nothing;\n        } else if (value !== nothing) {\n          value += (v ?? '') + strings[i + 1];\n        }\n        // We always record each value, even if one is `nothing`, for future\n        // change detection.\n        (this._$committedValue as Array<unknown>)[i] = v;\n      }\n    }\n    if (change && !noCommit) {\n      this._commitValue(value);\n    }\n  }\n\n  /** @internal */\n  _commitValue(value: unknown) {\n    if (value === nothing) {\n      (wrap(this.element) as Element).removeAttribute(this.name);\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._sanitizer === undefined) {\n          this._sanitizer = sanitizerFactoryInternal(\n            this.element,\n            this.name,\n            'attribute'\n          );\n        }\n        value = this._sanitizer(value ?? '');\n      }\n      debugLogEvent?.({\n        kind: 'commit attribute',\n        element: this.element,\n        name: this.name,\n        value,\n        options: this.options,\n      });\n      (wrap(this.element) as Element).setAttribute(\n        this.name,\n        (value ?? '') as string\n      );\n    }\n  }\n}\n\nexport type {PropertyPart};\nclass PropertyPart extends AttributePart {\n  override readonly type = PROPERTY_PART;\n\n  /** @internal */\n  override _commitValue(value: unknown) {\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      if (this._sanitizer === undefined) {\n        this._sanitizer = sanitizerFactoryInternal(\n          this.element,\n          this.name,\n          'property'\n        );\n      }\n      value = this._sanitizer(value);\n    }\n    debugLogEvent?.({\n      kind: 'commit property',\n      element: this.element,\n      name: this.name,\n      value,\n      options: this.options,\n    });\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (this.element as any)[this.name] = value === nothing ? undefined : value;\n  }\n}\n\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n  ? (trustedTypes.emptyScript as unknown as '')\n  : '';\n\nexport type {BooleanAttributePart};\nclass BooleanAttributePart extends AttributePart {\n  override readonly type = BOOLEAN_ATTRIBUTE_PART;\n\n  /** @internal */\n  override _commitValue(value: unknown) {\n    debugLogEvent?.({\n      kind: 'commit boolean attribute',\n      element: this.element,\n      name: this.name,\n      value: !!(value && value !== nothing),\n      options: this.options,\n    });\n    if (value && value !== nothing) {\n      (wrap(this.element) as Element).setAttribute(\n        this.name,\n        emptyStringForBooleanAttribute\n      );\n    } else {\n      (wrap(this.element) as Element).removeAttribute(this.name);\n    }\n  }\n}\n\ntype EventListenerWithOptions = EventListenerOrEventListenerObject &\n  Partial<AddEventListenerOptions>;\n\n/**\n * An AttributePart that manages an event listener via add/removeEventListener.\n *\n * This part works by adding itself as the event listener on an element, then\n * delegating to the value passed to it. This reduces the number of calls to\n * add/removeEventListener if the listener changes frequently, such as when an\n * inline function is used as a listener.\n *\n * Because event options are passed when adding listeners, we must take case\n * to add and remove the part as a listener when the event options change.\n */\nexport type {EventPart};\nclass EventPart extends AttributePart {\n  override readonly type = EVENT_PART;\n\n  constructor(\n    element: HTMLElement,\n    name: string,\n    strings: ReadonlyArray<string>,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    super(element, name, strings, parent, options);\n\n    if (DEV_MODE && this.strings !== undefined) {\n      throw new Error(\n        `A \\`<${element.localName}>\\` has a \\`@${name}=...\\` listener with ` +\n          'invalid content. Event listeners in templates must have exactly ' +\n          'one expression and no surrounding text.'\n      );\n    }\n  }\n\n  // EventPart does not use the base _$setValue/_resolveValue implementation\n  // since the dirty checking is more complex\n  /** @internal */\n  override _$setValue(\n    newListener: unknown,\n    directiveParent: DirectiveParent = this\n  ) {\n    newListener =\n      resolveDirective(this, newListener, directiveParent, 0) ?? nothing;\n    if (newListener === noChange) {\n      return;\n    }\n    const oldListener = this._$committedValue;\n\n    // If the new value is nothing or any options change we have to remove the\n    // part as a listener.\n    const shouldRemoveListener =\n      (newListener === nothing && oldListener !== nothing) ||\n      (newListener as EventListenerWithOptions).capture !==\n        (oldListener as EventListenerWithOptions).capture ||\n      (newListener as EventListenerWithOptions).once !==\n        (oldListener as EventListenerWithOptions).once ||\n      (newListener as EventListenerWithOptions).passive !==\n        (oldListener as EventListenerWithOptions).passive;\n\n    // If the new value is not nothing and we removed the listener, we have\n    // to add the part as a listener.\n    const shouldAddListener =\n      newListener !== nothing &&\n      (oldListener === nothing || shouldRemoveListener);\n\n    debugLogEvent?.({\n      kind: 'commit event listener',\n      element: this.element,\n      name: this.name,\n      value: newListener,\n      options: this.options,\n      removeListener: shouldRemoveListener,\n      addListener: shouldAddListener,\n      oldListener,\n    });\n    if (shouldRemoveListener) {\n      this.element.removeEventListener(\n        this.name,\n        this,\n        oldListener as EventListenerWithOptions\n      );\n    }\n    if (shouldAddListener) {\n      // Beware: IE11 and Chrome 41 don't like using the listener as the\n      // options object. Figure out how to deal w/ this in IE11 - maybe\n      // patch addEventListener?\n      this.element.addEventListener(\n        this.name,\n        this,\n        newListener as EventListenerWithOptions\n      );\n    }\n    this._$committedValue = newListener;\n  }\n\n  handleEvent(event: Event) {\n    if (typeof this._$committedValue === 'function') {\n      this._$committedValue.call(this.options?.host ?? this.element, event);\n    } else {\n      (this._$committedValue as EventListenerObject).handleEvent(event);\n    }\n  }\n}\n\nexport type {ElementPart};\nclass ElementPart implements Disconnectable {\n  readonly type = ELEMENT_PART;\n\n  /** @internal */\n  __directive?: Directive;\n\n  // This is to ensure that every Part has a _$committedValue\n  _$committedValue: undefined;\n\n  /** @internal */\n  _$parent!: Disconnectable;\n\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  options: RenderOptions | undefined;\n\n  constructor(\n    public element: Element,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    this._$parent = parent;\n    this.options = options;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  _$setValue(value: unknown): void {\n    debugLogEvent?.({\n      kind: 'commit to element binding',\n      element: this.element,\n      value,\n      options: this.options,\n    });\n    resolveDirective(this, value);\n  }\n}\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LH object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-element, which re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LH = {\n  // Used in lit-ssr\n  _boundAttributeSuffix: boundAttributeSuffix,\n  _marker: marker,\n  _markerMatch: markerMatch,\n  _HTML_RESULT: HTML_RESULT,\n  _getTemplateHtml: getTemplateHtml,\n  // Used in tests and private-ssr-support\n  _TemplateInstance: TemplateInstance,\n  _isIterable: isIterable,\n  _resolveDirective: resolveDirective,\n  _ChildPart: ChildPart,\n  _AttributePart: AttributePart,\n  _BooleanAttributePart: BooleanAttributePart,\n  _EventPart: EventPart,\n  _PropertyPart: PropertyPart,\n  _ElementPart: ElementPart,\n};\n\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n  ? global.litHtmlPolyfillSupportDevMode\n  : global.litHtmlPolyfillSupport;\npolyfillSupport?.(Template, ChildPart);\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for lit-html usage.\n(global.litHtmlVersions ??= []).push('2.8.0');\nif (DEV_MODE && global.litHtmlVersions.length > 1) {\n  issueWarning!(\n    'multiple-versions',\n    `Multiple versions of Lit loaded. ` +\n      `Loading multiple versions is not recommended.`\n  );\n}\n\n/**\n * Renders a value, usually a lit-html TemplateResult, to the container.\n *\n * This example renders the text \"Hello, Zoe!\" inside a paragraph tag, appending\n * it to the container `document.body`.\n *\n * ```js\n * import {html, render} from 'lit';\n *\n * const name = \"Zoe\";\n * render(html`<p>Hello, ${name}!</p>`, document.body);\n * ```\n *\n * @param value Any [renderable\n *   value](https://lit.dev/docs/templates/expressions/#child-expressions),\n *   typically a {@linkcode TemplateResult} created by evaluating a template tag\n *   like {@linkcode html} or {@linkcode svg}.\n * @param container A DOM container to render to. The first render will append\n *   the rendered value to the container, and subsequent renders will\n *   efficiently update the rendered value if the same result type was\n *   previously rendered there.\n * @param options See {@linkcode RenderOptions} for options documentation.\n * @see\n * {@link https://lit.dev/docs/libraries/standalone-templates/#rendering-lit-html-templates| Rendering Lit HTML Templates}\n */\nexport const render = (\n  value: unknown,\n  container: HTMLElement | DocumentFragment,\n  options?: RenderOptions\n): RootPart => {\n  if (DEV_MODE && container == null) {\n    // Give a clearer error message than\n    //     Uncaught TypeError: Cannot read properties of null (reading\n    //     '_$litPart$')\n    // which reads like an internal Lit error.\n    throw new TypeError(`The container to render into may not be ${container}`);\n  }\n  const renderId = DEV_MODE ? debugLogRenderId++ : 0;\n  const partOwnerNode = options?.renderBefore ?? container;\n  // This property needs to remain unminified.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let part: ChildPart = (partOwnerNode as any)['_$litPart$'];\n  debugLogEvent?.({\n    kind: 'begin render',\n    id: renderId,\n    value,\n    container,\n    options,\n    part,\n  });\n  if (part === undefined) {\n    const endNode = options?.renderBefore ?? null;\n    // This property needs to remain unminified.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (partOwnerNode as any)['_$litPart$'] = part = new ChildPart(\n      container.insertBefore(createMarker(), endNode),\n      endNode,\n      undefined,\n      options ?? {}\n    );\n  }\n  part._$setValue(value);\n  debugLogEvent?.({\n    kind: 'end render',\n    id: renderId,\n    value,\n    container,\n    options,\n    part,\n  });\n  return part as RootPart;\n};\n\nif (ENABLE_EXTRA_SECURITY_HOOKS) {\n  render.setSanitizer = setSanitizer;\n  render.createSanitizer = createSanitizer;\n  if (DEV_MODE) {\n    render._testOnlyClearSanitizerFactoryDoNotCallOrElse =\n      _testOnlyClearSanitizerFactoryDoNotCallOrElse;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * The main LitElement module, which defines the {@linkcode LitElement} base\n * class and related APIs.\n *\n *  LitElement components can define a template and a set of observed\n * properties. Changing an observed property triggers a re-render of the\n * element.\n *\n *  Import {@linkcode LitElement} and {@linkcode html} from this module to\n * create a component:\n *\n *  ```js\n * import {LitElement, html} from 'lit-element';\n *\n * class MyElement extends LitElement {\n *\n *   // Declare observed properties\n *   static get properties() {\n *     return {\n *       adjective: {}\n *     }\n *   }\n *\n *   constructor() {\n *     this.adjective = 'awesome';\n *   }\n *\n *   // Define the element's template\n *   render() {\n *     return html`<p>your ${adjective} template here</p>`;\n *   }\n * }\n *\n * customElements.define('my-element', MyElement);\n * ```\n *\n * `LitElement` extends {@linkcode ReactiveElement} and adds lit-html\n * templating. The `ReactiveElement` class is provided for users that want to\n * build their own custom element base classes that don't use lit-html.\n *\n * @packageDocumentation\n */\nimport {PropertyValues, ReactiveElement} from '@lit/reactive-element';\nimport {render, RenderOptions, noChange, RootPart} from 'lit-html';\nexport * from '@lit/reactive-element';\nexport * from 'lit-html';\n\nimport {LitUnstable} from 'lit-html';\nimport {ReactiveUnstable} from '@lit/reactive-element';\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace Unstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry =\n      | LitUnstable.DebugLog.Entry\n      | ReactiveUnstable.DebugLog.Entry;\n  }\n}\n\n// For backwards compatibility export ReactiveElement as UpdatingElement. Note,\n// IE transpilation requires exporting like this.\nexport const UpdatingElement = ReactiveElement;\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  const issuedWarnings: Set<string | undefined> =\n    (globalThis.litIssuedWarnings ??= new Set());\n\n  // Issue a warning, if we haven't already.\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (!issuedWarnings.has(warning)) {\n      console.warn(warning);\n      issuedWarnings.add(warning);\n    }\n  };\n}\n\n/**\n * Base element class that manages element properties and attributes, and\n * renders a lit-html template.\n *\n * To define a component, subclass `LitElement` and implement a\n * `render` method to provide the component's template. Define properties\n * using the {@linkcode LitElement.properties properties} property or the\n * {@linkcode property} decorator.\n */\nexport class LitElement extends ReactiveElement {\n  /**\n   * Ensure this class is marked as `finalized` as an optimization ensuring\n   * it will not needlessly try to `finalize`.\n   *\n   * Note this property name is a string to prevent breaking Closure JS Compiler\n   * optimizations. See @lit/reactive-element for more information.\n   */\n  protected static override ['finalized'] = true;\n\n  // This property needs to remain unminified.\n  static ['_$litElement$'] = true;\n\n  /**\n   * @category rendering\n   */\n  readonly renderOptions: RenderOptions = {host: this};\n\n  private __childPart: RootPart | undefined = undefined;\n\n  /**\n   * @category rendering\n   */\n  protected override createRenderRoot() {\n    const renderRoot = super.createRenderRoot();\n    // When adoptedStyleSheets are shimmed, they are inserted into the\n    // shadowRoot by createRenderRoot. Adjust the renderBefore node so that\n    // any styles in Lit content render before adoptedStyleSheets. This is\n    // important so that adoptedStyleSheets have precedence over styles in\n    // the shadowRoot.\n    this.renderOptions.renderBefore ??= renderRoot!.firstChild as ChildNode;\n    return renderRoot;\n  }\n\n  /**\n   * Updates the element. This method reflects property values to attributes\n   * and calls `render` to render DOM via lit-html. Setting properties inside\n   * this method will *not* trigger another update.\n   * @param changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected override update(changedProperties: PropertyValues) {\n    // Setting properties in `render` should not trigger an update. Since\n    // updates are allowed after super.update, it's important to call `render`\n    // before that.\n    const value = this.render();\n    if (!this.hasUpdated) {\n      this.renderOptions.isConnected = this.isConnected;\n    }\n    super.update(changedProperties);\n    this.__childPart = render(value, this.renderRoot, this.renderOptions);\n  }\n\n  /**\n   * Invoked when the component is added to the document's DOM.\n   *\n   * In `connectedCallback()` you should setup tasks that should only occur when\n   * the element is connected to the document. The most common of these is\n   * adding event listeners to nodes external to the element, like a keydown\n   * event handler added to the window.\n   *\n   * ```ts\n   * connectedCallback() {\n   *   super.connectedCallback();\n   *   addEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * Typically, anything done in `connectedCallback()` should be undone when the\n   * element is disconnected, in `disconnectedCallback()`.\n   *\n   * @category lifecycle\n   */\n  override connectedCallback() {\n    super.connectedCallback();\n    this.__childPart?.setConnected(true);\n  }\n\n  /**\n   * Invoked when the component is removed from the document's DOM.\n   *\n   * This callback is the main signal to the element that it may no longer be\n   * used. `disconnectedCallback()` should ensure that nothing is holding a\n   * reference to the element (such as event listeners added to nodes external\n   * to the element), so that it is free to be garbage collected.\n   *\n   * ```ts\n   * disconnectedCallback() {\n   *   super.disconnectedCallback();\n   *   window.removeEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * An element may be re-connected after being disconnected.\n   *\n   * @category lifecycle\n   */\n  override disconnectedCallback() {\n    super.disconnectedCallback();\n    this.__childPart?.setConnected(false);\n  }\n\n  /**\n   * Invoked on each update to perform rendering tasks. This method may return\n   * any value renderable by lit-html's `ChildPart` - typically a\n   * `TemplateResult`. Setting properties inside this method will *not* trigger\n   * the element to update.\n   * @category rendering\n   */\n  protected render(): unknown {\n    return noChange;\n  }\n}\n\n// Install hydration if available\nglobalThis.litElementHydrateSupport?.({LitElement});\n\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n  ? globalThis.litElementPolyfillSupportDevMode\n  : globalThis.litElementPolyfillSupport;\npolyfillSupport?.({LitElement});\n\n// DEV mode warnings\nif (DEV_MODE) {\n  /* eslint-disable @typescript-eslint/no-explicit-any */\n  // Note, for compatibility with closure compilation, this access\n  // needs to be as a string property index.\n  (LitElement as any)['finalize'] = function (this: typeof LitElement) {\n    const finalized = (ReactiveElement as any).finalize.call(this);\n    if (!finalized) {\n      return false;\n    }\n    const warnRemovedOrRenamed = (obj: any, name: string, renamed = false) => {\n      if (obj.hasOwnProperty(name)) {\n        const ctorName = (typeof obj === 'function' ? obj : obj.constructor)\n          .name;\n        issueWarning(\n          renamed ? 'renamed-api' : 'removed-api',\n          `\\`${name}\\` is implemented on class ${ctorName}. It ` +\n            `has been ${renamed ? 'renamed' : 'removed'} ` +\n            `in this version of LitElement.`\n        );\n      }\n    };\n    warnRemovedOrRenamed(this, 'render');\n    warnRemovedOrRenamed(this, 'getStyles', true);\n    warnRemovedOrRenamed((this as typeof LitElement).prototype, 'adoptStyles');\n    return true;\n  };\n  /* eslint-enable @typescript-eslint/no-explicit-any */\n}\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LE object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-html, since this module re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LE = {\n  _$attributeToProperty: (\n    el: LitElement,\n    name: string,\n    value: string | null\n  ) => {\n    // eslint-disable-next-line\n    (el as any)._$attributeToProperty(name, value);\n  },\n  // eslint-disable-next-line\n  _$changedProperties: (el: LitElement) => (el as any)._$changedProperties,\n};\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for LitElement usage.\n(globalThis.litElementVersions ??= []).push('3.3.3');\nif (DEV_MODE && globalThis.litElementVersions.length > 1) {\n  issueWarning!(\n    'multiple-versions',\n    `Multiple versions of Lit loaded. Loading multiple versions ` +\n      `is not recommended.`\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport {Constructor, ClassDescriptor} from './base.js';\n\n/**\n * Allow for custom element classes with private constructors\n */\ntype CustomElementClass = Omit<typeof HTMLElement, 'new'>;\n\nconst legacyCustomElement = (tagName: string, clazz: CustomElementClass) => {\n  customElements.define(tagName, clazz as CustomElementConstructor);\n  // Cast as any because TS doesn't recognize the return type as being a\n  // subtype of the decorated class when clazz is typed as\n  // `Constructor<HTMLElement>` for some reason.\n  // `Constructor<HTMLElement>` is helpful to make sure the decorator is\n  // applied to elements however.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return clazz as any;\n};\n\nconst standardCustomElement = (\n  tagName: string,\n  descriptor: ClassDescriptor\n) => {\n  const {kind, elements} = descriptor;\n  return {\n    kind,\n    elements,\n    // This callback is called once the class is otherwise fully defined\n    finisher(clazz: Constructor<HTMLElement>) {\n      customElements.define(tagName, clazz);\n    },\n  };\n};\n\n/**\n * Class decorator factory that defines the decorated class as a custom element.\n *\n * ```js\n * @customElement('my-element')\n * class MyElement extends LitElement {\n *   render() {\n *     return html``;\n *   }\n * }\n * ```\n * @category Decorator\n * @param tagName The tag name of the custom element to define.\n */\nexport const customElement =\n  (tagName: string) =>\n  (classOrDescriptor: CustomElementClass | ClassDescriptor) =>\n    typeof classOrDescriptor === 'function'\n      ? legacyCustomElement(tagName, classOrDescriptor)\n      : standardCustomElement(tagName, classOrDescriptor as ClassDescriptor);\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport {PropertyDeclaration, ReactiveElement} from '../reactive-element.js';\nimport {ClassElement} from './base.js';\n\nconst standardProperty = (\n  options: PropertyDeclaration,\n  element: ClassElement\n) => {\n  // When decorating an accessor, pass it through and add property metadata.\n  // Note, the `hasOwnProperty` check in `createProperty` ensures we don't\n  // stomp over the user's accessor.\n  if (\n    element.kind === 'method' &&\n    element.descriptor &&\n    !('value' in element.descriptor)\n  ) {\n    return {\n      ...element,\n      finisher(clazz: typeof ReactiveElement) {\n        clazz.createProperty(element.key, options);\n      },\n    };\n  } else {\n    // createProperty() takes care of defining the property, but we still\n    // must return some kind of descriptor, so return a descriptor for an\n    // unused prototype field. The finisher calls createProperty().\n    return {\n      kind: 'field',\n      key: Symbol(),\n      placement: 'own',\n      descriptor: {},\n      // store the original key so subsequent decorators have access to it.\n      originalKey: element.key,\n      // When @babel/plugin-proposal-decorators implements initializers,\n      // do this instead of the initializer below. See:\n      // https://github.com/babel/babel/issues/9260 extras: [\n      //   {\n      //     kind: 'initializer',\n      //     placement: 'own',\n      //     initializer: descriptor.initializer,\n      //   }\n      // ],\n      initializer(this: {[key: string]: unknown}) {\n        if (typeof element.initializer === 'function') {\n          this[element.key as string] = element.initializer.call(this);\n        }\n      },\n      finisher(clazz: typeof ReactiveElement) {\n        clazz.createProperty(element.key, options);\n      },\n    };\n  }\n};\n\nconst legacyProperty = (\n  options: PropertyDeclaration,\n  proto: Object,\n  name: PropertyKey\n) => {\n  (proto.constructor as typeof ReactiveElement).createProperty(name, options);\n};\n\n/**\n * A property decorator which creates a reactive property that reflects a\n * corresponding attribute value. When a decorated property is set\n * the element will update and render. A {@linkcode PropertyDeclaration} may\n * optionally be supplied to configure property features.\n *\n * This decorator should only be used for public fields. As public fields,\n * properties should be considered as primarily settable by element users,\n * either via attribute or the property itself.\n *\n * Generally, properties that are changed by the element should be private or\n * protected fields and should use the {@linkcode state} decorator.\n *\n * However, sometimes element code does need to set a public property. This\n * should typically only be done in response to user interaction, and an event\n * should be fired informing the user; for example, a checkbox sets its\n * `checked` property when clicked and fires a `changed` event. Mutating public\n * properties should typically not be done for non-primitive (object or array)\n * properties. In other cases when an element needs to manage state, a private\n * property decorated via the {@linkcode state} decorator should be used. When\n * needed, state properties can be initialized via public properties to\n * facilitate complex interactions.\n *\n * ```ts\n * class MyElement {\n *   @property({ type: Boolean })\n *   clicked = false;\n * }\n * ```\n * @category Decorator\n * @ExportDecoratedItems\n */\nexport function property(options?: PropertyDeclaration) {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (protoOrDescriptor: Object | ClassElement, name?: PropertyKey): any =>\n    name !== undefined\n      ? legacyProperty(options!, protoOrDescriptor as Object, name)\n      : standardProperty(options!, protoOrDescriptor as ClassElement);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {property} from './property.js';\n\nexport interface InternalPropertyDeclaration<Type = unknown> {\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n}\n\n/**\n * Declares a private or protected reactive property that still triggers\n * updates to the element when it changes. It does not reflect from the\n * corresponding attribute.\n *\n * Properties declared this way must not be used from HTML or HTML templating\n * systems, they're solely for properties internal to the element. These\n * properties may be renamed by optimization tools like closure compiler.\n * @category Decorator\n */\nexport function state(options?: InternalPropertyDeclaration) {\n  return property({\n    ...options,\n    state: true,\n  });\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {decorateProperty} from './base.js';\n\nimport type {ReactiveElement} from '../reactive-element.js';\nimport type {QueryAssignedNodesOptions} from './query-assigned-nodes.js';\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n\n/**\n * A tiny module scoped polyfill for HTMLSlotElement.assignedElements.\n */\nconst slotAssignedElements =\n  global.HTMLSlotElement?.prototype.assignedElements != null\n    ? (slot: HTMLSlotElement, opts?: AssignedNodesOptions) =>\n        slot.assignedElements(opts)\n    : (slot: HTMLSlotElement, opts?: AssignedNodesOptions) =>\n        slot\n          .assignedNodes(opts)\n          .filter(\n            (node): node is Element => node.nodeType === Node.ELEMENT_NODE\n          );\n\n/**\n * Options for the {@linkcode queryAssignedElements} decorator. Extends the\n * options that can be passed into\n * [HTMLSlotElement.assignedElements](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n */\nexport interface QueryAssignedElementsOptions\n  extends QueryAssignedNodesOptions {\n  /**\n   * CSS selector used to filter the elements returned. For example, a selector\n   * of `\".item\"` will only include elements with the `item` class.\n   */\n  selector?: string;\n}\n\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedElements` of the given `slot`. Provides a declarative\n * way to use\n * [`HTMLSlotElement.assignedElements`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedElements).\n *\n * Can be passed an optional {@linkcode QueryAssignedElementsOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedElements({ slot: 'list' })\n *   listItems!: Array<HTMLElement>;\n *   @queryAssignedElements()\n *   unnamedSlotEls!: Array<HTMLElement>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *       <slot></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note, the type of this property should be annotated as `Array<HTMLElement>`.\n *\n * @category Decorator\n */\nexport function queryAssignedElements(options?: QueryAssignedElementsOptions) {\n  const {slot, selector} = options ?? {};\n  return decorateProperty({\n    descriptor: (_name: PropertyKey) => ({\n      get(this: ReactiveElement) {\n        const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n        const slotEl =\n          this.renderRoot?.querySelector<HTMLSlotElement>(slotSelector);\n        const elements =\n          slotEl != null ? slotAssignedElements(slotEl, options) : [];\n        if (selector) {\n          return elements.filter((node) => node.matches(selector));\n        }\n        return elements;\n      },\n      enumerable: true,\n      configurable: true,\n    }),\n  });\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {Disconnectable, Part} from './lit-html.js';\n\nexport {\n  AttributePart,\n  BooleanAttributePart,\n  ChildPart,\n  ElementPart,\n  EventPart,\n  Part,\n  PropertyPart,\n} from './lit-html.js';\n\nexport interface DirectiveClass {\n  new (part: PartInfo): Directive;\n}\n\n/**\n * This utility type extracts the signature of a directive class's render()\n * method so we can use it for the type of the generated directive function.\n */\nexport type DirectiveParameters<C extends Directive> = Parameters<C['render']>;\n\n/**\n * A generated directive function doesn't evaluate the directive, but just\n * returns a DirectiveResult object that captures the arguments.\n */\nexport interface DirectiveResult<C extends DirectiveClass = DirectiveClass> {\n  /**\n   * This property needs to remain unminified.\n   * @internal */\n  ['_$litDirective$']: C;\n  /** @internal */\n  values: DirectiveParameters<InstanceType<C>>;\n}\n\nexport const PartType = {\n  ATTRIBUTE: 1,\n  CHILD: 2,\n  PROPERTY: 3,\n  BOOLEAN_ATTRIBUTE: 4,\n  EVENT: 5,\n  ELEMENT: 6,\n} as const;\n\nexport type PartType = (typeof PartType)[keyof typeof PartType];\n\nexport interface ChildPartInfo {\n  readonly type: typeof PartType.CHILD;\n}\n\nexport interface AttributePartInfo {\n  readonly type:\n    | typeof PartType.ATTRIBUTE\n    | typeof PartType.PROPERTY\n    | typeof PartType.BOOLEAN_ATTRIBUTE\n    | typeof PartType.EVENT;\n  readonly strings?: ReadonlyArray<string>;\n  readonly name: string;\n  readonly tagName: string;\n}\n\nexport interface ElementPartInfo {\n  readonly type: typeof PartType.ELEMENT;\n}\n\n/**\n * Information about the part a directive is bound to.\n *\n * This is useful for checking that a directive is attached to a valid part,\n * such as with directive that can only be used on attribute bindings.\n */\nexport type PartInfo = ChildPartInfo | AttributePartInfo | ElementPartInfo;\n\n/**\n * Creates a user-facing directive function from a Directive class. This\n * function has the same parameters as the directive's render() method.\n */\nexport const directive =\n  <C extends DirectiveClass>(c: C) =>\n  (...values: DirectiveParameters<InstanceType<C>>): DirectiveResult<C> => ({\n    // This property needs to remain unminified.\n    ['_$litDirective$']: c,\n    values,\n  });\n\n/**\n * Base class for creating custom directives. Users should extend this class,\n * implement `render` and/or `update`, and then pass their subclass to\n * `directive`.\n */\nexport abstract class Directive implements Disconnectable {\n  //@internal\n  __part!: Part;\n  //@internal\n  __attributeIndex: number | undefined;\n  //@internal\n  __directive?: Directive;\n\n  //@internal\n  _$parent!: Disconnectable;\n\n  // These will only exist on the AsyncDirective subclass\n  //@internal\n  _$disconnectableChildren?: Set<Disconnectable>;\n  // This property needs to remain unminified.\n  //@internal\n  ['_$notifyDirectiveConnectionChanged']?(isConnected: boolean): void;\n\n  constructor(_partInfo: PartInfo) {}\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  /** @internal */\n  _$initialize(\n    part: Part,\n    parent: Disconnectable,\n    attributeIndex: number | undefined\n  ) {\n    this.__part = part;\n    this._$parent = parent;\n    this.__attributeIndex = attributeIndex;\n  }\n  /** @internal */\n  _$resolve(part: Part, props: Array<unknown>): unknown {\n    return this.update(part, props);\n  }\n\n  abstract render(...props: Array<unknown>): unknown;\n\n  update(_part: Part, props: Array<unknown>): unknown {\n    return this.render(...props);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of class names to truthy values.\n */\nexport interface ClassInfo {\n  readonly [name: string]: string | boolean | number;\n}\n\nclass ClassMapDirective extends Directive {\n  /**\n   * Stores the ClassInfo object applied to a given AttributePart.\n   * Used to unset existing values when a new ClassInfo object is applied.\n   */\n  private _previousClasses?: Set<string>;\n  private _staticClasses?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'class' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        '`classMap()` can only be used in the `class` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(classInfo: ClassInfo) {\n    // Add spaces to ensure separation from static classes\n    return (\n      ' ' +\n      Object.keys(classInfo)\n        .filter((key) => classInfo[key])\n        .join(' ') +\n      ' '\n    );\n  }\n\n  override update(part: AttributePart, [classInfo]: DirectiveParameters<this>) {\n    // Remember dynamic classes on the first render\n    if (this._previousClasses === undefined) {\n      this._previousClasses = new Set();\n      if (part.strings !== undefined) {\n        this._staticClasses = new Set(\n          part.strings\n            .join(' ')\n            .split(/\\s/)\n            .filter((s) => s !== '')\n        );\n      }\n      for (const name in classInfo) {\n        if (classInfo[name] && !this._staticClasses?.has(name)) {\n          this._previousClasses.add(name);\n        }\n      }\n      return this.render(classInfo);\n    }\n\n    const classList = part.element.classList;\n\n    // Remove old classes that no longer apply\n    // We use forEach() instead of for-of so that we don't require down-level\n    // iteration.\n    this._previousClasses.forEach((name) => {\n      if (!(name in classInfo)) {\n        classList.remove(name);\n        this._previousClasses!.delete(name);\n      }\n    });\n\n    // Add or remove classes based on their classMap value\n    for (const name in classInfo) {\n      // We explicitly want a loose truthy check of `value` because it seems\n      // more convenient that '' and 0 are skipped.\n      const value = !!classInfo[name];\n      if (\n        value !== this._previousClasses.has(name) &&\n        !this._staticClasses?.has(name)\n      ) {\n        if (value) {\n          classList.add(name);\n          this._previousClasses.add(name);\n        } else {\n          classList.remove(name);\n          this._previousClasses.delete(name);\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies dynamic CSS classes.\n *\n * This must be used in the `class` attribute and must be the only part used in\n * the attribute. It takes each property in the `classInfo` argument and adds\n * the property name to the element's `classList` if the property value is\n * truthy; if the property value is falsey, the property name is removed from\n * the element's `class`.\n *\n * For example `{foo: bar}` applies the class `foo` if the value of `bar` is\n * truthy.\n *\n * @param classInfo\n */\nexport const classMap = directive(ClassMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {ClassMapDirective};\n", "function addUniqueItem(array, item) {\n    array.indexOf(item) === -1 && array.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    index > -1 && arr.splice(index, 1);\n}\n\nexport { addUniqueItem, removeItem };\n", "const clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\nexport { clamp };\n", "const defaults = {\n    duration: 0.3,\n    delay: 0,\n    endDelay: 0,\n    repeat: 0,\n    easing: \"ease\",\n};\n\nexport { defaults };\n", "const isNumber = (value) => typeof value === \"number\";\n\nexport { isNumber };\n", "import { isNumber } from './is-number.es.js';\n\nconst isEasingList = (easing) => Array.isArray(easing) && !isNumber(easing[0]);\n\nexport { isEasingList };\n", "const wrap = (min, max, v) => {\n    const rangeSize = max - min;\n    return ((((v - min) % rangeSize) + rangeSize) % rangeSize) + min;\n};\n\nexport { wrap };\n", "import { isEasingList } from './is-easing-list.es.js';\nimport { wrap } from './wrap.es.js';\n\nfunction getEasingForSegment(easing, i) {\n    return isEasingList(easing) ? easing[wrap(0, easing.length, i)] : easing;\n}\n\nexport { getEasingForSegment };\n", "const mix = (min, max, progress) => -progress * min + progress * max + min;\n\nexport { mix };\n", "const noop = () => { };\nconst noopReturn = (v) => v;\n\nexport { noop, noopReturn };\n", "const progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\n\nexport { progress };\n", "import { mix } from './mix.es.js';\nimport { progress } from './progress.es.js';\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = progress(0, remaining, i);\n        offset.push(mix(min, 1, offsetProgress));\n    }\n}\nfunction defaultOffset(length) {\n    const offset = [0];\n    fillOffset(offset, length - 1);\n    return offset;\n}\n\nexport { defaultOffset, fillOffset };\n", "import { mix } from './mix.es.js';\nimport { noopReturn } from './noop.es.js';\nimport { fillOffset, defaultOffset } from './offset.es.js';\nimport { progress } from './progress.es.js';\nimport { getEasingForSegment } from './easing.es.js';\nimport { clamp } from './clamp.es.js';\n\nfunction interpolate(output, input = defaultOffset(output.length), easing = noopReturn) {\n    const length = output.length;\n    /**\n     * If the input length is lower than the output we\n     * fill the input to match. This currently assumes the input\n     * is an animation progress value so is a good candidate for\n     * moving outside the function.\n     */\n    const remainder = length - input.length;\n    remainder > 0 && fillOffset(input, remainder);\n    return (t) => {\n        let i = 0;\n        for (; i < length - 2; i++) {\n            if (t < input[i + 1])\n                break;\n        }\n        let progressInRange = clamp(0, 1, progress(input[i], input[i + 1], t));\n        const segmentEasing = getEasingForSegment(easing, i);\n        progressInRange = segmentEasing(progressInRange);\n        return mix(output[i], output[i + 1], progressInRange);\n    };\n}\n\nexport { interpolate };\n", "import { isNumber } from './is-number.es.js';\n\nconst isCubicBezier = (easing) => Array.isArray(easing) && isNumber(easing[0]);\n\nexport { isCubicBezier };\n", "const isEasingGenerator = (easing) => typeof easing === \"object\" &&\n    Boolean(easing.createAnimation);\n\nexport { isEasingGenerator };\n", "const isFunction = (value) => typeof value === \"function\";\n\nexport { isFunction };\n", "const isString = (value) => typeof value === \"string\";\n\nexport { isString };\n", "const time = {\n    ms: (seconds) => seconds * 1000,\n    s: (milliseconds) => milliseconds / 1000,\n};\n\nexport { time };\n", "/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n", "import { noopReturn } from '@motionone/utils';\n\n/*\n  Bezier function generator\n\n  This has been modified from G<PERSON><PERSON><PERSON>'s BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticiably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) * t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return noopReturn;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n", "import { clamp } from '@motionone/utils';\n\nconst steps = (steps, direction = \"end\") => (progress) => {\n    progress =\n        direction === \"end\"\n            ? Math.min(progress, 0.999)\n            : Math.max(progress, 0.001);\n    const expanded = progress * steps;\n    const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n    return clamp(0, 1, rounded / steps);\n};\n\nexport { steps };\n", "import { cubicBezier, steps } from '@motionone/easing';\nimport { isFunction, isCubicBezier, noopReturn } from '@motionone/utils';\n\nconst namedEasings = {\n    ease: cubicBezier(0.25, 0.1, 0.25, 1.0),\n    \"ease-in\": cubicBezier(0.42, 0.0, 1.0, 1.0),\n    \"ease-in-out\": cubicBezier(0.42, 0.0, 0.58, 1.0),\n    \"ease-out\": cubicBezier(0.0, 0.0, 0.58, 1.0),\n};\nconst functionArgsRegex = /\\((.*?)\\)/;\nfunction getEasingFunction(definition) {\n    // If already an easing function, return\n    if (isFunction(definition))\n        return definition;\n    // If an easing curve definition, return bezier function\n    if (isCubicBezier(definition))\n        return cubicBezier(...definition);\n    // If we have a predefined easing function, return\n    const namedEasing = namedEasings[definition];\n    if (namedEasing)\n        return namedEasing;\n    // If this is a steps function, attempt to create easing curve\n    if (definition.startsWith(\"steps\")) {\n        const args = functionArgsRegex.exec(definition);\n        if (args) {\n            const argsArray = args[1].split(\",\");\n            return steps(parseFloat(argsArray[0]), argsArray[1].trim());\n        }\n    }\n    return noopReturn;\n}\n\nexport { getEasingFunction };\n", "import { noopReturn, defaults, isEasingGenerator, isEasingList, interpolate } from '@motionone/utils';\nimport { getEasingFunction } from './utils/easing.es.js';\n\nclass Animation {\n    constructor(output, keyframes = [0, 1], { easing, duration: initialDuration = defaults.duration, delay = defaults.delay, endDelay = defaults.endDelay, repeat = defaults.repeat, offset, direction = \"normal\", autoplay = true, } = {}) {\n        this.startTime = null;\n        this.rate = 1;\n        this.t = 0;\n        this.cancelTimestamp = null;\n        this.easing = noopReturn;\n        this.duration = 0;\n        this.totalDuration = 0;\n        this.repeat = 0;\n        this.playState = \"idle\";\n        this.finished = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n        easing = easing || defaults.easing;\n        if (isEasingGenerator(easing)) {\n            const custom = easing.createAnimation(keyframes);\n            easing = custom.easing;\n            keyframes = custom.keyframes || keyframes;\n            initialDuration = custom.duration || initialDuration;\n        }\n        this.repeat = repeat;\n        this.easing = isEasingList(easing) ? noopReturn : getEasingFunction(easing);\n        this.updateDuration(initialDuration);\n        const interpolate$1 = interpolate(keyframes, offset, isEasingList(easing) ? easing.map(getEasingFunction) : noopReturn);\n        this.tick = (timestamp) => {\n            var _a;\n            // TODO: Temporary fix for OptionsResolver typing\n            delay = delay;\n            let t = 0;\n            if (this.pauseTime !== undefined) {\n                t = this.pauseTime;\n            }\n            else {\n                t = (timestamp - this.startTime) * this.rate;\n            }\n            this.t = t;\n            // Convert to seconds\n            t /= 1000;\n            // Rebase on delay\n            t = Math.max(t - delay, 0);\n            /**\n             * If this animation has finished, set the current time\n             * to the total duration.\n             */\n            if (this.playState === \"finished\" && this.pauseTime === undefined) {\n                t = this.totalDuration;\n            }\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = t / this.duration;\n            // TODO progress += iterationStart\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            iterationProgress === 1 && currentIteration--;\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const iterationIsOdd = currentIteration % 2;\n            if (direction === \"reverse\" ||\n                (direction === \"alternate\" && iterationIsOdd) ||\n                (direction === \"alternate-reverse\" && !iterationIsOdd)) {\n                iterationProgress = 1 - iterationProgress;\n            }\n            const p = t >= this.totalDuration ? 1 : Math.min(iterationProgress, 1);\n            const latest = interpolate$1(this.easing(p));\n            output(latest);\n            const isAnimationFinished = this.pauseTime === undefined &&\n                (this.playState === \"finished\" || t >= this.totalDuration + endDelay);\n            if (isAnimationFinished) {\n                this.playState = \"finished\";\n                (_a = this.resolve) === null || _a === void 0 ? void 0 : _a.call(this, latest);\n            }\n            else if (this.playState !== \"idle\") {\n                this.frameRequestId = requestAnimationFrame(this.tick);\n            }\n        };\n        if (autoplay)\n            this.play();\n    }\n    play() {\n        const now = performance.now();\n        this.playState = \"running\";\n        if (this.pauseTime !== undefined) {\n            this.startTime = now - this.pauseTime;\n        }\n        else if (!this.startTime) {\n            this.startTime = now;\n        }\n        this.cancelTimestamp = this.startTime;\n        this.pauseTime = undefined;\n        this.frameRequestId = requestAnimationFrame(this.tick);\n    }\n    pause() {\n        this.playState = \"paused\";\n        this.pauseTime = this.t;\n    }\n    finish() {\n        this.playState = \"finished\";\n        this.tick(0);\n    }\n    stop() {\n        var _a;\n        this.playState = \"idle\";\n        if (this.frameRequestId !== undefined) {\n            cancelAnimationFrame(this.frameRequestId);\n        }\n        (_a = this.reject) === null || _a === void 0 ? void 0 : _a.call(this, false);\n    }\n    cancel() {\n        this.stop();\n        this.tick(this.cancelTimestamp);\n    }\n    reverse() {\n        this.rate *= -1;\n    }\n    commitStyles() { }\n    updateDuration(duration) {\n        this.duration = duration;\n        this.totalDuration = duration * (this.repeat + 1);\n    }\n    get currentTime() {\n        return this.t;\n    }\n    set currentTime(t) {\n        if (this.pauseTime !== undefined || this.rate === 0) {\n            this.pauseTime = t;\n        }\n        else {\n            this.startTime = performance.now() - t / this.rate;\n        }\n    }\n    get playbackRate() {\n        return this.rate;\n    }\n    set playbackRate(rate) {\n        this.rate = rate;\n    }\n}\n\nexport { Animation };\n", "var warning = function () { };\r\nvar invariant = function () { };\r\nif (process.env.NODE_ENV !== 'production') {\r\n    warning = function (check, message) {\r\n        if (!check && typeof console !== 'undefined') {\r\n            console.warn(message);\r\n        }\r\n    };\r\n    invariant = function (check, message) {\r\n        if (!check) {\r\n            throw new Error(message);\r\n        }\r\n    };\r\n}\n\nexport { invariant, warning };\n", "/**\n * The MotionValue tracks the state of a single animatable\n * value. Currently, updatedAt and current are unused. The\n * long term idea is to use this to minimise the number\n * of DOM reads, and to abstract the DOM interactions here.\n */\nclass MotionValue {\n    setAnimation(animation) {\n        this.animation = animation;\n        animation === null || animation === void 0 ? void 0 : animation.finished.then(() => this.clearAnimation()).catch(() => { });\n    }\n    clearAnimation() {\n        this.animation = this.generator = undefined;\n    }\n}\n\nexport { MotionValue };\n", "import { MotionValue } from '@motionone/types';\n\nconst data = new WeakMap();\nfunction getAnimationData(element) {\n    if (!data.has(element)) {\n        data.set(element, {\n            transforms: [],\n            values: new Map(),\n        });\n    }\n    return data.get(element);\n}\nfunction getMotionValue(motionValues, name) {\n    if (!motionValues.has(name)) {\n        motionValues.set(name, new MotionValue());\n    }\n    return motionValues.get(name);\n}\n\nexport { getAnimationData, getMotionValue };\n", "import { noopReturn, addUniqueItem } from '@motionone/utils';\nimport { getAnimationData } from '../data.es.js';\n\n/**\n * A list of all transformable axes. We'll use this list to generated a version\n * of each axes for each transform.\n */\nconst axes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * An ordered array of each transformable value. By default, transform values\n * will be sorted to this order.\n */\nconst order = [\"translate\", \"scale\", \"rotate\", \"skew\"];\nconst transformAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n};\nconst rotation = {\n    syntax: \"<angle>\",\n    initialValue: \"0deg\",\n    toDefaultUnit: (v) => v + \"deg\",\n};\nconst baseTransformProperties = {\n    translate: {\n        syntax: \"<length-percentage>\",\n        initialValue: \"0px\",\n        toDefaultUnit: (v) => v + \"px\",\n    },\n    rotate: rotation,\n    scale: {\n        syntax: \"<number>\",\n        initialValue: 1,\n        toDefaultUnit: noopReturn,\n    },\n    skew: rotation,\n};\nconst transformDefinitions = new Map();\nconst asTransformCssVar = (name) => `--motion-${name}`;\n/**\n * Generate a list of every possible transform key\n */\nconst transforms = [\"x\", \"y\", \"z\"];\norder.forEach((name) => {\n    axes.forEach((axis) => {\n        transforms.push(name + axis);\n        transformDefinitions.set(asTransformCssVar(name + axis), baseTransformProperties[name]);\n    });\n});\n/**\n * A function to use with Array.sort to sort transform keys by their default order.\n */\nconst compareTransformOrder = (a, b) => transforms.indexOf(a) - transforms.indexOf(b);\n/**\n * Provide a quick way to check if a string is the name of a transform\n */\nconst transformLookup = new Set(transforms);\nconst isTransform = (name) => transformLookup.has(name);\nconst addTransformToElement = (element, name) => {\n    // Map x to translateX etc\n    if (transformAlias[name])\n        name = transformAlias[name];\n    const { transforms } = getAnimationData(element);\n    addUniqueItem(transforms, name);\n    /**\n     * TODO: An optimisation here could be to cache the transform in element data\n     * and only update if this has changed.\n     */\n    element.style.transform = buildTransformTemplate(transforms);\n};\nconst buildTransformTemplate = (transforms) => transforms\n    .sort(compareTransformOrder)\n    .reduce(transformListToString, \"\")\n    .trim();\nconst transformListToString = (template, name) => `${template} ${name}(var(${asTransformCssVar(name)}))`;\n\nexport { addTransformToElement, asTransformCssVar, axes, buildTransformTemplate, compareTransformOrder, isTransform, transformAlias, transformDefinitions };\n", "import { transformDefinitions } from './transforms.es.js';\n\nconst isCssVar = (name) => name.startsWith(\"--\");\nconst registeredProperties = new Set();\nfunction registerCssVariable(name) {\n    if (registeredProperties.has(name))\n        return;\n    registeredProperties.add(name);\n    try {\n        const { syntax, initialValue } = transformDefinitions.has(name)\n            ? transformDefinitions.get(name)\n            : {};\n        CSS.registerProperty({\n            name,\n            inherits: false,\n            syntax,\n            initialValue,\n        });\n    }\n    catch (e) { }\n}\n\nexport { isCssVar, registerCssVariable, registeredProperties };\n", "const testAnimation = (keyframes, options) => document.createElement(\"div\").animate(keyframes, options);\nconst featureTests = {\n    cssRegisterProperty: () => typeof CSS !== \"undefined\" &&\n        Object.hasOwnProperty.call(CSS, \"registerProperty\"),\n    waapi: () => Object.hasOwnProperty.call(Element.prototype, \"animate\"),\n    partialKeyframes: () => {\n        try {\n            testAnimation({ opacity: [1] });\n        }\n        catch (e) {\n            return false;\n        }\n        return true;\n    },\n    finished: () => Boolean(testAnimation({ opacity: [0, 1] }, { duration: 0.001 }).finished),\n    linearEasing: () => {\n        try {\n            testAnimation({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n        }\n        catch (e) {\n            return false;\n        }\n        return true;\n    },\n};\nconst results = {};\nconst supports = {};\nfor (const key in featureTests) {\n    supports[key] = () => {\n        if (results[key] === undefined)\n            results[key] =\n                featureTests[key]();\n        return results[key];\n    };\n}\n\nexport { supports };\n", "import { isFunction, defaults, isCubicBezier, progress } from '@motionone/utils';\nimport { supports } from './feature-detection.es.js';\n\n// Create a linear easing point for every x second\nconst resolution = 0.015;\nconst generateLinearEasingPoints = (easing, duration) => {\n    let points = \"\";\n    const numPoints = Math.round(duration / resolution);\n    for (let i = 0; i < numPoints; i++) {\n        points += easing(progress(0, numPoints - 1, i)) + \", \";\n    }\n    return points.substring(0, points.length - 2);\n};\nconst convertEasing = (easing, duration) => {\n    if (isFunction(easing)) {\n        return supports.linearEasing()\n            ? `linear(${generateLinearEasingPoints(easing, duration)})`\n            : defaults.easing;\n    }\n    else {\n        return isCubicBezier(easing) ? cubicBezierAsString(easing) : easing;\n    }\n};\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\nexport { convertEasing, cubicBezierAsString, generateLinearEasingPoints };\n", "function hydrateKeyframes(keyframes, readInitialValue) {\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] === null) {\n            keyframes[i] = i ? keyframes[i - 1] : readInitialValue();\n        }\n    }\n    return keyframes;\n}\nconst keyframesList = (keyframes) => Array.isArray(keyframes) ? keyframes : [keyframes];\n\nexport { hydrateKeyframes, keyframesList };\n", "import { isTransform, asTransformCssVar, transformAlias } from './transforms.es.js';\n\nfunction getStyleName(key) {\n    if (transformAlias[key])\n        key = transformAlias[key];\n    return isTransform(key) ? asTransformCssVar(key) : key;\n}\n\nexport { getStyleName };\n", "import { isCssVar } from './utils/css-var.es.js';\nimport { getStyleName } from './utils/get-style-name.es.js';\nimport { transformDefinitions } from './utils/transforms.es.js';\n\nconst style = {\n    get: (element, name) => {\n        name = getStyleName(name);\n        let value = isCssVar(name)\n            ? element.style.getPropertyValue(name)\n            : getComputedStyle(element)[name];\n        // TODO Decide if value can be 0\n        if (!value && value !== 0) {\n            const definition = transformDefinitions.get(name);\n            if (definition)\n                value = definition.initialValue;\n        }\n        return value;\n    },\n    set: (element, name, value) => {\n        name = getStyleName(name);\n        if (isCssVar(name)) {\n            element.style.setProperty(name, value);\n        }\n        else {\n            element.style[name] = value;\n        }\n    },\n};\n\nexport { style };\n", "function stopAnimation(animation, needsCommit = true) {\n    if (!animation || animation.playState === \"finished\")\n        return;\n    // Suppress error thrown by WAAPI\n    try {\n        if (animation.stop) {\n            animation.stop();\n        }\n        else {\n            needsCommit && animation.commitStyles();\n            animation.cancel();\n        }\n    }\n    catch (e) { }\n}\n\nexport { stopAnimation };\n", "import { noopReturn, isString } from '@motionone/utils';\n\nfunction getUnitConverter(keyframes, definition) {\n    var _a;\n    let toUnit = (definition === null || definition === void 0 ? void 0 : definition.toDefaultUnit) || noopReturn;\n    const finalKeyframe = keyframes[keyframes.length - 1];\n    if (isString(finalKeyframe)) {\n        const unit = ((_a = finalKeyframe.match(/(-?[\\d.]+)([a-z%]*)/)) === null || _a === void 0 ? void 0 : _a[2]) || \"\";\n        if (unit)\n            toUnit = (value) => value + unit;\n    }\n    return toUnit;\n}\n\nexport { getUnitConverter };\n", "import { getAnimationData, getMotionValue } from './data.es.js';\nimport { isCssVar, registerCssVariable } from './utils/css-var.es.js';\nimport { defaults, isEasingGenerator, isFunction, isEasingList, isNumber, time, noop } from '@motionone/utils';\nimport { isTransform, addTransformToElement, transformDefinitions } from './utils/transforms.es.js';\nimport { convertEasing } from './utils/easing.es.js';\nimport { supports } from './utils/feature-detection.es.js';\nimport { hydrateKeyframes, keyframesList } from './utils/keyframes.es.js';\nimport { style } from './style.es.js';\nimport { getStyleName } from './utils/get-style-name.es.js';\nimport { stopAnimation } from './utils/stop-animation.es.js';\nimport { getUnitConverter } from './utils/get-unit.es.js';\n\nfunction getDevToolsRecord() {\n    return window.__MOTION_DEV_TOOLS_RECORD;\n}\nfunction animateStyle(element, key, keyframesDefinition, options = {}, AnimationPolyfill) {\n    const record = getDevToolsRecord();\n    const isRecording = options.record !== false && record;\n    let animation;\n    let { duration = defaults.duration, delay = defaults.delay, endDelay = defaults.endDelay, repeat = defaults.repeat, easing = defaults.easing, persist = false, direction, offset, allowWebkitAcceleration = false, autoplay = true, } = options;\n    const data = getAnimationData(element);\n    const valueIsTransform = isTransform(key);\n    let canAnimateNatively = supports.waapi();\n    /**\n     * If this is an individual transform, we need to map its\n     * key to a CSS variable and update the element's transform style\n     */\n    valueIsTransform && addTransformToElement(element, key);\n    const name = getStyleName(key);\n    const motionValue = getMotionValue(data.values, name);\n    /**\n     * Get definition of value, this will be used to convert numerical\n     * keyframes into the default value type.\n     */\n    const definition = transformDefinitions.get(name);\n    /**\n     * Stop the current animation, if any. Because this will trigger\n     * commitStyles (DOM writes) and we might later trigger DOM reads,\n     * this is fired now and we return a factory function to create\n     * the actual animation that can get called in batch,\n     */\n    stopAnimation(motionValue.animation, !(isEasingGenerator(easing) && motionValue.generator) &&\n        options.record !== false);\n    /**\n     * Batchable factory function containing all DOM reads.\n     */\n    return () => {\n        const readInitialValue = () => { var _a, _b; return (_b = (_a = style.get(element, name)) !== null && _a !== void 0 ? _a : definition === null || definition === void 0 ? void 0 : definition.initialValue) !== null && _b !== void 0 ? _b : 0; };\n        /**\n         * Replace null values with the previous keyframe value, or read\n         * it from the DOM if it's the first keyframe.\n         */\n        let keyframes = hydrateKeyframes(keyframesList(keyframesDefinition), readInitialValue);\n        /**\n         * Detect unit type of keyframes.\n         */\n        const toUnit = getUnitConverter(keyframes, definition);\n        if (isEasingGenerator(easing)) {\n            const custom = easing.createAnimation(keyframes, key !== \"opacity\", readInitialValue, name, motionValue);\n            easing = custom.easing;\n            keyframes = custom.keyframes || keyframes;\n            duration = custom.duration || duration;\n        }\n        /**\n         * If this is a CSS variable we need to register it with the browser\n         * before it can be animated natively. We also set it with setProperty\n         * rather than directly onto the element.style object.\n         */\n        if (isCssVar(name)) {\n            if (supports.cssRegisterProperty()) {\n                registerCssVariable(name);\n            }\n            else {\n                canAnimateNatively = false;\n            }\n        }\n        /**\n         * If we've been passed a custom easing function, and this browser\n         * does **not** support linear() easing, and the value is a transform\n         * (and thus a pure number) we can still support the custom easing\n         * by falling back to the animation polyfill.\n         */\n        if (valueIsTransform &&\n            !supports.linearEasing() &&\n            (isFunction(easing) || (isEasingList(easing) && easing.some(isFunction)))) {\n            canAnimateNatively = false;\n        }\n        /**\n         * If we can animate this value with WAAPI, do so.\n         */\n        if (canAnimateNatively) {\n            /**\n             * Convert numbers to default value types. Currently this only supports\n             * transforms but it could also support other value types.\n             */\n            if (definition) {\n                keyframes = keyframes.map((value) => isNumber(value) ? definition.toDefaultUnit(value) : value);\n            }\n            /**\n             * If this browser doesn't support partial/implicit keyframes we need to\n             * explicitly provide one.\n             */\n            if (keyframes.length === 1 &&\n                (!supports.partialKeyframes() || isRecording)) {\n                keyframes.unshift(readInitialValue());\n            }\n            const animationOptions = {\n                delay: time.ms(delay),\n                duration: time.ms(duration),\n                endDelay: time.ms(endDelay),\n                easing: !isEasingList(easing)\n                    ? convertEasing(easing, duration)\n                    : undefined,\n                direction,\n                iterations: repeat + 1,\n                fill: \"both\",\n            };\n            animation = element.animate({\n                [name]: keyframes,\n                offset,\n                easing: isEasingList(easing)\n                    ? easing.map((thisEasing) => convertEasing(thisEasing, duration))\n                    : undefined,\n            }, animationOptions);\n            /**\n             * Polyfill finished Promise in browsers that don't support it\n             */\n            if (!animation.finished) {\n                animation.finished = new Promise((resolve, reject) => {\n                    animation.onfinish = resolve;\n                    animation.oncancel = reject;\n                });\n            }\n            const target = keyframes[keyframes.length - 1];\n            animation.finished\n                .then(() => {\n                if (persist)\n                    return;\n                // Apply styles to target\n                style.set(element, name, target);\n                // Ensure fill modes don't persist\n                animation.cancel();\n            })\n                .catch(noop);\n            /**\n             * This forces Webkit to run animations on the main thread by exploiting\n             * this condition:\n             * https://trac.webkit.org/browser/webkit/trunk/Source/WebCore/platform/graphics/ca/GraphicsLayerCA.cpp?rev=281238#L1099\n             *\n             * This fixes Webkit's timing bugs, like accelerated animations falling\n             * out of sync with main thread animations and massive delays in starting\n             * accelerated animations in WKWebView.\n             */\n            if (!allowWebkitAcceleration)\n                animation.playbackRate = 1.000001;\n            /**\n             * If we can't animate the value natively then we can fallback to the numbers-only\n             * polyfill for transforms.\n             */\n        }\n        else if (AnimationPolyfill && valueIsTransform) {\n            /**\n             * If any keyframe is a string (because we measured it from the DOM), we need to convert\n             * it into a number before passing to the Animation polyfill.\n             */\n            keyframes = keyframes.map((value) => typeof value === \"string\" ? parseFloat(value) : value);\n            /**\n             * If we only have a single keyframe, we need to create an initial keyframe by reading\n             * the current value from the DOM.\n             */\n            if (keyframes.length === 1) {\n                keyframes.unshift(parseFloat(readInitialValue()));\n            }\n            animation = new AnimationPolyfill((latest) => {\n                style.set(element, name, toUnit ? toUnit(latest) : latest);\n            }, keyframes, Object.assign(Object.assign({}, options), { duration,\n                easing }));\n        }\n        else {\n            const target = keyframes[keyframes.length - 1];\n            style.set(element, name, definition && isNumber(target)\n                ? definition.toDefaultUnit(target)\n                : target);\n        }\n        if (isRecording) {\n            record(element, key, keyframes, {\n                duration,\n                delay: delay,\n                easing,\n                repeat,\n                offset,\n            }, \"motion-one\");\n        }\n        motionValue.setAnimation(animation);\n        if (animation && !autoplay)\n            animation.pause();\n        return animation;\n    };\n}\n\nexport { animateStyle };\n", "const getOptions = (options, key) => \n/**\n * TODO: Make test for this\n * Always return a new object otherwise delay is overwritten by results of stagger\n * and this results in no stagger\n */\noptions[key] ? Object.assign(Object.assign({}, options), options[key]) : Object.assign({}, options);\n\nexport { getOptions };\n", "function resolveElements(elements, selectorCache) {\n    var _a;\n    if (typeof elements === \"string\") {\n        if (selectorCache) {\n            (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : (selectorCache[elements] = document.querySelectorAll(elements));\n            elements = selectorCache[elements];\n        }\n        else {\n            elements = document.querySelectorAll(elements);\n        }\n    }\n    else if (elements instanceof Element) {\n        elements = [elements];\n    }\n    /**\n     * Return an empty array\n     */\n    return Array.from(elements || []);\n}\n\nexport { resolveElements };\n", "import { defaults, noop, time } from '@motionone/utils';\nimport { stopAnimation } from './stop-animation.es.js';\n\nconst createAnimation = (factory) => factory();\nconst withControls = (animationFactory, options, duration = defaults.duration) => {\n    return new Proxy({\n        animations: animationFactory.map(createAnimation).filter(Boolean),\n        duration,\n        options,\n    }, controls);\n};\n/**\n * TODO:\n * Currently this returns the first animation, ideally it would return\n * the first active animation.\n */\nconst getActiveAnimation = (state) => state.animations[0];\nconst controls = {\n    get: (target, key) => {\n        const activeAnimation = getActiveAnimation(target);\n        switch (key) {\n            case \"duration\":\n                return target.duration;\n            case \"currentTime\":\n                return time.s((activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) || 0);\n            case \"playbackRate\":\n            case \"playState\":\n                return activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key];\n            case \"finished\":\n                if (!target.finished) {\n                    target.finished = Promise.all(target.animations.map(selectFinished)).catch(noop);\n                }\n                return target.finished;\n            case \"stop\":\n                return () => {\n                    target.animations.forEach((animation) => stopAnimation(animation));\n                };\n            case \"forEachNative\":\n                /**\n                 * This is for internal use only, fire a callback for each\n                 * underlying animation.\n                 */\n                return (callback) => {\n                    target.animations.forEach((animation) => callback(animation, target));\n                };\n            default:\n                return typeof (activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) ===\n                    \"undefined\"\n                    ? undefined\n                    : () => target.animations.forEach((animation) => animation[key]());\n        }\n    },\n    set: (target, key, value) => {\n        switch (key) {\n            case \"currentTime\":\n                value = time.ms(value);\n            // Fall-through\n            case \"playbackRate\":\n                for (let i = 0; i < target.animations.length; i++) {\n                    target.animations[i][key] = value;\n                }\n                return true;\n        }\n        return false;\n    },\n};\nconst selectFinished = (animation) => animation.finished;\n\nexport { controls, withControls };\n", "import { isNumber, isFunction } from '@motionone/utils';\nimport { getEasingFunction } from '@motionone/animation';\n\nfunction stagger(duration = 0.1, { start = 0, from = 0, easing } = {}) {\n    return (i, total) => {\n        const fromIndex = isNumber(from) ? from : getFromIndex(from, total);\n        const distance = Math.abs(fromIndex - i);\n        let delay = duration * distance;\n        if (easing) {\n            const maxDelay = total * duration;\n            const easingFunction = getEasingFunction(easing);\n            delay = easingFunction(delay / maxDelay) * maxDelay;\n        }\n        return start + delay;\n    };\n}\nfunction getFromIndex(from, total) {\n    if (from === \"first\") {\n        return 0;\n    }\n    else {\n        const lastIndex = total - 1;\n        return from === \"last\" ? lastIndex : lastIndex / 2;\n    }\n}\nfunction resolveOption(option, i, total) {\n    return isFunction(option) ? option(i, total) : option;\n}\n\nexport { getFromIndex, resolveOption, stagger };\n", "import { invariant } from 'hey-listen';\nimport { animateStyle } from './animate-style.es.js';\nimport { getOptions } from './utils/options.es.js';\nimport { resolveElements } from '../utils/resolve-elements.es.js';\nimport { withControls } from './utils/controls.es.js';\nimport { resolveOption } from '../utils/stagger.es.js';\n\nfunction createAnimate(AnimatePolyfill) {\n    return function animate(elements, keyframes, options = {}) {\n        elements = resolveElements(elements);\n        const numElements = elements.length;\n        invariant(Boolean(numElements), \"No valid element provided.\");\n        invariant(Boolean(keyframes), \"No keyframes defined.\");\n        /**\n         * Create and start new animations\n         */\n        const animationFactories = [];\n        for (let i = 0; i < numElements; i++) {\n            const element = elements[i];\n            for (const key in keyframes) {\n                const valueOptions = getOptions(options, key);\n                valueOptions.delay = resolveOption(valueOptions.delay, i, numElements);\n                const animation = animateStyle(element, key, keyframes[key], valueOptions, AnimatePolyfill);\n                animationFactories.push(animation);\n            }\n        }\n        return withControls(animationFactories, options, \n        /**\n         * TODO:\n         * If easing is set to spring or glide, duration will be dynamically\n         * generated. Ideally we would dynamically generate this from\n         * animation.effect.getComputedTiming().duration but this isn't\n         * supported in iOS13 or our number polyfill. Perhaps it's possible\n         * to Proxy animations returned from animateStyle that has duration\n         * as a getter.\n         */\n        options.duration);\n    };\n}\n\nexport { createAnimate };\n", "import { Animation } from '@motionone/animation';\nimport { createAnimate } from './create-animate.es.js';\n\nconst animate = createAnimate(Animation);\n\nexport { animate };\n", "import { __rest } from 'tslib';\nimport { invariant } from 'hey-listen';\nimport { isString, defaults, isEasingGenerator, defaultOffset, fillOffset, progress } from '@motionone/utils';\nimport { resolveOption } from '../utils/stagger.es.js';\nimport { animateStyle } from '../animate/animate-style.es.js';\nimport { withControls } from '../animate/utils/controls.es.js';\nimport { keyframesList } from '../animate/utils/keyframes.es.js';\nimport { getOptions } from '../animate/utils/options.es.js';\nimport { resolveElements } from '../utils/resolve-elements.es.js';\nimport { calcNextTime } from './utils/calc-time.es.js';\nimport { addKeyframes } from './utils/edit.es.js';\nimport { compareByTime } from './utils/sort.es.js';\nimport { Animation } from '@motionone/animation';\n\nfunction timeline(definition, options = {}) {\n    var _a;\n    const animationDefinitions = createAnimationsFromTimeline(definition, options);\n    /**\n     * Create and start animations\n     */\n    const animationFactories = animationDefinitions\n        .map((definition) => animateStyle(...definition, Animation))\n        .filter(Boolean);\n    return withControls(animationFactories, options, \n    // Get the duration from the first animation definition\n    (_a = animationDefinitions[0]) === null || _a === void 0 ? void 0 : _a[3].duration);\n}\nfunction createAnimationsFromTimeline(definition, _a = {}) {\n    var { defaultOptions = {} } = _a, timelineOptions = __rest(_a, [\"defaultOptions\"]);\n    const animationDefinitions = [];\n    const elementSequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the definition array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < definition.length; i++) {\n        const segment = definition[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (isString(segment)) {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        const [elementDefinition, keyframes, options = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (options.at !== undefined) {\n            currentTime = calcNextTime(currentTime, options.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        /**\n         * Find all the elements specified in the definition and parse value\n         * keyframes from their timeline definitions.\n         */\n        const elements = resolveElements(elementDefinition, elementCache);\n        const numElements = elements.length;\n        for (let elementIndex = 0; elementIndex < numElements; elementIndex++) {\n            const element = elements[elementIndex];\n            const elementSequence = getElementSequence(element, elementSequences);\n            for (const key in keyframes) {\n                const valueSequence = getValueSequence(key, elementSequence);\n                let valueKeyframes = keyframesList(keyframes[key]);\n                const valueOptions = getOptions(options, key);\n                let { duration = defaultOptions.duration || defaults.duration, easing = defaultOptions.easing || defaults.easing, } = valueOptions;\n                if (isEasingGenerator(easing)) {\n                    invariant(key === \"opacity\" || valueKeyframes.length > 1, \"spring must be provided 2 keyframes within timeline()\");\n                    const custom = easing.createAnimation(valueKeyframes, key !== \"opacity\", () => 0, key);\n                    easing = custom.easing;\n                    valueKeyframes = custom.keyframes || valueKeyframes;\n                    duration = custom.duration || duration;\n                }\n                const delay = resolveOption(options.delay, elementIndex, numElements) || 0;\n                const startTime = currentTime + delay;\n                const targetTime = startTime + duration;\n                /**\n                 *\n                 */\n                let { offset = defaultOffset(valueKeyframes.length) } = valueOptions;\n                /**\n                 * If there's only one offset of 0, fill in a second with length 1\n                 *\n                 * TODO: Ensure there's a test that covers this removal\n                 */\n                if (offset.length === 1 && offset[0] === 0) {\n                    offset[1] = 1;\n                }\n                /**\n                 * Fill out if offset if fewer offsets than keyframes\n                 */\n                const remainder = offset.length - valueKeyframes.length;\n                remainder > 0 && fillOffset(offset, remainder);\n                /**\n                 * If only one value has been set, ie [1], push a null to the start of\n                 * the keyframe array. This will let us mark a keyframe at this point\n                 * that will later be hydrated with the previous value.\n                 */\n                valueKeyframes.length === 1 && valueKeyframes.unshift(null);\n                /**\n                 * Add keyframes, mapping offsets to absolute time.\n                 */\n                addKeyframes(valueSequence, valueKeyframes, easing, offset, startTime, targetTime);\n                maxDuration = Math.max(delay + duration, maxDuration);\n                totalDuration = Math.max(targetTime, totalDuration);\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    elementSequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(progress(0, totalDuration, at));\n                valueEasing.push(easing || defaults.easing);\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(\"linear\");\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            animationDefinitions.push([\n                element,\n                key,\n                keyframes,\n                Object.assign(Object.assign(Object.assign({}, defaultOptions), { duration: totalDuration, easing: valueEasing, offset: valueOffset }), timelineOptions),\n            ]);\n        }\n    });\n    return animationDefinitions;\n}\nfunction getElementSequence(element, sequences) {\n    !sequences.has(element) && sequences.set(element, {});\n    return sequences.get(element);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\n\nexport { createAnimationsFromTimeline, timeline };\n", "import { velocityPerSecond } from '@motionone/utils';\n\nconst sampleT = 5; // ms\nfunction calcGeneratorVelocity(resolveValue, t, current) {\n    const prevT = Math.max(t - sampleT, 0);\n    return velocityPerSecond(current - resolveValue(prevT), t - prevT);\n}\n\nexport { calcGeneratorVelocity };\n", "const defaults = {\n    stiffness: 100.0,\n    damping: 10.0,\n    mass: 1.0,\n};\n\nexport { defaults };\n", "import { defaults } from './defaults.es.js';\n\nconst calcDampingRatio = (stiffness = defaults.stiffness, damping = defaults.damping, mass = defaults.mass) => damping / (2 * Math.sqrt(stiffness * mass));\n\nexport { calcDampingRatio };\n", "function hasReachedTarget(origin, target, current) {\n    return ((origin < target && current >= target) ||\n        (origin > target && current <= target));\n}\n\nexport { hasReachedTarget };\n", "import { time } from '@motionone/utils';\nimport { defaults } from './defaults.es.js';\nimport { calcDampingRatio } from './utils.es.js';\nimport { hasReachedTarget } from '../utils/has-reached-target.es.js';\nimport { calcGeneratorVelocity } from '../utils/velocity.es.js';\n\nconst spring = ({ stiffness = defaults.stiffness, damping = defaults.damping, mass = defaults.mass, from = 0, to = 1, velocity = 0.0, restSpeed, restDistance, } = {}) => {\n    velocity = velocity ? time.s(velocity) : 0.0;\n    const state = {\n        done: false,\n        hasReachedTarget: false,\n        current: from,\n        target: to,\n    };\n    const initialDelta = to - from;\n    const undampedAngularFreq = Math.sqrt(stiffness / mass) / 1000;\n    const dampingRatio = calcDampingRatio(stiffness, damping, mass);\n    const isGranularScale = Math.abs(initialDelta) < 5;\n    restSpeed || (restSpeed = isGranularScale ? 0.01 : 2);\n    restDistance || (restDistance = isGranularScale ? 0.005 : 0.5);\n    let resolveSpring;\n    if (dampingRatio < 1) {\n        const angularFreq = undampedAngularFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n        // Underdamped spring (bouncy)\n        resolveSpring = (t) => to -\n            Math.exp(-dampingRatio * undampedAngularFreq * t) *\n                (((-velocity + dampingRatio * undampedAngularFreq * initialDelta) /\n                    angularFreq) *\n                    Math.sin(angularFreq * t) +\n                    initialDelta * Math.cos(angularFreq * t));\n    }\n    else {\n        // Critically damped spring\n        resolveSpring = (t) => {\n            return (to -\n                Math.exp(-undampedAngularFreq * t) *\n                    (initialDelta + (-velocity + undampedAngularFreq * initialDelta) * t));\n        };\n    }\n    return (t) => {\n        state.current = resolveSpring(t);\n        const currentVelocity = t === 0\n            ? velocity\n            : calcGeneratorVelocity(resolveSpring, t, state.current);\n        const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n        const isBelowDisplacementThreshold = Math.abs(to - state.current) <= restDistance;\n        state.done = isBelowVelocityThreshold && isBelowDisplacementThreshold;\n        state.hasReachedTarget = hasReachedTarget(from, to, state.current);\n        return state;\n    };\n};\n\nexport { spring };\n", "import { time } from '@motionone/utils';\nimport { calcGeneratorVelocity } from '../utils/velocity.es.js';\nimport { spring } from '../spring/index.es.js';\n\nconst glide = ({ from = 0, velocity = 0.0, power = 0.8, decay = 0.325, bounceDamping, bounceStiffness, changeTarget, min, max, restDistance = 0.5, restSpeed, }) => {\n    decay = time.ms(decay);\n    const state = {\n        hasReachedTarget: false,\n        done: false,\n        current: from,\n        target: from,\n    };\n    const isOutOfBounds = (v) => (min !== undefined && v < min) || (max !== undefined && v > max);\n    const nearestBoundary = (v) => {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    };\n    let amplitude = power * velocity;\n    const ideal = from + amplitude;\n    const target = changeTarget === undefined ? ideal : changeTarget(ideal);\n    state.target = target;\n    /**\n     * If the target has changed we need to re-calculate the amplitude, otherwise\n     * the animation will start from the wrong position.\n     */\n    if (target !== ideal)\n        amplitude = target - from;\n    const calcDelta = (t) => -amplitude * Math.exp(-t / decay);\n    const calcLatest = (t) => target + calcDelta(t);\n    const applyFriction = (t) => {\n        const delta = calcDelta(t);\n        const latest = calcLatest(t);\n        state.done = Math.abs(delta) <= restDistance;\n        state.current = state.done ? target : latest;\n    };\n    /**\n     * Ideally this would resolve for t in a stateless way, we could\n     * do that by always precalculating the animation but as we know\n     * this will be done anyway we can assume that spring will\n     * be discovered during that.\n     */\n    let timeReachedBoundary;\n    let spring$1;\n    const checkCatchBoundary = (t) => {\n        if (!isOutOfBounds(state.current))\n            return;\n        timeReachedBoundary = t;\n        spring$1 = spring({\n            from: state.current,\n            to: nearestBoundary(state.current),\n            velocity: calcGeneratorVelocity(calcLatest, t, state.current), // TODO: This should be passing * 1000\n            damping: bounceDamping,\n            stiffness: bounceStiffness,\n            restDistance,\n            restSpeed,\n        });\n    };\n    checkCatchBoundary(0);\n    return (t) => {\n        /**\n         * We need to resolve the friction to figure out if we need a\n         * spring but we don't want to do this twice per frame. So here\n         * we flag if we updated for this frame and later if we did\n         * we can skip doing it again.\n         */\n        let hasUpdatedFrame = false;\n        if (!spring$1 && timeReachedBoundary === undefined) {\n            hasUpdatedFrame = true;\n            applyFriction(t);\n            checkCatchBoundary(t);\n        }\n        /**\n         * If we have a spring and the provided t is beyond the moment the friction\n         * animation crossed the min/max boundary, use the spring.\n         */\n        if (timeReachedBoundary !== undefined && t > timeReachedBoundary) {\n            state.hasReachedTarget = true;\n            return spring$1(t - timeReachedBoundary);\n        }\n        else {\n            state.hasReachedTarget = false;\n            !hasUpdatedFrame && applyFriction(t);\n            return state;\n        }\n    };\n};\n\nexport { glide };\n", "import { noopReturn } from '@motionone/utils';\n\nconst timeStep = 10;\nconst maxDuration = 10000;\nfunction pregenerateKeyframes(generator, toUnit = noopReturn) {\n    let overshootDuration = undefined;\n    let timestamp = timeStep;\n    let state = generator(0);\n    const keyframes = [toUnit(state.current)];\n    while (!state.done && timestamp < maxDuration) {\n        state = generator(timestamp);\n        keyframes.push(toUnit(state.done ? state.target : state.current));\n        if (overshootDuration === undefined && state.hasReachedTarget) {\n            overshootDuration = timestamp;\n        }\n        timestamp += timeStep;\n    }\n    const duration = timestamp - timeStep;\n    /**\n     * If generating an animation that didn't actually move,\n     * generate a second keyframe so we have an origin and target.\n     */\n    if (keyframes.length === 1)\n        keyframes.push(state.current);\n    return {\n        keyframes,\n        duration: duration / 1000,\n        overshootDuration: (overshootDuration !== null && overshootDuration !== void 0 ? overshootDuration : duration) / 1000,\n    };\n}\n\nexport { pregenerateKeyframes };\n", "import { calcGeneratorVelocity, pregenerateKeyframes } from '@motionone/generators';\nimport { isNumber, isString, noopReturn } from '@motionone/utils';\nimport { getUnitConverter } from '../animate/utils/get-unit.es.js';\nimport { transformDefinitions } from '../animate/utils/transforms.es.js';\nimport { getStyleName } from '../animate/utils/get-style-name.es.js';\n\nfunction canGenerate(value) {\n    return isNumber(value) && !isNaN(value);\n}\nfunction getAsNumber(value) {\n    return isString(value) ? parseFloat(value) : value;\n}\nfunction createGeneratorEasing(createGenerator) {\n    const keyframesCache = new WeakMap();\n    return (options = {}) => {\n        const generatorCache = new Map();\n        const getGenerator = (from = 0, to = 100, velocity = 0, isScale = false) => {\n            const key = `${from}-${to}-${velocity}-${isScale}`;\n            if (!generatorCache.has(key)) {\n                generatorCache.set(key, createGenerator(Object.assign({ from,\n                    to,\n                    velocity }, options)));\n            }\n            return generatorCache.get(key);\n        };\n        const getKeyframes = (generator, toUnit) => {\n            if (!keyframesCache.has(generator)) {\n                keyframesCache.set(generator, pregenerateKeyframes(generator, toUnit));\n            }\n            return keyframesCache.get(generator);\n        };\n        return {\n            createAnimation: (keyframes, shouldGenerate = true, getOrigin, name, motionValue) => {\n                let settings;\n                let origin;\n                let target;\n                let velocity = 0;\n                let toUnit = noopReturn;\n                const numKeyframes = keyframes.length;\n                /**\n                 * If we should generate an animation for this value, run some preperation\n                 * like resolving target/origin, finding a unit (if any) and determine if\n                 * it is actually possible to generate.\n                 */\n                if (shouldGenerate) {\n                    toUnit = getUnitConverter(keyframes, name ? transformDefinitions.get(getStyleName(name)) : undefined);\n                    const targetDefinition = keyframes[numKeyframes - 1];\n                    target = getAsNumber(targetDefinition);\n                    if (numKeyframes > 1 && keyframes[0] !== null) {\n                        /**\n                         * If we have multiple keyframes, take the initial keyframe as the origin.\n                         */\n                        origin = getAsNumber(keyframes[0]);\n                    }\n                    else {\n                        const prevGenerator = motionValue === null || motionValue === void 0 ? void 0 : motionValue.generator;\n                        /**\n                         * If we have an existing generator for this value we can use it to resolve\n                         * the animation's current value and velocity.\n                         */\n                        if (prevGenerator) {\n                            /**\n                             * If we have a generator for this value we can use it to resolve\n                             * the animations's current value and velocity.\n                             */\n                            const { animation, generatorStartTime } = motionValue;\n                            const startTime = (animation === null || animation === void 0 ? void 0 : animation.startTime) || generatorStartTime || 0;\n                            const currentTime = (animation === null || animation === void 0 ? void 0 : animation.currentTime) || performance.now() - startTime;\n                            const prevGeneratorCurrent = prevGenerator(currentTime).current;\n                            origin = prevGeneratorCurrent;\n                            velocity = calcGeneratorVelocity((t) => prevGenerator(t).current, currentTime, prevGeneratorCurrent);\n                        }\n                        else if (getOrigin) {\n                            /**\n                             * As a last resort, read the origin from the DOM.\n                             */\n                            origin = getAsNumber(getOrigin());\n                        }\n                    }\n                }\n                /**\n                 * If we've determined it is possible to generate an animation, do so.\n                 */\n                if (canGenerate(origin) && canGenerate(target)) {\n                    const generator = getGenerator(origin, target, velocity, name === null || name === void 0 ? void 0 : name.includes(\"scale\"));\n                    settings = Object.assign(Object.assign({}, getKeyframes(generator, toUnit)), { easing: \"linear\" });\n                    // TODO Add test for this\n                    if (motionValue) {\n                        motionValue.generator = generator;\n                        motionValue.generatorStartTime = performance.now();\n                    }\n                }\n                /**\n                 * If by now we haven't generated a set of keyframes, create a generic generator\n                 * based on the provided props that animates from 0-100 to fetch a rough\n                 * \"overshootDuration\" - the moment when the generator first hits the animation target.\n                 * Then return animation settings that will run a normal animation for that duration.\n                 */\n                if (!settings) {\n                    const keyframesMetadata = getKeyframes(getGenerator(0, 100));\n                    settings = {\n                        easing: \"ease\",\n                        duration: keyframesMetadata.overshootDuration,\n                    };\n                }\n                return settings;\n            },\n        };\n    };\n}\n\nexport { createGeneratorEasing };\n", "import { spring as spring$1 } from '@motionone/generators';\nimport { createGeneratorEasing } from '../create-generator-easing.es.js';\n\nconst spring = createGeneratorEasing(spring$1);\n\nexport { spring };\n", "import { glide as glide$1 } from '@motionone/generators';\nimport { createGeneratorEasing } from '../create-generator-easing.es.js';\n\nconst glide = createGeneratorEasing(glide$1);\n\nexport { glide };\n", "import { resolveElements } from '../utils/resolve-elements.es.js';\nimport { isFunction } from '@motionone/utils';\n\nconst thresholds = {\n    any: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"any\" } = {}) {\n    /**\n     * If this browser doesn't support IntersectionObserver, return a dummy stop function.\n     * Default triggering of onStart is tricky - it could be used for starting/stopping\n     * videos, lazy loading content etc. We could provide an option to enable a fallback, or\n     * provide a fallback callback option.\n     */\n    if (typeof IntersectionObserver === \"undefined\") {\n        return () => { };\n    }\n    const elements = resolveElements(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry);\n                if (isFunction(newOnEnd)) {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (onEnd) {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\nexport { inView };\n", "import { __rest } from 'tslib';\nimport { resize } from '../resize/index.es.js';\nimport { createScrollInfo } from './info.es.js';\nimport { createOnScrollHandler } from './on-scroll-handler.es.js';\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.documentElement ? window : element;\nfunction scroll(onScroll, _a = {}) {\n    var { container = document.documentElement } = _a, options = __rest(_a, [\"container\"]);\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = createScrollInfo();\n    const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const listener = () => {\n            const time = performance.now();\n            for (const handler of containerHandlers)\n                handler.measure();\n            for (const handler of containerHandlers)\n                handler.update(time);\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, resize(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n    }\n    const listener = scrollListeners.get(container);\n    const onLoadProcesss = requestAnimationFrame(listener);\n    return () => {\n        var _a;\n        if (typeof onScroll !== \"function\")\n            onScroll.stop();\n        cancelAnimationFrame(onLoadProcesss);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const containerHandlers = onScrollHandlers.get(container);\n        if (!containerHandlers)\n            return;\n        containerHandlers.delete(containerHandler);\n        if (containerHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const listener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (listener) {\n            getEventTarget(container).removeEventListener(\"scroll\", listener);\n            (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n            window.removeEventListener(\"resize\", listener);\n        }\n    };\n}\n\nexport { scroll };\n", "import { __rest } from 'tslib';\nimport { invariant } from 'hey-listen';\nimport { noop } from '@motionone/utils';\nimport { animateStyle } from '../animate/animate-style.es.js';\nimport { style } from '../animate/style.es.js';\nimport { getOptions } from '../animate/utils/options.es.js';\nimport { hasChanged } from './utils/has-changed.es.js';\nimport { resolveVariant } from './utils/resolve-variant.es.js';\nimport { scheduleAnimation, unscheduleAnimation } from './utils/schedule.es.js';\nimport { inView } from './gestures/in-view.es.js';\nimport { hover } from './gestures/hover.es.js';\nimport { press } from './gestures/press.es.js';\nimport { motionEvent } from './utils/events.es.js';\nimport { Animation } from '@motionone/animation';\n\nconst gestures = { inView, hover, press };\n/**\n * A list of state types, in priority order. If a value is defined in\n * a righter-most type, it will override any definition in a lefter-most.\n */\nconst stateTypes = [\"initial\", \"animate\", ...Object.keys(gestures), \"exit\"];\n/**\n * A global store of all generated motion states. This can be used to lookup\n * a motion state for a given Element.\n */\nconst mountedStates = new WeakMap();\nfunction createMotionState(options = {}, parent) {\n    /**\n     * The element represented by the motion state. This is an empty reference\n     * when we create the state to support SSR and allow for later mounting\n     * in view libraries.\n     *\n     * @ts-ignore\n     */\n    let element;\n    /**\n     * Calculate a depth that we can use to order motion states by tree depth.\n     */\n    let depth = parent ? parent.getDepth() + 1 : 0;\n    /**\n     * Track which states are currently active.\n     */\n    const activeStates = { initial: true, animate: true };\n    /**\n     * A map of functions that, when called, will remove event listeners for\n     * a given gesture.\n     */\n    const gestureSubscriptions = {};\n    /**\n     * Initialise a context to share through motion states. This\n     * will be populated by variant names (if any).\n     */\n    const context = {};\n    for (const name of stateTypes) {\n        context[name] =\n            typeof options[name] === \"string\"\n                ? options[name]\n                : parent === null || parent === void 0 ? void 0 : parent.getContext()[name];\n    }\n    /**\n     * If initial is set to false we use the animate prop as the initial\n     * animation state.\n     */\n    const initialVariantSource = options.initial === false ? \"animate\" : \"initial\";\n    /**\n     * Destructure an initial target out from the resolved initial variant.\n     */\n    let _a = resolveVariant(options[initialVariantSource] || context[initialVariantSource], options.variants) || {}, target = __rest(_a, [\"transition\"]);\n    /**\n     * The base target is a cached map of values that we'll use to animate\n     * back to if a value is removed from all active state types. This\n     * is usually the initial value as read from the DOM, for instance if\n     * it hasn't been defined in initial.\n     */\n    const baseTarget = Object.assign({}, target);\n    /**\n     * A generator that will be processed by the global animation scheduler.\n     * This yields when it switches from reading the DOM to writing to it\n     * to prevent layout thrashing.\n     */\n    function* animateUpdates() {\n        var _a, _b;\n        const prevTarget = target;\n        target = {};\n        const animationOptions = {};\n        for (const name of stateTypes) {\n            if (!activeStates[name])\n                continue;\n            const variant = resolveVariant(options[name]);\n            if (!variant)\n                continue;\n            for (const key in variant) {\n                if (key === \"transition\")\n                    continue;\n                target[key] = variant[key];\n                animationOptions[key] = getOptions((_b = (_a = variant.transition) !== null && _a !== void 0 ? _a : options.transition) !== null && _b !== void 0 ? _b : {}, key);\n            }\n        }\n        const allTargetKeys = new Set([\n            ...Object.keys(target),\n            ...Object.keys(prevTarget),\n        ]);\n        const animationFactories = [];\n        allTargetKeys.forEach((key) => {\n            var _a;\n            if (target[key] === undefined) {\n                target[key] = baseTarget[key];\n            }\n            if (hasChanged(prevTarget[key], target[key])) {\n                (_a = baseTarget[key]) !== null && _a !== void 0 ? _a : (baseTarget[key] = style.get(element, key));\n                animationFactories.push(animateStyle(element, key, target[key], animationOptions[key], Animation));\n            }\n        });\n        // Wait for all animation states to read from the DOM\n        yield;\n        const animations = animationFactories\n            .map((factory) => factory())\n            .filter(Boolean);\n        if (!animations.length)\n            return;\n        const animationTarget = target;\n        element.dispatchEvent(motionEvent(\"motionstart\", animationTarget));\n        Promise.all(animations.map((animation) => animation.finished))\n            .then(() => {\n            element.dispatchEvent(motionEvent(\"motioncomplete\", animationTarget));\n        })\n            .catch(noop);\n    }\n    const setGesture = (name, isActive) => () => {\n        activeStates[name] = isActive;\n        scheduleAnimation(state);\n    };\n    const updateGestureSubscriptions = () => {\n        for (const name in gestures) {\n            const isGestureActive = gestures[name].isActive(options);\n            const remove = gestureSubscriptions[name];\n            if (isGestureActive && !remove) {\n                gestureSubscriptions[name] = gestures[name].subscribe(element, {\n                    enable: setGesture(name, true),\n                    disable: setGesture(name, false),\n                }, options);\n            }\n            else if (!isGestureActive && remove) {\n                remove();\n                delete gestureSubscriptions[name];\n            }\n        }\n    };\n    const state = {\n        update: (newOptions) => {\n            if (!element)\n                return;\n            options = newOptions;\n            updateGestureSubscriptions();\n            scheduleAnimation(state);\n        },\n        setActive: (name, isActive) => {\n            if (!element)\n                return;\n            activeStates[name] = isActive;\n            scheduleAnimation(state);\n        },\n        animateUpdates,\n        getDepth: () => depth,\n        getTarget: () => target,\n        getOptions: () => options,\n        getContext: () => context,\n        mount: (newElement) => {\n            invariant(Boolean(newElement), \"Animation state must be mounted with valid Element\");\n            element = newElement;\n            mountedStates.set(element, state);\n            updateGestureSubscriptions();\n            return () => {\n                mountedStates.delete(element);\n                unscheduleAnimation(state);\n                for (const key in gestureSubscriptions) {\n                    gestureSubscriptions[key]();\n                }\n            };\n        },\n        isMounted: () => Boolean(element),\n    };\n    return state;\n}\n\nexport { createMotionState, mountedStates };\n", "import { __rest } from 'tslib';\nimport { dispatchViewEvent } from '../utils/events.es.js';\nimport { inView as inView$1 } from '../../gestures/in-view.es.js';\n\nconst inView = {\n    isActive: (options) => <PERSON><PERSON><PERSON>(options.inView),\n    subscribe: (element, { enable, disable }, { inViewOptions = {} }) => {\n        const { once } = inViewOptions, viewOptions = __rest(inViewOptions, [\"once\"]);\n        return inView$1(element, (enterEntry) => {\n            enable();\n            dispatchViewEvent(element, \"viewenter\", enterEntry);\n            if (!once) {\n                return (leaveEntry) => {\n                    disable();\n                    dispatchViewEvent(element, \"viewleave\", leaveEntry);\n                };\n            }\n        }, viewOptions);\n    },\n};\n\nexport { inView };\n", "const motionEvent = (name, target) => new CustomEvent(name, { detail: { target } });\nfunction dispatchPointerEvent(element, name, event) {\n    element.dispatchEvent(new CustomEvent(name, { detail: { originalEvent: event } }));\n}\nfunction dispatchViewEvent(element, name, entry) {\n    element.dispatchEvent(new CustomEvent(name, { detail: { originalEntry: entry } }));\n}\n\nexport { dispatchPointerEvent, dispatchViewEvent, motionEvent };\n", "import { dispatchPointerEvent } from '../utils/events.es.js';\n\nconst mouseEvent = (element, name, action) => (event) => {\n    if (event.pointerType && event.pointerType !== \"mouse\")\n        return;\n    action();\n    dispatchPointerEvent(element, name, event);\n};\nconst hover = {\n    isActive: (options) => Bo<PERSON>an(options.hover),\n    subscribe: (element, { enable, disable }) => {\n        const onEnter = mouseEvent(element, \"hoverstart\", enable);\n        const onLeave = mouseEvent(element, \"hoverend\", disable);\n        element.addEventListener(\"pointerenter\", onEnter);\n        element.addEventListener(\"pointerleave\", onLeave);\n        return () => {\n            element.removeEventListener(\"pointerenter\", onEnter);\n            element.removeEventListener(\"pointerleave\", onLeave);\n        };\n    },\n};\n\nexport { hover };\n", "import { dispatchPointerEvent } from '../utils/events.es.js';\n\nconst press = {\n    isActive: (options) => <PERSON><PERSON><PERSON>(options.press),\n    subscribe: (element, { enable, disable }) => {\n        const onPointerUp = (event) => {\n            disable();\n            dispatchPointerEvent(element, \"pressend\", event);\n            window.removeEventListener(\"pointerup\", onPointerUp);\n        };\n        const onPointerDown = (event) => {\n            enable();\n            dispatchPointerEvent(element, \"pressstart\", event);\n            window.addEventListener(\"pointerup\", onPointerUp);\n        };\n        element.addEventListener(\"pointerdown\", onPointerDown);\n        return () => {\n            element.removeEventListener(\"pointerdown\", onPointerDown);\n            window.removeEventListener(\"pointerup\", onPointerUp);\n        };\n    },\n};\n\nexport { press };\n", "import { animate as animate$1, withControls } from '@motionone/dom';\nimport { isFunction } from '@motionone/utils';\nimport { Animation } from '@motionone/animation';\n\nfunction animateProgress(target, options = {}) {\n    return withControls([\n        () => {\n            const animation = new Animation(target, [0, 1], options);\n            animation.finished.catch(() => { });\n            return animation;\n        },\n    ], options, options.duration);\n}\nfunction animate(target, keyframesOrOptions, options) {\n    const factory = isFunction(target) ? animateProgress : animate$1;\n    return factory(target, keyframesOrOptions, options);\n}\n\nexport { animate, animateProgress };\n", "/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {nothing} from '../lit-html.js';\n\n/**\n * For AttributeParts, sets the attribute if the value is defined and removes\n * the attribute if the value is undefined.\n *\n * For other part types, this directive is a no-op.\n */\nexport const ifDefined = <T>(value: T) => value ?? nothing;\n", "import { ThemeCtrl } from '@walletconnect/modal-core'\nimport { css } from 'lit'\n\nfunction themeModeVariables() {\n  const themeMode = ThemeCtrl.state.themeMode ?? 'dark'\n  const themeModePresets = {\n    light: {\n      foreground: { 1: `rgb(20,20,20)`, 2: `rgb(121,134,134)`, 3: `rgb(158,169,169)` },\n      background: { 1: `rgb(255,255,255)`, 2: `rgb(241,243,243)`, 3: `rgb(228,231,231)` },\n      overlay: 'rgba(0,0,0,0.1)'\n    },\n    dark: {\n      foreground: { 1: `rgb(228,231,231)`, 2: `rgb(148,158,158)`, 3: `rgb(110,119,119)` },\n      background: { 1: `rgb(20,20,20)`, 2: `rgb(39,42,42)`, 3: `rgb(59,64,64)` },\n      overlay: 'rgba(255,255,255,0.1)'\n    }\n  }\n  const themeModeColors = themeModePresets[themeMode]\n\n  return {\n    '--wcm-color-fg-1': themeModeColors.foreground[1],\n    '--wcm-color-fg-2': themeModeColors.foreground[2],\n    '--wcm-color-fg-3': themeModeColors.foreground[3],\n    '--wcm-color-bg-1': themeModeColors.background[1],\n    '--wcm-color-bg-2': themeModeColors.background[2],\n    '--wcm-color-bg-3': themeModeColors.background[3],\n    '--wcm-color-overlay': themeModeColors.overlay\n  }\n}\n\nfunction themeVariablesPresets() {\n  return {\n    '--wcm-accent-color': '#3396FF',\n    '--wcm-accent-fill-color': '#FFFFFF',\n    '--wcm-z-index': '89',\n    '--wcm-background-color': '#3396FF',\n    '--wcm-background-border-radius': '8px',\n    '--wcm-container-border-radius': '30px',\n    '--wcm-wallet-icon-border-radius': '15px',\n    '--wcm-wallet-icon-large-border-radius': '30px',\n    '--wcm-wallet-icon-small-border-radius': '7px',\n    '--wcm-input-border-radius': '28px',\n    '--wcm-button-border-radius': '10px',\n    '--wcm-notification-border-radius': '36px',\n    '--wcm-secondary-button-border-radius': '28px',\n    '--wcm-icon-button-border-radius': '50%',\n    '--wcm-button-hover-highlight-border-radius': '10px',\n    '--wcm-text-big-bold-size': '20px',\n    '--wcm-text-big-bold-weight': '600',\n    '--wcm-text-big-bold-line-height': '24px',\n    '--wcm-text-big-bold-letter-spacing': '-0.03em',\n    '--wcm-text-big-bold-text-transform': 'none',\n    '--wcm-text-xsmall-bold-size': '10px',\n    '--wcm-text-xsmall-bold-weight': '700',\n    '--wcm-text-xsmall-bold-line-height': '12px',\n    '--wcm-text-xsmall-bold-letter-spacing': '0.02em',\n    '--wcm-text-xsmall-bold-text-transform': 'uppercase',\n    '--wcm-text-xsmall-regular-size': '12px',\n    '--wcm-text-xsmall-regular-weight': '600',\n    '--wcm-text-xsmall-regular-line-height': '14px',\n    '--wcm-text-xsmall-regular-letter-spacing': '-0.03em',\n    '--wcm-text-xsmall-regular-text-transform': 'none',\n    '--wcm-text-small-thin-size': '14px',\n    '--wcm-text-small-thin-weight': '500',\n    '--wcm-text-small-thin-line-height': '16px',\n    '--wcm-text-small-thin-letter-spacing': '-0.03em',\n    '--wcm-text-small-thin-text-transform': 'none',\n    '--wcm-text-small-regular-size': '14px',\n    '--wcm-text-small-regular-weight': '600',\n    '--wcm-text-small-regular-line-height': '16px',\n    '--wcm-text-small-regular-letter-spacing': '-0.03em',\n    '--wcm-text-small-regular-text-transform': 'none',\n    '--wcm-text-medium-regular-size': '16px',\n    '--wcm-text-medium-regular-weight': '600',\n    '--wcm-text-medium-regular-line-height': '20px',\n    '--wcm-text-medium-regular-letter-spacing': '-0.03em',\n    '--wcm-text-medium-regular-text-transform': 'none',\n    '--wcm-font-family':\n      \"-apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, Ubuntu, 'Helvetica Neue', sans-serif\",\n    '--wcm-font-feature-settings': `'tnum' on, 'lnum' on, 'case' on`,\n    '--wcm-success-color': 'rgb(38,181,98)',\n    '--wcm-error-color': 'rgb(242, 90, 103)',\n    '--wcm-overlay-background-color': 'rgba(0, 0, 0, 0.3)',\n    '--wcm-overlay-backdrop-filter': 'none'\n  }\n}\n\nexport const ThemeUtil = {\n  getPreset(key: string) {\n    return themeVariablesPresets()[key as never]\n  },\n\n  setTheme() {\n    const root: HTMLElement | null = document.querySelector(':root')\n    const { themeVariables } = ThemeCtrl.state\n\n    if (root) {\n      const variables = {\n        ...themeModeVariables(),\n        ...themeVariablesPresets(),\n        ...themeVariables\n      }\n\n      Object.entries(variables).forEach(([key, val]) => root.style.setProperty(key, val))\n    }\n  },\n\n  globalCss: css`\n    *,\n    *::after,\n    *::before {\n      margin: 0;\n      padding: 0;\n      box-sizing: border-box;\n      font-style: normal;\n      text-rendering: optimizeSpeed;\n      -webkit-font-smoothing: antialiased;\n      -moz-osx-font-smoothing: grayscale;\n      -webkit-tap-highlight-color: transparent;\n      backface-visibility: hidden;\n    }\n\n    button {\n      cursor: pointer;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      position: relative;\n      border: none;\n      background-color: transparent;\n      transition: all 0.2s ease;\n    }\n\n    @media (hover: hover) and (pointer: fine) {\n      button:active {\n        transition: all 0.1s ease;\n        transform: scale(0.93);\n      }\n    }\n\n    button::after {\n      content: '';\n      position: absolute;\n      top: 0;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      transition:\n        background-color,\n        0.2s ease;\n    }\n\n    button:disabled {\n      cursor: not-allowed;\n    }\n\n    button svg,\n    button wcm-text {\n      position: relative;\n      z-index: 1;\n    }\n\n    input {\n      border: none;\n      outline: none;\n      appearance: none;\n    }\n\n    img {\n      display: block;\n    }\n\n    ::selection {\n      color: var(--wcm-accent-fill-color);\n      background: var(--wcm-accent-color);\n    }\n  `\n}\n", "import type { TemplateResult } from 'lit'\nimport { html, LitElement } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { classMap } from 'lit/directives/class-map.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-button')\nexport class WcmButton extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property({ type: Boolean }) public disabled? = false\n\n  @property() public iconLeft?: TemplateResult<2> = undefined\n\n  @property() public iconRight?: TemplateResult<2> = undefined\n\n  @property() public onClick: () => void = () => null\n\n  @property() public variant: 'default' | 'ghost' | 'outline' = 'default'\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const classes = {\n      'wcm-icon-left': this.iconLeft !== undefined,\n      'wcm-icon-right': this.iconRight !== undefined,\n      'wcm-ghost': this.variant === 'ghost',\n      'wcm-outline': this.variant === 'outline'\n    }\n    let textColor = 'inverse'\n    if (this.variant === 'ghost') {\n      textColor = 'secondary'\n    }\n    if (this.variant === 'outline') {\n      textColor = 'accent'\n    }\n\n    return html`\n      <button class=${classMap(classes)} ?disabled=${this.disabled} @click=${this.onClick}>\n        ${this.iconLeft}\n        <wcm-text variant=\"small-regular\" color=${textColor}>\n          <slot></slot>\n        </wcm-text>\n        ${this.iconRight}\n      </button>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-button': WcmButton\n  }\n}\n", "import { html, LitElement } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { classMap } from 'lit/directives/class-map.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\ntype Variant = 'primary' | 'secondary'\n\n@customElement('wcm-button-big')\nexport class WcmButtonBig extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property({ type: Boolean }) public disabled? = false\n\n  @property() public variant?: Variant = 'primary'\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const classes = {\n      'wcm-secondary': this.variant === 'secondary'\n    }\n\n    return html`\n      <button ?disabled=${this.disabled} class=${classMap(classes)}>\n        <slot></slot>\n      </button>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-button-big': WcmButtonBig\n  }\n}\n", "import { html, LitElement } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-info-footer')\nexport class WcmInfoFooter extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <div>\n        <slot></slot>\n      </div>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-info-footer': WcmInfoFooter\n  }\n}\n", "import { svg } from 'lit-html'\n\nexport const SvgUtil = {\n  CROSS_ICON: svg`\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\">\n      <path\n        d=\"M9.94 11A.75.75 0 1 0 11 9.94L7.414 6.353a.5.5 0 0 1 0-.708L11 2.061A.75.75 0 1 0 9.94 1L6.353 4.586a.5.5 0 0 1-.708 0L2.061 1A.75.75 0 0 0 1 2.06l3.586 3.586a.5.5 0 0 1 0 .708L1 9.939A.75.75 0 1 0 2.06 11l3.586-3.586a.5.5 0 0 1 .708 0L9.939 11Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  WALLET_CONNECT_LOGO: svg`\n    <svg width=\"178\" height=\"29\" viewBox=\"0 0 178 29\" id=\"wcm-wc-logo\">\n      <path\n        d=\"M10.683 7.926c5.284-5.17 13.85-5.17 19.134 0l.636.623a.652.652 0 0 1 0 .936l-2.176 2.129a.343.343 0 0 1-.478 0l-.875-.857c-3.686-3.607-9.662-3.607-13.348 0l-.937.918a.343.343 0 0 1-.479 0l-2.175-2.13a.652.652 0 0 1 0-.936l.698-.683Zm23.633 4.403 1.935 1.895a.652.652 0 0 1 0 .936l-8.73 8.543a.687.687 0 0 1-.956 0L20.37 17.64a.172.172 0 0 0-.239 0l-6.195 6.063a.687.687 0 0 1-.957 0l-8.73-8.543a.652.652 0 0 1 0-.936l1.936-1.895a.687.687 0 0 1 .957 0l6.196 6.064a.172.172 0 0 0 .239 0l6.195-6.064a.687.687 0 0 1 .957 0l6.196 6.064a.172.172 0 0 0 .24 0l6.195-6.064a.687.687 0 0 1 .956 0ZM48.093 20.948l2.338-9.355c.139-.515.258-1.07.416-1.942.12.872.258 1.427.357 1.942l2.022 9.355h4.181l3.528-13.874h-3.21l-1.943 8.523a24.825 24.825 0 0 0-.456 2.457c-.158-.931-.317-1.625-.495-2.438l-1.883-8.542h-4.201l-2.042 8.542a41.204 41.204 0 0 0-.475 2.438 41.208 41.208 0 0 0-.476-2.438l-1.903-8.542h-3.349l3.508 13.874h4.083ZM63.33 21.304c1.585 0 2.596-.654 3.11-1.605-.059.297-.078.595-.078.892v.357h2.655V15.22c0-2.735-1.248-4.32-4.3-4.32-2.636 0-4.36 1.466-4.52 3.487h2.914c.1-.891.734-1.426 1.705-1.426.911 0 1.407.515 1.407 1.11 0 .435-.258.693-1.03.792l-1.388.159c-2.061.257-3.825 1.01-3.825 3.19 0 1.982 1.645 3.092 3.35 3.092Zm.891-2.041c-.773 0-1.348-.436-1.348-1.19 0-.733.655-1.09 1.645-1.268l.674-.119c.575-.118.892-.218 1.09-.396v.912c0 1.228-.892 2.06-2.06 2.06ZM70.398 7.074v13.874h2.874V7.074h-2.874ZM74.934 7.074v13.874h2.874V7.074h-2.874ZM84.08 21.304c2.735 0 4.5-1.546 4.697-3.567h-2.893c-.139.892-.892 1.387-1.804 1.387-1.228 0-2.12-.99-2.14-2.358h6.897v-.555c0-3.21-1.764-5.312-4.816-5.312-2.933 0-4.994 2.062-4.994 5.173 0 3.37 2.12 5.232 5.053 5.232Zm-2.16-6.421c.119-1.11.932-1.922 2.081-1.922 1.11 0 1.883.772 1.903 1.922H81.92ZM94.92 21.146c.633 0 1.248-.1 1.525-.179v-2.18c-.218.04-.475.06-.693.06-1.05 0-1.427-.595-1.427-1.566v-3.805h2.338v-2.24h-2.338V7.788H91.47v3.448H89.37v2.24h2.1v4.201c0 2.3 1.15 3.469 3.45 3.469ZM104.62 21.304c3.924 0 6.302-2.299 6.599-5.608h-3.111c-.238 1.803-1.506 3.032-3.369 3.032-2.2 0-3.746-1.784-3.746-4.796 0-2.953 1.605-4.638 3.805-4.638 1.883 0 2.953 1.15 3.171 2.834h3.191c-.317-3.448-2.854-5.41-6.342-5.41-3.984 0-7.036 2.695-7.036 7.214 0 4.677 2.676 7.372 6.838 7.372ZM117.449 21.304c2.993 0 5.114-1.882 5.114-5.172 0-3.23-2.121-5.233-5.114-5.233-2.972 0-5.093 2.002-5.093 5.233 0 3.29 2.101 5.172 5.093 5.172Zm0-2.22c-1.327 0-2.18-1.09-2.18-2.952 0-1.903.892-2.973 2.18-2.973 1.308 0 2.2 1.07 2.2 2.973 0 1.862-.872 2.953-2.2 2.953ZM126.569 20.948v-5.689c0-1.208.753-2.1 1.823-2.1 1.011 0 1.606.773 1.606 2.06v5.729h2.873v-6.144c0-2.339-1.229-3.905-3.428-3.905-1.526 0-2.458.734-2.953 1.606a5.31 5.31 0 0 0 .079-.892v-.377h-2.874v9.712h2.874ZM137.464 20.948v-5.689c0-1.208.753-2.1 1.823-2.1 1.011 0 1.606.773 1.606 2.06v5.729h2.873v-6.144c0-2.339-1.228-3.905-3.428-3.905-1.526 0-2.458.734-2.953 1.606a5.31 5.31 0 0 0 .079-.892v-.377h-2.874v9.712h2.874ZM149.949 21.304c2.735 0 4.499-1.546 4.697-3.567h-2.893c-.139.892-.892 1.387-1.804 1.387-1.228 0-2.12-.99-2.14-2.358h6.897v-.555c0-3.21-1.764-5.312-4.816-5.312-2.933 0-4.994 2.062-4.994 5.173 0 3.37 2.12 5.232 5.053 5.232Zm-2.16-6.421c.119-1.11.932-1.922 2.081-1.922 1.11 0 1.883.772 1.903 1.922h-3.984ZM160.876 21.304c3.013 0 4.658-1.645 4.975-4.201h-2.874c-.099 1.07-.713 1.982-2.001 1.982-1.309 0-2.2-1.21-2.2-2.993 0-1.942 1.03-2.933 2.259-2.933 1.209 0 1.803.872 1.883 1.882h2.873c-.218-2.358-1.823-4.142-4.776-4.142-2.874 0-5.153 1.903-5.153 5.193 0 3.25 1.923 5.212 5.014 5.212ZM172.067 21.146c.634 0 1.248-.1 1.526-.179v-2.18c-.218.04-.476.06-.694.06-1.05 0-1.427-.595-1.427-1.566v-3.805h2.339v-2.24h-2.339V7.788h-2.854v3.448h-2.1v2.24h2.1v4.201c0 2.3 1.15 3.469 3.449 3.469Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  WALLET_CONNECT_ICON: svg`\n    <svg width=\"28\" height=\"20\" viewBox=\"0 0 28 20\">\n      <g clip-path=\"url(#a)\">\n        <path\n          d=\"M7.386 6.482c3.653-3.576 9.575-3.576 13.228 0l.44.43a.451.451 0 0 1 0 .648L19.55 9.033a.237.237 0 0 1-.33 0l-.606-.592c-2.548-2.496-6.68-2.496-9.228 0l-.648.634a.237.237 0 0 1-.33 0L6.902 7.602a.451.451 0 0 1 0-.647l.483-.473Zm16.338 3.046 1.339 1.31a.451.451 0 0 1 0 .648l-6.035 5.909a.475.475 0 0 1-.662 0L14.083 13.2a.119.119 0 0 0-.166 0l-4.283 4.194a.475.475 0 0 1-.662 0l-6.035-5.91a.451.451 0 0 1 0-.647l1.338-1.31a.475.475 0 0 1 .662 0l4.283 4.194c.**************.166 0l4.283-4.194a.475.475 0 0 1 .662 0l4.283 4.194c.**************.166 0l4.283-4.194a.475.475 0 0 1 .662 0Z\"\n          fill=\"#000000\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"a\"><path fill=\"#ffffff\" d=\"M0 0h28v20H0z\" /></clipPath>\n      </defs>\n    </svg>\n  `,\n\n  WALLET_CONNECT_ICON_COLORED: svg`\n    <svg width=\"96\" height=\"96\" fill=\"none\">\n      <path\n        fill=\"#fff\"\n        d=\"M25.322 33.597c12.525-12.263 32.83-12.263 45.355 0l1.507 1.476a1.547 1.547 0 0 1 0 2.22l-5.156 5.048a.814.814 0 0 1-1.134 0l-2.074-2.03c-8.737-8.555-22.903-8.555-31.64 0l-2.222 2.175a.814.814 0 0 1-1.134 0l-5.156-5.049a1.547 1.547 0 0 1 0-2.22l1.654-1.62Zm56.019 10.44 4.589 4.494a1.547 1.547 0 0 1 0 2.22l-20.693 20.26a1.628 1.628 0 0 1-2.267 0L48.283 56.632a.407.407 0 0 0-.567 0L33.03 71.012a1.628 1.628 0 0 1-2.268 0L10.07 50.75a1.547 1.547 0 0 1 0-2.22l4.59-4.494a1.628 1.628 0 0 1 2.267 0l14.687 14.38c.**************.567 0l14.685-14.38a1.628 1.628 0 0 1 2.268 0l14.687 14.38c.**************.567 0l14.686-14.38a1.628 1.628 0 0 1 2.268 0Z\"\n      />\n      <path\n        stroke=\"#000\"\n        d=\"M25.672 33.954c12.33-12.072 32.325-12.072 44.655 0l1.508 1.476a1.047 1.047 0 0 1 0 1.506l-5.157 5.048a.314.314 0 0 1-.434 0l-2.074-2.03c-8.932-8.746-23.409-8.746-32.34 0l-2.222 2.174a.314.314 0 0 1-.434 0l-5.157-5.048a1.047 1.047 0 0 1 0-1.506l1.655-1.62Zm55.319 10.44 4.59 4.494a1.047 1.047 0 0 1 0 1.506l-20.694 20.26a1.128 1.128 0 0 1-1.568 0l-14.686-14.38a.907.907 0 0 0-1.267 0L32.68 70.655a1.128 1.128 0 0 1-1.568 0L10.42 50.394a1.047 1.047 0 0 1 0-1.506l4.59-4.493a1.128 1.128 0 0 1 1.567 0l14.687 14.379a.907.907 0 0 0 1.266 0l-.35-.357.35.357 14.686-14.38a1.128 1.128 0 0 1 1.568 0l14.687 14.38a.907.907 0 0 0 1.267 0l14.686-14.38a1.128 1.128 0 0 1 1.568 0Z\"\n      />\n    </svg>\n  `,\n\n  BACK_ICON: svg`\n    <svg width=\"10\" height=\"18\" viewBox=\"0 0 10 18\">\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M8.735.179a.75.75 0 0 1 .087 1.057L2.92 8.192a1.25 1.25 0 0 0 0 1.617l5.902 6.956a.75.75 0 1 1-1.144.97L1.776 10.78a2.75 2.75 0 0 1 0-3.559L7.678.265A.75.75 0 0 1 8.735.18Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  COPY_ICON: svg`\n    <svg width=\"24\" height=\"24\" fill=\"none\">\n      <path\n        fill=\"#fff\"\n        fill-rule=\"evenodd\"\n        d=\"M7.01 7.01c.03-1.545.138-2.5.535-3.28A5 5 0 0 1 9.73 1.545C10.8 1 12.2 1 15 1c2.8 0 4.2 0 5.27.545a5 5 0 0 1 2.185 2.185C23 4.8 23 6.2 23 9c0 2.8 0 4.2-.545 5.27a5 5 0 0 1-2.185 2.185c-.78.397-1.735.505-3.28.534l-.001.01c-.03 1.54-.138 2.493-.534 3.27a5 5 0 0 1-2.185 2.186C13.2 23 11.8 23 9 23c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C1 19.2 1 17.8 1 15c0-2.8 0-4.2.545-5.27A5 5 0 0 1 3.73 7.545C4.508 7.149 5.46 7.04 7 7.01h.01ZM15 15.5c-1.425 0-2.403-.001-3.162-.063-.74-.06-1.139-.172-1.427-.319a3.5 3.5 0 0 1-1.53-1.529c-.146-.288-.257-.686-.318-1.427C8.501 11.403 8.5 10.425 8.5 9c0-1.425.001-2.403.063-3.162.06-.74.172-1.139.318-1.427a3.5 3.5 0 0 1 1.53-1.53c.288-.146.686-.257 1.427-.318.759-.062 1.737-.063 3.162-.063 1.425 0 2.403.001 3.162.063.74.06 1.139.172 1.427.318a3.5 3.5 0 0 1 1.53 1.53c.146.288.257.686.318 1.427.062.759.063 1.737.063 3.162 0 1.425-.001 2.403-.063 3.162-.06.74-.172 1.139-.319 1.427a3.5 3.5 0 0 1-1.529 1.53c-.288.146-.686.257-1.427.318-.759.062-1.737.063-3.162.063ZM7 8.511c-.444.009-.825.025-1.162.052-.74.06-1.139.172-1.427.318a3.5 3.5 0 0 0-1.53 1.53c-.146.288-.257.686-.318 1.427-.062.759-.063 1.737-.063 3.162 0 1.425.001 2.403.063 ***********.172 1.139.318 1.427a3.5 3.5 0 0 0 1.53 1.53c.288.146.686.257 1.427.318.759.062 1.737.063 3.162.063 1.425 0 2.403-.001 3.162-.063.74-.06 1.139-.172 1.427-.319a3.5 3.5 0 0 0 1.53-1.53c.146-.287.257-.685.318-1.426.027-.337.043-.718.052-1.162H15c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C7 13.2 7 11.8 7 9v-.489Z\"\n        clip-rule=\"evenodd\"\n      />\n    </svg>\n  `,\n\n  RETRY_ICON: svg`\n    <svg width=\"15\" height=\"16\" viewBox=\"0 0 15 16\">\n      <path\n        d=\"M6.464 2.03A.75.75 0 0 0 5.403.97L2.08 4.293a1 1 0 0 0 0 1.414L5.403 9.03a.75.75 0 0 0 1.06-1.06L4.672 6.177a.25.25 0 0 1 .177-.427h2.085a4 4 0 1 1-3.93 4.746c-.077-.407-.405-.746-.82-.746-.414 0-.755.338-.7.748a5.501 5.501 0 1 0 5.45-6.248H4.848a.25.25 0 0 1-.177-.427L6.464 2.03Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  DESKTOP_ICON: svg`\n    <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\">\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M0 5.98c0-1.85 0-2.775.394-3.466a3 3 0 0 1 1.12-1.12C2.204 1 3.13 1 4.98 1h6.04c1.85 0 2.775 0 3.466.394a3 3 0 0 1 1.12 1.12C16 3.204 16 4.13 16 5.98v1.04c0 1.85 0 2.775-.394 3.466a3 3 0 0 1-1.12 1.12C13.796 12 12.87 12 11.02 12H4.98c-1.85 0-2.775 0-3.466-.394a3 3 0 0 1-1.12-1.12C0 9.796 0 8.87 0 7.02V5.98ZM4.98 2.5h6.04c.953 0 1.568.001 2.034.043.446.04.608.108.69.154a1.5 1.5 0 0 1 .559.56c.**************.154.69.042.465.043 1.08.043 2.033v1.04c0 .952-.001 1.568-.043 2.034-.04.446-.108.608-.154.69a1.499 1.499 0 0 1-.56.559c-.08.046-.243.114-.69.154-.466.042-1.08.043-2.033.043H4.98c-.952 0-1.568-.001-2.034-.043-.446-.04-.608-.108-.69-.154a1.5 1.5 0 0 1-.559-.56c-.046-.08-.114-.243-.154-.69-.042-.465-.043-1.08-.043-2.033V5.98c0-.952.001-1.568.043-2.034.04-.446.108-.608.154-.69a1.5 1.5 0 0 1 .56-.559c.08-.046.243-.114.69-.154.465-.042 1.08-.043 2.033-.043Z\"\n        fill=\"#fff\"\n      />\n      <path\n        d=\"M4 14.25a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 0 1.5h-6.5a.75.75 0 0 1-.75-.75Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  MOBILE_ICON: svg`\n    <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\">\n      <path d=\"M6.75 5a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5Z\" fill=\"#fff\" />\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M3 4.98c0-1.85 0-2.775.394-3.466a3 3 0 0 1 1.12-1.12C5.204 0 6.136 0 8 0s2.795 0 3.486.394a3 3 0 0 1 1.12 1.12C13 2.204 13 3.13 13 4.98v6.04c0 1.85 0 2.775-.394 3.466a3 3 0 0 1-1.12 1.12C10.796 16 9.864 16 8 16s-2.795 0-3.486-.394a3 3 0 0 1-1.12-1.12C3 13.796 3 12.87 3 11.02V4.98Zm8.5 0v6.04c0 .953-.001 1.568-.043 2.034-.04.446-.108.608-.154.69a1.499 1.499 0 0 1-.56.559c-.08.045-.242.113-.693.154-.47.042-1.091.043-2.05.043-.959 0-1.58-.001-2.05-.043-.45-.04-.613-.109-.693-.154a1.5 1.5 0 0 1-.56-.56c-.046-.08-.114-.243-.154-.69-.042-.466-.043-1.08-.043-2.033V4.98c0-.952.001-1.568.043-2.034.04-.446.108-.608.154-.69a1.5 1.5 0 0 1 .56-.559c.08-.045.243-.113.693-.154C6.42 1.501 7.041 1.5 8 1.5c.959 0 1.58.001 ***********.04.613.109.693.154a1.5 1.5 0 0 1 .56.56c.**************.154.69.042.465.043 1.08.043 2.033Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  ARROW_DOWN_ICON: svg`\n    <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\">\n      <path\n        d=\"M2.28 7.47a.75.75 0 0 0-1.06 1.06l5.25 5.25a.75.75 0 0 0 1.06 0l5.25-5.25a.75.75 0 0 0-1.06-1.06l-3.544 3.543a.25.25 0 0 1-.426-.177V.75a.75.75 0 0 0-1.5 0v10.086a.25.25 0 0 1-.427.176L2.28 7.47Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  ARROW_UP_RIGHT_ICON: svg`\n    <svg width=\"15\" height=\"14\" fill=\"none\">\n      <path\n        d=\"M4.5 1.75A.75.75 0 0 1 5.25 1H12a1.5 1.5 0 0 1 1.5 1.5v6.75a.75.75 0 0 1-1.5 0V4.164a.25.25 0 0 0-.427-.176L4.061 11.5A.75.75 0 0 1 3 10.44l7.513-7.513a.25.25 0 0 0-.177-.427H5.25a.75.75 0 0 1-.75-.75Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  ARROW_RIGHT_ICON: svg`\n    <svg width=\"6\" height=\"14\" viewBox=\"0 0 6 14\">\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M2.181 1.099a.75.75 0 0 1 1.024.279l2.433 4.258a2.75 2.75 0 0 1 0 2.729l-2.433 4.257a.75.75 0 1 1-1.303-.744L4.335 7.62a1.25 1.25 0 0 0 0-1.24L1.902 2.122a.75.75 0 0 1 .28-1.023Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  QRCODE_ICON: svg`\n    <svg width=\"25\" height=\"24\" viewBox=\"0 0 25 24\">\n      <path\n        d=\"M23.748 9a.748.748 0 0 0 .748-.752c-.018-2.596-.128-4.07-.784-5.22a6 6 0 0 0-2.24-2.24c-1.15-.656-2.624-.766-5.22-.784a.748.748 0 0 0-.752.748c0 .414.335.749.748.752 1.015.007 1.82.028 2.494.088.995.09 1.561.256 1.988.5.7.398 1.28.978 1.679 1.678.243.427.41.993.498 1.988.061.675.082 1.479.09 2.493a.753.753 0 0 0 .75.749ZM3.527.788C4.677.132 6.152.022 8.747.004A.748.748 0 0 1 9.5.752a.753.753 0 0 1-.749.752c-1.014.007-1.818.028-2.493.088-.995.09-1.561.256-1.988.5-.7.398-1.28.978-1.679 1.678-.243.427-.41.993-.499 1.988-.06.675-.081 1.479-.088 2.493A.753.753 0 0 1 1.252 9a.748.748 0 0 1-.748-.752c.018-2.596.128-4.07.784-5.22a6 6 0 0 1 2.24-2.24ZM1.252 15a.748.748 0 0 0-.748.752c.018 2.596.128 4.07.784 5.22a6 6 0 0 0 2.24 2.24c1.15.656 2.624.766 5.22.784a.748.748 0 0 0 .752-.748.753.753 0 0 0-.749-.752c-1.014-.007-1.818-.028-2.493-.089-.995-.089-1.561-.255-1.988-.498a4.5 4.5 0 0 1-1.679-1.68c-.243-.426-.41-.992-.499-1.987-.06-.675-.081-1.479-.088-2.493A.753.753 0 0 0 1.252 15ZM22.996 15.749a.753.753 0 0 1 .752-.749c.415 0 .751.338.748.752-.018 2.596-.128 4.07-.784 5.22a6 6 0 0 1-2.24 2.24c-1.15.656-2.624.766-5.22.784a.748.748 0 0 1-.752-.748c0-.414.335-.749.748-.752 1.015-.007 1.82-.028 2.494-.089.995-.089 1.561-.255 1.988-.498a4.5 4.5 0 0 0 1.679-1.68c.243-.426.41-.992.498-1.987.061-.675.082-1.479.09-2.493Z\"\n        fill=\"#fff\"\n      />\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M7 4a2.5 2.5 0 0 0-2.5 2.5v2A2.5 2.5 0 0 0 7 11h2a2.5 2.5 0 0 0 2.5-2.5v-2A2.5 2.5 0 0 0 9 4H7Zm2 1.5H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1ZM13.5 6.5A2.5 2.5 0 0 1 16 4h2a2.5 2.5 0 0 1 2.5 2.5v2A2.5 2.5 0 0 1 18 11h-2a2.5 2.5 0 0 1-2.5-2.5v-2Zm2.5-1h2a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1ZM7 13a2.5 2.5 0 0 0-2.5 2.5v2A2.5 2.5 0 0 0 7 20h2a2.5 2.5 0 0 0 2.5-2.5v-2A2.5 2.5 0 0 0 9 13H7Zm2 1.5H7a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1Z\"\n        fill=\"#fff\"\n      />\n      <path\n        d=\"M13.5 15.5c0-.465 0-.697.038-.89a2 2 0 0 1 1.572-1.572C15.303 13 15.535 13 16 13v2.5h-2.5ZM18 13c.465 0 .697 0 .89.038a2 2 0 0 1 1.572 1.572c.038.193.038.425.038.89H18V13ZM18 17.5h2.5c0 .465 0 .697-.038.89a2 2 0 0 1-1.572 1.572C18.697 20 18.465 20 18 20v-2.5ZM13.5 17.5H16V20c-.465 0-.697 0-.89-.038a2 2 0 0 1-1.572-1.572c-.038-.193-.038-.425-.038-.89Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  SCAN_ICON: svg`\n    <svg width=\"16\" height=\"16\" fill=\"none\">\n      <path\n        fill=\"#fff\"\n        d=\"M10 15.216c0 .422.347.763.768.74 1.202-.064 2.025-.222 2.71-.613a5.001 5.001 0 0 0 1.865-1.866c.39-.684.549-1.507.613-2.709a.735.735 0 0 0-.74-.768.768.768 0 0 0-.76.732c-.009.157-.02.306-.032.447-.073.812-.206 1.244-.384 1.555-.31.545-.761.996-1.306 1.306-.311.178-.743.311-1.555.384-.141.013-.29.023-.447.032a.768.768 0 0 0-.732.76ZM10 .784c0 .407.325.737.732.76.157.009.306.02.447.032.812.073 1.244.206 1.555.384a3.5 3.5 0 0 1 1.306 1.306c.178.311.311.743.384 1.555.013.142.023.29.032.447a.768.768 0 0 0 .76.732.734.734 0 0 0 .74-.768c-.064-1.202-.222-2.025-.613-2.71A5 5 0 0 0 13.477.658c-.684-.39-1.507-.549-2.709-.613a.735.735 0 0 0-.768.74ZM5.232.044A.735.735 0 0 1 6 .784a.768.768 0 0 1-.732.76c-.157.009-.305.02-.447.032-.812.073-1.244.206-1.555.384A3.5 3.5 0 0 0 1.96 3.266c-.178.311-.311.743-.384 1.555-.013.142-.023.29-.032.447A.768.768 0 0 1 .784 6a.735.735 0 0 1-.74-.768c.064-1.202.222-2.025.613-2.71A5 5 0 0 1 2.523.658C3.207.267 4.03.108 5.233.044ZM5.268 14.456a.768.768 0 0 1 .732.76.734.734 0 0 1-.768.74c-1.202-.064-2.025-.222-2.71-.613a5 5 0 0 1-1.865-1.866c-.39-.684-.549-1.507-.613-2.709A.735.735 0 0 1 .784 10c.407 0 .737.325.76.732.009.157.02.306.032.447.073.812.206 1.244.384 1.555a3.5 3.5 0 0 0 1.306 1.306c.311.178.743.311 1.555.384.**************.447.032Z\"\n      />\n    </svg>\n  `,\n\n  CHECKMARK_ICON: svg`\n    <svg width=\"13\" height=\"12\" viewBox=\"0 0 13 12\">\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M12.155.132a.75.75 0 0 1 .232 1.035L5.821 11.535a1 1 0 0 1-1.626.09L.665 7.21a.75.75 0 1 1 1.17-.937L4.71 9.867a.25.25 0 0 0 .406-.023L11.12.364a.75.75 0 0 1 1.035-.232Z\"\n        fill=\"#fff\"\n      />\n    </svg>\n  `,\n\n  SEARCH_ICON: svg`\n    <svg width=\"20\" height=\"21\">\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M12.432 13.992c-.354-.353-.91-.382-1.35-.146a5.5 5.5 0 1 1 2.265-2.265c-.237.441-.208.997.145 1.35l3.296 3.296a.75.75 0 1 1-1.06 1.061l-3.296-3.296Zm.06-5a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z\"\n        fill=\"#949E9E\"\n      />\n    </svg>\n  `,\n\n  WALLET_PLACEHOLDER: svg`\n    <svg width=\"60\" height=\"60\" fill=\"none\" viewBox=\"0 0 60 60\">\n      <g clip-path=\"url(#q)\">\n        <path\n          id=\"wallet-placeholder-fill\"\n          fill=\"#fff\"\n          d=\"M0 24.9c0-9.251 0-13.877 1.97-17.332a15 15 0 0 1 5.598-5.597C11.023 0 15.648 0 24.9 0h10.2c9.252 0 13.877 0 17.332 1.97a15 15 0 0 1 5.597 5.598C60 11.023 60 15.648 60 24.9v10.2c0 9.252 0 13.877-1.97 17.332a15.001 15.001 0 0 1-5.598 5.597C48.977 60 44.352 60 35.1 60H24.9c-9.251 0-13.877 0-17.332-1.97a15 15 0 0 1-5.597-5.598C0 48.977 0 44.352 0 35.1V24.9Z\"\n        />\n        <path\n          id=\"wallet-placeholder-dash\"\n          stroke=\"#000\"\n          stroke-dasharray=\"4 4\"\n          stroke-width=\"1.5\"\n          d=\"M.04 41.708a231.598 231.598 0 0 1-.039-4.403l.75-.001L.75 35.1v-2.55H0v-5.1h.75V24.9l.001-2.204h-.75c.003-1.617.011-3.077.039-4.404l.75.016c.034-1.65.099-3.08.218-4.343l-.746-.07c.158-1.678.412-3.083.82-4.316l.713.236c.224-.679.497-1.296.827-1.875a14.25 14.25 0 0 1 1.05-1.585L3.076 5.9A15 15 0 0 1 5.9 3.076l.455.596a14.25 14.25 0 0 1 1.585-1.05c.579-.33 1.196-.603 1.875-.827l-.236-.712C10.812.674 12.217.42 13.895.262l.07.746C15.23.89 16.66.824 18.308.79l-.016-.75C19.62.012 21.08.004 22.695.001l.001.75L24.9.75h2.55V0h5.1v.75h2.55l2.204.001v-.75c1.617.003 3.077.011 4.404.039l-.016.75c1.65.034 3.08.099 4.343.218l.07-.746c1.678.158 3.083.412 4.316.82l-.236.713c.679.224 1.296.497 1.875.827a14.24 14.24 0 0 1 1.585 1.05l.455-.596A14.999 14.999 0 0 1 56.924 5.9l-.596.455c.384.502.735 1.032 1.05 1.585.33.579.602 1.196.827 1.875l.712-.236c.409 1.233.663 2.638.822 4.316l-.747.07c.119 1.264.184 2.694.218 4.343l.75-.016c.028 1.327.036 2.787.039 4.403l-.75.001.001 2.204v2.55H60v5.1h-.75v2.55l-.001 2.204h.75a231.431 231.431 0 0 1-.039 4.404l-.75-.016c-.034 1.65-.099 3.08-.218 4.343l.747.07c-.159 1.678-.413 3.083-.822 4.316l-.712-.236a10.255 10.255 0 0 1-.827 1.875 14.242 14.242 0 0 1-1.05 1.585l.596.455a14.997 14.997 0 0 1-2.824 2.824l-.455-.596c-.502.384-1.032.735-1.585 1.05-.579.33-1.196.602-1.875.827l.236.712c-1.233.409-2.638.663-4.316.822l-.07-.747c-1.264.119-2.694.184-4.343.218l.016.75c-1.327.028-2.787.036-4.403.039l-.001-.75-2.204.001h-2.55V60h-5.1v-.75H24.9l-2.204-.001v.75a231.431 231.431 0 0 1-4.404-.039l.016-.75c-1.65-.034-3.08-.099-4.343-.218l-.07.747c-1.678-.159-3.083-.413-4.316-.822l.236-.712a10.258 10.258 0 0 1-1.875-.827 14.252 14.252 0 0 1-1.585-1.05l-.455.596A14.999 14.999 0 0 1 3.076 54.1l.596-.455a14.24 14.24 0 0 1-1.05-1.585 10.259 10.259 0 0 1-.827-1.875l-.712.236C.674 49.188.42 47.783.262 46.105l.746-.07C.89 44.77.824 43.34.79 41.692l-.75.016Z\"\n        />\n        <path\n          fill=\"#fff\"\n          fill-rule=\"evenodd\"\n          d=\"M35.643 32.145c-.297-.743-.445-1.114-.401-1.275a.42.42 0 0 1 .182-.27c.134-.1.463-.1 1.123-.1.742 0 1.499.046 2.236-.05a6 6 0 0 0 5.166-5.166c.051-.39.051-.855.051-1.784 0-.928 0-1.393-.051-1.783a6 6 0 0 0-5.166-5.165c-.39-.052-.854-.052-1.783-.052h-7.72c-4.934 0-7.401 0-9.244 1.051a8 8 0 0 0-2.985 2.986C16.057 22.28 16.003 24.58 16 29 15.998 31.075 16 33.15 16 35.224A7.778 7.778 0 0 0 23.778 43H28.5c1.394 0 2.09 0 2.67-.116a6 6 0 0 0 4.715-4.714c.115-.58.115-1.301.115-2.744 0-1.31 0-1.964-.114-2.49a4.998 4.998 0 0 0-.243-.792Z\"\n          clip-rule=\"evenodd\"\n        />\n        <path\n          fill=\"#9EA9A9\"\n          fill-rule=\"evenodd\"\n          d=\"M37 18h-7.72c-2.494 0-4.266.002-5.647.126-1.361.122-2.197.354-2.854.728a6.5 6.5 0 0 0-2.425 2.426c-.375.657-.607 1.492-.729 2.853-.11 1.233-.123 2.777-.125 4.867 0 .7 0 1.05.097 ************.182.181.343.2.163.02.518-.18 1.229-.581a6.195 6.195 0 0 1 3.053-.8H37c.977 0 1.32-.003 1.587-.038a4.5 4.5 0 0 0 3.874-3.874c.036-.268.039-.611.039-1.588 0-.976-.003-1.319-.038-1.587a4.5 4.5 0 0 0-3.875-3.874C38.32 18.004 37.977 18 37 18Zm-7.364 12.5h-7.414a4.722 4.722 0 0 0-4.722 4.723 6.278 6.278 0 0 0 6.278 6.278H28.5c1.466 0 1.98-.008 2.378-.087a4.5 4.5 0 0 0 3.535-3.536c.08-.397.087-.933.087-2.451 0-1.391-.009-1.843-.08-2.17a3.5 3.5 0 0 0-2.676-2.676c-.328-.072-.762-.08-2.108-.08Z\"\n          clip-rule=\"evenodd\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"q\"><path fill=\"#fff\" d=\"M0 0h60v60H0z\" /></clipPath>\n      </defs>\n    </svg>\n  `,\n\n  GLOBE_ICON: svg`\n    <svg width=\"16\" height=\"16\" fill=\"none\" viewBox=\"0 0 16 16\">\n      <path\n        fill=\"#fff\"\n        fill-rule=\"evenodd\"\n        d=\"M15.5 8a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0Zm-2.113.75c.301 0 .535.264.47.558a6.01 6.01 0 0 1-2.867 3.896c-.203.116-.42-.103-.334-.32.409-1.018.691-2.274.797-3.657a.512.512 0 0 1 .507-.477h1.427Zm.47-2.058c.065.294-.169.558-.47.558H11.96a.512.512 0 0 1-.507-.477c-.106-1.383-.389-2.638-.797-3.656-.087-.217.13-.437.333-.32a6.01 6.01 0 0 1 2.868 3.895Zm-4.402.558c.286 0 .515-.24.49-.525-.121-1.361-.429-2.534-.83-3.393-.279-.6-.549-.93-.753-1.112a.535.535 0 0 0-.724 0c-.204.182-.474.513-.754 1.112-.4.859-.708 2.032-.828 3.393a.486.486 0 0 0 .49.525h2.909Zm-5.415 0c.267 0 .486-.21.507-.477.106-1.383.389-2.638.797-3.656.087-.217-.13-.437-.333-.32a6.01 6.01 0 0 0-2.868 3.895c-.065.294.169.558.47.558H4.04ZM2.143 9.308c-.065-.294.169-.558.47-.558H4.04c.267 0 .486.21.507.477.106 1.383.389 2.639.797 3.657.087.217-.13.436-.333.32a6.01 6.01 0 0 1-2.868-3.896Zm3.913-.033a.486.486 0 0 1 .49-.525h2.909c.286 0 .515.24.49.525-.121 1.361-.428 2.535-.83 3.394-.279.6-.549.93-.753 1.112a.535.535 0 0 1-.724 0c-.204-.182-.474-.513-.754-1.112-.4-.859-.708-2.033-.828-3.394Z\"\n        clip-rule=\"evenodd\"\n      />\n    </svg>\n  `\n}\n", "import { ModalCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-modal-backcard')\nexport class WcmModalBackcard extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <div class=\"wcm-toolbar-placeholder\"></div>\n      <div class=\"wcm-toolbar\">\n        ${SvgUtil.WALLET_CONNECT_LOGO}\n        <button @click=${ModalCtrl.close}>${SvgUtil.CROSS_ICON}</button>\n      </div>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-modal-backcard': WcmModalBackcard\n  }\n}\n", "import { html, LitElement } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-modal-content')\nexport class WcmModalContent extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <main>\n        <slot></slot>\n      </main>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-modal-content': WcmModalContent\n  }\n}\n", "import { html, LitElement } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-modal-footer')\nexport class WcmModalFooter extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <footer>\n        <slot></slot>\n      </footer>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-modal-footer': WcmModalFooter\n  }\n}\n", "import { RouterCtrl } from '@walletconnect/modal-core'\nimport type { TemplateResult } from 'lit'\nimport { html, LitElement } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { classMap } from 'lit/directives/class-map.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-modal-header')\nexport class WcmModalHeader extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property() public title = ''\n\n  @property() public onAction?: () => void = undefined\n\n  @property() public actionIcon?: TemplateResult<2> = undefined\n\n  @property({ type: Boolean }) public border = false\n\n  // -- private ------------------------------------------------------ //\n  private backBtnTemplate() {\n    return html`\n      <button class=\"wcm-back-btn\" @click=${RouterCtrl.goBack}>${SvgUtil.BACK_ICON}</button>\n    `\n  }\n\n  private actionBtnTemplate() {\n    return html`<button class=\"wcm-action-btn\" @click=${this.onAction}>${this.actionIcon}</button>`\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const classes = {\n      'wcm-border': this.border\n    }\n    const backBtn = RouterCtrl.state.history.length > 1\n\n    const content = this.title\n      ? html`<wcm-text variant=\"big-bold\">${this.title}</wcm-text>`\n      : html`<slot></slot>`\n\n    return html`\n      <header class=${classMap(classes)}>\n        ${backBtn ? this.backBtnTemplate() : null} ${content}\n        ${this.onAction ? this.actionBtnTemplate() : null}\n      </header>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-modal-header': WcmModalHeader\n  }\n}\n", "import type { WalletData } from '@walletconnect/modal-core'\nimport {\n  ConfigCtrl,\n  CoreUtil,\n  ExplorerCtrl,\n  OptionsCtrl,\n  RouterCtrl,\n  ToastCtrl\n} from '@walletconnect/modal-core'\nimport type { LitElement } from 'lit'\n\nexport const UiUtil = {\n  MOBILE_BREAKPOINT: 600,\n\n  WCM_RECENT_WALLET_DATA: 'WCM_RECENT_WALLET_DATA',\n\n  EXPLORER_WALLET_URL: 'https://explorer.walletconnect.com/?type=wallet',\n\n  getShadowRootElement(root: LitElement, selector: string) {\n    const el = root.renderRoot.querySelector(selector)\n    if (!el) {\n      throw new Error(`${selector} not found`)\n    }\n\n    return el as HTMLElement\n  },\n\n  getWalletIcon({ id, image_id }: { id: string; image_id?: string }) {\n    const { walletImages } = ConfigCtrl.state\n\n    if (walletImages?.[id]) {\n      return walletImages[id]\n    } else if (image_id) {\n      return ExplorerCtrl.getWalletImageUrl(image_id)\n    }\n\n    return ''\n  },\n\n  getWalletName(name: string, short = false) {\n    return short && name.length > 8 ? `${name.substring(0, 8)}..` : name\n  },\n\n  isMobileAnimation() {\n    return window.innerWidth <= UiUtil.MOBILE_BREAKPOINT\n  },\n\n  async preloadImage(src: string) {\n    const imagePromise = new Promise((resolve, reject) => {\n      const image = new Image()\n      image.onload = resolve\n      image.onerror = reject\n      image.crossOrigin = 'anonymous'\n      image.src = src\n    })\n\n    return Promise.race([imagePromise, CoreUtil.wait(3_000)])\n  },\n\n  getErrorMessage(err: unknown) {\n    return err instanceof Error ? err.message : 'Unknown Error'\n  },\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  debounce(func: (...args: any[]) => unknown, timeout = 500) {\n    let timer: NodeJS.Timeout | undefined = undefined\n\n    return (...args: unknown[]) => {\n      function next() {\n        func(...args)\n      }\n      if (timer) {\n        clearTimeout(timer)\n      }\n      timer = setTimeout(next, timeout)\n    }\n  },\n\n  handleMobileLinking(wallet: WalletData) {\n    const { walletConnectUri } = OptionsCtrl.state\n    const { mobile, name } = wallet\n    const nativeUrl = mobile?.native\n    const universalUrl = mobile?.universal\n\n    UiUtil.setRecentWallet(wallet)\n\n    function onRedirect(uri: string) {\n      let href = ''\n      if (nativeUrl) {\n        href = CoreUtil.formatUniversalUrl(nativeUrl, uri, name)\n      } else if (universalUrl) {\n        href = CoreUtil.formatNativeUrl(universalUrl, uri, name)\n      }\n      CoreUtil.openHref(href, '_self')\n    }\n\n    if (walletConnectUri) {\n      onRedirect(walletConnectUri)\n    }\n  },\n\n  handleAndroidLinking() {\n    const { walletConnectUri } = OptionsCtrl.state\n\n    if (walletConnectUri) {\n      CoreUtil.setWalletConnectAndroidDeepLink(walletConnectUri)\n      CoreUtil.openHref(walletConnectUri, '_self')\n    }\n  },\n\n  async handleUriCopy() {\n    const { walletConnectUri } = OptionsCtrl.state\n    if (walletConnectUri) {\n      try {\n        await navigator.clipboard.writeText(walletConnectUri)\n        ToastCtrl.openToast('Link copied', 'success')\n      } catch {\n        ToastCtrl.openToast('Failed to copy', 'error')\n      }\n    }\n  },\n\n  getCustomImageUrls() {\n    const { walletImages } = ConfigCtrl.state\n    const walletUrls = Object.values(walletImages ?? {})\n\n    return Object.values(walletUrls)\n  },\n\n  truncate(value: string, strLen = 8) {\n    if (value.length <= strLen) {\n      return value\n    }\n\n    return `${value.substring(0, 4)}...${value.substring(value.length - 4)}`\n  },\n\n  setRecentWallet(wallet: WalletData) {\n    try {\n      localStorage.setItem(UiUtil.WCM_RECENT_WALLET_DATA, JSON.stringify(wallet))\n    } catch {\n      console.info('Unable to set recent wallet')\n    }\n  },\n\n  getRecentWallet() {\n    try {\n      const wallet = localStorage.getItem(UiUtil.WCM_RECENT_WALLET_DATA)\n      if (wallet) {\n        const json = JSON.parse(wallet)\n\n        return json as WalletData\n      }\n\n      return undefined\n    } catch {\n      console.info('Unable to get recent wallet')\n    }\n\n    return undefined\n  },\n\n  caseSafeIncludes(str1: string, str2: string) {\n    return str1.toUpperCase().includes(str2.toUpperCase())\n  },\n\n  openWalletExplorerUrl() {\n    CoreUtil.openHref(UiUtil.EXPLORER_WALLET_URL, '_blank')\n  },\n\n  getCachedRouterWalletPlatforms() {\n    const { desktop, mobile } = CoreUtil.getWalletRouterData()\n    const isDesktop = Boolean(desktop?.native)\n    const isWeb = Boolean(desktop?.universal)\n    const isMobile = Boolean(mobile?.native) || Boolean(mobile?.universal)\n\n    return { isDesktop, isMobile, isWeb }\n  },\n\n  goToConnectingView(wallet: WalletData) {\n    RouterCtrl.setData({ Wallet: wallet })\n    const isMobileDevice = CoreUtil.isMobile()\n    const { isDesktop, isWeb, isMobile } = UiUtil.getCachedRouterWalletPlatforms()\n\n    // Mobile\n    if (isMobileDevice) {\n      if (isMobile) {\n        RouterCtrl.push('MobileConnecting')\n      } else if (isWeb) {\n        RouterCtrl.push('WebConnecting')\n      } else {\n        RouterCtrl.push('InstallWallet')\n      }\n    }\n\n    // Desktop\n    else if (isDesktop) {\n      RouterCtrl.push('DesktopConnecting')\n    } else if (isWeb) {\n      RouterCtrl.push('WebConnecting')\n    } else if (isMobile) {\n      RouterCtrl.push('MobileQrcodeConnecting')\n    } else {\n      RouterCtrl.push('InstallWallet')\n    }\n  }\n}\n", "import type { RouterView } from '@walletconnect/modal-core'\nimport { RouterCtrl } from '@walletconnect/modal-core'\nimport { html, LitElement } from 'lit'\nimport { customElement, state } from 'lit/decorators.js'\nimport { animate } from 'motion'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-modal-router')\nexport class WcmModalRouter extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @state() public view: RouterView = RouterCtrl.state.view\n\n  @state() public prevView: RouterView = RouterCtrl.state.view\n\n  // -- lifecycle ---------------------------------------------------- //\n  public constructor() {\n    super()\n    this.unsubscribe = RouterCtrl.subscribe(routerState => {\n      if (this.view !== routerState.view) {\n        this.onChangeRoute()\n      }\n    })\n  }\n\n  public firstUpdated() {\n    this.resizeObserver = new ResizeObserver(([conetnt]) => {\n      const newHeight = `${conetnt.contentRect.height}px`\n      if (this.oldHeight !== '0px') {\n        animate(this.routerEl, { height: [this.oldHeight, newHeight] }, { duration: 0.2 })\n      }\n      this.oldHeight = newHeight\n    })\n    this.resizeObserver.observe(this.contentEl)\n  }\n\n  public disconnectedCallback() {\n    this.unsubscribe?.()\n    this.resizeObserver?.disconnect()\n  }\n\n  // -- private ------------------------------------------------------ //\n  private readonly unsubscribe?: () => void = undefined\n\n  private oldHeight = '0px'\n\n  private resizeObserver?: ResizeObserver = undefined\n\n  private get routerEl() {\n    return UiUtil.getShadowRootElement(this, '.wcm-router')\n  }\n\n  private get contentEl() {\n    return UiUtil.getShadowRootElement(this, '.wcm-content')\n  }\n\n  private viewTemplate() {\n    switch (this.view) {\n      case 'ConnectWallet':\n        return html`<wcm-connect-wallet-view></wcm-connect-wallet-view>`\n      case 'DesktopConnecting':\n        return html`<wcm-desktop-connecting-view></wcm-desktop-connecting-view>`\n      case 'MobileConnecting':\n        return html`<wcm-mobile-connecting-view></wcm-mobile-connecting-view>`\n      case 'WebConnecting':\n        return html`<wcm-web-connecting-view></wcm-web-connecting-view>`\n      case 'MobileQrcodeConnecting':\n        return html`<wcm-mobile-qr-connecting-view></wcm-mobile-qr-connecting-view>`\n      case 'WalletExplorer':\n        return html`<wcm-wallet-explorer-view></wcm-wallet-explorer-view>`\n      case 'Qrcode':\n        return html`<wcm-qrcode-view></wcm-qrcode-view>`\n      case 'InstallWallet':\n        return html`<wcm-install-wallet-view></wcm-install-wallet-view>`\n      default:\n        return html`<div>Not Found</div>`\n    }\n  }\n\n  private async onChangeRoute() {\n    await animate(\n      this.routerEl,\n      { opacity: [1, 0], scale: [1, 1.02] },\n      { duration: 0.15, delay: 0.1 }\n    ).finished\n    this.view = RouterCtrl.state.view\n    animate(this.routerEl, { opacity: [0, 1], scale: [0.99, 1] }, { duration: 0.37, delay: 0.05 })\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <div class=\"wcm-router\">\n        <div class=\"wcm-content\">${this.viewTemplate()}</div>\n      </div>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-modal-router': WcmModalRouter\n  }\n}\n", "import { ToastCtrl } from '@walletconnect/modal-core'\nimport { html, LitElement } from 'lit'\nimport { customElement, state } from 'lit/decorators.js'\nimport { classMap } from 'lit/directives/class-map.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-modal-toast')\nexport class WcmModalToast extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @state() public open = false\n\n  public constructor() {\n    super()\n    this.unsubscribe = ToastCtrl.subscribe(newState => {\n      if (newState.open) {\n        this.open = true\n        this.timeout = setTimeout(() => ToastCtrl.closeToast(), 2200)\n      } else {\n        this.open = false\n        clearTimeout(this.timeout)\n      }\n    })\n  }\n\n  public disconnectedCallback() {\n    this.unsubscribe?.()\n    clearTimeout(this.timeout)\n    ToastCtrl.closeToast()\n  }\n\n  // -- private ------------------------------------------------------ //\n  private readonly unsubscribe?: () => void = undefined\n\n  private timeout?: NodeJS.Timeout = undefined\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { message, variant } = ToastCtrl.state\n    const classes = {\n      'wcm-success': variant === 'success',\n      'wcm-error': variant === 'error'\n    }\n\n    return this.open\n      ? html`\n          <div class=${classMap(classes)}>\n            ${variant === 'success' ? SvgUtil.CHECKMARK_ICON : null}\n            ${variant === 'error' ? SvgUtil.CROSS_ICON : null}\n            <wcm-text variant=\"small-regular\">${message}</wcm-text>\n          </div>\n        `\n      : null\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-modal-toast': WcmModalToast\n  }\n}\n", "import type { TemplateR<PERSON>ult } from 'lit'\nimport { svg } from 'lit'\nimport QRCodeUtil from 'qrcode'\n\ntype CoordinateMapping = [number, number[]]\n\nconst CONNECTING_ERROR_MARGIN = 0.1\nconst CIRCLE_SIZE_MODIFIER = 2.5\nconst QRCODE_MATRIX_MARGIN = 7\n\nfunction isAdjecentDots(cy: number, otherCy: number, cellSize: number) {\n  if (cy === otherCy) {\n    return false\n  }\n  const diff = cy - otherCy < 0 ? otherCy - cy : cy - otherCy\n\n  return diff <= cellSize + CONNECTING_ERROR_MARGIN\n}\n\nfunction getMatrix(value: string, errorCorrectionLevel: QRCodeUtil.QRCodeErrorCorrectionLevel) {\n  const arr = Array.prototype.slice.call(\n    QRCodeUtil.create(value, { errorCorrectionLevel }).modules.data,\n    0\n  )\n  const sqrt = Math.sqrt(arr.length)\n\n  return arr.reduce(\n    (rows, key, index) =>\n      (index % sqrt === 0 ? rows.push([key]) : rows[rows.length - 1].push(key)) && rows,\n    []\n  )\n}\n\nexport const QrCodeUtil = {\n  generate(uri: string, size: number, logoSize: number) {\n    const dotColor = '#141414'\n    const edgeColor = '#ffffff'\n    const dots: TemplateResult[] = []\n    const matrix = getMatrix(uri, 'Q')\n    const cellSize = size / matrix.length\n    const qrList = [\n      { x: 0, y: 0 },\n      { x: 1, y: 0 },\n      { x: 0, y: 1 }\n    ]\n\n    qrList.forEach(({ x, y }) => {\n      const x1 = (matrix.length - QRCODE_MATRIX_MARGIN) * cellSize * x\n      const y1 = (matrix.length - QRCODE_MATRIX_MARGIN) * cellSize * y\n      const borderRadius = 0.45\n      for (let i = 0; i < qrList.length; i += 1) {\n        const dotSize = cellSize * (QRCODE_MATRIX_MARGIN - i * 2)\n        dots.push(\n          svg`\n            <rect\n              fill=${i % 2 === 0 ? dotColor : edgeColor}\n              height=${dotSize}\n              rx=${dotSize * borderRadius}\n              ry=${dotSize * borderRadius}\n              width=${dotSize}\n              x=${x1 + cellSize * i}\n              y=${y1 + cellSize * i}\n            />\n          `\n        )\n      }\n    })\n\n    const clearArenaSize = Math.floor((logoSize + 25) / cellSize)\n    const matrixMiddleStart = matrix.length / 2 - clearArenaSize / 2\n    const matrixMiddleEnd = matrix.length / 2 + clearArenaSize / 2 - 1\n    const circles: [number, number][] = []\n\n    // Getting coordinates for each of the QR code dots\n    matrix.forEach((row: QRCodeUtil.QRCode[], i: number) => {\n      row.forEach((_, j: number) => {\n        if (matrix[i][j]) {\n          if (\n            !(\n              (i < QRCODE_MATRIX_MARGIN && j < QRCODE_MATRIX_MARGIN) ||\n              (i > matrix.length - (QRCODE_MATRIX_MARGIN + 1) && j < QRCODE_MATRIX_MARGIN) ||\n              (i < QRCODE_MATRIX_MARGIN && j > matrix.length - (QRCODE_MATRIX_MARGIN + 1))\n            )\n          ) {\n            if (\n              !(\n                i > matrixMiddleStart &&\n                i < matrixMiddleEnd &&\n                j > matrixMiddleStart &&\n                j < matrixMiddleEnd\n              )\n            ) {\n              const cx = i * cellSize + cellSize / 2\n              const cy = j * cellSize + cellSize / 2\n              circles.push([cx, cy])\n            }\n          }\n        }\n      })\n    })\n\n    // Cx to multiple cys\n    const circlesToConnect: Record<number, number[]> = {}\n\n    // Mapping all dots cicles on the same x axis\n    circles.forEach(([cx, cy]) => {\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (circlesToConnect[cx]) {\n        circlesToConnect[cx].push(cy)\n      } else {\n        circlesToConnect[cx] = [cy]\n      }\n    })\n\n    // Drawing lonely dots\n    Object.entries(circlesToConnect)\n      // Only get dots that have neighbors\n      .map(([cx, cys]) => {\n        const newCys = cys.filter(cy =>\n          cys.every(otherCy => !isAdjecentDots(cy, otherCy, cellSize))\n        )\n\n        return [Number(cx), newCys] as CoordinateMapping\n      })\n      .forEach(([cx, cys]) => {\n        cys.forEach(cy => {\n          dots.push(\n            svg`<circle cx=${cx} cy=${cy} fill=${dotColor} r=${cellSize / CIRCLE_SIZE_MODIFIER} />`\n          )\n        })\n      })\n\n    // Drawing lines for dots that are close to each other\n    Object.entries(circlesToConnect)\n      // Only get dots that have more than one dot on the x axis\n      .filter(([_, cys]) => cys.length > 1)\n      // Removing dots with no neighbors\n      .map(([cx, cys]) => {\n        const newCys = cys.filter(cy => cys.some(otherCy => isAdjecentDots(cy, otherCy, cellSize)))\n\n        return [Number(cx), newCys] as CoordinateMapping\n      })\n      // Get the coordinates of the first and last dot of a line\n      .map(([cx, cys]) => {\n        cys.sort((a, b) => (a < b ? -1 : 1))\n        const groups: number[][] = []\n\n        for (const cy of cys) {\n          const group = groups.find(item =>\n            item.some(otherCy => isAdjecentDots(cy, otherCy, cellSize))\n          )\n          if (group) {\n            group.push(cy)\n          } else {\n            groups.push([cy])\n          }\n        }\n\n        return [cx, groups.map(item => [item[0], item[item.length - 1]])] as [number, number[][]]\n      })\n      .forEach(([cx, groups]) => {\n        groups.forEach(([y1, y2]) => {\n          dots.push(\n            svg`\n              <line\n                x1=${cx}\n                x2=${cx}\n                y1=${y1}\n                y2=${y2}\n                stroke=${dotColor}\n                stroke-width=${cellSize / (CIRCLE_SIZE_MODIFIER / 2)}\n                stroke-linecap=\"round\"\n              />\n            `\n          )\n        })\n      })\n\n    return dots\n  }\n}\n", "import { ThemeCtrl } from '@walletconnect/modal-core'\nimport { html, LitElement, svg } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { classMap } from 'lit/directives/class-map.js'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { QrCodeUtil } from '../../utils/QrCode'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-qrcode')\nexport class WcmQrCode extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property() public uri = ''\n\n  @property({ type: Number }) public size = 0\n\n  @property() public imageId?: string = undefined\n\n  @property() public walletId?: string = undefined\n\n  @property() public imageUrl?: string = undefined\n\n  // -- private ------------------------------------------------------ //\n  private svgTemplate() {\n    const isLightMode = ThemeCtrl.state.themeMode === 'light'\n    const size = isLightMode ? this.size : this.size - 18 * 2\n\n    return svg`\n      <svg height=${size} width=${size}>\n        ${QrCodeUtil.generate(this.uri, size, size / 4)}\n      </svg>\n    `\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const classes = {\n      'wcm-dark': ThemeCtrl.state.themeMode === 'dark'\n    }\n\n    return html`\n      <div style=${`width: ${this.size}px`} class=${classMap(classes)}>\n        ${this.walletId || this.imageUrl\n          ? html`\n              <wcm-wallet-image\n                walletId=${ifDefined(this.walletId)}\n                imageId=${ifDefined(this.imageId)}\n                imageUrl=${ifDefined(this.imageUrl)}\n              ></wcm-wallet-image>\n            `\n          : SvgUtil.WALLET_CONNECT_ICON_COLORED}\n        ${this.svgTemplate()}\n      </div>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-qrcode': WcmQrCode\n  }\n}\n", "import { html, LitElement } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-search-input')\nexport class WcmSearchInput extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  @property() public onChange = () => null\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <input type=\"text\" @input=${this.onChange} placeholder=\"Search wallets\" />\n      ${SvgUtil.SEARCH_ICON}\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-search-input': WcmSearchInput\n  }\n}\n", "import { html, LitElement } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-spinner')\nexport class WcmSpinner extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <svg viewBox=\"0 0 50 50\" width=\"24\" height=\"24\">\n        <circle cx=\"25\" cy=\"25\" r=\"20\" fill=\"none\" stroke-width=\"4\" stroke=\"#fff\" />\n      </svg>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-spinner': WcmSpinner\n  }\n}\n", "import { html, LitElement } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { classMap } from 'lit/directives/class-map.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\ntype Variant =\n  | 'big-bold'\n  | 'medium-regular'\n  | 'small-regular'\n  | 'small-thin'\n  | 'xsmall-bold'\n  | 'xsmall-regular'\n\ntype Color = 'accent' | 'error' | 'inverse' | 'primary' | 'secondary' | 'tertiary'\n\n@customElement('wcm-text')\nexport class WcmText extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property() public variant?: Variant = 'medium-regular'\n\n  @property() public color?: Color | string = 'primary'\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const classes = {\n      'wcm-big-bold': this.variant === 'big-bold',\n      'wcm-medium-regular': this.variant === 'medium-regular',\n      'wcm-small-regular': this.variant === 'small-regular',\n      'wcm-small-thin': this.variant === 'small-thin',\n      'wcm-xsmall-regular': this.variant === 'xsmall-regular',\n      'wcm-xsmall-bold': this.variant === 'xsmall-bold',\n      'wcm-color-primary': this.color === 'primary',\n      'wcm-color-secondary': this.color === 'secondary',\n      'wcm-color-tertiary': this.color === 'tertiary',\n      'wcm-color-inverse': this.color === 'inverse',\n      'wcm-color-accnt': this.color === 'accent',\n      'wcm-color-error': this.color === 'error'\n    }\n\n    return html`\n      <span>\n        <slot class=${classMap(classes)}></slot>\n      </span>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-text': WcmText\n  }\n}\n", "import { EventsCtrl } from '@walletconnect/modal-core'\nimport { html, LitElement } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-wallet-button')\nexport class WcmWalletButton extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property() public onClick: () => void = () => null\n\n  @property() public name = ''\n\n  @property() public walletId = ''\n\n  @property() public label?: string = undefined\n\n  @property() public imageId?: string = undefined\n\n  @property({ type: Boolean }) public installed? = false\n\n  @property({ type: Boolean }) public recent? = false\n\n  // -- private ------------------------------------------------------ //\n  private sublabelTemplate() {\n    if (this.recent) {\n      return html`\n        <wcm-text class=\"wcm-sublabel\" variant=\"xsmall-bold\" color=\"tertiary\">RECENT</wcm-text>\n      `\n    } else if (this.installed) {\n      return html`\n        <wcm-text class=\"wcm-sublabel\" variant=\"xsmall-bold\" color=\"tertiary\">INSTALLED</wcm-text>\n      `\n    }\n\n    return null\n  }\n\n  private handleClick() {\n    EventsCtrl.click({ name: 'WALLET_BUTTON', walletId: this.walletId })\n    this.onClick()\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <button @click=${this.handleClick.bind(this)}>\n        <div>\n          <wcm-wallet-image\n            walletId=${this.walletId}\n            imageId=${ifDefined(this.imageId)}\n          ></wcm-wallet-image>\n          <wcm-text variant=\"xsmall-regular\">\n            ${this.label ?? UiUtil.getWalletName(this.name, true)}\n          </wcm-text>\n\n          ${this.sublabelTemplate()}\n        </div>\n      </button>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-wallet-button': WcmWalletButton\n  }\n}\n", "import { html, LitElement } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-wallet-image')\nexport class WcmWalletImage extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property() public walletId = ''\n\n  @property() public imageId?: string = undefined\n\n  @property() public imageUrl?: string = undefined\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const src = this.imageUrl?.length\n      ? this.imageUrl\n      : UiUtil.getWalletIcon({ id: this.walletId, image_id: this.imageId })\n\n    return html`\n      ${src.length\n        ? html`\n            <div>\n              <img crossorigin=\"anonymous\" src=${src} alt=${this.id} />\n            </div>\n          `\n        : SvgUtil.WALLET_PLACEHOLDER}\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-wallet-image': WcmWalletImage\n  }\n}\n", "import { ConfigCtrl, ExplorerCtrl, OptionsCtrl, ToastCtrl } from '@walletconnect/modal-core'\nimport { LitElement } from 'lit'\nimport { customElement, state } from 'lit/decorators.js'\nimport { UiUtil } from '../utils/UiUtil'\n\n@customElement('wcm-explorer-context')\nexport class WcmExplorerContext extends LitElement {\n  // -- state & properties ------------------------------------------- //\n  @state() private preload = true\n\n  // -- lifecycle ---------------------------------------------------- //\n  public constructor() {\n    super()\n\n    // Load explorer and asset data\n    this.preloadData()\n  }\n\n  // -- private ------------------------------------------------------ //\n  private async loadImages(images?: string[]) {\n    try {\n      if (images?.length) {\n        await Promise.all(images.map(async url => UiUtil.preloadImage(url)))\n      }\n    } catch {\n      console.info('Unsuccessful attempt at preloading some images', images)\n    }\n  }\n\n  private async preloadListings() {\n    if (ConfigCtrl.state.enableExplorer) {\n      await ExplorerCtrl.getRecomendedWallets()\n      OptionsCtrl.setIsDataLoaded(true)\n      const { recomendedWallets } = ExplorerCtrl.state\n      const walletImgs = recomendedWallets.map(wallet => UiUtil.getWalletIcon(wallet))\n      await this.loadImages(walletImgs)\n    } else {\n      OptionsCtrl.setIsDataLoaded(true)\n    }\n  }\n\n  private async preloadCustomImages() {\n    const images = UiUtil.getCustomImageUrls()\n    await this.loadImages(images)\n  }\n\n  private async preloadData() {\n    try {\n      if (this.preload) {\n        this.preload = false\n        await Promise.all([this.preloadListings(), this.preloadCustomImages()])\n      }\n    } catch (err) {\n      console.error(err)\n      ToastCtrl.openToast('Failed preloading', 'error')\n    }\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-explorer-context': WcmExplorerContext\n  }\n}\n", "import { ThemeCtrl } from '@walletconnect/modal-core'\nimport { LitElement } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { ThemeUtil } from '../utils/ThemeUtil'\n\n@customElement('wcm-theme-context')\nexport class WcmThemeContext extends LitElement {\n  // -- lifecycle ---------------------------------------------------- //\n  public constructor() {\n    super()\n\n    // Set & Subscribe to theme state\n    ThemeUtil.setTheme()\n    this.unsubscribeTheme = ThemeCtrl.subscribe(ThemeUtil.setTheme)\n  }\n\n  public disconnectedCallback() {\n    this.unsubscribeTheme?.()\n  }\n\n  // -- private ------------------------------------------------------ //\n  private readonly unsubscribeTheme?: () => void = undefined\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-theme-context': WcmThemeContext\n  }\n}\n", "import { CoreUtil, ExplorerCtrl, RouterCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-android-wallet-selection')\nexport class WcmAndroidWalletSelection extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- private ------------------------------------------------------ //\n  private onGoToQrcode() {\n    RouterCtrl.push('Qrcode')\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { recomendedWallets } = ExplorerCtrl.state\n    const wallets = [...recomendedWallets, ...recomendedWallets]\n    const recomendedCount = CoreUtil.RECOMMENDED_WALLET_AMOUNT * 2\n\n    return html`\n      <wcm-modal-header\n        title=\"Connect your wallet\"\n        .onAction=${this.onGoToQrcode}\n        .actionIcon=${SvgUtil.QRCODE_ICON}\n      ></wcm-modal-header>\n\n      <wcm-modal-content>\n        <div class=\"wcm-title\">\n          ${SvgUtil.MOBILE_ICON}\n          <wcm-text variant=\"small-regular\" color=\"accent\">WalletConnect</wcm-text>\n        </div>\n\n        <div class=\"wcm-slider\">\n          <div class=\"wcm-track\">\n            ${[...Array(recomendedCount)].map((_, index) => {\n              const wallet = wallets[index % wallets.length]\n\n              return wallet\n                ? html`<wcm-wallet-image\n                    walletId=${wallet.id}\n                    imageId=${wallet.image_id}\n                  ></wcm-wallet-image>`\n                : SvgUtil.WALLET_PLACEHOLDER\n            })}\n          </div>\n          <wcm-button-big @click=${UiUtil.handleAndroidLinking}>\n            <wcm-text variant=\"medium-regular\" color=\"inverse\">Select Wallet</wcm-text>\n          </wcm-button-big>\n        </div>\n      </wcm-modal-content>\n\n      <wcm-info-footer>\n        <wcm-text color=\"secondary\" variant=\"small-thin\">\n          Choose WalletConnect to see supported apps on your device\n        </wcm-text>\n      </wcm-info-footer>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-android-wallet-selection': WcmAndroidWalletSelection\n  }\n}\n", "import { ThemeCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { classMap } from 'lit/directives/class-map.js'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-connector-waiting')\nexport class WcmConnectorWaiting extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property() public walletId?: string = undefined\n\n  @property() public imageId?: string = undefined\n\n  @property({ type: Boolean }) public isError = false\n\n  @property({ type: Boolean }) public isStale = false\n\n  @property() public label = ''\n\n  // -- private ------------------------------------------------------ //\n  private svgLoaderTemplate() {\n    const ICON_SIZE = 88\n    const DH_ARRAY = 317\n    const DH_OFFSET = 425\n\n    const radius =\n      ThemeCtrl.state.themeVariables?.['--wcm-wallet-icon-large-border-radius'] ??\n      ThemeUtil.getPreset('--wcm-wallet-icon-large-border-radius')\n    let numRadius = 0\n\n    if (radius.includes('%')) {\n      numRadius = (ICON_SIZE / 100) * parseInt(radius, 10)\n    } else {\n      numRadius = parseInt(radius, 10)\n    }\n\n    numRadius *= 1.17\n    const dashArray = DH_ARRAY - numRadius * 1.57\n    const dashOffset = DH_OFFSET - numRadius * 1.8\n\n    return html`\n      <svg viewBox=\"0 0 110 110\" width=\"110\" height=\"110\">\n        <rect id=\"wcm-loader\" x=\"2\" y=\"2\" width=\"106\" height=\"106\" rx=${numRadius} />\n        <use\n          xlink:href=\"#wcm-loader\"\n          stroke-dasharray=\"106 ${dashArray}\"\n          stroke-dashoffset=${dashOffset}\n        ></use>\n      </svg>\n    `\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const classes = {\n      'wcm-error': this.isError,\n      'wcm-stale': this.isStale\n    }\n\n    return html`\n      <div class=${classMap(classes)}>\n        ${this.svgLoaderTemplate()}\n        <wcm-wallet-image\n          walletId=${ifDefined(this.walletId)}\n          imageId=${ifDefined(this.imageId)}\n        ></wcm-wallet-image>\n      </div>\n      <wcm-text variant=\"medium-regular\" color=${this.isError ? 'error' : 'primary'}>\n        ${this.isError ? 'Connection declined' : this.label}\n      </wcm-text>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-connector-waiting': WcmConnectorWaiting\n  }\n}\n", "import { ConfigCtrl, CoreUtil, ExplorerCtrl } from '@walletconnect/modal-core'\nimport { UiUtil } from './UiUtil'\n\nexport const DataUtil = {\n  manualWallets() {\n    const { mobileWallets, desktopWallets } = ConfigCtrl.state\n    const recentWalletId = DataUtil.recentWallet()?.id\n    const platformWallets = CoreUtil.isMobile() ? mobileWallets : desktopWallets\n    const wallets = platformWallets?.filter(wallet => recentWalletId !== wallet.id)\n\n    return (\n      (CoreUtil.isMobile()\n        ? wallets?.map(({ id, name, links }) => ({ id, name, mobile: links, links }))\n        : wallets?.map(({ id, name, links }) => ({ id, name, desktop: links, links }))) ?? []\n    )\n  },\n\n  recentWallet() {\n    return UiUtil.getRecentWallet()\n  },\n\n  recomendedWallets(skipRecent = false) {\n    const recentWalletId = skipRecent ? undefined : DataUtil.recentWallet()?.id\n    const { recomendedWallets } = ExplorerCtrl.state\n    const wallets = recomendedWallets.filter(wallet => recentWalletId !== wallet.id)\n\n    return wallets\n  }\n}\n", "import type { WalletData } from '@walletconnect/modal-core'\nimport { html } from 'lit'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { DataUtil } from './DataUtil'\nimport { UiUtil } from './UiUtil'\n\nexport const TemplateUtil = {\n  onConnecting(data: WalletData) {\n    UiUtil.goToConnectingView(data)\n  },\n\n  manualWalletsTemplate() {\n    const wallets = DataUtil.manualWallets()\n\n    return wallets.map(\n      wallet => html`\n        <wcm-wallet-button\n          walletId=${wallet.id}\n          name=${wallet.name}\n          .onClick=${() => this.onConnecting(wallet)}\n        ></wcm-wallet-button>\n      `\n    )\n  },\n\n  recomendedWalletsTemplate(skipRecent = false) {\n    const wallets = DataUtil.recomendedWallets(skipRecent)\n\n    return wallets.map(\n      wallet => html`\n        <wcm-wallet-button\n          name=${wallet.name}\n          walletId=${wallet.id}\n          imageId=${wallet.image_id}\n          .onClick=${() => this.onConnecting(wallet)}\n        ></wcm-wallet-button>\n      `\n    )\n  },\n\n  recentWalletTemplate() {\n    const wallet = DataUtil.recentWallet()\n\n    if (!wallet) {\n      return undefined\n    }\n\n    return html`\n      <wcm-wallet-button\n        name=${wallet.name}\n        walletId=${wallet.id}\n        imageId=${ifDefined(wallet.image_id)}\n        .recent=${true}\n        .onClick=${() => this.onConnecting(wallet)}\n      ></wcm-wallet-button>\n    `\n  }\n}\n", "import { ConfigCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { TemplateUtil } from '../../utils/TemplateUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-desktop-wallet-selection')\nexport class WcmDesktopWalletSelection extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { explorerExcludedWalletIds, enableExplorer } = ConfigCtrl.state\n    const isExplorerWallets = explorerExcludedWalletIds !== 'ALL' && enableExplorer\n    const manualTemplate = TemplateUtil.manualWalletsTemplate()\n    const recomendedTemplate = TemplateUtil.recomendedWalletsTemplate()\n    const recentTemplate = TemplateUtil.recentWalletTemplate()\n    let templates = [recentTemplate, ...manualTemplate, ...recomendedTemplate]\n    templates = templates.filter(Boolean)\n\n    const isViewAll = templates.length > 4 || isExplorerWallets\n    let wallets = []\n    if (isViewAll) {\n      wallets = templates.slice(0, 3)\n    } else {\n      wallets = templates\n    }\n    const isWallets = Boolean(wallets.length)\n\n    return html`\n      <wcm-modal-header\n        .border=${true}\n        title=\"Connect your wallet\"\n        .onAction=${UiUtil.handleUriCopy}\n        .actionIcon=${SvgUtil.COPY_ICON}\n      ></wcm-modal-header>\n\n      <wcm-modal-content>\n        <div class=\"wcm-mobile-title\">\n          <div class=\"wcm-subtitle\">\n            ${SvgUtil.MOBILE_ICON}\n            <wcm-text variant=\"small-regular\" color=\"accent\">Mobile</wcm-text>\n          </div>\n\n          <div class=\"wcm-subtitle\">\n            ${SvgUtil.SCAN_ICON}\n            <wcm-text variant=\"small-regular\" color=\"secondary\">Scan with your wallet</wcm-text>\n          </div>\n        </div>\n        <wcm-walletconnect-qr></wcm-walletconnect-qr>\n      </wcm-modal-content>\n\n      ${isWallets\n        ? html`\n            <wcm-modal-footer>\n              <div class=\"wcm-desktop-title\">\n                ${SvgUtil.DESKTOP_ICON}\n                <wcm-text variant=\"small-regular\" color=\"accent\">Desktop</wcm-text>\n              </div>\n\n              <div class=\"wcm-grid\">\n                ${wallets}\n                ${isViewAll\n                  ? html`<wcm-view-all-wallets-button></wcm-view-all-wallets-button>`\n                  : null}\n              </div>\n            </wcm-modal-footer>\n          `\n        : null}\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-desktop-wallet-selection': WcmDesktopWalletSelection\n  }\n}\n", "import { ConfigCtrl } from '@walletconnect/modal-core'\nimport { html, LitElement } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-legal-notice')\nexport class WcmLegalNotice extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { termsOfServiceUrl, privacyPolicyUrl } = ConfigCtrl.state\n    const isLegal = termsOfServiceUrl ?? privacyPolicyUrl\n\n    if (!isLegal) {\n      return null\n    }\n\n    return html`\n      <div>\n        <wcm-text variant=\"small-regular\" color=\"secondary\">\n          By connecting your wallet to this app, you agree to the app's\n          ${termsOfServiceUrl\n            ? html`<a href=${termsOfServiceUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                Terms of Service\n              </a>`\n            : null}\n          ${termsOfServiceUrl && privacyPolicyUrl ? 'and' : null}\n          ${privacyPolicyUrl\n            ? html`<a href=${privacyPolicyUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                Privacy Policy\n              </a>`\n            : null}\n        </wcm-text>\n      </div>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-legal-notice': WcmLegalNotice\n  }\n}\n", "import { ConfigCtrl, RouterCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { TemplateUtil } from '../../utils/TemplateUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-mobile-wallet-selection')\nexport class WcmMobileWalletSelection extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- private ------------------------------------------------------ //\n  private onQrcode() {\n    RouterCtrl.push('Qrcode')\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { explorerExcludedWalletIds, enableExplorer } = ConfigCtrl.state\n    const isExplorerWallets = explorerExcludedWalletIds !== 'ALL' && enableExplorer\n    const manualTemplate = TemplateUtil.manualWalletsTemplate()\n    const recomendedTemplate = TemplateUtil.recomendedWalletsTemplate()\n    const recentTemplate = TemplateUtil.recentWalletTemplate()\n    let templates = [recentTemplate, ...manualTemplate, ...recomendedTemplate]\n    templates = templates.filter(Boolean)\n\n    const isViewAll = templates.length > 8 || isExplorerWallets\n    let wallets = []\n    if (isViewAll) {\n      wallets = templates.slice(0, 7)\n    } else {\n      wallets = templates\n    }\n\n    const isWallets = Boolean(wallets.length)\n\n    return html`\n      <wcm-modal-header\n        title=\"Connect your wallet\"\n        .onAction=${this.onQrcode}\n        .actionIcon=${SvgUtil.QRCODE_ICON}\n      ></wcm-modal-header>\n\n      ${isWallets\n        ? html`\n            <wcm-modal-content>\n              <div>\n                ${wallets}\n                ${isViewAll\n                  ? html`<wcm-view-all-wallets-button></wcm-view-all-wallets-button>`\n                  : null}\n              </div>\n            </wcm-modal-content>\n          `\n        : null}\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-mobile-wallet-selection': WcmMobileWalletSelection\n  }\n}\n", "import { ModalCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement, state } from 'lit/decorators.js'\nimport { classMap } from 'lit/directives/class-map.js'\nimport { animate } from 'motion'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\ntype Target = HTMLElement | undefined\n\n@customElement('wcm-modal')\nexport class WcmModal extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @state() private open = false\n\n  @state() private active = false\n\n  // -- lifecycle ---------------------------------------------------- //\n  public constructor() {\n    super()\n\n    // Subscribe to modal state\n    this.unsubscribeModal = ModalCtrl.subscribe(modalState => {\n      if (modalState.open) {\n        this.onOpenModalEvent()\n      } else {\n        this.onCloseModalEvent()\n      }\n    })\n  }\n\n  public disconnectedCallback() {\n    this.unsubscribeModal?.()\n  }\n\n  // -- private ------------------------------------------------------ //\n  private readonly unsubscribeModal?: () => void = undefined\n\n  private abortController?: AbortController = undefined\n\n  private get overlayEl() {\n    return UiUtil.getShadowRootElement(this, '.wcm-overlay')\n  }\n\n  private get containerEl() {\n    return UiUtil.getShadowRootElement(this, '.wcm-container')\n  }\n\n  private toggleBodyScroll(enabled: boolean) {\n    const body = document.querySelector('body')\n    if (body) {\n      if (enabled) {\n        const wcmStyles = document.getElementById('wcm-styles')\n        wcmStyles?.remove()\n      } else {\n        document.head.insertAdjacentHTML(\n          'beforeend',\n          `<style id=\"wcm-styles\">html,body{touch-action:none;overflow:hidden;overscroll-behavior:contain;}</style>`\n        )\n      }\n    }\n  }\n\n  private onCloseModal(event: PointerEvent) {\n    if (event.target === event.currentTarget) {\n      ModalCtrl.close()\n    }\n  }\n\n  private onOpenModalEvent() {\n    this.toggleBodyScroll(false)\n    this.addKeyboardEvents()\n    this.open = true\n    setTimeout(async () => {\n      const animation = UiUtil.isMobileAnimation() ? { y: ['50vh', '0vh'] } : { scale: [0.98, 1] }\n      const delay = 0.1\n      const duration = 0.2\n      await Promise.all([\n        animate(this.overlayEl, { opacity: [0, 1] }, { delay, duration }).finished,\n        animate(this.containerEl, animation, { delay, duration }).finished\n      ])\n      this.active = true\n    }, 0)\n  }\n\n  private async onCloseModalEvent() {\n    this.toggleBodyScroll(true)\n    this.removeKeyboardEvents()\n    const animation = UiUtil.isMobileAnimation() ? { y: ['0vh', '50vh'] } : { scale: [1, 0.98] }\n    const duration = 0.2\n    await Promise.all([\n      animate(this.overlayEl, { opacity: [1, 0] }, { duration }).finished,\n      animate(this.containerEl, animation, { duration }).finished\n    ])\n    this.containerEl.removeAttribute('style')\n    this.active = false\n    this.open = false\n  }\n\n  private addKeyboardEvents() {\n    this.abortController = new AbortController()\n    window.addEventListener(\n      'keydown',\n      event => {\n        if (event.key === 'Escape') {\n          ModalCtrl.close()\n        } else if (event.key === 'Tab') {\n          if (!(event.target as Target)?.tagName.includes('wcm-')) {\n            this.containerEl.focus()\n          }\n        }\n      },\n      this.abortController\n    )\n    this.containerEl.focus()\n  }\n\n  private removeKeyboardEvents() {\n    this.abortController?.abort()\n    this.abortController = undefined\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const classes = {\n      'wcm-overlay': true,\n      'wcm-active': this.active\n    }\n\n    return html`\n      <wcm-explorer-context></wcm-explorer-context>\n      <wcm-theme-context></wcm-theme-context>\n\n      <div\n        id=\"wcm-modal\"\n        class=${classMap(classes)}\n        @click=${this.onCloseModal}\n        role=\"alertdialog\"\n        aria-modal=\"true\"\n      >\n        <div class=\"wcm-container\" tabindex=\"0\">\n          ${this.open\n            ? html`\n                <wcm-modal-backcard></wcm-modal-backcard>\n                <div class=\"wcm-card\">\n                  <wcm-modal-router></wcm-modal-router>\n                  <wcm-modal-toast></wcm-modal-toast>\n                </div>\n              `\n            : null}\n        </div>\n      </div>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-modal': WcmModal\n  }\n}\n", "import { CoreUtil, RouterCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement, property } from 'lit/decorators.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-platform-selection')\nexport class WcmPlatformSelection extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property({ type: Boolean }) public isMobile = false\n\n  @property({ type: Boolean }) public isDesktop = false\n\n  @property({ type: Boolean }) public isWeb = false\n\n  @property({ type: Boolean }) public isRetry = false\n\n  // -- private ------------------------------------------------------ //\n  private onMobile() {\n    const isMobile = CoreUtil.isMobile()\n    if (isMobile) {\n      RouterCtrl.replace('MobileConnecting')\n    } else {\n      RouterCtrl.replace('MobileQrcodeConnecting')\n    }\n  }\n\n  private onDesktop() {\n    RouterCtrl.replace('DesktopConnecting')\n  }\n\n  private onWeb() {\n    RouterCtrl.replace('WebConnecting')\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <div>\n        ${this.isRetry ? html`<slot></slot>` : null}\n        ${this.isMobile\n          ? html`<wcm-button\n              .onClick=${this.onMobile}\n              .iconLeft=${SvgUtil.MOBILE_ICON}\n              variant=\"outline\"\n            >\n              Mobile\n            </wcm-button>`\n          : null}\n        ${this.isDesktop\n          ? html`<wcm-button\n              .onClick=${this.onDesktop}\n              .iconLeft=${SvgUtil.DESKTOP_ICON}\n              variant=\"outline\"\n            >\n              Desktop\n            </wcm-button>`\n          : null}\n        ${this.isWeb\n          ? html`<wcm-button\n              .onClick=${this.onWeb}\n              .iconLeft=${SvgUtil.GLOBE_ICON}\n              variant=\"outline\"\n            >\n              Web\n            </wcm-button>`\n          : null}\n      </div>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-platform-selection': WcmPlatformSelection\n  }\n}\n", "import { ExplorerCtrl, RouterCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { DataUtil } from '../../utils/DataUtil'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-view-all-wallets-button')\nexport class WcmViewAllWalletsButton extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- render ------------------------------------------------------- //\n  private onClick() {\n    RouterCtrl.push('WalletExplorer')\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { recomendedWallets } = ExplorerCtrl.state\n    const manualWallets = DataUtil.manualWallets()\n    const reversedWallets = [...recomendedWallets, ...manualWallets].reverse().slice(0, 4)\n\n    return html`\n      <button @click=${this.onClick}>\n        <div class=\"wcm-icons\">\n          ${reversedWallets.map(wallet => {\n            const explorerImg = UiUtil.getWalletIcon(wallet)\n            if (explorerImg) {\n              return html`<img crossorigin=\"anonymous\" src=${explorerImg} />`\n            }\n            const src = UiUtil.getWalletIcon({ id: wallet.id })\n\n            return src\n              ? html`<img crossorigin=\"anonymous\" src=${src} />`\n              : SvgUtil.WALLET_PLACEHOLDER\n          })}\n          ${[...Array(4 - reversedWallets.length)].map(() => SvgUtil.WALLET_PLACEHOLDER)}\n        </div>\n        <wcm-text variant=\"xsmall-regular\">View All</wcm-text>\n      </button>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-view-all-wallets-button': WcmViewAllWalletsButton\n  }\n}\n", "import { OptionsCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement, property, state } from 'lit/decorators.js'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-walletconnect-qr')\nexport class WcmWalletConnectQr extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @property() public walletId? = ''\n\n  @property() public imageId? = ''\n\n  @state() private uri? = ''\n\n  // -- lifecycle ---------------------------------------------------- //\n  public constructor() {\n    super()\n    setTimeout(() => {\n      const { walletConnectUri } = OptionsCtrl.state\n      this.uri = walletConnectUri\n    }, 0)\n  }\n\n  // -- private ------------------------------------------------------ //\n\n  private get overlayEl(): HTMLDivElement {\n    return UiUtil.getShadowRootElement(this, '.wcm-qr-container') as HTMLDivElement\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <div class=\"wcm-qr-container\">\n        ${this.uri\n          ? html`<wcm-qrcode\n              size=\"${this.overlayEl.offsetWidth}\"\n              uri=${this.uri}\n              walletId=${ifDefined(this.walletId)}\n              imageId=${ifDefined(this.imageId)}\n            ></wcm-qrcode>`\n          : html`<wcm-spinner></wcm-spinner>`}\n      </div>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-walletconnect-qr': WcmWalletConnectQr\n  }\n}\n", "import { CoreUtil } from '@walletconnect/modal-core'\nimport { html, LitElement } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\n\n@customElement('wcm-connect-wallet-view')\nexport class WcmConnectWalletView extends LitElement {\n  public static styles = [ThemeUtil.globalCss]\n\n  // -- private ------------------------------------------------------ //\n  private viewTemplate() {\n    if (CoreUtil.isAndroid()) {\n      return html`<wcm-android-wallet-selection></wcm-android-wallet-selection>`\n    }\n\n    if (CoreUtil.isMobile()) {\n      return html`<wcm-mobile-wallet-selection></wcm-mobile-wallet-selection>`\n    }\n\n    return html`<wcm-desktop-wallet-selection></wcm-desktop-wallet-selection>`\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      ${this.viewTemplate()}\n      <wcm-legal-notice></wcm-legal-notice>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-connect-wallet-view': WcmConnectWalletView\n  }\n}\n", "import { CoreUtil, OptionsCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement, state } from 'lit/decorators.js'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-desktop-connecting-view')\nexport class WcmDesktopConnectingView extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @state() public isError = false\n\n  // -- lifecycle ---------------------------------------------------- //\n  public constructor() {\n    super()\n    this.openDesktopApp()\n  }\n\n  // -- private ------------------------------------------------------ //\n\n  private onFormatAndRedirect(uri: string) {\n    const { desktop, name } = CoreUtil.getWalletRouterData()\n    const nativeUrl = desktop?.native\n\n    if (nativeUrl) {\n      const href = CoreUtil.formatNativeUrl(nativeUrl, uri, name)\n      CoreUtil.openHref(href, '_self')\n    }\n  }\n\n  private openDesktopApp() {\n    const { walletConnectUri } = OptionsCtrl.state\n    const routerData = CoreUtil.getWalletRouterData()\n    UiUtil.setRecentWallet(routerData)\n    if (walletConnectUri) {\n      this.onFormatAndRedirect(walletConnectUri)\n    }\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { name, id, image_id } = CoreUtil.getWalletRouterData()\n    const { isMobile, isWeb } = UiUtil.getCachedRouterWalletPlatforms()\n\n    return html`\n      <wcm-modal-header\n        title=${name}\n        .onAction=${UiUtil.handleUriCopy}\n        .actionIcon=${SvgUtil.COPY_ICON}\n      ></wcm-modal-header>\n\n      <wcm-modal-content>\n        <wcm-connector-waiting\n          walletId=${id}\n          imageId=${ifDefined(image_id)}\n          label=${`Continue in ${name}...`}\n          .isError=${this.isError}\n        ></wcm-connector-waiting>\n      </wcm-modal-content>\n\n      <wcm-info-footer>\n        <wcm-text color=\"secondary\" variant=\"small-thin\">\n          ${`Connection can continue loading if ${name} is not installed on your device`}\n        </wcm-text>\n\n        <wcm-platform-selection .isMobile=${isMobile} .isWeb=${isWeb} .isRetry=${true}>\n          <wcm-button .onClick=${this.openDesktopApp.bind(this)} .iconRight=${SvgUtil.RETRY_ICON}>\n            Retry\n          </wcm-button>\n        </wcm-platform-selection>\n      </wcm-info-footer>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-desktop-connecting-view': WcmDesktopConnectingView\n  }\n}\n", "import { CoreUtil } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-install-wallet-view')\nexport class WcmInstallWalletView extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- private ------------------------------------------------------ //\n\n  private onInstall(uri?: string) {\n    if (uri) {\n      CoreUtil.openHref(uri, '_blank')\n    }\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { name, id, image_id, homepage } = CoreUtil.getWalletRouterData()\n\n    return html`\n      <wcm-modal-header title=${name}></wcm-modal-header>\n\n      <wcm-modal-content>\n        <wcm-connector-waiting\n          walletId=${id}\n          imageId=${ifDefined(image_id)}\n          label=\"Not Detected\"\n          .isStale=${true}\n        ></wcm-connector-waiting>\n      </wcm-modal-content>\n\n      <wcm-info-footer>\n        <wcm-text color=\"secondary\" variant=\"small-thin\">\n          ${`Download ${name} to continue. If multiple browser extensions are installed, disable non ${name} ones and try again`}\n        </wcm-text>\n\n        <wcm-button .onClick=${() => this.onInstall(homepage)} .iconLeft=${SvgUtil.ARROW_DOWN_ICON}>\n          Download\n        </wcm-button>\n      </wcm-info-footer>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-install-wallet-view': WcmInstallWalletView\n  }\n}\n", "import { CoreUtil, OptionsCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement, state } from 'lit/decorators.js'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-mobile-connecting-view')\nexport class WcmMobileConnectingView extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @state() public isError = false\n\n  // -- lifecycle ---------------------------------------------------- //\n  public constructor() {\n    super()\n    this.openMobileApp()\n  }\n\n  // -- private ------------------------------------------------------ //\n\n  private onFormatAndRedirect(uri: string, forceUniversalUrl = false) {\n    const { mobile, name } = CoreUtil.getWalletRouterData()\n    const nativeUrl = mobile?.native\n    const universalUrl = mobile?.universal\n\n    if (nativeUrl && !forceUniversalUrl) {\n      const href = CoreUtil.formatNativeUrl(nativeUrl, uri, name)\n      CoreUtil.openHref(href, '_self')\n    } else if (universalUrl) {\n      const href = CoreUtil.formatUniversalUrl(universalUrl, uri, name)\n      CoreUtil.openHref(href, '_self')\n    }\n  }\n\n  private openMobileApp(forceUniversalUrl = false) {\n    const { walletConnectUri } = OptionsCtrl.state\n    const routerData = CoreUtil.getWalletRouterData()\n    UiUtil.setRecentWallet(routerData)\n    if (walletConnectUri) {\n      this.onFormatAndRedirect(walletConnectUri, forceUniversalUrl)\n    }\n  }\n\n  private onGoToAppStore(downloadUrl?: string) {\n    if (downloadUrl) {\n      CoreUtil.openHref(downloadUrl, '_blank')\n    }\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { name, id, image_id, app, mobile } = CoreUtil.getWalletRouterData()\n    const { isWeb } = UiUtil.getCachedRouterWalletPlatforms()\n    const downloadUrl = app?.ios\n    const universalUrl = mobile?.universal\n\n    return html`\n      <wcm-modal-header title=${name}></wcm-modal-header>\n\n      <wcm-modal-content>\n        <wcm-connector-waiting\n          walletId=${id}\n          imageId=${ifDefined(image_id)}\n          label=\"Tap 'Open' to continue…\"\n          .isError=${this.isError}\n        ></wcm-connector-waiting>\n      </wcm-modal-content>\n\n      <wcm-info-footer class=\"wcm-note\">\n        <wcm-platform-selection .isWeb=${isWeb} .isRetry=${true}>\n          <wcm-button .onClick=${() => this.openMobileApp(false)} .iconRight=${SvgUtil.RETRY_ICON}>\n            Retry\n          </wcm-button>\n        </wcm-platform-selection>\n\n        ${universalUrl\n          ? html`\n              <wcm-text color=\"secondary\" variant=\"small-thin\">\n                Still doesn't work?\n                <span tabindex=\"0\" @click=${() => this.openMobileApp(true)}>\n                  Try this alternate link\n                </span>\n              </wcm-text>\n            `\n          : null}\n      </wcm-info-footer>\n\n      <wcm-info-footer class=\"wcm-app-store\">\n        <div>\n          <wcm-wallet-image walletId=${id} imageId=${ifDefined(image_id)}></wcm-wallet-image>\n          <wcm-text>${`Get ${name}`}</wcm-text>\n        </div>\n        <wcm-button\n          .iconRight=${SvgUtil.ARROW_RIGHT_ICON}\n          .onClick=${() => this.onGoToAppStore(downloadUrl)}\n          variant=\"ghost\"\n        >\n          App Store\n        </wcm-button>\n      </wcm-info-footer>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-mobile-connecting-view': WcmMobileConnectingView\n  }\n}\n", "import { CoreUtil } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-mobile-qr-connecting-view')\nexport class WcmMobileQrConnectingView extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { name, id, image_id } = CoreUtil.getWalletRouterData()\n    const { isDesktop, isWeb } = UiUtil.getCachedRouterWalletPlatforms()\n\n    return html`\n      <wcm-modal-header\n        title=${name}\n        .onAction=${UiUtil.handleUriCopy}\n        .actionIcon=${SvgUtil.COPY_ICON}\n      ></wcm-modal-header>\n\n      <wcm-modal-content>\n        <wcm-walletconnect-qr walletId=${id} imageId=${ifDefined(image_id)}></wcm-walletconnect-qr>\n      </wcm-modal-content>\n\n      <wcm-info-footer>\n        <wcm-text color=\"secondary\" variant=\"small-thin\">\n          ${`Scan this QR Code with your phone's camera or inside ${name} app`}\n        </wcm-text>\n\n        <wcm-platform-selection .isDesktop=${isDesktop} .isWeb=${isWeb}> </wcm-platform-selection>\n      </wcm-info-footer>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-mobile-qr-connecting-view': WcmMobileQrConnectingView\n  }\n}\n", "import { html, LitElement } from 'lit'\nimport { customElement } from 'lit/decorators.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\n\n@customElement('wcm-qrcode-view')\nexport class WcmQrcodeView extends LitElement {\n  public static styles = [ThemeUtil.globalCss]\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    return html`\n      <wcm-modal-header\n        title=\"Scan the code\"\n        .onAction=${UiUtil.handleUriCopy}\n        .actionIcon=${SvgUtil.COPY_ICON}\n      ></wcm-modal-header>\n\n      <wcm-modal-content>\n        <wcm-walletconnect-qr></wcm-walletconnect-qr>\n      </wcm-modal-content>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-qrcode-view': WcmQrcodeView\n  }\n}\n", "import type { Listing } from '@walletconnect/modal-core'\nimport { CoreUtil, ExplorerCtrl, OptionsCtrl, ToastCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement, state } from 'lit/decorators.js'\nimport { classMap } from 'lit/directives/class-map.js'\nimport { TemplateUtil } from '../../utils/TemplateUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\nconst PAGE_ENTRIES = 40\n\n@customElement('wcm-wallet-explorer-view')\nexport class WcmWalletExplorerView extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @state() private loading = !ExplorerCtrl.state.wallets.listings.length\n\n  @state() private firstFetch = !ExplorerCtrl.state.wallets.listings.length\n\n  @state() private search = ''\n\n  @state() private endReached = false\n\n  // -- lifecycle ---------------------------------------------------- //\n  public firstUpdated() {\n    this.createPaginationObserver()\n  }\n\n  public disconnectedCallback() {\n    this.intersectionObserver?.disconnect()\n  }\n\n  // -- private ------------------------------------------------------ //\n  private get placeholderEl() {\n    return UiUtil.getShadowRootElement(this, '.wcm-placeholder-block')\n  }\n\n  private intersectionObserver: IntersectionObserver | undefined = undefined\n\n  private createPaginationObserver() {\n    this.intersectionObserver = new IntersectionObserver(([element]) => {\n      if (element.isIntersecting && !(this.search && this.firstFetch)) {\n        this.fetchWallets()\n      }\n    })\n    this.intersectionObserver.observe(this.placeholderEl)\n  }\n\n  private isLastPage() {\n    const { wallets, search } = ExplorerCtrl.state\n    const { listings, total } = this.search ? search : wallets\n\n    return total <= PAGE_ENTRIES || listings.length >= total\n  }\n\n  private async fetchWallets() {\n    const { wallets, search } = ExplorerCtrl.state\n    const { listings, total, page } = this.search ? search : wallets\n\n    if (\n      !this.endReached &&\n      (this.firstFetch || (total > PAGE_ENTRIES && listings.length < total))\n    ) {\n      try {\n        this.loading = true\n        const chains = OptionsCtrl.state.chains?.join(',')\n        const { listings: newListings } = await ExplorerCtrl.getWallets({\n          page: this.firstFetch ? 1 : page + 1,\n          entries: PAGE_ENTRIES,\n          search: this.search,\n          version: 2,\n          chains\n        })\n        const explorerImages = newListings.map(wallet => UiUtil.getWalletIcon(wallet))\n        await Promise.all([\n          ...explorerImages.map(async url => UiUtil.preloadImage(url)),\n          CoreUtil.wait(300)\n        ])\n        this.endReached = this.isLastPage()\n      } catch (err) {\n        console.error(err)\n        ToastCtrl.openToast(UiUtil.getErrorMessage(err), 'error')\n      } finally {\n        this.loading = false\n        this.firstFetch = false\n      }\n    }\n  }\n\n  private onConnect(listing: Listing) {\n    if (CoreUtil.isAndroid()) {\n      UiUtil.handleMobileLinking(listing)\n    } else {\n      UiUtil.goToConnectingView(listing)\n    }\n  }\n\n  private onSearchChange(event: Event) {\n    const { value } = event.target as HTMLInputElement\n    this.searchDebounce(value)\n  }\n\n  private readonly searchDebounce = UiUtil.debounce((value: string) => {\n    if (value.length >= 1) {\n      this.firstFetch = true\n      this.endReached = false\n      this.search = value\n      ExplorerCtrl.resetSearch()\n      this.fetchWallets()\n    } else if (this.search) {\n      this.search = ''\n      this.endReached = this.isLastPage()\n      ExplorerCtrl.resetSearch()\n    }\n  })\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { wallets, search } = ExplorerCtrl.state\n    const { listings } = this.search ? search : wallets\n    const isLoading = this.loading && !listings.length\n    const isSearch = this.search.length >= 3\n    let manualWallets = TemplateUtil.manualWalletsTemplate()\n    let recomendedWallets = TemplateUtil.recomendedWalletsTemplate(true)\n\n    // If search is active, we only show results matching query\n    if (isSearch) {\n      manualWallets = manualWallets.filter(({ values }) =>\n        UiUtil.caseSafeIncludes(values[0] as string, this.search)\n      )\n      recomendedWallets = recomendedWallets.filter(({ values }) =>\n        UiUtil.caseSafeIncludes(values[0] as string, this.search)\n      )\n    }\n\n    const isEmpty = !this.loading && !listings.length && !recomendedWallets.length\n    const classes = {\n      'wcm-loading': isLoading,\n      'wcm-end-reached': this.endReached || !this.loading,\n      'wcm-empty': isEmpty\n    }\n\n    return html`\n      <wcm-modal-header>\n        <wcm-search-input .onChange=${this.onSearchChange.bind(this)}></wcm-search-input>\n      </wcm-modal-header>\n\n      <wcm-modal-content class=${classMap(classes)}>\n        <div class=\"wcm-grid\">\n          ${isLoading ? null : manualWallets} ${isLoading ? null : recomendedWallets}\n          ${isLoading\n            ? null\n            : listings.map(\n                listing => html`\n                  ${listing\n                    ? html`\n                        <wcm-wallet-button\n                          imageId=${listing.image_id}\n                          name=${listing.name}\n                          walletId=${listing.id}\n                          .onClick=${() => this.onConnect(listing)}\n                        >\n                        </wcm-wallet-button>\n                      `\n                    : null}\n                `\n              )}\n        </div>\n        <div class=\"wcm-placeholder-block\">\n          ${isEmpty\n            ? html`<wcm-text variant=\"big-bold\" color=\"secondary\">No results found</wcm-text>`\n            : null}\n          ${!isEmpty && this.loading ? html`<wcm-spinner></wcm-spinner>` : null}\n        </div>\n      </wcm-modal-content>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-wallet-explorer-view': WcmWalletExplorerView\n  }\n}\n", "import { CoreUtil, OptionsCtrl } from '@walletconnect/modal-core'\nimport { LitElement, html } from 'lit'\nimport { customElement, state } from 'lit/decorators.js'\nimport { ifDefined } from 'lit/directives/if-defined.js'\nimport { SvgUtil } from '../../utils/SvgUtil'\nimport { ThemeUtil } from '../../utils/ThemeUtil'\nimport { UiUtil } from '../../utils/UiUtil'\nimport styles from './styles.css'\n\n@customElement('wcm-web-connecting-view')\nexport class WcmWebConnectingView extends LitElement {\n  public static styles = [ThemeUtil.globalCss, styles]\n\n  // -- state & properties ------------------------------------------- //\n  @state() public isError = false\n\n  // -- lifecycle ---------------------------------------------------- //\n  public constructor() {\n    super()\n    this.openWebWallet()\n  }\n\n  // -- private ------------------------------------------------------ //\n  private onFormatAndRedirect(uri: string) {\n    const { desktop, name } = CoreUtil.getWalletRouterData()\n    const universalUrl = desktop?.universal\n\n    if (universalUrl) {\n      const href = CoreUtil.formatUniversalUrl(universalUrl, uri, name)\n      CoreUtil.openHref(href, '_blank')\n    }\n  }\n\n  private openWebWallet() {\n    const { walletConnectUri } = OptionsCtrl.state\n    const routerData = CoreUtil.getWalletRouterData()\n    UiUtil.setRecentWallet(routerData)\n    if (walletConnectUri) {\n      this.onFormatAndRedirect(walletConnectUri)\n    }\n  }\n\n  // -- render ------------------------------------------------------- //\n  protected render() {\n    const { name, id, image_id } = CoreUtil.getWalletRouterData()\n    const { isMobile, isDesktop } = UiUtil.getCachedRouterWalletPlatforms()\n    const isMobilePlatform = CoreUtil.isMobile()\n\n    return html`\n      <wcm-modal-header\n        title=${name}\n        .onAction=${UiUtil.handleUriCopy}\n        .actionIcon=${SvgUtil.COPY_ICON}\n      ></wcm-modal-header>\n\n      <wcm-modal-content>\n        <wcm-connector-waiting\n          walletId=${id}\n          imageId=${ifDefined(image_id)}\n          label=${`Continue in ${name}...`}\n          .isError=${this.isError}\n        ></wcm-connector-waiting>\n      </wcm-modal-content>\n\n      <wcm-info-footer>\n        <wcm-text color=\"secondary\" variant=\"small-thin\">\n          ${`${name} web app has opened in a new tab. Go there, accept the connection, and come back`}\n        </wcm-text>\n\n        <wcm-platform-selection\n          .isMobile=${isMobile}\n          .isDesktop=${isMobilePlatform ? false : isDesktop}\n          .isRetry=${true}\n        >\n          <wcm-button .onClick=${this.openWebWallet.bind(this)} .iconRight=${SvgUtil.RETRY_ICON}>\n            Retry\n          </wcm-button>\n        </wcm-platform-selection>\n      </wcm-info-footer>\n    `\n  }\n}\n\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'wcm-web-connecting-view': WcmWebConnectingView\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,YAAY;AAClB,IAAM,SAAS,YAAY,aAAa;AAKjC,IAAM,8BACX,OAAO,eACN,OAAO,aAAa,UAAa,OAAO,SAAS,iBAClD,wBAAwB,SAAS,aACjC,aAAa,cAAc;AAkB7B,IAAM,oBAAoB,OAAM;AAEhC,IAAM,cAAc,oBAAI,QAAO;AASzB,IAAO,YAAP,MAAgB;EAOpB,YACE,SACA,SACA,WAAiB;AARnB,SAAC,cAAc,IAAI;AAUjB,QAAI,cAAc,mBAAmB;AACnC,YAAM,IAAI,MACR,mEAAmE;;AAGvE,SAAK,UAAU;AACf,SAAK,WAAW;EAClB;;;EAIA,IAAI,aAAU;AAGZ,QAAI,aAAa,KAAK;AACtB,UAAM,UAAU,KAAK;AACrB,QAAI,+BAA+B,eAAe,QAAW;AAC3D,YAAM,YAAY,YAAY,UAAa,QAAQ,WAAW;AAC9D,UAAI,WAAW;AACb,qBAAa,YAAY,IAAI,OAAO;;AAEtC,UAAI,eAAe,QAAW;AAC5B,SAAC,KAAK,cAAc,aAAa,IAAI,cAAa,GAAI,YACpD,KAAK,OAAO;AAEd,YAAI,WAAW;AACb,sBAAY,IAAI,SAAS,UAAU;;;;AAIzC,WAAO;EACT;EAEA,WAAQ;AACN,WAAO,KAAK;EACd;;AAWF,IAAM,oBAAoB,CAAC,UAAkC;AAE3D,MAAK,MAAoB,cAAc,MAAM,MAAM;AACjD,WAAQ,MAAoB;aACnB,OAAO,UAAU,UAAU;AACpC,WAAO;SACF;AACL,UAAM,IAAI,MACR,mEACK,KAAK,sFACkB;;AAGlC;AASO,IAAM,YAAY,CAAC,UACxB,IAAK,UACH,OAAO,UAAU,WAAW,QAAQ,OAAO,KAAK,GAChD,QACA,iBAAiB;AAWd,IAAM,MAAM,CACjB,YACG,WACU;AACb,QAAM,UACJ,QAAQ,WAAW,IACf,QAAQ,CAAC,IACT,OAAO,OACL,CAAC,KAAKA,IAAG,QAAQ,MAAM,kBAAkBA,EAAC,IAAI,QAAQ,MAAM,CAAC,GAC7D,QAAQ,CAAC,CAAC;AAElB,SAAO,IAAK,UACV,SACA,SACA,iBAAiB;AAErB;AAWO,IAAM,cAAc,CACzB,YACA,WACE;AACF,MAAI,6BAA6B;AAC9B,eAA0B,qBAAqB,OAAO,IAAI,CAAC,MAC1D,aAAa,gBAAgB,IAAI,EAAE,UAAW;SAE3C;AACL,WAAO,QAAQ,CAAC,MAAK;AACnB,YAAMC,SAAQ,SAAS,cAAc,OAAO;AAE5C,YAAM,QAAS,OAAe,UAAU;AACxC,UAAI,UAAU,QAAW;AACvB,QAAAA,OAAM,aAAa,SAAS,KAAK;;AAEnC,MAAAA,OAAM,cAAe,EAAgB;AACrC,iBAAW,YAAYA,MAAK;IAC9B,CAAC;;AAEL;AAEA,IAAM,0BAA0B,CAAC,UAAwB;AACvD,MAAI,UAAU;AACd,aAAW,QAAQ,MAAM,UAAU;AACjC,eAAW,KAAK;;AAElB,SAAO,UAAU,OAAO;AAC1B;AAEO,IAAM,qBACX,+BACC,aAAa,OAAO,kBAAkB,SACnC,CAAC,MAAyB,IAC1B,CAAC,MACC,aAAa,gBAAgB,wBAAwB,CAAC,IAAI;;;;;;;;AC1KlE,IAAMC,aAAY;AAClB,IAAMC,UAASD,aAAY,aAAa;AAExC,IAAIA,YAAW;AACb,GAAA,KAAAC,QAAO,oBAAc,QAAA,OAAA,SAAA,KAArBA,QAAO,iBAAmB;;AAG5B,IAAM,WAAW;AAEjB,IAAI;AAOJ,IAAI;AAEJ,IAAM,eAAgBA,QACnB;AAMH,IAAM,iCAAiC,eAClC,aAAa,cACd;AAEJ,IAAM,kBAAkB,WACpBA,QAAO,wCACPA,QAAO;AAEX,IAAI,UAAU;AAGZ,QAAM,kBAA0C,KAACA,QAAO,uBAAiB,QAAA,OAAA,SAAA,KAAxBA,QAAO,oBACtD,oBAAI,IAAG;AAGT,iBAAe,CAAC,MAAcC,aAAmB;AAC/C,IAAAA,YAAW,4BAA4B,IAAI;AAC3C,QAAI,CAAC,eAAe,IAAIA,QAAO,GAAG;AAChC,cAAQ,KAAKA,QAAO;AACpB,qBAAe,IAAIA,QAAO;;EAE9B;AAEA,eACE,YACA,qDAAqD;AAIvD,QAAI,KAAAD,QAAO,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,oBAAoB,QAAW;AAC3D,iBACE,4BACA,0GACwD;;AAI5D,0BAAwB,CAAC,UAAU;IACjC,MAAM,CACJ,aACA,gBACE;AACF,mBACE,0BACA,mFACkB,IAAI,qCAAqC;AAE7D,UAAI,gBAAgB,QAAW;AAC7B,oBAAY,KAAK;;IAErB;;;AA0CJ,IAAM,gBAAgB,WAClB,CAAC,UAA0C;AACzC,QAAM,aAAcA,QACjB;AACH,MAAI,CAAC,YAAY;AACf;;AAEF,EAAAA,QAAO,cACL,IAAI,YAA6C,aAAa;IAC5D,QAAQ;GACT,CAAC;AAEN,IACA;AASJ,IAAM,4BAA4B,CAChC,MACA,SACM;AA+ID,IAAM,mBAA8C;EACzD,YAAY,OAAgB,MAAc;AACxC,YAAQ,MAAM;MACZ,KAAK;AACH,gBAAQ,QAAQ,iCAAiC;AACjD;MACF,KAAK;MACL,KAAK;AAGH,gBAAQ,SAAS,OAAO,QAAQ,KAAK,UAAU,KAAK;AACpD;;AAEJ,WAAO;EACT;EAEA,cAAc,OAAsB,MAAc;AAChD,QAAI,YAAqB;AACzB,YAAQ,MAAM;MACZ,KAAK;AACH,oBAAY,UAAU;AACtB;MACF,KAAK;AACH,oBAAY,UAAU,OAAO,OAAO,OAAO,KAAK;AAChD;MACF,KAAK;MACL,KAAK;AAIH,YAAI;AAEF,sBAAY,KAAK,MAAM,KAAM;iBACtB,GAAG;AACV,sBAAY;;AAEd;;AAEJ,WAAO;EACT;;AAWK,IAAM,WAAuB,CAAC,OAAgB,QAAyB;AAE5E,SAAO,QAAQ,UAAU,QAAQ,OAAO,UAAU;AACpD;AAEA,IAAM,6BAAkD;EACtD,WAAW;EACX,MAAM;EACN,WAAW;EACX,SAAS;EACT,YAAY;;AASd,IAAM,YAAY;AAeZ,IAAgB,kBAAhB,cASI,YAAW;EAsgBnB,cAAA;AACE,UAAK;AA3CC,SAAA,uBAAwC,oBAAI,IAAG;AAUvD,SAAA,kBAAkB;AAOlB,SAAA,aAAa;AAkBL,SAAA,uBAA2C;AASjD,SAAK,aAAY;EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EApbA,OAAO,eAAe,aAAwB;;AAC5C,SAAK,SAAQ;AACb,MAAAE,MAAC,KAAK,mBAAa,QAAAA,QAAA,SAAAA,MAAlB,KAAK,gBAAkB,CAAA,GAAI,KAAK,WAAW;EAC9C;;;;;;EA0GA,WAAW,qBAAkB;AAE3B,SAAK,SAAQ;AACb,UAAM,aAAuB,CAAA;AAG7B,SAAK,kBAAkB,QAAQ,CAACC,IAAGC,OAAK;AACtC,YAAM,OAAO,KAAK,2BAA2BA,IAAGD,EAAC;AACjD,UAAI,SAAS,QAAW;AACtB,aAAK,yBAAyB,IAAI,MAAMC,EAAC;AACzC,mBAAW,KAAK,IAAI;;IAExB,CAAC;AACD,WAAO;EACT;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BA,OAAO,eACL,MACA,UAA+B,4BAA0B;;AAGzD,QAAI,QAAQ,OAAO;AAGhB,cAAgB,YAAY;;AAI/B,SAAK,SAAQ;AACb,SAAK,kBAAkB,IAAI,MAAM,OAAO;AAMxC,QAAI,CAAC,QAAQ,cAAc,CAAC,KAAK,UAAU,eAAe,IAAI,GAAG;AAC/D,YAAM,MAAM,OAAO,SAAS,WAAW,OAAM,IAAK,KAAK,IAAI;AAC3D,YAAM,aAAa,KAAK,sBAAsB,MAAM,KAAK,OAAO;AAChE,UAAI,eAAe,QAAW;AAC5B,eAAO,eAAe,KAAK,WAAW,MAAM,UAAU;AACtD,YAAI,UAAU;AAGZ,cAAI,CAAC,KAAK,eAAe,wBAAwB,GAAG;AAClD,iBAAK,yBAAyB,IAAI,KAChCF,MAAA,KAAK,4BAAsB,QAAAA,QAAA,SAAAA,MAAI,CAAA,CAAE;;AAGrC,eAAK,uBAAwB,IAAI,IAAI;;;;EAI7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BU,OAAO,sBACf,MACA,KACA,SAA4B;AAE5B,WAAO;;MAEL,MAAG;AACD,eAAQ,KAAkC,GAAa;MACzD;MACA,IAA2B,OAAc;AACvC,cAAM,WAAY,KAChB,IAAc;AAEf,aAAwC,GAAa,IAAI;AACzD,aAAoC,cACnC,MACA,UACA,OAAO;MAEX;MACA,cAAc;MACd,YAAY;;EAEhB;;;;;;;;;;;;;;;EAgBA,OAAO,mBAAmB,MAAiB;AACzC,WAAO,KAAK,kBAAkB,IAAI,IAAI,KAAK;EAC7C;;;;;;;EAQU,OAAO,WAAQ;AACvB,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,aAAO;;AAET,SAAK,SAAS,IAAI;AAElB,UAAM,YAAY,OAAO,eAAe,IAAI;AAC5C,cAAU,SAAQ;AAIlB,QAAI,UAAU,kBAAkB,QAAW;AACzC,WAAK,gBAAgB,CAAC,GAAG,UAAU,aAAa;;AAElD,SAAK,oBAAoB,IAAI,IAAI,UAAU,iBAAiB;AAE5D,SAAK,2BAA2B,oBAAI,IAAG;AAKvC,QAAI,KAAK,eAAe,0BAA0B,cAAc,IAAI,CAAC,GAAG;AACtE,YAAM,QAAQ,KAAK;AAEnB,YAAM,WAAW;QACf,GAAG,OAAO,oBAAoB,KAAK;QACnC,GAAG,OAAO,sBAAsB,KAAK;;AAGvC,iBAAWE,MAAK,UAAU;AAIxB,aAAK,eAAeA,IAAI,MAAcA,EAAC,CAAC;;;AAG5C,SAAK,gBAAgB,KAAK,eAAe,KAAK,MAAM;AAEpD,QAAI,UAAU;AACZ,YAAM,uBAAuB,CAAC,MAAc,UAAU,UAAS;AAC7D,YAAI,KAAK,UAAU,eAAe,IAAI,GAAG;AACvC,uBACE,UAAU,gBAAgB,eAC1B,KAAK,IAAI,8BAA8B,KAAK,IAAI,iBAClC,UAAU,YAAY,SAAS,iCACX;;MAGxC;AACA,2BAAqB,YAAY;AACjC,2BAAqB,uBAAuB;AAC5C,2BAAqB,sBAAsB,IAAI;;AAEjD,WAAO;EACT;;;;;;;;;;;;;;;EA4BU,OAAO,eACf,QAAuB;AAEvB,UAAM,gBAAgB,CAAA;AACtB,QAAI,MAAM,QAAQ,MAAM,GAAG;AAIzB,YAAM,MAAM,IAAI,IAAK,OAA0B,KAAK,QAAQ,EAAE,QAAO,CAAE;AAEvE,iBAAW,KAAK,KAAK;AACnB,sBAAc,QAAQ,mBAAmB,CAAsB,CAAC;;eAEzD,WAAW,QAAW;AAC/B,oBAAc,KAAK,mBAAmB,MAAM,CAAC;;AAE/C,WAAO;EACT;;;;;EAaQ,OAAO,2BACb,MACA,SAA4B;AAE5B,UAAM,YAAY,QAAQ;AAC1B,WAAO,cAAc,QACjB,SACA,OAAO,cAAc,WACrB,YACA,OAAO,SAAS,WAChB,KAAK,YAAW,IAChB;EACN;;;;;EAqDQ,eAAY;;AAClB,SAAK,kBAAkB,IAAI,QACzB,CAAC,QAAS,KAAK,iBAAiB,GAAI;AAEtC,SAAK,sBAAsB,oBAAI,IAAG;AAClC,SAAK,yBAAwB;AAG7B,SAAK,cAAa;AAClB,KAAAF,MAAC,KAAK,YAAuC,mBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAQ,CAAC,MACnE,EAAE,IAAI,CAAC;EAEX;;;;;;;;;;EAWA,cAAc,YAA8B;;AAC1C,MAAAA,MAAC,KAAK,mBAAa,QAAAA,QAAA,SAAAA,MAAlB,KAAK,gBAAkB,CAAA,GAAI,KAAK,UAAU;AAK3C,QAAI,KAAK,eAAe,UAAa,KAAK,aAAa;AACrD,OAAAG,MAAA,WAAW,mBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,UAAA;;EAE5B;;;;;EAMA,iBAAiB,YAA8B;;AAG7C,KAAAH,MAAA,KAAK,mBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAE,OAAO,KAAK,cAAc,QAAQ,UAAU,MAAM,GAAG,CAAC;EAC5E;;;;;;;;;;;;;EAcQ,2BAAwB;AAG7B,SAAK,YAAuC,kBAAkB,QAC7D,CAAC,IAAIE,OAAK;AACR,UAAI,KAAK,eAAeA,EAAC,GAAG;AAC1B,aAAK,qBAAsB,IAAIA,IAAG,KAAKA,EAAe,CAAC;AACvD,eAAO,KAAKA,EAAe;;IAE/B,CAAC;EAEL;;;;;;;;;;EAWU,mBAAgB;;AACxB,UAAM,cACJF,MAAA,KAAK,gBAAU,QAAAA,QAAA,SAAAA,MACf,KAAK,aACF,KAAK,YAAuC,iBAAiB;AAElE,gBACE,YACC,KAAK,YAAuC,aAAa;AAE5D,WAAO;EACT;;;;;;EAOA,oBAAiB;;AAEf,QAAI,KAAK,eAAe,QAAW;AAE/B,WAGA,aAAa,KAAK,iBAAgB;;AAEtC,SAAK,eAAe,IAAI;AACxB,KAAAA,MAAA,KAAK,mBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAQ,CAACI,OAAK;AAAA,UAAAJ;AAAC,cAAAA,MAAAI,GAAE,mBAAa,QAAAJ,QAAA,SAAA,SAAAA,IAAA,KAAAI,EAAA;IAAI,CAAA;EACxD;;;;;;;EAQU,eAAe,kBAAyB;EAAG;;;;;;;EAQrD,uBAAoB;;AAClB,KAAAJ,MAAA,KAAK,mBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAQ,CAACI,OAAK;AAAA,UAAAJ;AAAC,cAAAA,MAAAI,GAAE,sBAAgB,QAAAJ,QAAA,SAAA,SAAAA,IAAA,KAAAI,EAAA;IAAI,CAAA;EAC3D;;;;;;;;;;;;;EAcA,yBACE,MACA,MACA,OAAoB;AAEpB,SAAK,sBAAsB,MAAM,KAAK;EACxC;EAEQ,sBACN,MACA,OACA,UAA+B,4BAA0B;;AAEzD,UAAM,OACJ,KAAK,YACL,2BAA2B,MAAM,OAAO;AAC1C,QAAI,SAAS,UAAa,QAAQ,YAAY,MAAM;AAClD,YAAM,cACJJ,MAAC,QAAQ,eAAuC,QAAAA,QAAA,SAAA,SAAAA,IAAE,iBAClD,SACK,QAAQ,YACT;AACN,YAAM,YAAY,UAAU,YAAa,OAAO,QAAQ,IAAI;AAC5D,UACE,YACC,KAAK,YAAuC,gBAAiB,QAC5D,WAAW,KACR,KACL,cAAc,QACd;AACA,qBACE,6BACA,+BAA+B,IAAc,qCACnB,KAAK,SAAS,4HAEC;;AAW7C,WAAK,uBAAuB;AAC5B,UAAI,aAAa,MAAM;AACrB,aAAK,gBAAgB,IAAI;aACpB;AACL,aAAK,aAAa,MAAM,SAAmB;;AAG7C,WAAK,uBAAuB;;EAEhC;;EAGA,sBAAsB,MAAc,OAAoB;;AACtD,UAAM,OAAO,KAAK;AAGlB,UAAM,WAAY,KAAK,yBAA0C,IAAI,IAAI;AAGzE,QAAI,aAAa,UAAa,KAAK,yBAAyB,UAAU;AACpE,YAAM,UAAU,KAAK,mBAAmB,QAAQ;AAChD,YAAM,YACJ,OAAO,QAAQ,cAAc,aACzB,EAAC,eAAe,QAAQ,UAAS,MACjCA,MAAA,QAAQ,eAAS,QAAAA,QAAA,SAAA,SAAAA,IAAE,mBAAkB,SACrC,QAAQ,YACR;AAEN,WAAK,uBAAuB;AAC5B,WAAK,QAAsB,IAAI,UAAU;QACvC;QACA,QAAQ;;;AAIV,WAAK,uBAAuB;;EAEhC;;;;;;;;;;;;;;;EAgBA,cACE,MACA,UACA,SAA6B;AAE7B,QAAI,sBAAsB;AAE1B,QAAI,SAAS,QAAW;AACtB,gBACE,WACC,KAAK,YAAuC,mBAAmB,IAAI;AACtE,YAAMK,cAAa,QAAQ,cAAc;AACzC,UAAIA,YAAW,KAAK,IAAkB,GAAG,QAAQ,GAAG;AAClD,YAAI,CAAC,KAAK,oBAAoB,IAAI,IAAI,GAAG;AACvC,eAAK,oBAAoB,IAAI,MAAM,QAAQ;;AAM7C,YAAI,QAAQ,YAAY,QAAQ,KAAK,yBAAyB,MAAM;AAClE,cAAI,KAAK,2BAA2B,QAAW;AAC7C,iBAAK,yBAAyB,oBAAI,IAAG;;AAEvC,eAAK,uBAAuB,IAAI,MAAM,OAAO;;aAE1C;AAEL,8BAAsB;;;AAG1B,QAAI,CAAC,KAAK,mBAAmB,qBAAqB;AAChD,WAAK,kBAAkB,KAAK,gBAAe;;AAI7C,WAAO,WACF,sBAAsB,KAAK,SAAS,IACrC;EACN;;;;EAKQ,MAAM,kBAAe;AAC3B,SAAK,kBAAkB;AACvB,QAAI;AAGF,YAAM,KAAK;aACJ,GAAG;AAKV,cAAQ,OAAO,CAAC;;AAElB,UAAM,SAAS,KAAK,eAAc;AAIlC,QAAI,UAAU,MAAM;AAClB,YAAM;;AAER,WAAO,CAAC,KAAK;EACf;;;;;;;;;;;;;;;;;;EAmBU,iBAAc;AACtB,WAAO,KAAK,cAAa;EAC3B;;;;;;;;;;;;;;;;;;EAmBU,gBAAa;;AAIrB,QAAI,CAAC,KAAK,iBAAiB;AACzB;;AAEF,sBAAa,QAAb,kBAAa,SAAA,SAAb,cAAgB,EAAC,MAAM,SAAQ,CAAC;AAEhC,QAAI,CAAC,KAAK,YAAY;AAEpB,UAAI,UAAU;AACZ,cAAM,qBAA+B,CAAA;AACrC,SAAAL,MACE,KAAK,YACL,4BAAsB,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAQ,CAACE,OAAK;;AACtC,cAAI,KAAK,eAAeA,EAAC,KAAK,GAACF,MAAA,KAAK,0BAAoB,QAAAA,QAAA,SAAA,SAAAA,IAAE,IAAIE,EAAC,IAAG;AAChE,+BAAmB,KAAKA,EAAW;;QAEvC,CAAC;AACD,YAAI,mBAAmB,QAAQ;AAC7B,gBAAM,IAAI,MACR,uCAAuC,KAAK,SAAS,kFAExC,mBAAmB,KAAK,IAAI,CAAC,yKAIjB;;;;AAMjC,QAAI,KAAK,sBAAsB;AAI7B,WAAK,qBAAsB,QAAQ,CAACD,IAAGC,OAAQ,KAAaA,EAAC,IAAID,EAAE;AACnE,WAAK,uBAAuB;;AAE9B,QAAI,eAAe;AACnB,UAAM,oBAAoB,KAAK;AAC/B,QAAI;AACF,qBAAe,KAAK,aAAa,iBAAiB;AAClD,UAAI,cAAc;AAChB,aAAK,WAAW,iBAAiB;AACjC,SAAAE,MAAA,KAAK,mBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAQ,CAACC,OAAK;AAAA,cAAAJ;AAAC,kBAAAA,MAAAI,GAAE,gBAAU,QAAAJ,QAAA,SAAA,SAAAA,IAAA,KAAAI,EAAA;QAAI,CAAA;AACnD,aAAK,OAAO,iBAAiB;aACxB;AACL,aAAK,cAAa;;aAEb,GAAG;AAGV,qBAAe;AAEf,WAAK,cAAa;AAClB,YAAM;;AAGR,QAAI,cAAc;AAChB,WAAK,YAAY,iBAAiB;;EAEtC;;;;;;;;;;;;;;;;;;;;;;EAuBU,WAAW,oBAAkC;EAAS;;;EAIhE,YAAY,mBAAiC;;AAC3C,KAAAJ,MAAA,KAAK,mBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAQ,CAACI,OAAK;AAAA,UAAAJ;AAAC,cAAAA,MAAAI,GAAE,iBAAW,QAAAJ,QAAA,SAAA,SAAAA,IAAA,KAAAI,EAAA;IAAI,CAAA;AACpD,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa;AAClB,WAAK,aAAa,iBAAiB;;AAErC,SAAK,QAAQ,iBAAiB;AAC9B,QACE,YACA,KAAK,mBACJ,KAAK,YAAuC,gBAAiB,QAC5D,kBAAkB,KACf,GACL;AACA,mBACE,oBACA,WAAW,KAAK,SAAS,8PAIyC;;EAGxE;EAEQ,gBAAa;AACnB,SAAK,sBAAsB,oBAAI,IAAG;AAClC,SAAK,kBAAkB;EACzB;;;;;;;;;;;;;;;;;EAkBA,IAAI,iBAAc;AAChB,WAAO,KAAK,kBAAiB;EAC/B;;;;;;;;;;;;;;;;;;;;;;;;EAyBU,oBAAiB;AACzB,WAAO,KAAK;EACd;;;;;;;;;EAUU,aAAa,oBAAkC;AACvD,WAAO;EACT;;;;;;;;;;EAWU,OAAO,oBAAkC;AACjD,QAAI,KAAK,2BAA2B,QAAW;AAG7C,WAAK,uBAAuB,QAAQ,CAACH,IAAG,MACtC,KAAK,sBAAsB,GAAG,KAAK,CAAe,GAAGA,EAAC,CAAC;AAEzD,WAAK,yBAAyB;;AAEhC,SAAK,cAAa;EACpB;;;;;;;;;;;EAYU,QAAQ,oBAAkC;EAAG;;;;;;;;;;;;;;;;;EAkB7C,aAAa,oBAAkC;EAAG;;KA99B1C;AAAD,gBAAA,EAAA,IAAc;AAQxB,gBAAA,oBAA4C,oBAAI,IAAG;AAmCnD,gBAAA,gBAA0C,CAAA;AAwQ1C,gBAAA,oBAAoC,EAAC,MAAM,OAAM;AA+qB1D,oBAAe,QAAf,oBAAe,SAAA,SAAf,gBAAkB,EAAC,gBAAe,CAAC;AAGnC,IAAI,UAAU;AAEZ,kBAAgB,kBAAkB,CAAC,kBAAkB;AACrD,QAAM,oBAAoB,SAAU,MAA4B;AAC9D,QACE,CAAC,KAAK,eAAe,0BAA0B,mBAAmB,IAAI,CAAC,GACvE;AACA,WAAK,kBAAkB,KAAK,gBAAiB,MAAK;;EAEtD;AACA,kBAAgB,gBAAgB,SAE9BF,UAAoB;AAEpB,sBAAkB,IAAI;AACtB,QAAI,KAAK,gBAAiB,QAAQA,QAAO,IAAI,GAAG;AAC9C,WAAK,gBAAiB,KAAKA,QAAO;;EAEtC;AACA,kBAAgB,iBAAiB,SAE/BA,UAAoB;AAEpB,sBAAkB,IAAI;AACtB,UAAM,IAAI,KAAK,gBAAiB,QAAQA,QAAO;AAC/C,QAAI,KAAK,GAAG;AACV,WAAK,gBAAiB,OAAO,GAAG,CAAC;;EAErC;;EAKF,KAACD,QAAO,6BAAuB,QAAA,OAAA,SAAA,KAA9BA,QAAO,0BAA4B,CAAA,GAAI,KAAK,OAAO;AACpD,IAAI,YAAYA,QAAO,wBAAwB,SAAS,GAAG;AACzD,eACE,qBACA,gFACuB;;;;;;;;ACzgD3B,IAAMQ,YAAW;AACjB,IAAM,8BAA8B;AACpC,IAAM,0BAA0B;AAChC,IAAMC,aAAY;AAElB,IAAMC,UAASD,aAAY,aAAa;AAmLxC,IAAME,iBAAgBH,YAClB,CAAC,UAAqC;AACpC,QAAM,aAAcE,QACjB;AACH,MAAI,CAAC,YAAY;AACf;;AAEF,EAAAA,QAAO,cACL,IAAI,YAAwC,aAAa;IACvD,QAAQ;GACT,CAAC;AAEN,IACA;AAIJ,IAAI,mBAAmB;AAEvB,IAAIE;AAEJ,IAAIJ,WAAU;AACZ,GAAAK,MAAAH,QAAO,uBAAiB,QAAAG,QAAA,SAAAA,MAAxBH,QAAO,oBAAsB,oBAAI,IAAG;AAGpC,EAAAE,gBAAe,CAAC,MAAcE,aAAmB;AAC/C,IAAAA,YAAW,OACP,4BAA4B,IAAI,2BAChC;AACJ,QAAI,CAACJ,QAAO,kBAAmB,IAAII,QAAO,GAAG;AAC3C,cAAQ,KAAKA,QAAO;AACpB,MAAAJ,QAAO,kBAAmB,IAAII,QAAO;;EAEzC;AAEA,EAAAF,cACE,YACA,qDAAqD;;AAIzD,IAAM,OACJ,6BACAG,MAAAL,QAAO,cAAQ,QAAAK,QAAA,SAAA,SAAAA,IAAE,YACjBC,MAAAN,QAAO,cAAQ,QAAAM,QAAA,SAAA,SAAAA,IAAE,aAAY,OACzBN,QAAO,SAAU,OACjB,CAAC,SAAe;AAEtB,IAAMO,gBAAgBP,QAAsC;AAU5D,IAAM,SAASO,gBACXA,cAAa,aAAa,YAAY;EACpC,YAAY,CAAC,MAAM;CACpB,IACD;AA0CJ,IAAM,mBAAmC,CAAC,UAAmB;AAC7D,IAAM,gBAAkC,CACtC,OACA,OACA,UACG;AAGL,IAAM,eAAe,CAAC,iBAAkC;AACtD,MAAI,CAAC,6BAA6B;AAChC;;AAEF,MAAI,6BAA6B,eAAe;AAC9C,UAAM,IAAI,MACR,qHAC8D;;AAGlE,6BAA2B;AAC7B;AAKA,IAAM,gDAAgD,MAAK;AACzD,6BAA2B;AAC7B;AAEA,IAAM,kBAAoC,CAAC,MAAM,MAAM,SAAQ;AAC7D,SAAO,yBAAyB,MAAM,MAAM,IAAI;AAClD;AAIA,IAAM,uBAAuB;AAM7B,IAAM,SAAS,OAAO,OAAO,KAAK,OAAM,CAAE,EAAE,MAAM,CAAC,CAAC;AAGpD,IAAM,cAAc,MAAM;AAI1B,IAAM,aAAa,IAAI,WAAW;AAElC,IAAM,IACJR,cAAaC,QAAO,aAAa,SAC5B;EACC,mBAAgB;AACd,WAAO,CAAA;EACT;IAEF;AAGN,IAAM,eAAe,MAAM,EAAE,cAAc,EAAE;AAI7C,IAAM,cAAc,CAAC,UACnB,UAAU,QAAS,OAAO,SAAS,YAAY,OAAO,SAAS;AACjE,IAAM,UAAU,MAAM;AACtB,IAAM,aAAa,CAAC,UAClB,QAAQ,KAAK;AAEb,QAAQ,UAAa,QAAb,UAAK,SAAA,SAAL,MAAgB,OAAO,QAAQ,OAAM;AAE/C,IAAM,aAAa;;AACnB,IAAM,kBAAkB;;AACxB,IAAM,YAAY;AAgBlB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,WAAW;AACjB,IAAM,mBAAmB;AAEzB,IAAM,kBAAkB;AAIxB,IAAM,mBAAmB;AAwBzB,IAAM,cAAc,IAAI,OACtB,KAAK,UAAU,OAAO,SAAS,MAAM,UAAU,KAAK,UAAU,OAAO,eAAe,gBACpF,GAAG;AAEL,IAAM,eAAe;AACrB,IAAM,iBAAiB;AACvB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AAEnB,IAAM,0BAA0B;AAChC,IAAM,0BAA0B;AAOhC,IAAM,iBAAiB;AAGvB,IAAM,cAAc;AACpB,IAAM,aAAa;AAMnB,IAAM,iBAAiB;AACvB,IAAM,aAAa;AACnB,IAAM,gBAAgB;AACtB,IAAM,yBAAyB;AAC/B,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,eAAe;AAgDrB,IAAM,MACJ,CAAuB,SACvB,CAAC,YAAkC,WAAwC;AAIzE,MAAIF,aAAY,QAAQ,KAAK,CAAC,MAAM,MAAM,MAAS,GAAG;AACpD,YAAQ,KACN,kGAC8D;;AAGlE,SAAO;;IAEL,CAAC,YAAY,GAAG;IAChB;IACA;;AAEJ;AAeK,IAAM,OAAO,IAAI,WAAW;AAyB5B,IAAM,MAAM,IAAI,UAAU;AAM1B,IAAM,WAAW,OAAO,IAAI,cAAc;AAqB1C,IAAM,UAAU,OAAO,IAAI,aAAa;AAS/C,IAAM,gBAAgB,oBAAI,QAAO;AAqCjC,IAAM,SAAS,EAAE,iBACf,GACA,KACA,MACA,KAAK;AAGP,IAAI,2BAA6C;AAkBjD,SAAS,wBACP,KACA,eAAqB;AAOrB,MAAI,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,eAAe,KAAK,GAAG;AACrD,QAAI,UAAU;AACd,QAAIA,WAAU;AACZ,gBAAU;;;;;;;;;;UAWP,KAAI,EACJ,QAAQ,SAAS,IAAI;;AAE1B,UAAM,IAAI,MAAM,OAAO;;AAEzB,SAAO,WAAW,SACd,OAAO,WAAW,aAAa,IAC9B;AACP;AAcA,IAAM,kBAAkB,CACtB,SACA,SAC4C;AAO5C,QAAM,IAAI,QAAQ,SAAS;AAI3B,QAAM,YAAuC,CAAA;AAC7C,MAAIU,QAAO,SAAS,aAAa,UAAU;AAK3C,MAAI;AAIJ,MAAI,QAAQ;AAEZ,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAM,IAAI,QAAQ,CAAC;AAMnB,QAAI,mBAAmB;AACvB,QAAI;AACJ,QAAI,YAAY;AAChB,QAAI;AAIJ,WAAO,YAAY,EAAE,QAAQ;AAE3B,YAAM,YAAY;AAClB,cAAQ,MAAM,KAAK,CAAC;AACpB,UAAI,UAAU,MAAM;AAClB;;AAEF,kBAAY,MAAM;AAClB,UAAI,UAAU,cAAc;AAC1B,YAAI,MAAM,aAAa,MAAM,OAAO;AAClC,kBAAQ;mBACC,MAAM,aAAa,MAAM,QAAW;AAE7C,kBAAQ;mBACC,MAAM,QAAQ,MAAM,QAAW;AACxC,cAAI,eAAe,KAAK,MAAM,QAAQ,CAAC,GAAG;AAGxC,8BAAkB,IAAI,OAAO,KAAK,MAAM,QAAQ,CAAC,IAAI,GAAG;;AAE1D,kBAAQ;mBACC,MAAM,gBAAgB,MAAM,QAAW;AAChD,cAAIV,WAAU;AACZ,kBAAM,IAAI,MACR,kJACsE;;AAG1E,kBAAQ;;iBAED,UAAU,aAAa;AAChC,YAAI,MAAM,YAAY,MAAM,KAAK;AAG/B,kBAAQ,oBAAe,QAAf,oBAAe,SAAf,kBAAmB;AAG3B,6BAAmB;mBACV,MAAM,cAAc,MAAM,QAAW;AAE9C,6BAAmB;eACd;AACL,6BAAmB,MAAM,YAAY,MAAM,iBAAiB,EAAE;AAC9D,qBAAW,MAAM,cAAc;AAC/B,kBACE,MAAM,UAAU,MAAM,SAClB,cACA,MAAM,UAAU,MAAM,MACtB,0BACA;;iBAGR,UAAU,2BACV,UAAU,yBACV;AACA,gBAAQ;iBACC,UAAU,mBAAmB,UAAU,kBAAkB;AAClE,gBAAQ;aACH;AAGL,gBAAQ;AACR,0BAAkB;;;AAItB,QAAIA,WAAU;AAIZ,cAAQ,OACN,qBAAqB,MACnB,UAAU,eACV,UAAU,2BACV,UAAU,yBACZ,0BAA0B;;AAiB9B,UAAM,MACJ,UAAU,eAAe,QAAQ,IAAI,CAAC,EAAE,WAAW,IAAI,IAAI,MAAM;AACnE,IAAAU,SACE,UAAU,eACN,IAAI,aACJ,oBAAoB,KACnB,UAAU,KAAK,QAAS,GACzB,EAAE,MAAM,GAAG,gBAAgB,IACzB,uBACA,EAAE,MAAM,gBAAgB,KAC1B,SACA,MACA,IACA,UACC,qBAAqB,MAAM,UAAU,KAAK,MAAS,GAAG,KAAK;;AAGpE,QAAM,aACJA,SAAQ,QAAQ,CAAC,KAAK,UAAU,SAAS,aAAa,WAAW;AAGnE,SAAO,CAAC,wBAAwB,SAAS,UAAU,GAAG,SAAS;AACjE;AAIA,IAAM,WAAN,MAAM,UAAQ;EAMZ,YAEE,EAAC,SAAS,CAAC,YAAY,GAAG,KAAI,GAC9B,SAAuB;AALzB,SAAA,QAA6B,CAAA;AAO3B,QAAI;AACJ,QAAI,YAAY;AAChB,QAAI,gBAAgB;AACpB,UAAM,YAAY,QAAQ,SAAS;AACnC,UAAM,QAAQ,KAAK;AAGnB,UAAM,CAACA,OAAM,SAAS,IAAI,gBAAgB,SAAS,IAAI;AACvD,SAAK,KAAK,UAAS,cAAcA,OAAM,OAAO;AAC9C,WAAO,cAAc,KAAK,GAAG;AAG7B,QAAI,SAAS,YAAY;AACvB,YAAM,UAAU,KAAK,GAAG;AACxB,YAAM,aAAa,QAAQ;AAC3B,iBAAW,OAAM;AACjB,cAAQ,OAAO,GAAG,WAAW,UAAU;;AAIzC,YAAQ,OAAO,OAAO,SAAQ,OAAQ,QAAQ,MAAM,SAAS,WAAW;AACtE,UAAI,KAAK,aAAa,GAAG;AACvB,YAAIV,WAAU;AACZ,gBAAMW,OAAO,KAAiB;AAK9B,cACE,2BAA4B,KAAKA,IAAG,KACnC,KAAiB,UAAU,SAAS,MAAM,GAC3C;AACA,kBAAM,IACJ,0CAA0CA,IAAG,sDACMA,IAAG;AAExD,gBAAIA,SAAQ,YAAY;AACtB,oBAAM,IAAI,MAAM,CAAC;;AACZ,cAAAP,cAAa,IAAI,CAAC;;;AAM7B,YAAK,KAAiB,cAAa,GAAI;AAIrC,gBAAM,gBAAgB,CAAA;AACtB,qBAAW,QAAS,KAAiB,kBAAiB,GAAI;AAQxD,gBACE,KAAK,SAAS,oBAAoB,KAClC,KAAK,WAAW,MAAM,GACtB;AACA,oBAAM,WAAW,UAAU,eAAe;AAC1C,4BAAc,KAAK,IAAI;AACvB,kBAAI,aAAa,QAAW;AAE1B,sBAAM,QAAS,KAAiB,aAC9B,SAAS,YAAW,IAAK,oBAAoB;AAE/C,sBAAM,UAAU,MAAM,MAAM,MAAM;AAClC,sBAAM,IAAI,eAAe,KAAK,QAAQ;AACtC,sBAAM,KAAK;kBACT,MAAM;kBACN,OAAO;kBACP,MAAM,EAAE,CAAC;kBACT,SAAS;kBACT,MACE,EAAE,CAAC,MAAM,MACL,eACA,EAAE,CAAC,MAAM,MACT,uBACA,EAAE,CAAC,MAAM,MACT,YACA;iBACP;qBACI;AACL,sBAAM,KAAK;kBACT,MAAM;kBACN,OAAO;iBACR;;;;AAIP,qBAAW,QAAQ,eAAe;AAC/B,iBAAiB,gBAAgB,IAAI;;;AAK1C,YAAI,eAAe,KAAM,KAAiB,OAAO,GAAG;AAIlD,gBAAMQ,WAAW,KAAiB,YAAa,MAAM,MAAM;AAC3D,gBAAM,YAAYA,SAAQ,SAAS;AACnC,cAAI,YAAY,GAAG;AAChB,iBAAiB,cAAcH,gBAC3BA,cAAa,cACd;AAMJ,qBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AACjC,mBAAiB,OAAOG,SAAQ,CAAC,GAAG,aAAY,CAAE;AAEnD,qBAAO,SAAQ;AACf,oBAAM,KAAK,EAAC,MAAM,YAAY,OAAO,EAAE,UAAS,CAAC;;AAKlD,iBAAiB,OAAOA,SAAQ,SAAS,GAAG,aAAY,CAAE;;;iBAGtD,KAAK,aAAa,GAAG;AAC9B,cAAMC,QAAQ,KAAiB;AAC/B,YAAIA,UAAS,aAAa;AACxB,gBAAM,KAAK,EAAC,MAAM,YAAY,OAAO,UAAS,CAAC;eAC1C;AACL,cAAI,IAAI;AACR,kBAAQ,IAAK,KAAiB,KAAK,QAAQ,QAAQ,IAAI,CAAC,OAAO,IAAI;AAGjE,kBAAM,KAAK,EAAC,MAAM,cAAc,OAAO,UAAS,CAAC;AAEjD,iBAAK,OAAO,SAAS;;;;AAI3B;;AAKF,IAAAV,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;MACd,MAAM;MACN,UAAU;MACV,kBAAkB,KAAK;MACvB,OAAO,KAAK;MACZ;KACD;EACH;;;EAIA,OAAO,cAAcO,OAAmB,UAAwB;AAC9D,UAAM,KAAK,EAAE,cAAc,UAAU;AACrC,OAAG,YAAYA;AACf,WAAO;EACT;;AAgBF,SAAS,iBACP,MACA,OACA,SAA0B,MAC1B,gBAAuB;;;AAIvB,MAAI,UAAU,UAAU;AACtB,WAAO;;AAET,MAAI,mBACF,mBAAmB,UACfL,MAAC,OAAyB,kBAAY,QAAAA,QAAA,SAAA,SAAAA,IAAG,cAAc,IACtD,OAA+C;AACtD,QAAM,2BAA2B,YAAY,KAAK,IAC9C;;IAEC,MAA0B,iBAAiB;;AAChD,OAAI,qBAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAkB,iBAAgB,0BAA0B;AAE9D,KAAAE,MAAA,qBAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAmB,oCAAoC,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,kBAAG,KAAK;AAChE,QAAI,6BAA6B,QAAW;AAC1C,yBAAmB;WACd;AACL,yBAAmB,IAAI,yBAAyB,IAAgB;AAChE,uBAAiB,aAAa,MAAM,QAAQ,cAAc;;AAE5D,QAAI,mBAAmB,QAAW;AAChC,QAAAC,OAAAM,MAAE,QAAyB,kBAAY,QAAAN,QAAA,SAAAA,MAAAM,IAAZ,eAAiB,CAAA,GAAI,cAAc,IAC5D;WACG;AACJ,aAAiC,cAAc;;;AAGpD,MAAI,qBAAqB,QAAW;AAClC,YAAQ,iBACN,MACA,iBAAiB,UAAU,MAAO,MAA0B,MAAM,GAClE,kBACA,cAAc;;AAGlB,SAAO;AACT;AAOA,IAAM,mBAAN,MAAsB;EASpB,YAAY,UAAoB,QAAiB;AAPjD,SAAA,UAAmC,CAAA;AAKnC,SAAA,2BAAiD;AAG/C,SAAK,aAAa;AAClB,SAAK,WAAW;EAClB;;EAGA,IAAI,aAAU;AACZ,WAAO,KAAK,SAAS;EACvB;;EAGA,IAAI,gBAAa;AACf,WAAO,KAAK,SAAS;EACvB;;;EAIA,OAAO,SAAkC;;AACvC,UAAM,EACJ,IAAI,EAAC,QAAO,GACZ,MAAY,IACV,KAAK;AACT,UAAM,aAAYT,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,mBAAa,QAAAA,QAAA,SAAAA,MAAI,GAAG,WAAW,SAAS,IAAI;AACvE,WAAO,cAAc;AAErB,QAAI,OAAO,OAAO,SAAQ;AAC1B,QAAI,YAAY;AAChB,QAAI,YAAY;AAChB,QAAI,eAAe,MAAM,CAAC;AAE1B,WAAO,iBAAiB,QAAW;AACjC,UAAI,cAAc,aAAa,OAAO;AACpC,YAAI;AACJ,YAAI,aAAa,SAAS,YAAY;AACpC,iBAAO,IAAI,UACT,MACA,KAAK,aACL,MACA,OAAO;mBAEA,aAAa,SAAS,gBAAgB;AAC/C,iBAAO,IAAI,aAAa,KACtB,MACA,aAAa,MACb,aAAa,SACb,MACA,OAAO;mBAEA,aAAa,SAAS,cAAc;AAC7C,iBAAO,IAAI,YAAY,MAAqB,MAAM,OAAO;;AAE3D,aAAK,QAAQ,KAAK,IAAI;AACtB,uBAAe,MAAM,EAAE,SAAS;;AAElC,UAAI,eAAc,iBAAY,QAAZ,iBAAY,SAAA,SAAZ,aAAc,QAAO;AACrC,eAAO,OAAO,SAAQ;AACtB;;;AAMJ,WAAO,cAAc;AACrB,WAAO;EACT;EAEA,QAAQ,QAAsB;AAC5B,QAAI,IAAI;AACR,eAAW,QAAQ,KAAK,SAAS;AAC/B,UAAI,SAAS,QAAW;AACtB,QAAAF,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;UACd,MAAM;UACN;UACA,OAAO,OAAO,CAAC;UACf,YAAY;UACZ;UACA,kBAAkB;SACnB;AACD,YAAK,KAAuB,YAAY,QAAW;AAChD,eAAuB,WAAW,QAAQ,MAAuB,CAAC;AAInE,eAAM,KAAuB,QAAS,SAAS;eAC1C;AACL,eAAK,WAAW,OAAO,CAAC,CAAC;;;AAG7B;;EAEJ;;AA8CF,IAAM,YAAN,MAAM,WAAS;EA4Cb,YACE,WACA,SACA,QACA,SAAkC;;AA/C3B,SAAA,OAAO;AAEhB,SAAA,mBAA4B;AA+B5B,SAAA,2BAAiD;AAgB/C,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,UAAU;AAIf,SAAK,iBAAgBE,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,iBAAW,QAAAA,QAAA,SAAAA,MAAI;AAC7C,QAAI,6BAA6B;AAE/B,WAAK,iBAAiB;;EAE1B;;EAtCA,IAAI,gBAAa;;AAIf,YAAOE,OAAAF,MAAA,KAAK,cAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAE,mBAAa,QAAAE,QAAA,SAAAA,MAAI,KAAK;EAC9C;;;;;;;;;;;;;;;;;;;EAqDA,IAAI,aAAU;AACZ,QAAI,aAAmB,KAAK,KAAK,WAAW,EAAE;AAC9C,UAAM,SAAS,KAAK;AACpB,QACE,WAAW,WACX,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,cAAa,IACzB;AAIA,mBAAc,OAAwC;;AAExD,WAAO;EACT;;;;;EAMA,IAAI,YAAS;AACX,WAAO,KAAK;EACd;;;;;EAMA,IAAI,UAAO;AACT,WAAO,KAAK;EACd;EAEA,WAAW,OAAgB,kBAAmC,MAAI;;AAChE,QAAIP,aAAY,KAAK,eAAe,MAAM;AACxC,YAAM,IAAI,MACR,0UAA0U;;AAG9U,YAAQ,iBAAiB,MAAM,OAAO,eAAe;AACrD,QAAI,YAAY,KAAK,GAAG;AAItB,UAAI,UAAU,WAAW,SAAS,QAAQ,UAAU,IAAI;AACtD,YAAI,KAAK,qBAAqB,SAAS;AACrC,UAAAG,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;YACd,MAAM;YACN,OAAO,KAAK;YACZ,KAAK,KAAK;YACV,QAAQ,KAAK;YACb,SAAS,KAAK;WACf;AACD,eAAK,QAAO;;AAEd,aAAK,mBAAmB;iBACf,UAAU,KAAK,oBAAoB,UAAU,UAAU;AAChE,aAAK,YAAY,KAAK;;eAGd,MAAyB,YAAY,MAAM,QAAW;AAChE,WAAK,sBAAsB,KAAuB;eACxC,MAAe,aAAa,QAAW;AACjD,UAAIH,eAAYK,MAAA,KAAK,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE,UAAS,OAAO;AAC5C,aAAK,YACH,6GACuD;AAEzD,gBAAQ,KACN,yCACA,OACA,oEACA,8DACA,oEACA,2CAA2C;AAE7C;;AAEF,WAAK,YAAY,KAAa;eACrB,WAAW,KAAK,GAAG;AAC5B,WAAK,gBAAgB,KAAK;WACrB;AAEL,WAAK,YAAY,KAAK;;EAE1B;EAEQ,QAAwB,MAAO;AACrC,WAAO,KAAK,KAAK,KAAK,WAAW,EAAE,UAAW,EAAE,aAC9C,MACA,KAAK,SAAS;EAElB;EAEQ,YAAY,OAAW;;AAC7B,QAAI,KAAK,qBAAqB,OAAO;AACnC,WAAK,QAAO;AACZ,UACE,+BACA,6BAA6B,eAC7B;AACA,cAAM,kBAAiBA,MAAA,KAAK,YAAY,gBAAU,QAAAA,QAAA,SAAA,SAAAA,IAAE;AACpD,YAAI,mBAAmB,WAAW,mBAAmB,UAAU;AAC7D,cAAI,UAAU;AACd,cAAIL,WAAU;AACZ,gBAAI,mBAAmB,SAAS;AAC9B,wBACE;mBAOG;AACL,wBACE;;;AAKN,gBAAM,IAAI,MAAM,OAAO;;;AAG3B,MAAAG,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;QACd,MAAM;QACN,OAAO,KAAK;QACZ,QAAQ,KAAK;QACb;QACA,SAAS,KAAK;OACf;AACD,WAAK,mBAAmB,KAAK,QAAQ,KAAK;;EAE9C;EAEQ,YAAY,OAAc;AAIhC,QACE,KAAK,qBAAqB,WAC1B,YAAY,KAAK,gBAAgB,GACjC;AACA,YAAM,OAAO,KAAK,KAAK,WAAW,EAAE;AACpC,UAAI,6BAA6B;AAC/B,YAAI,KAAK,mBAAmB,QAAW;AACrC,eAAK,iBAAiB,gBAAgB,MAAM,QAAQ,UAAU;;AAEhE,gBAAQ,KAAK,eAAe,KAAK;;AAEnC,MAAAA,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;QACd,MAAM;QACN;QACA;QACA,SAAS,KAAK;OACf;AACA,WAAc,OAAO;WACjB;AACL,UAAI,6BAA6B;AAC/B,cAAM,WAAW,EAAE,eAAe,EAAE;AACpC,aAAK,YAAY,QAAQ;AAKzB,YAAI,KAAK,mBAAmB,QAAW;AACrC,eAAK,iBAAiB,gBAAgB,UAAU,QAAQ,UAAU;;AAEpE,gBAAQ,KAAK,eAAe,KAAK;AACjC,QAAAA,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;UACd,MAAM;UACN,MAAM;UACN;UACA,SAAS,KAAK;SACf;AACD,iBAAS,OAAO;aACX;AACL,aAAK,YAAY,EAAE,eAAe,KAAe,CAAC;AAClD,QAAAA,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;UACd,MAAM;UACN,MAAM,KAAK,KAAK,WAAW,EAAE;UAC7B;UACA,SAAS,KAAK;SACf;;;AAGL,SAAK,mBAAmB;EAC1B;EAEQ,sBACN,QAA+C;;AAG/C,UAAM,EAAC,QAAQ,CAAC,YAAY,GAAG,KAAI,IAAI;AAKvC,UAAM,WACJ,OAAO,SAAS,WACZ,KAAK,cAAc,MAAwB,KAC1C,KAAK,OAAO,WACV,KAAK,KAAK,SAAS,cAClB,wBAAwB,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,GACzC,KAAK,OAAO,IAEhB;AAEN,UAAIE,MAAC,KAAK,sBAAqC,QAAAA,QAAA,SAAA,SAAAA,IAAE,gBAAe,UAAU;AACxE,MAAAF,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;QACd,MAAM;QACN;QACA,UAAU,KAAK;QACf,OAAQ,KAAK,iBAAsC;QACnD,SAAS,KAAK;QACd;OACD;AACA,WAAK,iBAAsC,QAAQ,MAAM;WACrD;AACL,YAAM,WAAW,IAAI,iBAAiB,UAAsB,IAAI;AAChE,YAAM,WAAW,SAAS,OAAO,KAAK,OAAO;AAC7C,MAAAA,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;QACd,MAAM;QACN;QACA;QACA,OAAO,SAAS;QAChB,SAAS,KAAK;QACd;QACA;OACD;AACD,eAAS,QAAQ,MAAM;AACvB,MAAAA,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;QACd,MAAM;QACN;QACA;QACA,OAAO,SAAS;QAChB,SAAS,KAAK;QACd;QACA;OACD;AACD,WAAK,YAAY,QAAQ;AACzB,WAAK,mBAAmB;;EAE5B;;;EAIA,cAAc,QAAsB;AAClC,QAAI,WAAW,cAAc,IAAI,OAAO,OAAO;AAC/C,QAAI,aAAa,QAAW;AAC1B,oBAAc,IAAI,OAAO,SAAU,WAAW,IAAI,SAAS,MAAM,CAAE;;AAErE,WAAO;EACT;EAEQ,gBAAgB,OAAwB;AAW9C,QAAI,CAAC,QAAQ,KAAK,gBAAgB,GAAG;AACnC,WAAK,mBAAmB,CAAA;AACxB,WAAK,QAAO;;AAKd,UAAM,YAAY,KAAK;AACvB,QAAI,YAAY;AAChB,QAAI;AAEJ,eAAW,QAAQ,OAAO;AACxB,UAAI,cAAc,UAAU,QAAQ;AAKlC,kBAAU,KACP,WAAW,IAAI,WACd,KAAK,QAAQ,aAAY,CAAE,GAC3B,KAAK,QAAQ,aAAY,CAAE,GAC3B,MACA,KAAK,OAAO,CACZ;aAEC;AAEL,mBAAW,UAAU,SAAS;;AAEhC,eAAS,WAAW,IAAI;AACxB;;AAGF,QAAI,YAAY,UAAU,QAAQ;AAEhC,WAAK,QACH,YAAY,KAAK,SAAS,SAAU,EAAE,aACtC,SAAS;AAGX,gBAAU,SAAS;;EAEvB;;;;;;;;;;;;EAaA,QACE,QAA0B,KAAK,KAAK,WAAW,EAAE,aACjD,MAAa;;AAEb,KAAAE,MAAA,KAAK,+BAAyB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,MAAG,OAAO,MAAM,IAAI;AAClD,WAAO,SAAS,UAAU,KAAK,WAAW;AACxC,YAAM,IAAI,KAAK,KAAM,EAAE;AACtB,WAAK,KAAM,EAAc,OAAM;AAChC,cAAQ;;EAEZ;;;;;;;;EAQA,aAAa,aAAoB;;AAC/B,QAAI,KAAK,aAAa,QAAW;AAC/B,WAAK,gBAAgB;AACrB,OAAAA,MAAA,KAAK,+BAAyB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,MAAG,WAAW;eACnCL,WAAU;AACnB,YAAM,IAAI,MACR,8EACoC;;EAG1C;;AA2BF,IAAM,gBAAN,MAAmB;EAoCjB,YACE,SACA,MACA,SACA,QACA,SAAkC;AAxC3B,SAAA,OAAO;AAgBhB,SAAA,mBAA6C;AAM7C,SAAA,2BAAiD;AAoB/C,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,QAAI,QAAQ,SAAS,KAAK,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,MAAM,IAAI;AAChE,WAAK,mBAAmB,IAAI,MAAM,QAAQ,SAAS,CAAC,EAAE,KAAK,IAAI,OAAM,CAAE;AACvE,WAAK,UAAU;WACV;AACL,WAAK,mBAAmB;;AAE1B,QAAI,6BAA6B;AAC/B,WAAK,aAAa;;EAEtB;EA7BA,IAAI,UAAO;AACT,WAAO,KAAK,QAAQ;EACtB;;EAGA,IAAI,gBAAa;AACf,WAAO,KAAK,SAAS;EACvB;;;;;;;;;;;;;;;;;;;;;;;EA8CA,WACE,OACA,kBAAmC,MACnC,YACA,UAAkB;AAElB,UAAM,UAAU,KAAK;AAGrB,QAAI,SAAS;AAEb,QAAI,YAAY,QAAW;AAEzB,cAAQ,iBAAiB,MAAM,OAAO,iBAAiB,CAAC;AACxD,eACE,CAAC,YAAY,KAAK,KACjB,UAAU,KAAK,oBAAoB,UAAU;AAChD,UAAI,QAAQ;AACV,aAAK,mBAAmB;;WAErB;AAEL,YAAM,SAAS;AACf,cAAQ,QAAQ,CAAC;AAEjB,UAAI,GAAGe;AACP,WAAK,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,KAAK;AACvC,QAAAA,KAAI,iBAAiB,MAAM,OAAO,aAAc,CAAC,GAAG,iBAAiB,CAAC;AAEtE,YAAIA,OAAM,UAAU;AAElB,UAAAA,KAAK,KAAK,iBAAoC,CAAC;;AAEjD,mBAAA,SACE,CAAC,YAAYA,EAAC,KAAKA,OAAO,KAAK,iBAAoC,CAAC;AACtE,YAAIA,OAAM,SAAS;AACjB,kBAAQ;mBACC,UAAU,SAAS;AAC5B,oBAAUA,OAAC,QAADA,OAAC,SAADA,KAAK,MAAM,QAAQ,IAAI,CAAC;;AAInC,aAAK,iBAAoC,CAAC,IAAIA;;;AAGnD,QAAI,UAAU,CAAC,UAAU;AACvB,WAAK,aAAa,KAAK;;EAE3B;;EAGA,aAAa,OAAc;AACzB,QAAI,UAAU,SAAS;AACpB,WAAK,KAAK,OAAO,EAAc,gBAAgB,KAAK,IAAI;WACpD;AACL,UAAI,6BAA6B;AAC/B,YAAI,KAAK,eAAe,QAAW;AACjC,eAAK,aAAa,yBAChB,KAAK,SACL,KAAK,MACL,WAAW;;AAGf,gBAAQ,KAAK,WAAW,UAAK,QAAL,UAAK,SAAL,QAAS,EAAE;;AAErC,MAAAZ,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;QACd,MAAM;QACN,SAAS,KAAK;QACd,MAAM,KAAK;QACX;QACA,SAAS,KAAK;OACf;AACA,WAAK,KAAK,OAAO,EAAc,aAC9B,KAAK,MACJ,UAAK,QAAL,UAAK,SAAL,QAAS,EAAa;;EAG7B;;AAIF,IAAM,eAAN,cAA2B,cAAa;EAAxC,cAAA;;AACoB,SAAA,OAAO;EAwB3B;;EArBW,aAAa,OAAc;AAClC,QAAI,6BAA6B;AAC/B,UAAI,KAAK,eAAe,QAAW;AACjC,aAAK,aAAa,yBAChB,KAAK,SACL,KAAK,MACL,UAAU;;AAGd,cAAQ,KAAK,WAAW,KAAK;;AAE/B,IAAAA,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;MACd,MAAM;MACN,SAAS,KAAK;MACd,MAAM,KAAK;MACX;MACA,SAAS,KAAK;KACf;AAEA,SAAK,QAAgB,KAAK,IAAI,IAAI,UAAU,UAAU,SAAY;EACrE;;AAOF,IAAMa,kCAAiCP,gBAClCA,cAAa,cACd;AAGJ,IAAM,uBAAN,cAAmC,cAAa;EAAhD,cAAA;;AACoB,SAAA,OAAO;EAoB3B;;EAjBW,aAAa,OAAc;AAClC,IAAAN,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;MACd,MAAM;MACN,SAAS,KAAK;MACd,MAAM,KAAK;MACX,OAAO,CAAC,EAAE,SAAS,UAAU;MAC7B,SAAS,KAAK;KACf;AACD,QAAI,SAAS,UAAU,SAAS;AAC7B,WAAK,KAAK,OAAO,EAAc,aAC9B,KAAK,MACLa,+BAA8B;WAE3B;AACJ,WAAK,KAAK,OAAO,EAAc,gBAAgB,KAAK,IAAI;;EAE7D;;AAkBF,IAAM,YAAN,cAAwB,cAAa;EAGnC,YACE,SACA,MACA,SACA,QACA,SAAkC;AAElC,UAAM,SAAS,MAAM,SAAS,QAAQ,OAAO;AAT7B,SAAA,OAAO;AAWvB,QAAIhB,aAAY,KAAK,YAAY,QAAW;AAC1C,YAAM,IAAI,MACR,QAAQ,QAAQ,SAAS,gBAAgB,IAAI,8HAEF;;EAGjD;;;;EAKS,WACP,aACA,kBAAmC,MAAI;;AAEvC,mBACEK,MAAA,iBAAiB,MAAM,aAAa,iBAAiB,CAAC,OAAC,QAAAA,QAAA,SAAAA,MAAI;AAC7D,QAAI,gBAAgB,UAAU;AAC5B;;AAEF,UAAM,cAAc,KAAK;AAIzB,UAAM,uBACH,gBAAgB,WAAW,gBAAgB,WAC3C,YAAyC,YACvC,YAAyC,WAC3C,YAAyC,SACvC,YAAyC,QAC3C,YAAyC,YACvC,YAAyC;AAI9C,UAAM,oBACJ,gBAAgB,YACf,gBAAgB,WAAW;AAE9B,IAAAF,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;MACd,MAAM;MACN,SAAS,KAAK;MACd,MAAM,KAAK;MACX,OAAO;MACP,SAAS,KAAK;MACd,gBAAgB;MAChB,aAAa;MACb;KACD;AACD,QAAI,sBAAsB;AACxB,WAAK,QAAQ,oBACX,KAAK,MACL,MACA,WAAuC;;AAG3C,QAAI,mBAAmB;AAIrB,WAAK,QAAQ,iBACX,KAAK,MACL,MACA,WAAuC;;AAG3C,SAAK,mBAAmB;EAC1B;EAEA,YAAY,OAAY;;AACtB,QAAI,OAAO,KAAK,qBAAqB,YAAY;AAC/C,WAAK,iBAAiB,MAAKI,OAAAF,MAAA,KAAK,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE,UAAI,QAAAE,QAAA,SAAAA,MAAI,KAAK,SAAS,KAAK;WAC/D;AACJ,WAAK,iBAAyC,YAAY,KAAK;;EAEpE;;AAIF,IAAM,cAAN,MAAiB;EAiBf,YACS,SACP,QACA,SAAkC;AAF3B,SAAA,UAAA;AAjBA,SAAA,OAAO;AAYhB,SAAA,2BAAiD;AAS/C,SAAK,WAAW;AAChB,SAAK,UAAU;EACjB;;EAGA,IAAI,gBAAa;AACf,WAAO,KAAK,SAAS;EACvB;EAEA,WAAW,OAAc;AACvB,IAAAJ,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;MACd,MAAM;MACN,SAAS,KAAK;MACd;MACA,SAAS,KAAK;KACf;AACD,qBAAiB,MAAM,KAAK;EAC9B;;AAyCF,IAAMc,mBAAkBC,YACpBC,QAAO,gCACPA,QAAO;AACXF,qBAAe,QAAfA,qBAAe,SAAA,SAAfA,iBAAkB,UAAU,SAAS;EAIrCG,MAACD,QAAO,qBAAe,QAAAC,QAAA,SAAAA,MAAtBD,QAAO,kBAAoB,CAAA,GAAI,KAAK,OAAO;AAC5C,IAAID,aAAYC,QAAO,gBAAgB,SAAS,GAAG;AACjD,EAAAE,cACE,qBACA,gFACiD;;AA6B9C,IAAM,SAAS,CACpB,OACA,WACA,YACY;;AACZ,MAAIH,aAAY,aAAa,MAAM;AAKjC,UAAM,IAAI,UAAU,2CAA2C,SAAS,EAAE;;AAE5E,QAAM,WAAWA,YAAW,qBAAqB;AACjD,QAAM,iBAAgBI,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,kBAAY,QAAAA,QAAA,SAAAA,MAAI;AAG/C,MAAI,OAAmB,cAAsB,YAAY;AACzD,EAAAC,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;IACd,MAAM;IACN,IAAI;IACJ;IACA;IACA;IACA;GACD;AACD,MAAI,SAAS,QAAW;AACtB,UAAM,WAAUC,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,kBAAY,QAAAA,QAAA,SAAAA,MAAI;AAGxC,kBAAsB,YAAY,IAAI,OAAO,IAAI,UAChD,UAAU,aAAa,aAAY,GAAI,OAAO,GAC9C,SACA,QACA,YAAO,QAAP,YAAO,SAAP,UAAW,CAAA,CAAE;;AAGjB,OAAK,WAAW,KAAK;AACrB,EAAAD,mBAAa,QAAbA,mBAAa,SAAA,SAAbA,eAAgB;IACd,MAAM;IACN,IAAI;IACJ;IACA;IACA;IACA;GACD;AACD,SAAO;AACT;AAEA,IAAI,6BAA6B;AAC/B,SAAO,eAAe;AACtB,SAAO,kBAAkB;AACzB,MAAIL,WAAU;AACZ,WAAO,gDACL;;;;;;;;AClkEN,IAAMO,YAAW;AAEjB,IAAIC;AAEJ,IAAID,WAAU;AAGZ,QAAM,kBACJE,MAAC,WAAW,uBAAiB,QAAAA,QAAA,SAAAA,MAA5B,WAAW,oBAAsB,oBAAI,IAAG;AAG3C,EAAAD,gBAAe,CAAC,MAAcE,aAAmB;AAC/C,IAAAA,YAAW,4BAA4B,IAAI;AAC3C,QAAI,CAAC,eAAe,IAAIA,QAAO,GAAG;AAChC,cAAQ,KAAKA,QAAO;AACpB,qBAAe,IAAIA,QAAO;;EAE9B;;AAYI,IAAO,aAAP,cAA0B,gBAAe;EAA/C,cAAA;;AAgBW,SAAA,gBAA+B,EAAC,MAAM,KAAI;AAE3C,SAAA,cAAoC;EA8F9C;;;;EAzFqB,mBAAgB;;;AACjC,UAAM,aAAa,MAAM,iBAAgB;AAMzC,KAAAD,OAAAE,MAAA,KAAK,eAAc,kBAAY,QAAAF,QAAA,SAAAA,MAAAE,IAAZ,eAAiB,WAAY;AAChD,WAAO;EACT;;;;;;;;EASmB,OAAO,mBAAiC;AAIzD,UAAM,QAAQ,KAAK,OAAM;AACzB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,cAAc,cAAc,KAAK;;AAExC,UAAM,OAAO,iBAAiB;AAC9B,SAAK,cAAc,OAAO,OAAO,KAAK,YAAY,KAAK,aAAa;EACtE;;;;;;;;;;;;;;;;;;;;;EAsBS,oBAAiB;;AACxB,UAAM,kBAAiB;AACvB,KAAAF,MAAA,KAAK,iBAAW,QAAAA,QAAA,SAAA,SAAAA,IAAE,aAAa,IAAI;EACrC;;;;;;;;;;;;;;;;;;;;EAqBS,uBAAoB;;AAC3B,UAAM,qBAAoB;AAC1B,KAAAA,MAAA,KAAK,iBAAW,QAAAA,QAAA,SAAA,SAAAA,IAAE,aAAa,KAAK;EACtC;;;;;;;;EASU,SAAM;AACd,WAAO;EACT;;AAvG0B,WAAC,WAAW,IAAI;AAGnC,WAAC,eAAe,IAAI;CAwG7BE,MAAA,WAAW,8BAAwB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,YAAG,EAAC,WAAU,CAAC;AAGlD,IAAMC,mBAAkBL,YACpB,WAAW,mCACX,WAAW;AACfK,qBAAe,QAAfA,qBAAe,SAAA,SAAfA,iBAAkB,EAAC,WAAU,CAAC;AAG9B,IAAIL,WAAU;AAIX,aAAmB,UAAU,IAAI,WAAA;AAChC,UAAMM,aAAa,gBAAwB,SAAS,KAAK,IAAI;AAC7D,QAAI,CAACA,YAAW;AACd,aAAO;;AAET,UAAM,uBAAuB,CAAC,KAAU,MAAc,UAAU,UAAS;AACvE,UAAI,IAAI,eAAe,IAAI,GAAG;AAC5B,cAAM,YAAY,OAAO,QAAQ,aAAa,MAAM,IAAI,aACrD;AACH,QAAAL,cACE,UAAU,gBAAgB,eAC1B,KAAK,IAAI,8BAA8B,QAAQ,iBACjC,UAAU,YAAY,SAAS,iCACX;;IAGxC;AACA,yBAAqB,MAAM,QAAQ;AACnC,yBAAqB,MAAM,aAAa,IAAI;AAC5C,yBAAsB,KAA2B,WAAW,aAAa;AACzE,WAAO;EACT;;EAqCFM,MAAC,WAAW,wBAAkB,QAAAA,QAAA,SAAAA,MAA7B,WAAW,qBAAuB,CAAA,GAAI,KAAK,OAAO;AACnD,IAAIC,aAAY,WAAW,mBAAmB,SAAS,GAAG;AACxD,EAAAC,cACE,qBACA,gFACuB;;;;AC9R3B,IAAM,sBAAsB,CAAC,SAAiB,UAA6B;AACzE,iBAAe,OAAO,SAAS,KAAiC;AAOhE,SAAO;AACT;AAEA,IAAM,wBAAwB,CAC5B,SACA,eACE;AACF,QAAM,EAAC,MAAM,SAAQ,IAAI;AACzB,SAAO;IACL;IACA;;IAEA,SAAS,OAA+B;AACtC,qBAAe,OAAO,SAAS,KAAK;IACtC;;AAEJ;AAgBO,IAAM,gBACX,CAAC,YACD,CAAC,sBACC,OAAO,sBAAsB,aACzB,oBAAoB,SAAS,iBAAiB,IAC9C,sBAAsB,SAAS,iBAAoC;;;ACjD3E,IAAM,mBAAmB,CACvB,SACA,YACE;AAIF,MACE,QAAQ,SAAS,YACjB,QAAQ,cACR,EAAE,WAAW,QAAQ,aACrB;AACA,WAAO;MACL,GAAG;MACH,SAAS,OAA6B;AACpC,cAAM,eAAe,QAAQ,KAAK,OAAO;MAC3C;;SAEG;AAIL,WAAO;MACL,MAAM;MACN,KAAK,OAAM;MACX,WAAW;MACX,YAAY,CAAA;;MAEZ,aAAa,QAAQ;;;;;;;;;;MAUrB,cAAW;AACT,YAAI,OAAO,QAAQ,gBAAgB,YAAY;AAC7C,eAAK,QAAQ,GAAa,IAAI,QAAQ,YAAY,KAAK,IAAI;;MAE/D;MACA,SAAS,OAA6B;AACpC,cAAM,eAAe,QAAQ,KAAK,OAAO;MAC3C;;;AAGN;AAEA,IAAM,iBAAiB,CACrB,SACA,OACA,SACE;AACD,QAAM,YAAuC,eAAe,MAAM,OAAO;AAC5E;AAkCM,SAAU,SAAS,SAA6B;AAEpD,SAAO,CAAC,mBAA0C,SAChD,SAAS,SACL,eAAe,SAAU,mBAA6B,IAAI,IAC1D,iBAAiB,SAAU,iBAAiC;AACpE;;;AC7EM,SAAU,MAAM,SAAqC;AACzD,SAAO,SAAS;IACd,GAAG;IACH,OAAO;GACR;AACH;;;;ACrBA,IAAMC,aAAY;AAClB,IAAMC,UAASD,aAAY,aAAa;AAKxC,IAAM,yBACJE,MAAAD,QAAO,qBAAe,QAAAC,QAAA,SAAA,SAAAA,IAAE,UAAU,qBAAoB,OAClD,CAAC,MAAuB,SACtB,KAAK,iBAAiB,IAAI,IAC5B,CAAC,MAAuB,SACtB,KACG,cAAc,IAAI,EAClB,OACC,CAAC,SAA0B,KAAK,aAAa,KAAK,YAAY;;;ACSnE,IAAM,WAAW;EACtB,WAAW;EACX,OAAO;EACP,UAAU;EACV,mBAAmB;EACnB,OAAO;EACP,SAAS;;AAoCJ,IAAM,YACX,CAA2BC,OAC3B,IAAI,YAAsE;;EAExE,CAAC,iBAAiB,GAAGA;EACrB;;AAQE,IAAgB,YAAhB,MAAyB;EAkB7B,YAAY,WAAmB;EAAG;;EAGlC,IAAI,gBAAa;AACf,WAAO,KAAK,SAAS;EACvB;;EAGA,aACE,MACA,QACA,gBAAkC;AAElC,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,mBAAmB;EAC1B;;EAEA,UAAU,MAAY,OAAqB;AACzC,WAAO,KAAK,OAAO,MAAM,KAAK;EAChC;EAIA,OAAO,OAAa,OAAqB;AACvC,WAAO,KAAK,OAAO,GAAG,KAAK;EAC7B;;;;ACtHF,IAAM,oBAAN,cAAgC,UAAS;EAQvC,YAAY,UAAkB;;AAC5B,UAAM,QAAQ;AACd,QACE,SAAS,SAAS,SAAS,aAC3B,SAAS,SAAS,aACjBC,MAAA,SAAS,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE,UAAoB,GACvC;AACA,YAAM,IAAI,MACR,oGAC+C;;EAGrD;EAEA,OAAO,WAAoB;AAEzB,WACE,MACA,OAAO,KAAK,SAAS,EAClB,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,EAC9B,KAAK,GAAG,IACX;EAEJ;EAES,OAAO,MAAqB,CAAC,SAAS,GAA4B;;AAEzE,QAAI,KAAK,qBAAqB,QAAW;AACvC,WAAK,mBAAmB,oBAAI,IAAG;AAC/B,UAAI,KAAK,YAAY,QAAW;AAC9B,aAAK,iBAAiB,IAAI,IACxB,KAAK,QACF,KAAK,GAAG,EACR,MAAM,IAAI,EACV,OAAO,CAAC,MAAM,MAAM,EAAE,CAAC;;AAG9B,iBAAW,QAAQ,WAAW;AAC5B,YAAI,UAAU,IAAI,KAAK,GAACA,MAAA,KAAK,oBAAc,QAAAA,QAAA,SAAA,SAAAA,IAAE,IAAI,IAAI,IAAG;AACtD,eAAK,iBAAiB,IAAI,IAAI;;;AAGlC,aAAO,KAAK,OAAO,SAAS;;AAG9B,UAAM,YAAY,KAAK,QAAQ;AAK/B,SAAK,iBAAiB,QAAQ,CAAC,SAAQ;AACrC,UAAI,EAAE,QAAQ,YAAY;AACxB,kBAAU,OAAO,IAAI;AACrB,aAAK,iBAAkB,OAAO,IAAI;;IAEtC,CAAC;AAGD,eAAW,QAAQ,WAAW;AAG5B,YAAM,QAAQ,CAAC,CAAC,UAAU,IAAI;AAC9B,UACE,UAAU,KAAK,iBAAiB,IAAI,IAAI,KACxC,GAACC,MAAA,KAAK,oBAAc,QAAAA,QAAA,SAAA,SAAAA,IAAE,IAAI,IAAI,IAC9B;AACA,YAAI,OAAO;AACT,oBAAU,IAAI,IAAI;AAClB,eAAK,iBAAiB,IAAI,IAAI;eACzB;AACL,oBAAU,OAAO,IAAI;AACrB,eAAK,iBAAiB,OAAO,IAAI;;;;AAIvC,WAAO;EACT;;AAiBK,IAAM,WAAW,UAAU,iBAAiB;;;AC3HnD,SAAS,cAAc,OAAO,MAAM;AAChC,QAAM,QAAQ,IAAI,MAAM,MAAM,MAAM,KAAK,IAAI;AACjD;;;ACFA,IAAM,QAAQ,CAAC,KAAK,KAAKC,OAAM,KAAK,IAAI,KAAK,IAAIA,IAAG,GAAG,GAAG,GAAG;;;ACA7D,IAAM,WAAW;AAAA,EACb,UAAU;AAAA,EACV,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AACZ;;;ACNA,IAAM,WAAW,CAAC,UAAU,OAAO,UAAU;;;ACE7C,IAAM,eAAe,CAAC,WAAW,MAAM,QAAQ,MAAM,KAAK,CAAC,SAAS,OAAO,CAAC,CAAC;;;ACF7E,IAAMC,QAAO,CAAC,KAAK,KAAKC,OAAM;AAC1B,QAAM,YAAY,MAAM;AACxB,WAAWA,KAAI,OAAO,YAAa,aAAa,YAAa;AACjE;;;ACAA,SAAS,oBAAoB,QAAQ,GAAG;AACpC,SAAO,aAAa,MAAM,IAAI,OAAOC,MAAK,GAAG,OAAO,QAAQ,CAAC,CAAC,IAAI;AACtE;;;ACLA,IAAM,MAAM,CAAC,KAAK,KAAKC,cAAa,CAACA,YAAW,MAAMA,YAAW,MAAM;;;ACAvE,IAAM,OAAO,MAAM;AAAE;AACrB,IAAM,aAAa,CAACC,OAAMA;;;ACD1B,IAAM,WAAW,CAAC,KAAK,KAAK,UAAU,MAAM,QAAQ,IAAI,KAAK,QAAQ,QAAQ,MAAM;;;ACGnF,SAAS,WAAW,QAAQ,WAAW;AACnC,QAAM,MAAM,OAAO,OAAO,SAAS,CAAC;AACpC,WAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACjC,UAAM,iBAAiB,SAAS,GAAG,WAAW,CAAC;AAC/C,WAAO,KAAK,IAAI,KAAK,GAAG,cAAc,CAAC;AAAA,EAC3C;AACJ;AACA,SAAS,cAAc,QAAQ;AAC3B,QAAM,SAAS,CAAC,CAAC;AACjB,aAAW,QAAQ,SAAS,CAAC;AAC7B,SAAO;AACX;;;ACPA,SAAS,YAAY,QAAQ,QAAQ,cAAc,OAAO,MAAM,GAAG,SAAS,YAAY;AACpF,QAAM,SAAS,OAAO;AAOtB,QAAM,YAAY,SAAS,MAAM;AACjC,cAAY,KAAK,WAAW,OAAO,SAAS;AAC5C,SAAO,CAAC,MAAM;AACV,QAAI,IAAI;AACR,WAAO,IAAI,SAAS,GAAG,KAAK;AACxB,UAAI,IAAI,MAAM,IAAI,CAAC;AACf;AAAA,IACR;AACA,QAAI,kBAAkB,MAAM,GAAG,GAAG,SAAS,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AACrE,UAAM,gBAAgB,oBAAoB,QAAQ,CAAC;AACnD,sBAAkB,cAAc,eAAe;AAC/C,WAAO,IAAI,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,eAAe;AAAA,EACxD;AACJ;;;AC1BA,IAAM,gBAAgB,CAAC,WAAW,MAAM,QAAQ,MAAM,KAAK,SAAS,OAAO,CAAC,CAAC;;;ACF7E,IAAM,oBAAoB,CAAC,WAAW,OAAO,WAAW,YACpD,QAAQ,OAAO,eAAe;;;ACDlC,IAAM,aAAa,CAAC,UAAU,OAAO,UAAU;;;ACA/C,IAAM,WAAW,CAAC,UAAU,OAAO,UAAU;;;ACA7C,IAAM,OAAO;AAAA,EACT,IAAI,CAAC,YAAY,UAAU;AAAA,EAC3B,GAAG,CAAC,iBAAiB,eAAe;AACxC;;;ACGA,SAAS,kBAAkB,UAAU,eAAe;AAChD,SAAO,gBAAgB,YAAY,MAAO,iBAAiB;AAC/D;;;ACeA,IAAM,aAAa,CAAC,GAAG,IAAI,UAAU,IAAM,IAAM,KAAK,IAAM,MAAM,KAAK,IAAM,KAAK,IAAM,OAAO,IAAI,IAAM,MAAM;AAC/G,IAAM,uBAAuB;AAC7B,IAAM,2BAA2B;AACjC,SAAS,gBAAgB,GAAG,YAAY,YAAY,KAAK,KAAK;AAC1D,MAAI;AACJ,MAAI;AACJ,MAAI,IAAI;AACR,KAAG;AACC,eAAW,cAAc,aAAa,cAAc;AACpD,eAAW,WAAW,UAAU,KAAK,GAAG,IAAI;AAC5C,QAAI,WAAW,GAAK;AAChB,mBAAa;AAAA,IACjB,OACK;AACD,mBAAa;AAAA,IACjB;AAAA,EACJ,SAAS,KAAK,IAAI,QAAQ,IAAI,wBAC1B,EAAE,IAAI;AACV,SAAO;AACX;AACA,SAAS,YAAY,KAAK,KAAK,KAAK,KAAK;AAErC,MAAI,QAAQ,OAAO,QAAQ;AACvB,WAAO;AACX,QAAM,WAAW,CAAC,OAAO,gBAAgB,IAAI,GAAG,GAAG,KAAK,GAAG;AAE3D,SAAO,CAAC,MAAM,MAAM,KAAK,MAAM,IAAI,IAAI,WAAW,SAAS,CAAC,GAAG,KAAK,GAAG;AAC3E;;;AChDA,IAAM,QAAQ,CAACC,QAAO,YAAY,UAAU,CAACC,cAAa;AACtD,EAAAA,YACI,cAAc,QACR,KAAK,IAAIA,WAAU,KAAK,IACxB,KAAK,IAAIA,WAAU,IAAK;AAClC,QAAM,WAAWA,YAAWD;AAC5B,QAAM,UAAU,cAAc,QAAQ,KAAK,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAC/E,SAAO,MAAM,GAAG,GAAG,UAAUA,MAAK;AACtC;;;ACPA,IAAM,eAAe;AAAA,EACjB,MAAM,YAAY,MAAM,KAAK,MAAM,CAAG;AAAA,EACtC,WAAW,YAAY,MAAM,GAAK,GAAK,CAAG;AAAA,EAC1C,eAAe,YAAY,MAAM,GAAK,MAAM,CAAG;AAAA,EAC/C,YAAY,YAAY,GAAK,GAAK,MAAM,CAAG;AAC/C;AACA,IAAM,oBAAoB;AAC1B,SAAS,kBAAkB,YAAY;AAEnC,MAAI,WAAW,UAAU;AACrB,WAAO;AAEX,MAAI,cAAc,UAAU;AACxB,WAAO,YAAY,GAAG,UAAU;AAEpC,QAAM,cAAc,aAAa,UAAU;AAC3C,MAAI;AACA,WAAO;AAEX,MAAI,WAAW,WAAW,OAAO,GAAG;AAChC,UAAM,OAAO,kBAAkB,KAAK,UAAU;AAC9C,QAAI,MAAM;AACN,YAAM,YAAY,KAAK,CAAC,EAAE,MAAM,GAAG;AACnC,aAAO,MAAM,WAAW,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,KAAK,CAAC;AAAA,IAC9D;AAAA,EACJ;AACA,SAAO;AACX;;;AC3BA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,QAAQ,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,UAAU,kBAAkB,SAAS,UAAU,QAAQ,SAAS,OAAO,WAAW,SAAS,UAAU,SAAS,SAAS,QAAQ,QAAQ,YAAY,UAAU,WAAW,KAAM,IAAI,CAAC,GAAG;AACpO,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,IAAI;AACT,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7C,WAAK,UAAU;AACf,WAAK,SAAS;AAAA,IAClB,CAAC;AACD,aAAS,UAAU,SAAS;AAC5B,QAAI,kBAAkB,MAAM,GAAG;AAC3B,YAAM,SAAS,OAAO,gBAAgB,SAAS;AAC/C,eAAS,OAAO;AAChB,kBAAY,OAAO,aAAa;AAChC,wBAAkB,OAAO,YAAY;AAAA,IACzC;AACA,SAAK,SAAS;AACd,SAAK,SAAS,aAAa,MAAM,IAAI,aAAa,kBAAkB,MAAM;AAC1E,SAAK,eAAe,eAAe;AACnC,UAAM,gBAAgB,YAAY,WAAW,QAAQ,aAAa,MAAM,IAAI,OAAO,IAAI,iBAAiB,IAAI,UAAU;AACtH,SAAK,OAAO,CAAC,cAAc;AACvB,UAAIE;AAEJ,cAAQ;AACR,UAAI,IAAI;AACR,UAAI,KAAK,cAAc,QAAW;AAC9B,YAAI,KAAK;AAAA,MACb,OACK;AACD,aAAK,YAAY,KAAK,aAAa,KAAK;AAAA,MAC5C;AACA,WAAK,IAAI;AAET,WAAK;AAEL,UAAI,KAAK,IAAI,IAAI,OAAO,CAAC;AAKzB,UAAI,KAAK,cAAc,cAAc,KAAK,cAAc,QAAW;AAC/D,YAAI,KAAK;AAAA,MACb;AAMA,YAAMC,YAAW,IAAI,KAAK;AAM1B,UAAI,mBAAmB,KAAK,MAAMA,SAAQ;AAK1C,UAAI,oBAAoBA,YAAW;AACnC,UAAI,CAAC,qBAAqBA,aAAY,GAAG;AACrC,4BAAoB;AAAA,MACxB;AAKA,4BAAsB,KAAK;AAI3B,YAAM,iBAAiB,mBAAmB;AAC1C,UAAI,cAAc,aACb,cAAc,eAAe,kBAC7B,cAAc,uBAAuB,CAAC,gBAAiB;AACxD,4BAAoB,IAAI;AAAA,MAC5B;AACA,YAAMC,KAAI,KAAK,KAAK,gBAAgB,IAAI,KAAK,IAAI,mBAAmB,CAAC;AACrE,YAAM,SAAS,cAAc,KAAK,OAAOA,EAAC,CAAC;AAC3C,aAAO,MAAM;AACb,YAAM,sBAAsB,KAAK,cAAc,WAC1C,KAAK,cAAc,cAAc,KAAK,KAAK,gBAAgB;AAChE,UAAI,qBAAqB;AACrB,aAAK,YAAY;AACjB,SAACF,MAAK,KAAK,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,MAAM,MAAM;AAAA,MACjF,WACS,KAAK,cAAc,QAAQ;AAChC,aAAK,iBAAiB,sBAAsB,KAAK,IAAI;AAAA,MACzD;AAAA,IACJ;AACA,QAAI;AACA,WAAK,KAAK;AAAA,EAClB;AAAA,EACA,OAAO;AACH,UAAM,MAAM,YAAY,IAAI;AAC5B,SAAK,YAAY;AACjB,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,MAAM,KAAK;AAAA,IAChC,WACS,CAAC,KAAK,WAAW;AACtB,WAAK,YAAY;AAAA,IACrB;AACA,SAAK,kBAAkB,KAAK;AAC5B,SAAK,YAAY;AACjB,SAAK,iBAAiB,sBAAsB,KAAK,IAAI;AAAA,EACzD;AAAA,EACA,QAAQ;AACJ,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK;AAAA,EAC1B;AAAA,EACA,SAAS;AACL,SAAK,YAAY;AACjB,SAAK,KAAK,CAAC;AAAA,EACf;AAAA,EACA,OAAO;AACH,QAAIA;AACJ,SAAK,YAAY;AACjB,QAAI,KAAK,mBAAmB,QAAW;AACnC,2BAAqB,KAAK,cAAc;AAAA,IAC5C;AACA,KAACA,MAAK,KAAK,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,MAAM,KAAK;AAAA,EAC/E;AAAA,EACA,SAAS;AACL,SAAK,KAAK;AACV,SAAK,KAAK,KAAK,eAAe;AAAA,EAClC;AAAA,EACA,UAAU;AACN,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,eAAe;AAAA,EAAE;AAAA,EACjB,eAAe,UAAU;AACrB,SAAK,WAAW;AAChB,SAAK,gBAAgB,YAAY,KAAK,SAAS;AAAA,EACnD;AAAA,EACA,IAAI,cAAc;AACd,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY,GAAG;AACf,QAAI,KAAK,cAAc,UAAa,KAAK,SAAS,GAAG;AACjD,WAAK,YAAY;AAAA,IACrB,OACK;AACD,WAAK,YAAY,YAAY,IAAI,IAAI,IAAI,KAAK;AAAA,IAClD;AAAA,EACJ;AAAA,EACA,IAAI,eAAe;AACf,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,aAAa,MAAM;AACnB,SAAK,OAAO;AAAA,EAChB;AACJ;;;AChKA,IAAI,UAAU,WAAY;AAAE;AAC5B,IAAI,YAAY,WAAY;AAAE;AAC9B,IAAI,MAAuC;AACvC,YAAU,SAAU,OAAO,SAAS;AAChC,QAAI,CAAC,SAAS,OAAO,YAAY,aAAa;AAC1C,cAAQ,KAAK,OAAO;AAAA,IACxB;AAAA,EACJ;AACA,cAAY,SAAU,OAAO,SAAS;AAClC,QAAI,CAAC,OAAO;AACR,YAAM,IAAI,MAAM,OAAO;AAAA,IAC3B;AAAA,EACJ;AACJ;;;ACPA,IAAM,cAAN,MAAkB;AAAA,EACd,aAAa,WAAW;AACpB,SAAK,YAAY;AACjB,kBAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS,KAAK,MAAM,KAAK,eAAe,CAAC,EAAE,MAAM,MAAM;AAAA,IAAE,CAAC;AAAA,EAC9H;AAAA,EACA,iBAAiB;AACb,SAAK,YAAY,KAAK,YAAY;AAAA,EACtC;AACJ;;;ACZA,IAAM,OAAO,oBAAI,QAAQ;AACzB,SAAS,iBAAiB,SAAS;AAC/B,MAAI,CAAC,KAAK,IAAI,OAAO,GAAG;AACpB,SAAK,IAAI,SAAS;AAAA,MACd,YAAY,CAAC;AAAA,MACb,QAAQ,oBAAI,IAAI;AAAA,IACpB,CAAC;AAAA,EACL;AACA,SAAO,KAAK,IAAI,OAAO;AAC3B;AACA,SAAS,eAAe,cAAc,MAAM;AACxC,MAAI,CAAC,aAAa,IAAI,IAAI,GAAG;AACzB,iBAAa,IAAI,MAAM,IAAI,YAAY,CAAC;AAAA,EAC5C;AACA,SAAO,aAAa,IAAI,IAAI;AAChC;;;ACVA,IAAM,OAAO,CAAC,IAAI,KAAK,KAAK,GAAG;AAK/B,IAAM,QAAQ,CAAC,aAAa,SAAS,UAAU,MAAM;AACrD,IAAM,iBAAiB;AAAA,EACnB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACP;AACA,IAAM,WAAW;AAAA,EACb,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,eAAe,CAACG,OAAMA,KAAI;AAC9B;AACA,IAAM,0BAA0B;AAAA,EAC5B,WAAW;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,eAAe,CAACA,OAAMA,KAAI;AAAA,EAC9B;AAAA,EACA,QAAQ;AAAA,EACR,OAAO;AAAA,IACH,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,eAAe;AAAA,EACnB;AAAA,EACA,MAAM;AACV;AACA,IAAM,uBAAuB,oBAAI,IAAI;AACrC,IAAM,oBAAoB,CAAC,SAAS,YAAY,IAAI;AAIpD,IAAM,aAAa,CAAC,KAAK,KAAK,GAAG;AACjC,MAAM,QAAQ,CAAC,SAAS;AACpB,OAAK,QAAQ,CAAC,SAAS;AACnB,eAAW,KAAK,OAAO,IAAI;AAC3B,yBAAqB,IAAI,kBAAkB,OAAO,IAAI,GAAG,wBAAwB,IAAI,CAAC;AAAA,EAC1F,CAAC;AACL,CAAC;AAID,IAAM,wBAAwB,CAACC,IAAG,MAAM,WAAW,QAAQA,EAAC,IAAI,WAAW,QAAQ,CAAC;AAIpF,IAAM,kBAAkB,IAAI,IAAI,UAAU;AAC1C,IAAM,cAAc,CAAC,SAAS,gBAAgB,IAAI,IAAI;AACtD,IAAM,wBAAwB,CAAC,SAAS,SAAS;AAE7C,MAAI,eAAe,IAAI;AACnB,WAAO,eAAe,IAAI;AAC9B,QAAM,EAAE,YAAAC,YAAW,IAAI,iBAAiB,OAAO;AAC/C,gBAAcA,aAAY,IAAI;AAK9B,UAAQ,MAAM,YAAY,uBAAuBA,WAAU;AAC/D;AACA,IAAM,yBAAyB,CAACA,gBAAeA,YAC1C,KAAK,qBAAqB,EAC1B,OAAO,uBAAuB,EAAE,EAChC,KAAK;AACV,IAAM,wBAAwB,CAAC,UAAU,SAAS,GAAG,QAAQ,IAAI,IAAI,QAAQ,kBAAkB,IAAI,CAAC;;;ACxEpG,IAAM,WAAW,CAAC,SAAS,KAAK,WAAW,IAAI;AAC/C,IAAM,uBAAuB,oBAAI,IAAI;AACrC,SAAS,oBAAoB,MAAM;AAC/B,MAAI,qBAAqB,IAAI,IAAI;AAC7B;AACJ,uBAAqB,IAAI,IAAI;AAC7B,MAAI;AACA,UAAM,EAAE,QAAQ,aAAa,IAAI,qBAAqB,IAAI,IAAI,IACxD,qBAAqB,IAAI,IAAI,IAC7B,CAAC;AACP,QAAI,iBAAiB;AAAA,MACjB;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL,SACO,GAAG;AAAA,EAAE;AAChB;;;ACpBA,IAAM,gBAAgB,CAAC,WAAW,YAAY,SAAS,cAAc,KAAK,EAAE,QAAQ,WAAW,OAAO;AACtG,IAAM,eAAe;AAAA,EACjB,qBAAqB,MAAM,OAAO,QAAQ,eACtC,OAAO,eAAe,KAAK,KAAK,kBAAkB;AAAA,EACtD,OAAO,MAAM,OAAO,eAAe,KAAK,QAAQ,WAAW,SAAS;AAAA,EACpE,kBAAkB,MAAM;AACpB,QAAI;AACA,oBAAc,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;AAAA,IAClC,SACO,GAAG;AACN,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU,MAAM,QAAQ,cAAc,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,UAAU,KAAM,CAAC,EAAE,QAAQ;AAAA,EACxF,cAAc,MAAM;AAChB,QAAI;AACA,oBAAc,EAAE,SAAS,EAAE,GAAG,EAAE,QAAQ,eAAe,CAAC;AAAA,IAC5D,SACO,GAAG;AACN,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,UAAU,CAAC;AACjB,IAAM,WAAW,CAAC;AAClB,WAAW,OAAO,cAAc;AAC5B,WAAS,GAAG,IAAI,MAAM;AAClB,QAAI,QAAQ,GAAG,MAAM;AACjB,cAAQ,GAAG,IACP,aAAa,GAAG,EAAE;AAC1B,WAAO,QAAQ,GAAG;AAAA,EACtB;AACJ;;;AC9BA,IAAM,aAAa;AACnB,IAAM,6BAA6B,CAAC,QAAQ,aAAa;AACrD,MAAI,SAAS;AACb,QAAM,YAAY,KAAK,MAAM,WAAW,UAAU;AAClD,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,cAAU,OAAO,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC,IAAI;AAAA,EACtD;AACA,SAAO,OAAO,UAAU,GAAG,OAAO,SAAS,CAAC;AAChD;AACA,IAAM,gBAAgB,CAAC,QAAQ,aAAa;AACxC,MAAI,WAAW,MAAM,GAAG;AACpB,WAAO,SAAS,aAAa,IACvB,UAAU,2BAA2B,QAAQ,QAAQ,CAAC,MACtD,SAAS;AAAA,EACnB,OACK;AACD,WAAO,cAAc,MAAM,IAAI,oBAAoB,MAAM,IAAI;AAAA,EACjE;AACJ;AACA,IAAM,sBAAsB,CAAC,CAACC,IAAG,GAAGC,IAAGC,EAAC,MAAM,gBAAgBF,EAAC,KAAK,CAAC,KAAKC,EAAC,KAAKC,EAAC;;;ACvBjF,SAAS,iBAAiB,WAAW,kBAAkB;AACnD,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,UAAU,CAAC,MAAM,MAAM;AACvB,gBAAU,CAAC,IAAI,IAAI,UAAU,IAAI,CAAC,IAAI,iBAAiB;AAAA,IAC3D;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,gBAAgB,CAAC,cAAc,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;;;ACNtF,SAAS,aAAa,KAAK;AACvB,MAAI,eAAe,GAAG;AAClB,UAAM,eAAe,GAAG;AAC5B,SAAO,YAAY,GAAG,IAAI,kBAAkB,GAAG,IAAI;AACvD;;;ACFA,IAAM,QAAQ;AAAA,EACV,KAAK,CAAC,SAAS,SAAS;AACpB,WAAO,aAAa,IAAI;AACxB,QAAI,QAAQ,SAAS,IAAI,IACnB,QAAQ,MAAM,iBAAiB,IAAI,IACnC,iBAAiB,OAAO,EAAE,IAAI;AAEpC,QAAI,CAAC,SAAS,UAAU,GAAG;AACvB,YAAM,aAAa,qBAAqB,IAAI,IAAI;AAChD,UAAI;AACA,gBAAQ,WAAW;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AAAA,EACA,KAAK,CAAC,SAAS,MAAM,UAAU;AAC3B,WAAO,aAAa,IAAI;AACxB,QAAI,SAAS,IAAI,GAAG;AAChB,cAAQ,MAAM,YAAY,MAAM,KAAK;AAAA,IACzC,OACK;AACD,cAAQ,MAAM,IAAI,IAAI;AAAA,IAC1B;AAAA,EACJ;AACJ;;;AC3BA,SAAS,cAAc,WAAW,cAAc,MAAM;AAClD,MAAI,CAAC,aAAa,UAAU,cAAc;AACtC;AAEJ,MAAI;AACA,QAAI,UAAU,MAAM;AAChB,gBAAU,KAAK;AAAA,IACnB,OACK;AACD,qBAAe,UAAU,aAAa;AACtC,gBAAU,OAAO;AAAA,IACrB;AAAA,EACJ,SACO,GAAG;AAAA,EAAE;AAChB;;;ACZA,SAAS,iBAAiB,WAAW,YAAY;AAC7C,MAAIC;AACJ,MAAI,UAAU,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,kBAAkB;AACnG,QAAM,gBAAgB,UAAU,UAAU,SAAS,CAAC;AACpD,MAAI,SAAS,aAAa,GAAG;AACzB,UAAM,SAASA,MAAK,cAAc,MAAM,qBAAqB,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,CAAC,MAAM;AAC/G,QAAI;AACA,eAAS,CAAC,UAAU,QAAQ;AAAA,EACpC;AACA,SAAO;AACX;;;ACAA,SAAS,oBAAoB;AACzB,SAAO,OAAO;AAClB;AACA,SAAS,aAAa,SAAS,KAAK,qBAAqB,UAAU,CAAC,GAAG,mBAAmB;AACtF,QAAM,SAAS,kBAAkB;AACjC,QAAM,cAAc,QAAQ,WAAW,SAAS;AAChD,MAAI;AACJ,MAAI,EAAE,WAAW,SAAS,UAAU,QAAQ,SAAS,OAAO,WAAW,SAAS,UAAU,SAAS,SAAS,QAAQ,SAAS,SAAS,QAAQ,UAAU,OAAO,WAAW,QAAQ,0BAA0B,OAAO,WAAW,KAAM,IAAI;AACxO,QAAMC,QAAO,iBAAiB,OAAO;AACrC,QAAM,mBAAmB,YAAY,GAAG;AACxC,MAAI,qBAAqB,SAAS,MAAM;AAKxC,sBAAoB,sBAAsB,SAAS,GAAG;AACtD,QAAM,OAAO,aAAa,GAAG;AAC7B,QAAM,cAAc,eAAeA,MAAK,QAAQ,IAAI;AAKpD,QAAM,aAAa,qBAAqB,IAAI,IAAI;AAOhD,gBAAc,YAAY,WAAW,EAAE,kBAAkB,MAAM,KAAK,YAAY,cAC5E,QAAQ,WAAW,KAAK;AAI5B,SAAO,MAAM;AACT,UAAM,mBAAmB,MAAM;AAAE,UAAIC,KAAIC;AAAI,cAAQA,OAAMD,MAAK,MAAM,IAAI,SAAS,IAAI,OAAO,QAAQA,QAAO,SAASA,MAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,kBAAkB,QAAQC,QAAO,SAASA,MAAK;AAAA,IAAG;AAKhP,QAAI,YAAY,iBAAiB,cAAc,mBAAmB,GAAG,gBAAgB;AAIrF,UAAM,SAAS,iBAAiB,WAAW,UAAU;AACrD,QAAI,kBAAkB,MAAM,GAAG;AAC3B,YAAM,SAAS,OAAO,gBAAgB,WAAW,QAAQ,WAAW,kBAAkB,MAAM,WAAW;AACvG,eAAS,OAAO;AAChB,kBAAY,OAAO,aAAa;AAChC,iBAAW,OAAO,YAAY;AAAA,IAClC;AAMA,QAAI,SAAS,IAAI,GAAG;AAChB,UAAI,SAAS,oBAAoB,GAAG;AAChC,4BAAoB,IAAI;AAAA,MAC5B,OACK;AACD,6BAAqB;AAAA,MACzB;AAAA,IACJ;AAOA,QAAI,oBACA,CAAC,SAAS,aAAa,MACtB,WAAW,MAAM,KAAM,aAAa,MAAM,KAAK,OAAO,KAAK,UAAU,IAAK;AAC3E,2BAAqB;AAAA,IACzB;AAIA,QAAI,oBAAoB;AAKpB,UAAI,YAAY;AACZ,oBAAY,UAAU,IAAI,CAAC,UAAU,SAAS,KAAK,IAAI,WAAW,cAAc,KAAK,IAAI,KAAK;AAAA,MAClG;AAKA,UAAI,UAAU,WAAW,MACpB,CAAC,SAAS,iBAAiB,KAAK,cAAc;AAC/C,kBAAU,QAAQ,iBAAiB,CAAC;AAAA,MACxC;AACA,YAAM,mBAAmB;AAAA,QACrB,OAAO,KAAK,GAAG,KAAK;AAAA,QACpB,UAAU,KAAK,GAAG,QAAQ;AAAA,QAC1B,UAAU,KAAK,GAAG,QAAQ;AAAA,QAC1B,QAAQ,CAAC,aAAa,MAAM,IACtB,cAAc,QAAQ,QAAQ,IAC9B;AAAA,QACN;AAAA,QACA,YAAY,SAAS;AAAA,QACrB,MAAM;AAAA,MACV;AACA,kBAAY,QAAQ,QAAQ;AAAA,QACxB,CAAC,IAAI,GAAG;AAAA,QACR;AAAA,QACA,QAAQ,aAAa,MAAM,IACrB,OAAO,IAAI,CAAC,eAAe,cAAc,YAAY,QAAQ,CAAC,IAC9D;AAAA,MACV,GAAG,gBAAgB;AAInB,UAAI,CAAC,UAAU,UAAU;AACrB,kBAAU,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAW;AAClD,oBAAU,WAAW;AACrB,oBAAU,WAAW;AAAA,QACzB,CAAC;AAAA,MACL;AACA,YAAM,SAAS,UAAU,UAAU,SAAS,CAAC;AAC7C,gBAAU,SACL,KAAK,MAAM;AACZ,YAAI;AACA;AAEJ,cAAM,IAAI,SAAS,MAAM,MAAM;AAE/B,kBAAU,OAAO;AAAA,MACrB,CAAC,EACI,MAAM,IAAI;AAUf,UAAI,CAAC;AACD,kBAAU,eAAe;AAAA,IAKjC,WACS,qBAAqB,kBAAkB;AAK5C,kBAAY,UAAU,IAAI,CAAC,UAAU,OAAO,UAAU,WAAW,WAAW,KAAK,IAAI,KAAK;AAK1F,UAAI,UAAU,WAAW,GAAG;AACxB,kBAAU,QAAQ,WAAW,iBAAiB,CAAC,CAAC;AAAA,MACpD;AACA,kBAAY,IAAI,kBAAkB,CAAC,WAAW;AAC1C,cAAM,IAAI,SAAS,MAAM,SAAS,OAAO,MAAM,IAAI,MAAM;AAAA,MAC7D,GAAG,WAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,QAAE;AAAA,QACtD;AAAA,MAAO,CAAC,CAAC;AAAA,IACjB,OACK;AACD,YAAM,SAAS,UAAU,UAAU,SAAS,CAAC;AAC7C,YAAM,IAAI,SAAS,MAAM,cAAc,SAAS,MAAM,IAChD,WAAW,cAAc,MAAM,IAC/B,MAAM;AAAA,IAChB;AACA,QAAI,aAAa;AACb,aAAO,SAAS,KAAK,WAAW;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,GAAG,YAAY;AAAA,IACnB;AACA,gBAAY,aAAa,SAAS;AAClC,QAAI,aAAa,CAAC;AACd,gBAAU,MAAM;AACpB,WAAO;AAAA,EACX;AACJ;;;ACtMA,IAAM,aAAa,CAAC,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,QAAQ,GAAG,IAAI,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,QAAQ,GAAG,CAAC,IAAI,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA;;;ACNlG,SAAS,gBAAgB,UAAU,eAAe;AAC9C,MAAIC;AACJ,MAAI,OAAO,aAAa,UAAU;AAC9B,QAAI,eAAe;AACf,OAACA,MAAK,cAAc,QAAQ,OAAO,QAAQA,QAAO,SAASA,MAAM,cAAc,QAAQ,IAAI,SAAS,iBAAiB,QAAQ;AAC7H,iBAAW,cAAc,QAAQ;AAAA,IACrC,OACK;AACD,iBAAW,SAAS,iBAAiB,QAAQ;AAAA,IACjD;AAAA,EACJ,WACS,oBAAoB,SAAS;AAClC,eAAW,CAAC,QAAQ;AAAA,EACxB;AAIA,SAAO,MAAM,KAAK,YAAY,CAAC,CAAC;AACpC;;;ACfA,IAAM,kBAAkB,CAAC,YAAY,QAAQ;AAC7C,IAAM,eAAe,CAAC,kBAAkB,SAAS,WAAW,SAAS,aAAa;AAC9E,SAAO,IAAI,MAAM;AAAA,IACb,YAAY,iBAAiB,IAAI,eAAe,EAAE,OAAO,OAAO;AAAA,IAChE;AAAA,IACA;AAAA,EACJ,GAAG,QAAQ;AACf;AAMA,IAAM,qBAAqB,CAACC,WAAUA,OAAM,WAAW,CAAC;AACxD,IAAM,WAAW;AAAA,EACb,KAAK,CAAC,QAAQ,QAAQ;AAClB,UAAM,kBAAkB,mBAAmB,MAAM;AACjD,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,eAAO,OAAO;AAAA,MAClB,KAAK;AACD,eAAO,KAAK,GAAG,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,GAAG,MAAM,CAAC;AAAA,MAC/G,KAAK;AAAA,MACL,KAAK;AACD,eAAO,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,GAAG;AAAA,MAChG,KAAK;AACD,YAAI,CAAC,OAAO,UAAU;AAClB,iBAAO,WAAW,QAAQ,IAAI,OAAO,WAAW,IAAI,cAAc,CAAC,EAAE,MAAM,IAAI;AAAA,QACnF;AACA,eAAO,OAAO;AAAA,MAClB,KAAK;AACD,eAAO,MAAM;AACT,iBAAO,WAAW,QAAQ,CAAC,cAAc,cAAc,SAAS,CAAC;AAAA,QACrE;AAAA,MACJ,KAAK;AAKD,eAAO,CAAC,aAAa;AACjB,iBAAO,WAAW,QAAQ,CAAC,cAAc,SAAS,WAAW,MAAM,CAAC;AAAA,QACxE;AAAA,MACJ;AACI,eAAO,QAAQ,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,GAAG,OAChG,cACE,SACA,MAAM,OAAO,WAAW,QAAQ,CAAC,cAAc,UAAU,GAAG,EAAE,CAAC;AAAA,IAC7E;AAAA,EACJ;AAAA,EACA,KAAK,CAAC,QAAQ,KAAK,UAAU;AACzB,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,gBAAQ,KAAK,GAAG,KAAK;AAAA,MAEzB,KAAK;AACD,iBAAS,IAAI,GAAG,IAAI,OAAO,WAAW,QAAQ,KAAK;AAC/C,iBAAO,WAAW,CAAC,EAAE,GAAG,IAAI;AAAA,QAChC;AACA,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,iBAAiB,CAAC,cAAc,UAAU;;;ACzChD,SAAS,cAAc,QAAQ,GAAG,OAAO;AACrC,SAAO,WAAW,MAAM,IAAI,OAAO,GAAG,KAAK,IAAI;AACnD;;;ACpBA,SAAS,cAAc,iBAAiB;AACpC,SAAO,SAASC,SAAQ,UAAU,WAAW,UAAU,CAAC,GAAG;AACvD,eAAW,gBAAgB,QAAQ;AACnC,UAAM,cAAc,SAAS;AAC7B,cAAU,QAAQ,WAAW,GAAG,4BAA4B;AAC5D,cAAU,QAAQ,SAAS,GAAG,uBAAuB;AAIrD,UAAM,qBAAqB,CAAC;AAC5B,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,YAAM,UAAU,SAAS,CAAC;AAC1B,iBAAW,OAAO,WAAW;AACzB,cAAM,eAAe,WAAW,SAAS,GAAG;AAC5C,qBAAa,QAAQ,cAAc,aAAa,OAAO,GAAG,WAAW;AACrE,cAAM,YAAY,aAAa,SAAS,KAAK,UAAU,GAAG,GAAG,cAAc,eAAe;AAC1F,2BAAmB,KAAK,SAAS;AAAA,MACrC;AAAA,IACJ;AACA,WAAO;AAAA,MAAa;AAAA,MAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUxC,QAAQ;AAAA,IAAQ;AAAA,EACpB;AACJ;;;ACnCA,IAAM,UAAU,cAAc,SAAS;;;ACHvC;;;ACEA,IAAM,UAAU;AAChB,SAAS,sBAAsB,cAAc,GAAG,SAAS;AACrD,QAAM,QAAQ,KAAK,IAAI,IAAI,SAAS,CAAC;AACrC,SAAO,kBAAkB,UAAU,aAAa,KAAK,GAAG,IAAI,KAAK;AACrE;;;ACNA,IAAMC,YAAW;AAAA,EACb,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AACV;;;ACFA,IAAM,mBAAmB,CAAC,YAAYC,UAAS,WAAW,UAAUA,UAAS,SAAS,OAAOA,UAAS,SAAS,WAAW,IAAI,KAAK,KAAK,YAAY,IAAI;;;ACFxJ,SAAS,iBAAiB,QAAQ,QAAQ,SAAS;AAC/C,SAAS,SAAS,UAAU,WAAW,UAClC,SAAS,UAAU,WAAW;AACvC;;;ACGA,IAAM,SAAS,CAAC,EAAE,YAAYC,UAAS,WAAW,UAAUA,UAAS,SAAS,OAAOA,UAAS,MAAM,OAAO,GAAG,IAAAC,MAAK,GAAG,WAAW,GAAK,WAAW,aAAc,IAAI,CAAC,MAAM;AACtK,aAAW,WAAW,KAAK,EAAE,QAAQ,IAAI;AACzC,QAAMC,SAAQ;AAAA,IACV,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,QAAQD;AAAA,EACZ;AACA,QAAM,eAAeA,MAAK;AAC1B,QAAM,sBAAsB,KAAK,KAAK,YAAY,IAAI,IAAI;AAC1D,QAAM,eAAe,iBAAiB,WAAW,SAAS,IAAI;AAC9D,QAAM,kBAAkB,KAAK,IAAI,YAAY,IAAI;AACjD,gBAAc,YAAY,kBAAkB,OAAO;AACnD,mBAAiB,eAAe,kBAAkB,OAAQ;AAC1D,MAAI;AACJ,MAAI,eAAe,GAAG;AAClB,UAAM,cAAc,sBAAsB,KAAK,KAAK,IAAI,eAAe,YAAY;AAEnF,oBAAgB,CAAC,MAAMA,MACnB,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC,MACzC,CAAC,WAAW,eAAe,sBAAsB,gBAChD,cACA,KAAK,IAAI,cAAc,CAAC,IACxB,eAAe,KAAK,IAAI,cAAc,CAAC;AAAA,EACvD,OACK;AAED,oBAAgB,CAAC,MAAM;AACnB,aAAQA,MACJ,KAAK,IAAI,CAAC,sBAAsB,CAAC,KAC5B,gBAAgB,CAAC,WAAW,sBAAsB,gBAAgB;AAAA,IAC/E;AAAA,EACJ;AACA,SAAO,CAAC,MAAM;AACV,IAAAC,OAAM,UAAU,cAAc,CAAC;AAC/B,UAAM,kBAAkB,MAAM,IACxB,WACA,sBAAsB,eAAe,GAAGA,OAAM,OAAO;AAC3D,UAAM,2BAA2B,KAAK,IAAI,eAAe,KAAK;AAC9D,UAAM,+BAA+B,KAAK,IAAID,MAAKC,OAAM,OAAO,KAAK;AACrE,IAAAA,OAAM,OAAO,4BAA4B;AACzC,IAAAA,OAAM,mBAAmB,iBAAiB,MAAMD,KAAIC,OAAM,OAAO;AACjE,WAAOA;AAAA,EACX;AACJ;;;AC9CA,IAAM,QAAQ,CAAC,EAAE,OAAO,GAAG,WAAW,GAAK,QAAQ,KAAK,QAAQ,OAAO,eAAe,iBAAiB,cAAc,KAAK,KAAK,eAAe,KAAK,UAAW,MAAM;AAChK,UAAQ,KAAK,GAAG,KAAK;AACrB,QAAMC,SAAQ;AAAA,IACV,kBAAkB;AAAA,IAClB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ;AACA,QAAM,gBAAgB,CAACC,OAAO,QAAQ,UAAaA,KAAI,OAAS,QAAQ,UAAaA,KAAI;AACzF,QAAM,kBAAkB,CAACA,OAAM;AAC3B,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,QAAQ;AACR,aAAO;AACX,WAAO,KAAK,IAAI,MAAMA,EAAC,IAAI,KAAK,IAAI,MAAMA,EAAC,IAAI,MAAM;AAAA,EACzD;AACA,MAAI,YAAY,QAAQ;AACxB,QAAM,QAAQ,OAAO;AACrB,QAAM,SAAS,iBAAiB,SAAY,QAAQ,aAAa,KAAK;AACtE,EAAAD,OAAM,SAAS;AAKf,MAAI,WAAW;AACX,gBAAY,SAAS;AACzB,QAAM,YAAY,CAAC,MAAM,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,KAAK;AACzD,QAAM,aAAa,CAAC,MAAM,SAAS,UAAU,CAAC;AAC9C,QAAM,gBAAgB,CAAC,MAAM;AACzB,UAAM,QAAQ,UAAU,CAAC;AACzB,UAAM,SAAS,WAAW,CAAC;AAC3B,IAAAA,OAAM,OAAO,KAAK,IAAI,KAAK,KAAK;AAChC,IAAAA,OAAM,UAAUA,OAAM,OAAO,SAAS;AAAA,EAC1C;AAOA,MAAI;AACJ,MAAI;AACJ,QAAM,qBAAqB,CAAC,MAAM;AAC9B,QAAI,CAAC,cAAcA,OAAM,OAAO;AAC5B;AACJ,0BAAsB;AACtB,eAAW,OAAO;AAAA,MACd,MAAMA,OAAM;AAAA,MACZ,IAAI,gBAAgBA,OAAM,OAAO;AAAA,MACjC,UAAU,sBAAsB,YAAY,GAAGA,OAAM,OAAO;AAAA;AAAA,MAC5D,SAAS;AAAA,MACT,WAAW;AAAA,MACX;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AACA,qBAAmB,CAAC;AACpB,SAAO,CAAC,MAAM;AAOV,QAAI,kBAAkB;AACtB,QAAI,CAAC,YAAY,wBAAwB,QAAW;AAChD,wBAAkB;AAClB,oBAAc,CAAC;AACf,yBAAmB,CAAC;AAAA,IACxB;AAKA,QAAI,wBAAwB,UAAa,IAAI,qBAAqB;AAC9D,MAAAA,OAAM,mBAAmB;AACzB,aAAO,SAAS,IAAI,mBAAmB;AAAA,IAC3C,OACK;AACD,MAAAA,OAAM,mBAAmB;AACzB,OAAC,mBAAmB,cAAc,CAAC;AACnC,aAAOA;AAAA,IACX;AAAA,EACJ;AACJ;;;ACtFA,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,SAAS,qBAAqB,WAAW,SAAS,YAAY;AAC1D,MAAI,oBAAoB;AACxB,MAAI,YAAY;AAChB,MAAIE,SAAQ,UAAU,CAAC;AACvB,QAAM,YAAY,CAAC,OAAOA,OAAM,OAAO,CAAC;AACxC,SAAO,CAACA,OAAM,QAAQ,YAAY,aAAa;AAC3C,IAAAA,SAAQ,UAAU,SAAS;AAC3B,cAAU,KAAK,OAAOA,OAAM,OAAOA,OAAM,SAASA,OAAM,OAAO,CAAC;AAChE,QAAI,sBAAsB,UAAaA,OAAM,kBAAkB;AAC3D,0BAAoB;AAAA,IACxB;AACA,iBAAa;AAAA,EACjB;AACA,QAAM,WAAW,YAAY;AAK7B,MAAI,UAAU,WAAW;AACrB,cAAU,KAAKA,OAAM,OAAO;AAChC,SAAO;AAAA,IACH;AAAA,IACA,UAAU,WAAW;AAAA,IACrB,oBAAoB,sBAAsB,QAAQ,sBAAsB,SAAS,oBAAoB,YAAY;AAAA,EACrH;AACJ;;;ACvBA,SAAS,YAAY,OAAO;AACxB,SAAO,SAAS,KAAK,KAAK,CAAC,MAAM,KAAK;AAC1C;AACA,SAAS,YAAY,OAAO;AACxB,SAAO,SAAS,KAAK,IAAI,WAAW,KAAK,IAAI;AACjD;AACA,SAAS,sBAAsB,iBAAiB;AAC5C,QAAM,iBAAiB,oBAAI,QAAQ;AACnC,SAAO,CAAC,UAAU,CAAC,MAAM;AACrB,UAAM,iBAAiB,oBAAI,IAAI;AAC/B,UAAM,eAAe,CAAC,OAAO,GAAGC,MAAK,KAAK,WAAW,GAAG,UAAU,UAAU;AACxE,YAAM,MAAM,GAAG,IAAI,IAAIA,GAAE,IAAI,QAAQ,IAAI,OAAO;AAChD,UAAI,CAAC,eAAe,IAAI,GAAG,GAAG;AAC1B,uBAAe,IAAI,KAAK,gBAAgB,OAAO,OAAO;AAAA,UAAE;AAAA,UACpD,IAAAA;AAAA,UACA;AAAA,QAAS,GAAG,OAAO,CAAC,CAAC;AAAA,MAC7B;AACA,aAAO,eAAe,IAAI,GAAG;AAAA,IACjC;AACA,UAAM,eAAe,CAAC,WAAW,WAAW;AACxC,UAAI,CAAC,eAAe,IAAI,SAAS,GAAG;AAChC,uBAAe,IAAI,WAAW,qBAAqB,WAAW,MAAM,CAAC;AAAA,MACzE;AACA,aAAO,eAAe,IAAI,SAAS;AAAA,IACvC;AACA,WAAO;AAAA,MACH,iBAAiB,CAAC,WAAW,iBAAiB,MAAM,WAAW,MAAM,gBAAgB;AACjF,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,WAAW;AACf,YAAI,SAAS;AACb,cAAM,eAAe,UAAU;AAM/B,YAAI,gBAAgB;AAChB,mBAAS,iBAAiB,WAAW,OAAO,qBAAqB,IAAI,aAAa,IAAI,CAAC,IAAI,MAAS;AACpG,gBAAM,mBAAmB,UAAU,eAAe,CAAC;AACnD,mBAAS,YAAY,gBAAgB;AACrC,cAAI,eAAe,KAAK,UAAU,CAAC,MAAM,MAAM;AAI3C,qBAAS,YAAY,UAAU,CAAC,CAAC;AAAA,UACrC,OACK;AACD,kBAAM,gBAAgB,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AAK5F,gBAAI,eAAe;AAKf,oBAAM,EAAE,WAAW,mBAAmB,IAAI;AAC1C,oBAAM,aAAa,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,cAAc,sBAAsB;AACvH,oBAAM,eAAe,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,gBAAgB,YAAY,IAAI,IAAI;AACzH,oBAAM,uBAAuB,cAAc,WAAW,EAAE;AACxD,uBAAS;AACT,yBAAW,sBAAsB,CAAC,MAAM,cAAc,CAAC,EAAE,SAAS,aAAa,oBAAoB;AAAA,YACvG,WACS,WAAW;AAIhB,uBAAS,YAAY,UAAU,CAAC;AAAA,YACpC;AAAA,UACJ;AAAA,QACJ;AAIA,YAAI,YAAY,MAAM,KAAK,YAAY,MAAM,GAAG;AAC5C,gBAAM,YAAY,aAAa,QAAQ,QAAQ,UAAU,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,SAAS,OAAO,CAAC;AAC3H,qBAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,WAAW,MAAM,CAAC,GAAG,EAAE,QAAQ,SAAS,CAAC;AAEjG,cAAI,aAAa;AACb,wBAAY,YAAY;AACxB,wBAAY,qBAAqB,YAAY,IAAI;AAAA,UACrD;AAAA,QACJ;AAOA,YAAI,CAAC,UAAU;AACX,gBAAM,oBAAoB,aAAa,aAAa,GAAG,GAAG,CAAC;AAC3D,qBAAW;AAAA,YACP,QAAQ;AAAA,YACR,UAAU,kBAAkB;AAAA,UAChC;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;;;AC1GA,IAAMC,UAAS,sBAAsB,MAAQ;;;ACA7C,IAAMC,SAAQ,sBAAsB,KAAO;;;ACA3C,IAAM,aAAa;AAAA,EACf,KAAK;AAAA,EACL,KAAK;AACT;AACA,SAAS,OAAO,mBAAmB,SAAS,EAAE,MAAM,QAAQ,YAAY,SAAS,MAAM,IAAI,CAAC,GAAG;AAO3F,MAAI,OAAO,yBAAyB,aAAa;AAC7C,WAAO,MAAM;AAAA,IAAE;AAAA,EACnB;AACA,QAAM,WAAW,gBAAgB,iBAAiB;AAClD,QAAM,sBAAsB,oBAAI,QAAQ;AACxC,QAAM,uBAAuB,CAAC,YAAY;AACtC,YAAQ,QAAQ,CAAC,UAAU;AACvB,YAAM,QAAQ,oBAAoB,IAAI,MAAM,MAAM;AAKlD,UAAI,MAAM,mBAAmB,QAAQ,KAAK;AACtC;AACJ,UAAI,MAAM,gBAAgB;AACtB,cAAM,WAAW,QAAQ,KAAK;AAC9B,YAAI,WAAW,QAAQ,GAAG;AACtB,8BAAoB,IAAI,MAAM,QAAQ,QAAQ;AAAA,QAClD,OACK;AACD,mBAAS,UAAU,MAAM,MAAM;AAAA,QACnC;AAAA,MACJ,WACS,OAAO;AACZ,cAAM,KAAK;AACX,4BAAoB,OAAO,MAAM,MAAM;AAAA,MAC3C;AAAA,IACJ,CAAC;AAAA,EACL;AACA,QAAM,WAAW,IAAI,qBAAqB,sBAAsB;AAAA,IAC5D;AAAA,IACA;AAAA,IACA,WAAW,OAAO,WAAW,WAAW,SAAS,WAAW,MAAM;AAAA,EACtE,CAAC;AACD,WAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,OAAO,CAAC;AACvD,SAAO,MAAM,SAAS,WAAW;AACrC;;;AClDA;;;ACAA;;;ACAA;;;ACCA,SAAS,qBAAqB,SAAS,MAAM,OAAO;AAChD,UAAQ,cAAc,IAAI,YAAY,MAAM,EAAE,QAAQ,EAAE,eAAe,MAAM,EAAE,CAAC,CAAC;AACrF;AACA,SAAS,kBAAkB,SAAS,MAAM,OAAO;AAC7C,UAAQ,cAAc,IAAI,YAAY,MAAM,EAAE,QAAQ,EAAE,eAAe,MAAM,EAAE,CAAC,CAAC;AACrF;;;ADFA,IAAMC,UAAS;AAAA,EACX,UAAU,CAAC,YAAY,QAAQ,QAAQ,MAAM;AAAA,EAC7C,WAAW,CAAC,SAAS,EAAE,QAAQ,QAAQ,GAAG,EAAE,gBAAgB,CAAC,EAAE,MAAM;AACjE,UAAM,EAAE,KAAK,IAAI,eAAe,cAAc,OAAO,eAAe,CAAC,MAAM,CAAC;AAC5E,WAAO,OAAS,SAAS,CAAC,eAAe;AACrC,aAAO;AACP,wBAAkB,SAAS,aAAa,UAAU;AAClD,UAAI,CAAC,MAAM;AACP,eAAO,CAAC,eAAe;AACnB,kBAAQ;AACR,4BAAkB,SAAS,aAAa,UAAU;AAAA,QACtD;AAAA,MACJ;AAAA,IACJ,GAAG,WAAW;AAAA,EAClB;AACJ;;;AEjBA,IAAM,aAAa,CAAC,SAAS,MAAM,WAAW,CAAC,UAAU;AACrD,MAAI,MAAM,eAAe,MAAM,gBAAgB;AAC3C;AACJ,SAAO;AACP,uBAAqB,SAAS,MAAM,KAAK;AAC7C;AACA,IAAM,QAAQ;AAAA,EACV,UAAU,CAAC,YAAY,QAAQ,QAAQ,KAAK;AAAA,EAC5C,WAAW,CAAC,SAAS,EAAE,QAAQ,QAAQ,MAAM;AACzC,UAAM,UAAU,WAAW,SAAS,cAAc,MAAM;AACxD,UAAM,UAAU,WAAW,SAAS,YAAY,OAAO;AACvD,YAAQ,iBAAiB,gBAAgB,OAAO;AAChD,YAAQ,iBAAiB,gBAAgB,OAAO;AAChD,WAAO,MAAM;AACT,cAAQ,oBAAoB,gBAAgB,OAAO;AACnD,cAAQ,oBAAoB,gBAAgB,OAAO;AAAA,IACvD;AAAA,EACJ;AACJ;;;AClBA,IAAM,QAAQ;AAAA,EACV,UAAU,CAAC,YAAY,QAAQ,QAAQ,KAAK;AAAA,EAC5C,WAAW,CAAC,SAAS,EAAE,QAAQ,QAAQ,MAAM;AACzC,UAAM,cAAc,CAAC,UAAU;AAC3B,cAAQ;AACR,2BAAqB,SAAS,YAAY,KAAK;AAC/C,aAAO,oBAAoB,aAAa,WAAW;AAAA,IACvD;AACA,UAAM,gBAAgB,CAAC,UAAU;AAC7B,aAAO;AACP,2BAAqB,SAAS,cAAc,KAAK;AACjD,aAAO,iBAAiB,aAAa,WAAW;AAAA,IACpD;AACA,YAAQ,iBAAiB,eAAe,aAAa;AACrD,WAAO,MAAM;AACT,cAAQ,oBAAoB,eAAe,aAAa;AACxD,aAAO,oBAAoB,aAAa,WAAW;AAAA,IACvD;AAAA,EACJ;AACJ;;;AJNA,IAAM,WAAW,EAAE,QAAAC,SAAQ,OAAO,MAAM;AAKxC,IAAM,aAAa,CAAC,WAAW,WAAW,GAAG,OAAO,KAAK,QAAQ,GAAG,MAAM;;;AKhB1E,SAAS,gBAAgB,QAAQ,UAAU,CAAC,GAAG;AAC3C,SAAO,aAAa;AAAA,IAChB,MAAM;AACF,YAAM,YAAY,IAAI,UAAU,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO;AACvD,gBAAU,SAAS,MAAM,MAAM;AAAA,MAAE,CAAC;AAClC,aAAO;AAAA,IACX;AAAA,EACJ,GAAG,SAAS,QAAQ,QAAQ;AAChC;AACA,SAASC,SAAQ,QAAQ,oBAAoB,SAAS;AAClD,QAAM,UAAU,WAAW,MAAM,IAAI,kBAAkB;AACvD,SAAO,QAAQ,QAAQ,oBAAoB,OAAO;AACtD;;;ACFO,IAAM,YAAY,CAAI,UAAa,UAAK,QAAL,UAAK,SAAL,QAAS;;;;ACdnD,IAAAC,KAAA,OAAA;AAAA,IAAAC,KAAA,OAAA;AAAA,IAAAC,KAAA,OAAA,UAAA;AAAA,IAAAC,KAAA,OAAA,UAAA;AAAA,IAAAC,KAAA,CAAAC,GAAAC,GAAAC,MAAAD,KAAAD,IAAAL,GAAAK,GAAAC,GAAA,EAAA,YAAA,MAAA,cAAA,MAAA,UAAA,MAAA,OAAAC,EAAA,CAAA,IAAAF,EAAAC,CAAA,IAAAC;AAAA,IAAAC,KAAA,CAAAH,GAAAC,MAAA;AAAA,WAAAC,KAAAD,MAAAA,IAAA,CAAA;AAAAJ,OAAA,KAAAI,GAAAC,CAAA,KAAAH,GAAAC,GAAAE,GAAAD,EAAAC,CAAA,CAAA;AAAA,MAAAN;AAAA,aAAAM,KAAAN,GAAAK,CAAA;AAAAH,SAAA,KAAAG,GAAAC,CAAA,KAAAH,GAAAC,GAAAE,GAAAD,EAAAC,CAAA,CAAA;AAAA,SAAAF;AAAA;AAGA,SAASI,KAAqB;AAH9B,MAAAC;AAIE,QAAMC,KAAYD,IAAAE,GAAU,MAAM,cAAhB,OAAAF,IAA6B,QAazCG,IAZmB,EACvB,OAAO,EACL,YAAY,EAAE,GAAG,iBAAiB,GAAG,oBAAoB,GAAG,mBAAmB,GAC/E,YAAY,EAAE,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,mBAAmB,GAClF,SAAS,kBACX,GACA,MAAM,EACJ,YAAY,EAAE,GAAG,oBAAoB,GAAG,oBAAoB,GAAG,mBAAmB,GAClF,YAAY,EAAE,GAAG,iBAAiB,GAAG,iBAAiB,GAAG,gBAAgB,GACzE,SAAS,wBACX,EACF,EACyCF,CAAS;AAElD,SAAO,EACL,oBAAoBE,EAAgB,WAAW,CAAC,GAChD,oBAAoBA,EAAgB,WAAW,CAAC,GAChD,oBAAoBA,EAAgB,WAAW,CAAC,GAChD,oBAAoBA,EAAgB,WAAW,CAAC,GAChD,oBAAoBA,EAAgB,WAAW,CAAC,GAChD,oBAAoBA,EAAgB,WAAW,CAAC,GAChD,uBAAuBA,EAAgB,QACzC;AACF;AAEA,SAASC,KAAwB;AAC/B,SAAO,EACL,sBAAsB,WACtB,2BAA2B,WAC3B,iBAAiB,MACjB,0BAA0B,WAC1B,kCAAkC,OAClC,iCAAiC,QACjC,mCAAmC,QACnC,yCAAyC,QACzC,yCAAyC,OACzC,6BAA6B,QAC7B,8BAA8B,QAC9B,oCAAoC,QACpC,wCAAwC,QACxC,mCAAmC,OACnC,8CAA8C,QAC9C,4BAA4B,QAC5B,8BAA8B,OAC9B,mCAAmC,QACnC,sCAAsC,WACtC,sCAAsC,QACtC,+BAA+B,QAC/B,iCAAiC,OACjC,sCAAsC,QACtC,yCAAyC,UACzC,yCAAyC,aACzC,kCAAkC,QAClC,oCAAoC,OACpC,yCAAyC,QACzC,4CAA4C,WAC5C,4CAA4C,QAC5C,8BAA8B,QAC9B,gCAAgC,OAChC,qCAAqC,QACrC,wCAAwC,WACxC,wCAAwC,QACxC,iCAAiC,QACjC,mCAAmC,OACnC,wCAAwC,QACxC,2CAA2C,WAC3C,2CAA2C,QAC3C,kCAAkC,QAClC,oCAAoC,OACpC,yCAAyC,QACzC,4CAA4C,WAC5C,4CAA4C,QAC5C,qBACE,0GACF,+BAA+B,mCAC/B,uBAAuB,kBACvB,qBAAqB,qBACrB,kCAAkC,sBAClC,iCAAiC,OACnC;AACF;AAEO,IAAMC,IAAY,EACvB,UAAUC,GAAa;AACrB,SAAOF,GAAAA,EAAwBE,CAAY;AAC7C,GAEA,WAAW;AACT,QAAMC,IAA2B,SAAS,cAAc,OAAO,GACzD,EAAE,gBAAAC,EAAe,IAAIN,GAAU;AAErC,MAAIK,GAAM;AACR,UAAME,IAAYC,GAAAA,GAAAA,GAAA,CACbX,GAAAA,GACAK,CAAAA,GAAAA,GAAAA,CACAI,GAAAA,CAAAA;AAGL,WAAO,QAAQC,CAAS,EAAE,QAAQ,CAAC,CAACH,IAAKK,CAAG,MAAMJ,EAAK,MAAM,YAAYD,IAAKK,CAAG,CAAC;EAAA;AAEtF,GAEA,WAAWC,q1BAsEb;AA1FO,IA0FP,KAAA;AChLA,IAAAC,KAAA,OAAA;AAAA,IAAAC,KAAA,OAAA;AAAA,IAAAC,IAAA,CAAArB,GAAAI,GAAAR,GAAAK,OAAA;AAAA,WAAAE,IAAAF,KAAA,IAAA,SAAAA,KAAAmB,GAAAhB,GAAAR,CAAA,IAAAQ,GAAAP,IAAAG,EAAA,SAAA,GAAAE,GAAAL,KAAA,GAAAA;AAAAA,KAAAK,IAAAF,EAAAH,CAAA,OAAAM,KAAAF,KAAAC,EAAAE,GAAAR,GAAAO,CAAA,IAAAD,EAAAC,CAAA,MAAAA;AAAA,SAAAF,MAAAE,KAAAgB,GAAAf,GAAAR,GAAAO,CAAA,GAAAA;AAAA;AAAA,IAOamB,KAAN,cAAwBC,WAAW;EAAnC,cAIwB;AAAA,UAAA,GAAA,SAAA,GAAA,KAAO,WAAY,OAEpC,KAAO,WAA+B,QAEtC,KAAO,YAAgC,QAEvC,KAAO,UAAsB,MAAM,MAEnC,KAAO,UAA2C;EAGpD;EAAA,SAAS;AACjB,UAAMC,IAAU,EACd,iBAAiB,KAAK,aAAa,QACnC,kBAAkB,KAAK,cAAc,QACrC,aAAa,KAAK,YAAY,SAC9B,eAAe,KAAK,YAAY,UAClC;AACA,QAAIC,IAAY;AAChB,WAAI,KAAK,YAAY,YACnBA,IAAY,cAEV,KAAK,YAAY,cACnBA,IAAY,WAGPC,sBACWC,SAASH,CAAO,CAAA,gBAAe,KAAK,QAAA,aAAmB,KAAK,OAAA,KACxE,KAAK,QAAA,4CACmCC,CAAAA,6BAGxC,KAAK,SAAA;EAGb;AACF;AAxCaH,GACG,SAAS,CAACX,EAAU,WAAWiB,EAAM,GAGfC,EAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAAA,GAJhBR,GAIyB,WAAA,YAAA,CAAA,GAEjBO,EAAA,CAAlBC,SAAAA,CANU,GAAAR,GAMQ,WAEAO,YAAAA,CAAAA,GAAAA,EAAA,CAAlBC,SAAAA,CAAAA,GARUR,GAQQ,WAAA,aAAA,CAAA,GAEAO,EAAA,CAAlBC,SAAS,CAAA,GAVCR,GAUQ,WAAA,WAAA,CAAA,GAEAO,EAAA,CAAlBC,SAAAA,CAZU,GAAAR,GAYQ,WAAA,WAAA,CAAA,GAZRA,KAANO,EAAA,CADNE,cAAc,YAAY,CACd,GAAAT,EAAA;AAAA,IAAA,KAAA;ACRb,IAAAF,KAAA,OAAA;AAAA,IAAAvB,KAAA,OAAA;AAAA,IAAAD,KAAA,CAAAQ,GAAAH,GAAAD,GAAAE,OAAA;AAAA,WAAAC,IAAAD,KAAA,IAAA,SAAAA,KAAAL,GAAAI,GAAAD,CAAA,IAAAC,GAAA+B,IAAA5B,EAAA,SAAA,GAAA,GAAA4B,KAAA,GAAAA;AAAAA,KAAA,IAAA5B,EAAA4B,CAAA,OAAA7B,KAAAD,KAAA,EAAAD,GAAAD,GAAAG,CAAA,IAAA,EAAAA,CAAA,MAAAA;AAAA,SAAAD,MAAAC,KAAAiB,GAAAnB,GAAAD,GAAAG,CAAA,GAAAA;AAAA;AASa,IAAA8B,KAAN,cAA2BV,WAAW;EAAtC,cAIwB;AAAA,UAAA,GAAA,SAAA,GAAA,KAAO,WAAY,OAEpC,KAAO,UAAoB;EAAA;EAG7B,SAAS;AACjB,UAAMC,IAAU,EACd,iBAAiB,KAAK,YAAY,YACpC;AAEA,WAAOE,0BACe,KAAK,QAAA,YAAkBC,SAASH,CAAO,CAAA;EAI/D;AACF;AApBaS,GACG,SAAS,CAACtB,EAAU,WAAWiB,EAAM,GAGfC,GAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAJhB,GAAAG,GAIyB,WAAA,YAAA,CAAA,GAEjBJ,GAAA,CAAlBC,SAAS,CAAA,GANCG,GAMQ,WANR,WAAA,CAAA,GAAAA,KAANJ,GAAA,CADNE,cAAc,gBAAgB,CAClB,GAAAE,EAAA;AAAA,IAAA,KAAA;ACTb,IAAAjC,KAAA,OAAA;AAAA,IAAAkC,KAAA,OAAA;AAAA,IAAAF,KAAA,CAAA9B,GAAAD,GAAAmB,GAAAhB,OAAA;AAAA,WAAAD,IAAAC,KAAA,IAAA,SAAAA,KAAA8B,GAAAjC,GAAAmB,CAAA,IAAAnB,GAAAoB,IAAAnB,EAAA,SAAA,GAAAN,GAAAyB,KAAA,GAAAA;AAAAA,KAAAzB,IAAAM,EAAAmB,CAAA,OAAAlB,KAAAC,KAAAR,EAAAK,GAAAmB,GAAAjB,CAAA,IAAAP,EAAAO,CAAA,MAAAA;AAAA,SAAAC,MAAAD,KAAAH,GAAAC,GAAAmB,GAAAjB,CAAA,GAAAA;AAAA;AAAA,IAMagC,KAAN,cAA4BZ,WAAW;EAIlC,SAAS;AACjB,WAAOG;EAKT;AACF;AAXaS,GACG,SAAS,CAACxB,EAAU,WAAWiB,EAAM,GADxCO,KAANN,GAAA,CADNE,cAAc,iBAAiB,CACnB,GAAAI,EAAA;AAAA,ICJAC,IAAU,EACrB,YAAYC,0UASZ,qBAAqBA,stHASrB,qBAAqBA,wwBAcrB,6BAA6BA,o4CAa7B,WAAWA,ySAWX,WAAWA,klDAWX,YAAYA,8WASZ,cAAcA,qkCAed,aAAaA,y/BAYb,iBAAiBA,wRASjB,qBAAqBA,sRASrB,kBAAkBA,6SAWlB,aAAaA,0zEAmBb,WAAWA,i1CASX,gBAAgBA,sSAWhB,aAAaA,mSAWb,oBAAoBA,o0HAkCpB,YAAYA,krCAUd;AD/Na,IC+Nb,KAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,CAAA,GAAA,GAAA,GAAArC,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;AC7Na,IAAAsC,KAAN,cAA+Bf,WAAW;EAIrC,SAAS;AACjB,WAAOG,2EAGDU,EAAQ,mBAAA,oBACOG,GAAU,KAAA,KAASH,EAAQ,UAAA;EAGlD;AACF;AAbaE,GACG,SAAS,CAAC3B,EAAU,WAAWiB,EAAM,GADxCU,KAANT,GAAA,CADNE,cAAc,oBAAoB,CACtB,GAAAO,EAAA;AAAA,IAAA,KAAA;ACRb,IAAAtC,KAAA,OAAA;AAAA,IAAAmB,KAAA,OAAA;AAAA,IAAAE,KAAA,CAAAnB,GAAAD,GAAAG,GAAAgB,OAAA;AAAA,WAAAjB,IAAAiB,KAAA,IAAA,SAAAA,KAAAD,GAAAlB,GAAAG,CAAA,IAAAH,GAAA+B,IAAA9B,EAAA,SAAA,GAAAN,GAAAoC,KAAA,GAAAA;AAAAA,KAAApC,IAAAM,EAAA8B,CAAA,OAAA7B,KAAAiB,KAAAxB,EAAAK,GAAAG,GAAAD,CAAA,IAAAP,EAAAO,CAAA,MAAAA;AAAA,SAAAiB,MAAAjB,KAAAH,GAAAC,GAAAG,GAAAD,CAAA,GAAAA;AAAA;AAAA,IAMaqC,KAAN,cAA8BjB,WAAW;EAIpC,SAAS;AACjB,WAAOG;EAKT;AACF;AAXac,GACG,SAAS,CAAC7B,EAAU,WAAWiB,EAAM,GADxCY,KAANX,GAAA,CADNE,cAAc,mBAAmB,CACrB,GAAAS,EAAA;AAAA,IAAA,KAAA;ACNb,IAAAnB,KAAA,OAAA;AAAA,IAAArB,KAAA,OAAA;AAAA,IAAAyC,KAAA,CAAArB,GAAAnB,GAAA+B,GAAA9B,OAAA;AAAA,WAAAC,IAAAD,KAAA,IAAA,SAAAA,KAAAF,GAAAC,GAAA+B,CAAA,IAAA/B,GAAA,IAAAmB,EAAA,SAAA,GAAAxB,GAAA,KAAA,GAAA;AAAA,KAAAA,IAAAwB,EAAA,CAAA,OAAAjB,KAAAD,KAAAN,EAAAK,GAAA+B,GAAA7B,CAAA,IAAAP,EAAAO,CAAA,MAAAA;AAAA,SAAAD,MAAAC,KAAAkB,GAAApB,GAAA+B,GAAA7B,CAAA,GAAAA;AAAA;AAAA,IAMauC,KAAN,cAA6BnB,WAAW;EAInC,SAAS;AACjB,WAAOG;EAKT;AACF;AAXagB,GACG,SAAS,CAAC/B,EAAU,WAAWiB,EAAM,GADxCc,KAANb,GAAA,CADNE,cAAc,kBAAkB,CACpB,GAAAW,EAAA;AAAA,IAAA,KAAA;ACNb,IAAAR,KAAA,OAAA;AAAA,IAAAnC,KAAA,OAAA;AAAA,IAAAiC,MAAA,CAAAb,GAAAE,GAAAxB,GAAAM,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAAJ,GAAAsB,GAAAxB,CAAA,IAAAwB,GAAAnB,IAAAiB,EAAA,SAAA,GAAAC,GAAAlB,KAAA,GAAAA;AAAAA,KAAAkB,IAAAD,EAAAjB,CAAA,OAAA,KAAAC,KAAAiB,EAAAC,GAAAxB,GAAA,CAAA,IAAAuB,EAAA,CAAA,MAAA;AAAA,SAAAjB,MAAA,KAAA+B,GAAAb,GAAAxB,GAAA,CAAA,GAAA;AAAA;AAUa,IAAA8C,IAAN,cAA6BpB,WAAW;EAAxC,cAIO;AAAA,UAAA,GAAA,SAAA,GAAA,KAAO,QAAQ,IAEf,KAAO,WAAwB,QAE/B,KAAO,aAAiC,QAEvB,KAAO,SAAS;EAGrC;EAAA,kBAAkB;AACxB,WAAOG,4CACiCkB,EAAW,MAAA,KAAUR,EAAQ,SAAA;EAEvE;EAEQ,oBAAoB;AAC1B,WAAOV,8CAA6C,KAAK,QAAA,KAAY,KAAK,UAAA;EAC5E;EAGU,SAAS;AACjB,UAAMF,IAAU,EACd,cAAc,KAAK,OACrB,GACMqB,IAAUD,EAAW,MAAM,QAAQ,SAAS,GAE5CE,IAAU,KAAK,QACjBpB,oCAAoC,KAAK,KAAA,gBACzCA;AAEJ,WAAOA,sBACWC,SAASH,CAAO,CAAA,KAC5BqB,IAAU,KAAK,gBAAA,IAAoB,IAAA,IAAQC,CAAAA,IAC3C,KAAK,WAAW,KAAK,kBAAA,IAAsB,IAAA;EAGnD;AACF;AAzCaH,EACG,SAAS,CAAChC,EAAU,WAAWiB,EAAM,GAGhCC,IAAA,CAAlBC,SAAAA,CAAAA,GAJUa,EAIQ,WAAA,SAAA,CAAA,GAEAd,IAAA,CAAlBC,SAAAA,CANU,GAAAa,EAMQ,WAEAd,YAAAA,CAAAA,GAAAA,IAAA,CAAlBC,SAAS,CAAA,GARCa,EAQQ,WAAA,cAAA,CAAA,GAEiBd,IAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAVhB,GAAAa,EAUyB,WAAA,UAAA,CAAA,GAVzBA,IAANd,IAAA,CADNE,cAAc,kBAAkB,CACpB,GAAAY,CAAA;ACCN,IAAMI,IAAS,EACpB,mBAAmB,KAEnB,wBAAwB,0BAExB,qBAAqB,mDAErB,qBAAqBlC,GAAkBmC,GAAkB;AACvD,QAAMC,IAAKpC,EAAK,WAAW,cAAcmC,CAAQ;AACjD,MAAI,CAACC;AACH,UAAM,IAAI,MAAM,GAAGD,CAAAA,YAAoB;AAGzC,SAAOC;AACT,GAEA,cAAc,EAAE,IAAAC,GAAI,UAAAC,EAAS,GAAsC;AACjE,QAAM,EAAE,cAAAC,EAAa,IAAIC,EAAW;AAEpC,SAAID,KAAA,QAAAA,EAAeF,CAAAA,IACVE,EAAaF,CAAE,IACbC,IACFG,GAAa,kBAAkBH,CAAQ,IAGzC;AACT,GAEA,cAAcI,GAAcC,IAAQ,OAAO;AACzC,SAAOA,KAASD,EAAK,SAAS,IAAI,GAAGA,EAAK,UAAU,GAAG,CAAC,CAAA,OAAQA;AAClE,GAEA,oBAAoB;AAClB,SAAO,OAAO,cAAcR,EAAO;AACrC,GAEA,MAAM,aAAaU,GAAa;AAC9B,QAAMC,IAAe,IAAI,QAAQ,CAACC,GAASC,OAAW;AACpD,UAAMC,IAAQ,IAAI;AAClBA,MAAM,SAASF,GACfE,EAAM,UAAUD,IAChBC,EAAM,cAAc,aACpBA,EAAM,MAAMJ;EACd,CAAC;AAED,SAAO,QAAQ,KAAK,CAACC,GAAcI,EAAS,KAAK,GAAK,CAAC,CAAC;AAC1D,GAEA,gBAAgBC,GAAc;AAC5B,SAAOA,aAAe,QAAQA,EAAI,UAAU;AAC9C,GAGA,SAASC,GAAmCC,IAAU,KAAK;AACzD,MAAIC;AAEJ,SAAO,IAAIC,OAAoB;AAC7B,aAASC,IAAO;AACdJ,QAAK,GAAGG,EAAI;IACd;AACID,SACF,aAAaA,CAAK,GAEpBA,IAAQ,WAAWE,GAAMH,CAAO;EAClC;AACF,GAEA,oBAAoBI,GAAoB;AACtC,QAAM,EAAE,kBAAAC,EAAiB,IAAIC,EAAY,OACnC,EAAE,QAAAC,GAAQ,MAAAjB,GAAK,IAAIc,GACnBI,IAAYD,uBAAQ,QACpBE,IAAeF,uBAAQ;AAE7BzB,IAAO,gBAAgBsB,CAAM;AAE7B,WAASM,EAAWC,GAAa;AAC/B,QAAIC,IAAO;AACPJ,QACFI,IAAOf,EAAS,mBAAmBW,GAAWG,GAAKrB,EAAI,IAC9CmB,MACTG,IAAOf,EAAS,gBAAgBY,GAAcE,GAAKrB,EAAI,IAEzDO,EAAS,SAASe,GAAM,OAAO;EACjC;AAEIP,OACFK,EAAWL,CAAgB;AAE/B,GAEA,uBAAuB;AACrB,QAAM,EAAE,kBAAAA,EAAiB,IAAIC,EAAY;AAErCD,QACFR,EAAS,gCAAgCQ,CAAgB,GACzDR,EAAS,SAASQ,GAAkB,OAAO;AAE/C,GAEA,MAAM,gBAAgB;AACpB,QAAM,EAAE,kBAAAA,EAAiB,IAAIC,EAAY;AACzC,MAAID;AACF,QAAI;AACF,YAAM,UAAU,UAAU,UAAUA,CAAgB,GACpDQ,GAAU,UAAU,eAAe,SAAS;IAC9C,QAAA;AACEA,SAAU,UAAU,kBAAkB,OAAO;IAC/C;AAEJ,GAEA,qBAAqB;AACnB,QAAM,EAAE,cAAA1B,EAAa,IAAIC,EAAW,OAC9B0B,IAAa,OAAO,OAAO3B,KAAgB,CAAE,CAAA;AAEnD,SAAO,OAAO,OAAO2B,CAAU;AACjC,GAEA,SAASC,GAAeC,IAAS,GAAG;AAClC,SAAID,EAAM,UAAUC,IACXD,IAGF,GAAGA,EAAM,UAAU,GAAG,CAAC,CAAA,MAAOA,EAAM,UAAUA,EAAM,SAAS,CAAC,CAAA;AACvE,GAEA,gBAAgBX,GAAoB;AAClC,MAAI;AACF,iBAAa,QAAQtB,EAAO,wBAAwB,KAAK,UAAUsB,CAAM,CAAC;EAC5E,QAAQlE;AACN,YAAQ,KAAK,6BAA6B;EAC5C;AACF,GAEA,kBAAkB;AAChB,MAAI;AACF,UAAMkE,IAAS,aAAa,QAAQtB,EAAO,sBAAsB;AACjE,WAAIsB,IACW,KAAK,MAAMA,CAAM,IAKhC;EACF,QAAQ;AACN,YAAQ,KAAK,6BAA6B;EAC5C;AAGF,GAEA,iBAAiBa,GAAcC,GAAc;AAC3C,SAAOD,EAAK,YAAY,EAAE,SAASC,EAAK,YAAa,CAAA;AACvD,GAEA,wBAAwB;AACtBrB,IAAS,SAASf,EAAO,qBAAqB,QAAQ;AACxD,GAEA,iCAAiC;AAC/B,QAAM,EAAE,SAAAqC,GAAS,QAAAZ,EAAO,IAAIV,EAAS,oBAAoB,GACnDuB,IAAY,QAAQD,uBAAS,MAAM,GACnCE,KAAQ,QAAQF,uBAAS,SAAS,GAClCG,IAAW,QAAQf,uBAAQ,MAAM,KAAK,QAAQA,uBAAQ,SAAS;AAErE,SAAO,EAAE,WAAAa,GAAW,UAAAE,GAAU,OAAAD,GAAM;AACtC,GAEA,mBAAmBjB,GAAoB;AACrCzB,IAAW,QAAQ,EAAE,QAAQyB,EAAO,CAAC;AACrC,QAAMmB,IAAiB1B,EAAS,SAAS,GACnC,EAAE,WAAAuB,GAAW,OAAAC,IAAO,UAAAC,EAAS,IAAIxC,EAAO,+BAAA;AAG1CyC,MACED,IACF3C,EAAW,KAAK,kBAAkB,IACzB0C,KACT1C,EAAW,KAAK,eAAe,IAE/BA,EAAW,KAAK,eAAe,IAK1ByC,IACPzC,EAAW,KAAK,mBAAmB,IAC1B0C,KACT1C,EAAW,KAAK,eAAe,IACtB2C,IACT3C,EAAW,KAAK,wBAAwB,IAExCA,EAAW,KAAK,eAAe;AAEnC,EACF;AAnMO,IAmMP,KAAA;AC7MA,IAAA6C,KAAA,OAAA;AAAA,IAAAvD,KAAA,OAAA;AAAA,IAAAf,KAAA,CAAAvB,GAAAC,GAAAM,GAAAF,OAAA;AAAA,WAAAC,IAAAD,KAAA,IAAA,SAAAA,KAAAiC,GAAArC,GAAAM,CAAA,IAAAN,GAAA,IAAAD,EAAA,SAAA,GAAA8F,GAAA,KAAA,GAAA;AAAA,KAAAA,IAAA9F,EAAA,CAAA,OAAAM,KAAAD,KAAAyF,EAAA7F,GAAAM,GAAAD,CAAA,IAAAwF,EAAAxF,CAAA,MAAAA;AAAA,SAAAD,MAAAC,KAAAuF,GAAA5F,GAAAM,GAAAD,CAAA,GAAAA;AAAA;AASO,IAAMyF,MAAN,cAA6BpE,WAAW;EAStC,cAAc;AACnB,UAAA,GANO,KAAO,OAAmBqB,EAAW,MAAM,MAE3C,KAAO,WAAuBA,EAAW,MAAM,MA6BxD,KAAiB,cAA2B,QAE5C,KAAQ,YAAY,OAEpB,KAAQ,iBAAkC,QA5BxC,KAAK,cAAcA,EAAW,UAAUgD,OAAe;AACjD,WAAK,SAASA,EAAY,QAC5B,KAAK,cAAc;IAEvB,CAAC;EACH;EAEO,eAAe;AACpB,SAAK,iBAAiB,IAAI,eAAe,CAAC,CAACC,CAAO,MAAM;AACtD,YAAMC,IAAY,GAAGD,EAAQ,YAAY,MAAA;AACrC,WAAK,cAAc,SACrBE,SAAQ,KAAK,UAAU,EAAE,QAAQ,CAAC,KAAK,WAAWD,CAAS,EAAE,GAAG,EAAE,UAAU,IAAI,CAAC,GAEnF,KAAK,YAAYA;IACnB,CAAC,GACD,KAAK,eAAe,QAAQ,KAAK,SAAS;EAC5C;EAEO,uBAAuB;AAvChC,QAAAxF,GAAA0F;AAAAA,KAwCI1F,IAAA,KAAK,gBAAL,QAAAA,EAAA,KAAA,IAAA,IACA0F,IAAA,KAAK,mBAAL,QAAAA,EAAqB,WACvB;EAAA;EASA,IAAY,WAAW;AACrB,WAAOjD,EAAO,qBAAqB,MAAM,aAAa;EACxD;EAEA,IAAY,YAAY;AACtB,WAAOA,EAAO,qBAAqB,MAAM,cAAc;EACzD;EAEQ,eAAe;AACrB,YAAQ,KAAK,MAAA;MACX,KAAK;AACH,eAAOrB;MACT,KAAK;AACH,eAAOA;MACT,KAAK;AACH,eAAOA;MACT,KAAK;AACH,eAAOA;MACT,KAAK;AACH,eAAOA;MACT,KAAK;AACH,eAAOA;MACT,KAAK;AACH,eAAOA;MACT,KAAK;AACH,eAAOA;MACT;AACE,eAAOA;IACX;EACF;EAEA,MAAc,gBAAgB;AAC5B,UAAMqE,SACJ,KAAK,UACL,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,IAAI,EAAE,GACpC,EAAE,UAAU,MAAM,OAAO,IAAI,CAC/B,EAAE,UACF,KAAK,OAAOnD,EAAW,MAAM,MAC7BmD,SAAQ,KAAK,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,UAAU,MAAM,OAAO,KAAK,CAAC;EAC/F;EAGU,SAAS;AACjB,WAAOrE,wDAEwB,KAAK,aAAa,CAAA;EAGnD;AACF;AA1FaiE,IACG,SAAS,CAAChF,EAAU,WAAWiB,EAAM,GAGnCC,GAAA,CAAfoE,MAAM,CAAA,GAJIN,IAIK,WAAA,QAAA,CAAA,GAEA9D,GAAA,CAAfoE,MANU,CAAA,GAAAN,IAMK,WAAA,YAAA,CAAA,GANLA,MAAN9D,GAAA,CADNE,cAAc,kBAAkB,CACpB,GAAA4D,GAAA;AAAA,IAAA,KAAA;ACVb,IAAA3F,KAAA,OAAA;AAAA,IAAAH,KAAA,OAAA;AAAA,IAAAuB,KAAA,CAAAY,GAAA9B,GAAAC,GAAAF,OAAA;AAAA,WAAAL,IAAAK,KAAA,IAAA,SAAAA,KAAAJ,GAAAK,GAAAC,CAAA,IAAAD,GAAA,IAAA8B,EAAA,SAAA,GAAAb,GAAA,KAAA,GAAA;AAAA,KAAAA,IAAAa,EAAA,CAAA,OAAApC,KAAAK,KAAAkB,EAAAjB,GAAAC,GAAAP,CAAA,IAAAuB,EAAAvB,CAAA,MAAAA;AAAA,SAAAK,MAAAL,KAAAI,GAAAE,GAAAC,GAAAP,CAAA,GAAAA;AAAA;AASa,IAAAsG,MAAN,cAA4B3E,WAAW;EAMrC,cAAc;AACnB,UAAM,GAHC,KAAO,OAAO,OAsBvB,KAAiB,cAA2B,QAE5C,KAAQ,UAA2B,QApBjC,KAAK,cAAcuD,GAAU,UAAUqB,OAAY;AAC7CA,QAAS,QACX,KAAK,OAAO,MACZ,KAAK,UAAU,WAAW,MAAMrB,GAAU,WAAA,GAAc,IAAI,MAE5D,KAAK,OAAO,OACZ,aAAa,KAAK,OAAO;IAE7B,CAAC;EACH;EAEO,uBAAuB;AA5BhC,QAAAxE;AAAAA,KA6BIA,IAAA,KAAK,gBAAL,QAAAA,EAAA,KACA,IAAA,GAAA,aAAa,KAAK,OAAO,GACzBwE,GAAU,WAAA;EACZ;EAQU,SAAS;AACjB,UAAM,EAAE,SAAAsB,GAAS,SAAAC,EAAQ,IAAIvB,GAAU,OACjCtD,IAAU,EACd,eAAe6E,MAAY,WAC3B,aAAaA,MAAY,QAC3B;AAEA,WAAO,KAAK,OACR3E,mBACeC,SAASH,CAAO,CAAA,KACzB6E,MAAY,YAAYjE,EAAQ,iBAAiB,IAAA,IACjDiE,MAAY,UAAUjE,EAAQ,aAAa,IAAA,qCACTgE,CAAAA,sBAGxC;EACN;AACF;AAhDaF,IACG,SAAS,CAACvF,EAAU,WAAWiB,EAAM,GAGnCC,GAAA,CAAfoE,MAAAA,CAJU,GAAAC,IAIK,WAAA,QAAA,CAAA,GAJLA,MAANrE,GAAA,CADNE,cAAc,iBAAiB,CACnB,GAAAmE,GAAA;ACHb,IAAMI,KAA0B;AAAhC,IACMC,KAAuB;AAD7B,IAEMC,IAAuB;AAE7B,SAASC,GAAeC,GAAYC,GAAiBC,GAAkB;AACrE,SAAIF,MAAOC,IACF,SAEID,IAAKC,IAAU,IAAIA,IAAUD,IAAKA,IAAKC,MAErCC,IAAWN;AAC5B;AAEA,SAASO,GAAU7B,GAAe8B,GAA6D;AAC7F,QAAMC,IAAM,MAAM,UAAU,MAAM,KAChCC,cAAAA,QAAW,OAAOhC,GAAO,EAAE,sBAAA8B,EAAqB,CAAC,EAAE,QAAQ,MAC3D,CACF,GACMG,KAAO,KAAK,KAAKF,EAAI,MAAM;AAEjC,SAAOA,EAAI,OACT,CAACG,GAAMtG,GAAKuG,OACTA,IAAQF,OAAS,IAAIC,EAAK,KAAK,CAACtG,CAAG,CAAC,IAAIsG,EAAKA,EAAK,SAAS,CAAC,EAAE,KAAKtG,CAAG,MAAMsG,GAC/E,CAAA,CACF;AACF;AAEO,IAAME,KAAa,EACxB,SAASxC,GAAayC,GAAcC,GAAkB;AACpD,QAAMC,KAAW,WACXC,IAAY,WACZC,IAAyB,CAAA,GACzBC,IAASb,GAAUjC,GAAK,GAAG,GAC3BgC,IAAWS,IAAOK,EAAO,QACzBC,IAAS,CACb,EAAE,GAAG,GAAG,GAAG,EAAE,GACb,EAAE,GAAG,GAAG,GAAG,EAAE,GACb,EAAE,GAAG,GAAG,GAAG,EAAE,CACf;AAEAA,IAAO,QAAQ,CAAC,EAAE,GAAAC,IAAG,GAAAC,EAAE,MAAM;AAC3B,UAAMC,KAAMJ,EAAO,SAASlB,KAAwBI,IAAWgB,IACzDG,KAAML,EAAO,SAASlB,KAAwBI,IAAWiB,GACzDG,IAAe;AACrB,aAAS3G,IAAI,GAAGA,IAAIsG,EAAO,QAAQtG,KAAK,GAAG;AACzC,YAAM4G,IAAUrB,KAAYJ,IAAuBnF,IAAI;AACvDoG,QAAK,KACHpF,kBAEWhB,IAAI,MAAM,IAAIkG,KAAWC,CAAAA,aACvBS,CAAAA,SACJA,IAAUD,CAAAA,SACVC,IAAUD,CAAAA,YACPC,CAAAA,QACJH,IAAKlB,IAAWvF,CAAAA,QAChB0G,IAAKnB,IAAWvF,CAAAA,IAG1B;IAAA;EAEJ,CAAC;AAED,QAAM6G,IAAiB,KAAK,OAAOZ,IAAW,MAAMV,CAAQ,GACtDuB,KAAoBT,EAAO,SAAS,IAAIQ,IAAiB,GACzDE,KAAkBV,EAAO,SAAS,IAAIQ,IAAiB,IAAI,GAC3DG,KAA8B,CAAC;AAGrCX,IAAO,QAAQ,CAACY,IAA0BjH,MAAc;AACtDiH,IAAAA,GAAI,QAAQ,CAACC,GAAGC,MAAc;AAC5B,UAAId,EAAOrG,CAAC,EAAEmH,CAAC,KAEX,EACGnH,IAAImF,KAAwBgC,IAAIhC,KAChCnF,IAAIqG,EAAO,UAAUlB,IAAuB,MAAMgC,IAAIhC,KACtDnF,IAAImF,KAAwBgC,IAAId,EAAO,UAAUlB,IAAuB,OAIzE,EACEnF,IAAI8G,MACJ9G,IAAI+G,MACJI,IAAIL,MACJK,IAAIJ,KAEN;AACA,cAAMK,IAAKpH,IAAIuF,IAAWA,IAAW,GAC/BF,IAAK8B,IAAI5B,IAAWA,IAAW;AACrCyB,WAAQ,KAAK,CAACI,GAAI/B,CAAE,CAAC;MAAA;IAI7B,CAAC;EACH,CAAC;AAGD,QAAMgC,IAA6C,CAAA;AAGnD,SAAAL,GAAQ,QAAQ,CAAC,CAACI,IAAI/B,CAAE,MAAM;AAExBgC,MAAiBD,EAAE,IACrBC,EAAiBD,EAAE,EAAE,KAAK/B,CAAE,IAE5BgC,EAAiBD,EAAE,IAAI,CAAC/B,CAAE;EAE9B,CAAC,GAGD,OAAO,QAAQgC,CAAgB,EAE5B,IAAI,CAAC,CAACD,IAAIE,CAAG,MAAM;AAClB,UAAMC,IAASD,EAAI,OAAOjC,OACxBiC,EAAI,MAAMhC,OAAW,CAACF,GAAeC,GAAIC,GAASC,CAAQ,CAAC,CAC7D;AAEA,WAAO,CAAC,OAAO6B,EAAE,GAAGG,CAAM;EAC5B,CAAC,EACA,QAAQ,CAAC,CAACH,IAAIE,CAAG,MAAM;AACtBA,MAAI,QAAQjC,OAAM;AAChBe,QAAK,KACHpF,kBAAiBoG,EAAAA,SAAS/B,CAAAA,WAAWa,EAAAA,QAAcX,IAAWL,EAAAA,IAChE;IACF,CAAC;EACH,CAAC,GAGH,OAAO,QAAQmC,CAAgB,EAE5B,OAAO,CAAC,CAACH,IAAGI,CAAG,MAAMA,EAAI,SAAS,CAAC,EAEnC,IAAI,CAAC,CAACF,IAAIE,CAAG,MAAM;AAClB,UAAMC,IAASD,EAAI,OAAOjC,OAAMiC,EAAI,KAAKhC,OAAWF,GAAeC,GAAIC,GAASC,CAAQ,CAAC,CAAC;AAE1F,WAAO,CAAC,OAAO6B,EAAE,GAAGG,CAAM;EAC5B,CAAC,EAEA,IAAI,CAAC,CAACH,IAAIE,CAAG,MAAM;AAClBA,MAAI,KAAK,CAAC3I,GAAGD,MAAOC,IAAID,IAAI,KAAK,CAAE;AACnC,UAAM8I,IAAqB,CAAA;AAE3B,eAAWnC,KAAMiC,GAAK;AACpB,YAAMG,IAAQD,EAAO,KAAKE,OACxBA,EAAK,KAAKpC,OAAWF,GAAeC,GAAIC,GAASC,CAAQ,CAAC,CAC5D;AACIkC,UACFA,EAAM,KAAKpC,CAAE,IAEbmC,EAAO,KAAK,CAACnC,CAAE,CAAC;IAAA;AAIpB,WAAO,CAAC+B,IAAII,EAAO,IAAIE,OAAQ,CAACA,EAAK,CAAC,GAAGA,EAAKA,EAAK,SAAS,CAAC,CAAC,CAAC,CAAC;EAClE,CAAC,EACA,QAAQ,CAAC,CAACN,IAAII,CAAM,MAAM;AACzBA,MAAO,QAAQ,CAAC,CAACd,GAAIiB,CAAE,MAAM;AAC3BvB,QAAK,KACHpF,gBAESoG,EAAAA,SACAA,EAAAA,SACAV,CAAAA,SACAiB,CAAAA,aACIzB,EAAAA,mBACMX,KAAYL,KAAuB,EAAA,2BAIxD;IACF,CAAC;EACH,CAAC,GAEIkB;AACT,EACF;AAnJO,IAmJP,KAAA;ACpLA,IAAAwB,KAAA,OAAA;AAAA,IAAApJ,KAAA,OAAA;AAAA,IAAAwB,IAAA,CAAAD,GAAAhB,GAAAR,GAAAO,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAAN,GAAAO,GAAAR,CAAA,IAAAQ,GAAAJ,IAAAoB,EAAA,SAAA,GAAAtB,GAAAE,KAAA,GAAAA;AAAAA,KAAAF,IAAAsB,EAAApB,CAAA,OAAA,KAAAG,KAAAL,EAAAM,GAAAR,GAAA,CAAA,IAAAE,EAAA,CAAA,MAAA;AAAA,SAAAK,MAAA,KAAA8I,GAAA7I,GAAAR,GAAA,CAAA,GAAA;AAAA;AAAA,IAWasJ,IAAN,cAAwB3H,WAAW;EAAnC,cAAA;AAAA,UAAA,GAAA,SAAA,GAIO,KAAO,MAAM,IAEG,KAAO,OAAO,GAE9B,KAAO,UAAmB,QAE1B,KAAO,WAAoB,QAE3B,KAAO,WAAoB;EAG/B;EAAA,cAAc;AAEpB,UAAM8F,IADc7G,GAAU,MAAM,cAAc,UACvB,KAAK,OAAO,KAAK,OAAO;AAEnD,WAAO6B,mBACSgF,CAAAA,YAAcA,CAAAA,KACxBD,GAAW,SAAS,KAAK,KAAKC,GAAMA,IAAO,CAAC,CAAA;EAGpD;EAGU,SAAS;AACjB,UAAM7F,IAAU,EACd,YAAYhB,GAAU,MAAM,cAAc,OAC5C;AAEA,WAAOkB,mBACQ,UAAU,KAAK,IAAA,IAAA,YAAkBC,SAASH,CAAO,CAAA,KAC1D,KAAK,YAAY,KAAK,WACpBE,mCAEeyH,UAAU,KAAK,QAAQ,CAAA,cACxBA,UAAU,KAAK,OAAO,CAAA,eACrBA,UAAU,KAAK,QAAQ,CAAA,0BAGtC/G,EAAQ,2BAAA,IACV,KAAK,YAGb,CAAA;EAAA;AACF;AA/Ca8G,EACG,SAAS,CAACvI,EAAU,WAAWiB,EAAM,GAGhCC,EAAA,CAAlBC,SAAAA,CAAAA,GAJUoH,EAIQ,WAAA,OAAA,CAAA,GAEgBrH,EAAA,CAAlCC,SAAS,EAAE,MAAM,OAAO,CAAC,CANf,GAAAoH,EAMwB,WAAA,QAAA,CAAA,GAEhBrH,EAAA,CAAlBC,SAAS,CAAA,GARCoH,EAQQ,WAEArH,WAAAA,CAAAA,GAAAA,EAAA,CAAlBC,SAAS,CAAA,GAVCoH,EAUQ,WAEArH,YAAAA,CAAAA,GAAAA,EAAA,CAAlBC,SAAS,CAAA,GAZCoH,EAYQ,WAZR,YAAA,CAAA,GAAAA,IAANrH,EAAA,CADNE,cAAc,YAAY,CACd,GAAAmH,CAAA;AAAA,IAAA,KAAA;ACXb,IAAAhH,KAAA,OAAA;AAAA,IAAAlC,KAAA,OAAA;AAAA,IAAAJ,KAAA,CAAAM,GAAAD,GAAA+B,GAAA5B,OAAA;AAAA,WAAAD,IAAAC,KAAA,IAAA,SAAAA,KAAAJ,GAAAC,GAAA+B,CAAA,IAAA/B,GAAAoB,IAAAnB,EAAA,SAAA,GAAAkB,GAAAC,KAAA,GAAAA;AAAAA,KAAAD,IAAAlB,EAAAmB,CAAA,OAAAlB,KAAAC,KAAAgB,EAAAnB,GAAA+B,GAAA7B,CAAA,IAAAiB,EAAAjB,CAAA,MAAAA;AAAA,SAAAC,MAAAD,KAAA+B,GAAAjC,GAAA+B,GAAA7B,CAAA,GAAAA;AAAA;AAOa,IAAAiJ,KAAN,cAA6B7H,WAAW;EAAxC,cAAA;AAAA,UAAA,GAAA,SAAA,GAGO,KAAO,WAAW,MAAM;EAAA;EAG1B,SAAS;AACjB,WAAOG,kCACuB,KAAK,QAAA,mCAC/BU,EAAQ,WAAA;EAEd;AACF;AAZagH,GACG,SAAS,CAACzI,EAAU,WAAWiB,EAAM,GAEhCC,GAAA,CAAlBC,SAAAA,CAHU,GAAAsH,GAGQ,WAHR,YAAA,CAAA,GAAAA,KAANvH,GAAA,CADNE,cAAc,kBAAkB,CACpB,GAAAqH,EAAA;AAAA,IAAA,KAAA;ACPb,IAAAjI,KAAA,OAAA;AAAA,IAAAnB,KAAA,OAAA;AAAA,IAAAoB,KAAA,CAAAhB,GAAAH,GAAA+B,GAAA9B,OAAA;AAAA,WAAAC,IAAAD,KAAA,IAAA,SAAAA,KAAAF,GAAAC,GAAA+B,CAAA,IAAA/B,GAAAL,IAAAQ,EAAA,SAAA,GAAA,GAAAR,KAAA,GAAAA;AAAAA,KAAA,IAAAQ,EAAAR,CAAA,OAAAO,KAAAD,KAAA,EAAAD,GAAA+B,GAAA7B,CAAA,IAAA,EAAAA,CAAA,MAAAA;AAAA,SAAAD,MAAAC,KAAAgB,GAAAlB,GAAA+B,GAAA7B,CAAA,GAAAA;AAAA;AAAA,IAMakJ,KAAN,cAAyB9H,WAAW;EAI/B,SAAS;AACjB,WAAOG;EAKT;AACF;AAXa2H,GACG,SAAS,CAAC1I,EAAU,WAAWiB,EAAM,GADxCyH,KAANxH,GAAA,CADNE,cAAc,aAAa,CACf,GAAAsH,EAAA;AAAA,IAAA,KAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,CAAA,GAAA,GAAA,GAAArJ,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;ACWA,IAAAsJ,KAAN,cAAsB/H,WAAW;EAAjC,cAAA;AAAA,UAAA,GAAA,SAAA,GAIO,KAAO,UAAoB,kBAE3B,KAAO,QAAyB;EAGlC;EAAA,SAAS;AACjB,UAAMC,IAAU,EACd,gBAAgB,KAAK,YAAY,YACjC,sBAAsB,KAAK,YAAY,kBACvC,qBAAqB,KAAK,YAAY,iBACtC,kBAAkB,KAAK,YAAY,cACnC,sBAAsB,KAAK,YAAY,kBACvC,mBAAmB,KAAK,YAAY,eACpC,qBAAqB,KAAK,UAAU,WACpC,uBAAuB,KAAK,UAAU,aACtC,sBAAsB,KAAK,UAAU,YACrC,qBAAqB,KAAK,UAAU,WACpC,mBAAmB,KAAK,UAAU,UAClC,mBAAmB,KAAK,UAAU,QACpC;AAEA,WAAOE,0BAEWC,SAASH,CAAO,CAAA;EAGpC;AACF;AA/Ba8H,GACG,SAAS,CAAC3I,EAAU,WAAWiB,EAAM,GAGhCC,GAAA,CAAlBC,SAJU,CAAA,GAAAwH,GAIQ,WAEAzH,WAAAA,CAAAA,GAAAA,GAAA,CAAlBC,SAAAA,CANU,GAAAwH,GAMQ,WANR,SAAA,CAAA,GAAAA,KAANzH,GAAA,CADNE,cAAc,UAAU,CACZ,GAAAuH,EAAA;AAAA,IAAA,KAAA;ACjBb,IAAAvJ,KAAA,OAAA;AAAA,IAAAwJ,KAAA,OAAA;AAAA,IAAApJ,IAAA,CAAAP,GAAAI,GAAAC,GAAAC,OAAA;AAAA,WAAAE,IAAAF,KAAA,IAAA,SAAAA,KAAAqJ,GAAAvJ,GAAAC,CAAA,IAAAD,GAAAoB,IAAAxB,EAAA,SAAA,GAAAC,GAAAuB,KAAA,GAAAA;AAAAA,KAAAvB,IAAAD,EAAAwB,CAAA,OAAAhB,KAAAF,KAAAL,EAAAG,GAAAC,GAAAG,CAAA,IAAAP,EAAAO,CAAA,MAAAA;AAAA,SAAAF,MAAAE,KAAAL,GAAAC,GAAAC,GAAAG,CAAA,GAAAA;AAAA;AASa,IAAAoJ,IAAN,cAA8BjI,WAAW;EAAzC,cAAA;AAAA,UAAA,GAAA,SAAA,GAIO,KAAO,UAAsB,MAAM,MAEnC,KAAO,OAAO,IAEd,KAAO,WAAW,IAElB,KAAO,QAAiB,QAExB,KAAO,UAAmB,QAET,KAAO,YAAa,OAEpB,KAAO,SAAU;EAAA;EAGtC,mBAAmB;AACzB,WAAI,KAAK,SACAG,gGAGE,KAAK,YACPA,mGAKF;EACT;EAEQ,cAAc;AACpB+H,MAAW,MAAM,EAAE,MAAM,iBAAiB,UAAU,KAAK,SAAS,CAAC,GACnE,KAAK,QAAA;EACP;EAGU,SAAS;AAhDrB,QAAAnJ;AAiDI,WAAOoB,uBACY,KAAK,YAAY,KAAK,IAAI,CAAA,sCAG1B,KAAK,QAAA,cACNyH,UAAU,KAAK,OAAO,CAAA,4DAG9B7I,IAAA,KAAK,UAAL,OAAAA,IAAcyC,EAAO,cAAc,KAAK,MAAM,IAAI,CAAA,cAGpD,KAAK,iBAAA,CAAA;EAIf;AACF;AAxDayG,EACG,SAAS,CAAC7I,EAAU,WAAWiB,EAAM,GAGhCC,EAAA,CAAlBC,SAAAA,CAJU,GAAA0H,EAIQ,WAAA,WAAA,CAAA,GAEA3H,EAAA,CAAlBC,SANU,CAAA,GAAA0H,EAMQ,WAEA3H,QAAAA,CAAAA,GAAAA,EAAA,CAAlBC,SAAS,CAAA,GARC0H,EAQQ,WAEA3H,YAAAA,CAAAA,GAAAA,EAAA,CAAlBC,SAAS,CAAA,GAVC0H,EAUQ,WAEA3H,SAAAA,CAAAA,GAAAA,EAAA,CAAlBC,SAAAA,CAAS,GAZC0H,EAYQ,WAEiB3H,WAAAA,CAAAA,GAAAA,EAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAdhB,GAAA0H,EAcyB,WAAA,aAAA,CAAA,GAEA3H,EAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAAA,GAhBhB0H,EAgByB,WAhBzB,UAAA,CAAA,GAAAA,IAAN3H,EAAA,CADNE,cAAc,mBAAmB,CACrB,GAAAyH,CAAA;AAAA,IAAA,KAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAAE,MAAA,CAAA,GAAA,GAAA,GAAA1J,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;AAAA,ICDA2J,IAAN,cAA6BpI,WAAW;EAAxC,cAIO;AAAA,UAAA,GAAA,SAAA,GAAA,KAAO,WAAW,IAElB,KAAO,UAAmB,QAE1B,KAAO,WAAoB;EAAA;EAG7B,SAAS;AAnBrB,QAAAjB;AAoBI,UAAMmD,KAAMnD,IAAA,KAAK,aAAL,QAAAA,EAAe,SACvB,KAAK,WACLyC,EAAO,cAAc,EAAE,IAAI,KAAK,UAAU,UAAU,KAAK,QAAQ,CAAC;AAEtE,WAAOrB,OACH+B,EAAI,SACF/B,8CAEuC+B,CAAAA,UAAW,KAAK,EAAA,aAGvDrB,EAAQ,kBAAA;EAEhB;AACF;AA1BauH,EACG,SAAS,CAAChJ,EAAU,WAAWiB,EAAM,GAGhCC,IAAA,CAAlBC,SAAS,CAAA,GAJC6H,EAIQ,WAEA9H,YAAAA,CAAAA,GAAAA,IAAA,CAAlBC,SAAAA,CANU,GAAA6H,EAMQ,WAAA,WAAA,CAAA,GAEA9H,IAAA,CAAlBC,SAAS,CAAA,GARC6H,EAQQ,WAAA,YAAA,CAAA,GARRA,IAAN9H,IAAA,CADNE,cAAc,kBAAkB,CACpB,GAAA4H,CAAA;ACRb,IAAAvI,KAAA,OAAA;AAAA,IAAAmI,KAAA,OAAA;AAAA,IAAAlI,KAAA,CAAAW,GAAA9B,GAAAC,GAAAF,OAAA;AAAA,WAAAD,IAAAC,KAAA,IAAA,SAAAA,KAAAsJ,GAAArJ,GAAAC,CAAA,IAAAD,GAAAN,IAAAoC,EAAA,SAAA,GAAA5B,GAAAR,KAAA,GAAAA;AAAAA,KAAAQ,IAAA4B,EAAApC,CAAA,OAAAI,KAAAC,KAAAG,EAAAF,GAAAC,GAAAH,CAAA,IAAAI,EAAAJ,CAAA,MAAAA;AAAA,SAAAC,MAAAD,KAAAoB,GAAAlB,GAAAC,GAAAH,CAAA,GAAAA;AAAA;AAMa,IAAA4J,KAAN,cAAiCrI,WAAW;EAK1C,cAAc;AACnB,UAAA,GAJO,KAAQ,UAAU,MAOzB,KAAK,YACP;EAAA;EAGA,MAAc,WAAWsI,GAAmB;AAC1C,QAAI;AACEA,WAAA,QAAAA,EAAQ,UACV,MAAM,QAAQ,IAAIA,EAAO,IAAI,OAAMC,MAAO/G,EAAO,aAAa+G,CAAG,CAAC,CAAC;IAEvE,QAAA;AACE,cAAQ,KAAK,kDAAkDD,CAAM;IACvE;EACF;EAEA,MAAc,kBAAkB;AAC9B,QAAIxG,EAAW,MAAM,gBAAgB;AACnC,YAAMC,GAAa,qBACnBiB,GAAAA,EAAY,gBAAgB,IAAI;AAChC,YAAM,EAAE,mBAAAwF,EAAkB,IAAIzG,GAAa,OACrC0G,IAAaD,EAAkB,IAAI1F,OAAUtB,EAAO,cAAcsB,CAAM,CAAC;AAC/E,YAAM,KAAK,WAAW2F,CAAU;IAAA;AAEhCzF,QAAY,gBAAgB,IAAI;EAEpC;EAEA,MAAc,sBAAsB;AAClC,UAAMsF,IAAS9G,EAAO,mBAAA;AACtB,UAAM,KAAK,WAAW8G,CAAM;EAC9B;EAEA,MAAc,cAAc;AAC1B,QAAI;AACE,WAAK,YACP,KAAK,UAAU,OACf,MAAM,QAAQ,IAAI,CAAC,KAAK,gBAAmB,GAAA,KAAK,oBAAA,CAAqB,CAAC;IAE1E,SAAS9F,GAAAA;AACP,cAAQ,MAAMA,CAAG,GACjBe,GAAU,UAAU,qBAAqB,OAAO;IAClD;EACF;AACF;AAjDmBjD,GAAA,CAAhBoE,MAAAA,CAAM,GAFI2D,GAEM,WAFN,WAAA,CAAA,GAAAA,KAAN/H,GAAA,CADNE,cAAc,sBAAsB,CACxB,GAAA6H,EAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,CAAA,GAAA,GAAA,GAAA5J,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;ACAA,IAAAiK,KAAN,cAA8B1I,WAAW;EAEvC,cAAc;AACnB,UAYF,GAAA,KAAiB,mBAAgC,QAT/CZ,EAAU,SAAS,GACnB,KAAK,mBAAmBH,GAAU,UAAUG,EAAU,QAAQ;EAChE;EAEO,uBAAuB;AAhBhC,QAAAL;AAAAA,KAiBIA,IAAA,KAAK,qBAAL,QAAAA,EAAA,KACF,IAAA;EAAA;AAIF;AAhBa2J,KAANpI,GAAA,CADNE,cAAc,mBAAmB,CACrB,GAAAkI,EAAA;AAAA,IAAA,KAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,CAAA,GAAA,GAAA,GAAAjK,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;ACGN,IAAMkK,KAAN,cAAwC3I,WAAW;EAIhD,eAAe;AACrBqB,MAAW,KAAK,QAAQ;EAC1B;EAGU,SAAS;AACjB,UAAM,EAAE,mBAAAmH,EAAkB,IAAIzG,GAAa,OACrC6G,IAAU,CAAC,GAAGJ,GAAmB,GAAGA,CAAiB,GACrDK,IAAkBtG,EAAS,4BAA4B;AAE7D,WAAOpC,gEAGS,KAAK,YAAA,kBACHU,EAAQ,WAAA,kEAKlBA,EAAQ,WAAA,iIAMN,CAAC,GAAG,MAAMgI,CAAe,CAAC,EAAE,IAAI,CAAC7B,IAAGpB,MAAU;AAC9C,YAAM9C,IAAS8F,EAAQhD,IAAQgD,EAAQ,MAAM;AAE7C,aAAO9F,IACH3C,mCACa2C,EAAO,EAAA,cACRA,EAAO,QAAA,0BAEnBjC,EAAQ;IACd,CAAC,CAAA,iCAEsBW,EAAO,oBAAA;EAYxC;AACF;AArDamH,GACG,SAAS,CAACvJ,EAAU,WAAWiB,EAAM,GADxCsI,KAANrI,GAAA,CADNE,cAAc,8BAA8B,CAChC,GAAAmI,EAAA;AAAA,IAAA,KAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,IAAA,CAAA,GAAA,GAAA,GAAAlK,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;ACAA,IAAAqK,IAAN,cAAkC9I,WAAW;EAA7C,cAAA;AAAA,UAAA,GAAA,SAAA,GAIO,KAAO,WAAoB,QAE3B,KAAO,UAAmB,QAET,KAAO,UAAU,OAEjB,KAAO,UAAU,OAElC,KAAO,QAAQ;EAAA;EAGnB,oBAAoB;AAxB9B,QAAAjB,GAAA0F;AA6BI,UAAMsE,KACJtE,KAAA1F,IAAAE,GAAU,MAAM,mBAAhB,OAAA,SAAAF,EAAiC,uCAAA,MAAjC,OAAA0F,IACArF,EAAU,UAAU,uCAAuC;AAC7D,QAAI4J,KAAY;AAEZD,MAAO,SAAS,GAAG,IACrBC,KAAa,KAAY,MAAO,SAASD,GAAQ,EAAE,IAEnDC,KAAY,SAASD,GAAQ,EAAE,GAGjCC,MAAa;AACb,UAAMC,IAAY,MAAWD,KAAY,MACnCE,IAAa,MAAYF,KAAY;AAE3C,WAAO7I,0HAE6D6I,EAAAA,0DAGtCC,CAAAA,wBACJC,CAAAA;EAI5B;EAGU,SAAS;AACjB,UAAMjJ,IAAU,EACd,aAAa,KAAK,SAClB,aAAa,KAAK,QACpB;AAEA,WAAOE,mBACQC,SAASH,CAAO,CAAA,KACzB,KAAK,kBAAA,CAAA,+BAEM2H,UAAU,KAAK,QAAQ,CAAA,cACxBA,UAAU,KAAK,OAAO,CAAA,wEAGO,KAAK,UAAU,UAAU,SAAA,KAChE,KAAK,UAAU,wBAAwB,KAAK,KAAA;EAGpD;AACF;AAnEakB,EACG,SAAS,CAAC1J,EAAU,WAAWiB,EAAM,GAGhCC,EAAA,CAAlBC,SAAAA,CAAAA,GAJUuI,EAIQ,WAAA,YAAA,CAAA,GAEAxI,EAAA,CAAlBC,SAAAA,CAAAA,GANUuI,EAMQ,WAAA,WAAA,CAAA,GAEiBxI,EAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAAA,GARhBuI,EAQyB,WAAA,WAAA,CAAA,GAEAxI,EAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAAA,GAVhBuI,EAUyB,WAAA,WAAA,CAAA,GAEjBxI,EAAA,CAAlBC,SAAAA,CAAAA,GAZUuI,EAYQ,WAAA,SAAA,CAAA,GAZRA,IAANxI,EAAA,CADNE,cAAc,uBAAuB,CACzB,GAAAsI,CAAA;ACNA,IAAAK,IAAW,EACtB,gBAAgB;AAJlB,MAAApK,GAAA0F;AAKI,QAAM,EAAE,eAAA2E,GAAe,gBAAAC,GAAe,IAAIvH,EAAW,OAC/CwH,KAAiBvK,IAAAoK,EAAS,aAAA,MAAT,OAAApK,SAAAA,EAAyB,IAC1CwK,IAAkBhH,EAAS,SAAa6G,IAAAA,IAAgBC,IACxDT,IAAUW,uBAAiB,OAAOzG,OAAUwG,MAAmBxG,EAAO;AAE5E,UACG2B,IAAAlC,EAAS,SAAA,IACNqG,uBAAS,IAAI,CAAC,EAAE,IAAAjH,GAAI,MAAAK,GAAM,OAAAwH,EAAM,OAAO,EAAE,IAAA7H,GAAI,MAAAK,GAAM,QAAQwH,GAAO,OAAAA,EAAM,MACxEZ,uBAAS,IAAI,CAAC,EAAE,IAAAjH,GAAI,MAAAK,GAAM,OAAAwH,EAAM,OAAO,EAAE,IAAA7H,GAAI,MAAAK,GAAM,SAASwH,GAAO,OAAAA,EAAM,QAF5E,OAAA/E,IAEoF,CAAA;AAEzF,GAEA,eAAe;AACb,SAAOjD,EAAO,gBAAgB;AAChC,GAEA,kBAAkBiI,IAAa,OAAO;AArBxC,MAAA1K;AAsBI,QAAMuK,IAAiBG,MAAyB1K,IAAAoK,EAAS,aAAT,MAAA,OAAZ,SAAYpK,EAAyB,IACnE,EAAE,mBAAAyJ,GAAkB,IAAIzG,GAAa;AAG3C,SAFgByG,GAAkB,OAAO1F,OAAUwG,MAAmBxG,EAAO,EAAE;AAGjF,EACF;AAzBa,ICGA4G,IAAe,EAC1B,aAAaC,GAAkB;AAC7BnI,IAAO,mBAAmBmI,CAAI;AAChC,GAEA,wBAAwB;AAGtB,SAFgBR,EAAS,cAAc,EAExB,IACbrG,OAAU3C,oCAEK2C,EAAO,EAAA,WACXA,EAAO,IAAA,eACH,MAAM,KAAK,aAAaA,CAAM,CAAA,wBAG/C;AACF,GAEA,0BAA0B2G,IAAa,OAAO;AAG5C,SAFgBN,EAAS,kBAAkBM,CAAU,EAEtC,IACb3G,OAAU3C,gCAEC2C,EAAO,IAAA,eACHA,EAAO,EAAA,cACRA,EAAO,QAAA,eACN,MAAM,KAAK,aAAaA,CAAM,CAAA,wBAG/C;AACF,GAEA,uBAAuB;AACrB,QAAMA,IAASqG,EAAS,aAExB;AAAA,MAAKrG;AAIL,WAAO3C,gCAEI2C,EAAO,IAAA,eACHA,EAAO,EAAA,cACR8E,UAAU9E,EAAO,QAAQ,CAAA,cACzB,IAAA,eACC,MAAM,KAAK,aAAaA,CAAM,CAAA;AAG/C,EACF;ADtDa,ICsDb,KAAA;ACzDA,IAAA8G,KAAA,OAAA;AAAA,IAAA1I,KAAA,OAAA;AAAA,IAAAP,KAAA,CAAAtC,GAAAK,GAAA+B,GAAA5B,OAAA;AAAA,WAAAD,IAAAC,KAAA,IAAA,SAAAA,KAAAqC,GAAAxC,GAAA+B,CAAA,IAAA/B,GAAAD,IAAAJ,EAAA,SAAA,GAAAuB,GAAAnB,KAAA,GAAAA;AAAAA,KAAAmB,IAAAvB,EAAAI,CAAA,OAAAG,KAAAC,KAAAe,EAAAlB,GAAA+B,GAAA7B,CAAA,IAAAgB,EAAAhB,CAAA,MAAAA;AAAA,SAAAC,MAAAD,KAAAgL,GAAAlL,GAAA+B,GAAA7B,CAAA,GAAAA;AAAA;AAUa,IAAAiL,KAAN,cAAwC7J,WAAW;EAI9C,SAAS;AACjB,UAAM,EAAE,2BAAA8J,GAA2B,gBAAAC,EAAe,IAAIjI,EAAW,OAC3DkI,IAAoBF,MAA8B,SAASC,GAC3DE,KAAiBP,EAAa,sBAAA,GAC9BQ,IAAqBR,EAAa,0BAAA;AAExC,QAAIS,IAAY,CADOT,EAAa,qBAAA,GACH,GAAGO,IAAgB,GAAGC,CAAkB;AACzEC,QAAYA,EAAU,OAAO,OAAO;AAEpC,UAAMC,IAAYD,EAAU,SAAS,KAAKH;AAC1C,QAAIpB,IAAU,CAAC;AACXwB,QACFxB,IAAUuB,EAAU,MAAM,GAAG,CAAC,IAE9BvB,IAAUuB;AAEZ,UAAME,IAAY,QAAQzB,EAAQ,MAAM;AAExC,WAAOzI,kCAEO,IAAA,4CAEEqB,EAAO,aAAA,kBACLX,EAAQ,SAAA,mGAMhBA,EAAQ,WAAA,qGAKRA,EAAQ,SAAA,oKAOdwJ,IACElK,wDAGQU,EAAQ,YAAA,kGAKR+H,CAAAA,IACAwB,IACEjK,oEACA,IAAA,8BAIV,IAAA;EAER;AACF;AAhEa0J,GACG,SAAS,CAACzK,EAAU,WAAWiB,EAAM,GADxCwJ,KAANvJ,GAAA,CADNE,cAAc,8BAA8B,CAChC,GAAAqJ,EAAA;AAAA,IAAA,KAAA;ACVb,IAAAhK,KAAA,OAAA;AAAA,IAAAD,KAAA,OAAA;AAAA,IAAAvB,KAAA,CAAAoC,GAAA7B,GAAA,GAAAC,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAAe,GAAAhB,GAAA,CAAA,IAAAA,GAAAH,IAAAgC,EAAA,SAAA,GAAAnC,GAAAG,KAAA,GAAAA;AAAAA,KAAAH,IAAAmC,EAAAhC,CAAA,OAAA,KAAAI,KAAAP,EAAAM,GAAA,GAAA,CAAA,IAAAN,EAAA,CAAA,MAAA;AAAA,SAAAO,MAAA,KAAAgB,GAAAjB,GAAA,GAAA,CAAA,GAAA;AAAA;AAOa,IAAA0L,KAAN,cAA6BtK,WAAW;EAInC,SAAS;AACjB,UAAM,EAAE,mBAAAuK,GAAmB,kBAAAC,EAAiB,IAAI1I,EAAW;AAG3D,WAFgByI,KAAqBC,IAM9BrK,8HAICoK,IACEpK,gBAAeoK,CAAAA,qEAGf,IAAA,IACFA,KAAqBC,IAAmB,QAAQ,IAAA,IAChDA,IACErK,gBAAeqK,CAAAA,mEAGf,IAAA,sBAjBD;EAqBX;AACF;AA/BaF,GACG,SAAS,CAAClL,EAAU,WAAWiB,EAAM,GADxCiK,KAANhK,GAAA,CADNE,cAAc,kBAAkB,CACpB,GAAA8J,EAAA;AAAA,IAAA,KAAA;ACPb,IAAApG,KAAA,OAAA;AAAA,IAAAhD,KAAA,OAAA;AAAA,IAAA8G,KAAA,CAAA3J,GAAAK,GAAA+B,GAAA5B,OAAA;AAAA,WAAAD,IAAAC,KAAA,IAAA,SAAAA,KAAAqC,GAAAxC,GAAA+B,CAAA,IAAA/B,GAAAD,IAAAJ,EAAA,SAAA,GAAAM,GAAAF,KAAA,GAAAA;AAAAA,KAAAE,IAAAN,EAAAI,CAAA,OAAAG,KAAAC,KAAAF,EAAAD,GAAA+B,GAAA7B,CAAA,IAAAD,EAAAC,CAAA,MAAAA;AAAA,SAAAC,MAAAD,KAAAsF,GAAAxF,GAAA+B,GAAA7B,CAAA,GAAAA;AAAA;AASa,IAAA6L,KAAN,cAAuCzK,WAAW;EAI/C,WAAW;AACjBqB,MAAW,KAAK,QAAQ;EAC1B;EAGU,SAAS;AACjB,UAAM,EAAE,2BAAAyI,GAA2B,gBAAAC,EAAe,IAAIjI,EAAW,OAC3DkI,IAAoBF,MAA8B,SAASC,GAC3DE,KAAiBP,EAAa,sBAAsB,GACpDQ,IAAqBR,EAAa,0BAExC;AAAA,QAAIS,IAAY,CADOT,EAAa,qBACH,GAAA,GAAGO,IAAgB,GAAGC,CAAkB;AACzEC,QAAYA,EAAU,OAAO,OAAO;AAEpC,UAAMC,IAAYD,EAAU,SAAS,KAAKH;AAC1C,QAAIpB,IAAU,CAAC;AACXwB,QACFxB,IAAUuB,EAAU,MAAM,GAAG,CAAC,IAE9BvB,IAAUuB;AAGZ,UAAME,IAAY,QAAQzB,EAAQ,MAAM;AAExC,WAAOzI,gEAGS,KAAK,QAAA,kBACHU,EAAQ,WAAA,wBAGtBwJ,IACElK,+BAGQyI,CAAAA,IACAwB,IACEjK,oEACA,IAAA,+BAIV,IAAA;EAER;AACF;AAjDasK,GACG,SAAS,CAACrL,EAAU,WAAWiB,EAAM,GADxCoK,KAANnK,GAAA,CADNE,cAAc,6BAA6B,CAC/B,GAAAiK,EAAA;AAAA,IAAA,KAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,CAAA,GAAA,GAAA,GAAAhM,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;ACGN,IAAMiM,KAAN,cAAuB1K,WAAW;EAShC,cAAc;AACnB,UAAA,GANO,KAAQ,OAAO,OAEf,KAAQ,SAAS,OAqB1B,KAAiB,mBAAgC,QAEjD,KAAQ,kBAAoC,QAhB1C,KAAK,mBAAmBgB,GAAU,UAAU2J,OAAc;AACpDA,QAAW,OACb,KAAK,iBAAiB,IAEtB,KAAK,kBAET;IAAA,CAAC;EACH;EAEO,uBAAuB;AAlChC,QAAA5L;AAAAA,KAmCIA,IAAA,KAAK,qBAAL,QAAAA,EAAA,KAAA,IAAA;EACF;EAOA,IAAY,YAAY;AACtB,WAAOyC,EAAO,qBAAqB,MAAM,cAAc;EACzD;EAEA,IAAY,cAAc;AACxB,WAAOA,EAAO,qBAAqB,MAAM,gBAAgB;EAC3D;EAEQ,iBAAiBoJ,GAAkB;AAEzC,QADa,SAAS,cAAc,MAAM;AAExC,UAAIA,GAAS;AACX,cAAMC,IAAY,SAAS,eAAe,YAAY;AACtDA,+BAAW;MAAA;AAEX,iBAAS,KAAK,mBACZ,aACA,0GACF;EAGN;EAEQ,aAAaC,GAAqB;AACpCA,MAAM,WAAWA,EAAM,iBACzB9J,GAAU,MAAA;EAEd;EAEQ,mBAAmB;AACzB,SAAK,iBAAiB,KAAK,GAC3B,KAAK,kBAAA,GACL,KAAK,OAAO,MACZ,WAAW,YAAY;AACrB,YAAM+J,IAAYvJ,EAAO,kBAAkB,IAAI,EAAE,GAAG,CAAC,QAAQ,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,GACrFwJ,IAAQ,KACRC,IAAW;AACjB,YAAM,QAAQ,IAAI,CAChBzG,SAAQ,KAAK,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,OAAAwG,GAAO,UAAAC,EAAS,CAAC,EAAE,UAClEzG,SAAQ,KAAK,aAAauG,GAAW,EAAE,OAAAC,GAAO,UAAAC,EAAS,CAAC,EAAE,QAC5D,CAAC,GACD,KAAK,SAAS;IAChB,GAAG,CAAC;EACN;EAEA,MAAc,oBAAoB;AAChC,SAAK,iBAAiB,IAAI,GAC1B,KAAK,qBAAA;AACL,UAAMF,IAAYvJ,EAAO,kBAAsB,IAAA,EAAE,GAAG,CAAC,OAAO,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE,GACrFyJ,IAAW;AACjB,UAAM,QAAQ,IAAI,CAChBzG,SAAQ,KAAK,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,UAAAyG,EAAS,CAAC,EAAE,UAC3DzG,SAAQ,KAAK,aAAauG,GAAW,EAAE,UAAAE,EAAS,CAAC,EAAE,QACrD,CAAC,GACD,KAAK,YAAY,gBAAgB,OAAO,GACxC,KAAK,SAAS,OACd,KAAK,OAAO;EACd;EAEQ,oBAAoB;AAC1B,SAAK,kBAAkB,IAAI,mBAC3B,OAAO,iBACL,WACAH,OAAS;AA1Gf,UAAA/L;AA2GY+L,QAAM,QAAQ,WAChB9J,GAAU,MAAA,IACD8J,EAAM,QAAQ,WACjB/L,IAAA+L,EAAM,WAAN,QAAA/L,EAAyB,QAAQ,SAAS,MAAA,KAC9C,KAAK,YAAY,MAAA;IAGvB,GACA,KAAK,eACP,GACA,KAAK,YAAY,MAAA;EACnB;EAEQ,uBAAuB;AAxHjC,QAAAA;AAAAA,KAyHIA,IAAA,KAAK,oBAAL,QAAAA,EAAsB,MACtB,GAAA,KAAK,kBAAkB;EACzB;EAGU,SAAS;AACjB,UAAMkB,IAAU,EACd,eAAe,MACf,cAAc,KAAK,OACrB;AAEA,WAAOE,sHAMKC,SAASH,CAAO,CAAA,aACf,KAAK,YAAA,kFAKV,KAAK,OACHE,sJAOA,IAAA;EAIZ;AACF;AAjJauK,GACG,SAAS,CAACtL,EAAU,WAAWiB,EAAM,GAGlCC,GAAA,CAAhBoE,MAAAA,CAAM,GAJIgG,GAIM,WAAA,QAAA,CAAA,GAEApK,GAAA,CAAhBoE,MAAAA,CAAAA,GANUgG,GAMM,WAAA,UAAA,CAAA,GANNA,KAANpK,GAAA,CADNE,cAAc,WAAW,CACb,GAAAkK,EAAA;AAAA,IAAA,KAAA;ACZb,IAAAxG,KAAA,OAAA;AAAA,IAAArE,KAAA,OAAA;AAAA,IAAAnB,KAAA,CAAAC,GAAA,GAAAE,GAAAP,OAAA;AAAA,WAAAM,IAAAN,KAAA,IAAA,SAAAA,KAAAuB,GAAA,GAAAhB,CAAA,IAAA,GAAAL,IAAAG,EAAA,SAAA,GAAAF,GAAAD,KAAA,GAAAA;AAAAA,KAAAC,IAAAE,EAAAH,CAAA,OAAAI,KAAAN,KAAAG,EAAA,GAAAI,GAAAD,CAAA,IAAAH,EAAAG,CAAA,MAAAA;AAAA,SAAAN,MAAAM,KAAAsF,GAAA,GAAArF,GAAAD,CAAA,GAAAA;AAAA;AAQa,IAAAsM,IAAN,cAAmClL,WAAW;EAA9C,cAAA;AAAA,UAAA,GAAA,SAAA,GAIwB,KAAO,WAAW,OAElB,KAAO,YAAY,OAEnB,KAAO,QAAQ,OAEf,KAAO,UAAU;EAAA;EAGtC,WAAW;AACAuC,MAAS,SAAS,IAEjClB,EAAW,QAAQ,kBAAkB,IAErCA,EAAW,QAAQ,wBAAwB;EAE/C;EAEQ,YAAY;AAClBA,MAAW,QAAQ,mBAAmB;EACxC;EAEQ,QAAQ;AACdA,MAAW,QAAQ,eAAe;EACpC;EAGU,SAAS;AACjB,WAAOlB,YAED,KAAK,UAAUA,sBAAsB,IAAA,IACrC,KAAK,WACHA,6BACa,KAAK,QAAA,gBACJU,EAAQ,WAAA,4CAKtB,IAAA,IACF,KAAK,YACHV,6BACa,KAAK,SAAA,gBACJU,EAAQ,YAAA,6CAKtB,IAAA,IACF,KAAK,QACHV,6BACa,KAAK,KAAA,gBACJU,EAAQ,UAAA,yCAKtB,IAAA;EAGV;AACF;AAjEaqK,EACG,SAAS,CAAC9L,EAAU,WAAWiB,EAAM,GAGfC,GAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAAA,GAJhB2K,EAIyB,WAEA5K,YAAAA,CAAAA,GAAAA,GAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAAA,GANhB2K,EAMyB,WAEA5K,aAAAA,CAAAA,GAAAA,GAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAAA,GARhB2K,EAQyB,WAEA5K,SAAAA,CAAAA,GAAAA,GAAA,CAAnCC,SAAS,EAAE,MAAM,QAAQ,CAAC,CAAA,GAVhB2K,EAUyB,WAVzB,WAAA,CAAA,GAAAA,IAAN5K,GAAA,CADNE,cAAc,wBAAwB,CAC1B,GAAA0K,CAAA;AAAA,IAAA,KAAA;ACRb,IAAAvK,KAAA,OAAA;AAAA,IAAApC,KAAA,OAAA;AAAA,IAAAE,KAAA,CAAAqB,GAAAlB,GAAAP,GAAAM,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAAJ,GAAAK,GAAAP,CAAA,IAAAO,GAAA6B,IAAAX,EAAA,SAAA,GAAAjB,GAAA4B,KAAA,GAAAA;AAAAA,KAAA5B,IAAAiB,EAAAW,CAAA,OAAA,KAAA9B,KAAAE,EAAAD,GAAAP,GAAA,CAAA,IAAAQ,EAAA,CAAA,MAAA;AAAA,SAAAF,MAAA,KAAAgC,GAAA/B,GAAAP,GAAA,CAAA,GAAA;AAAA;AAUO,IAAM8M,KAAN,cAAsCnL,WAAW;EAI9C,UAAU;AAChBqB,MAAW,KAAK,gBAAgB;EAClC;EAGU,SAAS;AACjB,UAAM,EAAE,mBAAAmH,EAAkB,IAAIzG,GAAa,OACrCqJ,IAAgBjC,EAAS,cAAc,GACvCkC,IAAkB,CAAC,GAAG7C,GAAmB,GAAG4C,CAAa,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC;AAErF,WAAOjL,uBACY,KAAK,OAAA,4BAEhBkL,EAAgB,IAAIvI,CAAAA,OAAU;AAC9B,YAAMwI,IAAc9J,EAAO,cAAcsB,EAAM;AAC/C,UAAIwI;AACF,eAAOnL,yCAAwCmL,CAAAA;AAEjD,YAAMpJ,IAAMV,EAAO,cAAc,EAAE,IAAIsB,GAAO,GAAG,CAAC;AAElD,aAAOZ,IACH/B,yCAAwC+B,CAAAA,OACxCrB,EAAQ;IACd,CAAC,CAAA,IACC,CAAC,GAAG,MAAM,IAAIwK,EAAgB,MAAM,CAAC,EAAE,IAAI,MAAMxK,EAAQ,kBAAkB,CAAA;EAKrF;AACF;AAlCasK,GACG,SAAS,CAAC/L,EAAU,WAAWiB,EAAM,GADxC8K,KAAN7K,GAAA,CADNE,cAAc,6BAA6B,CAC/B,GAAA2K,EAAA;AAAA,IAAA,KAAA;ACVb,IAAAnD,KAAA,OAAA;AAAA,IAAA1J,KAAA,OAAA;AAAA,IAAAK,KAAA,CAAAN,GAAAO,GAAAkB,GAAAW,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAAnC,GAAAM,GAAAkB,CAAA,IAAAlB,GAAAiB,IAAAxB,EAAA,SAAA,GAAAQ,GAAAgB,KAAA,GAAAA;AAAAA,KAAAhB,IAAAR,EAAAwB,CAAA,OAAA,KAAAY,KAAA5B,EAAAD,GAAAkB,GAAA,CAAA,IAAAjB,EAAA,CAAA,MAAA;AAAA,SAAA4B,MAAA,KAAAuH,GAAApJ,GAAAkB,GAAA,CAAA,GAAA;AAAA;AASO,IAAMyL,IAAN,cAAiCvL,WAAW;EAW1C,cAAc;AACnB,UAAM,GARI,KAAO,WAAY,IAEnB,KAAO,UAAW,IAErB,KAAQ,MAAO,IAKtB,WAAW,MAAM;AACf,YAAM,EAAE,kBAAA+C,EAAiB,IAAIC,EAAY;AACzC,WAAK,MAAMD;IACb,GAAG,CAAC;EACN;EAIA,IAAY,YAA4B;AACtC,WAAOvB,EAAO,qBAAqB,MAAM,mBAAmB;EAC9D;EAGU,SAAS;AACjB,WAAOrB,qCAED,KAAK,MACHA,yBACU,KAAK,UAAU,WAAA,UACjB,KAAK,GAAA,eACAyH,UAAU,KAAK,QAAQ,CAAA,cACxBA,UAAU,KAAK,OAAO,CAAA,oBAElCzH,iCAAAA;EAGV;AACF;AAxCaoL,EACG,SAAS,CAACnM,EAAU,WAAWiB,EAAM,GAGhCC,GAAA,CAAlBC,SAAAA,CAJU,GAAAgL,EAIQ,WAAA,YAAA,CAAA,GAEAjL,GAAA,CAAlBC,SAAS,CAAA,GANCgL,EAMQ,WAAA,WAAA,CAAA,GAEFjL,GAAA,CAAhBoE,MAAM,CAAA,GARI6G,EAQM,WAAA,OAAA,CAAA,GARNA,IAANjL,GAAA,CADNE,cAAc,sBAAsB,CACxB,GAAA+K,CAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,CAAA,GAAA,GAAA,GAAA9M,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;ACHA,IAAA+M,KAAN,cAAmCxL,WAAW;EAI3C,eAAe;AACrB,WAAIuC,EAAS,UAAA,IACJpC,sEAGLoC,EAAS,SAAS,IACbpC,oEAGFA;EACT;EAGU,SAAS;AACjB,WAAOA,OACH,KAAK,aAAa,CAAA;EAGxB;AACF;AAvBaqL,GACG,SAAS,CAACpM,EAAU,SAAS,GADhCoM,KAANlL,GAAA,CADNE,cAAc,yBAAyB,CAC3B,GAAAgL,EAAA;AAAA,IAAA,KAAA;ACNb,IAAA7K,KAAA,OAAA;AAAA,IAAAqH,KAAA,OAAA;AAAA,IAAApI,KAAA,CAAAf,GAAAP,GAAAI,GAAAE,OAAA;AAAA,WAAA6B,IAAA7B,KAAA,IAAA,SAAAA,KAAAoJ,GAAA1J,GAAAI,CAAA,IAAAJ,GAAAwB,IAAAjB,EAAA,SAAA,GAAAF,GAAAmB,KAAA,GAAAA;AAAAA,KAAAnB,IAAAE,EAAAiB,CAAA,OAAAW,KAAA7B,KAAAD,EAAAL,GAAAI,GAAA+B,CAAA,IAAA9B,EAAA8B,CAAA,MAAAA;AAAA,SAAA7B,MAAA6B,KAAAE,GAAArC,GAAAI,GAAA+B,CAAA,GAAAA;AAAA;AAUa,IAAAgL,KAAN,cAAuCzL,WAAW;EAOhD,cAAc;AACnB,UAAA,GAJO,KAAO,UAAU,OAKxB,KAAK,eACP;EAAA;EAIQ,oBAAoBqD,GAAa;AACvC,UAAM,EAAE,SAAAQ,GAAS,MAAA7B,EAAK,IAAIO,EAAS,oBAAA,GAC7BW,KAAYW,uBAAS;AAE3B,QAAIX,IAAW;AACb,YAAMI,IAAOf,EAAS,gBAAgBW,IAAWG,GAAKrB,CAAI;AAC1DO,QAAS,SAASe,GAAM,OAAO;IAAA;EAEnC;EAEQ,iBAAiB;AACvB,UAAM,EAAE,kBAAAP,EAAiB,IAAIC,EAAY,OACnC0I,IAAanJ,EAAS,oBAAA;AAC5Bf,MAAO,gBAAgBkK,CAAU,GAC7B3I,KACF,KAAK,oBAAoBA,CAAgB;EAE7C;EAGU,SAAS;AACjB,UAAM,EAAE,MAAAf,GAAM,IAAAL,GAAI,UAAAC,EAAS,IAAIW,EAAS,oBAAA,GAClC,EAAE,UAAAyB,IAAU,OAAAD,EAAM,IAAIvC,EAAO,+BAAA;AAEnC,WAAOrB,gCAEK6B,CAAAA,gBACIR,EAAO,aAAA,kBACLX,EAAQ,SAAA,4EAKTc,CAAAA,cACDiG,UAAUhG,CAAQ,CAAA,YACpB,eAAeI,CAAAA,KAAAA,eACZ,KAAK,OAAA,mHAMd,sCAAsCA,CAAAA,kCAAAA,iDAGNgC,EAAAA,aAAmBD,CAAAA,eAAkB,IAAA,2BAChD,KAAK,eAAe,KAAK,IAAI,CAAA,iBAAgBlD,EAAQ,UAAA;EAMpF;AACF;AAnEa4K,GACG,SAAS,CAACrM,EAAU,WAAWiB,EAAM,GAGnCC,GAAA,CAAfoE,MAAAA,CAAM,GAJI+G,GAIK,WAJL,WAAA,CAAA,GAAAA,KAANnL,GAAA,CADNE,cAAc,6BAA6B,CAC/B,GAAAiL,EAAA;AAAA,IAAA,KAAA;ACVb,IAAA7L,KAAA,OAAA;AAAA,IAAAvB,KAAA,OAAA;AAAA,IAAAQ,KAAA,CAAAiB,GAAApB,GAAAJ,GAAAmC,OAAA;AAAA,WAAA7B,IAAA6B,KAAA,IAAA,SAAAA,KAAApC,GAAAK,GAAAJ,CAAA,IAAAI,GAAAmB,IAAAC,EAAA,SAAA,GAAArB,GAAAoB,KAAA,GAAAA;AAAAA,KAAApB,IAAAqB,EAAAD,CAAA,OAAAjB,KAAA6B,KAAAhC,EAAAC,GAAAJ,GAAAM,CAAA,IAAAH,EAAAG,CAAA,MAAAA;AAAA,SAAA6B,MAAA7B,KAAAgB,GAAAlB,GAAAJ,GAAAM,CAAA,GAAAA;AAAA;AASa,IAAA+M,KAAN,cAAmC3L,WAAW;EAK3C,UAAUqD,GAAc;AAC1BA,SACFd,EAAS,SAASc,GAAK,QAAQ;EAEnC;EAGU,SAAS;AACjB,UAAM,EAAE,MAAArB,GAAM,IAAAL,GAAI,UAAAC,GAAU,UAAAgK,GAAS,IAAIrJ,EAAS,oBAElD;AAAA,WAAOpC,gCACqB6B,CAAAA,4EAIXL,CAAAA,cACDiG,UAAUhG,CAAQ,CAAA,oCAEjB,IAAA,mHAMT,YAAYI,CAAAA,2EAA+EA,CAAAA,qBAAAA,oCAGxE,MAAM,KAAK,UAAU4J,EAAQ,CAAA,gBAAe/K,EAAQ,eAAA;EAKjF;AACF;AAtCa8K,GACG,SAAS,CAACvM,EAAU,WAAWiB,EAAM,GADxCsL,KAANrL,GAAA,CADNE,cAAc,yBAAyB,CAC3B,GAAAmL,EAAA;AAAA,IAAA,KAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,CAAA,GAAA,GAAA,GAAAlN,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;ACCN,IAAMoN,KAAN,cAAsC7L,WAAW;EAO/C,cAAc;AACnB,UAAM,GAJC,KAAO,UAAU,OAKxB,KAAK,cAAc;EACrB;EAIQ,oBAAoBqD,GAAayI,IAAoB,OAAO;AAClE,UAAM,EAAE,QAAA7I,GAAQ,MAAAjB,GAAK,IAAIO,EAAS,oBAAA,GAC5BW,IAAYD,uBAAQ,QACpBE,IAAeF,uBAAQ;AAE7B,QAAIC,KAAa,CAAC4I,GAAmB;AACnC,YAAMxI,IAAOf,EAAS,gBAAgBW,GAAWG,GAAKrB,EAAI;AAC1DO,QAAS,SAASe,GAAM,OAAO;IAAA,WACtBH,GAAc;AACvB,YAAMG,IAAOf,EAAS,mBAAmBY,GAAcE,GAAKrB,EAAI;AAChEO,QAAS,SAASe,GAAM,OAAO;IAAA;EAEnC;EAEQ,cAAcwI,IAAoB,OAAO;AAC/C,UAAM,EAAE,kBAAA/I,EAAiB,IAAIC,EAAY,OACnC0I,IAAanJ,EAAS,oBAAA;AAC5Bf,MAAO,gBAAgBkK,CAAU,GAC7B3I,KACF,KAAK,oBAAoBA,GAAkB+I,CAAiB;EAEhE;EAEQ,eAAeC,GAAsB;AACvCA,SACFxJ,EAAS,SAASwJ,GAAa,QAAQ;EAE3C;EAGU,SAAS;AACjB,UAAM,EAAE,MAAA/J,GAAM,IAAAL,GAAI,UAAAC,GAAU,KAAAoK,IAAK,QAAA/I,EAAO,IAAIV,EAAS,oBAAoB,GACnE,EAAE,OAAAwB,EAAM,IAAIvC,EAAO,+BAAA,GACnBuK,IAAcC,MAAAA,gBAAAA,GAAK,KACnB7I,IAAeF,uBAAQ;AAE7B,WAAO9C,gCACqB6B,CAAAA,4EAIXL,CAAAA,cACDiG,UAAUhG,CAAQ,CAAA,+CAEjB,KAAK,OAAA,mHAKemC,CAAAA,eAAkB,IAAA,2BAC1B,MAAM,KAAK,cAAc,KAAK,CAAA,iBAAgBlD,EAAQ,UAAA,gDAK7EsC,IACEhD,uGAGgC,MAAM,KAAK,cAAc,IAAI,CAAA,gDAK7D,IAAA,6FAK2BwB,CAAAA,cAAciG,UAAUhG,CAAQ,CAAA,kCACjD,OAAOI,CAAAA,EAAAA,4CAGNnB,EAAQ,gBAAA,eACV,MAAM,KAAK,eAAekL,CAAW,CAAA;EAOxD;AACF;AAhGaF,GACG,SAAS,CAACzM,EAAU,WAAWiB,EAAM,GAGnCC,GAAA,CAAfoE,MAJU,CAAA,GAAAmH,GAIK,WAJL,WAAA,CAAA,GAAAA,KAANvL,GAAA,CADNE,cAAc,4BAA4B,CAC9B,GAAAqL,EAAA;AAAA,IAAA,KAAA;ACVb,IAAAvN,KAAA,OAAA;AAAA,IAAAD,KAAA,OAAA;AAAA,IAAAyB,KAAA,CAAAjB,GAAAD,GAAAiB,GAAAY,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAApC,GAAAO,GAAAiB,CAAA,IAAAjB,GAAAD,IAAAE,EAAA,SAAA,GAAAJ,GAAAE,KAAA,GAAAA;AAAAA,KAAAF,IAAAI,EAAAF,CAAA,OAAA,KAAA8B,KAAAhC,EAAAG,GAAAiB,GAAA,CAAA,IAAApB,EAAA,CAAA,MAAA;AAAA,SAAAgC,MAAA,KAAAnC,GAAAM,GAAAiB,GAAA,CAAA,GAAA;AAAA;AAAA,IAUaoM,KAAN,cAAwCjM,WAAW;EAI9C,SAAS;AACjB,UAAM,EAAE,MAAAgC,GAAM,IAAAL,GAAI,UAAAC,EAAS,IAAIW,EAAS,oBAClC,GAAA,EAAE,WAAAuB,IAAW,OAAAC,EAAM,IAAIvC,EAAO,+BAEpC;AAAA,WAAOrB,gCAEK6B,CAAAA,gBACIR,EAAO,aAAA,kBACLX,EAAQ,SAAA,2EAIWc,CAAAA,cAAciG,UAAUhG,CAAQ,CAAA,kHAK7D,wDAAwDI,CAAAA,MAAAA,kDAGvB8B,EAAAA,aAAoBC,CAAAA;EAG/D;AACF;AA5BakI,GACG,SAAS,CAAC7M,EAAU,WAAWiB,EAAM,GADxC4L,KAAN3L,GAAA,CADNE,cAAc,+BAA+B,CACjC,GAAAyL,EAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,OAAA;AAAA,IAAA,KAAA,CAAA,GAAA,GAAA,GAAAxN,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,EAAA,SAAA,GAAA,GAAA,KAAA,GAAA;AAAA,KAAA,IAAA,EAAA,CAAA,OAAA,KAAAA,KAAA,EAAA,GAAA,GAAA,CAAA,IAAA,EAAA,CAAA,MAAA;AAAA,SAAAA,MAAA,KAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AAAA;ACHA,IAAAyN,MAAN,cAA4BlM,WAAW;EAIlC,SAAS;AACjB,WAAOG,0DAGSqB,EAAO,aAAA,kBACLX,EAAQ,SAAA;EAO5B;AACF;AAjBaqL,IACG,SAAS,CAAC9M,EAAU,SAAS,GADhC8M,MAAN5L,GAAA,CADNE,cAAc,iBAAiB,CACnB,GAAA0L,GAAA;AAAA,IAAA,KAAA;ACNb,IAAAC,KAAA,OAAA;AAAA,IAAAC,KAAA,OAAA;AAAA,IAAA1E,KAAA,CAAAxG,GAAA8G,GAAApJ,GAAAkB,OAAA;AAAA,WAAA,IAAAA,KAAA,IAAA,SAAAA,KAAAsM,GAAApE,GAAApJ,CAAA,IAAAoJ,GAAA3J,IAAA6C,EAAA,SAAA,GAAAtB,GAAAvB,KAAA,GAAAA;AAAAA,KAAAuB,IAAAsB,EAAA7C,CAAA,OAAA,KAAAyB,KAAAF,EAAAoI,GAAApJ,GAAA,CAAA,IAAAgB,EAAA,CAAA,MAAA;AAAA,SAAAE,MAAA,KAAAqM,GAAAnE,GAAApJ,GAAA,CAAA,GAAA;AAAA;AASA,IAAMyN,KAAe;AAGd,IAAMC,IAAN,cAAoCtM,WAAW;EAA/C,cAAA;AAAA,UAAA,GAAA,SAAA,GAII,KAAQ,UAAU,CAAC+B,GAAa,MAAM,QAAQ,SAAS,QAEvD,KAAQ,aAAa,CAACA,GAAa,MAAM,QAAQ,SAAS,QAE1D,KAAQ,SAAS,IAEjB,KAAQ,aAAa,OAgB9B,KAAQ,uBAAyD,QAiEjE,KAAiB,iBAAiBP,EAAO,SAAUiC,OAAkB;AAC/DA,QAAM,UAAU,KAClB,KAAK,aAAa,MAClB,KAAK,aAAa,OAClB,KAAK,SAASA,GACd1B,GAAa,YAAY,GACzB,KAAK,aAAA,KACI,KAAK,WACd,KAAK,SAAS,IACd,KAAK,aAAa,KAAK,WAAW,GAClCA,GAAa,YAEjB;IAAA,CAAC;EA1FM;EAAA,eAAe;AACpB,SAAK,yBACP;EAAA;EAEO,uBAAuB;AA9BhC,QAAAhD;AAAAA,KA+BIA,IAAA,KAAK,yBAAL,QAAAA,EAA2B,WAC7B;EAAA;EAGA,IAAY,gBAAgB;AAC1B,WAAOyC,EAAO,qBAAqB,MAAM,wBAAwB;EACnE;EAIQ,2BAA2B;AACjC,SAAK,uBAAuB,IAAI,qBAAqB,CAAC,CAAC+K,CAAO,MAAM;AAC9DA,QAAQ,kBAAkB,EAAE,KAAK,UAAU,KAAK,eAClD,KAAK,aAAA;IAET,CAAC,GACD,KAAK,qBAAqB,QAAQ,KAAK,aAAa;EACtD;EAEQ,aAAa;AACnB,UAAM,EAAE,SAAA3D,GAAS,QAAA4D,EAAO,IAAIzK,GAAa,OACnC,EAAE,UAAA0K,GAAU,OAAAC,GAAM,IAAI,KAAK,SAASF,IAAS5D;AAEnD,WAAO8D,MAASL,MAAgBI,EAAS,UAAUC;EACrD;EAEA,MAAc,eAAe;AAzD/B,QAAA3N;AA0DI,UAAM,EAAE,SAAA6J,GAAS,QAAA4D,EAAO,IAAIzK,GAAa,OACnC,EAAE,UAAA0K,IAAU,OAAAC,GAAO,MAAAC,EAAK,IAAI,KAAK,SAASH,IAAS5D;AAEzD,QACE,CAAC,KAAK,eACL,KAAK,cAAe8D,IAAQL,MAAgBI,GAAS,SAASC;AAE/D,UAAI;AACF,aAAK,UAAU;AACf,cAAME,KAAS7N,IAAAiE,EAAY,MAAM,WAAlB,OAAAjE,SAAAA,EAA0B,KAAK,GAAA,GACxC,EAAE,UAAU8N,EAAY,IAAI,MAAM9K,GAAa,WAAW,EAC9D,MAAM,KAAK,aAAa,IAAI4K,IAAO,GACnC,SAASN,IACT,QAAQ,KAAK,QACb,SAAS,GACT,QAAAO,EACF,CAAC,GACKE,IAAiBD,EAAY,IAAI/J,OAAUtB,EAAO,cAAcsB,CAAM,CAAC;AAC7E,cAAM,QAAQ,IAAI,CAChB,GAAGgK,EAAe,IAAI,OAAMvE,MAAO/G,EAAO,aAAa+G,CAAG,CAAC,GAC3DhG,EAAS,KAAK,GAAG,CACnB,CAAC,GACD,KAAK,aAAa,KAAK,WAAW;MACpC,SAASC,GAAAA;AACP,gBAAQ,MAAMA,CAAG,GACjBe,GAAU,UAAU/B,EAAO,gBAAgBgB,CAAG,GAAG,OAAO;MAC1D,UAAA;AACE,aAAK,UAAU,OACf,KAAK,aAAa;MACpB;EAEJ;EAEQ,UAAUuK,GAAkB;AAC9BxK,MAAS,UAAA,IACXf,EAAO,oBAAoBuL,CAAO,IAElCvL,EAAO,mBAAmBuL,CAAO;EAErC;EAEQ,eAAejC,GAAc;AACnC,UAAM,EAAE,OAAArH,EAAM,IAAIqH,EAAM;AACxB,SAAK,eAAerH,CAAK;EAC3B;EAiBU,SAAS;AACjB,UAAM,EAAE,SAAAmF,GAAS,QAAA4D,EAAO,IAAIzK,GAAa,OACnC,EAAE,UAAA0K,EAAS,IAAI,KAAK,SAASD,IAAS5D,GACtCoE,KAAY,KAAK,WAAW,CAACP,EAAS,QACtCQ,IAAW,KAAK,OAAO,UAAU;AACvC,QAAI7B,IAAgB1B,EAAa,sBAAsB,GACnDlB,IAAoBkB,EAAa,0BAA0B,IAAI;AAG/DuD,UACF7B,IAAgBA,EAAc,OAAO,CAAC,EAAE,QAAA8B,EAAO,MAC7C1L,EAAO,iBAAiB0L,EAAO,CAAC,GAAa,KAAK,MAAM,CAC1D,GACA1E,IAAoBA,EAAkB,OAAO,CAAC,EAAE,QAAA0E,EAAO,MACrD1L,EAAO,iBAAiB0L,EAAO,CAAC,GAAa,KAAK,MAAM,CAC1D;AAGF,UAAMC,IAAU,CAAC,KAAK,WAAW,CAACV,EAAS,UAAU,CAACjE,EAAkB,QAClEvI,IAAU,EACd,eAAe+M,IACf,mBAAmB,KAAK,cAAc,CAAC,KAAK,SAC5C,aAAaG,EACf;AAEA,WAAOhN,sDAE2B,KAAK,eAAe,KAAK,IAAI,CAAA,qEAGlCC,SAASH,CAAO,CAAA,2BAErC+M,KAAY,OAAO5B,CAAAA,IAAiB4B,KAAY,OAAOxE,CAAAA,IACvDwE,KACE,OACAP,EAAS,IACPM,OAAW5M,OACP4M,IACE5M,mCAEc4M,EAAQ,QAAA,WACXA,EAAQ,IAAA,eACJA,EAAQ,EAAA,eACR,MAAM,KAAK,UAAUA,CAAO,CAAA,2BAI3C,IAAA,EAER,CAAA,4CAGFI,IACEhN,mFACA,IAAA,IACF,CAACgN,KAAW,KAAK,UAAUhN,oCAAoC,IAAA;EAIzE;AACF;AAtKamM,EACG,SAAS,CAAClN,EAAU,WAAWiB,EAAM,GAGlCC,GAAA,CAAhBoE,MAAM,CAAA,GAJI4H,EAIM,WAAA,WAAA,CAAA,GAEAhM,GAAA,CAAhBoE,MAAM,CAAA,GANI4H,EAMM,WAEAhM,cAAAA,CAAAA,GAAAA,GAAA,CAAhBoE,MAAAA,CARU,GAAA4H,EAQM,WAEAhM,UAAAA,CAAAA,GAAAA,GAAA,CAAhBoE,MAAM,CAAA,GAVI4H,EAUM,WAAA,cAAA,CAAA,GAVNA,IAANhM,GAAA,CADNE,cAAc,0BAA0B,CAC5B,GAAA8L,CAAA;AAAA,IAAA,KAAA;ACbb,IAAApL,KAAA,OAAA;AAAA,IAAA8G,KAAA,OAAA;AAAA,IAAApI,KAAA,CAAAtB,GAAAO,GAAAH,GAAAE,OAAA;AAAA,WAAA6B,IAAA7B,KAAA,IAAA,SAAAA,KAAAoJ,GAAAnJ,GAAAH,CAAA,IAAAG,GAAAF,IAAAL,EAAA,SAAA,GAAA,GAAAK,KAAA,GAAAA;AAAAA,KAAA,IAAAL,EAAAK,CAAA,OAAA8B,KAAA7B,KAAA,EAAAC,GAAAH,GAAA+B,CAAA,IAAA,EAAAA,CAAA,MAAAA;AAAA,SAAA7B,MAAA6B,KAAAS,GAAArC,GAAAH,GAAA+B,CAAA,GAAAA;AAAA;AAAA,IAUa2M,KAAN,cAAmCpN,WAAW;EAO5C,cAAc;AACnB,UAAM,GAJC,KAAO,UAAU,OAKxB,KAAK,cAAA;EACP;EAGQ,oBAAoBqD,GAAa;AACvC,UAAM,EAAE,SAAAQ,GAAS,MAAA7B,EAAK,IAAIO,EAAS,oBAC7BY,GAAAA,KAAeU,uBAAS;AAE9B,QAAIV,IAAc;AAChB,YAAMG,IAAOf,EAAS,mBAAmBY,IAAcE,GAAKrB,CAAI;AAChEO,QAAS,SAASe,GAAM,QAAQ;IAAA;EAEpC;EAEQ,gBAAgB;AACtB,UAAM,EAAE,kBAAAP,EAAiB,IAAIC,EAAY,OACnC0I,IAAanJ,EAAS,oBAAA;AAC5Bf,MAAO,gBAAgBkK,CAAU,GAC7B3I,KACF,KAAK,oBAAoBA,CAAgB;EAE7C;EAGU,SAAS;AACjB,UAAM,EAAE,MAAAf,GAAM,IAAAL,GAAI,UAAAC,EAAS,IAAIW,EAAS,oBAAA,GAClC,EAAE,UAAAyB,IAAU,WAAAF,EAAU,IAAItC,EAAO,+BAAA,GACjC6L,IAAmB9K,EAAS,SAAA;AAElC,WAAOpC,gCAEK6B,CAAAA,gBACIR,EAAO,aAAA,kBACLX,EAAQ,SAAA,4EAKTc,CAAAA,cACDiG,UAAUhG,CAAQ,CAAA,YACpB,eAAeI,CAAAA,KAAAA,eACZ,KAAK,OAAA,mHAMd,GAAGA,CAAAA,kFAAAA,iDAIOgC,EAAAA,iBACCqJ,IAAmB,QAAQvJ,CAAAA,eAC7B,IAAA,2BAEY,KAAK,cAAc,KAAK,IAAI,CAAA,iBAAgBjD,EAAQ,UAAA;EAMnF;AACF;AAvEauM,GACG,SAAS,CAAChO,EAAU,WAAWiB,EAAM,GAGnCC,GAAA,CAAfoE,MAAM,CAAA,GAJI0I,GAIK,WAJL,WAAA,CAAA,GAAAA,KAAN9M,GAAA,CADNE,cAAc,yBAAyB,CAC3B,GAAA4M,EAAA;", "names": ["v", "style", "NODE_MODE", "global", "warning", "_a", "v", "p", "_b", "c", "has<PERSON><PERSON>ed", "DEV_MODE", "NODE_MODE", "global", "debugLogEvent", "issueWarning", "_a", "warning", "_b", "_c", "trustedTypes", "html", "tag", "strings", "data", "_d", "v", "emptyStringForBooleanAttribute", "polyfillSupport", "DEV_MODE", "global", "_d", "issueWarning", "_a", "debugLogEvent", "_b", "DEV_MODE", "issueWarning", "_a", "warning", "_b", "polyfillSupport", "finalized", "_c", "DEV_MODE", "issueWarning", "NODE_MODE", "global", "_a", "c", "_a", "_b", "v", "wrap", "v", "wrap", "progress", "v", "steps", "progress", "_a", "progress", "p", "v", "a", "transforms", "a", "c", "d", "_a", "data", "_a", "_b", "_a", "state", "animate", "defaults", "defaults", "defaults", "to", "state", "state", "v", "state", "to", "spring", "glide", "inView", "inView", "animate", "s", "n", "g", "b", "a", "t", "r", "e", "l", "themeModeVariables", "_a", "themeMode", "ThemeCtrl", "themeModeColors", "themeVariablesPresets", "ThemeUtil", "key", "root", "themeVariables", "variables", "__spreadValues", "val", "css", "c", "m", "i", "WcmButton", "LitElement", "classes", "textColor", "html", "classMap", "styles", "__decorateClass", "property", "customElement", "o", "WcmButtonBig", "p", "WcmInfoFooter", "SvgUtil", "svg", "WcmModalBackcard", "ModalCtrl", "WcmModalContent", "f", "WcmModalFooter", "WcmModalHeader", "RouterCtrl", "backBtn", "content", "<PERSON><PERSON><PERSON><PERSON>", "selector", "el", "id", "image_id", "walletImages", "ConfigCtrl", "ExplorerCtrl", "name", "short", "src", "imagePromise", "resolve", "reject", "image", "CoreUtil", "err", "func", "timeout", "timer", "args", "next", "wallet", "walletConnectUri", "OptionsCtrl", "mobile", "nativeUrl", "universalUrl", "onRedirect", "uri", "href", "ToastCtrl", "walletUrls", "value", "strLen", "str1", "str2", "desktop", "isDesktop", "isWeb", "isMobile", "isMobileDevice", "u", "w", "WcmModalRouter", "routerState", "conetnt", "newHeight", "animate", "_b", "state", "WcmModalToast", "newState", "message", "variant", "CONNECTING_ERROR_MARGIN", "CIRCLE_SIZE_MODIFIER", "QRCODE_MATRIX_MARGIN", "isAdjecentDots", "cy", "otherCy", "cellSize", "getMatrix", "errorCorrectionLevel", "arr", "QRCodeUtil", "sqrt", "rows", "index", "QrCodeUtil", "size", "logoSize", "dotColor", "edgeColor", "dots", "matrix", "qrList", "x", "y", "x1", "y1", "borderRadius", "dotSize", "clearArenaSize", "matrixMiddleStart", "matrixMiddleEnd", "circles", "row", "_", "j", "cx", "circlesToConnect", "cys", "newCys", "groups", "group", "item", "y2", "h", "WcmQrCode", "ifDefined", "WcmSearchInput", "WcmSpinner", "WcmText", "d", "WcmWalletButton", "EventsCtrl", "se", "WcmWalletImage", "WcmExplorerContext", "images", "url", "recomendedWallets", "walletImgs", "WcmThemeContext", "WcmAndroidWalletSelection", "wallets", "recomendedCount", "WcmConnectorWaiting", "radius", "numRadius", "dashArray", "dashOffset", "DataUtil", "mobileWallets", "desktopWallets", "recentWalletId", "platformWallets", "links", "skipR<PERSON>ent", "TemplateUtil", "data", "v", "WcmDesktopWalletSelection", "explorerExcludedWalletIds", "enableExplorer", "isExplorerWallets", "manualTemplate", "recomendedTemplate", "templates", "isViewAll", "isWallets", "WcmLegalNotice", "termsOfServiceUrl", "privacyPolicyUrl", "WcmMobileWalletSelection", "WcmModal", "modalState", "enabled", "wcmStyles", "event", "animation", "delay", "duration", "WcmPlatformSelection", "WcmViewAllWalletsButton", "manualWallets", "reversedWallets", "explorerImg", "WcmWalletConnectQr", "WcmConnectWalletView", "WcmDesktopConnectingView", "routerData", "WcmInstallWalletView", "homepage", "WcmMobileConnectingView", "forceUniversalUrl", "downloadUrl", "app", "WcmMobileQrConnectingView", "WcmQrcodeView", "C", "I", "PAGE_ENTRIES", "WcmWalletExplorerView", "element", "search", "listings", "total", "page", "chains", "newListings", "explorerImages", "listing", "isLoading", "isSearch", "values", "isEmpty", "WcmWebConnectingView", "isMobilePlatform"]}