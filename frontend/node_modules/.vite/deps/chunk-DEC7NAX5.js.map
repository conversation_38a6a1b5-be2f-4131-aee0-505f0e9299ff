{"version": 3, "sources": ["../../cross-fetch/dist/browser-ponyfill.js", "../../@walletconnect/safe-json/src/index.ts", "../../@walletconnect/jsonrpc-utils/src/constants.ts", "../../@walletconnect/jsonrpc-utils/src/error.ts", "../../@walletconnect/environment/node_modules/tslib/tslib.es6.js", "../../@walletconnect/environment/src/crypto.ts", "../../@walletconnect/environment/src/env.ts", "../../@walletconnect/environment/src/index.ts", "../../@walletconnect/jsonrpc-utils/src/env.ts", "../../@walletconnect/jsonrpc-utils/src/format.ts", "../../@walletconnect/jsonrpc-utils/src/routing.ts", "../../@walletconnect/jsonrpc-types/src/misc.ts", "../../@walletconnect/jsonrpc-types/src/provider.ts", "../../@walletconnect/jsonrpc-utils/src/types.ts", "../../@walletconnect/jsonrpc-utils/src/url.ts", "../../@walletconnect/jsonrpc-utils/src/validators.ts", "../../@walletconnect/jsonrpc-utils/src/index.ts"], "sourcesContent": ["// Save global object in a variable\nvar __global__ =\n(typeof globalThis !== 'undefined' && globalThis) ||\n(typeof self !== 'undefined' && self) ||\n(typeof global !== 'undefined' && global);\n// Create an object that extends from __global__ without the fetch function\nvar __globalThis__ = (function () {\nfunction F() {\nthis.fetch = false;\nthis.DOMException = __global__.DOMException\n}\nF.prototype = __global__; // Needed for feature detection on whatwg-fetch's code\nreturn new F();\n})();\n// Wraps whatwg-fetch with a function scope to hijack the global object\n// \"globalThis\" that's going to be patched\n(function(globalThis) {\n\nvar irrelevant = (function (exports) {\n\n  /* eslint-disable no-prototype-builtins */\n  var g =\n    (typeof globalThis !== 'undefined' && globalThis) ||\n    (typeof self !== 'undefined' && self) ||\n    // eslint-disable-next-line no-undef\n    (typeof global !== 'undefined' && global) ||\n    {};\n\n  var support = {\n    searchParams: 'URLSearchParams' in g,\n    iterable: 'Symbol' in g && 'iterator' in Symbol,\n    blob:\n      'FileReader' in g &&\n      'Blob' in g &&\n      (function() {\n        try {\n          new Blob();\n          return true\n        } catch (e) {\n          return false\n        }\n      })(),\n    formData: 'FormData' in g,\n    arrayBuffer: 'ArrayBuffer' in g\n  };\n\n  function isDataView(obj) {\n    return obj && DataView.prototype.isPrototypeOf(obj)\n  }\n\n  if (support.arrayBuffer) {\n    var viewClasses = [\n      '[object Int8Array]',\n      '[object Uint8Array]',\n      '[object Uint8ClampedArray]',\n      '[object Int16Array]',\n      '[object Uint16Array]',\n      '[object Int32Array]',\n      '[object Uint32Array]',\n      '[object Float32Array]',\n      '[object Float64Array]'\n    ];\n\n    var isArrayBufferView =\n      ArrayBuffer.isView ||\n      function(obj) {\n        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n      };\n  }\n\n  function normalizeName(name) {\n    if (typeof name !== 'string') {\n      name = String(name);\n    }\n    if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n      throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n    }\n    return name.toLowerCase()\n  }\n\n  function normalizeValue(value) {\n    if (typeof value !== 'string') {\n      value = String(value);\n    }\n    return value\n  }\n\n  // Build a destructive iterator for the value list\n  function iteratorFor(items) {\n    var iterator = {\n      next: function() {\n        var value = items.shift();\n        return {done: value === undefined, value: value}\n      }\n    };\n\n    if (support.iterable) {\n      iterator[Symbol.iterator] = function() {\n        return iterator\n      };\n    }\n\n    return iterator\n  }\n\n  function Headers(headers) {\n    this.map = {};\n\n    if (headers instanceof Headers) {\n      headers.forEach(function(value, name) {\n        this.append(name, value);\n      }, this);\n    } else if (Array.isArray(headers)) {\n      headers.forEach(function(header) {\n        if (header.length != 2) {\n          throw new TypeError('Headers constructor: expected name/value pair to be length 2, found' + header.length)\n        }\n        this.append(header[0], header[1]);\n      }, this);\n    } else if (headers) {\n      Object.getOwnPropertyNames(headers).forEach(function(name) {\n        this.append(name, headers[name]);\n      }, this);\n    }\n  }\n\n  Headers.prototype.append = function(name, value) {\n    name = normalizeName(name);\n    value = normalizeValue(value);\n    var oldValue = this.map[name];\n    this.map[name] = oldValue ? oldValue + ', ' + value : value;\n  };\n\n  Headers.prototype['delete'] = function(name) {\n    delete this.map[normalizeName(name)];\n  };\n\n  Headers.prototype.get = function(name) {\n    name = normalizeName(name);\n    return this.has(name) ? this.map[name] : null\n  };\n\n  Headers.prototype.has = function(name) {\n    return this.map.hasOwnProperty(normalizeName(name))\n  };\n\n  Headers.prototype.set = function(name, value) {\n    this.map[normalizeName(name)] = normalizeValue(value);\n  };\n\n  Headers.prototype.forEach = function(callback, thisArg) {\n    for (var name in this.map) {\n      if (this.map.hasOwnProperty(name)) {\n        callback.call(thisArg, this.map[name], name, this);\n      }\n    }\n  };\n\n  Headers.prototype.keys = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push(name);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.values = function() {\n    var items = [];\n    this.forEach(function(value) {\n      items.push(value);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.entries = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push([name, value]);\n    });\n    return iteratorFor(items)\n  };\n\n  if (support.iterable) {\n    Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n  }\n\n  function consumed(body) {\n    if (body._noBody) return\n    if (body.bodyUsed) {\n      return Promise.reject(new TypeError('Already read'))\n    }\n    body.bodyUsed = true;\n  }\n\n  function fileReaderReady(reader) {\n    return new Promise(function(resolve, reject) {\n      reader.onload = function() {\n        resolve(reader.result);\n      };\n      reader.onerror = function() {\n        reject(reader.error);\n      };\n    })\n  }\n\n  function readBlobAsArrayBuffer(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsArrayBuffer(blob);\n    return promise\n  }\n\n  function readBlobAsText(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    var match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type);\n    var encoding = match ? match[1] : 'utf-8';\n    reader.readAsText(blob, encoding);\n    return promise\n  }\n\n  function readArrayBufferAsText(buf) {\n    var view = new Uint8Array(buf);\n    var chars = new Array(view.length);\n\n    for (var i = 0; i < view.length; i++) {\n      chars[i] = String.fromCharCode(view[i]);\n    }\n    return chars.join('')\n  }\n\n  function bufferClone(buf) {\n    if (buf.slice) {\n      return buf.slice(0)\n    } else {\n      var view = new Uint8Array(buf.byteLength);\n      view.set(new Uint8Array(buf));\n      return view.buffer\n    }\n  }\n\n  function Body() {\n    this.bodyUsed = false;\n\n    this._initBody = function(body) {\n      /*\n        fetch-mock wraps the Response object in an ES6 Proxy to\n        provide useful test harness features such as flush. However, on\n        ES5 browsers without fetch or Proxy support pollyfills must be used;\n        the proxy-pollyfill is unable to proxy an attribute unless it exists\n        on the object before the Proxy is created. This change ensures\n        Response.bodyUsed exists on the instance, while maintaining the\n        semantic of setting Request.bodyUsed in the constructor before\n        _initBody is called.\n      */\n      // eslint-disable-next-line no-self-assign\n      this.bodyUsed = this.bodyUsed;\n      this._bodyInit = body;\n      if (!body) {\n        this._noBody = true;\n        this._bodyText = '';\n      } else if (typeof body === 'string') {\n        this._bodyText = body;\n      } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n        this._bodyBlob = body;\n      } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n        this._bodyFormData = body;\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this._bodyText = body.toString();\n      } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n        this._bodyArrayBuffer = bufferClone(body.buffer);\n        // IE 10-11 can't handle a DataView body.\n        this._bodyInit = new Blob([this._bodyArrayBuffer]);\n      } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n        this._bodyArrayBuffer = bufferClone(body);\n      } else {\n        this._bodyText = body = Object.prototype.toString.call(body);\n      }\n\n      if (!this.headers.get('content-type')) {\n        if (typeof body === 'string') {\n          this.headers.set('content-type', 'text/plain;charset=UTF-8');\n        } else if (this._bodyBlob && this._bodyBlob.type) {\n          this.headers.set('content-type', this._bodyBlob.type);\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        }\n      }\n    };\n\n    if (support.blob) {\n      this.blob = function() {\n        var rejected = consumed(this);\n        if (rejected) {\n          return rejected\n        }\n\n        if (this._bodyBlob) {\n          return Promise.resolve(this._bodyBlob)\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as blob')\n        } else {\n          return Promise.resolve(new Blob([this._bodyText]))\n        }\n      };\n    }\n\n    this.arrayBuffer = function() {\n      if (this._bodyArrayBuffer) {\n        var isConsumed = consumed(this);\n        if (isConsumed) {\n          return isConsumed\n        } else if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n          return Promise.resolve(\n            this._bodyArrayBuffer.buffer.slice(\n              this._bodyArrayBuffer.byteOffset,\n              this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n            )\n          )\n        } else {\n          return Promise.resolve(this._bodyArrayBuffer)\n        }\n      } else if (support.blob) {\n        return this.blob().then(readBlobAsArrayBuffer)\n      } else {\n        throw new Error('could not read as ArrayBuffer')\n      }\n    };\n\n    this.text = function() {\n      var rejected = consumed(this);\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return readBlobAsText(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as text')\n      } else {\n        return Promise.resolve(this._bodyText)\n      }\n    };\n\n    if (support.formData) {\n      this.formData = function() {\n        return this.text().then(decode)\n      };\n    }\n\n    this.json = function() {\n      return this.text().then(JSON.parse)\n    };\n\n    return this\n  }\n\n  // HTTP methods whose capitalization should be normalized\n  var methods = ['CONNECT', 'DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT', 'TRACE'];\n\n  function normalizeMethod(method) {\n    var upcased = method.toUpperCase();\n    return methods.indexOf(upcased) > -1 ? upcased : method\n  }\n\n  function Request(input, options) {\n    if (!(this instanceof Request)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n\n    options = options || {};\n    var body = options.body;\n\n    if (input instanceof Request) {\n      if (input.bodyUsed) {\n        throw new TypeError('Already read')\n      }\n      this.url = input.url;\n      this.credentials = input.credentials;\n      if (!options.headers) {\n        this.headers = new Headers(input.headers);\n      }\n      this.method = input.method;\n      this.mode = input.mode;\n      this.signal = input.signal;\n      if (!body && input._bodyInit != null) {\n        body = input._bodyInit;\n        input.bodyUsed = true;\n      }\n    } else {\n      this.url = String(input);\n    }\n\n    this.credentials = options.credentials || this.credentials || 'same-origin';\n    if (options.headers || !this.headers) {\n      this.headers = new Headers(options.headers);\n    }\n    this.method = normalizeMethod(options.method || this.method || 'GET');\n    this.mode = options.mode || this.mode || null;\n    this.signal = options.signal || this.signal || (function () {\n      if ('AbortController' in g) {\n        var ctrl = new AbortController();\n        return ctrl.signal;\n      }\n    }());\n    this.referrer = null;\n\n    if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n      throw new TypeError('Body not allowed for GET or HEAD requests')\n    }\n    this._initBody(body);\n\n    if (this.method === 'GET' || this.method === 'HEAD') {\n      if (options.cache === 'no-store' || options.cache === 'no-cache') {\n        // Search for a '_' parameter in the query string\n        var reParamSearch = /([?&])_=[^&]*/;\n        if (reParamSearch.test(this.url)) {\n          // If it already exists then set the value with the current time\n          this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime());\n        } else {\n          // Otherwise add a new '_' parameter to the end with the current time\n          var reQueryString = /\\?/;\n          this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime();\n        }\n      }\n    }\n  }\n\n  Request.prototype.clone = function() {\n    return new Request(this, {body: this._bodyInit})\n  };\n\n  function decode(body) {\n    var form = new FormData();\n    body\n      .trim()\n      .split('&')\n      .forEach(function(bytes) {\n        if (bytes) {\n          var split = bytes.split('=');\n          var name = split.shift().replace(/\\+/g, ' ');\n          var value = split.join('=').replace(/\\+/g, ' ');\n          form.append(decodeURIComponent(name), decodeURIComponent(value));\n        }\n      });\n    return form\n  }\n\n  function parseHeaders(rawHeaders) {\n    var headers = new Headers();\n    // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n    // https://tools.ietf.org/html/rfc7230#section-3.2\n    var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n    // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n    // https://github.com/github/fetch/issues/748\n    // https://github.com/zloirock/core-js/issues/751\n    preProcessedHeaders\n      .split('\\r')\n      .map(function(header) {\n        return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n      })\n      .forEach(function(line) {\n        var parts = line.split(':');\n        var key = parts.shift().trim();\n        if (key) {\n          var value = parts.join(':').trim();\n          try {\n            headers.append(key, value);\n          } catch (error) {\n            console.warn('Response ' + error.message);\n          }\n        }\n      });\n    return headers\n  }\n\n  Body.call(Request.prototype);\n\n  function Response(bodyInit, options) {\n    if (!(this instanceof Response)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n    if (!options) {\n      options = {};\n    }\n\n    this.type = 'default';\n    this.status = options.status === undefined ? 200 : options.status;\n    if (this.status < 200 || this.status > 599) {\n      throw new RangeError(\"Failed to construct 'Response': The status provided (0) is outside the range [200, 599].\")\n    }\n    this.ok = this.status >= 200 && this.status < 300;\n    this.statusText = options.statusText === undefined ? '' : '' + options.statusText;\n    this.headers = new Headers(options.headers);\n    this.url = options.url || '';\n    this._initBody(bodyInit);\n  }\n\n  Body.call(Response.prototype);\n\n  Response.prototype.clone = function() {\n    return new Response(this._bodyInit, {\n      status: this.status,\n      statusText: this.statusText,\n      headers: new Headers(this.headers),\n      url: this.url\n    })\n  };\n\n  Response.error = function() {\n    var response = new Response(null, {status: 200, statusText: ''});\n    response.ok = false;\n    response.status = 0;\n    response.type = 'error';\n    return response\n  };\n\n  var redirectStatuses = [301, 302, 303, 307, 308];\n\n  Response.redirect = function(url, status) {\n    if (redirectStatuses.indexOf(status) === -1) {\n      throw new RangeError('Invalid status code')\n    }\n\n    return new Response(null, {status: status, headers: {location: url}})\n  };\n\n  exports.DOMException = g.DOMException;\n  try {\n    new exports.DOMException();\n  } catch (err) {\n    exports.DOMException = function(message, name) {\n      this.message = message;\n      this.name = name;\n      var error = Error(message);\n      this.stack = error.stack;\n    };\n    exports.DOMException.prototype = Object.create(Error.prototype);\n    exports.DOMException.prototype.constructor = exports.DOMException;\n  }\n\n  function fetch(input, init) {\n    return new Promise(function(resolve, reject) {\n      var request = new Request(input, init);\n\n      if (request.signal && request.signal.aborted) {\n        return reject(new exports.DOMException('Aborted', 'AbortError'))\n      }\n\n      var xhr = new XMLHttpRequest();\n\n      function abortXhr() {\n        xhr.abort();\n      }\n\n      xhr.onload = function() {\n        var options = {\n          statusText: xhr.statusText,\n          headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n        };\n        // This check if specifically for when a user fetches a file locally from the file system\n        // Only if the status is out of a normal range\n        if (request.url.indexOf('file://') === 0 && (xhr.status < 200 || xhr.status > 599)) {\n          options.status = 200;\n        } else {\n          options.status = xhr.status;\n        }\n        options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n        var body = 'response' in xhr ? xhr.response : xhr.responseText;\n        setTimeout(function() {\n          resolve(new Response(body, options));\n        }, 0);\n      };\n\n      xhr.onerror = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.ontimeout = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request timed out'));\n        }, 0);\n      };\n\n      xhr.onabort = function() {\n        setTimeout(function() {\n          reject(new exports.DOMException('Aborted', 'AbortError'));\n        }, 0);\n      };\n\n      function fixUrl(url) {\n        try {\n          return url === '' && g.location.href ? g.location.href : url\n        } catch (e) {\n          return url\n        }\n      }\n\n      xhr.open(request.method, fixUrl(request.url), true);\n\n      if (request.credentials === 'include') {\n        xhr.withCredentials = true;\n      } else if (request.credentials === 'omit') {\n        xhr.withCredentials = false;\n      }\n\n      if ('responseType' in xhr) {\n        if (support.blob) {\n          xhr.responseType = 'blob';\n        } else if (\n          support.arrayBuffer\n        ) {\n          xhr.responseType = 'arraybuffer';\n        }\n      }\n\n      if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers || (g.Headers && init.headers instanceof g.Headers))) {\n        var names = [];\n        Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n          names.push(normalizeName(name));\n          xhr.setRequestHeader(name, normalizeValue(init.headers[name]));\n        });\n        request.headers.forEach(function(value, name) {\n          if (names.indexOf(name) === -1) {\n            xhr.setRequestHeader(name, value);\n          }\n        });\n      } else {\n        request.headers.forEach(function(value, name) {\n          xhr.setRequestHeader(name, value);\n        });\n      }\n\n      if (request.signal) {\n        request.signal.addEventListener('abort', abortXhr);\n\n        xhr.onreadystatechange = function() {\n          // DONE (success or failure)\n          if (xhr.readyState === 4) {\n            request.signal.removeEventListener('abort', abortXhr);\n          }\n        };\n      }\n\n      xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n    })\n  }\n\n  fetch.polyfill = true;\n\n  if (!g.fetch) {\n    g.fetch = fetch;\n    g.Headers = Headers;\n    g.Request = Request;\n    g.Response = Response;\n  }\n\n  exports.Headers = Headers;\n  exports.Request = Request;\n  exports.Response = Response;\n  exports.fetch = fetch;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n})({});\n})(__globalThis__);\n// This is a ponyfill, so...\n__globalThis__.fetch.ponyfill = true;\ndelete __globalThis__.fetch.polyfill;\n// Choose between native implementation (__global__) or custom implementation (__globalThis__)\nvar ctx = __global__.fetch ? __global__ : __globalThis__;\nexports = ctx.fetch // To enable: import fetch from 'cross-fetch'\nexports.default = ctx.fetch // For TypeScript consumers without esModuleInterop.\nexports.fetch = ctx.fetch // To enable: import {fetch} from 'cross-fetch'\nexports.Headers = ctx.Headers\nexports.Request = ctx.Request\nexports.Response = ctx.Response\nmodule.exports = exports\n", null, null, null, "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", null, null, null, null, null, null, "import { EventEmitter } from \"events\";\n\nexport abstract class IEvents {\n  public abstract events: EventEmitter;\n\n  // events\n  public abstract on(event: string, listener: any): void;\n  public abstract once(event: string, listener: any): void;\n  public abstract off(event: string, listener: any): void;\n  public abstract removeListener(event: string, listener: any): void;\n}\n", "import { JsonRpcPayload, JsonRpcRequest, RequestArguments } from \"./jsonrpc\";\nimport { IEvents } from \"./misc\";\n\nexport abstract class IJsonRpcConnection extends IEvents {\n  public abstract connected: boolean;\n  public abstract connecting: boolean;\n  // @ts-ignore - opts is not used in abstract class constructor\n  constructor(opts?: any) {\n    super();\n  }\n\n  public abstract open(opts?: any): Promise<void>;\n  public abstract close(): Promise<void>;\n  public abstract send(payload: JsonRpcPayload, context?: any): Promise<void>;\n}\n\nexport abstract class IBaseJsonRpcProvider extends IEvents {\n  // eslint-disable-next-line no-useless-constructor\n  constructor() {\n    super();\n  }\n\n  public abstract connect(params?: any): Promise<void>;\n\n  public abstract disconnect(): Promise<void>;\n\n  public abstract request<Result = any, Params = any>(\n    request: RequestArguments<Params>,\n    context?: any,\n  ): Promise<Result>;\n\n  // ---------- Protected ----------------------------------------------- //\n\n  protected abstract requestStrict<Result = any, Params = any>(\n    request: JsonRpcRequest<Params>,\n    context?: any,\n  ): Promise<Result>;\n}\n\nexport abstract class IJsonRpcProvider extends IBaseJsonRpcProvider {\n  public abstract connection: IJsonRpcConnection;\n\n  // @ts-ignore - connection is not used in abstract class constructor\n  constructor(connection: string | IJsonRpcConnection) {\n    super();\n  }\n\n  public abstract connect(connection?: string | IJsonRpcConnection): Promise<void>;\n\n  // ---------- Protected ----------------------------------------------- //\n\n  protected abstract setConnection(connection?: string | IJsonRpcConnection): IJsonRpcConnection;\n\n  protected abstract onPayload(payload: JsonRpcPayload): void;\n\n  protected abstract open(connection?: string | IJsonRpcConnection): Promise<void>;\n\n  protected abstract close(): Promise<void>;\n}\n", null, null, null, null], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AACA,QAAI,aACH,OAAO,eAAe,eAAe,cACrC,OAAO,SAAS,eAAe,QAC/B,OAAO,WAAW,eAAe;AAElC,QAAI,iBAAkB,WAAY;AAClC,eAAS,IAAI;AACb,aAAK,QAAQ;AACb,aAAK,eAAe,WAAW;AAAA,MAC/B;AACA,QAAE,YAAY;AACd,aAAO,IAAI,EAAE;AAAA,IACb,EAAG;AAGH,KAAC,SAASA,aAAY;AAEtB,UAAI,aAAc,SAAUC,UAAS;AAGnC,YAAIC,KACD,OAAOF,gBAAe,eAAeA,eACrC,OAAO,SAAS,eAAe;AAAA,QAE/B,OAAO,WAAW,eAAe,UAClC,CAAC;AAEH,YAAI,UAAU;AAAA,UACZ,cAAc,qBAAqBE;AAAA,UACnC,UAAU,YAAYA,MAAK,cAAc;AAAA,UACzC,MACE,gBAAgBA,MAChB,UAAUA,MACT,WAAW;AACV,gBAAI;AACF,kBAAI,KAAK;AACT,qBAAO;AAAA,YACT,SAASC,IAAG;AACV,qBAAO;AAAA,YACT;AAAA,UACF,EAAG;AAAA,UACL,UAAU,cAAcD;AAAA,UACxB,aAAa,iBAAiBA;AAAA,QAChC;AAEA,iBAAS,WAAW,KAAK;AACvB,iBAAO,OAAO,SAAS,UAAU,cAAc,GAAG;AAAA,QACpD;AAEA,YAAI,QAAQ,aAAa;AACvB,cAAI,cAAc;AAAA,YAChB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAEA,cAAI,oBACF,YAAY,UACZ,SAAS,KAAK;AACZ,mBAAO,OAAO,YAAY,QAAQ,OAAO,UAAU,SAAS,KAAK,GAAG,CAAC,IAAI;AAAA,UAC3E;AAAA,QACJ;AAEA,iBAAS,cAAc,MAAM;AAC3B,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO,OAAO,IAAI;AAAA,UACpB;AACA,cAAI,6BAA6B,KAAK,IAAI,KAAK,SAAS,IAAI;AAC1D,kBAAM,IAAI,UAAU,8CAA8C,OAAO,GAAG;AAAA,UAC9E;AACA,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,iBAAS,eAAe,OAAO;AAC7B,cAAI,OAAO,UAAU,UAAU;AAC7B,oBAAQ,OAAO,KAAK;AAAA,UACtB;AACA,iBAAO;AAAA,QACT;AAGA,iBAAS,YAAY,OAAO;AAC1B,cAAI,WAAW;AAAA,YACb,MAAM,WAAW;AACf,kBAAI,QAAQ,MAAM,MAAM;AACxB,qBAAO,EAAC,MAAM,UAAU,QAAW,MAAY;AAAA,YACjD;AAAA,UACF;AAEA,cAAI,QAAQ,UAAU;AACpB,qBAAS,OAAO,QAAQ,IAAI,WAAW;AACrC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,QAAQ,SAAS;AACxB,eAAK,MAAM,CAAC;AAEZ,cAAI,mBAAmB,SAAS;AAC9B,oBAAQ,QAAQ,SAAS,OAAO,MAAM;AACpC,mBAAK,OAAO,MAAM,KAAK;AAAA,YACzB,GAAG,IAAI;AAAA,UACT,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,oBAAQ,QAAQ,SAAS,QAAQ;AAC/B,kBAAI,OAAO,UAAU,GAAG;AACtB,sBAAM,IAAI,UAAU,wEAAwE,OAAO,MAAM;AAAA,cAC3G;AACA,mBAAK,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,YAClC,GAAG,IAAI;AAAA,UACT,WAAW,SAAS;AAClB,mBAAO,oBAAoB,OAAO,EAAE,QAAQ,SAAS,MAAM;AACzD,mBAAK,OAAO,MAAM,QAAQ,IAAI,CAAC;AAAA,YACjC,GAAG,IAAI;AAAA,UACT;AAAA,QACF;AAEA,gBAAQ,UAAU,SAAS,SAAS,MAAM,OAAO;AAC/C,iBAAO,cAAc,IAAI;AACzB,kBAAQ,eAAe,KAAK;AAC5B,cAAI,WAAW,KAAK,IAAI,IAAI;AAC5B,eAAK,IAAI,IAAI,IAAI,WAAW,WAAW,OAAO,QAAQ;AAAA,QACxD;AAEA,gBAAQ,UAAU,QAAQ,IAAI,SAAS,MAAM;AAC3C,iBAAO,KAAK,IAAI,cAAc,IAAI,CAAC;AAAA,QACrC;AAEA,gBAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,iBAAO,cAAc,IAAI;AACzB,iBAAO,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,QAC3C;AAEA,gBAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,iBAAO,KAAK,IAAI,eAAe,cAAc,IAAI,CAAC;AAAA,QACpD;AAEA,gBAAQ,UAAU,MAAM,SAAS,MAAM,OAAO;AAC5C,eAAK,IAAI,cAAc,IAAI,CAAC,IAAI,eAAe,KAAK;AAAA,QACtD;AAEA,gBAAQ,UAAU,UAAU,SAAS,UAAU,SAAS;AACtD,mBAAS,QAAQ,KAAK,KAAK;AACzB,gBAAI,KAAK,IAAI,eAAe,IAAI,GAAG;AACjC,uBAAS,KAAK,SAAS,KAAK,IAAI,IAAI,GAAG,MAAM,IAAI;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ,UAAU,OAAO,WAAW;AAClC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,kBAAM,KAAK,IAAI;AAAA,UACjB,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,gBAAQ,UAAU,SAAS,WAAW;AACpC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO;AAC3B,kBAAM,KAAK,KAAK;AAAA,UAClB,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,gBAAQ,UAAU,UAAU,WAAW;AACrC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,kBAAM,KAAK,CAAC,MAAM,KAAK,CAAC;AAAA,UAC1B,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,YAAI,QAAQ,UAAU;AACpB,kBAAQ,UAAU,OAAO,QAAQ,IAAI,QAAQ,UAAU;AAAA,QACzD;AAEA,iBAAS,SAAS,MAAM;AACtB,cAAI,KAAK;AAAS;AAClB,cAAI,KAAK,UAAU;AACjB,mBAAO,QAAQ,OAAO,IAAI,UAAU,cAAc,CAAC;AAAA,UACrD;AACA,eAAK,WAAW;AAAA,QAClB;AAEA,iBAAS,gBAAgB,QAAQ;AAC/B,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,mBAAO,SAAS,WAAW;AACzB,sBAAQ,OAAO,MAAM;AAAA,YACvB;AACA,mBAAO,UAAU,WAAW;AAC1B,qBAAO,OAAO,KAAK;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,QACH;AAEA,iBAAS,sBAAsB,MAAM;AACnC,cAAI,SAAS,IAAI,WAAW;AAC5B,cAAI,UAAU,gBAAgB,MAAM;AACpC,iBAAO,kBAAkB,IAAI;AAC7B,iBAAO;AAAA,QACT;AAEA,iBAAS,eAAe,MAAM;AAC5B,cAAI,SAAS,IAAI,WAAW;AAC5B,cAAI,UAAU,gBAAgB,MAAM;AACpC,cAAI,QAAQ,2BAA2B,KAAK,KAAK,IAAI;AACrD,cAAI,WAAW,QAAQ,MAAM,CAAC,IAAI;AAClC,iBAAO,WAAW,MAAM,QAAQ;AAChC,iBAAO;AAAA,QACT;AAEA,iBAAS,sBAAsB,KAAK;AAClC,cAAI,OAAO,IAAI,WAAW,GAAG;AAC7B,cAAI,QAAQ,IAAI,MAAM,KAAK,MAAM;AAEjC,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,CAAC,IAAI,OAAO,aAAa,KAAK,CAAC,CAAC;AAAA,UACxC;AACA,iBAAO,MAAM,KAAK,EAAE;AAAA,QACtB;AAEA,iBAAS,YAAY,KAAK;AACxB,cAAI,IAAI,OAAO;AACb,mBAAO,IAAI,MAAM,CAAC;AAAA,UACpB,OAAO;AACL,gBAAI,OAAO,IAAI,WAAW,IAAI,UAAU;AACxC,iBAAK,IAAI,IAAI,WAAW,GAAG,CAAC;AAC5B,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAEA,iBAAS,OAAO;AACd,eAAK,WAAW;AAEhB,eAAK,YAAY,SAAS,MAAM;AAY9B,iBAAK,WAAW,KAAK;AACrB,iBAAK,YAAY;AACjB,gBAAI,CAAC,MAAM;AACT,mBAAK,UAAU;AACf,mBAAK,YAAY;AAAA,YACnB,WAAW,OAAO,SAAS,UAAU;AACnC,mBAAK,YAAY;AAAA,YACnB,WAAW,QAAQ,QAAQ,KAAK,UAAU,cAAc,IAAI,GAAG;AAC7D,mBAAK,YAAY;AAAA,YACnB,WAAW,QAAQ,YAAY,SAAS,UAAU,cAAc,IAAI,GAAG;AACrE,mBAAK,gBAAgB;AAAA,YACvB,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,mBAAK,YAAY,KAAK,SAAS;AAAA,YACjC,WAAW,QAAQ,eAAe,QAAQ,QAAQ,WAAW,IAAI,GAAG;AAClE,mBAAK,mBAAmB,YAAY,KAAK,MAAM;AAE/C,mBAAK,YAAY,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC;AAAA,YACnD,WAAW,QAAQ,gBAAgB,YAAY,UAAU,cAAc,IAAI,KAAK,kBAAkB,IAAI,IAAI;AACxG,mBAAK,mBAAmB,YAAY,IAAI;AAAA,YAC1C,OAAO;AACL,mBAAK,YAAY,OAAO,OAAO,UAAU,SAAS,KAAK,IAAI;AAAA,YAC7D;AAEA,gBAAI,CAAC,KAAK,QAAQ,IAAI,cAAc,GAAG;AACrC,kBAAI,OAAO,SAAS,UAAU;AAC5B,qBAAK,QAAQ,IAAI,gBAAgB,0BAA0B;AAAA,cAC7D,WAAW,KAAK,aAAa,KAAK,UAAU,MAAM;AAChD,qBAAK,QAAQ,IAAI,gBAAgB,KAAK,UAAU,IAAI;AAAA,cACtD,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,qBAAK,QAAQ,IAAI,gBAAgB,iDAAiD;AAAA,cACpF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,QAAQ,MAAM;AAChB,iBAAK,OAAO,WAAW;AACrB,kBAAI,WAAW,SAAS,IAAI;AAC5B,kBAAI,UAAU;AACZ,uBAAO;AAAA,cACT;AAEA,kBAAI,KAAK,WAAW;AAClB,uBAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,cACvC,WAAW,KAAK,kBAAkB;AAChC,uBAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC;AAAA,cAC1D,WAAW,KAAK,eAAe;AAC7B,sBAAM,IAAI,MAAM,sCAAsC;AAAA,cACxD,OAAO;AACL,uBAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;AAAA,cACnD;AAAA,YACF;AAAA,UACF;AAEA,eAAK,cAAc,WAAW;AAC5B,gBAAI,KAAK,kBAAkB;AACzB,kBAAI,aAAa,SAAS,IAAI;AAC9B,kBAAI,YAAY;AACd,uBAAO;AAAA,cACT,WAAW,YAAY,OAAO,KAAK,gBAAgB,GAAG;AACpD,uBAAO,QAAQ;AAAA,kBACb,KAAK,iBAAiB,OAAO;AAAA,oBAC3B,KAAK,iBAAiB;AAAA,oBACtB,KAAK,iBAAiB,aAAa,KAAK,iBAAiB;AAAA,kBAC3D;AAAA,gBACF;AAAA,cACF,OAAO;AACL,uBAAO,QAAQ,QAAQ,KAAK,gBAAgB;AAAA,cAC9C;AAAA,YACF,WAAW,QAAQ,MAAM;AACvB,qBAAO,KAAK,KAAK,EAAE,KAAK,qBAAqB;AAAA,YAC/C,OAAO;AACL,oBAAM,IAAI,MAAM,+BAA+B;AAAA,YACjD;AAAA,UACF;AAEA,eAAK,OAAO,WAAW;AACrB,gBAAI,WAAW,SAAS,IAAI;AAC5B,gBAAI,UAAU;AACZ,qBAAO;AAAA,YACT;AAEA,gBAAI,KAAK,WAAW;AAClB,qBAAO,eAAe,KAAK,SAAS;AAAA,YACtC,WAAW,KAAK,kBAAkB;AAChC,qBAAO,QAAQ,QAAQ,sBAAsB,KAAK,gBAAgB,CAAC;AAAA,YACrE,WAAW,KAAK,eAAe;AAC7B,oBAAM,IAAI,MAAM,sCAAsC;AAAA,YACxD,OAAO;AACL,qBAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,YACvC;AAAA,UACF;AAEA,cAAI,QAAQ,UAAU;AACpB,iBAAK,WAAW,WAAW;AACzB,qBAAO,KAAK,KAAK,EAAE,KAAK,MAAM;AAAA,YAChC;AAAA,UACF;AAEA,eAAK,OAAO,WAAW;AACrB,mBAAO,KAAK,KAAK,EAAE,KAAK,KAAK,KAAK;AAAA,UACpC;AAEA,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,CAAC,WAAW,UAAU,OAAO,QAAQ,WAAW,SAAS,QAAQ,OAAO,OAAO;AAE7F,iBAAS,gBAAgB,QAAQ;AAC/B,cAAI,UAAU,OAAO,YAAY;AACjC,iBAAO,QAAQ,QAAQ,OAAO,IAAI,KAAK,UAAU;AAAA,QACnD;AAEA,iBAAS,QAAQ,OAAO,SAAS;AAC/B,cAAI,EAAE,gBAAgB,UAAU;AAC9B,kBAAM,IAAI,UAAU,4FAA4F;AAAA,UAClH;AAEA,oBAAU,WAAW,CAAC;AACtB,cAAI,OAAO,QAAQ;AAEnB,cAAI,iBAAiB,SAAS;AAC5B,gBAAI,MAAM,UAAU;AAClB,oBAAM,IAAI,UAAU,cAAc;AAAA,YACpC;AACA,iBAAK,MAAM,MAAM;AACjB,iBAAK,cAAc,MAAM;AACzB,gBAAI,CAAC,QAAQ,SAAS;AACpB,mBAAK,UAAU,IAAI,QAAQ,MAAM,OAAO;AAAA,YAC1C;AACA,iBAAK,SAAS,MAAM;AACpB,iBAAK,OAAO,MAAM;AAClB,iBAAK,SAAS,MAAM;AACpB,gBAAI,CAAC,QAAQ,MAAM,aAAa,MAAM;AACpC,qBAAO,MAAM;AACb,oBAAM,WAAW;AAAA,YACnB;AAAA,UACF,OAAO;AACL,iBAAK,MAAM,OAAO,KAAK;AAAA,UACzB;AAEA,eAAK,cAAc,QAAQ,eAAe,KAAK,eAAe;AAC9D,cAAI,QAAQ,WAAW,CAAC,KAAK,SAAS;AACpC,iBAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAAA,UAC5C;AACA,eAAK,SAAS,gBAAgB,QAAQ,UAAU,KAAK,UAAU,KAAK;AACpE,eAAK,OAAO,QAAQ,QAAQ,KAAK,QAAQ;AACzC,eAAK,SAAS,QAAQ,UAAU,KAAK,UAAW,WAAY;AAC1D,gBAAI,qBAAqBA,IAAG;AAC1B,kBAAI,OAAO,IAAI,gBAAgB;AAC/B,qBAAO,KAAK;AAAA,YACd;AAAA,UACF,EAAE;AACF,eAAK,WAAW;AAEhB,eAAK,KAAK,WAAW,SAAS,KAAK,WAAW,WAAW,MAAM;AAC7D,kBAAM,IAAI,UAAU,2CAA2C;AAAA,UACjE;AACA,eAAK,UAAU,IAAI;AAEnB,cAAI,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ;AACnD,gBAAI,QAAQ,UAAU,cAAc,QAAQ,UAAU,YAAY;AAEhE,kBAAI,gBAAgB;AACpB,kBAAI,cAAc,KAAK,KAAK,GAAG,GAAG;AAEhC,qBAAK,MAAM,KAAK,IAAI,QAAQ,eAAe,UAAS,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAAA,cAC1E,OAAO;AAEL,oBAAI,gBAAgB;AACpB,qBAAK,QAAQ,cAAc,KAAK,KAAK,GAAG,IAAI,MAAM,OAAO,QAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,cACrF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ,UAAU,QAAQ,WAAW;AACnC,iBAAO,IAAI,QAAQ,MAAM,EAAC,MAAM,KAAK,UAAS,CAAC;AAAA,QACjD;AAEA,iBAAS,OAAO,MAAM;AACpB,cAAI,OAAO,IAAI,SAAS;AACxB,eACG,KAAK,EACL,MAAM,GAAG,EACT,QAAQ,SAAS,OAAO;AACvB,gBAAI,OAAO;AACT,kBAAI,QAAQ,MAAM,MAAM,GAAG;AAC3B,kBAAI,OAAO,MAAM,MAAM,EAAE,QAAQ,OAAO,GAAG;AAC3C,kBAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC9C,mBAAK,OAAO,mBAAmB,IAAI,GAAG,mBAAmB,KAAK,CAAC;AAAA,YACjE;AAAA,UACF,CAAC;AACH,iBAAO;AAAA,QACT;AAEA,iBAAS,aAAa,YAAY;AAChC,cAAI,UAAU,IAAI,QAAQ;AAG1B,cAAI,sBAAsB,WAAW,QAAQ,gBAAgB,GAAG;AAIhE,8BACG,MAAM,IAAI,EACV,IAAI,SAAS,QAAQ;AACpB,mBAAO,OAAO,QAAQ,IAAI,MAAM,IAAI,OAAO,OAAO,GAAG,OAAO,MAAM,IAAI;AAAA,UACxE,CAAC,EACA,QAAQ,SAAS,MAAM;AACtB,gBAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,gBAAI,MAAM,MAAM,MAAM,EAAE,KAAK;AAC7B,gBAAI,KAAK;AACP,kBAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,KAAK;AACjC,kBAAI;AACF,wBAAQ,OAAO,KAAK,KAAK;AAAA,cAC3B,SAAS,OAAO;AACd,wBAAQ,KAAK,cAAc,MAAM,OAAO;AAAA,cAC1C;AAAA,YACF;AAAA,UACF,CAAC;AACH,iBAAO;AAAA,QACT;AAEA,aAAK,KAAK,QAAQ,SAAS;AAE3B,iBAAS,SAAS,UAAU,SAAS;AACnC,cAAI,EAAE,gBAAgB,WAAW;AAC/B,kBAAM,IAAI,UAAU,4FAA4F;AAAA,UAClH;AACA,cAAI,CAAC,SAAS;AACZ,sBAAU,CAAC;AAAA,UACb;AAEA,eAAK,OAAO;AACZ,eAAK,SAAS,QAAQ,WAAW,SAAY,MAAM,QAAQ;AAC3D,cAAI,KAAK,SAAS,OAAO,KAAK,SAAS,KAAK;AAC1C,kBAAM,IAAI,WAAW,0FAA0F;AAAA,UACjH;AACA,eAAK,KAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAC9C,eAAK,aAAa,QAAQ,eAAe,SAAY,KAAK,KAAK,QAAQ;AACvE,eAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAC1C,eAAK,MAAM,QAAQ,OAAO;AAC1B,eAAK,UAAU,QAAQ;AAAA,QACzB;AAEA,aAAK,KAAK,SAAS,SAAS;AAE5B,iBAAS,UAAU,QAAQ,WAAW;AACpC,iBAAO,IAAI,SAAS,KAAK,WAAW;AAAA,YAClC,QAAQ,KAAK;AAAA,YACb,YAAY,KAAK;AAAA,YACjB,SAAS,IAAI,QAAQ,KAAK,OAAO;AAAA,YACjC,KAAK,KAAK;AAAA,UACZ,CAAC;AAAA,QACH;AAEA,iBAAS,QAAQ,WAAW;AAC1B,cAAI,WAAW,IAAI,SAAS,MAAM,EAAC,QAAQ,KAAK,YAAY,GAAE,CAAC;AAC/D,mBAAS,KAAK;AACd,mBAAS,SAAS;AAClB,mBAAS,OAAO;AAChB,iBAAO;AAAA,QACT;AAEA,YAAI,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAE/C,iBAAS,WAAW,SAAS,KAAK,QAAQ;AACxC,cAAI,iBAAiB,QAAQ,MAAM,MAAM,IAAI;AAC3C,kBAAM,IAAI,WAAW,qBAAqB;AAAA,UAC5C;AAEA,iBAAO,IAAI,SAAS,MAAM,EAAC,QAAgB,SAAS,EAAC,UAAU,IAAG,EAAC,CAAC;AAAA,QACtE;AAEA,QAAAD,SAAQ,eAAeC,GAAE;AACzB,YAAI;AACF,cAAID,SAAQ,aAAa;AAAA,QAC3B,SAAS,KAAK;AACZ,UAAAA,SAAQ,eAAe,SAAS,SAAS,MAAM;AAC7C,iBAAK,UAAU;AACf,iBAAK,OAAO;AACZ,gBAAI,QAAQ,MAAM,OAAO;AACzB,iBAAK,QAAQ,MAAM;AAAA,UACrB;AACA,UAAAA,SAAQ,aAAa,YAAY,OAAO,OAAO,MAAM,SAAS;AAC9D,UAAAA,SAAQ,aAAa,UAAU,cAAcA,SAAQ;AAAA,QACvD;AAEA,iBAAS,MAAM,OAAO,MAAM;AAC1B,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,gBAAI,UAAU,IAAI,QAAQ,OAAO,IAAI;AAErC,gBAAI,QAAQ,UAAU,QAAQ,OAAO,SAAS;AAC5C,qBAAO,OAAO,IAAIA,SAAQ,aAAa,WAAW,YAAY,CAAC;AAAA,YACjE;AAEA,gBAAI,MAAM,IAAI,eAAe;AAE7B,qBAAS,WAAW;AAClB,kBAAI,MAAM;AAAA,YACZ;AAEA,gBAAI,SAAS,WAAW;AACtB,kBAAI,UAAU;AAAA,gBACZ,YAAY,IAAI;AAAA,gBAChB,SAAS,aAAa,IAAI,sBAAsB,KAAK,EAAE;AAAA,cACzD;AAGA,kBAAI,QAAQ,IAAI,QAAQ,SAAS,MAAM,MAAM,IAAI,SAAS,OAAO,IAAI,SAAS,MAAM;AAClF,wBAAQ,SAAS;AAAA,cACnB,OAAO;AACL,wBAAQ,SAAS,IAAI;AAAA,cACvB;AACA,sBAAQ,MAAM,iBAAiB,MAAM,IAAI,cAAc,QAAQ,QAAQ,IAAI,eAAe;AAC1F,kBAAI,OAAO,cAAc,MAAM,IAAI,WAAW,IAAI;AAClD,yBAAW,WAAW;AACpB,wBAAQ,IAAI,SAAS,MAAM,OAAO,CAAC;AAAA,cACrC,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,UAAU,WAAW;AACvB,yBAAW,WAAW;AACpB,uBAAO,IAAI,UAAU,wBAAwB,CAAC;AAAA,cAChD,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,YAAY,WAAW;AACzB,yBAAW,WAAW;AACpB,uBAAO,IAAI,UAAU,2BAA2B,CAAC;AAAA,cACnD,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,UAAU,WAAW;AACvB,yBAAW,WAAW;AACpB,uBAAO,IAAIA,SAAQ,aAAa,WAAW,YAAY,CAAC;AAAA,cAC1D,GAAG,CAAC;AAAA,YACN;AAEA,qBAAS,OAAO,KAAK;AACnB,kBAAI;AACF,uBAAO,QAAQ,MAAMC,GAAE,SAAS,OAAOA,GAAE,SAAS,OAAO;AAAA,cAC3D,SAASC,IAAG;AACV,uBAAO;AAAA,cACT;AAAA,YACF;AAEA,gBAAI,KAAK,QAAQ,QAAQ,OAAO,QAAQ,GAAG,GAAG,IAAI;AAElD,gBAAI,QAAQ,gBAAgB,WAAW;AACrC,kBAAI,kBAAkB;AAAA,YACxB,WAAW,QAAQ,gBAAgB,QAAQ;AACzC,kBAAI,kBAAkB;AAAA,YACxB;AAEA,gBAAI,kBAAkB,KAAK;AACzB,kBAAI,QAAQ,MAAM;AAChB,oBAAI,eAAe;AAAA,cACrB,WACE,QAAQ,aACR;AACA,oBAAI,eAAe;AAAA,cACrB;AAAA,YACF;AAEA,gBAAI,QAAQ,OAAO,KAAK,YAAY,YAAY,EAAE,KAAK,mBAAmB,WAAYD,GAAE,WAAW,KAAK,mBAAmBA,GAAE,UAAW;AACtI,kBAAI,QAAQ,CAAC;AACb,qBAAO,oBAAoB,KAAK,OAAO,EAAE,QAAQ,SAAS,MAAM;AAC9D,sBAAM,KAAK,cAAc,IAAI,CAAC;AAC9B,oBAAI,iBAAiB,MAAM,eAAe,KAAK,QAAQ,IAAI,CAAC,CAAC;AAAA,cAC/D,CAAC;AACD,sBAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAC5C,oBAAI,MAAM,QAAQ,IAAI,MAAM,IAAI;AAC9B,sBAAI,iBAAiB,MAAM,KAAK;AAAA,gBAClC;AAAA,cACF,CAAC;AAAA,YACH,OAAO;AACL,sBAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAC5C,oBAAI,iBAAiB,MAAM,KAAK;AAAA,cAClC,CAAC;AAAA,YACH;AAEA,gBAAI,QAAQ,QAAQ;AAClB,sBAAQ,OAAO,iBAAiB,SAAS,QAAQ;AAEjD,kBAAI,qBAAqB,WAAW;AAElC,oBAAI,IAAI,eAAe,GAAG;AACxB,0BAAQ,OAAO,oBAAoB,SAAS,QAAQ;AAAA,gBACtD;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,KAAK,OAAO,QAAQ,cAAc,cAAc,OAAO,QAAQ,SAAS;AAAA,UAC9E,CAAC;AAAA,QACH;AAEA,cAAM,WAAW;AAEjB,YAAI,CAACA,GAAE,OAAO;AACZ,UAAAA,GAAE,QAAQ;AACV,UAAAA,GAAE,UAAU;AACZ,UAAAA,GAAE,UAAU;AACZ,UAAAA,GAAE,WAAW;AAAA,QACf;AAEA,QAAAD,SAAQ,UAAU;AAClB,QAAAA,SAAQ,UAAU;AAClB,QAAAA,SAAQ,WAAW;AACnB,QAAAA,SAAQ,QAAQ;AAEhB,eAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,eAAOA;AAAA,MAET,EAAG,CAAC,CAAC;AAAA,IACL,GAAG,cAAc;AAEjB,mBAAe,MAAM,WAAW;AAChC,WAAO,eAAe,MAAM;AAE5B,QAAI,MAAM,WAAW,QAAQ,aAAa;AAC1C,cAAU,IAAI;AACd,YAAQ,UAAU,IAAI;AACtB,YAAQ,QAAQ,IAAI;AACpB,YAAQ,UAAU,IAAI;AACtB,YAAQ,UAAU,IAAI;AACtB,YAAQ,WAAW,IAAI;AACvB,WAAO,UAAU;AAAA;AAAA;;;ACroBX,SAAU,cAAuB,OAAa;AAClD,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,MAAM,wCAAwC,OAAO,KAAK,EAAE;;AAExE,MAAI;AACF,WAAO,UAAU,KAAK;WACtB,IAAM;AACN,WAAO;;AAEX;AAEM,SAAU,kBAAkB,OAAU;AAC1C,SAAO,OAAO,UAAU,WAAW,QAAQ,cAAc,KAAK,KAAK;AACrE;AA7CA,IAAM,eAYA;AAZN;;IAAM,gBAAgB,UACpB,KAAK,UAAU,MAAM,CAAC,GAAG,UACvB,OAAO,UAAU,WAAW,MAAM,SAAQ,IAAK,MAAM,KAAK;AAU9D,IAAM,YAAY,UAAO;AAMvB,YAAM,0BAA0B;AAChC,YAAM,iBAAiB,KAAK,QAAQ,yBAAyB,WAAa;AAE1E,aAAO,KAAK,MAAM,gBAAgB,CAAC,GAAG,UAAS;AAC7C,cAAM,uBACJ,OAAO,UAAU,YAAY,MAAM,MAAM,QAAQ;AAEnD,YAAI;AACF,iBAAO,OAAO,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,CAAC;AAEpD,eAAO;MACT,CAAC;IACH;;;;;ACtCA,IAAa,aACA,iBACA,kBACA,gBACA,gBACA,cAEA,sBACA,yBAEA,oBASA;AAnBb;;AAAO,IAAM,cAAc;AACpB,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AACzB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AACvB,IAAM,eAAe;AAErB,IAAM,uBAAuB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AACpE,IAAM,0BAA0B,CAAC,OAAQ,MAAM;AAE/C,IAAM,qBAAqB;MAChC,CAAC,WAAW,GAAG,EAAE,MAAM,QAAQ,SAAS,cAAa;MACrD,CAAC,eAAe,GAAG,EAAE,MAAM,QAAQ,SAAS,kBAAiB;MAC7D,CAAC,gBAAgB,GAAG,EAAE,MAAM,QAAQ,SAAS,mBAAkB;MAC/D,CAAC,cAAc,GAAG,EAAE,MAAM,QAAQ,SAAS,iBAAgB;MAC3D,CAAC,cAAc,GAAG,EAAE,MAAM,QAAQ,SAAS,iBAAgB;MAC3D,CAAC,YAAY,GAAG,EAAE,MAAM,OAAQ,SAAS,eAAc;;AAGlD,IAAM,gBAAgB;;;;;ACVvB,SAAU,kBAAkB,MAAY;AAC5C,SAAO,QAAQ,wBAAwB,CAAC,KAAK,QAAQ,wBAAwB,CAAC;AAChF;AAEM,SAAU,oBAAoB,MAAY;AAC9C,SAAO,qBAAqB,SAAS,IAAI;AAC3C;AAEM,SAAU,iBAAiB,MAAY;AAC3C,SAAO,OAAO,SAAS;AACzB;AAEM,SAAU,SAAS,MAAY;AACnC,MAAI,CAAC,OAAO,KAAK,kBAAkB,EAAE,SAAS,IAAI,GAAG;AACnD,WAAO,mBAAmB,aAAa;;AAEzC,SAAO,mBAAmB,IAAI;AAChC;AAEM,SAAU,eAAe,MAAY;AACzC,QAAM,QAAQ,OAAO,OAAO,kBAAkB,EAAE,KAAK,CAAAG,OAAKA,GAAE,SAAS,IAAI;AACzE,MAAI,CAAC,OAAO;AACV,WAAO,mBAAmB,aAAa;;AAEzC,SAAO;AACT;AAEM,SAAU,qBAAqB,UAAsB;AACzD,MAAI,OAAO,SAAS,MAAM,SAAS,aAAa;AAC9C,WAAO,EAAE,OAAO,OAAO,OAAO,kCAAiC;;AAEjE,MAAI,OAAO,SAAS,MAAM,YAAY,aAAa;AACjD,WAAO,EAAE,OAAO,OAAO,OAAO,qCAAoC;;AAEpE,MAAI,CAAC,iBAAiB,SAAS,MAAM,IAAI,GAAG;AAC1C,WAAO;MACL,OAAO;MACP,OAAO,yCAAyC,SAAS,MAAM,IAAI;;;AAGvE,MAAI,oBAAoB,SAAS,MAAM,IAAI,GAAG;AAC5C,UAAM,QAAQ,eAAe,SAAS,MAAM,IAAI;AAChD,QACE,MAAM,YAAY,mBAAmB,aAAa,EAAE,WACpD,SAAS,MAAM,YAAY,MAAM,SACjC;AACA,aAAO;QACL,OAAO;QACP,OAAO,4CAA4C,SAAS,MAAM,IAAI;;;;AAI5E,SAAO,EAAE,OAAO,KAAI;AACtB;AAEM,SAAU,qBAAqBA,IAAU,KAAa,MAAY;AACtE,SAAOA,GAAE,QAAQ,SAAS,uBAAuB,KAAKA,GAAE,QAAQ,SAAS,sBAAsB,IAC3F,IAAI,MAAM,eAAe,IAAI,eAAe,GAAG,EAAE,IACjDA;AACN;AAnEA;;;;;;;ACDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBO,SAAS,UAAUC,IAAG,GAAG;AAC5B,gBAAcA,IAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAcA;AAAA,EAAG;AACtC,EAAAA,GAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAaO,SAAS,OAAO,GAAGC,IAAG;AACzB,MAAI,IAAI,CAAC;AACT,WAASC,MAAK;AAAG,QAAI,OAAO,UAAU,eAAe,KAAK,GAAGA,EAAC,KAAKD,GAAE,QAAQC,EAAC,IAAI;AAC9E,QAAEA,EAAC,IAAI,EAAEA,EAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAGA,KAAI,OAAO,sBAAsB,CAAC,GAAG,IAAIA,GAAE,QAAQ,KAAK;AACpE,UAAID,GAAE,QAAQC,GAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAGA,GAAE,CAAC,CAAC;AACzE,UAAEA,GAAE,CAAC,CAAC,IAAI,EAAEA,GAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAEO,SAAS,WAAW,YAAY,QAAQ,KAAK,MAAM;AACtD,MAAIC,KAAI,UAAU,QAAQC,KAAID,KAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAMH;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,IAAAI,KAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA;AACxH,aAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG;AAAK,UAAIJ,KAAI,WAAW,CAAC;AAAG,QAAAI,MAAKD,KAAI,IAAIH,GAAEI,EAAC,IAAID,KAAI,IAAIH,GAAE,QAAQ,KAAKI,EAAC,IAAIJ,GAAE,QAAQ,GAAG,MAAMI;AAChJ,SAAOD,KAAI,KAAKC,MAAK,OAAO,eAAe,QAAQ,KAAKA,EAAC,GAAGA;AAChE;AAEO,SAAS,QAAQ,YAAY,WAAW;AAC3C,SAAO,SAAU,QAAQ,KAAK;AAAE,cAAU,QAAQ,KAAK,UAAU;AAAA,EAAG;AACxE;AAEO,SAAS,WAAW,aAAa,eAAe;AACnD,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,WAAO,QAAQ,SAAS,aAAa,aAAa;AACjI;AAEO,SAAS,UAAU,SAAS,YAAYC,IAAG,WAAW;AACzD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiBA,KAAI,QAAQ,IAAIA,GAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAKA,OAAMA,KAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAASJ,IAAG;AAAE,eAAOA,EAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAASA,IAAG;AAAE,eAAOA,EAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEO,SAAS,YAAY,SAAS,MAAM;AACvC,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAGK,IAAG,GAAG,GAAGC;AAC/G,SAAOA,KAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAeA,GAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAIA;AACvJ,WAAS,KAAKC,IAAG;AAAE,WAAO,SAAUC,IAAG;AAAE,aAAO,KAAK,CAACD,IAAGC,EAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAIH;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO;AAAG,UAAI;AACV,YAAIA,KAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI,IAAI;AAAG,cAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,gBAAE,IAAI,IAAI;AACpB,cAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,SAASL,IAAG;AAAE,aAAK,CAAC,GAAGA,EAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,QAAAK,KAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAEO,SAAS,gBAAgBI,IAAGC,IAAG,GAAG,IAAI;AACzC,MAAI,OAAO;AAAW,SAAK;AAC3B,EAAAD,GAAE,EAAE,IAAIC,GAAE,CAAC;AACf;AAEO,SAAS,aAAaA,IAAG,SAAS;AACrC,WAAST,MAAKS;AAAG,QAAIT,OAAM,aAAa,CAAC,QAAQ,eAAeA,EAAC;AAAG,cAAQA,EAAC,IAAIS,GAAET,EAAC;AACxF;AAEO,SAAS,SAASQ,IAAG;AACxB,MAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAUC,KAAI,KAAKD,GAAE,CAAC,GAAG,IAAI;AAC5E,MAAIC;AAAG,WAAOA,GAAE,KAAKD,EAAC;AACtB,MAAIA,MAAK,OAAOA,GAAE,WAAW;AAAU,WAAO;AAAA,MAC1C,MAAM,WAAY;AACd,YAAIA,MAAK,KAAKA,GAAE;AAAQ,UAAAA,KAAI;AAC5B,eAAO,EAAE,OAAOA,MAAKA,GAAE,GAAG,GAAG,MAAM,CAACA,GAAE;AAAA,MAC1C;AAAA,IACJ;AACA,QAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AACzF;AAEO,SAAS,OAAOA,IAAGF,IAAG;AACzB,MAAIG,KAAI,OAAO,WAAW,cAAcD,GAAE,OAAO,QAAQ;AACzD,MAAI,CAACC;AAAG,WAAOD;AACf,MAAI,IAAIC,GAAE,KAAKD,EAAC,GAAGN,IAAG,KAAK,CAAC,GAAGH;AAC/B,MAAI;AACA,YAAQO,OAAM,UAAUA,OAAM,MAAM,EAAEJ,KAAI,EAAE,KAAK,GAAG;AAAM,SAAG,KAAKA,GAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,IAAAH,KAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAIG,MAAK,CAACA,GAAE,SAASO,KAAI,EAAE,QAAQ;AAAI,QAAAA,GAAE,KAAK,CAAC;AAAA,IACnD,UACA;AAAU,UAAIV;AAAG,cAAMA,GAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACX;AAEO,SAAS,WAAW;AACvB,WAAS,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ;AAC3C,SAAK,GAAG,OAAO,OAAO,UAAU,CAAC,CAAC,CAAC;AACvC,SAAO;AACX;AAEO,SAAS,iBAAiB;AAC7B,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI;AAAK,SAAK,UAAU,CAAC,EAAE;AAC7E,WAASG,KAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI;AACzC,aAAS,IAAI,UAAU,CAAC,GAAGQ,KAAI,GAAG,KAAK,EAAE,QAAQA,KAAI,IAAIA,MAAK;AAC1D,MAAAR,GAAE,CAAC,IAAI,EAAEQ,EAAC;AAClB,SAAOR;AACX;AAEO,SAAS,QAAQK,IAAG;AACvB,SAAO,gBAAgB,WAAW,KAAK,IAAIA,IAAG,QAAQ,IAAI,QAAQA,EAAC;AACvE;AAEO,SAAS,iBAAiB,SAAS,YAAY,WAAW;AAC7D,MAAI,CAAC,OAAO;AAAe,UAAM,IAAI,UAAU,sCAAsC;AACrF,MAAIF,KAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5D,SAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AACpH,WAAS,KAAKC,IAAG;AAAE,QAAID,GAAEC,EAAC;AAAG,QAAEA,EAAC,IAAI,SAAUC,IAAG;AAAE,eAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AAAE,YAAE,KAAK,CAACD,IAAGC,IAAG,GAAG,CAAC,CAAC,IAAI,KAAK,OAAOD,IAAGC,EAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAA,EAAG;AACzI,WAAS,OAAOD,IAAGC,IAAG;AAAE,QAAI;AAAE,WAAKF,GAAEC,EAAC,EAAEC,EAAC,CAAC;AAAA,IAAG,SAASR,IAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAGA,EAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAKG,IAAG;AAAE,IAAAA,GAAE,iBAAiB,UAAU,QAAQ,QAAQA,GAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAGA,EAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAOE,IAAGG,IAAG;AAAE,QAAIH,GAAEG,EAAC,GAAG,EAAE,MAAM,GAAG,EAAE;AAAQ,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACrF;AAEO,SAAS,iBAAiBC,IAAG;AAChC,MAAI,GAAGR;AACP,SAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,SAAS,SAAUD,IAAG;AAAE,UAAMA;AAAA,EAAG,CAAC,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AAC1I,WAAS,KAAKO,IAAGF,IAAG;AAAE,MAAEE,EAAC,IAAIE,GAAEF,EAAC,IAAI,SAAUC,IAAG;AAAE,cAAQP,KAAI,CAACA,MAAK,EAAE,OAAO,QAAQQ,GAAEF,EAAC,EAAEC,EAAC,CAAC,GAAG,MAAMD,OAAM,SAAS,IAAIF,KAAIA,GAAEG,EAAC,IAAIA;AAAA,IAAG,IAAIH;AAAA,EAAG;AAClJ;AAEO,SAAS,cAAcI,IAAG;AAC7B,MAAI,CAAC,OAAO;AAAe,UAAM,IAAI,UAAU,sCAAsC;AACrF,MAAIC,KAAID,GAAE,OAAO,aAAa,GAAG;AACjC,SAAOC,KAAIA,GAAE,KAAKD,EAAC,KAAKA,KAAI,OAAO,aAAa,aAAa,SAASA,EAAC,IAAIA,GAAE,OAAO,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AAC9M,WAAS,KAAKF,IAAG;AAAE,MAAEA,EAAC,IAAIE,GAAEF,EAAC,KAAK,SAAUC,IAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,QAAAA,KAAIC,GAAEF,EAAC,EAAEC,EAAC,GAAG,OAAO,SAAS,QAAQA,GAAE,MAAMA,GAAE,KAAK;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AAC/J,WAAS,OAAO,SAAS,QAAQT,IAAGS,IAAG;AAAE,YAAQ,QAAQA,EAAC,EAAE,KAAK,SAASA,IAAG;AAAE,cAAQ,EAAE,OAAOA,IAAG,MAAMT,GAAE,CAAC;AAAA,IAAG,GAAG,MAAM;AAAA,EAAG;AAC/H;AAEO,SAAS,qBAAqB,QAAQ,KAAK;AAC9C,MAAI,OAAO,gBAAgB;AAAE,WAAO,eAAe,QAAQ,OAAO,EAAE,OAAO,IAAI,CAAC;AAAA,EAAG,OAAO;AAAE,WAAO,MAAM;AAAA,EAAK;AAC9G,SAAO;AACX;AAEO,SAAS,aAAa,KAAK;AAC9B,MAAI,OAAO,IAAI;AAAY,WAAO;AAClC,MAAI,SAAS,CAAC;AACd,MAAI,OAAO;AAAM,aAAS,KAAK;AAAK,UAAI,OAAO,eAAe,KAAK,KAAK,CAAC;AAAG,eAAO,CAAC,IAAI,IAAI,CAAC;AAAA;AAC7F,SAAO,UAAU;AACjB,SAAO;AACX;AAEO,SAAS,gBAAgB,KAAK;AACjC,SAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,SAAS,IAAI;AAC1D;AAEO,SAAS,uBAAuB,UAAU,YAAY;AACzD,MAAI,CAAC,WAAW,IAAI,QAAQ,GAAG;AAC3B,UAAM,IAAI,UAAU,gDAAgD;AAAA,EACxE;AACA,SAAO,WAAW,IAAI,QAAQ;AAClC;AAEO,SAAS,uBAAuB,UAAU,YAAY,OAAO;AAChE,MAAI,CAAC,WAAW,IAAI,QAAQ,GAAG;AAC3B,UAAM,IAAI,UAAU,gDAAgD;AAAA,EACxE;AACA,aAAW,IAAI,UAAU,KAAK;AAC9B,SAAO;AACX;AAzNA,IAgBI,eAaO;AA7BX;AAAA;AAgBA,IAAI,gBAAgB,SAASA,IAAG,GAAG;AAC/B,sBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUA,IAAGa,IAAG;AAAE,QAAAb,GAAE,YAAYa;AAAA,MAAG,KAC1E,SAAUb,IAAGa,IAAG;AAAE,iBAASX,MAAKW;AAAG,cAAIA,GAAE,eAAeX,EAAC;AAAG,YAAAF,GAAEE,EAAC,IAAIW,GAAEX,EAAC;AAAA,MAAG;AAC7E,aAAO,cAAcF,IAAG,CAAC;AAAA,IAC7B;AAQO,IAAI,WAAW,WAAW;AAC7B,iBAAW,OAAO,UAAU,SAASc,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAGN,KAAI,UAAU,QAAQ,IAAIA,IAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAASN,MAAK;AAAG,gBAAI,OAAO,UAAU,eAAe,KAAK,GAAGA,EAAC;AAAG,gBAAEA,EAAC,IAAI,EAAEA,EAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAAA;AAAA;;;;;;;;ACtCA,aAAgB,kBAAe;AAE7B,cAAO,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,YAAU,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,aAAY,CAAA;IAC/C;AAHA,YAAA,kBAAA;AAKA,aAAgB,kBAAe;AAC7B,YAAM,gBAAgB,gBAAe;AAErC,aAAO,cAAc,UAAU,cAAc;IAC/C;AAJA,YAAA,kBAAA;AAMA,aAAgB,2BAAwB;AACtC,aAAO,CAAC,CAAC,gBAAe,KAAM,CAAC,CAAC,gBAAe;IACjD;AAFA,YAAA,2BAAA;;;;;;;;;;ACXA,aAAgB,gBAAa;AAC3B,aACE,OAAO,aAAa,eACpB,OAAO,cAAc,eACrB,UAAU,YAAY;IAE1B;AANA,YAAA,gBAAA;AAQA,aAAgBa,UAAM;AACpB,aACE,OAAO,YAAY,eACnB,OAAO,QAAQ,aAAa,eAC5B,OAAO,QAAQ,SAAS,SAAS;IAErC;AANA,YAAA,SAAAA;AAQA,aAAgB,YAAS;AACvB,aAAO,CAAC,cAAa,KAAM,CAACA,QAAM;IACpC;AAFA,YAAA,YAAA;;;;;;;;;;AChBA,YAAA,aAAA,kBAAA,OAAA;AACA,YAAA,aAAA,eAAA,OAAA;;;;;ACDA;;;;wBAGa;AAHb;;yBAAuB;AAKvB,4BAAc;AAFP,IAAM,WAAW;;;;;ACClB,SAAU,UAAU,UAAU,GAAC;AACnC,QAAM,OAAO,KAAK,IAAG,IAAK,KAAK,IAAI,IAAI,OAAO;AAC9C,QAAM,QAAQ,KAAK,MAAM,KAAK,OAAM,IAAK,KAAK,IAAI,IAAI,OAAO,CAAC;AAC9D,SAAO,OAAO;AAChB;AAEM,SAAU,eAAe,UAAU,GAAC;AACxC,SAAO,OAAO,UAAU,OAAO,CAAC;AAClC;AAEM,SAAU,qBACd,QACA,QACA,IAAW;AAEX,SAAO;IACL,IAAI,MAAM,UAAS;IACnB,SAAS;IACT;IACA;;AAEJ;AAEM,SAAU,oBAA6B,IAAY,QAAS;AAChE,SAAO;IACL;IACA,SAAS;IACT;;AAEJ;AAEM,SAAU,mBACd,IACA,OACA,MAAa;AAEb,SAAO;IACL;IACA,SAAS;IACT,OAAO,mBAAmB,OAAO,IAAI;;AAEzC;AAEM,SAAU,mBAAmB,OAAgC,MAAa;AAC9E,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO,SAAS,cAAc;;AAEhC,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GACA,SAAS,YAAY,CAAC,GAAA,EACzB,SAAS,MAAK,CAAA;;AAGlB,MAAI,OAAO,SAAS,aAAa;AAC/B,UAAM,OAAO;;AAEf,MAAI,oBAAoB,MAAM,IAAI,GAAG;AACnC,YAAQ,eAAe,MAAM,IAAI;;AAEnC,SAAO;AACT;AAhEA;;;AACA;;;;;ACDM,SAAU,aAAa,OAAa;AACxC,MAAI,MAAM,SAAS,GAAG,GAAG;AACvB,WAAO,qBAAqB,KAAK;;AAEnC,MAAI,MAAM,KAAK,KAAK,GAAG;AACrB,WAAO;;AAET,SAAO;AACT;AAEM,SAAU,oBAAoB,OAAa;AAC/C,SAAO,UAAU;AACnB;AAEM,SAAU,qBAAqB,OAAa;AAChD,MAAI,oBAAoB,KAAK,GAAG;AAC9B,WAAO;;AAET,MAAI,CAAC,MAAM,SAAS,GAAG,GAAG;AACxB,WAAO;;AAET,MAAI,MAAM,MAAM,GAAG,EAAE,WAAW,GAAG;AACjC,WAAO;;AAET,MAAI,MAAM,MAAM,GAAG,EAAE,OAAO,OAAK,EAAE,KAAI,MAAO,EAAE,EAAE,WAAW,GAAG;AAC9D,WAAO;;AAET,SAAO;AACT;AAEM,SAAU,4BAA4B,OAAa;AACvD,SAAO,CAAC,oBAAoB,KAAK,KAAK,qBAAqB,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,KAAI;AAChG;AAEM,SAAU,6BAA6B,OAAa;AACxD,SAAO,CAAC,oBAAoB,KAAK,KAAK,qBAAqB,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,KAAI;AAChG;AApCA;;;;;;ACEO,IAAe,GAAQ,GAAA,GAAA;AAAvB;;IAAe,IAAf,MAAe;IAAA;AAAA,IAAQ,IAAR,cAAQ,EAAA;MAAA,YAAAC,IAAA;AAAA,cAAA;MAAA;IAAA;AAAA,IAAA,IAAA,cAAA,EAAA;MAAA,cAAA;AAAA,cAAA;MAAA;IAAA;AAAA,IAAA,IAAA,cAAA,EAAA;MAAA,YAAAA,IAAA;AAAA,cAAA;MAAA;IAAA;;;;;AEF9B;;;;;;;ACIA,SAAS,eAAe,KAAW;AACjC,QAAM,UAAU,IAAI,MAAM,IAAI,OAAO,SAAS,IAAI,CAAC;AACnD,MAAI,CAAC,WAAW,CAAC,QAAQ;AAAQ;AACjC,SAAO,QAAQ,CAAC;AAClB;AAEA,SAAS,mBAAmB,KAAa,OAAa;AACpD,QAAM,WAAW,eAAe,GAAG;AACnC,MAAI,OAAO,aAAa;AAAa,WAAO;AAC5C,SAAO,IAAI,OAAO,KAAK,EAAE,KAAK,QAAQ;AACxC;AAEM,SAAU,UAAU,KAAW;AACnC,SAAO,mBAAmB,KAAK,UAAU;AAC3C;AAEM,SAAU,QAAQ,KAAW;AACjC,SAAO,mBAAmB,KAAK,QAAQ;AACzC;AAEM,SAAU,eAAe,KAAW;AACxC,SAAO,IAAI,OAAO,4BAA4B,EAAE,KAAK,GAAG;AAC1D;AA1BA,IAAM,YAEA;AAFN;;IAAM,aAAa;AAEnB,IAAM,WAAW;;;;;ACQX,SAAU,iBAAiB,SAAY;AAC3C,SACE,OAAO,YAAY,YACnB,QAAQ,WACR,aAAa,WACb,QAAQ,YAAY;AAExB;AAEM,SAAU,iBAA0B,SAAuB;AAC/D,SAAO,iBAAiB,OAAO,KAAK,YAAY;AAClD;AAEM,SAAU,kBAA2B,SAAuB;AAChE,SAAO,iBAAiB,OAAO,MAAM,gBAAgB,OAAO,KAAK,eAAe,OAAO;AACzF;AAEM,SAAU,gBAAyB,SAAuB;AAC9D,SAAO,YAAY;AACrB;AAEM,SAAU,eAAe,SAAuB;AACpD,SAAO,WAAW;AACpB;AAEM,SAAU,2BACd,YAA6B;AAE7B,SAAO,WAAW,cAAc,WAAW,UAAU;AACvD;AA7BA;;;;;;ACVA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAAC,YAAA;;;AACA;AACA;4BAAc;AACd;AACA;AACA;AACA;AACA;;;", "names": ["globalThis", "exports", "g", "e", "e", "d", "e", "p", "c", "r", "P", "f", "g", "n", "v", "o", "m", "j", "b", "__assign", "isNode", "c", "init_esm"]}