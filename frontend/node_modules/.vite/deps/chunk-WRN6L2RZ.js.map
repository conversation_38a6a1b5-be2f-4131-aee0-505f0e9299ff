{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../use-sync-external-store/shim/with-selector.js", "../../@tanstack/query-sync-storage-persister/src/index.ts", "../../@tanstack/query-persist-client-core/src/persist.ts", "../../wagmi/dist/index.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "import type {\n  PersistRetryer,\n  PersistedClient,\n  Persister,\n} from '@tanstack/query-persist-client-core'\n\ninterface Storage {\n  getItem: (key: string) => string | null\n  setItem: (key: string, value: string) => void\n  removeItem: (key: string) => void\n}\n\ninterface CreateSyncStoragePersisterOptions {\n  /** The storage client used for setting and retrieving items from cache.\n   * For SSR pass in `undefined`. Note that window.localStorage can be\n   * `null` in Android WebViews depending on how they are configured.\n   */\n  storage: Storage | undefined | null\n  /** The key to use when storing the cache */\n  key?: string\n  /** To avoid spamming,\n   * pass a time in ms to throttle saving the cache to disk */\n  throttleTime?: number\n  /**\n   * How to serialize the data to storage.\n   * @default `JSON.stringify`\n   */\n  serialize?: (client: PersistedClient) => string\n  /**\n   * How to deserialize the data from storage.\n   * @default `JSON.parse`\n   */\n  deserialize?: (cachedString: string) => PersistedClient\n\n  retry?: PersistRetryer\n}\n\nexport function createSyncStoragePersister({\n  storage,\n  key = `REACT_QUERY_OFFLINE_CACHE`,\n  throttleTime = 1000,\n  serialize = JSON.stringify,\n  deserialize = JSON.parse,\n  retry,\n}: CreateSyncStoragePersisterOptions): Persister {\n  if (storage) {\n    const trySave = (persistedClient: PersistedClient): Error | undefined => {\n      try {\n        storage.setItem(key, serialize(persistedClient))\n        return\n      } catch (error) {\n        return error as Error\n      }\n    }\n    return {\n      persistClient: throttle((persistedClient) => {\n        let client: PersistedClient | undefined = persistedClient\n        let error = trySave(client)\n        let errorCount = 0\n        while (error && client) {\n          errorCount++\n          client = retry?.({\n            persistedClient: client,\n            error,\n            errorCount,\n          })\n\n          if (client) {\n            error = trySave(client)\n          }\n        }\n      }, throttleTime),\n      restoreClient: () => {\n        const cacheString = storage.getItem(key)\n\n        if (!cacheString) {\n          return\n        }\n\n        return deserialize(cacheString) as PersistedClient\n      },\n      removeClient: () => {\n        storage.removeItem(key)\n      },\n    }\n  }\n\n  return {\n    persistClient: noop,\n    restoreClient: () => undefined,\n    removeClient: noop,\n  }\n}\n\nfunction throttle<TArgs extends any[]>(\n  func: (...args: TArgs) => any,\n  wait = 100,\n) {\n  let timer: ReturnType<typeof setTimeout> | null = null\n  let params: TArgs\n  return function (...args: TArgs) {\n    params = args\n    if (timer === null) {\n      timer = setTimeout(() => {\n        func(...params)\n        timer = null\n      }, wait)\n    }\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n", "import { dehydrate, hydrate } from '@tanstack/query-core'\nimport type {\n  DehydrateOptions,\n  DehydratedState,\n  HydrateOptions,\n  QueryClient,\n} from '@tanstack/query-core'\nimport type { NotifyEventType } from '@tanstack/query-core'\n\nexport type Promisable<T> = T | PromiseLike<T>\n\nexport interface Persister {\n  persistClient(persistClient: PersistedClient): Promisable<void>\n  restoreClient(): Promisable<PersistedClient | undefined>\n  removeClient(): Promisable<void>\n}\n\nexport interface PersistedClient {\n  timestamp: number\n  buster: string\n  clientState: DehydratedState\n}\n\nexport interface PersistQueryClienRootOptions {\n  /** The QueryClient to persist */\n  queryClient: QueryClient\n  /** The Persister interface for storing and restoring the cache\n   * to/from a persisted location */\n  persister: Persister\n  /** A unique string that can be used to forcefully\n   * invalidate existing caches if they do not share the same buster string */\n  buster?: string\n}\n\nexport interface PersistedQueryClientRestoreOptions\n  extends PersistQueryClienRootOptions {\n  /** The max-allowed age of the cache in milliseconds.\n   * If a persisted cache is found that is older than this\n   * time, it will be discarded */\n  maxAge?: number\n  /** The options passed to the hydrate function */\n  hydrateOptions?: HydrateOptions\n}\n\nexport interface PersistedQueryClientSaveOptions\n  extends PersistQueryClienRootOptions {\n  /** The options passed to the dehydrate function */\n  dehydrateOptions?: DehydrateOptions\n}\n\nexport interface PersistQueryClientOptions\n  extends PersistedQueryClientRestoreOptions,\n    PersistedQueryClientSaveOptions,\n    PersistQueryClienRootOptions {}\n\n/**\n * Checks if emitted event is about cache change and not about observers.\n * Useful for persist, where we only want to trigger save when cache is changed.\n */\nconst cacheableEventTypes: Array<NotifyEventType> = [\n  'added',\n  'removed',\n  'updated',\n]\n\nfunction isCacheableEventType(eventType: NotifyEventType) {\n  return cacheableEventTypes.includes(eventType)\n}\n\n/**\n * Restores persisted data to the QueryCache\n *  - data obtained from persister.restoreClient\n *  - data is hydrated using hydrateOptions\n * If data is expired, busted, empty, or throws, it runs persister.removeClient\n */\nexport async function persistQueryClientRestore({\n  queryClient,\n  persister,\n  maxAge = 1000 * 60 * 60 * 24,\n  buster = '',\n  hydrateOptions,\n}: PersistedQueryClientRestoreOptions) {\n  try {\n    const persistedClient = await persister.restoreClient()\n\n    if (persistedClient) {\n      if (persistedClient.timestamp) {\n        const expired = Date.now() - persistedClient.timestamp > maxAge\n        const busted = persistedClient.buster !== buster\n        if (expired || busted) {\n          persister.removeClient()\n        } else {\n          hydrate(queryClient, persistedClient.clientState, hydrateOptions)\n        }\n      } else {\n        persister.removeClient()\n      }\n    }\n  } catch (err) {\n    if (process.env.NODE_ENV !== 'production') {\n      queryClient.getLogger().error(err)\n      queryClient\n        .getLogger()\n        .warn(\n          'Encountered an error attempting to restore client cache from persisted location. As a precaution, the persisted cache will be discarded.',\n        )\n    }\n    persister.removeClient()\n  }\n}\n\n/**\n * Persists data from the QueryCache\n *  - data dehydrated using dehydrateOptions\n *  - data is persisted using persister.persistClient\n */\nexport async function persistQueryClientSave({\n  queryClient,\n  persister,\n  buster = '',\n  dehydrateOptions,\n}: PersistedQueryClientSaveOptions) {\n  const persistClient: PersistedClient = {\n    buster,\n    timestamp: Date.now(),\n    clientState: dehydrate(queryClient, dehydrateOptions),\n  }\n\n  await persister.persistClient(persistClient)\n}\n\n/**\n * Subscribe to QueryCache and MutationCache updates (for persisting)\n * @returns an unsubscribe function (to discontinue monitoring)\n */\nexport function persistQueryClientSubscribe(\n  props: PersistedQueryClientSaveOptions,\n) {\n  const unsubscribeQueryCache = props.queryClient\n    .getQueryCache()\n    .subscribe((event) => {\n      if (isCacheableEventType(event.type)) {\n        persistQueryClientSave(props)\n      }\n    })\n\n  const unusbscribeMutationCache = props.queryClient\n    .getMutationCache()\n    .subscribe((event) => {\n      if (isCacheableEventType(event.type)) {\n        persistQueryClientSave(props)\n      }\n    })\n\n  return () => {\n    unsubscribeQueryCache()\n    unusbscribeMutationCache()\n  }\n}\n\n/**\n * Restores persisted data to QueryCache and persists further changes.\n */\nexport function persistQueryClient(\n  props: PersistQueryClientOptions,\n): [() => void, Promise<void>] {\n  let hasUnsubscribed = false\n  let persistQueryClientUnsubscribe: (() => void) | undefined\n  const unsubscribe = () => {\n    hasUnsubscribed = true\n    persistQueryClientUnsubscribe?.()\n  }\n\n  // Attempt restore\n  const restorePromise = persistQueryClientRestore(props).then(() => {\n    if (!hasUnsubscribed) {\n      // Subscribe to changes in the query cache to trigger the save\n      persistQueryClientUnsubscribe = persistQueryClientSubscribe(props)\n    }\n  })\n\n  return [unsubscribe, restorePromise]\n}\n", "\"use client\";\n\n// src/config.ts\nimport { createSyncStoragePersister } from \"@tanstack/query-sync-storage-persister\";\nimport { QueryClient } from \"@tanstack/react-query\";\nimport { persistQueryClient } from \"@tanstack/react-query-persist-client\";\nimport {\n  createConfig as createCoreConfig,\n  createStorage,\n  noopStorage\n} from \"@wagmi/core\";\nfunction createConfig({\n  queryClient = new QueryClient({\n    defaultOptions: {\n      queries: {\n        cacheTime: 1e3 * 60 * 60 * 24,\n        networkMode: \"offlineFirst\",\n        refetchOnWindowFocus: false,\n        retry: 0\n      },\n      mutations: {\n        networkMode: \"offlineFirst\"\n      }\n    }\n  }),\n  storage = createStorage({\n    storage: typeof window !== \"undefined\" && window.localStorage ? window.localStorage : noopStorage\n  }),\n  persister = typeof window !== \"undefined\" ? createSyncStoragePersister({\n    key: \"cache\",\n    storage,\n    serialize: (x) => x,\n    deserialize: (x) => x\n  }) : void 0,\n  ...args\n}) {\n  const config = createCoreConfig({\n    ...args,\n    storage\n  });\n  if (persister)\n    persistQueryClient({\n      queryClient,\n      persister,\n      dehydrateOptions: {\n        shouldDehydrateQuery: (query) => query.cacheTime !== 0 && query.queryKey[0].persist !== false\n      }\n    });\n  return Object.assign(config, { queryClient });\n}\n\n// src/context.ts\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport * as React from \"react\";\nvar Context = React.createContext(void 0);\nvar queryClientContext = React.createContext(\n  void 0\n);\nfunction WagmiConfig({\n  children,\n  config\n}) {\n  return React.createElement(Context.Provider, {\n    children: React.createElement(QueryClientProvider, {\n      children,\n      client: config.queryClient,\n      context: queryClientContext\n    }),\n    value: config\n  });\n}\nfunction useConfig() {\n  const config = React.useContext(Context);\n  if (!config)\n    throw new Error(\n      [\n        \"`useConfig` must be used within `WagmiConfig`.\\n\",\n        \"Read more: https://wagmi.sh/react/WagmiConfig\"\n      ].join(\"\\n\")\n    );\n  return config;\n}\n\n// src/hooks/accounts/useAccount.ts\nimport { getAccount, watchAccount } from \"@wagmi/core\";\nimport * as React9 from \"react\";\n\n// src/hooks/utils/query/useBaseQuery.ts\nimport {\n  notifyManager,\n  useIsRestoring,\n  useQueryClient,\n  useQueryErrorResetBoundary\n} from \"@tanstack/react-query\";\nimport * as React2 from \"react\";\n\n// src/hooks/utils/useSyncExternalStore.ts\nimport * as pkg from \"use-sync-external-store/shim/index.js\";\nvar useSyncExternalStore2 = pkg.useSyncExternalStore;\n\n// src/hooks/utils/query/utils.ts\nfunction isQueryKey(value) {\n  return Array.isArray(value);\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (typeof ctor === \"undefined\") {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQueryKey(arg1)) {\n    return arg1;\n  }\n  if (typeof arg2 === \"function\") {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 };\n  }\n  return { ...arg2, queryKey: arg1 };\n}\nfunction queryKeyHashFn(queryKey17) {\n  return JSON.stringify(\n    queryKey17,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : typeof val === \"bigint\" ? val.toString() : val\n  );\n}\nfunction shouldThrowError(_useErrorBoundary, params) {\n  if (typeof _useErrorBoundary === \"function\") {\n    return _useErrorBoundary(...params);\n  }\n  return !!_useErrorBoundary;\n}\nfunction trackResult(result, observer) {\n  const trackedResult = {};\n  Object.keys(result).forEach((key) => {\n    Object.defineProperty(trackedResult, key, {\n      configurable: false,\n      enumerable: true,\n      get: () => {\n        observer.trackedProps.add(key);\n        return result[key];\n      }\n    });\n  });\n  return trackedResult;\n}\n\n// src/hooks/utils/query/useBaseQuery.ts\nfunction useBaseQuery(options, Observer) {\n  const queryClient = useQueryClient({ context: options.context });\n  const isRestoring = useIsRestoring();\n  const errorResetBoundary = useQueryErrorResetBoundary();\n  const defaultedOptions = queryClient.defaultQueryOptions({\n    ...options,\n    queryKeyHashFn\n  });\n  defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(\n      defaultedOptions.onError\n    );\n  }\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(\n      defaultedOptions.onSuccess\n    );\n  }\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(\n      defaultedOptions.onSettled\n    );\n  }\n  if (defaultedOptions.suspense) {\n    if (typeof defaultedOptions.staleTime !== \"number\") {\n      defaultedOptions.staleTime = 1e3;\n    }\n  }\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false;\n    }\n  }\n  const [observer] = React2.useState(\n    () => new Observer(\n      queryClient,\n      defaultedOptions\n    )\n  );\n  const result = observer.getOptimisticResult(defaultedOptions);\n  useSyncExternalStore2(\n    React2.useCallback(\n      (onStoreChange) => isRestoring ? () => void 0 : observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer, isRestoring]\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult()\n  );\n  React2.useEffect(() => {\n    errorResetBoundary.clearReset();\n  }, [errorResetBoundary]);\n  React2.useEffect(() => {\n    observer.setOptions(defaultedOptions, { listeners: false });\n  }, [defaultedOptions, observer]);\n  if (defaultedOptions.suspense && result.isLoading && result.isFetching && !isRestoring) {\n    throw observer.fetchOptimistic(defaultedOptions).then(({ data }) => {\n      defaultedOptions.onSuccess?.(data);\n      defaultedOptions.onSettled?.(data, null);\n    }).catch((error) => {\n      errorResetBoundary.clearReset();\n      defaultedOptions.onError?.(error);\n      defaultedOptions.onSettled?.(void 0, error);\n    });\n  }\n  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && shouldThrowError(defaultedOptions.useErrorBoundary, [\n    result.error,\n    observer.getCurrentQuery()\n  ])) {\n    throw result.error;\n  }\n  const status = result.status === \"loading\" && result.fetchStatus === \"idle\" ? \"idle\" : result.status;\n  const isIdle = status === \"idle\";\n  const isLoading = status === \"loading\" && result.fetchStatus === \"fetching\";\n  return {\n    ...result,\n    defaultedOptions,\n    isIdle,\n    isLoading,\n    observer,\n    status\n  };\n}\n\n// src/hooks/utils/query/useInfiniteQuery.ts\nimport { InfiniteQueryObserver } from \"@tanstack/react-query\";\nfunction useInfiniteQuery(arg1, arg2, arg3) {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n  const baseQuery = useBaseQuery(\n    { context: queryClientContext, ...parsedOptions },\n    InfiniteQueryObserver\n  );\n  const result = {\n    data: baseQuery.data,\n    error: baseQuery.error,\n    fetchNextPage: baseQuery.fetchNextPage,\n    fetchStatus: baseQuery.fetchStatus,\n    hasNextPage: baseQuery.hasNextPage,\n    isError: baseQuery.isError,\n    isFetched: baseQuery.isFetched,\n    isFetchedAfterMount: baseQuery.isFetchedAfterMount,\n    isFetching: baseQuery.isFetching,\n    isFetchingNextPage: baseQuery.isFetchingNextPage,\n    isIdle: baseQuery.isIdle,\n    isLoading: baseQuery.isLoading,\n    isRefetching: baseQuery.isRefetching,\n    isSuccess: baseQuery.isSuccess,\n    refetch: baseQuery.refetch,\n    status: baseQuery.status,\n    internal: {\n      dataUpdatedAt: baseQuery.dataUpdatedAt,\n      errorUpdatedAt: baseQuery.errorUpdatedAt,\n      failureCount: baseQuery.failureCount,\n      isFetchedAfterMount: baseQuery.isFetchedAfterMount,\n      isLoadingError: baseQuery.isLoadingError,\n      isPaused: baseQuery.isPaused,\n      isPlaceholderData: baseQuery.isPlaceholderData,\n      isPreviousData: baseQuery.isPreviousData,\n      isRefetchError: baseQuery.isRefetchError,\n      isStale: baseQuery.isStale,\n      remove: baseQuery.remove\n    }\n  };\n  return !baseQuery.defaultedOptions.notifyOnChangeProps ? trackResult(result, baseQuery.observer) : result;\n}\n\n// src/hooks/utils/query/useMutation.ts\nimport {\n  parseMutationArgs,\n  useMutation as useMutation_\n} from \"@tanstack/react-query\";\nfunction useMutation(arg1, arg2, arg3) {\n  const options = parseMutationArgs(arg1, arg2, arg3);\n  return useMutation_({ context: queryClientContext, ...options });\n}\n\n// src/hooks/utils/query/useQuery.ts\nimport { QueryObserver } from \"@tanstack/react-query\";\nfunction useQuery(arg1, arg2, arg3) {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n  const baseQuery = useBaseQuery({ context: queryClientContext, ...parsedOptions }, QueryObserver);\n  const result = {\n    data: baseQuery.data,\n    error: baseQuery.error,\n    fetchStatus: baseQuery.fetchStatus,\n    isError: baseQuery.isError,\n    isFetched: baseQuery.isFetched,\n    isFetchedAfterMount: baseQuery.isFetchedAfterMount,\n    isFetching: baseQuery.isFetching,\n    isIdle: baseQuery.isIdle,\n    isLoading: baseQuery.isLoading,\n    isRefetching: baseQuery.isRefetching,\n    isSuccess: baseQuery.isSuccess,\n    refetch: baseQuery.refetch,\n    status: baseQuery.status,\n    internal: {\n      dataUpdatedAt: baseQuery.dataUpdatedAt,\n      errorUpdatedAt: baseQuery.errorUpdatedAt,\n      failureCount: baseQuery.failureCount,\n      isFetchedAfterMount: baseQuery.isFetchedAfterMount,\n      isLoadingError: baseQuery.isLoadingError,\n      isPaused: baseQuery.isPaused,\n      isPlaceholderData: baseQuery.isPlaceholderData,\n      isPreviousData: baseQuery.isPreviousData,\n      isRefetchError: baseQuery.isRefetchError,\n      isStale: baseQuery.isStale,\n      remove: baseQuery.remove\n    }\n  };\n  return !baseQuery.defaultedOptions.notifyOnChangeProps ? trackResult(result, baseQuery.observer) : result;\n}\n\n// src/hooks/utils/query/useQueryClient.ts\nimport { useQueryClient as useQueryClient_ } from \"@tanstack/react-query\";\nvar useQueryClient2 = () => useQueryClient_({ context: queryClientContext });\n\n// src/hooks/viem/usePublicClient.ts\nimport { getPublicClient, watchPublicClient } from \"@wagmi/core\";\nimport { useSyncExternalStoreWithSelector } from \"use-sync-external-store/shim/with-selector.js\";\nfunction usePublicClient({\n  chainId\n} = {}) {\n  return useSyncExternalStoreWithSelector(\n    (cb) => watchPublicClient({ chainId }, cb),\n    () => getPublicClient({ chainId }),\n    () => getPublicClient({ chainId }),\n    (x) => x,\n    (a, b) => a.uid === b.uid\n  );\n}\n\n// src/hooks/viem/useWalletClient.ts\nimport { getWalletClient, watchWalletClient } from \"@wagmi/core\";\nimport * as React3 from \"react\";\nfunction queryKey({ chainId }) {\n  return [{ entity: \"walletClient\", chainId, persist: false }];\n}\nfunction queryFn({\n  queryKey: [{ chainId }]\n}) {\n  return getWalletClient({ chainId });\n}\nfunction useWalletClient({\n  chainId: chainId_,\n  suspense,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const { connector } = useAccount();\n  const chainId = useChainId({ chainId: chainId_ });\n  const walletClientQuery = useQuery(queryKey({ chainId }), queryFn, {\n    cacheTime: 0,\n    enabled: Boolean(connector),\n    staleTime: Infinity,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n  const queryClient = useQueryClient2();\n  React3.useEffect(() => {\n    const unwatch = watchWalletClient({ chainId }, (walletClient) => {\n      if (walletClient)\n        queryClient.invalidateQueries(queryKey({ chainId }));\n      else\n        queryClient.removeQueries(queryKey({ chainId }));\n    });\n    return unwatch;\n  }, [queryClient, chainId]);\n  return walletClientQuery;\n}\n\n// src/hooks/viem/useWebSocketPublicClient.ts\nimport {\n  getWebSocketPublicClient,\n  watchWebSocketPublicClient\n} from \"@wagmi/core\";\nimport { useSyncExternalStoreWithSelector as useSyncExternalStoreWithSelector2 } from \"use-sync-external-store/shim/with-selector.js\";\nfunction useWebSocketPublicClient({ chainId } = {}) {\n  return useSyncExternalStoreWithSelector2(\n    (cb) => watchWebSocketPublicClient({ chainId }, cb),\n    () => getWebSocketPublicClient({ chainId }),\n    () => getWebSocketPublicClient({ chainId }),\n    (x) => x,\n    (a, b) => a?.uid === b?.uid\n  );\n}\n\n// src/hooks/utils/useChainId.ts\nfunction useChainId({ chainId } = {}) {\n  const publicClient = usePublicClient({ chainId });\n  return publicClient.chain.id;\n}\n\n// src/hooks/utils/useForceUpdate.ts\nimport * as React4 from \"react\";\nfunction useForceUpdate() {\n  const [, forceUpdate] = React4.useReducer((x) => x + 1, 0);\n  return forceUpdate;\n}\n\n// src/hooks/utils/useInvalidateOnBlock.ts\nimport * as React7 from \"react\";\n\n// src/hooks/network-status/useBlockNumber.ts\nimport { fetchBlockNumber } from \"@wagmi/core\";\nimport * as React5 from \"react\";\nfunction queryKey2({ chainId, scopeKey }) {\n  return [{ entity: \"blockNumber\", chainId, scopeKey }];\n}\nfunction queryFn2({\n  queryKey: [{ chainId }]\n}) {\n  return fetchBlockNumber({ chainId });\n}\nfunction useBlockNumber({\n  cacheTime = 0,\n  chainId: chainId_,\n  enabled = true,\n  scopeKey,\n  staleTime,\n  suspense,\n  watch = false,\n  onBlock,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  const publicClient = usePublicClient({ chainId });\n  const webSocketPublicClient = useWebSocketPublicClient({ chainId });\n  const queryClient = useQueryClient2();\n  React5.useEffect(() => {\n    if (!enabled)\n      return;\n    if (!watch && !onBlock)\n      return;\n    const publicClient_ = webSocketPublicClient ?? publicClient;\n    const unwatch = publicClient_.watchBlockNumber({\n      onBlockNumber: (blockNumber) => {\n        if (watch)\n          queryClient.setQueryData(queryKey2({ chainId, scopeKey }), blockNumber);\n        if (onBlock)\n          onBlock(blockNumber);\n      },\n      emitOnBegin: true\n    });\n    return unwatch;\n  }, [\n    chainId,\n    scopeKey,\n    onBlock,\n    publicClient,\n    queryClient,\n    watch,\n    webSocketPublicClient,\n    enabled\n  ]);\n  return useQuery(queryKey2({ scopeKey, chainId }), queryFn2, {\n    cacheTime,\n    enabled,\n    staleTime,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n}\n\n// src/hooks/network-status/useFeeData.ts\nimport { fetchFeeData } from \"@wagmi/core\";\nimport * as React6 from \"react\";\nfunction queryKey3({\n  chainId,\n  formatUnits,\n  scopeKey\n}) {\n  return [{ entity: \"feeData\", chainId, formatUnits, scopeKey }];\n}\nfunction queryFn3({\n  queryKey: [{ chainId, formatUnits }]\n}) {\n  return fetchFeeData({ chainId, formatUnits });\n}\nfunction useFeeData({\n  cacheTime,\n  chainId: chainId_,\n  enabled = true,\n  formatUnits = \"gwei\",\n  scopeKey,\n  staleTime,\n  suspense,\n  watch,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  const queryKey_ = React6.useMemo(\n    () => queryKey3({\n      chainId,\n      formatUnits,\n      scopeKey\n    }),\n    [chainId, formatUnits, scopeKey]\n  );\n  const feeDataQuery = useQuery(queryKey_, queryFn3, {\n    cacheTime,\n    enabled,\n    staleTime,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n  useInvalidateOnBlock({\n    chainId,\n    enabled: Boolean(enabled && watch),\n    queryKey: queryKey_\n  });\n  return feeDataQuery;\n}\n\n// src/hooks/utils/useInvalidateOnBlock.ts\nfunction useInvalidateOnBlock({\n  chainId,\n  enabled,\n  queryKey: queryKey17\n}) {\n  const queryClient = useQueryClient2();\n  const onBlock = React7.useCallback(\n    () => queryClient.invalidateQueries({ queryKey: queryKey17 }, { cancelRefetch: false }),\n    [queryClient, queryKey17]\n  );\n  useBlockNumber({\n    chainId,\n    enabled,\n    onBlock: enabled ? onBlock : void 0,\n    scopeKey: enabled ? void 0 : \"idle\"\n  });\n}\n\n// src/hooks/utils/useSyncExternalStoreWithTracked.ts\nimport { deepEqual } from \"@wagmi/core\";\nimport * as React8 from \"react\";\nimport { useSyncExternalStoreWithSelector as useSyncExternalStoreWithSelector3 } from \"use-sync-external-store/shim/with-selector.js\";\nvar isPlainObject2 = (obj) => typeof obj === \"object\" && !Array.isArray(obj);\nfunction useSyncExternalStoreWithTracked(subscribe, getSnapshot, getServerSnapshot = getSnapshot, isEqual = deepEqual) {\n  const trackedKeys = React8.useRef([]);\n  const result = useSyncExternalStoreWithSelector3(\n    subscribe,\n    getSnapshot,\n    getServerSnapshot,\n    (x) => x,\n    (a, b) => {\n      if (isPlainObject2(a) && isPlainObject2(b) && trackedKeys.current.length) {\n        for (const key of trackedKeys.current) {\n          const equal = isEqual(\n            a[key],\n            b[key]\n          );\n          if (!equal)\n            return false;\n        }\n        return true;\n      }\n      return isEqual(a, b);\n    }\n  );\n  if (isPlainObject2(result)) {\n    const trackedResult = { ...result };\n    Object.defineProperties(\n      trackedResult,\n      Object.entries(trackedResult).reduce(\n        (res, [key, value]) => {\n          return {\n            ...res,\n            [key]: {\n              configurable: false,\n              enumerable: true,\n              get: () => {\n                if (!trackedKeys.current.includes(key)) {\n                  trackedKeys.current.push(key);\n                }\n                return value;\n              }\n            }\n          };\n        },\n        {}\n      )\n    );\n    return trackedResult;\n  }\n  return result;\n}\n\n// src/hooks/accounts/useAccount.ts\nfunction useAccount({ onConnect, onDisconnect } = {}) {\n  const config = useConfig();\n  const watchAccount_ = React9.useCallback(\n    (callback) => watchAccount(callback),\n    [config]\n  );\n  const account = useSyncExternalStoreWithTracked(watchAccount_, getAccount);\n  const previousAccountRef = React9.useRef();\n  const previousAccount = previousAccountRef.current;\n  React9.useEffect(() => {\n    if (previousAccount?.status !== \"connected\" && account.status === \"connected\") {\n      onConnect?.({\n        address: account.address,\n        connector: account.connector,\n        isReconnected: previousAccount?.status === \"reconnecting\" || previousAccount?.status === void 0\n      });\n    }\n    if (previousAccount?.status === \"connected\" && account.status === \"disconnected\") {\n      onDisconnect?.();\n    }\n    previousAccountRef.current = account;\n  }, [onConnect, onDisconnect, previousAccount, account]);\n  return account;\n}\n\n// src/hooks/accounts/useBalance.ts\nimport { fetchBalance } from \"@wagmi/core\";\nimport * as React10 from \"react\";\nfunction queryKey4({\n  address,\n  chainId,\n  formatUnits,\n  scopeKey,\n  token\n}) {\n  return [\n    {\n      entity: \"balance\",\n      address,\n      chainId,\n      formatUnits,\n      scopeKey,\n      token\n    }\n  ];\n}\nfunction queryFn4({\n  queryKey: [{ address, chainId, formatUnits, token }]\n}) {\n  if (!address)\n    throw new Error(\"address is required\");\n  return fetchBalance({ address, chainId, formatUnits, token });\n}\nfunction useBalance({\n  address,\n  cacheTime,\n  chainId: chainId_,\n  enabled = true,\n  formatUnits,\n  scopeKey,\n  staleTime,\n  suspense,\n  token,\n  watch,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  const queryKey_ = React10.useMemo(\n    () => queryKey4({ address, chainId, formatUnits, scopeKey, token }),\n    [address, chainId, formatUnits, scopeKey, token]\n  );\n  const balanceQuery = useQuery(queryKey_, queryFn4, {\n    cacheTime,\n    enabled: Boolean(enabled && address),\n    staleTime,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n  useInvalidateOnBlock({\n    chainId,\n    enabled: Boolean(enabled && watch && address),\n    queryKey: queryKey_\n  });\n  return balanceQuery;\n}\n\n// src/hooks/accounts/useConnect.ts\nimport { connect } from \"@wagmi/core\";\nimport * as React11 from \"react\";\nvar mutationKey = (args) => [{ entity: \"connect\", ...args }];\nvar mutationFn = (args) => {\n  const { connector, chainId } = args;\n  if (!connector)\n    throw new Error(\"connector is required\");\n  return connect({ connector, chainId });\n};\nfunction useConnect({\n  chainId,\n  connector,\n  onError,\n  onMutate,\n  onSettled,\n  onSuccess\n} = {}) {\n  const config = useConfig();\n  const {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    mutate,\n    mutateAsync,\n    reset,\n    status,\n    variables\n  } = useMutation(mutationKey({ connector, chainId }), mutationFn, {\n    onError,\n    onMutate,\n    onSettled,\n    onSuccess\n  });\n  const connect2 = React11.useCallback(\n    (args) => {\n      return mutate({\n        chainId: args?.chainId ?? chainId,\n        connector: args?.connector ?? connector\n      });\n    },\n    [chainId, connector, mutate]\n  );\n  const connectAsync = React11.useCallback(\n    (args) => {\n      return mutateAsync({\n        chainId: args?.chainId ?? chainId,\n        connector: args?.connector ?? connector\n      });\n    },\n    [chainId, connector, mutateAsync]\n  );\n  return {\n    connect: connect2,\n    connectAsync,\n    connectors: config.connectors,\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    pendingConnector: variables?.connector,\n    reset,\n    status,\n    variables\n  };\n}\n\n// src/hooks/accounts/useDisconnect.ts\nimport { disconnect } from \"@wagmi/core\";\nvar mutationKey2 = [{ entity: \"disconnect\" }];\nvar mutationFn2 = () => disconnect();\nfunction useDisconnect({\n  onError,\n  onMutate,\n  onSettled,\n  onSuccess\n} = {}) {\n  const {\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    mutate: disconnect2,\n    mutateAsync: disconnectAsync,\n    reset,\n    status\n  } = useMutation(mutationKey2, mutationFn2, {\n    ...onError ? {\n      onError(error2, _variables, context) {\n        onError(error2, context);\n      }\n    } : {},\n    onMutate,\n    ...onSettled ? {\n      onSettled(_data, error2, _variables, context) {\n        onSettled(error2, context);\n      }\n    } : {},\n    ...onSuccess ? {\n      onSuccess(_data, _variables, context) {\n        onSuccess(context);\n      }\n    } : {}\n  });\n  return {\n    disconnect: disconnect2,\n    disconnectAsync,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    reset,\n    status\n  };\n}\n\n// src/hooks/accounts/useNetwork.ts\nimport { getNetwork, watchNetwork } from \"@wagmi/core\";\nimport { useCallback as useCallback5 } from \"react\";\nfunction useNetwork() {\n  const config = useConfig();\n  const watchNetwork_ = useCallback5(\n    (callback) => watchNetwork(callback),\n    [config]\n  );\n  return useSyncExternalStoreWithTracked(watchNetwork_, getNetwork);\n}\n\n// src/hooks/accounts/useSignMessage.ts\nimport { signMessage } from \"@wagmi/core\";\nimport * as React12 from \"react\";\nvar mutationKey3 = (args) => [{ entity: \"signMessage\", ...args }];\nvar mutationFn3 = (args) => {\n  const { message } = args;\n  if (!message)\n    throw new Error(\"message is required\");\n  return signMessage({ message });\n};\nfunction useSignMessage({\n  message,\n  onError,\n  onMutate,\n  onSettled,\n  onSuccess\n} = {}) {\n  const {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    mutate,\n    mutateAsync,\n    reset,\n    status,\n    variables\n  } = useMutation(mutationKey3({ message }), mutationFn3, {\n    onError,\n    onMutate,\n    onSettled,\n    onSuccess\n  });\n  const signMessage2 = React12.useCallback(\n    (args) => mutate(args || { message }),\n    [message, mutate]\n  );\n  const signMessageAsync = React12.useCallback(\n    (args) => mutateAsync(args || { message }),\n    [message, mutateAsync]\n  );\n  return {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    reset,\n    signMessage: signMessage2,\n    signMessageAsync,\n    status,\n    variables\n  };\n}\n\n// src/hooks/accounts/useSignTypedData.ts\nimport { signTypedData } from \"@wagmi/core\";\nimport * as React13 from \"react\";\nfunction mutationKey4({ domain, types, message, primaryType }) {\n  return [\n    { entity: \"signTypedData\", domain, types, message, primaryType }\n  ];\n}\nfunction mutationFn4(args) {\n  const { domain, types, primaryType, message } = args;\n  if (!domain)\n    throw new Error(\"domain is required\");\n  if (!types)\n    throw new Error(\"types is required\");\n  if (!primaryType)\n    throw new Error(\"primaryType is required\");\n  if (!message)\n    throw new Error(\"message is required\");\n  return signTypedData({\n    domain,\n    message,\n    primaryType,\n    types\n  });\n}\nfunction useSignTypedData({\n  domain,\n  types,\n  message,\n  primaryType,\n  onError,\n  onMutate,\n  onSettled,\n  onSuccess\n} = {}) {\n  const {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    mutate,\n    mutateAsync,\n    reset,\n    status,\n    variables\n  } = useMutation(\n    mutationKey4({\n      domain,\n      message,\n      primaryType,\n      types\n    }),\n    mutationFn4,\n    {\n      onError,\n      onMutate,\n      onSettled,\n      onSuccess\n    }\n  );\n  const signTypedData2 = React13.useCallback(\n    (args) => mutate({\n      domain: args?.domain ?? domain,\n      types: args?.types ?? types,\n      message: args?.message ?? message,\n      primaryType: args?.primaryType ?? primaryType\n    }),\n    [domain, types, primaryType, message, mutate]\n  );\n  const signTypedDataAsync = React13.useCallback(\n    (args) => mutateAsync({\n      domain: args?.domain ?? domain,\n      types: args?.types ?? types,\n      message: args?.message ?? message,\n      primaryType: args?.primaryType ?? primaryType\n    }),\n    [domain, types, primaryType, message, mutateAsync]\n  );\n  return {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    reset,\n    signTypedData: signTypedData2,\n    signTypedDataAsync,\n    status,\n    variables\n  };\n}\n\n// src/hooks/accounts/useSwitchNetwork.ts\nimport { switchNetwork } from \"@wagmi/core\";\nimport * as React14 from \"react\";\nvar mutationKey5 = (args) => [{ entity: \"switchNetwork\", ...args }];\nvar mutationFn5 = (args) => {\n  const { chainId } = args;\n  if (!chainId)\n    throw new Error(\"chainId is required\");\n  return switchNetwork({ chainId });\n};\nfunction useSwitchNetwork({\n  chainId,\n  throwForSwitchChainNotSupported,\n  onError,\n  onMutate,\n  onSettled,\n  onSuccess\n} = {}) {\n  const config = useConfig();\n  const forceUpdate = useForceUpdate();\n  const {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    mutate,\n    mutateAsync,\n    reset,\n    status,\n    variables\n  } = useMutation(mutationKey5({ chainId }), mutationFn5, {\n    onError,\n    onMutate,\n    onSettled,\n    onSuccess\n  });\n  const switchNetwork_ = React14.useCallback(\n    (chainId_) => mutate({ chainId: chainId_ ?? chainId }),\n    [chainId, mutate]\n  );\n  const switchNetworkAsync_ = React14.useCallback(\n    (chainId_) => mutateAsync({ chainId: chainId_ ?? chainId }),\n    [chainId, mutateAsync]\n  );\n  React14.useEffect(() => {\n    const unwatch = config.subscribe(\n      ({ chains, connector }) => ({\n        chains,\n        connector\n      }),\n      forceUpdate\n    );\n    return unwatch;\n  }, [config, forceUpdate]);\n  let switchNetwork2;\n  let switchNetworkAsync;\n  const supportsSwitchChain = !!config.connector?.switchChain;\n  if (throwForSwitchChainNotSupported || supportsSwitchChain) {\n    switchNetwork2 = switchNetwork_;\n    switchNetworkAsync = switchNetworkAsync_;\n  }\n  return {\n    chains: config.chains ?? [],\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    pendingChainId: variables?.chainId,\n    reset,\n    status,\n    switchNetwork: switchNetwork2,\n    switchNetworkAsync,\n    variables\n  };\n}\n\n// src/hooks/contracts/useContractEvent.ts\nimport * as React15 from \"react\";\nfunction useContractEvent({\n  address,\n  chainId,\n  abi,\n  listener,\n  eventName\n} = {}) {\n  const publicClient = usePublicClient({ chainId });\n  const webSocketPublicClient = useWebSocketPublicClient({ chainId });\n  const unwatch = React15.useRef();\n  React15.useEffect(() => {\n    if (!abi || !address || !eventName)\n      return;\n    const publicClient_ = webSocketPublicClient || publicClient;\n    unwatch.current = publicClient_.watchContractEvent({\n      abi,\n      address,\n      eventName,\n      onLogs: listener\n    });\n    return unwatch.current;\n  }, [abi, address, eventName, publicClient.uid, webSocketPublicClient?.uid]);\n  return unwatch.current;\n}\n\n// src/hooks/contracts/useContractInfiniteReads.ts\nimport { replaceEqualDeep } from \"@tanstack/react-query\";\nimport { deepEqual as deepEqual2, readContracts } from \"@wagmi/core\";\nimport * as React16 from \"react\";\nfunction queryKey5({\n  allowFailure,\n  blockNumber,\n  blockTag,\n  cacheKey,\n  scopeKey\n}) {\n  return [\n    {\n      entity: \"readContractsInfinite\",\n      allowFailure,\n      blockNumber,\n      blockTag,\n      cacheKey,\n      scopeKey\n    }\n  ];\n}\nfunction queryFn5({\n  contracts\n}) {\n  return ({\n    queryKey: [{ allowFailure, blockNumber, blockTag }],\n    pageParam\n  }) => {\n    return readContracts({\n      allowFailure,\n      blockNumber,\n      blockTag,\n      contracts: contracts(pageParam || void 0)\n    });\n  };\n}\nfunction useContractInfiniteReads({\n  allowFailure,\n  blockNumber,\n  blockTag,\n  cacheKey,\n  cacheTime,\n  contracts,\n  enabled: enabled_ = true,\n  getNextPageParam,\n  isDataEqual,\n  keepPreviousData,\n  onError,\n  onSettled,\n  onSuccess,\n  scopeKey,\n  select,\n  staleTime,\n  structuralSharing = (oldData, newData) => deepEqual2(oldData, newData) ? oldData : replaceEqualDeep(oldData, newData),\n  suspense\n}) {\n  const queryKey_ = React16.useMemo(\n    () => queryKey5({ allowFailure, blockNumber, blockTag, cacheKey, scopeKey }),\n    [allowFailure, blockNumber, blockTag, cacheKey, scopeKey]\n  );\n  const enabled = React16.useMemo(() => {\n    const enabled2 = Boolean(enabled_ && contracts);\n    return enabled2;\n  }, [contracts, enabled_]);\n  return useInfiniteQuery(queryKey_, queryFn5({ contracts }), {\n    cacheTime,\n    enabled,\n    getNextPageParam,\n    isDataEqual,\n    keepPreviousData,\n    select,\n    staleTime,\n    structuralSharing,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n}\nfunction paginatedIndexesConfig(fn, {\n  perPage,\n  start,\n  direction\n}) {\n  const contracts = (page = 0) => [...Array(perPage).keys()].map((index) => {\n    return direction === \"increment\" ? start + index + page * perPage : start - index - page * perPage;\n  }).filter((index) => index >= 0).flatMap(fn);\n  return {\n    contracts,\n    getNextPageParam(lastPage, pages) {\n      return lastPage?.length === perPage ? pages.length : void 0;\n    }\n  };\n}\n\n// src/hooks/contracts/useContractRead.ts\nimport { replaceEqualDeep as replaceEqualDeep2 } from \"@tanstack/react-query\";\nimport { deepEqual as deepEqual3, readContract } from \"@wagmi/core\";\nimport * as React17 from \"react\";\nfunction queryKey6({\n  account,\n  address,\n  args,\n  blockNumber,\n  blockTag,\n  chainId,\n  functionName,\n  scopeKey\n}) {\n  return [\n    {\n      entity: \"readContract\",\n      account,\n      address,\n      args,\n      blockNumber,\n      blockTag,\n      chainId,\n      functionName,\n      scopeKey\n    }\n  ];\n}\nfunction queryFn6({ abi }) {\n  return async ({\n    queryKey: [\n      { account, address, args, blockNumber, blockTag, chainId, functionName }\n    ]\n  }) => {\n    if (!abi)\n      throw new Error(\"abi is required\");\n    if (!address)\n      throw new Error(\"address is required\");\n    return await readContract({\n      account,\n      address,\n      args,\n      blockNumber,\n      blockTag,\n      chainId,\n      abi,\n      functionName\n    }) ?? null;\n  };\n}\nfunction useContractRead({\n  abi,\n  address,\n  account,\n  args,\n  blockNumber: blockNumberOverride,\n  blockTag,\n  cacheOnBlock = false,\n  cacheTime,\n  chainId: chainId_,\n  enabled: enabled_ = true,\n  functionName,\n  isDataEqual,\n  keepPreviousData,\n  onError,\n  onSettled,\n  onSuccess,\n  scopeKey,\n  select,\n  staleTime,\n  structuralSharing = (oldData, newData) => deepEqual3(oldData, newData) ? oldData : replaceEqualDeep2(oldData, newData),\n  suspense,\n  watch\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  const { data: blockNumber_ } = useBlockNumber({\n    chainId,\n    enabled: watch || cacheOnBlock,\n    scopeKey: watch || cacheOnBlock ? void 0 : \"idle\",\n    watch\n  });\n  const blockNumber = blockNumberOverride ?? blockNumber_;\n  const queryKey_ = React17.useMemo(\n    () => queryKey6({\n      account,\n      address,\n      args,\n      blockNumber: cacheOnBlock ? blockNumber : void 0,\n      blockTag,\n      chainId,\n      functionName,\n      scopeKey\n    }),\n    [\n      account,\n      address,\n      args,\n      blockNumber,\n      blockTag,\n      cacheOnBlock,\n      chainId,\n      functionName,\n      scopeKey\n    ]\n  );\n  const enabled = React17.useMemo(() => {\n    let enabled2 = Boolean(enabled_ && abi && address && functionName);\n    if (cacheOnBlock)\n      enabled2 = Boolean(enabled2 && blockNumber);\n    return enabled2;\n  }, [abi, address, blockNumber, cacheOnBlock, enabled_, functionName]);\n  useInvalidateOnBlock({\n    chainId,\n    enabled: Boolean(enabled && watch && !cacheOnBlock),\n    queryKey: queryKey_\n  });\n  return useQuery(\n    queryKey_,\n    queryFn6({\n      abi\n    }),\n    {\n      cacheTime,\n      enabled,\n      isDataEqual,\n      keepPreviousData,\n      select,\n      staleTime,\n      structuralSharing,\n      suspense,\n      onError,\n      onSettled,\n      onSuccess\n    }\n  );\n}\n\n// src/hooks/contracts/useContractReads.ts\nimport { replaceEqualDeep as replaceEqualDeep3 } from \"@tanstack/react-query\";\nimport { deepEqual as deepEqual4, readContracts as readContracts2 } from \"@wagmi/core\";\nimport * as React18 from \"react\";\nfunction queryKey7({\n  allowFailure,\n  blockNumber,\n  blockTag,\n  chainId,\n  contracts,\n  scopeKey\n}) {\n  return [\n    {\n      entity: \"readContracts\",\n      allowFailure,\n      blockNumber,\n      blockTag,\n      chainId,\n      scopeKey,\n      contracts: (contracts ?? []).map(({ address, args, chainId: chainId2, functionName }) => ({\n        address,\n        args,\n        chainId: chainId2,\n        functionName\n      }))\n    }\n  ];\n}\nfunction queryFn7({ abis }) {\n  return ({\n    queryKey: [{ allowFailure, blockNumber, blockTag, contracts: contracts_ }]\n  }) => {\n    const contracts = contracts_.map((contract, i) => ({\n      ...contract,\n      abi: abis[i]\n    }));\n    return readContracts2({\n      allowFailure,\n      contracts,\n      blockNumber,\n      blockTag\n    });\n  };\n}\nfunction useContractReads({\n  allowFailure: allowFailure_,\n  blockNumber: blockNumberOverride,\n  blockTag,\n  cacheOnBlock = false,\n  cacheTime,\n  contracts,\n  enabled: enabled_ = true,\n  isDataEqual,\n  keepPreviousData,\n  onError,\n  onSettled,\n  onSuccess,\n  scopeKey,\n  select,\n  staleTime,\n  structuralSharing = (oldData, newData) => deepEqual4(oldData, newData) ? oldData : replaceEqualDeep3(oldData, newData),\n  suspense,\n  watch\n} = {}) {\n  const allowFailure = allowFailure_ ?? true;\n  const { data: blockNumber_ } = useBlockNumber({\n    enabled: watch || cacheOnBlock,\n    watch\n  });\n  const chainId = useChainId();\n  const blockNumber = blockNumberOverride ?? blockNumber_;\n  const queryKey_ = React18.useMemo(\n    () => queryKey7({\n      allowFailure,\n      blockNumber: cacheOnBlock ? blockNumber : void 0,\n      blockTag,\n      chainId,\n      contracts,\n      scopeKey\n    }),\n    [\n      allowFailure,\n      blockNumber,\n      blockTag,\n      cacheOnBlock,\n      chainId,\n      scopeKey,\n      contracts\n    ]\n  );\n  const enabled = React18.useMemo(() => {\n    let enabled2 = Boolean(\n      enabled_ && contracts?.every(\n        (x) => x.abi && x.address && x.functionName\n      )\n    );\n    if (cacheOnBlock)\n      enabled2 = Boolean(enabled2 && blockNumber);\n    return enabled2;\n  }, [blockNumber, cacheOnBlock, contracts, enabled_]);\n  useInvalidateOnBlock({\n    enabled: Boolean(enabled && watch && !cacheOnBlock),\n    queryKey: queryKey_\n  });\n  const abis = (contracts ?? []).map(\n    ({ abi }) => abi\n  );\n  return useQuery(queryKey_, queryFn7({ abis }), {\n    cacheTime,\n    enabled,\n    isDataEqual,\n    keepPreviousData,\n    staleTime,\n    select,\n    structuralSharing,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n}\n\n// src/hooks/contracts/useContractWrite.ts\nimport { writeContract } from \"@wagmi/core\";\nimport { getSendTransactionParameters } from \"@wagmi/core/internal\";\nimport * as React19 from \"react\";\nfunction mutationKey6({\n  address,\n  abi,\n  functionName,\n  ...config\n}) {\n  const {\n    args,\n    accessList,\n    account,\n    dataSuffix,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    request,\n    value\n  } = config;\n  return [\n    {\n      entity: \"writeContract\",\n      address,\n      args,\n      abi,\n      accessList,\n      account,\n      dataSuffix,\n      functionName,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      request,\n      value\n    }\n  ];\n}\nfunction mutationFn6(config) {\n  if (config.mode === \"prepared\") {\n    if (!config.request)\n      throw new Error(\"request is required\");\n    return writeContract({\n      mode: \"prepared\",\n      request: config.request\n    });\n  }\n  if (!config.address)\n    throw new Error(\"address is required\");\n  if (!config.abi)\n    throw new Error(\"abi is required\");\n  if (!config.functionName)\n    throw new Error(\"functionName is required\");\n  return writeContract({\n    address: config.address,\n    args: config.args,\n    chainId: config.chainId,\n    abi: config.abi,\n    functionName: config.functionName,\n    accessList: config.accessList,\n    account: config.account,\n    dataSuffix: config.dataSuffix,\n    gas: config.gas,\n    gasPrice: config.gasPrice,\n    maxFeePerGas: config.maxFeePerGas,\n    maxPriorityFeePerGas: config.maxPriorityFeePerGas,\n    nonce: config.nonce,\n    value: config.value\n  });\n}\nfunction useContractWrite(config) {\n  const {\n    address,\n    abi,\n    args,\n    chainId,\n    functionName,\n    mode,\n    request,\n    dataSuffix\n  } = config;\n  const {\n    accessList,\n    account,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    value\n  } = getSendTransactionParameters(config);\n  const {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    mutate,\n    mutateAsync,\n    reset,\n    status,\n    variables\n  } = useMutation(\n    mutationKey6({\n      address,\n      abi,\n      functionName,\n      chainId,\n      mode,\n      args,\n      accessList,\n      account,\n      dataSuffix,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      request,\n      value\n    }),\n    mutationFn6,\n    {\n      onError: config.onError,\n      onMutate: config.onMutate,\n      onSettled: config.onSettled,\n      onSuccess: config.onSuccess\n    }\n  );\n  const write = React19.useMemo(() => {\n    if (config.mode === \"prepared\") {\n      if (!request)\n        return void 0;\n      return () => mutate({\n        mode: \"prepared\",\n        request: config.request,\n        chainId: config.chainId\n      });\n    }\n    return (overrideConfig) => mutate({\n      address,\n      args,\n      abi,\n      functionName,\n      chainId,\n      accessList,\n      account,\n      dataSuffix,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      value,\n      ...overrideConfig\n    });\n  }, [\n    accessList,\n    account,\n    abi,\n    address,\n    args,\n    chainId,\n    config.chainId,\n    config.mode,\n    config.request,\n    dataSuffix,\n    functionName,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    mutate,\n    nonce,\n    request,\n    value\n  ]);\n  const writeAsync = React19.useMemo(() => {\n    if (config.mode === \"prepared\") {\n      if (!request)\n        return void 0;\n      return () => mutateAsync({\n        mode: \"prepared\",\n        request: config.request\n      });\n    }\n    return (overrideConfig) => mutateAsync({\n      address,\n      args,\n      abi,\n      chainId,\n      functionName,\n      accessList,\n      account,\n      dataSuffix,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      value,\n      ...overrideConfig\n    });\n  }, [\n    accessList,\n    account,\n    abi,\n    address,\n    args,\n    chainId,\n    config.mode,\n    config.request,\n    dataSuffix,\n    functionName,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    mutateAsync,\n    nonce,\n    request,\n    value\n  ]);\n  return {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    reset,\n    status,\n    variables,\n    write,\n    writeAsync\n  };\n}\n\n// src/hooks/contracts/usePrepareContractWrite.ts\nimport { prepareWriteContract } from \"@wagmi/core\";\nimport { getCallParameters } from \"@wagmi/core/internal\";\nfunction queryKey8({\n  accessList,\n  account,\n  activeChainId,\n  args,\n  address,\n  blockNumber,\n  blockTag,\n  chainId,\n  dataSuffix,\n  functionName,\n  gas,\n  gasPrice,\n  maxFeePerGas,\n  maxPriorityFeePerGas,\n  nonce,\n  scopeKey,\n  walletClientAddress,\n  value\n}) {\n  return [\n    {\n      entity: \"prepareContractTransaction\",\n      accessList,\n      account,\n      activeChainId,\n      address,\n      args,\n      blockNumber,\n      blockTag,\n      chainId,\n      dataSuffix,\n      functionName,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      scopeKey,\n      walletClientAddress,\n      value\n    }\n  ];\n}\nfunction queryFn8({\n  abi,\n  walletClient\n}) {\n  return ({\n    queryKey: [\n      {\n        accessList,\n        account,\n        args,\n        address,\n        blockNumber,\n        blockTag,\n        chainId,\n        dataSuffix,\n        functionName,\n        gas,\n        gasPrice,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        nonce,\n        value\n      }\n    ]\n  }) => {\n    if (!abi)\n      throw new Error(\"abi is required\");\n    if (!address)\n      throw new Error(\"address is required\");\n    if (!functionName)\n      throw new Error(\"functionName is required\");\n    return prepareWriteContract({\n      abi,\n      accessList,\n      account,\n      args,\n      address,\n      blockNumber,\n      blockTag,\n      chainId,\n      dataSuffix,\n      functionName,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      walletClient,\n      value\n    });\n  };\n}\nfunction usePrepareContractWrite({\n  address,\n  abi,\n  functionName,\n  chainId,\n  args,\n  cacheTime,\n  dataSuffix,\n  enabled = true,\n  scopeKey,\n  staleTime,\n  suspense,\n  onError,\n  onSettled,\n  onSuccess,\n  ...config\n} = {}) {\n  const { chain: activeChain } = useNetwork();\n  const { data: walletClient } = useWalletClient({ chainId });\n  const {\n    accessList,\n    account,\n    blockNumber,\n    blockTag,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    value\n  } = getCallParameters(config);\n  const prepareContractWriteQuery = useQuery(\n    queryKey8({\n      accessList,\n      account,\n      activeChainId: activeChain?.id,\n      address,\n      args,\n      blockNumber,\n      blockTag,\n      chainId,\n      dataSuffix,\n      functionName,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      scopeKey,\n      walletClientAddress: walletClient?.account.address,\n      value\n    }),\n    queryFn8({\n      abi,\n      walletClient\n    }),\n    {\n      cacheTime,\n      enabled: Boolean(\n        enabled && abi && address && functionName && walletClient\n      ),\n      staleTime,\n      suspense,\n      onError,\n      onSettled,\n      onSuccess\n    }\n  );\n  return Object.assign(prepareContractWriteQuery, {\n    config: {\n      chainId,\n      mode: \"prepared\",\n      request: void 0,\n      ...prepareContractWriteQuery.data\n    }\n  });\n}\n\n// src/hooks/contracts/useToken.ts\nimport { fetchToken } from \"@wagmi/core\";\nfunction queryKey9({\n  address,\n  chainId,\n  formatUnits,\n  scopeKey\n}) {\n  return [{ entity: \"token\", address, chainId, formatUnits, scopeKey }];\n}\nfunction queryFn9({\n  queryKey: [{ address, chainId, formatUnits }]\n}) {\n  if (!address)\n    throw new Error(\"address is required\");\n  return fetchToken({ address, chainId, formatUnits });\n}\nfunction useToken({\n  address,\n  chainId: chainId_,\n  formatUnits = \"ether\",\n  cacheTime,\n  enabled = true,\n  scopeKey,\n  staleTime = 1e3 * 60 * 60 * 24,\n  suspense,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  return useQuery(\n    queryKey9({ address, chainId, formatUnits, scopeKey }),\n    queryFn9,\n    {\n      cacheTime,\n      enabled: Boolean(enabled && address),\n      staleTime,\n      suspense,\n      onError,\n      onSettled,\n      onSuccess\n    }\n  );\n}\n\n// src/hooks/ens/useEnsAddress.ts\nimport { fetchEnsAddress } from \"@wagmi/core\";\nfunction queryKey10({ chainId, name, scopeKey }) {\n  return [{ entity: \"ensAddress\", chainId, name, scopeKey }];\n}\nfunction queryFn10({\n  queryKey: [{ chainId, name }]\n}) {\n  if (!name)\n    throw new Error(\"name is required\");\n  return fetchEnsAddress({ chainId, name });\n}\nfunction useEnsAddress({\n  cacheTime,\n  chainId: chainId_,\n  enabled = true,\n  name,\n  scopeKey,\n  staleTime = 1e3 * 60 * 60 * 24,\n  suspense,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  return useQuery(queryKey10({ chainId, name, scopeKey }), queryFn10, {\n    cacheTime,\n    enabled: Boolean(enabled && chainId && name),\n    staleTime,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n}\n\n// src/hooks/ens/useEnsAvatar.ts\nimport { fetchEnsAvatar } from \"@wagmi/core\";\nfunction queryKey11({ name, chainId, scopeKey }) {\n  return [{ entity: \"ensAvatar\", name, chainId, scopeKey }];\n}\nfunction queryFn11({\n  queryKey: [{ name, chainId }]\n}) {\n  if (!name)\n    throw new Error(\"name is required\");\n  return fetchEnsAvatar({ name, chainId });\n}\nfunction useEnsAvatar({\n  cacheTime,\n  chainId: chainId_,\n  enabled = true,\n  name,\n  scopeKey,\n  staleTime = 1e3 * 60 * 60 * 24,\n  suspense,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  return useQuery(queryKey11({ name, chainId, scopeKey }), queryFn11, {\n    cacheTime,\n    enabled: Boolean(enabled && name && chainId),\n    staleTime,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n}\n\n// src/hooks/ens/useEnsName.ts\nimport { fetchEnsName } from \"@wagmi/core\";\nfunction queryKey12({\n  address,\n  chainId,\n  scopeKey\n}) {\n  return [{ entity: \"ensName\", address, chainId, scopeKey }];\n}\nfunction queryFn12({\n  queryKey: [{ address, chainId }]\n}) {\n  if (!address)\n    throw new Error(\"address is required\");\n  return fetchEnsName({ address, chainId });\n}\nfunction useEnsName({\n  address,\n  cacheTime,\n  chainId: chainId_,\n  enabled = true,\n  scopeKey,\n  staleTime = 1e3 * 60 * 60 * 24,\n  suspense,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  return useQuery(queryKey12({ address, chainId, scopeKey }), queryFn12, {\n    cacheTime,\n    enabled: Boolean(enabled && address && chainId),\n    staleTime,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n}\n\n// src/hooks/ens/useEnsResolver.ts\nimport { fetchEnsResolver } from \"@wagmi/core\";\nfunction queryKey13({ chainId, name, scopeKey }) {\n  return [\n    { entity: \"ensResolver\", chainId, name, scopeKey, persist: false }\n  ];\n}\nfunction queryFn13({\n  queryKey: [{ chainId, name }]\n}) {\n  if (!name)\n    throw new Error(\"name is required\");\n  return fetchEnsResolver({ chainId, name });\n}\nfunction useEnsResolver({\n  chainId: chainId_,\n  name,\n  enabled = true,\n  scopeKey,\n  suspense,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  return useQuery(queryKey13({ chainId, name, scopeKey }), queryFn13, {\n    cacheTime: 0,\n    enabled: Boolean(enabled && chainId && name),\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n}\n\n// src/hooks/transactions/usePrepareSendTransaction.ts\nimport { prepareSendTransaction } from \"@wagmi/core\";\nfunction queryKey14({\n  accessList,\n  account,\n  activeChainId,\n  chainId,\n  data,\n  gas,\n  gasPrice,\n  maxFeePerGas,\n  maxPriorityFeePerGas,\n  nonce,\n  to,\n  value,\n  scopeKey,\n  walletClientAddress\n}) {\n  return [\n    {\n      entity: \"prepareSendTransaction\",\n      activeChainId,\n      accessList,\n      account,\n      chainId,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to,\n      value,\n      scopeKey,\n      walletClientAddress\n    }\n  ];\n}\nfunction queryFn14({ walletClient }) {\n  return ({\n    queryKey: [\n      {\n        accessList,\n        account,\n        chainId,\n        data,\n        gas,\n        gasPrice,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        nonce,\n        to,\n        value\n      }\n    ]\n  }) => {\n    if (!to)\n      throw new Error(\"to is required\");\n    return prepareSendTransaction({\n      accessList,\n      account,\n      chainId,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to,\n      value,\n      walletClient\n    });\n  };\n}\nfunction usePrepareSendTransaction({\n  accessList,\n  account,\n  chainId,\n  cacheTime,\n  data,\n  enabled = true,\n  gas,\n  gasPrice,\n  maxFeePerGas,\n  maxPriorityFeePerGas,\n  nonce,\n  scopeKey,\n  staleTime,\n  suspense,\n  to,\n  value,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const { chain: activeChain } = useNetwork();\n  const { data: walletClient } = useWalletClient({ chainId });\n  const prepareSendTransactionQuery = useQuery(\n    queryKey14({\n      accessList,\n      activeChainId: activeChain?.id,\n      account,\n      chainId,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      scopeKey,\n      to,\n      value,\n      walletClientAddress: walletClient?.account.address\n    }),\n    queryFn14({ walletClient }),\n    {\n      cacheTime,\n      enabled: Boolean(enabled && walletClient && to),\n      staleTime,\n      suspense,\n      onError,\n      onSettled,\n      onSuccess\n    }\n  );\n  return Object.assign(prepareSendTransactionQuery, {\n    config: {\n      mode: \"prepared\",\n      ...prepareSendTransactionQuery.isSuccess ? prepareSendTransactionQuery.data : void 0\n    }\n  });\n}\n\n// src/hooks/transactions/useSendTransaction.ts\nimport { sendTransaction } from \"@wagmi/core\";\nimport * as React20 from \"react\";\nvar mutationKey7 = (args) => [{ entity: \"sendTransaction\", ...args }];\nvar mutationFn7 = ({\n  accessList,\n  account,\n  chainId,\n  data,\n  gas,\n  gasPrice,\n  maxFeePerGas,\n  maxPriorityFeePerGas,\n  mode,\n  nonce,\n  to,\n  value\n}) => {\n  if (!to)\n    throw new Error(\"to is required.\");\n  return sendTransaction({\n    accessList,\n    account,\n    chainId,\n    data,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    mode,\n    nonce,\n    to,\n    value\n  });\n};\nfunction useSendTransaction({\n  accessList,\n  account,\n  chainId,\n  data: data_,\n  gas,\n  gasPrice,\n  maxFeePerGas,\n  maxPriorityFeePerGas,\n  mode,\n  nonce,\n  to,\n  value,\n  onError,\n  onMutate,\n  onSettled,\n  onSuccess\n} = {}) {\n  const {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    mutate,\n    mutateAsync,\n    reset,\n    status,\n    variables\n  } = useMutation(\n    mutationKey7({\n      accessList,\n      account,\n      chainId,\n      data: data_,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      mode,\n      nonce,\n      to,\n      value\n    }),\n    mutationFn7,\n    {\n      onError,\n      onMutate,\n      onSettled,\n      onSuccess\n    }\n  );\n  const sendTransaction2 = React20.useCallback(\n    (args) => mutate({\n      chainId,\n      mode,\n      ...args || {\n        accessList,\n        account,\n        chainId,\n        data: data_,\n        gas,\n        gasPrice,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        mode,\n        nonce,\n        value,\n        to\n      }\n    }),\n    [\n      accessList,\n      account,\n      chainId,\n      data_,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      mode,\n      mutate,\n      nonce,\n      to,\n      value\n    ]\n  );\n  const sendTransactionAsync = React20.useCallback(\n    (args) => mutateAsync({\n      chainId,\n      mode,\n      ...args || {\n        accessList,\n        account,\n        chainId,\n        data: data_,\n        gas,\n        gasPrice,\n        maxFeePerGas,\n        maxPriorityFeePerGas,\n        mode,\n        nonce,\n        value,\n        to\n      }\n    }),\n    [\n      accessList,\n      account,\n      chainId,\n      data_,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      mode,\n      mutateAsync,\n      nonce,\n      to,\n      value\n    ]\n  );\n  return {\n    data,\n    error,\n    isError,\n    isIdle,\n    isLoading,\n    isSuccess,\n    reset,\n    sendTransaction: mode === \"prepared\" && !to ? void 0 : sendTransaction2,\n    sendTransactionAsync: mode === \"prepared\" && !to ? void 0 : sendTransactionAsync,\n    status,\n    variables\n  };\n}\n\n// src/hooks/transactions/useTransaction.ts\nimport { fetchTransaction } from \"@wagmi/core\";\nfunction queryKey15({ chainId, hash, scopeKey }) {\n  return [{ entity: \"transaction\", chainId, hash, scopeKey }];\n}\nfunction queryFn15({\n  queryKey: [{ chainId, hash }]\n}) {\n  if (!hash)\n    throw new Error(\"hash is required\");\n  return fetchTransaction({ chainId, hash });\n}\nfunction useTransaction({\n  cacheTime = 0,\n  chainId: chainId_,\n  enabled = true,\n  hash,\n  scopeKey,\n  staleTime,\n  suspense,\n  onError,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  return useQuery(queryKey15({ chainId, hash, scopeKey }), queryFn15, {\n    cacheTime,\n    enabled: Boolean(enabled && hash),\n    staleTime,\n    suspense,\n    onError,\n    onSettled,\n    onSuccess\n  });\n}\n\n// src/hooks/transactions/useWaitForTransaction.ts\nimport { waitForTransaction } from \"@wagmi/core\";\nfunction queryKey16({\n  confirmations,\n  chainId,\n  hash,\n  scopeKey,\n  timeout\n}) {\n  return [\n    {\n      entity: \"waitForTransaction\",\n      confirmations,\n      chainId,\n      hash,\n      scopeKey,\n      timeout\n    }\n  ];\n}\nfunction queryFn16({\n  onReplaced\n}) {\n  return ({\n    queryKey: [{ chainId, confirmations, hash, timeout }]\n  }) => {\n    if (!hash)\n      throw new Error(\"hash is required\");\n    return waitForTransaction({\n      chainId,\n      confirmations,\n      hash,\n      onReplaced,\n      timeout\n    });\n  };\n}\nfunction useWaitForTransaction({\n  chainId: chainId_,\n  confirmations,\n  hash,\n  timeout,\n  cacheTime,\n  enabled = true,\n  scopeKey,\n  staleTime,\n  suspense,\n  onError,\n  onReplaced,\n  onSettled,\n  onSuccess\n} = {}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  return useQuery(\n    queryKey16({ chainId, confirmations, hash, scopeKey, timeout }),\n    queryFn16({ onReplaced }),\n    {\n      cacheTime,\n      enabled: Boolean(enabled && hash),\n      staleTime,\n      suspense,\n      onError,\n      onSettled,\n      onSuccess\n    }\n  );\n}\n\n// src/hooks/transactions/useWatchPendingTransactions.ts\nimport * as React21 from \"react\";\nfunction useWatchPendingTransactions({\n  chainId: chainId_,\n  enabled = true,\n  listener\n}) {\n  const chainId = useChainId({ chainId: chainId_ });\n  const publicClient = usePublicClient({ chainId });\n  const webSocketPublicClient = useWebSocketPublicClient({ chainId });\n  React21.useEffect(() => {\n    if (!enabled)\n      return;\n    const publicClient_ = webSocketPublicClient ?? publicClient;\n    return publicClient_.watchPendingTransactions({\n      onTransactions: listener\n    });\n  }, [enabled, listener, publicClient, webSocketPublicClient]);\n}\n\n// src/index.ts\nimport {\n  ChainMismatchError,\n  ChainNotConfiguredError,\n  Connector,\n  ConnectorAlreadyConnectedError,\n  ConnectorNotFoundError,\n  ConfigChainsNotFound,\n  SwitchChainNotSupportedError,\n  configureChains,\n  createStorage as createStorage2,\n  deepEqual as deepEqual5,\n  deserialize,\n  erc20ABI,\n  erc721ABI,\n  erc4626ABI,\n  readContracts as readContracts3,\n  serialize\n} from \"@wagmi/core\";\nimport { mainnet, sepolia } from \"viem/chains\";\nexport {\n  ChainMismatchError,\n  ChainNotConfiguredError,\n  ConfigChainsNotFound,\n  Connector,\n  ConnectorAlreadyConnectedError,\n  ConnectorNotFoundError,\n  Context,\n  SwitchChainNotSupportedError,\n  WagmiConfig,\n  configureChains,\n  createConfig,\n  createStorage2 as createStorage,\n  deepEqual5 as deepEqual,\n  deserialize,\n  erc20ABI,\n  erc4626ABI,\n  erc721ABI,\n  mainnet,\n  paginatedIndexesConfig,\n  readContracts3 as readContracts,\n  sepolia,\n  serialize,\n  useAccount,\n  useBalance,\n  useBlockNumber,\n  useChainId,\n  useConfig,\n  useConnect,\n  useContractEvent,\n  useContractInfiniteReads,\n  useContractRead,\n  useContractReads,\n  useContractWrite,\n  useDisconnect,\n  useEnsAddress,\n  useEnsAvatar,\n  useEnsName,\n  useEnsResolver,\n  useFeeData,\n  useInfiniteQuery,\n  useMutation,\n  useNetwork,\n  usePrepareContractWrite,\n  usePrepareSendTransaction,\n  usePublicClient,\n  useQuery,\n  useQueryClient2 as useQueryClient,\n  useSendTransaction,\n  useSignMessage,\n  useSignTypedData,\n  useSwitchNetwork,\n  useToken,\n  useTransaction,\n  useWaitForTransaction,\n  useWalletClient,\n  useWatchPendingTransactions,\n  useWebSocketPublicClient\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAIA,UAAQ,iBACV,OAAO,gBACP,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzDC,wBAAuB,KAAK,sBAC5BC,UAASF,QAAM,QACfG,aAAYH,QAAM,WAClBI,WAAUJ,QAAM,SAChB,gBAAgBA,QAAM;AACxB,cAAQ,mCAAmC,SACzC,WACA,aACA,mBACA,UACA,SACA;AACA,YAAI,UAAUE,QAAO,IAAI;AACzB,YAAI,SAAS,QAAQ,SAAS;AAC5B,cAAI,OAAO,EAAE,UAAU,OAAI,OAAO,KAAK;AACvC,kBAAQ,UAAU;AAAA,QACpB;AAAO,iBAAO,QAAQ;AACtB,kBAAUE;AAAA,UACR,WAAY;AACV,qBAAS,iBAAiB,cAAc;AACtC,kBAAI,CAAC,SAAS;AACZ,0BAAU;AACV,mCAAmB;AACnB,+BAAe,SAAS,YAAY;AACpC,oBAAI,WAAW,WAAW,KAAK,UAAU;AACvC,sBAAI,mBAAmB,KAAK;AAC5B,sBAAI,QAAQ,kBAAkB,YAAY;AACxC,2BAAQ,oBAAoB;AAAA,gBAChC;AACA,uBAAQ,oBAAoB;AAAA,cAC9B;AACA,iCAAmB;AACnB,kBAAI,SAAS,kBAAkB,YAAY;AACzC,uBAAO;AACT,kBAAI,gBAAgB,SAAS,YAAY;AACzC,kBAAI,WAAW,WAAW,QAAQ,kBAAkB,aAAa;AAC/D,uBAAQ,mBAAmB,cAAe;AAC5C,iCAAmB;AACnB,qBAAQ,oBAAoB;AAAA,YAC9B;AACA,gBAAI,UAAU,OACZ,kBACA,mBACA,yBACE,WAAW,oBAAoB,OAAO;AAC1C,mBAAO;AAAA,cACL,WAAY;AACV,uBAAO,iBAAiB,YAAY,CAAC;AAAA,cACvC;AAAA,cACA,SAAS,yBACL,SACA,WAAY;AACV,uBAAO,iBAAiB,uBAAuB,CAAC;AAAA,cAClD;AAAA,YACN;AAAA,UACF;AAAA,UACA,CAAC,aAAa,mBAAmB,UAAU,OAAO;AAAA,QACpD;AACA,YAAI,QAAQH,sBAAqB,WAAW,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE,QAAAE;AAAA,UACE,WAAY;AACV,iBAAK,WAAW;AAChB,iBAAK,QAAQ;AAAA,UACf;AAAA,UACA,CAAC,KAAK;AAAA,QACR;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AChGL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;AC+BO,SAASE,2BAA2B;EACzCC;EACAC,MAFyC;EAGzCC,eAAe;EACfC,WAAAA,aAAYC,KAAKC;EACjBC,aAAAA,eAAcF,KAAKG;EACnBC;AANyC,GAOM;AAC/C,MAAIR,SAAS;AACX,UAAMS,UAAWC,qBAAwD;AACvE,UAAI;AACFV,gBAAQW,QAAQV,KAAKE,WAAUO,eAAD,CAA9B;AACA;eACOE,OAAO;AACd,eAAOA;MACR;;AAEH,WAAO;MACLC,eAAeC,SAAUJ,qBAAoB;AAC3C,YAAIK,SAAsCL;AAC1C,YAAIE,QAAQH,QAAQM,MAAD;AACnB,YAAIC,aAAa;AACjB,eAAOJ,SAASG,QAAQ;AACtBC;AACAD,mBAASP,SAAAA,OAAAA,SAAAA,MAAQ;YACfE,iBAAiBK;YACjBH;YACAI;UAHe,CAAH;AAMd,cAAID,QAAQ;AACVH,oBAAQH,QAAQM,MAAD;UAChB;QACF;SACAb,YAhBoB;MAiBvBe,eAAe,MAAM;AACnB,cAAMC,cAAclB,QAAQmB,QAAQlB,GAAhB;AAEpB,YAAI,CAACiB,aAAa;AAChB;QACD;AAED,eAAOZ,aAAYY,WAAD;;MAEpBE,cAAc,MAAM;AAClBpB,gBAAQqB,WAAWpB,GAAnB;MACD;;EAEJ;AAED,SAAO;IACLY,eAAeS;IACfL,eAAe,MAAMM;IACrBH,cAAcE;;AAEjB;AAED,SAASR,SACPU,MACAC,OAAO,KACP;AACA,MAAIC,QAA8C;AAClD,MAAIC;AACJ,SAAO,YAAaC,MAAa;AAC/BD,aAASC;AACT,QAAIF,UAAU,MAAM;AAClBA,cAAQG,WAAW,MAAM;AACvBL,aAAK,GAAGG,MAAJ;AACJD,gBAAQ;SACPD,IAHe;IAInB;;AAEJ;AAGD,SAASH,OAAO;AAAA;;;ACrDhB,IAAMQ,sBAA8C,CAClD,SACA,WACA,SAHkD;AAMpD,SAASC,qBAAqBC,WAA4B;AACxD,SAAOF,oBAAoBG,SAASD,SAA7B;AACR;AAQM,eAAeE,0BAA0B;EAC9CC;EACAC;EACAC,SAAS,MAAO,KAAK,KAAK;EAC1BC,SAAS;EACTC;AAL8C,GAMT;AACrC,MAAI;AACF,UAAMC,kBAAkB,MAAMJ,UAAUK,cAAV;AAE9B,QAAID,iBAAiB;AACnB,UAAIA,gBAAgBE,WAAW;AAC7B,cAAMC,UAAUC,KAAKC,IAAL,IAAaL,gBAAgBE,YAAYL;AACzD,cAAMS,SAASN,gBAAgBF,WAAWA;AAC1C,YAAIK,WAAWG,QAAQ;AACrBV,oBAAUW,aAAV;QACD,OAAM;AACLC,kBAAQb,aAAaK,gBAAgBS,aAAaV,cAA3C;QACR;MACF,OAAM;AACLH,kBAAUW,aAAV;MACD;IACF;WACMG,KAAK;AACZ,QAAIC,MAAuC;AACzChB,kBAAYiB,UAAZ,EAAwBC,MAAMH,GAA9B;AACAf,kBACGiB,UADH,EAEGE,KACC,0IAHJ;IAKD;AACDlB,cAAUW,aAAV;EACD;AACF;AAOM,eAAeQ,uBAAuB;EAC3CpB;EACAC;EACAE,SAAS;EACTkB;AAJ2C,GAKT;AAClC,QAAMC,gBAAiC;IACrCnB;IACAI,WAAWE,KAAKC,IAAL;IACXI,aAAaS,UAAUvB,aAAaqB,gBAAd;;AAGxB,QAAMpB,UAAUqB,cAAcA,aAAxB;AACP;AAMM,SAASE,4BACdC,OACA;AACA,QAAMC,wBAAwBD,MAAMzB,YACjC2B,cAD2B,EAE3BC,UAAWC,WAAU;AACpB,QAAIjC,qBAAqBiC,MAAMC,IAAP,GAAc;AACpCV,6BAAuBK,KAAD;IACvB;EACF,CAN2B;AAQ9B,QAAMM,2BAA2BN,MAAMzB,YACpCgC,iBAD8B,EAE9BJ,UAAWC,WAAU;AACpB,QAAIjC,qBAAqBiC,MAAMC,IAAP,GAAc;AACpCV,6BAAuBK,KAAD;IACvB;EACF,CAN8B;AAQjC,SAAO,MAAM;AACXC,0BAAqB;AACrBK,6BAAwB;;AAE3B;AAKM,SAASE,mBACdR,OAC6B;AAC7B,MAAIS,kBAAkB;AACtB,MAAIC;AACJ,QAAMC,cAAc,MAAM;AACxBF,sBAAkB;AAClBC,qCAA6B,OAA7B,SAAAA,8BAA6B;EAC9B;AAGD,QAAME,iBAAiBtC,0BAA0B0B,KAAD,EAAQa,KAAK,MAAM;AACjE,QAAI,CAACJ,iBAAiB;AAEpBC,sCAAgCX,4BAA4BC,KAAD;IAC5D;EACF,CALsB;AAOvB,SAAO,CAACW,aAAaC,cAAd;AACR;;;;;;ACjID,IAAAE,SAAuB;AAgCvB,aAAwB;AASxB,IAAAC,UAAwB;AAGxB,UAAqB;AAqPrB,2BAAiD;AAejD,aAAwB;AA6CxB,IAAAC,wBAAsF;AAkBtF,aAAwB;AAOxB,aAAwB;AAIxB,aAAwB;AAiExB,aAAwB;AAyExB,aAAwB;AACxB,IAAAC,wBAAsF;AAgFtF,cAAyB;AAiEzB,cAAyB;AA2HzB,mBAA4C;AAY5C,cAAyB;AA0DzB,cAAyB;AA+FzB,cAAyB;AA+EzB,cAAyB;AA6BzB,cAAyB;AAgGzB,cAAyB;AAyIzB,cAAyB;AA2HzB,cAAyB;AA0uBzB,cAAyB;AAoRzB,cAAyB;AA96EzB,SAASC,cAAa;AAAA,EACpB,cAAc,IAAI,YAAY;AAAA,IAC5B,gBAAgB;AAAA,MACd,SAAS;AAAA,QACP,WAAW,MAAM,KAAK,KAAK;AAAA,QAC3B,aAAa;AAAA,QACb,sBAAsB;AAAA,QACtB,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF,CAAC;AAAA,EACD,UAAU,cAAc;AAAA,IACtB,SAAS,OAAO,WAAW,eAAe,OAAO,eAAe,OAAO,eAAe;AAAA,EACxF,CAAC;AAAA,EACD,YAAY,OAAO,WAAW,cAAc,2BAA2B;AAAA,IACrE,KAAK;AAAA,IACL;AAAA,IACA,WAAW,CAAC,MAAM;AAAA,IAClB,aAAa,CAAC,MAAM;AAAA,EACtB,CAAC,IAAI;AAAA,EACL,GAAG;AACL,GAAG;AACD,QAAM,SAAS,aAAiB;AAAA,IAC9B,GAAG;AAAA,IACH;AAAA,EACF,CAAC;AACD,MAAI;AACF,uBAAmB;AAAA,MACjB;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,QAChB,sBAAsB,CAAC,UAAU,MAAM,cAAc,KAAK,MAAM,SAAS,CAAC,EAAE,YAAY;AAAA,MAC1F;AAAA,IACF,CAAC;AACH,SAAO,OAAO,OAAO,QAAQ,EAAE,YAAY,CAAC;AAC9C;AAKA,IAAI,UAAgB,qBAAc,MAAM;AACxC,IAAI,qBAA2B;AAAA,EAC7B;AACF;AACA,SAAS,YAAY;AAAA,EACnB;AAAA,EACA;AACF,GAAG;AACD,SAAa,qBAAc,QAAQ,UAAU;AAAA,IAC3C,UAAgB,qBAAc,qBAAqB;AAAA,MACjD;AAAA,MACA,QAAQ,OAAO;AAAA,MACf,SAAS;AAAA,IACX,CAAC;AAAA,IACD,OAAO;AAAA,EACT,CAAC;AACH;AACA,SAAS,YAAY;AACnB,QAAM,SAAe,kBAAW,OAAO;AACvC,MAAI,CAAC;AACH,UAAM,IAAI;AAAA,MACR;AAAA,QACE;AAAA,QACA;AAAA,MACF,EAAE,KAAK,IAAI;AAAA,IACb;AACF,SAAO;AACT;AAiBA,IAAI,wBAA4B;AAGhC,SAAS,WAAW,OAAO;AACzB,SAAO,MAAM,QAAQ,KAAK;AAC5B;AACA,SAAS,cAAc,GAAG;AACxB,MAAI,CAAC,mBAAmB,CAAC,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,QAAM,OAAO,EAAE;AACf,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK;AAClB,MAAI,CAAC,mBAAmB,IAAI,GAAG;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,CAAC,KAAK,eAAe,eAAe,GAAG;AACzC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,GAAG;AAC7B,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AACA,SAAS,eAAe,MAAM,MAAM,MAAM;AACxC,MAAI,CAAC,WAAW,IAAI,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,EAAE,GAAG,MAAM,UAAU,MAAM,SAAS,KAAK;AAAA,EAClD;AACA,SAAO,EAAE,GAAG,MAAM,UAAU,KAAK;AACnC;AACA,SAAS,eAAe,YAAY;AAClC,SAAO,KAAK;AAAA,IACV;AAAA,IACA,CAAC,GAAG,QAAQ,cAAc,GAAG,IAAI,OAAO,KAAK,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,QAAQ;AAC/E,aAAO,GAAG,IAAI,IAAI,GAAG;AACrB,aAAO;AAAA,IACT,GAAG,CAAC,CAAC,IAAI,OAAO,QAAQ,WAAW,IAAI,SAAS,IAAI;AAAA,EACtD;AACF;AACA,SAAS,iBAAiB,mBAAmB,QAAQ;AACnD,MAAI,OAAO,sBAAsB,YAAY;AAC3C,WAAO,kBAAkB,GAAG,MAAM;AAAA,EACpC;AACA,SAAO,CAAC,CAAC;AACX;AACA,SAAS,YAAY,QAAQ,UAAU;AACrC,QAAM,gBAAgB,CAAC;AACvB,SAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AACnC,WAAO,eAAe,eAAe,KAAK;AAAA,MACxC,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,KAAK,MAAM;AACT,iBAAS,aAAa,IAAI,GAAG;AAC7B,eAAO,OAAO,GAAG;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAGA,SAAS,aAAa,SAAS,UAAU;AACvC,QAAM,cAAc,eAAe,EAAE,SAAS,QAAQ,QAAQ,CAAC;AAC/D,QAAM,cAAc,eAAe;AACnC,QAAM,qBAAqB,2BAA2B;AACtD,QAAM,mBAAmB,YAAY,oBAAoB;AAAA,IACvD,GAAG;AAAA,IACH;AAAA,EACF,CAAC;AACD,mBAAiB,qBAAqB,cAAc,gBAAgB;AACpE,MAAI,iBAAiB,SAAS;AAC5B,qBAAiB,UAAU,cAAc;AAAA,MACvC,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,MAAI,iBAAiB,WAAW;AAC9B,qBAAiB,YAAY,cAAc;AAAA,MACzC,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,MAAI,iBAAiB,WAAW;AAC9B,qBAAiB,YAAY,cAAc;AAAA,MACzC,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,MAAI,iBAAiB,UAAU;AAC7B,QAAI,OAAO,iBAAiB,cAAc,UAAU;AAClD,uBAAiB,YAAY;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,iBAAiB,YAAY,iBAAiB,kBAAkB;AAClE,QAAI,CAAC,mBAAmB,QAAQ,GAAG;AACjC,uBAAiB,eAAe;AAAA,IAClC;AAAA,EACF;AACA,QAAM,CAAC,QAAQ,IAAW;AAAA,IACxB,MAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,SAAS,SAAS,oBAAoB,gBAAgB;AAC5D;AAAA,IACS;AAAA,MACL,CAAC,kBAAkB,cAAc,MAAM,SAAS,SAAS,UAAU,cAAc,WAAW,aAAa,CAAC;AAAA,MAC1G,CAAC,UAAU,WAAW;AAAA,IACxB;AAAA,IACA,MAAM,SAAS,iBAAiB;AAAA,IAChC,MAAM,SAAS,iBAAiB;AAAA,EAClC;AACA,EAAO,kBAAU,MAAM;AACrB,uBAAmB,WAAW;AAAA,EAChC,GAAG,CAAC,kBAAkB,CAAC;AACvB,EAAO,kBAAU,MAAM;AACrB,aAAS,WAAW,kBAAkB,EAAE,WAAW,MAAM,CAAC;AAAA,EAC5D,GAAG,CAAC,kBAAkB,QAAQ,CAAC;AAC/B,MAAI,iBAAiB,YAAY,OAAO,aAAa,OAAO,cAAc,CAAC,aAAa;AACtF,UAAM,SAAS,gBAAgB,gBAAgB,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM;AA5NxE;AA6NM,6BAAiB,cAAjB,0CAA6B;AAC7B,6BAAiB,cAAjB,0CAA6B,MAAM;AAAA,IACrC,CAAC,EAAE,MAAM,CAAC,UAAU;AA/NxB;AAgOM,yBAAmB,WAAW;AAC9B,6BAAiB,YAAjB,0CAA2B;AAC3B,6BAAiB,cAAjB,0CAA6B,QAAQ;AAAA,IACvC,CAAC;AAAA,EACH;AACA,MAAI,OAAO,WAAW,CAAC,mBAAmB,QAAQ,KAAK,CAAC,OAAO,cAAc,iBAAiB,iBAAiB,kBAAkB;AAAA,IAC/H,OAAO;AAAA,IACP,SAAS,gBAAgB;AAAA,EAC3B,CAAC,GAAG;AACF,UAAM,OAAO;AAAA,EACf;AACA,QAAM,SAAS,OAAO,WAAW,aAAa,OAAO,gBAAgB,SAAS,SAAS,OAAO;AAC9F,QAAM,SAAS,WAAW;AAC1B,QAAM,YAAY,WAAW,aAAa,OAAO,gBAAgB;AACjE,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAIA,SAAS,iBAAiB,MAAM,MAAM,MAAM;AAC1C,QAAM,gBAAgB,eAAe,MAAM,MAAM,IAAI;AACrD,QAAM,YAAY;AAAA,IAChB,EAAE,SAAS,oBAAoB,GAAG,cAAc;AAAA,IAChD;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,MAAM,UAAU;AAAA,IAChB,OAAO,UAAU;AAAA,IACjB,eAAe,UAAU;AAAA,IACzB,aAAa,UAAU;AAAA,IACvB,aAAa,UAAU;AAAA,IACvB,SAAS,UAAU;AAAA,IACnB,WAAW,UAAU;AAAA,IACrB,qBAAqB,UAAU;AAAA,IAC/B,YAAY,UAAU;AAAA,IACtB,oBAAoB,UAAU;AAAA,IAC9B,QAAQ,UAAU;AAAA,IAClB,WAAW,UAAU;AAAA,IACrB,cAAc,UAAU;AAAA,IACxB,WAAW,UAAU;AAAA,IACrB,SAAS,UAAU;AAAA,IACnB,QAAQ,UAAU;AAAA,IAClB,UAAU;AAAA,MACR,eAAe,UAAU;AAAA,MACzB,gBAAgB,UAAU;AAAA,MAC1B,cAAc,UAAU;AAAA,MACxB,qBAAqB,UAAU;AAAA,MAC/B,gBAAgB,UAAU;AAAA,MAC1B,UAAU,UAAU;AAAA,MACpB,mBAAmB,UAAU;AAAA,MAC7B,gBAAgB,UAAU;AAAA,MAC1B,gBAAgB,UAAU;AAAA,MAC1B,SAAS,UAAU;AAAA,MACnB,QAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AACA,SAAO,CAAC,UAAU,iBAAiB,sBAAsB,YAAY,QAAQ,UAAU,QAAQ,IAAI;AACrG;AAOA,SAASC,aAAY,MAAM,MAAM,MAAM;AACrC,QAAM,UAAU,kBAAkB,MAAM,MAAM,IAAI;AAClD,SAAO,YAAa,EAAE,SAAS,oBAAoB,GAAG,QAAQ,CAAC;AACjE;AAIA,SAAS,SAAS,MAAM,MAAM,MAAM;AAClC,QAAM,gBAAgB,eAAe,MAAM,MAAM,IAAI;AACrD,QAAM,YAAY,aAAa,EAAE,SAAS,oBAAoB,GAAG,cAAc,GAAG,aAAa;AAC/F,QAAM,SAAS;AAAA,IACb,MAAM,UAAU;AAAA,IAChB,OAAO,UAAU;AAAA,IACjB,aAAa,UAAU;AAAA,IACvB,SAAS,UAAU;AAAA,IACnB,WAAW,UAAU;AAAA,IACrB,qBAAqB,UAAU;AAAA,IAC/B,YAAY,UAAU;AAAA,IACtB,QAAQ,UAAU;AAAA,IAClB,WAAW,UAAU;AAAA,IACrB,cAAc,UAAU;AAAA,IACxB,WAAW,UAAU;AAAA,IACrB,SAAS,UAAU;AAAA,IACnB,QAAQ,UAAU;AAAA,IAClB,UAAU;AAAA,MACR,eAAe,UAAU;AAAA,MACzB,gBAAgB,UAAU;AAAA,MAC1B,cAAc,UAAU;AAAA,MACxB,qBAAqB,UAAU;AAAA,MAC/B,gBAAgB,UAAU;AAAA,MAC1B,UAAU,UAAU;AAAA,MACpB,mBAAmB,UAAU;AAAA,MAC7B,gBAAgB,UAAU;AAAA,MAC1B,gBAAgB,UAAU;AAAA,MAC1B,SAAS,UAAU;AAAA,MACnB,QAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AACA,SAAO,CAAC,UAAU,iBAAiB,sBAAsB,YAAY,QAAQ,UAAU,QAAQ,IAAI;AACrG;AAIA,IAAI,kBAAkB,MAAM,eAAgB,EAAE,SAAS,mBAAmB,CAAC;AAK3E,SAAS,gBAAgB;AAAA,EACvB;AACF,IAAI,CAAC,GAAG;AACN,aAAO;AAAA,IACL,CAAC,OAAO,kBAAkB,EAAE,QAAQ,GAAG,EAAE;AAAA,IACzC,MAAM,gBAAgB,EAAE,QAAQ,CAAC;AAAA,IACjC,MAAM,gBAAgB,EAAE,QAAQ,CAAC;AAAA,IACjC,CAAC,MAAM;AAAA,IACP,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE;AAAA,EACxB;AACF;AAKA,SAAS,SAAS,EAAE,QAAQ,GAAG;AAC7B,SAAO,CAAC,EAAE,QAAQ,gBAAgB,SAAS,SAAS,MAAM,CAAC;AAC7D;AACA,SAAS,QAAQ;AAAA,EACf,UAAU,CAAC,EAAE,QAAQ,CAAC;AACxB,GAAG;AACD,SAAO,gBAAgB,EAAE,QAAQ,CAAC;AACpC;AACA,SAAS,gBAAgB;AAAA,EACvB,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,EAAE,UAAU,IAAI,WAAW;AACjC,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,QAAM,oBAAoB,SAAS,SAAS,EAAE,QAAQ,CAAC,GAAG,SAAS;AAAA,IACjE,WAAW;AAAA,IACX,SAAS,QAAQ,SAAS;AAAA,IAC1B,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,cAAc,gBAAgB;AACpC,EAAO,iBAAU,MAAM;AACrB,UAAM,UAAU,kBAAkB,EAAE,QAAQ,GAAG,CAAC,iBAAiB;AAC/D,UAAI;AACF,oBAAY,kBAAkB,SAAS,EAAE,QAAQ,CAAC,CAAC;AAAA;AAEnD,oBAAY,cAAc,SAAS,EAAE,QAAQ,CAAC,CAAC;AAAA,IACnD,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,SAAO;AACT;AAQA,SAAS,yBAAyB,EAAE,QAAQ,IAAI,CAAC,GAAG;AAClD,aAAO,sBAAAC;AAAA,IACL,CAAC,OAAO,2BAA2B,EAAE,QAAQ,GAAG,EAAE;AAAA,IAClD,MAAM,yBAAyB,EAAE,QAAQ,CAAC;AAAA,IAC1C,MAAM,yBAAyB,EAAE,QAAQ,CAAC;AAAA,IAC1C,CAAC,MAAM;AAAA,IACP,CAAC,GAAG,OAAM,uBAAG,UAAQ,uBAAG;AAAA,EAC1B;AACF;AAGA,SAAS,WAAW,EAAE,QAAQ,IAAI,CAAC,GAAG;AACpC,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,SAAO,aAAa,MAAM;AAC5B;AAIA,SAAS,iBAAiB;AACxB,QAAM,CAAC,EAAE,WAAW,IAAW,kBAAW,CAAC,MAAM,IAAI,GAAG,CAAC;AACzD,SAAO;AACT;AAQA,SAAS,UAAU,EAAE,SAAS,SAAS,GAAG;AACxC,SAAO,CAAC,EAAE,QAAQ,eAAe,SAAS,SAAS,CAAC;AACtD;AACA,SAAS,SAAS;AAAA,EAChB,UAAU,CAAC,EAAE,QAAQ,CAAC;AACxB,GAAG;AACD,SAAO,iBAAiB,EAAE,QAAQ,CAAC;AACrC;AACA,SAAS,eAAe;AAAA,EACtB,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,wBAAwB,yBAAyB,EAAE,QAAQ,CAAC;AAClE,QAAM,cAAc,gBAAgB;AACpC,EAAO,iBAAU,MAAM;AACrB,QAAI,CAAC;AACH;AACF,QAAI,CAAC,SAAS,CAAC;AACb;AACF,UAAM,gBAAgB,yBAAyB;AAC/C,UAAM,UAAU,cAAc,iBAAiB;AAAA,MAC7C,eAAe,CAAC,gBAAgB;AAC9B,YAAI;AACF,sBAAY,aAAa,UAAU,EAAE,SAAS,SAAS,CAAC,GAAG,WAAW;AACxE,YAAI;AACF,kBAAQ,WAAW;AAAA,MACvB;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AACD,WAAO;AAAA,EACT,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,SAAS,UAAU,EAAE,UAAU,QAAQ,CAAC,GAAG,UAAU;AAAA,IAC1D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAKA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,CAAC,EAAE,QAAQ,WAAW,SAAS,aAAa,SAAS,CAAC;AAC/D;AACA,SAAS,SAAS;AAAA,EAChB,UAAU,CAAC,EAAE,SAAS,YAAY,CAAC;AACrC,GAAG;AACD,SAAO,aAAa,EAAE,SAAS,YAAY,CAAC;AAC9C;AACA,SAAS,WAAW;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,QAAM,YAAmB;AAAA,IACvB,MAAM,UAAU;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD,CAAC,SAAS,aAAa,QAAQ;AAAA,EACjC;AACA,QAAM,eAAe,SAAS,WAAW,UAAU;AAAA,IACjD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,uBAAqB;AAAA,IACnB;AAAA,IACA,SAAS,QAAQ,WAAW,KAAK;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AAGA,SAAS,qBAAqB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,UAAU;AACZ,GAAG;AACD,QAAM,cAAc,gBAAgB;AACpC,QAAM,UAAiB;AAAA,IACrB,MAAM,YAAY,kBAAkB,EAAE,UAAU,WAAW,GAAG,EAAE,eAAe,MAAM,CAAC;AAAA,IACtF,CAAC,aAAa,UAAU;AAAA,EAC1B;AACA,iBAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA,SAAS,UAAU,UAAU;AAAA,IAC7B,UAAU,UAAU,SAAS;AAAA,EAC/B,CAAC;AACH;AAMA,IAAI,iBAAiB,CAAC,QAAQ,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG;AAC3E,SAAS,gCAAgC,WAAW,aAAa,oBAAoB,aAAa,UAAU,WAAW;AACrH,QAAM,cAAqB,cAAO,CAAC,CAAC;AACpC,QAAM,aAAS,sBAAAC;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,MAAM;AAAA,IACP,CAAC,GAAG,MAAM;AACR,UAAI,eAAe,CAAC,KAAK,eAAe,CAAC,KAAK,YAAY,QAAQ,QAAQ;AACxE,mBAAW,OAAO,YAAY,SAAS;AACrC,gBAAM,QAAQ;AAAA,YACZ,EAAE,GAAG;AAAA,YACL,EAAE,GAAG;AAAA,UACP;AACA,cAAI,CAAC;AACH,mBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,GAAG,CAAC;AAAA,IACrB;AAAA,EACF;AACA,MAAI,eAAe,MAAM,GAAG;AAC1B,UAAM,gBAAgB,EAAE,GAAG,OAAO;AAClC,WAAO;AAAA,MACL;AAAA,MACA,OAAO,QAAQ,aAAa,EAAE;AAAA,QAC5B,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACrB,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,CAAC,GAAG,GAAG;AAAA,cACL,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,KAAK,MAAM;AACT,oBAAI,CAAC,YAAY,QAAQ,SAAS,GAAG,GAAG;AACtC,8BAAY,QAAQ,KAAK,GAAG;AAAA,gBAC9B;AACA,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,SAAS,WAAW,EAAE,WAAW,aAAa,IAAI,CAAC,GAAG;AACpD,QAAM,SAAS,UAAU;AACzB,QAAM,gBAAuB;AAAA,IAC3B,CAAC,aAAa,aAAa,QAAQ;AAAA,IACnC,CAAC,MAAM;AAAA,EACT;AACA,QAAM,UAAU,gCAAgC,eAAe,UAAU;AACzE,QAAM,qBAA4B,cAAO;AACzC,QAAM,kBAAkB,mBAAmB;AAC3C,EAAO,iBAAU,MAAM;AACrB,SAAI,mDAAiB,YAAW,eAAe,QAAQ,WAAW,aAAa;AAC7E,6CAAY;AAAA,QACV,SAAS,QAAQ;AAAA,QACjB,WAAW,QAAQ;AAAA,QACnB,gBAAe,mDAAiB,YAAW,mBAAkB,mDAAiB,YAAW;AAAA,MAC3F;AAAA,IACF;AACA,SAAI,mDAAiB,YAAW,eAAe,QAAQ,WAAW,gBAAgB;AAChF;AAAA,IACF;AACA,uBAAmB,UAAU;AAAA,EAC/B,GAAG,CAAC,WAAW,cAAc,iBAAiB,OAAO,CAAC;AACtD,SAAO;AACT;AAKA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO;AAAA,IACL;AAAA,MACE,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,SAAS;AAAA,EAChB,UAAU,CAAC,EAAE,SAAS,SAAS,aAAa,MAAM,CAAC;AACrD,GAAG;AACD,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,qBAAqB;AACvC,SAAO,aAAa,EAAE,SAAS,SAAS,aAAa,MAAM,CAAC;AAC9D;AACA,SAAS,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,QAAM,YAAoB;AAAA,IACxB,MAAM,UAAU,EAAE,SAAS,SAAS,aAAa,UAAU,MAAM,CAAC;AAAA,IAClE,CAAC,SAAS,SAAS,aAAa,UAAU,KAAK;AAAA,EACjD;AACA,QAAM,eAAe,SAAS,WAAW,UAAU;AAAA,IACjD;AAAA,IACA,SAAS,QAAQ,WAAW,OAAO;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,uBAAqB;AAAA,IACnB;AAAA,IACA,SAAS,QAAQ,WAAW,SAAS,OAAO;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AAKA,IAAI,cAAc,CAAC,SAAS,CAAC,EAAE,QAAQ,WAAW,GAAG,KAAK,CAAC;AAC3D,IAAI,aAAa,CAAC,SAAS;AACzB,QAAM,EAAE,WAAW,QAAQ,IAAI;AAC/B,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,uBAAuB;AACzC,SAAO,QAAQ,EAAE,WAAW,QAAQ,CAAC;AACvC;AACA,SAAS,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,SAAS,UAAU;AACzB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIF,aAAY,YAAY,EAAE,WAAW,QAAQ,CAAC,GAAG,YAAY;AAAA,IAC/D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,WAAmB;AAAA,IACvB,CAAC,SAAS;AACR,aAAO,OAAO;AAAA,QACZ,UAAS,6BAAM,YAAW;AAAA,QAC1B,YAAW,6BAAM,cAAa;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,IACA,CAAC,SAAS,WAAW,MAAM;AAAA,EAC7B;AACA,QAAM,eAAuB;AAAA,IAC3B,CAAC,SAAS;AACR,aAAO,YAAY;AAAA,QACjB,UAAS,6BAAM,YAAW;AAAA,QAC1B,YAAW,6BAAM,cAAa;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,IACA,CAAC,SAAS,WAAW,WAAW;AAAA,EAClC;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA,YAAY,OAAO;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,kBAAkB,uCAAW;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAI,eAAe,CAAC,EAAE,QAAQ,aAAa,CAAC;AAC5C,IAAI,cAAc,MAAM,WAAW;AACnC,SAAS,cAAc;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,IAAIA,aAAY,cAAc,aAAa;AAAA,IACzC,GAAG,UAAU;AAAA,MACX,QAAQ,QAAQ,YAAY,SAAS;AACnC,gBAAQ,QAAQ,OAAO;AAAA,MACzB;AAAA,IACF,IAAI,CAAC;AAAA,IACL;AAAA,IACA,GAAG,YAAY;AAAA,MACb,UAAU,OAAO,QAAQ,YAAY,SAAS;AAC5C,kBAAU,QAAQ,OAAO;AAAA,MAC3B;AAAA,IACF,IAAI,CAAC;AAAA,IACL,GAAG,YAAY;AAAA,MACb,UAAU,OAAO,YAAY,SAAS;AACpC,kBAAU,OAAO;AAAA,MACnB;AAAA,IACF,IAAI,CAAC;AAAA,EACP,CAAC;AACD,SAAO;AAAA,IACL,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKA,SAAS,aAAa;AACpB,QAAM,SAAS,UAAU;AACzB,QAAM,oBAAgB,aAAAG;AAAA,IACpB,CAAC,aAAa,aAAa,QAAQ;AAAA,IACnC,CAAC,MAAM;AAAA,EACT;AACA,SAAO,gCAAgC,eAAe,UAAU;AAClE;AAKA,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,QAAQ,eAAe,GAAG,KAAK,CAAC;AAChE,IAAI,cAAc,CAAC,SAAS;AAC1B,QAAM,EAAE,QAAQ,IAAI;AACpB,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,qBAAqB;AACvC,SAAO,YAAY,EAAE,QAAQ,CAAC;AAChC;AACA,SAAS,eAAe;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIH,aAAY,aAAa,EAAE,QAAQ,CAAC,GAAG,aAAa;AAAA,IACtD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,eAAuB;AAAA,IAC3B,CAAC,SAAS,OAAO,QAAQ,EAAE,QAAQ,CAAC;AAAA,IACpC,CAAC,SAAS,MAAM;AAAA,EAClB;AACA,QAAM,mBAA2B;AAAA,IAC/B,CAAC,SAAS,YAAY,QAAQ,EAAE,QAAQ,CAAC;AAAA,IACzC,CAAC,SAAS,WAAW;AAAA,EACvB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKA,SAAS,aAAa,EAAE,QAAQ,OAAO,SAAS,YAAY,GAAG;AAC7D,SAAO;AAAA,IACL,EAAE,QAAQ,iBAAiB,QAAQ,OAAO,SAAS,YAAY;AAAA,EACjE;AACF;AACA,SAAS,YAAY,MAAM;AACzB,QAAM,EAAE,QAAQ,OAAO,aAAa,QAAQ,IAAI;AAChD,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,oBAAoB;AACtC,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,mBAAmB;AACrC,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,yBAAyB;AAC3C,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,qBAAqB;AACvC,SAAO,cAAc;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,SAAS,iBAAiB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIA;AAAA,IACF,aAAa;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAyB;AAAA,IAC7B,CAAC,SAAS,OAAO;AAAA,MACf,SAAQ,6BAAM,WAAU;AAAA,MACxB,QAAO,6BAAM,UAAS;AAAA,MACtB,UAAS,6BAAM,YAAW;AAAA,MAC1B,cAAa,6BAAM,gBAAe;AAAA,IACpC,CAAC;AAAA,IACD,CAAC,QAAQ,OAAO,aAAa,SAAS,MAAM;AAAA,EAC9C;AACA,QAAM,qBAA6B;AAAA,IACjC,CAAC,SAAS,YAAY;AAAA,MACpB,SAAQ,6BAAM,WAAU;AAAA,MACxB,QAAO,6BAAM,UAAS;AAAA,MACtB,UAAS,6BAAM,YAAW;AAAA,MAC1B,cAAa,6BAAM,gBAAe;AAAA,IACpC,CAAC;AAAA,IACD,CAAC,QAAQ,OAAO,aAAa,SAAS,WAAW;AAAA,EACnD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKA,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,QAAQ,iBAAiB,GAAG,KAAK,CAAC;AAClE,IAAI,cAAc,CAAC,SAAS;AAC1B,QAAM,EAAE,QAAQ,IAAI;AACpB,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,qBAAqB;AACvC,SAAO,cAAc,EAAE,QAAQ,CAAC;AAClC;AACA,SAAS,iBAAiB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AA1/BR;AA2/BE,QAAM,SAAS,UAAU;AACzB,QAAM,cAAc,eAAe;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIA,aAAY,aAAa,EAAE,QAAQ,CAAC,GAAG,aAAa;AAAA,IACtD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,iBAAyB;AAAA,IAC7B,CAAC,aAAa,OAAO,EAAE,SAAS,YAAY,QAAQ,CAAC;AAAA,IACrD,CAAC,SAAS,MAAM;AAAA,EAClB;AACA,QAAM,sBAA8B;AAAA,IAClC,CAAC,aAAa,YAAY,EAAE,SAAS,YAAY,QAAQ,CAAC;AAAA,IAC1D,CAAC,SAAS,WAAW;AAAA,EACvB;AACA,EAAQ,kBAAU,MAAM;AACtB,UAAM,UAAU,OAAO;AAAA,MACrB,CAAC,EAAE,QAAQ,UAAU,OAAO;AAAA,QAC1B;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,WAAW,CAAC;AACxB,MAAI;AACJ,MAAI;AACJ,QAAM,sBAAsB,CAAC,GAAC,YAAO,cAAP,mBAAkB;AAChD,MAAI,mCAAmC,qBAAqB;AAC1D,qBAAiB;AACjB,yBAAqB;AAAA,EACvB;AACA,SAAO;AAAA,IACL,QAAQ,OAAO,UAAU,CAAC;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB,uCAAW;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,EACF;AACF;AAIA,SAAS,iBAAiB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,wBAAwB,yBAAyB,EAAE,QAAQ,CAAC;AAClE,QAAM,UAAkB,eAAO;AAC/B,EAAQ,kBAAU,MAAM;AACtB,QAAI,CAAC,OAAO,CAAC,WAAW,CAAC;AACvB;AACF,UAAM,gBAAgB,yBAAyB;AAC/C,YAAQ,UAAU,cAAc,mBAAmB;AAAA,MACjD;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,IACV,CAAC;AACD,WAAO,QAAQ;AAAA,EACjB,GAAG,CAAC,KAAK,SAAS,WAAW,aAAa,KAAK,+DAAuB,GAAG,CAAC;AAC1E,SAAO,QAAQ;AACjB;AAMA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO;AAAA,IACL;AAAA,MACE,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,SAAS;AAAA,EAChB;AACF,GAAG;AACD,SAAO,CAAC;AAAA,IACN,UAAU,CAAC,EAAE,cAAc,aAAa,SAAS,CAAC;AAAA,IAClD;AAAA,EACF,MAAM;AACJ,WAAO,cAAc;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,UAAU,aAAa,MAAM;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;AACA,SAAS,yBAAyB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,WAAW;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB,CAAC,SAAS,YAAY,UAAW,SAAS,OAAO,IAAI,UAAU,iBAAiB,SAAS,OAAO;AAAA,EACpH;AACF,GAAG;AACD,QAAM,YAAoB;AAAA,IACxB,MAAM,UAAU,EAAE,cAAc,aAAa,UAAU,UAAU,SAAS,CAAC;AAAA,IAC3E,CAAC,cAAc,aAAa,UAAU,UAAU,QAAQ;AAAA,EAC1D;AACA,QAAM,UAAkB,gBAAQ,MAAM;AACpC,UAAM,WAAW,QAAQ,YAAY,SAAS;AAC9C,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,QAAQ,CAAC;AACxB,SAAO,iBAAiB,WAAW,SAAS,EAAE,UAAU,CAAC,GAAG;AAAA,IAC1D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,SAAS,uBAAuB,IAAI;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,YAAY,CAAC,OAAO,MAAM,CAAC,GAAG,MAAM,OAAO,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,UAAU;AACxE,WAAO,cAAc,cAAc,QAAQ,QAAQ,OAAO,UAAU,QAAQ,QAAQ,OAAO;AAAA,EAC7F,CAAC,EAAE,OAAO,CAAC,UAAU,SAAS,CAAC,EAAE,QAAQ,EAAE;AAC3C,SAAO;AAAA,IACL;AAAA,IACA,iBAAiB,UAAU,OAAO;AAChC,cAAO,qCAAU,YAAW,UAAU,MAAM,SAAS;AAAA,IACvD;AAAA,EACF;AACF;AAMA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO;AAAA,IACL;AAAA,MACE,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,SAAS,EAAE,IAAI,GAAG;AACzB,SAAO,OAAO;AAAA,IACZ,UAAU;AAAA,MACR,EAAE,SAAS,SAAS,MAAM,aAAa,UAAU,SAAS,aAAa;AAAA,IACzE;AAAA,EACF,MAAM;AACJ,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,iBAAiB;AACnC,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,qBAAqB;AACvC,WAAO,MAAM,aAAa;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,KAAK;AAAA,EACR;AACF;AACA,SAAS,gBAAgB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EACA,SAAS;AAAA,EACT,SAAS,WAAW;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB,CAAC,SAAS,YAAY,UAAW,SAAS,OAAO,IAAI,UAAU,iBAAkB,SAAS,OAAO;AAAA,EACrH;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,QAAM,EAAE,MAAM,aAAa,IAAI,eAAe;AAAA,IAC5C;AAAA,IACA,SAAS,SAAS;AAAA,IAClB,UAAU,SAAS,eAAe,SAAS;AAAA,IAC3C;AAAA,EACF,CAAC;AACD,QAAM,cAAc,uBAAuB;AAC3C,QAAM,YAAoB;AAAA,IACxB,MAAM,UAAU;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,eAAe,cAAc;AAAA,MAC1C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAkB,gBAAQ,MAAM;AACpC,QAAI,WAAW,QAAQ,YAAY,OAAO,WAAW,YAAY;AACjE,QAAI;AACF,iBAAW,QAAQ,YAAY,WAAW;AAC5C,WAAO;AAAA,EACT,GAAG,CAAC,KAAK,SAAS,aAAa,cAAc,UAAU,YAAY,CAAC;AACpE,uBAAqB;AAAA,IACnB;AAAA,IACA,SAAS,QAAQ,WAAW,SAAS,CAAC,YAAY;AAAA,IAClD,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA,SAAS;AAAA,MACP;AAAA,IACF,CAAC;AAAA,IACD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAMA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO;AAAA,IACL;AAAA,MACE,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,aAAa,CAAC,GAAG,IAAI,CAAC,EAAE,SAAS,MAAM,SAAS,UAAU,aAAa,OAAO;AAAA,QACxF;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACF,EAAE;AAAA,IACJ;AAAA,EACF;AACF;AACA,SAAS,SAAS,EAAE,KAAK,GAAG;AAC1B,SAAO,CAAC;AAAA,IACN,UAAU,CAAC,EAAE,cAAc,aAAa,UAAU,WAAW,WAAW,CAAC;AAAA,EAC3E,MAAM;AACJ,UAAM,YAAY,WAAW,IAAI,CAAC,UAAU,OAAO;AAAA,MACjD,GAAG;AAAA,MACH,KAAK,KAAK,CAAC;AAAA,IACb,EAAE;AACF,WAAO,cAAe;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,iBAAiB;AAAA,EACxB,cAAc;AAAA,EACd,aAAa;AAAA,EACb;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA,SAAS,WAAW;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB,CAAC,SAAS,YAAY,UAAW,SAAS,OAAO,IAAI,UAAU,iBAAkB,SAAS,OAAO;AAAA,EACrH;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,eAAe,iBAAiB;AACtC,QAAM,EAAE,MAAM,aAAa,IAAI,eAAe;AAAA,IAC5C,SAAS,SAAS;AAAA,IAClB;AAAA,EACF,CAAC;AACD,QAAM,UAAU,WAAW;AAC3B,QAAM,cAAc,uBAAuB;AAC3C,QAAM,YAAoB;AAAA,IACxB,MAAM,UAAU;AAAA,MACd;AAAA,MACA,aAAa,eAAe,cAAc;AAAA,MAC1C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAkB,gBAAQ,MAAM;AACpC,QAAI,WAAW;AAAA,MACb,aAAY,uCAAW;AAAA,QACrB,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE;AAAA;AAAA,IAEnC;AACA,QAAI;AACF,iBAAW,QAAQ,YAAY,WAAW;AAC5C,WAAO;AAAA,EACT,GAAG,CAAC,aAAa,cAAc,WAAW,QAAQ,CAAC;AACnD,uBAAqB;AAAA,IACnB,SAAS,QAAQ,WAAW,SAAS,CAAC,YAAY;AAAA,IAClD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,QAAQ,aAAa,CAAC,GAAG;AAAA,IAC7B,CAAC,EAAE,IAAI,MAAM;AAAA,EACf;AACA,SAAO,SAAS,WAAW,SAAS,EAAE,KAAK,CAAC,GAAG;AAAA,IAC7C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAMA,SAAS,aAAa;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAG;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,MACE,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,YAAY,QAAQ;AAC3B,MAAI,OAAO,SAAS,YAAY;AAC9B,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MAAM,qBAAqB;AACvC,WAAO,cAAc;AAAA,MACnB,MAAM;AAAA,MACN,SAAS,OAAO;AAAA,IAClB,CAAC;AAAA,EACH;AACA,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,qBAAqB;AACvC,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,iBAAiB;AACnC,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,0BAA0B;AAC5C,SAAO,cAAc;AAAA,IACnB,SAAS,OAAO;AAAA,IAChB,MAAM,OAAO;AAAA,IACb,SAAS,OAAO;AAAA,IAChB,KAAK,OAAO;AAAA,IACZ,cAAc,OAAO;AAAA,IACrB,YAAY,OAAO;AAAA,IACnB,SAAS,OAAO;AAAA,IAChB,YAAY,OAAO;AAAA,IACnB,KAAK,OAAO;AAAA,IACZ,UAAU,OAAO;AAAA,IACjB,cAAc,OAAO;AAAA,IACrB,sBAAsB,OAAO;AAAA,IAC7B,OAAO,OAAO;AAAA,IACd,OAAO,OAAO;AAAA,EAChB,CAAC;AACH;AACA,SAAS,iBAAiB,QAAQ;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,6BAA6B,MAAM;AACvC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIA;AAAA,IACF,aAAa;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA;AAAA,MACE,SAAS,OAAO;AAAA,MAChB,UAAU,OAAO;AAAA,MACjB,WAAW,OAAO;AAAA,MAClB,WAAW,OAAO;AAAA,IACpB;AAAA,EACF;AACA,QAAM,QAAgB,gBAAQ,MAAM;AAClC,QAAI,OAAO,SAAS,YAAY;AAC9B,UAAI,CAAC;AACH,eAAO;AACT,aAAO,MAAM,OAAO;AAAA,QAClB,MAAM;AAAA,QACN,SAAS,OAAO;AAAA,QAChB,SAAS,OAAO;AAAA,MAClB,CAAC;AAAA,IACH;AACA,WAAO,CAAC,mBAAmB,OAAO;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,CAAC;AAAA,EACH,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,aAAqB,gBAAQ,MAAM;AACvC,QAAI,OAAO,SAAS,YAAY;AAC9B,UAAI,CAAC;AACH,eAAO;AACT,aAAO,MAAM,YAAY;AAAA,QACvB,MAAM;AAAA,QACN,SAAS,OAAO;AAAA,MAClB,CAAC;AAAA,IACH;AACA,WAAO,CAAC,mBAAmB,YAAY;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,CAAC;AAAA,EACH,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO;AAAA,IACL;AAAA,MACE,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA;AACF,GAAG;AACD,SAAO,CAAC;AAAA,IACN,UAAU;AAAA,MACR;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,MAAM;AACJ,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,iBAAiB;AACnC,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,qBAAqB;AACvC,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,0BAA0B;AAC5C,WAAO,qBAAqB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,wBAAwB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,IAAI,CAAC,GAAG;AACN,QAAM,EAAE,OAAO,YAAY,IAAI,WAAW;AAC1C,QAAM,EAAE,MAAM,aAAa,IAAI,gBAAgB,EAAE,QAAQ,CAAC;AAC1D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,kBAAkB,MAAM;AAC5B,QAAM,4BAA4B;AAAA,IAChC,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA,eAAe,2CAAa;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,qBAAqB,6CAAc,QAAQ;AAAA,MAC3C;AAAA,IACF,CAAC;AAAA,IACD,SAAS;AAAA,MACP;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD;AAAA,MACE;AAAA,MACA,SAAS;AAAA,QACP,WAAW,OAAO,WAAW,gBAAgB;AAAA,MAC/C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,OAAO,2BAA2B;AAAA,IAC9C,QAAQ;AAAA,MACN;AAAA,MACA,MAAM;AAAA,MACN,SAAS;AAAA,MACT,GAAG,0BAA0B;AAAA,IAC/B;AAAA,EACF,CAAC;AACH;AAIA,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,CAAC,EAAE,QAAQ,SAAS,SAAS,SAAS,aAAa,SAAS,CAAC;AACtE;AACA,SAAS,SAAS;AAAA,EAChB,UAAU,CAAC,EAAE,SAAS,SAAS,YAAY,CAAC;AAC9C,GAAG;AACD,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,qBAAqB;AACvC,SAAO,WAAW,EAAE,SAAS,SAAS,YAAY,CAAC;AACrD;AACA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA,SAAS;AAAA,EACT,cAAc;AAAA,EACd;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA,YAAY,MAAM,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,SAAO;AAAA,IACL,UAAU,EAAE,SAAS,SAAS,aAAa,SAAS,CAAC;AAAA,IACrD;AAAA,IACA;AAAA,MACE;AAAA,MACA,SAAS,QAAQ,WAAW,OAAO;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAIA,SAAS,WAAW,EAAE,SAAS,MAAM,SAAS,GAAG;AAC/C,SAAO,CAAC,EAAE,QAAQ,cAAc,SAAS,MAAM,SAAS,CAAC;AAC3D;AACA,SAAS,UAAU;AAAA,EACjB,UAAU,CAAC,EAAE,SAAS,KAAK,CAAC;AAC9B,GAAG;AACD,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,kBAAkB;AACpC,SAAO,gBAAgB,EAAE,SAAS,KAAK,CAAC;AAC1C;AACA,SAAS,cAAc;AAAA,EACrB;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA,YAAY,MAAM,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,SAAO,SAAS,WAAW,EAAE,SAAS,MAAM,SAAS,CAAC,GAAG,WAAW;AAAA,IAClE;AAAA,IACA,SAAS,QAAQ,WAAW,WAAW,IAAI;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,WAAW,EAAE,MAAM,SAAS,SAAS,GAAG;AAC/C,SAAO,CAAC,EAAE,QAAQ,aAAa,MAAM,SAAS,SAAS,CAAC;AAC1D;AACA,SAAS,UAAU;AAAA,EACjB,UAAU,CAAC,EAAE,MAAM,QAAQ,CAAC;AAC9B,GAAG;AACD,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,kBAAkB;AACpC,SAAO,eAAe,EAAE,MAAM,QAAQ,CAAC;AACzC;AACA,SAAS,aAAa;AAAA,EACpB;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA,YAAY,MAAM,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,SAAO,SAAS,WAAW,EAAE,MAAM,SAAS,SAAS,CAAC,GAAG,WAAW;AAAA,IAClE;AAAA,IACA,SAAS,QAAQ,WAAW,QAAQ,OAAO;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,CAAC,EAAE,QAAQ,WAAW,SAAS,SAAS,SAAS,CAAC;AAC3D;AACA,SAAS,UAAU;AAAA,EACjB,UAAU,CAAC,EAAE,SAAS,QAAQ,CAAC;AACjC,GAAG;AACD,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,qBAAqB;AACvC,SAAO,aAAa,EAAE,SAAS,QAAQ,CAAC;AAC1C;AACA,SAAS,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV;AAAA,EACA,YAAY,MAAM,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,SAAO,SAAS,WAAW,EAAE,SAAS,SAAS,SAAS,CAAC,GAAG,WAAW;AAAA,IACrE;AAAA,IACA,SAAS,QAAQ,WAAW,WAAW,OAAO;AAAA,IAC9C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,WAAW,EAAE,SAAS,MAAM,SAAS,GAAG;AAC/C,SAAO;AAAA,IACL,EAAE,QAAQ,eAAe,SAAS,MAAM,UAAU,SAAS,MAAM;AAAA,EACnE;AACF;AACA,SAAS,UAAU;AAAA,EACjB,UAAU,CAAC,EAAE,SAAS,KAAK,CAAC;AAC9B,GAAG;AACD,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,kBAAkB;AACpC,SAAO,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC3C;AACA,SAAS,eAAe;AAAA,EACtB,SAAS;AAAA,EACT;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,SAAO,SAAS,WAAW,EAAE,SAAS,MAAM,SAAS,CAAC,GAAG,WAAW;AAAA,IAClE,WAAW;AAAA,IACX,SAAS,QAAQ,WAAW,WAAW,IAAI;AAAA,IAC3C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO;AAAA,IACL;AAAA,MACE,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,UAAU,EAAE,aAAa,GAAG;AACnC,SAAO,CAAC;AAAA,IACN,UAAU;AAAA,MACR;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,MAAM;AACJ,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,gBAAgB;AAClC,WAAO,uBAAuB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,0BAA0B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,EAAE,OAAO,YAAY,IAAI,WAAW;AAC1C,QAAM,EAAE,MAAM,aAAa,IAAI,gBAAgB,EAAE,QAAQ,CAAC;AAC1D,QAAM,8BAA8B;AAAA,IAClC,WAAW;AAAA,MACT;AAAA,MACA,eAAe,2CAAa;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,qBAAqB,6CAAc,QAAQ;AAAA,IAC7C,CAAC;AAAA,IACD,UAAU,EAAE,aAAa,CAAC;AAAA,IAC1B;AAAA,MACE;AAAA,MACA,SAAS,QAAQ,WAAW,gBAAgB,EAAE;AAAA,MAC9C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,OAAO,6BAA6B;AAAA,IAChD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,GAAG,4BAA4B,YAAY,4BAA4B,OAAO;AAAA,IAChF;AAAA,EACF,CAAC;AACH;AAKA,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,QAAQ,mBAAmB,GAAG,KAAK,CAAC;AACpE,IAAI,cAAc,CAAC;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,iBAAiB;AACnC,SAAO,gBAAgB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AACA,SAAS,mBAAmB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIA;AAAA,IACF,aAAa;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,mBAA2B;AAAA,IAC/B,CAAC,SAAS,OAAO;AAAA,MACf;AAAA,MACA;AAAA,MACA,GAAG,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,uBAA+B;AAAA,IACnC,CAAC,SAAS,YAAY;AAAA,MACpB;AAAA,MACA;AAAA,MACA,GAAG,QAAQ;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,IACD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,SAAS,cAAc,CAAC,KAAK,SAAS;AAAA,IACvD,sBAAsB,SAAS,cAAc,CAAC,KAAK,SAAS;AAAA,IAC5D;AAAA,IACA;AAAA,EACF;AACF;AAIA,SAAS,WAAW,EAAE,SAAS,MAAM,SAAS,GAAG;AAC/C,SAAO,CAAC,EAAE,QAAQ,eAAe,SAAS,MAAM,SAAS,CAAC;AAC5D;AACA,SAAS,UAAU;AAAA,EACjB,UAAU,CAAC,EAAE,SAAS,KAAK,CAAC;AAC9B,GAAG;AACD,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,kBAAkB;AACpC,SAAO,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC3C;AACA,SAAS,eAAe;AAAA,EACtB,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,SAAO,SAAS,WAAW,EAAE,SAAS,MAAM,SAAS,CAAC,GAAG,WAAW;AAAA,IAClE;AAAA,IACA,SAAS,QAAQ,WAAW,IAAI;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO;AAAA,IACL;AAAA,MACE,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,UAAU;AAAA,EACjB;AACF,GAAG;AACD,SAAO,CAAC;AAAA,IACN,UAAU,CAAC,EAAE,SAAS,eAAe,MAAM,QAAQ,CAAC;AAAA,EACtD,MAAM;AACJ,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,kBAAkB;AACpC,WAAO,mBAAmB;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,sBAAsB;AAAA,EAC7B,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,SAAO;AAAA,IACL,WAAW,EAAE,SAAS,eAAe,MAAM,UAAU,QAAQ,CAAC;AAAA,IAC9D,UAAU,EAAE,WAAW,CAAC;AAAA,IACxB;AAAA,MACE;AAAA,MACA,SAAS,QAAQ,WAAW,IAAI;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAIA,SAAS,4BAA4B;AAAA,EACnC,SAAS;AAAA,EACT,UAAU;AAAA,EACV;AACF,GAAG;AACD,QAAM,UAAU,WAAW,EAAE,SAAS,SAAS,CAAC;AAChD,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,wBAAwB,yBAAyB,EAAE,QAAQ,CAAC;AAClE,EAAQ,kBAAU,MAAM;AACtB,QAAI,CAAC;AACH;AACF,UAAM,gBAAgB,yBAAyB;AAC/C,WAAO,cAAc,yBAAyB;AAAA,MAC5C,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH,GAAG,CAAC,SAAS,UAAU,cAAc,qBAAqB,CAAC;AAC7D;", "names": ["React", "useSyncExternalStore", "useRef", "useEffect", "useMemo", "createSyncStoragePersister", "storage", "key", "throttleTime", "serialize", "JSON", "stringify", "deserialize", "parse", "retry", "trySave", "persistedClient", "setItem", "error", "persistClient", "throttle", "client", "errorCount", "restoreClient", "cacheString", "getItem", "removeClient", "removeItem", "noop", "undefined", "func", "wait", "timer", "params", "args", "setTimeout", "cacheableEventTypes", "isCacheableEventType", "eventType", "includes", "persistQueryClientRestore", "queryClient", "persister", "maxAge", "buster", "hydrateOptions", "persistedClient", "restoreClient", "timestamp", "expired", "Date", "now", "busted", "removeClient", "hydrate", "clientState", "err", "process", "<PERSON><PERSON><PERSON><PERSON>", "error", "warn", "persistQueryClientSave", "dehydrateOptions", "persistClient", "dehydrate", "persistQueryClientSubscribe", "props", "unsubscribeQueryCache", "get<PERSON><PERSON><PERSON><PERSON>ache", "subscribe", "event", "type", "unusbscribeMutationCache", "getMutationCache", "persistQueryClient", "hasUnsubscribed", "persistQueryClientUnsubscribe", "unsubscribe", "restorePromise", "then", "React", "React2", "import_with_selector", "import_with_selector", "createConfig", "useMutation", "useSyncExternalStoreWithSelector2", "useSyncExternalStoreWithSelector3", "useCallback5"]}