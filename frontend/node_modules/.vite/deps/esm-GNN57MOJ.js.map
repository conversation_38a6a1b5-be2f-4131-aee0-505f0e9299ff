{"version": 3, "sources": ["../../@walletconnect/legacy-types/dist/esm/crypto.js", "../../@walletconnect/legacy-types/src/constants/errors.ts", "../../@walletconnect/legacy-types/src/constants/events.ts", "../../@walletconnect/legacy-types/src/constants/jsonrpc.ts", "../../@walletconnect/legacy-types/src/constants/mobile.ts", "../../@walletconnect/legacy-types/src/constants/networks.ts", "../../@walletconnect/legacy-types/src/constants/index.ts", "../../@walletconnect/legacy-types/dist/esm/ethereum.js", "../../@walletconnect/legacy-types/src/events.ts", "../../@walletconnect/legacy-types/dist/esm/jsonrpc.js", "../../@walletconnect/legacy-types/dist/esm/network.js", "../../@walletconnect/legacy-types/dist/esm/protocol.js", "../../@walletconnect/legacy-types/dist/esm/qrcode.js", "../../@walletconnect/legacy-types/dist/esm/registry.js", "../../@walletconnect/legacy-types/dist/esm/socket.js", "../../@walletconnect/legacy-types/dist/esm/storage.js", "../../@walletconnect/legacy-types/src/index.ts", "../../is-typedarray/index.js", "../../typedarray-to-buffer/index.js", "../../@walletconnect/encoding/src/index.ts", "../../@walletconnect/legacy-utils/src/encoding.ts", "../../@walletconnect/legacy-utils/src/window.ts", "../../@walletconnect/legacy-utils/src/env.ts", "../../@walletconnect/legacy-utils/src/json.ts", "../../@walletconnect/legacy-utils/src/local.ts", "../../@walletconnect/legacy-utils/src/meta.ts", "../../@walletconnect/legacy-utils/src/misc.ts", "../../@walletconnect/legacy-utils/src/mobile.ts", "../../@walletconnect/legacy-utils/src/payload.ts", "../../@walletconnect/legacy-utils/src/registry.ts", "../../query-string/index.js", "../../@walletconnect/legacy-utils/src/url.ts", "../../@walletconnect/legacy-utils/src/session.ts", "../../@walletconnect/legacy-utils/src/validators.ts", "../../@walletconnect/legacy-utils/src/index.ts", "../../ws/browser.js", "../../toggle-selection/index.js", "../../copy-to-clipboard/index.js", "../../preact/compat/src/util.js", "../../preact/compat/src/hooks.js", "../../preact/compat/src/PureComponent.js", "../../preact/compat/src/memo.js", "../../preact/compat/src/forwardRef.js", "../../preact/compat/src/Children.js", "../../preact/compat/src/suspense.js", "../../preact/compat/src/suspense-list.js", "../../preact/src/constants.js", "../../preact/compat/src/portals.js", "../../preact/compat/src/render.js", "../../preact/compat/src/index.js", "../../@walletconnect/legacy-modal/src/node.ts", "../../@walletconnect/legacy-modal/src/browser/assets/style.ts", "../../node_modules/babel-plugin-transform-async-to-promises/helpers.mjs", "../../@walletconnect/legacy-modal/src/browser/assets/logo.ts", "../../@walletconnect/legacy-modal/src/browser/constants/index.ts", "../../@walletconnect/legacy-modal/src/browser/components/Header.tsx", "../../@walletconnect/legacy-modal/src/browser/components/ConnectButton.tsx", "../../@walletconnect/legacy-modal/src/browser/assets/caret.ts", "../../@walletconnect/legacy-modal/src/browser/components/WalletButton.tsx", "../../@walletconnect/legacy-modal/src/browser/components/WalletIcon.tsx", "../../@walletconnect/legacy-modal/src/browser/components/LinkDisplay.tsx", "../../@walletconnect/legacy-modal/src/browser/components/Notification.tsx", "../../@walletconnect/legacy-modal/src/browser/components/QRCodeDisplay.tsx", "../../@walletconnect/legacy-modal/src/browser/components/Modal.tsx", "../../@walletconnect/legacy-modal/src/browser/languages/de.ts", "../../@walletconnect/legacy-modal/src/browser/languages/en.ts", "../../@walletconnect/legacy-modal/src/browser/languages/es.ts", "../../@walletconnect/legacy-modal/src/browser/languages/fr.ts", "../../@walletconnect/legacy-modal/src/browser/languages/ko.ts", "../../@walletconnect/legacy-modal/src/browser/languages/pt.ts", "../../@walletconnect/legacy-modal/src/browser/languages/zh.ts", "../../@walletconnect/legacy-modal/src/browser/languages/fa.ts", "../../@walletconnect/legacy-modal/src/browser/languages/index.ts", "../../@walletconnect/legacy-modal/src/browser/index.tsx", "../../@walletconnect/legacy-modal/src/index.ts", "../../@walletconnect/legacy-provider/src/index.ts", "../../@walletconnect/legacy-provider/src/signer.ts", "../../@walletconnect/legacy-client/src/core.ts", "../../@walletconnect/legacy-client/src/socket.ts", "../../@walletconnect/legacy-client/src/network.ts", "../../@walletconnect/legacy-client/src/events.ts", "../../@walletconnect/legacy-client/src/storage.ts", "../../@walletconnect/legacy-client/src/url.ts", "../../@walletconnect/legacy-client/src/crypto.ts", "../../@walletconnect/crypto/src/browser/index.ts", "../../@walletconnect/randombytes/src/browser/index.ts", "../../@walletconnect/crypto/src/lib/browser.ts", "../../@walletconnect/crypto/src/constants/length.ts", "../../@walletconnect/crypto/src/constants/default.ts", "../../@walletconnect/crypto/src/constants/encoding.ts", "../../@walletconnect/crypto/src/constants/error.ts", "../../@walletconnect/crypto/src/constants/operations.ts", "../../@walletconnect/crypto/src/browser/aes.ts", "../../@walletconnect/crypto/src/helpers/index.ts", "../../@walletconnect/crypto/src/helpers/env.ts", "../../@walletconnect/crypto/src/helpers/pkcs7.ts", "../../@walletconnect/crypto/src/helpers/validators.ts", "../../@walletconnect/crypto/src/browser/hmac.ts", "../../@walletconnect/crypto/src/browser/sha2.ts", "../../@walletconnect/legacy-client/src/index.ts"], "sourcesContent": ["//# sourceMappingURL=crypto.js.map", null, null, null, null, null, null, "//# sourceMappingURL=ethereum.js.map", null, "//# sourceMappingURL=jsonrpc.js.map", "//# sourceMappingURL=network.js.map", "//# sourceMappingURL=protocol.js.map", "//# sourceMappingURL=qrcode.js.map", "//# sourceMappingURL=registry.js.map", "//# sourceMappingURL=socket.js.map", "//# sourceMappingURL=storage.js.map", null, "module.exports      = isTypedArray\nisTypedArray.strict = isStrictTypedArray\nisTypedArray.loose  = isLooseTypedArray\n\nvar toString = Object.prototype.toString\nvar names = {\n    '[object Int8Array]': true\n  , '[object Int16Array]': true\n  , '[object Int32Array]': true\n  , '[object Uint8Array]': true\n  , '[object Uint8ClampedArray]': true\n  , '[object Uint16Array]': true\n  , '[object Uint32Array]': true\n  , '[object Float32Array]': true\n  , '[object Float64Array]': true\n}\n\nfunction isTypedArray(arr) {\n  return (\n       isStrictTypedArray(arr)\n    || isLooseTypedArray(arr)\n  )\n}\n\nfunction isStrictTypedArray(arr) {\n  return (\n       arr instanceof Int8Array\n    || arr instanceof Int16Array\n    || arr instanceof Int32Array\n    || arr instanceof Uint8Array\n    || arr instanceof Uint8ClampedArray\n    || arr instanceof Uint16Array\n    || arr instanceof Uint32Array\n    || arr instanceof Float32Array\n    || arr instanceof Float64Array\n  )\n}\n\nfunction isLooseTypedArray(arr) {\n  return names[toString.call(arr)]\n}\n", "/**\n * Convert a typed array to a Buffer without a copy\n *\n * Author:   <PERSON><PERSON> <https://feross.org>\n * License:  MIT\n *\n * `npm install typedarray-to-buffer`\n */\n\nvar isTypedArray = require('is-typedarray').strict\n\nmodule.exports = function typedarrayToBuffer (arr) {\n  if (isTypedArray(arr)) {\n    // To avoid a copy, use the typed array's underlying ArrayBuffer to back new Buffer\n    var buf = Buffer.from(arr.buffer)\n    if (arr.byteLength !== arr.buffer.byteLength) {\n      // Respect the \"view\", i.e. byteOffset and byteLength, without doing a copy\n      buf = buf.slice(arr.byteOffset, arr.byteOffset + arr.byteLength)\n    }\n    return buf\n  } else {\n    // Pass through all other types to `Buffer.from`\n    return Buffer.from(arr)\n  }\n}\n", null, null, null, null, null, null, null, null, null, null, null, "'use strict';\nconst strictUriEncode = require('strict-uri-encode');\nconst decodeComponent = require('decode-uri-component');\nconst splitOnFirst = require('split-on-first');\nconst filterObject = require('filter-obj');\n\nconst isNullOrUndefined = value => value === null || value === undefined;\n\nfunction encoderForArrayFormat(options) {\n\tswitch (options.arrayFormat) {\n\t\tcase 'index':\n\t\t\treturn key => (result, value) => {\n\t\t\t\tconst index = result.length;\n\n\t\t\t\tif (\n\t\t\t\t\tvalue === undefined ||\n\t\t\t\t\t(options.skipNull && value === null) ||\n\t\t\t\t\t(options.skipEmptyString && value === '')\n\t\t\t\t) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\n\t\t\t\tif (value === null) {\n\t\t\t\t\treturn [...result, [encode(key, options), '[', index, ']'].join('')];\n\t\t\t\t}\n\n\t\t\t\treturn [\n\t\t\t\t\t...result,\n\t\t\t\t\t[encode(key, options), '[', encode(index, options), ']=', encode(value, options)].join('')\n\t\t\t\t];\n\t\t\t};\n\n\t\tcase 'bracket':\n\t\t\treturn key => (result, value) => {\n\t\t\t\tif (\n\t\t\t\t\tvalue === undefined ||\n\t\t\t\t\t(options.skipNull && value === null) ||\n\t\t\t\t\t(options.skipEmptyString && value === '')\n\t\t\t\t) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\n\t\t\t\tif (value === null) {\n\t\t\t\t\treturn [...result, [encode(key, options), '[]'].join('')];\n\t\t\t\t}\n\n\t\t\t\treturn [...result, [encode(key, options), '[]=', encode(value, options)].join('')];\n\t\t\t};\n\n\t\tcase 'comma':\n\t\tcase 'separator':\n\t\t\treturn key => (result, value) => {\n\t\t\t\tif (value === null || value === undefined || value.length === 0) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\n\t\t\t\tif (result.length === 0) {\n\t\t\t\t\treturn [[encode(key, options), '=', encode(value, options)].join('')];\n\t\t\t\t}\n\n\t\t\t\treturn [[result, encode(value, options)].join(options.arrayFormatSeparator)];\n\t\t\t};\n\n\t\tdefault:\n\t\t\treturn key => (result, value) => {\n\t\t\t\tif (\n\t\t\t\t\tvalue === undefined ||\n\t\t\t\t\t(options.skipNull && value === null) ||\n\t\t\t\t\t(options.skipEmptyString && value === '')\n\t\t\t\t) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\n\t\t\t\tif (value === null) {\n\t\t\t\t\treturn [...result, encode(key, options)];\n\t\t\t\t}\n\n\t\t\t\treturn [...result, [encode(key, options), '=', encode(value, options)].join('')];\n\t\t\t};\n\t}\n}\n\nfunction parserForArrayFormat(options) {\n\tlet result;\n\n\tswitch (options.arrayFormat) {\n\t\tcase 'index':\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tresult = /\\[(\\d*)\\]$/.exec(key);\n\n\t\t\t\tkey = key.replace(/\\[\\d*\\]$/, '');\n\n\t\t\t\tif (!result) {\n\t\t\t\t\taccumulator[key] = value;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (accumulator[key] === undefined) {\n\t\t\t\t\taccumulator[key] = {};\n\t\t\t\t}\n\n\t\t\t\taccumulator[key][result[1]] = value;\n\t\t\t};\n\n\t\tcase 'bracket':\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tresult = /(\\[\\])$/.exec(key);\n\t\t\t\tkey = key.replace(/\\[\\]$/, '');\n\n\t\t\t\tif (!result) {\n\t\t\t\t\taccumulator[key] = value;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (accumulator[key] === undefined) {\n\t\t\t\t\taccumulator[key] = [value];\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\taccumulator[key] = [].concat(accumulator[key], value);\n\t\t\t};\n\n\t\tcase 'comma':\n\t\tcase 'separator':\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tconst isArray = typeof value === 'string' && value.includes(options.arrayFormatSeparator);\n\t\t\t\tconst isEncodedArray = (typeof value === 'string' && !isArray && decode(value, options).includes(options.arrayFormatSeparator));\n\t\t\t\tvalue = isEncodedArray ? decode(value, options) : value;\n\t\t\t\tconst newValue = isArray || isEncodedArray ? value.split(options.arrayFormatSeparator).map(item => decode(item, options)) : value === null ? value : decode(value, options);\n\t\t\t\taccumulator[key] = newValue;\n\t\t\t};\n\n\t\tdefault:\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tif (accumulator[key] === undefined) {\n\t\t\t\t\taccumulator[key] = value;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\taccumulator[key] = [].concat(accumulator[key], value);\n\t\t\t};\n\t}\n}\n\nfunction validateArrayFormatSeparator(value) {\n\tif (typeof value !== 'string' || value.length !== 1) {\n\t\tthrow new TypeError('arrayFormatSeparator must be single character string');\n\t}\n}\n\nfunction encode(value, options) {\n\tif (options.encode) {\n\t\treturn options.strict ? strictUriEncode(value) : encodeURIComponent(value);\n\t}\n\n\treturn value;\n}\n\nfunction decode(value, options) {\n\tif (options.decode) {\n\t\treturn decodeComponent(value);\n\t}\n\n\treturn value;\n}\n\nfunction keysSorter(input) {\n\tif (Array.isArray(input)) {\n\t\treturn input.sort();\n\t}\n\n\tif (typeof input === 'object') {\n\t\treturn keysSorter(Object.keys(input))\n\t\t\t.sort((a, b) => Number(a) - Number(b))\n\t\t\t.map(key => input[key]);\n\t}\n\n\treturn input;\n}\n\nfunction removeHash(input) {\n\tconst hashStart = input.indexOf('#');\n\tif (hashStart !== -1) {\n\t\tinput = input.slice(0, hashStart);\n\t}\n\n\treturn input;\n}\n\nfunction getHash(url) {\n\tlet hash = '';\n\tconst hashStart = url.indexOf('#');\n\tif (hashStart !== -1) {\n\t\thash = url.slice(hashStart);\n\t}\n\n\treturn hash;\n}\n\nfunction extract(input) {\n\tinput = removeHash(input);\n\tconst queryStart = input.indexOf('?');\n\tif (queryStart === -1) {\n\t\treturn '';\n\t}\n\n\treturn input.slice(queryStart + 1);\n}\n\nfunction parseValue(value, options) {\n\tif (options.parseNumbers && !Number.isNaN(Number(value)) && (typeof value === 'string' && value.trim() !== '')) {\n\t\tvalue = Number(value);\n\t} else if (options.parseBooleans && value !== null && (value.toLowerCase() === 'true' || value.toLowerCase() === 'false')) {\n\t\tvalue = value.toLowerCase() === 'true';\n\t}\n\n\treturn value;\n}\n\nfunction parse(query, options) {\n\toptions = Object.assign({\n\t\tdecode: true,\n\t\tsort: true,\n\t\tarrayFormat: 'none',\n\t\tarrayFormatSeparator: ',',\n\t\tparseNumbers: false,\n\t\tparseBooleans: false\n\t}, options);\n\n\tvalidateArrayFormatSeparator(options.arrayFormatSeparator);\n\n\tconst formatter = parserForArrayFormat(options);\n\n\t// Create an object with no prototype\n\tconst ret = Object.create(null);\n\n\tif (typeof query !== 'string') {\n\t\treturn ret;\n\t}\n\n\tquery = query.trim().replace(/^[?#&]/, '');\n\n\tif (!query) {\n\t\treturn ret;\n\t}\n\n\tfor (const param of query.split('&')) {\n\t\tif (param === '') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet [key, value] = splitOnFirst(options.decode ? param.replace(/\\+/g, ' ') : param, '=');\n\n\t\t// Missing `=` should be `null`:\n\t\t// http://w3.org/TR/2012/WD-url-20120524/#collect-url-parameters\n\t\tvalue = value === undefined ? null : ['comma', 'separator'].includes(options.arrayFormat) ? value : decode(value, options);\n\t\tformatter(decode(key, options), value, ret);\n\t}\n\n\tfor (const key of Object.keys(ret)) {\n\t\tconst value = ret[key];\n\t\tif (typeof value === 'object' && value !== null) {\n\t\t\tfor (const k of Object.keys(value)) {\n\t\t\t\tvalue[k] = parseValue(value[k], options);\n\t\t\t}\n\t\t} else {\n\t\t\tret[key] = parseValue(value, options);\n\t\t}\n\t}\n\n\tif (options.sort === false) {\n\t\treturn ret;\n\t}\n\n\treturn (options.sort === true ? Object.keys(ret).sort() : Object.keys(ret).sort(options.sort)).reduce((result, key) => {\n\t\tconst value = ret[key];\n\t\tif (Boolean(value) && typeof value === 'object' && !Array.isArray(value)) {\n\t\t\t// Sort object keys, not values\n\t\t\tresult[key] = keysSorter(value);\n\t\t} else {\n\t\t\tresult[key] = value;\n\t\t}\n\n\t\treturn result;\n\t}, Object.create(null));\n}\n\nexports.extract = extract;\nexports.parse = parse;\n\nexports.stringify = (object, options) => {\n\tif (!object) {\n\t\treturn '';\n\t}\n\n\toptions = Object.assign({\n\t\tencode: true,\n\t\tstrict: true,\n\t\tarrayFormat: 'none',\n\t\tarrayFormatSeparator: ','\n\t}, options);\n\n\tvalidateArrayFormatSeparator(options.arrayFormatSeparator);\n\n\tconst shouldFilter = key => (\n\t\t(options.skipNull && isNullOrUndefined(object[key])) ||\n\t\t(options.skipEmptyString && object[key] === '')\n\t);\n\n\tconst formatter = encoderForArrayFormat(options);\n\n\tconst objectCopy = {};\n\n\tfor (const key of Object.keys(object)) {\n\t\tif (!shouldFilter(key)) {\n\t\t\tobjectCopy[key] = object[key];\n\t\t}\n\t}\n\n\tconst keys = Object.keys(objectCopy);\n\n\tif (options.sort !== false) {\n\t\tkeys.sort(options.sort);\n\t}\n\n\treturn keys.map(key => {\n\t\tconst value = object[key];\n\n\t\tif (value === undefined) {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (value === null) {\n\t\t\treturn encode(key, options);\n\t\t}\n\n\t\tif (Array.isArray(value)) {\n\t\t\treturn value\n\t\t\t\t.reduce(formatter(key), [])\n\t\t\t\t.join('&');\n\t\t}\n\n\t\treturn encode(key, options) + '=' + encode(value, options);\n\t}).filter(x => x.length > 0).join('&');\n};\n\nexports.parseUrl = (url, options) => {\n\toptions = Object.assign({\n\t\tdecode: true\n\t}, options);\n\n\tconst [url_, hash] = splitOnFirst(url, '#');\n\n\treturn Object.assign(\n\t\t{\n\t\t\turl: url_.split('?')[0] || '',\n\t\t\tquery: parse(extract(url), options)\n\t\t},\n\t\toptions && options.parseFragmentIdentifier && hash ? {fragmentIdentifier: decode(hash, options)} : {}\n\t);\n};\n\nexports.stringifyUrl = (object, options) => {\n\toptions = Object.assign({\n\t\tencode: true,\n\t\tstrict: true\n\t}, options);\n\n\tconst url = removeHash(object.url).split('?')[0] || '';\n\tconst queryFromUrl = exports.extract(object.url);\n\tconst parsedQueryFromUrl = exports.parse(queryFromUrl, {sort: false});\n\n\tconst query = Object.assign(parsedQueryFromUrl, object.query);\n\tlet queryString = exports.stringify(query, options);\n\tif (queryString) {\n\t\tqueryString = `?${queryString}`;\n\t}\n\n\tlet hash = getHash(object.url);\n\tif (object.fragmentIdentifier) {\n\t\thash = `#${encode(object.fragmentIdentifier, options)}`;\n\t}\n\n\treturn `${url}${queryString}${hash}`;\n};\n\nexports.pick = (input, filter, options) => {\n\toptions = Object.assign({\n\t\tparseFragmentIdentifier: true\n\t}, options);\n\n\tconst {url, query, fragmentIdentifier} = exports.parseUrl(input, options);\n\treturn exports.stringifyUrl({\n\t\turl,\n\t\tquery: filterObject(query, filter),\n\t\tfragmentIdentifier\n\t}, options);\n};\n\nexports.exclude = (input, filter, options) => {\n\tconst exclusionFilter = Array.isArray(filter) ? key => !filter.includes(key) : (key, value) => !filter(key, value);\n\n\treturn exports.pick(input, exclusionFilter, options);\n};\n", null, null, null, null, "'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n", "\nmodule.exports = function () {\n  var selection = document.getSelection();\n  if (!selection.rangeCount) {\n    return function () {};\n  }\n  var active = document.activeElement;\n\n  var ranges = [];\n  for (var i = 0; i < selection.rangeCount; i++) {\n    ranges.push(selection.getRangeAt(i));\n  }\n\n  switch (active.tagName.toUpperCase()) { // .toUpperCase handles XHTML\n    case 'INPUT':\n    case 'TEXTAREA':\n      active.blur();\n      break;\n\n    default:\n      active = null;\n      break;\n  }\n\n  selection.removeAllRanges();\n  return function () {\n    selection.type === 'Caret' &&\n    selection.removeAllRanges();\n\n    if (!selection.rangeCount) {\n      ranges.forEach(function(range) {\n        selection.addRange(range);\n      });\n    }\n\n    active &&\n    active.focus();\n  };\n};\n", "\"use strict\";\n\nvar deselectCurrent = require(\"toggle-selection\");\n\nvar clipboardToIE11Formatting = {\n  \"text/plain\": \"Text\",\n  \"text/html\": \"Url\",\n  \"default\": \"Text\"\n}\n\nvar defaultMessage = \"Copy to clipboard: #{key}, Enter\";\n\nfunction format(message) {\n  var copyKey = (/mac os x/i.test(navigator.userAgent) ? \"⌘\" : \"Ctrl\") + \"+C\";\n  return message.replace(/#{\\s*key\\s*}/g, copyKey);\n}\n\nfunction copy(text, options) {\n  var debug,\n    message,\n    reselectPrevious,\n    range,\n    selection,\n    mark,\n    success = false;\n  if (!options) {\n    options = {};\n  }\n  debug = options.debug || false;\n  try {\n    reselectPrevious = deselectCurrent();\n\n    range = document.createRange();\n    selection = document.getSelection();\n\n    mark = document.createElement(\"span\");\n    mark.textContent = text;\n    // avoid screen readers from reading out loud the text\n    mark.ariaHidden = \"true\"\n    // reset user styles for span element\n    mark.style.all = \"unset\";\n    // prevents scrolling to the end of the page\n    mark.style.position = \"fixed\";\n    mark.style.top = 0;\n    mark.style.clip = \"rect(0, 0, 0, 0)\";\n    // used to preserve spaces and line breaks\n    mark.style.whiteSpace = \"pre\";\n    // do not inherit user-select (it may be `none`)\n    mark.style.webkitUserSelect = \"text\";\n    mark.style.MozUserSelect = \"text\";\n    mark.style.msUserSelect = \"text\";\n    mark.style.userSelect = \"text\";\n    mark.addEventListener(\"copy\", function(e) {\n      e.stopPropagation();\n      if (options.format) {\n        e.preventDefault();\n        if (typeof e.clipboardData === \"undefined\") { // IE 11\n          debug && console.warn(\"unable to use e.clipboardData\");\n          debug && console.warn(\"trying IE specific stuff\");\n          window.clipboardData.clearData();\n          var format = clipboardToIE11Formatting[options.format] || clipboardToIE11Formatting[\"default\"]\n          window.clipboardData.setData(format, text);\n        } else { // all other browsers\n          e.clipboardData.clearData();\n          e.clipboardData.setData(options.format, text);\n        }\n      }\n      if (options.onCopy) {\n        e.preventDefault();\n        options.onCopy(e.clipboardData);\n      }\n    });\n\n    document.body.appendChild(mark);\n\n    range.selectNodeContents(mark);\n    selection.addRange(range);\n\n    var successful = document.execCommand(\"copy\");\n    if (!successful) {\n      throw new Error(\"copy command was unsuccessful\");\n    }\n    success = true;\n  } catch (err) {\n    debug && console.error(\"unable to copy using execCommand: \", err);\n    debug && console.warn(\"trying IE specific stuff\");\n    try {\n      window.clipboardData.setData(options.format || \"text\", text);\n      options.onCopy && options.onCopy(window.clipboardData);\n      success = true;\n    } catch (err) {\n      debug && console.error(\"unable to copy using clipboardData: \", err);\n      debug && console.error(\"falling back to prompt\");\n      message = format(\"message\" in options ? options.message : defaultMessage);\n      window.prompt(message, text);\n    }\n  } finally {\n    if (selection) {\n      if (typeof selection.removeRange == \"function\") {\n        selection.removeRange(range);\n      } else {\n        selection.removeAllRanges();\n      }\n    }\n\n    if (mark) {\n      document.body.removeChild(mark);\n    }\n    reselectPrevious();\n  }\n\n  return success;\n}\n\nmodule.exports = copy;\n", "/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Check if two objects have a different shape\n * @param {object} a\n * @param {object} b\n * @returns {boolean}\n */\nexport function shallowDiffers(a, b) {\n\tfor (let i in a) if (i !== '__source' && !(i in b)) return true;\n\tfor (let i in b) if (i !== '__source' && a[i] !== b[i]) return true;\n\treturn false;\n}\n\n/**\n * Check if two values are the same value\n * @param {*} x\n * @param {*} y\n * @returns {boolean}\n */\nexport function is(x, y) {\n\treturn (x === y && (x !== 0 || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\n", "import { useState, useLayoutEffect, useEffect } from 'preact/hooks';\nimport { is } from './util';\n\n/**\n * This is taken from https://github.com/facebook/react/blob/main/packages/use-sync-external-store/src/useSyncExternalStoreShimClient.js#L84\n * on a high level this cuts out the warnings, ... and attempts a smaller implementation\n * @typedef {{ _value: any; _getSnapshot: () => any }} Store\n */\nexport function useSyncExternalStore(subscribe, getSnapshot) {\n\tconst value = getSnapshot();\n\n\t/**\n\t * @typedef {{ _instance: Store }} StoreRef\n\t * @type {[StoreRef, (store: StoreRef) => void]}\n\t */\n\tconst [{ _instance }, forceUpdate] = useState({\n\t\t_instance: { _value: value, _getSnapshot: getSnapshot }\n\t});\n\n\tuseLayoutEffect(() => {\n\t\t_instance._value = value;\n\t\t_instance._getSnapshot = getSnapshot;\n\n\t\tif (didSnapshotChange(_instance)) {\n\t\t\tforceUpdate({ _instance });\n\t\t}\n\t}, [subscribe, value, getSnapshot]);\n\n\tuseEffect(() => {\n\t\tif (didSnapshotChange(_instance)) {\n\t\t\tforceUpdate({ _instance });\n\t\t}\n\n\t\treturn subscribe(() => {\n\t\t\tif (didSnapshotChange(_instance)) {\n\t\t\t\tforceUpdate({ _instance });\n\t\t\t}\n\t\t});\n\t}, [subscribe]);\n\n\treturn value;\n}\n\n/** @type {(inst: Store) => boolean} */\nfunction didSnapshotChange(inst) {\n\tconst latestGetSnapshot = inst._getSnapshot;\n\tconst prevValue = inst._value;\n\ttry {\n\t\tconst nextValue = latestGetSnapshot();\n\t\treturn !is(prevValue, nextValue);\n\t} catch (error) {\n\t\treturn true;\n\t}\n}\n\nexport function startTransition(cb) {\n\tcb();\n}\n\nexport function useDeferredValue(val) {\n\treturn val;\n}\n\nexport function useTransition() {\n\treturn [false, startTransition];\n}\n\n// TODO: in theory this should be done after a VNode is diffed as we want to insert\n// styles/... before it attaches\nexport const useInsertionEffect = useLayoutEffect;\n", "import { Component } from 'preact';\nimport { shallowDiffers } from './util';\n\n/**\n * Component class with a predefined `shouldComponentUpdate` implementation\n */\nexport function PureComponent(p, c) {\n\tthis.props = p;\n\tthis.context = c;\n}\nPureComponent.prototype = new Component();\n// Some third-party libraries check if this property is present\nPureComponent.prototype.isPureReactComponent = true;\nPureComponent.prototype.shouldComponentUpdate = function (props, state) {\n\treturn shallowDiffers(this.props, props) || shallowDiffers(this.state, state);\n};\n", "import { createElement } from 'preact';\nimport { shallowDiffers } from './util';\n\n/**\n * Memoize a component, so that it only updates when the props actually have\n * changed. This was previously known as `React.pure`.\n * @param {import('./internal').FunctionComponent} c functional component\n * @param {(prev: object, next: object) => boolean} [comparer] Custom equality function\n * @returns {import('./internal').FunctionComponent}\n */\nexport function memo(c, comparer) {\n\tfunction shouldUpdate(nextProps) {\n\t\tlet ref = this.props.ref;\n\t\tlet updateRef = ref == nextProps.ref;\n\t\tif (!updateRef && ref) {\n\t\t\tref.call ? ref(null) : (ref.current = null);\n\t\t}\n\n\t\tif (!comparer) {\n\t\t\treturn shallowDiffers(this.props, nextProps);\n\t\t}\n\n\t\treturn !comparer(this.props, nextProps) || !updateRef;\n\t}\n\n\tfunction Memoed(props) {\n\t\tthis.shouldComponentUpdate = shouldUpdate;\n\t\treturn createElement(c, props);\n\t}\n\tMemoed.displayName = 'Memo(' + (c.displayName || c.name) + ')';\n\tMemoed.prototype.isReactComponent = true;\n\tMemoed._forwarded = true;\n\treturn Memoed;\n}\n", "import { options } from 'preact';\nimport { assign } from './util';\n\nlet oldDiffHook = options._diff;\noptions._diff = vnode => {\n\tif (vnode.type && vnode.type._forwarded && vnode.ref) {\n\t\tvnode.props.ref = vnode.ref;\n\t\tvnode.ref = null;\n\t}\n\tif (oldDiffHook) oldDiffHook(vnode);\n};\n\nexport const REACT_FORWARD_SYMBOL =\n\t(typeof Symbol != 'undefined' &&\n\t\tSymbol.for &&\n\t\tSymbol.for('react.forward_ref')) ||\n\t0xf47;\n\n/**\n * Pass ref down to a child. This is mainly used in libraries with HOCs that\n * wrap components. Using `forwardRef` there is an easy way to get a reference\n * of the wrapped component instead of one of the wrapper itself.\n * @param {import('./index').ForwardFn} fn\n * @returns {import('./internal').FunctionComponent}\n */\nexport function forwardRef(fn) {\n\tfunction Forwarded(props) {\n\t\tlet clone = assign({}, props);\n\t\tdelete clone.ref;\n\t\treturn fn(clone, props.ref || null);\n\t}\n\n\t// mobx-react checks for this being present\n\tForwarded.$$typeof = REACT_FORWARD_SYMBOL;\n\t// mobx-react heavily relies on implementation details.\n\t// It expects an object here with a `render` property,\n\t// and prototype.render will fail. Without this\n\t// mobx-react throws.\n\tForwarded.render = Forwarded;\n\n\tForwarded.prototype.isReactComponent = Forwarded._forwarded = true;\n\tForwarded.displayName = 'ForwardRef(' + (fn.displayName || fn.name) + ')';\n\treturn Forwarded;\n}\n", "import { toChildArray } from 'preact';\n\nconst mapFn = (children, fn) => {\n\tif (children == null) return null;\n\treturn toChildArray(toChildArray(children).map(fn));\n};\n\n// This API is completely unnecessary for Preact, so it's basically passthrough.\nexport const Children = {\n\tmap: mapFn,\n\tforEach: mapFn,\n\tcount(children) {\n\t\treturn children ? toChildArray(children).length : 0;\n\t},\n\tonly(children) {\n\t\tconst normalized = toChildArray(children);\n\t\tif (normalized.length !== 1) throw 'Children.only';\n\t\treturn normalized[0];\n\t},\n\ttoArray: toChildArray\n};\n", "import { Component, createElement, options, Fragment } from 'preact';\nimport { MODE_HYDRATE } from '../../src/constants';\nimport { assign } from './util';\n\nconst oldCatchError = options._catchError;\noptions._catchError = function (error, newVNode, oldVNode, errorInfo) {\n\tif (error.then) {\n\t\t/** @type {import('./internal').Component} */\n\t\tlet component;\n\t\tlet vnode = newVNode;\n\n\t\tfor (; (vnode = vnode._parent); ) {\n\t\t\tif ((component = vnode._component) && component._childDidSuspend) {\n\t\t\t\tif (newVNode._dom == null) {\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t}\n\t\t\t\t// Don't call oldCatchError if we found a Suspense\n\t\t\t\treturn component._childDidSuspend(error, newVNode);\n\t\t\t}\n\t\t}\n\t}\n\toldCatchError(error, newVNode, oldVNode, errorInfo);\n};\n\nconst oldUnmount = options.unmount;\noptions.unmount = function (vnode) {\n\t/** @type {import('./internal').Component} */\n\tconst component = vnode._component;\n\tif (component && component._onResolve) {\n\t\tcomponent._onResolve();\n\t}\n\n\t// if the component is still hydrating\n\t// most likely it is because the component is suspended\n\t// we set the vnode.type as `null` so that it is not a typeof function\n\t// so the unmount will remove the vnode._dom\n\tif (component && vnode._flags & MODE_HYDRATE) {\n\t\tvnode.type = null;\n\t}\n\n\tif (oldUnmount) oldUnmount(vnode);\n};\n\nfunction detachedClone(vnode, detachedParent, parentDom) {\n\tif (vnode) {\n\t\tif (vnode._component && vnode._component.__hooks) {\n\t\t\tvnode._component.__hooks._list.forEach(effect => {\n\t\t\t\tif (typeof effect._cleanup == 'function') effect._cleanup();\n\t\t\t});\n\n\t\t\tvnode._component.__hooks = null;\n\t\t}\n\n\t\tvnode = assign({}, vnode);\n\t\tif (vnode._component != null) {\n\t\t\tif (vnode._component._parentDom === parentDom) {\n\t\t\t\tvnode._component._parentDom = detachedParent;\n\t\t\t}\n\n\t\t\tvnode._component._force = true;\n\n\t\t\tvnode._component = null;\n\t\t}\n\n\t\tvnode._children =\n\t\t\tvnode._children &&\n\t\t\tvnode._children.map(child =>\n\t\t\t\tdetachedClone(child, detachedParent, parentDom)\n\t\t\t);\n\t}\n\n\treturn vnode;\n}\n\nfunction removeOriginal(vnode, detachedParent, originalParent) {\n\tif (vnode && originalParent) {\n\t\tvnode._original = null;\n\t\tvnode._children =\n\t\t\tvnode._children &&\n\t\t\tvnode._children.map(child =>\n\t\t\t\tremoveOriginal(child, detachedParent, originalParent)\n\t\t\t);\n\n\t\tif (vnode._component) {\n\t\t\tif (vnode._component._parentDom === detachedParent) {\n\t\t\t\tif (vnode._dom) {\n\t\t\t\t\toriginalParent.appendChild(vnode._dom);\n\t\t\t\t}\n\t\t\t\tvnode._component._force = true;\n\t\t\t\tvnode._component._parentDom = originalParent;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn vnode;\n}\n\n// having custom inheritance instead of a class here saves a lot of bytes\nexport function Suspense() {\n\t// we do not call super here to golf some bytes...\n\tthis._pendingSuspensionCount = 0;\n\tthis._suspenders = null;\n\tthis._detachOnNextRender = null;\n}\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspense.prototype = new Component();\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {Promise} promise The thrown promise\n * @param {import('./internal').VNode<any, any>} suspendingVNode The suspending component\n */\nSuspense.prototype._childDidSuspend = function (promise, suspendingVNode) {\n\tconst suspendingComponent = suspendingVNode._component;\n\n\t/** @type {import('./internal').SuspenseComponent} */\n\tconst c = this;\n\n\tif (c._suspenders == null) {\n\t\tc._suspenders = [];\n\t}\n\tc._suspenders.push(suspendingComponent);\n\n\tconst resolve = suspended(c._vnode);\n\n\tlet resolved = false;\n\tconst onResolved = () => {\n\t\tif (resolved) return;\n\n\t\tresolved = true;\n\t\tsuspendingComponent._onResolve = null;\n\n\t\tif (resolve) {\n\t\t\tresolve(onSuspensionComplete);\n\t\t} else {\n\t\t\tonSuspensionComplete();\n\t\t}\n\t};\n\n\tsuspendingComponent._onResolve = onResolved;\n\n\tconst onSuspensionComplete = () => {\n\t\tif (!--c._pendingSuspensionCount) {\n\t\t\t// If the suspension was during hydration we don't need to restore the\n\t\t\t// suspended children into the _children array\n\t\t\tif (c.state._suspended) {\n\t\t\t\tconst suspendedVNode = c.state._suspended;\n\t\t\t\tc._vnode._children[0] = removeOriginal(\n\t\t\t\t\tsuspendedVNode,\n\t\t\t\t\tsuspendedVNode._component._parentDom,\n\t\t\t\t\tsuspendedVNode._component._originalParentDom\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tc.setState({ _suspended: (c._detachOnNextRender = null) });\n\n\t\t\tlet suspended;\n\t\t\twhile ((suspended = c._suspenders.pop())) {\n\t\t\t\tsuspended.forceUpdate();\n\t\t\t}\n\t\t}\n\t};\n\n\t/**\n\t * We do not set `suspended: true` during hydration because we want the actual markup\n\t * to remain on screen and hydrate it when the suspense actually gets resolved.\n\t * While in non-hydration cases the usual fallback -> component flow would occour.\n\t */\n\tif (\n\t\t!c._pendingSuspensionCount++ &&\n\t\t!(suspendingVNode._flags & MODE_HYDRATE)\n\t) {\n\t\tc.setState({ _suspended: (c._detachOnNextRender = c._vnode._children[0]) });\n\t}\n\tpromise.then(onResolved, onResolved);\n};\n\nSuspense.prototype.componentWillUnmount = function () {\n\tthis._suspenders = [];\n};\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {import('./internal').SuspenseComponent[\"props\"]} props\n * @param {import('./internal').SuspenseState} state\n */\nSuspense.prototype.render = function (props, state) {\n\tif (this._detachOnNextRender) {\n\t\t// When the Suspense's _vnode was created by a call to createVNode\n\t\t// (i.e. due to a setState further up in the tree)\n\t\t// it's _children prop is null, in this case we \"forget\" about the parked vnodes to detach\n\t\tif (this._vnode._children) {\n\t\t\tconst detachedParent = document.createElement('div');\n\t\t\tconst detachedComponent = this._vnode._children[0]._component;\n\t\t\tthis._vnode._children[0] = detachedClone(\n\t\t\t\tthis._detachOnNextRender,\n\t\t\t\tdetachedParent,\n\t\t\t\t(detachedComponent._originalParentDom = detachedComponent._parentDom)\n\t\t\t);\n\t\t}\n\n\t\tthis._detachOnNextRender = null;\n\t}\n\n\t// Wrap fallback tree in a VNode that prevents itself from being marked as aborting mid-hydration:\n\t/** @type {import('./internal').VNode} */\n\tconst fallback =\n\t\tstate._suspended && createElement(Fragment, null, props.fallback);\n\tif (fallback) fallback._flags &= ~MODE_HYDRATE;\n\n\treturn [\n\t\tcreateElement(Fragment, null, state._suspended ? null : props.children),\n\t\tfallback\n\t];\n};\n\n/**\n * Checks and calls the parent component's _suspended method, passing in the\n * suspended vnode. This is a way for a parent (e.g. SuspenseList) to get notified\n * that one of its children/descendants suspended.\n *\n * The parent MAY return a callback. The callback will get called when the\n * suspension resolves, notifying the parent of the fact.\n * Moreover, the callback gets function `unsuspend` as a parameter. The resolved\n * child descendant will not actually get unsuspended until `unsuspend` gets called.\n * This is a way for the parent to delay unsuspending.\n *\n * If the parent does not return a callback then the resolved vnode\n * gets unsuspended immediately when it resolves.\n *\n * @param {import('./internal').VNode} vnode\n * @returns {((unsuspend: () => void) => void)?}\n */\nexport function suspended(vnode) {\n\t/** @type {import('./internal').Component} */\n\tlet component = vnode._parent._component;\n\treturn component && component._suspended && component._suspended(vnode);\n}\n\nexport function lazy(loader) {\n\tlet prom;\n\tlet component;\n\tlet error;\n\n\tfunction Lazy(props) {\n\t\tif (!prom) {\n\t\t\tprom = loader();\n\t\t\tprom.then(\n\t\t\t\texports => {\n\t\t\t\t\tcomponent = exports.default || exports;\n\t\t\t\t},\n\t\t\t\te => {\n\t\t\t\t\terror = e;\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tif (error) {\n\t\t\tthrow error;\n\t\t}\n\n\t\tif (!component) {\n\t\t\tthrow prom;\n\t\t}\n\n\t\treturn createElement(component, props);\n\t}\n\n\tLazy.displayName = 'Lazy';\n\tLazy._forwarded = true;\n\treturn Lazy;\n}\n", "import { Component, toChildArray } from 'preact';\nimport { suspended } from './suspense.js';\n\n// Indexes to linked list nodes (nodes are stored as arrays to save bytes).\nconst SUSPENDED_COUNT = 0;\nconst RESOLVED_COUNT = 1;\nconst NEXT_NODE = 2;\n\n// Having custom inheritance instead of a class here saves a lot of bytes.\nexport function SuspenseList() {\n\tthis._next = null;\n\tthis._map = null;\n}\n\n// Mark one of child's earlier suspensions as resolved.\n// Some pending callbacks may become callable due to this\n// (e.g. the last suspended descendant gets resolved when\n// revealOrder === 'together'). Process those callbacks as well.\nconst resolve = (list, child, node) => {\n\tif (++node[RESOLVED_COUNT] === node[SUSPENDED_COUNT]) {\n\t\t// The number a child (or any of its descendants) has been suspended\n\t\t// matches the number of times it's been resolved. Therefore we\n\t\t// mark the child as completely resolved by deleting it from ._map.\n\t\t// This is used to figure out when *all* children have been completely\n\t\t// resolved when revealOrder is 'together'.\n\t\tlist._map.delete(child);\n\t}\n\n\t// If revealOrder is falsy then we can do an early exit, as the\n\t// callbacks won't get queued in the node anyway.\n\t// If revealOrder is 'together' then also do an early exit\n\t// if all suspended descendants have not yet been resolved.\n\tif (\n\t\t!list.props.revealOrder ||\n\t\t(list.props.revealOrder[0] === 't' && list._map.size)\n\t) {\n\t\treturn;\n\t}\n\n\t// Walk the currently suspended children in order, calling their\n\t// stored callbacks on the way. Stop if we encounter a child that\n\t// has not been completely resolved yet.\n\tnode = list._next;\n\twhile (node) {\n\t\twhile (node.length > 3) {\n\t\t\tnode.pop()();\n\t\t}\n\t\tif (node[RESOLVED_COUNT] < node[SUSPENDED_COUNT]) {\n\t\t\tbreak;\n\t\t}\n\t\tlist._next = node = node[NEXT_NODE];\n\t}\n};\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspenseList.prototype = new Component();\n\nSuspenseList.prototype._suspended = function (child) {\n\tconst list = this;\n\tconst delegated = suspended(list._vnode);\n\n\tlet node = list._map.get(child);\n\tnode[SUSPENDED_COUNT]++;\n\n\treturn unsuspend => {\n\t\tconst wrappedUnsuspend = () => {\n\t\t\tif (!list.props.revealOrder) {\n\t\t\t\t// Special case the undefined (falsy) revealOrder, as there\n\t\t\t\t// is no need to coordinate a specific order or unsuspends.\n\t\t\t\tunsuspend();\n\t\t\t} else {\n\t\t\t\tnode.push(unsuspend);\n\t\t\t\tresolve(list, child, node);\n\t\t\t}\n\t\t};\n\t\tif (delegated) {\n\t\t\tdelegated(wrappedUnsuspend);\n\t\t} else {\n\t\t\twrappedUnsuspend();\n\t\t}\n\t};\n};\n\nSuspenseList.prototype.render = function (props) {\n\tthis._next = null;\n\tthis._map = new Map();\n\n\tconst children = toChildArray(props.children);\n\tif (props.revealOrder && props.revealOrder[0] === 'b') {\n\t\t// If order === 'backwards' (or, well, anything starting with a 'b')\n\t\t// then flip the child list around so that the last child will be\n\t\t// the first in the linked list.\n\t\tchildren.reverse();\n\t}\n\t// Build the linked list. Iterate through the children in reverse order\n\t// so that `_next` points to the first linked list node to be resolved.\n\tfor (let i = children.length; i--; ) {\n\t\t// Create a new linked list node as an array of form:\n\t\t// \t[suspended_count, resolved_count, next_node]\n\t\t// where suspended_count and resolved_count are numeric counters for\n\t\t// keeping track how many times a node has been suspended and resolved.\n\t\t//\n\t\t// Note that suspended_count starts from 1 instead of 0, so we can block\n\t\t// processing callbacks until componentDidMount has been called. In a sense\n\t\t// node is suspended at least until componentDidMount gets called!\n\t\t//\n\t\t// Pending callbacks are added to the end of the node:\n\t\t// \t[suspended_count, resolved_count, next_node, callback_0, callback_1, ...]\n\t\tthis._map.set(children[i], (this._next = [1, 0, this._next]));\n\t}\n\treturn props.children;\n};\n\nSuspenseList.prototype.componentDidUpdate =\n\tSuspenseList.prototype.componentDidMount = function () {\n\t\t// Iterate through all children after mounting for two reasons:\n\t\t// 1. As each node[SUSPENDED_COUNT] starts from 1, this iteration increases\n\t\t//    each node[RELEASED_COUNT] by 1, therefore balancing the counters.\n\t\t//    The nodes can now be completely consumed from the linked list.\n\t\t// 2. Handle nodes that might have gotten resolved between render and\n\t\t//    componentDidMount.\n\t\tthis._map.forEach((node, child) => {\n\t\t\tresolve(this, child, node);\n\t\t});\n\t};\n", "/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 2;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 1;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\nexport const XHTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\nexport const MATH_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n\nexport const NULL = null;\nexport const UNDEFINED = undefined;\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { createElement, render } from 'preact';\n\n/**\n * @param {import('../../src/index').RenderableProps<{ context: any }>} props\n */\nfunction ContextProvider(props) {\n\tthis.getChildContext = () => props.context;\n\treturn props.children;\n}\n\n/**\n * Portal component\n * @this {import('./internal').Component}\n * @param {object | null | undefined} props\n *\n * TODO: use createRoot() instead of fake root\n */\nfunction Portal(props) {\n\tconst _this = this;\n\tlet container = props._container;\n\n\t_this.componentWillUnmount = function () {\n\t\trender(null, _this._temp);\n\t\t_this._temp = null;\n\t\t_this._container = null;\n\t};\n\n\t// When we change container we should clear our old container and\n\t// indicate a new mount.\n\tif (_this._container && _this._container !== container) {\n\t\t_this.componentWillUnmount();\n\t}\n\n\tif (!_this._temp) {\n\t\t// Ensure the element has a mask for useId invocations\n\t\tlet root = _this._vnode;\n\t\twhile (root !== null && !root._mask && root._parent !== null) {\n\t\t\troot = root._parent;\n\t\t}\n\n\t\t_this._container = container;\n\n\t\t// Create a fake DOM parent node that manages a subset of `container`'s children:\n\t\t_this._temp = {\n\t\t\tnodeType: 1,\n\t\t\tparentNode: container,\n\t\t\tchildNodes: [],\n\t\t\t_children: { _mask: root._mask },\n\t\t\tcontains: () => true,\n\t\t\tinsertBefore(child, before) {\n\t\t\t\tthis.childNodes.push(child);\n\t\t\t\t_this._container.insertBefore(child, before);\n\t\t\t},\n\t\t\tremoveChild(child) {\n\t\t\t\tthis.childNodes.splice(this.childNodes.indexOf(child) >>> 1, 1);\n\t\t\t\t_this._container.removeChild(child);\n\t\t\t}\n\t\t};\n\t}\n\n\t// Render our wrapping element into temp.\n\trender(\n\t\tcreateElement(ContextProvider, { context: _this.context }, props._vnode),\n\t\t_this._temp\n\t);\n}\n\n/**\n * Create a `Portal` to continue rendering the vnode tree at a different DOM node\n * @param {import('./internal').VNode} vnode The vnode to render\n * @param {import('./internal').PreactElement} container The DOM node to continue rendering in to.\n */\nexport function createPortal(vnode, container) {\n\tconst el = createElement(Portal, { _vnode: vnode, _container: container });\n\tel.containerInfo = container;\n\treturn el;\n}\n", "import {\n\trender as preactRender,\n\thydrate as preactHydrate,\n\toptions,\n\ttoChildArray,\n\tComponent\n} from 'preact';\nimport {\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue,\n\tuseEffect,\n\tuseId,\n\tuseImperativeHandle,\n\tuseLayoutEffect,\n\tuseMemo,\n\tuseReducer,\n\tuseRef,\n\tuseState\n} from 'preact/hooks';\nimport {\n\tuseDeferredValue,\n\tuseInsertionEffect,\n\tuseSyncExternalStore,\n\tuseTransition\n} from './index';\n\nexport const REACT_ELEMENT_TYPE =\n\t(typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.element')) ||\n\t0xeac7;\n\nconst CAMEL_PROPS =\n\t/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;\nconst ON_ANI = /^on(Ani|Tra|Tou|BeforeInp|Compo)/;\nconst CAMEL_REPLACE = /[A-Z0-9]/g;\nconst IS_DOM = typeof document !== 'undefined';\n\n// Input types for which onchange should not be converted to oninput.\n// type=\"file|checkbox|radio\", plus \"range\" in IE11.\n// (IE11 doesn't support Symbol, which we use here to turn `rad` into `ra` which matches \"range\")\nconst onChangeInputType = type =>\n\t(typeof Symbol != 'undefined' && typeof Symbol() == 'symbol'\n\t\t? /fil|che|rad/\n\t\t: /fil|che|ra/\n\t).test(type);\n\n// Some libraries like `react-virtualized` explicitly check for this.\nComponent.prototype.isReactComponent = {};\n\n// `UNSAFE_*` lifecycle hooks\n// Preact only ever invokes the unprefixed methods.\n// Here we provide a base \"fallback\" implementation that calls any defined UNSAFE_ prefixed method.\n// - If a component defines its own `componentDidMount()` (including via defineProperty), use that.\n// - If a component defines `UNSAFE_componentDidMount()`, `componentDidMount` is the alias getter/setter.\n// - If anything assigns to an `UNSAFE_*` property, the assignment is forwarded to the unprefixed property.\n// See https://github.com/preactjs/preact/issues/1941\n[\n\t'componentWillMount',\n\t'componentWillReceiveProps',\n\t'componentWillUpdate'\n].forEach(key => {\n\tObject.defineProperty(Component.prototype, key, {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn this['UNSAFE_' + key];\n\t\t},\n\t\tset(v) {\n\t\t\tObject.defineProperty(this, key, {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true,\n\t\t\t\tvalue: v\n\t\t\t});\n\t\t}\n\t});\n});\n\n/**\n * Proxy render() since React returns a Component reference.\n * @param {import('./internal').VNode} vnode VNode tree to render\n * @param {import('./internal').PreactElement} parent DOM node to render vnode tree into\n * @param {() => void} [callback] Optional callback that will be called after rendering\n * @returns {import('./internal').Component | null} The root component reference or null\n */\nexport function render(vnode, parent, callback) {\n\t// React destroys any existing DOM nodes, see #1727\n\t// ...but only on the first render, see #1828\n\tif (parent._children == null) {\n\t\tparent.textContent = '';\n\t}\n\n\tpreactRender(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nexport function hydrate(vnode, parent, callback) {\n\tpreactHydrate(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nlet oldEventHook = options.event;\noptions.event = e => {\n\tif (oldEventHook) e = oldEventHook(e);\n\n\te.persist = empty;\n\te.isPropagationStopped = isPropagationStopped;\n\te.isDefaultPrevented = isDefaultPrevented;\n\treturn (e.nativeEvent = e);\n};\n\nfunction empty() {}\n\nfunction isPropagationStopped() {\n\treturn this.cancelBubble;\n}\n\nfunction isDefaultPrevented() {\n\treturn this.defaultPrevented;\n}\n\nconst classNameDescriptorNonEnumberable = {\n\tenumerable: false,\n\tconfigurable: true,\n\tget() {\n\t\treturn this.class;\n\t}\n};\n\nfunction handleDomVNode(vnode) {\n\tlet props = vnode.props,\n\t\ttype = vnode.type,\n\t\tnormalizedProps = {};\n\n\tlet isNonDashedType = type.indexOf('-') === -1;\n\tfor (let i in props) {\n\t\tlet value = props[i];\n\n\t\tif (\n\t\t\t(i === 'value' && 'defaultValue' in props && value == null) ||\n\t\t\t// Emulate React's behavior of not rendering the contents of noscript tags on the client.\n\t\t\t(IS_DOM && i === 'children' && type === 'noscript') ||\n\t\t\ti === 'class' ||\n\t\t\ti === 'className'\n\t\t) {\n\t\t\t// Skip applying value if it is null/undefined and we already set\n\t\t\t// a default value\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet lowerCased = i.toLowerCase();\n\t\tif (i === 'defaultValue' && 'value' in props && props.value == null) {\n\t\t\t// `defaultValue` is treated as a fallback `value` when a value prop is present but null/undefined.\n\t\t\t// `defaultValue` for Elements with no value prop is the same as the DOM defaultValue property.\n\t\t\ti = 'value';\n\t\t} else if (i === 'download' && value === true) {\n\t\t\t// Calling `setAttribute` with a truthy value will lead to it being\n\t\t\t// passed as a stringified value, e.g. `download=\"true\"`. React\n\t\t\t// converts it to an empty string instead, otherwise the attribute\n\t\t\t// value will be used as the file name and the file will be called\n\t\t\t// \"true\" upon downloading it.\n\t\t\tvalue = '';\n\t\t} else if (lowerCased === 'translate' && value === 'no') {\n\t\t\tvalue = false;\n\t\t} else if (lowerCased[0] === 'o' && lowerCased[1] === 'n') {\n\t\t\tif (lowerCased === 'ondoubleclick') {\n\t\t\t\ti = 'ondblclick';\n\t\t\t} else if (\n\t\t\t\tlowerCased === 'onchange' &&\n\t\t\t\t(type === 'input' || type === 'textarea') &&\n\t\t\t\t!onChangeInputType(props.type)\n\t\t\t) {\n\t\t\t\tlowerCased = i = 'oninput';\n\t\t\t} else if (lowerCased === 'onfocus') {\n\t\t\t\ti = 'onfocusin';\n\t\t\t} else if (lowerCased === 'onblur') {\n\t\t\t\ti = 'onfocusout';\n\t\t\t} else if (ON_ANI.test(i)) {\n\t\t\t\ti = lowerCased;\n\t\t\t}\n\t\t} else if (isNonDashedType && CAMEL_PROPS.test(i)) {\n\t\t\ti = i.replace(CAMEL_REPLACE, '-$&').toLowerCase();\n\t\t} else if (value === null) {\n\t\t\tvalue = undefined;\n\t\t}\n\n\t\t// Add support for onInput and onChange, see #3561\n\t\t// if we have an oninput prop already change it to oninputCapture\n\t\tif (lowerCased === 'oninput') {\n\t\t\ti = lowerCased;\n\t\t\tif (normalizedProps[i]) {\n\t\t\t\ti = 'oninputCapture';\n\t\t\t}\n\t\t}\n\n\t\tnormalizedProps[i] = value;\n\t}\n\n\t// Add support for array select values: <select multiple value={[]} />\n\tif (\n\t\ttype == 'select' &&\n\t\tnormalizedProps.multiple &&\n\t\tArray.isArray(normalizedProps.value)\n\t) {\n\t\t// forEach() always returns undefined, which we abuse here to unset the value prop.\n\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\n\t\t\tchild.props.selected =\n\t\t\t\tnormalizedProps.value.indexOf(child.props.value) != -1;\n\t\t});\n\t}\n\n\t// Adding support for defaultValue in select tag\n\tif (type == 'select' && normalizedProps.defaultValue != null) {\n\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\n\t\t\tif (normalizedProps.multiple) {\n\t\t\t\tchild.props.selected =\n\t\t\t\t\tnormalizedProps.defaultValue.indexOf(child.props.value) != -1;\n\t\t\t} else {\n\t\t\t\tchild.props.selected =\n\t\t\t\t\tnormalizedProps.defaultValue == child.props.value;\n\t\t\t}\n\t\t});\n\t}\n\n\tif (props.class && !props.className) {\n\t\tnormalizedProps.class = props.class;\n\t\tObject.defineProperty(\n\t\t\tnormalizedProps,\n\t\t\t'className',\n\t\t\tclassNameDescriptorNonEnumberable\n\t\t);\n\t} else if (props.className && !props.class) {\n\t\tnormalizedProps.class = normalizedProps.className = props.className;\n\t} else if (props.class && props.className) {\n\t\tnormalizedProps.class = normalizedProps.className = props.className;\n\t}\n\n\tvnode.props = normalizedProps;\n}\n\nlet oldVNodeHook = options.vnode;\noptions.vnode = vnode => {\n\t// only normalize props on Element nodes\n\tif (typeof vnode.type === 'string') {\n\t\thandleDomVNode(vnode);\n\t}\n\n\tvnode.$$typeof = REACT_ELEMENT_TYPE;\n\n\tif (oldVNodeHook) oldVNodeHook(vnode);\n};\n\n// Only needed for react-relay\nlet currentComponent;\nconst oldBeforeRender = options._render;\noptions._render = function (vnode) {\n\tif (oldBeforeRender) {\n\t\toldBeforeRender(vnode);\n\t}\n\tcurrentComponent = vnode._component;\n};\n\nconst oldDiffed = options.diffed;\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.diffed = function (vnode) {\n\tif (oldDiffed) {\n\t\toldDiffed(vnode);\n\t}\n\n\tconst props = vnode.props;\n\tconst dom = vnode._dom;\n\n\tif (\n\t\tdom != null &&\n\t\tvnode.type === 'textarea' &&\n\t\t'value' in props &&\n\t\tprops.value !== dom.value\n\t) {\n\t\tdom.value = props.value == null ? '' : props.value;\n\t}\n\n\tcurrentComponent = null;\n};\n\n// This is a very very private internal function for React it\n// is used to sort-of do runtime dependency injection.\nexport const __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {\n\tReactCurrentDispatcher: {\n\t\tcurrent: {\n\t\t\treadContext(context) {\n\t\t\t\treturn currentComponent._globalContext[context._id].props.value;\n\t\t\t},\n\t\t\tuseCallback,\n\t\t\tuseContext,\n\t\t\tuseDebugValue,\n\t\t\tuseDeferredValue,\n\t\t\tuseEffect,\n\t\t\tuseId,\n\t\t\tuseImperativeHandle,\n\t\t\tuseInsertionEffect,\n\t\t\tuseLayoutEffect,\n\t\t\tuseMemo,\n\t\t\t// useMutableSource, // experimental-only and replaced by uSES, likely not worth supporting\n\t\t\tuseReducer,\n\t\t\tuseRef,\n\t\t\tuseState,\n\t\t\tuseSyncExternalStore,\n\t\t\tuseTransition\n\t\t}\n\t}\n};\n", "import {\n\tcreateElement,\n\trender as preactRender,\n\tcloneElement as preactCloneElement,\n\tcreateRef,\n\tComponent,\n\tcreateContext,\n\tFragment\n} from 'preact';\nimport {\n\tuseState,\n\tuseId,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue\n} from 'preact/hooks';\nimport {\n\tuseInsertionEffect,\n\tstartTransition,\n\tuseDeferredValue,\n\tuseSyncExternalStore,\n\tuseTransition\n} from './hooks';\nimport { PureComponent } from './PureComponent';\nimport { memo } from './memo';\nimport { forwardRef } from './forwardRef';\nimport { Children } from './Children';\nimport { Suspense, lazy } from './suspense';\nimport { SuspenseList } from './suspense-list';\nimport { createPortal } from './portals';\nimport {\n\thydrate,\n\trender,\n\tREACT_ELEMENT_TYPE,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n} from './render';\n\nconst version = '18.3.1'; // trick libraries to think we are react\n\n/**\n * Legacy version of createElement.\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor\n */\nfunction createFactory(type) {\n\treturn createElement.bind(null, type);\n}\n\n/**\n * Check if the passed element is a valid (p)react node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isValidElement(element) {\n\treturn !!element && element.$$typeof === REACT_ELEMENT_TYPE;\n}\n\n/**\n * Check if the passed element is a Fragment node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isFragment(element) {\n\treturn isValidElement(element) && element.type === Fragment;\n}\n\n/**\n * Check if the passed element is a Memo node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isMemo(element) {\n\treturn (\n\t\t!!element &&\n\t\t!!element.displayName &&\n\t\t(typeof element.displayName === 'string' ||\n\t\t\telement.displayName instanceof String) &&\n\t\telement.displayName.startsWith('Memo(')\n\t);\n}\n\n/**\n * Wrap `cloneElement` to abort if the passed element is not a valid element and apply\n * all vnode normalizations.\n * @param {import('./internal').VNode} element The vnode to clone\n * @param {object} props Props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Optional component children\n */\nfunction cloneElement(element) {\n\tif (!isValidElement(element)) return element;\n\treturn preactCloneElement.apply(null, arguments);\n}\n\n/**\n * Remove a component tree from the DOM, including state and event handlers.\n * @param {import('./internal').PreactElement} container\n * @returns {boolean}\n */\nfunction unmountComponentAtNode(container) {\n\tif (container._children) {\n\t\tpreactRender(null, container);\n\t\treturn true;\n\t}\n\treturn false;\n}\n\n/**\n * Get the matching DOM node for a component\n * @param {import('./internal').Component} component\n * @returns {import('./internal').PreactElement | null}\n */\nfunction findDOMNode(component) {\n\treturn (\n\t\t(component &&\n\t\t\t(component.base || (component.nodeType === 1 && component))) ||\n\t\tnull\n\t);\n}\n\n/**\n * Deprecated way to control batched rendering inside the reconciler, but we\n * already schedule in batches inside our rendering code\n * @template Arg\n * @param {(arg: Arg) => void} callback function that triggers the updated\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n */\n// eslint-disable-next-line camelcase\nconst unstable_batchedUpdates = (callback, arg) => callback(arg);\n\n/**\n * In React, `flushSync` flushes the entire tree and forces a rerender. It's\n * implmented here as a no-op.\n * @template Arg\n * @template Result\n * @param {(arg: Arg) => Result} callback function that runs before the flush\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n * @returns\n */\nconst flushSync = (callback, arg) => callback(arg);\n\n/**\n * Strict Mode is not implemented in Preact, so we provide a stand-in for it\n * that just renders its children without imposing any restrictions.\n */\nconst StrictMode = Fragment;\n\n// compat to react-is\nexport const isElement = isValidElement;\n\nexport * from 'preact/hooks';\nexport {\n\tversion,\n\tChildren,\n\trender,\n\thydrate,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tisFragment,\n\tisMemo,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\tflushSync,\n\tuseInsertionEffect,\n\tstartTransition,\n\tuseDeferredValue,\n\tuseSyncExternalStore,\n\tuseTransition,\n\t// eslint-disable-next-line camelcase\n\tunstable_batchedUpdates,\n\tStrictMode,\n\tSuspense,\n\tSuspenseList,\n\tlazy,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n};\n\n// React copies the named exports to the default one.\nexport default {\n\tuseState,\n\tuseId,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseInsertionEffect,\n\tuseTransition,\n\tuseDeferredValue,\n\tuseSyncExternalStore,\n\tstartTransition,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue,\n\tversion,\n\tChildren,\n\trender,\n\thydrate,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tisElement,\n\tisFragment,\n\tisMemo,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\tflushSync,\n\tunstable_batchedUpdates,\n\tStrictMode,\n\tSuspense,\n\tSuspenseList,\n\tlazy,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n};\n", "import QRCode from \"qrcode\";\n\nexport function open(uri: string) {\n  // eslint-disable-next-line no-console\n  QRCode.toString(uri, { type: \"terminal\" }).then(console.log);\n}\n\nexport function close() {\n  // empty\n}\n", "export const WALLETCONNECT_STYLE_SHEET = `:root {\n  --animation-duration: 300ms;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n.animated {\n  animation-duration: var(--animation-duration);\n  animation-fill-mode: both;\n}\n\n.fadeIn {\n  animation-name: fadeIn;\n}\n\n.fadeOut {\n  animation-name: fadeOut;\n}\n\n#walletconnect-wrapper {\n  -webkit-user-select: none;\n  align-items: center;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  left: 0;\n  pointer-events: none;\n  position: fixed;\n  top: 0;\n  user-select: none;\n  width: 100%;\n  z-index: 99999999999999;\n}\n\n.walletconnect-modal__headerLogo {\n  height: 21px;\n}\n\n.walletconnect-modal__header p {\n  color: #ffffff;\n  font-size: 20px;\n  font-weight: 600;\n  margin: 0;\n  align-items: flex-start;\n  display: flex;\n  flex: 1;\n  margin-left: 5px;\n}\n\n.walletconnect-modal__close__wrapper {\n  position: absolute;\n  top: 0px;\n  right: 0px;\n  z-index: 10000;\n  background: white;\n  border-radius: 26px;\n  padding: 6px;\n  box-sizing: border-box;\n  width: 26px;\n  height: 26px;\n  cursor: pointer;\n}\n\n.walletconnect-modal__close__icon {\n  position: relative;\n  top: 7px;\n  right: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transform: rotate(45deg);\n}\n\n.walletconnect-modal__close__line1 {\n  position: absolute;\n  width: 100%;\n  border: 1px solid rgb(48, 52, 59);\n}\n\n.walletconnect-modal__close__line2 {\n  position: absolute;\n  width: 100%;\n  border: 1px solid rgb(48, 52, 59);\n  transform: rotate(90deg);\n}\n\n.walletconnect-qrcode__base {\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  background: rgba(37, 41, 46, 0.95);\n  height: 100%;\n  left: 0;\n  pointer-events: auto;\n  position: fixed;\n  top: 0;\n  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);\n  width: 100%;\n  will-change: opacity;\n  padding: 40px;\n  box-sizing: border-box;\n}\n\n.walletconnect-qrcode__text {\n  color: rgba(60, 66, 82, 0.6);\n  font-size: 16px;\n  font-weight: 600;\n  letter-spacing: 0;\n  line-height: 1.1875em;\n  margin: 10px 0 20px 0;\n  text-align: center;\n  width: 100%;\n}\n\n@media only screen and (max-width: 768px) {\n  .walletconnect-qrcode__text {\n    font-size: 4vw;\n  }\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-qrcode__text {\n    font-size: 14px;\n  }\n}\n\n.walletconnect-qrcode__image {\n  width: calc(100% - 30px);\n  box-sizing: border-box;\n  cursor: none;\n  margin: 0 auto;\n}\n\n.walletconnect-qrcode__notification {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  font-size: 16px;\n  padding: 16px 20px;\n  border-radius: 16px;\n  text-align: center;\n  transition: all 0.1s ease-in-out;\n  background: white;\n  color: black;\n  margin-bottom: -60px;\n  opacity: 0;\n}\n\n.walletconnect-qrcode__notification.notification__show {\n  opacity: 1;\n}\n\n@media only screen and (max-width: 768px) {\n  .walletconnect-modal__header {\n    height: 130px;\n  }\n  .walletconnect-modal__base {\n    overflow: auto;\n  }\n}\n\n@media only screen and (min-device-width: 415px) and (max-width: 768px) {\n  #content {\n    max-width: 768px;\n    box-sizing: border-box;\n  }\n}\n\n@media only screen and (min-width: 375px) and (max-width: 415px) {\n  #content {\n    max-width: 414px;\n    box-sizing: border-box;\n  }\n}\n\n@media only screen and (min-width: 320px) and (max-width: 375px) {\n  #content {\n    max-width: 375px;\n    box-sizing: border-box;\n  }\n}\n\n@media only screen and (max-width: 320px) {\n  #content {\n    max-width: 320px;\n    box-sizing: border-box;\n  }\n}\n\n.walletconnect-modal__base {\n  -webkit-font-smoothing: antialiased;\n  background: #ffffff;\n  border-radius: 24px;\n  box-shadow: 0 10px 50px 5px rgba(0, 0, 0, 0.4);\n  font-family: ui-rounded, \"SF Pro Rounded\", \"SF Pro Text\", medium-content-sans-serif-font,\n    -apple-system, BlinkMacSystemFont, ui-sans-serif, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell,\n    \"Open Sans\", \"Helvetica Neue\", sans-serif;\n  margin-top: 41px;\n  padding: 24px 24px 22px;\n  pointer-events: auto;\n  position: relative;\n  text-align: center;\n  transition: 0.4s cubic-bezier(0.19, 1, 0.22, 1);\n  will-change: transform;\n  overflow: visible;\n  transform: translateY(-50%);\n  top: 50%;\n  max-width: 500px;\n  margin: auto;\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-modal__base {\n    padding: 24px 12px;\n  }\n}\n\n.walletconnect-modal__base .hidden {\n  transform: translateY(150%);\n  transition: 0.125s cubic-bezier(0.4, 0, 1, 1);\n}\n\n.walletconnect-modal__header {\n  align-items: center;\n  display: flex;\n  height: 26px;\n  left: 0;\n  justify-content: space-between;\n  position: absolute;\n  top: -42px;\n  width: 100%;\n}\n\n.walletconnect-modal__base .wc-logo {\n  align-items: center;\n  display: flex;\n  height: 26px;\n  margin-top: 15px;\n  padding-bottom: 15px;\n  pointer-events: auto;\n}\n\n.walletconnect-modal__base .wc-logo div {\n  background-color: #3399ff;\n  height: 21px;\n  margin-right: 5px;\n  mask-image: url(\"images/wc-logo.svg\") center no-repeat;\n  width: 32px;\n}\n\n.walletconnect-modal__base .wc-logo p {\n  color: #ffffff;\n  font-size: 20px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.walletconnect-modal__base h2 {\n  color: rgba(60, 66, 82, 0.6);\n  font-size: 16px;\n  font-weight: 600;\n  letter-spacing: 0;\n  line-height: 1.1875em;\n  margin: 0 0 19px 0;\n  text-align: center;\n  width: 100%;\n}\n\n.walletconnect-modal__base__row {\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  align-items: center;\n  border-radius: 20px;\n  cursor: pointer;\n  display: flex;\n  height: 56px;\n  justify-content: space-between;\n  padding: 0 15px;\n  position: relative;\n  margin: 0px 0px 8px;\n  text-align: left;\n  transition: 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  will-change: transform;\n  text-decoration: none;\n}\n\n.walletconnect-modal__base__row:hover {\n  background: rgba(60, 66, 82, 0.06);\n}\n\n.walletconnect-modal__base__row:active {\n  background: rgba(60, 66, 82, 0.06);\n  transform: scale(0.975);\n  transition: 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n}\n\n.walletconnect-modal__base__row__h3 {\n  color: #25292e;\n  font-size: 20px;\n  font-weight: 700;\n  margin: 0;\n  padding-bottom: 3px;\n}\n\n.walletconnect-modal__base__row__right {\n  align-items: center;\n  display: flex;\n  justify-content: center;\n}\n\n.walletconnect-modal__base__row__right__app-icon {\n  border-radius: 8px;\n  height: 34px;\n  margin: 0 11px 2px 0;\n  width: 34px;\n  background-size: 100%;\n  box-shadow: 0 4px 12px 0 rgba(37, 41, 46, 0.25);\n}\n\n.walletconnect-modal__base__row__right__caret {\n  height: 18px;\n  opacity: 0.3;\n  transition: 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n  width: 8px;\n  will-change: opacity;\n}\n\n.walletconnect-modal__base__row:hover .caret,\n.walletconnect-modal__base__row:active .caret {\n  opacity: 0.6;\n}\n\n.walletconnect-modal__mobile__toggle {\n  width: 80%;\n  display: flex;\n  margin: 0 auto;\n  position: relative;\n  overflow: hidden;\n  border-radius: 8px;\n  margin-bottom: 18px;\n  background: #d4d5d9;\n}\n\n.walletconnect-modal__single_wallet {\n  display: flex;\n  justify-content: center;\n  margin-top: 7px;\n  margin-bottom: 18px;\n}\n\n.walletconnect-modal__single_wallet a {\n  cursor: pointer;\n  color: rgb(64, 153, 255);\n  font-size: 21px;\n  font-weight: 800;\n  text-decoration: none !important;\n  margin: 0 auto;\n}\n\n.walletconnect-modal__mobile__toggle_selector {\n  width: calc(50% - 8px);\n  background: white;\n  position: absolute;\n  border-radius: 5px;\n  height: calc(100% - 8px);\n  top: 4px;\n  transition: all 0.2s ease-in-out;\n  transform: translate3d(4px, 0, 0);\n}\n\n.walletconnect-modal__mobile__toggle.right__selected .walletconnect-modal__mobile__toggle_selector {\n  transform: translate3d(calc(100% + 12px), 0, 0);\n}\n\n.walletconnect-modal__mobile__toggle a {\n  font-size: 12px;\n  width: 50%;\n  text-align: center;\n  padding: 8px;\n  margin: 0;\n  font-weight: 600;\n  z-index: 1;\n}\n\n.walletconnect-modal__footer {\n  display: flex;\n  justify-content: center;\n  margin-top: 20px;\n}\n\n@media only screen and (max-width: 768px) {\n  .walletconnect-modal__footer {\n    margin-top: 5vw;\n  }\n}\n\n.walletconnect-modal__footer a {\n  cursor: pointer;\n  color: #898d97;\n  font-size: 15px;\n  margin: 0 auto;\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-modal__footer a {\n    font-size: 14px;\n  }\n}\n\n.walletconnect-connect__buttons__wrapper {\n  max-height: 44vh;\n}\n\n.walletconnect-connect__buttons__wrapper__android {\n  margin: 50% 0;\n}\n\n.walletconnect-connect__buttons__wrapper__wrap {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  margin: 10px 0;\n}\n\n@media only screen and (min-width: 768px) {\n  .walletconnect-connect__buttons__wrapper__wrap {\n    margin-top: 40px;\n  }\n}\n\n.walletconnect-connect__button {\n  background-color: rgb(64, 153, 255);\n  padding: 12px;\n  border-radius: 8px;\n  text-decoration: none;\n  color: rgb(255, 255, 255);\n  font-weight: 500;\n}\n\n.walletconnect-connect__button__icon_anchor {\n  cursor: pointer;\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  margin: 8px;\n  width: 42px;\n  justify-self: center;\n  flex-direction: column;\n  text-decoration: none !important;\n}\n\n@media only screen and (max-width: 320px) {\n  .walletconnect-connect__button__icon_anchor {\n    margin: 4px;\n  }\n}\n\n.walletconnect-connect__button__icon {\n  border-radius: 10px;\n  height: 42px;\n  margin: 0;\n  width: 42px;\n  background-size: cover !important;\n  box-shadow: 0 4px 12px 0 rgba(37, 41, 46, 0.25);\n}\n\n.walletconnect-connect__button__text {\n  color: #424952;\n  font-size: 2.7vw;\n  text-decoration: none !important;\n  padding: 0;\n  margin-top: 1.8vw;\n  font-weight: 600;\n}\n\n@media only screen and (min-width: 768px) {\n  .walletconnect-connect__button__text {\n    font-size: 16px;\n    margin-top: 12px;\n  }\n}\n\n.walletconnect-search__input {\n  border: none;\n  background: #d4d5d9;\n  border-style: none;\n  padding: 8px 16px;\n  outline: none;\n  font-style: normal;\n  font-stretch: normal;\n  font-size: 16px;\n  font-style: normal;\n  font-stretch: normal;\n  line-height: normal;\n  letter-spacing: normal;\n  text-align: left;\n  border-radius: 8px;\n  width: calc(100% - 16px);\n  margin: 0;\n  margin-bottom: 8px;\n}\n`;", "// A type of promise-like that resolves synchronously and supports only one observer\nexport const _Pact = /*#__PURE__*/(function() {\n\tfunction _Pact() {}\n\t_Pact.prototype.then = function(onFulfilled, onRejected) {\n\t\tconst result = new _Pact();\n\t\tconst state = this.s;\n\t\tif (state) {\n\t\t\tconst callback = state & 1 ? onFulfilled : onRejected;\n\t\t\tif (callback) {\n\t\t\t\ttry {\n\t\t\t\t\t_settle(result, 1, callback(this.v));\n\t\t\t\t} catch (e) {\n\t\t\t\t\t_settle(result, 2, e);\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t} else {\n\t\t\t\treturn this;\n\t\t\t}\n\t\t}\n\t\tthis.o = function(_this) {\n\t\t\ttry {\n\t\t\t\tconst value = _this.v;\n\t\t\t\tif (_this.s & 1) {\n\t\t\t\t\t_settle(result, 1, onFulfilled ? onFulfilled(value) : value);\n\t\t\t\t} else if (onRejected) {\n\t\t\t\t\t_settle(result, 1, onRejected(value));\n\t\t\t\t} else {\n\t\t\t\t\t_settle(result, 2, value);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t_settle(result, 2, e);\n\t\t\t}\n\t\t};\n\t\treturn result;\n\t}\n\treturn _Pact;\n})();\n\n// Settles a pact synchronously\nexport function _settle(pact, state, value) {\n\tif (!pact.s) {\n\t\tif (value instanceof _Pact) {\n\t\t\tif (value.s) {\n\t\t\t\tif (state & 1) {\n\t\t\t\t\tstate = value.s;\n\t\t\t\t}\n\t\t\t\tvalue = value.v;\n\t\t\t} else {\n\t\t\t\tvalue.o = _settle.bind(null, pact, state);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\tif (value && value.then) {\n\t\t\tvalue.then(_settle.bind(null, pact, state), _settle.bind(null, pact, 2));\n\t\t\treturn;\n\t\t}\n\t\tpact.s = state;\n\t\tpact.v = value;\n\t\tconst observer = pact.o;\n\t\tif (observer) {\n\t\t\tobserver(pact);\n\t\t}\n\t}\n}\n\nexport function _isSettledPact(thenable) {\n\treturn thenable instanceof _Pact && thenable.s & 1;\n}\n\n// Converts argument to a function that always returns a Promise\nexport function _async(f) {\n\treturn function() {\n\t\tfor (var args = [], i = 0; i < arguments.length; i++) {\n\t\t\targs[i] = arguments[i];\n\t\t}\n\t\ttry {\n\t\t\treturn Promise.resolve(f.apply(this, args));\n\t\t} catch(e) {\n\t\t\treturn Promise.reject(e);\n\t\t}\n\t}\n}\n\n// Awaits on a value that may or may not be a Promise (equivalent to the await keyword in ES2015, with continuations passed explicitly)\nexport function _await(value, then, direct) {\n\tif (direct) {\n\t\treturn then ? then(value) : value;\n\t}\n\tif (!value || !value.then) {\n\t\tvalue = Promise.resolve(value);\n\t}\n\treturn then ? value.then(then) : value;\n}\n\n// Awaits on a value that may or may not be a Promise, then ignores it\nexport function _awaitIgnored(value, direct) {\n\tif (!direct) {\n\t\treturn value && value.then ? value.then(_empty) : Promise.resolve();\n\t}\n}\n\n// Proceeds after a value has resolved, or proceeds immediately if the value is not thenable\nexport function _continue(value, then) {\n\treturn value && value.then ? value.then(then) : then(value);\n}\n\n// Proceeds after a value has resolved, or proceeds immediately if the value is not thenable\nexport function _continueIgnored(value) {\n\tif (value && value.then) {\n\t\treturn value.then(_empty);\n\t}\n}\n\n// Asynchronously iterate through an object that has a length property, passing the index as the first argument to the callback (even as the length property changes)\nexport function _forTo(array, body, check) {\n\tvar i = -1, pact, reject;\n\tfunction _cycle(result) {\n\t\ttry {\n\t\t\twhile (++i < array.length && (!check || !check())) {\n\t\t\t\tresult = body(i);\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.then(_cycle, reject || (reject = _settle.bind(null, pact = new _Pact(), 2)));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (pact) {\n\t\t\t\t_settle(pact, 1, result);\n\t\t\t} else {\n\t\t\t\tpact = result;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\t_settle(pact || (pact = new _Pact()), 2, e);\n\t\t}\n\t}\n\t_cycle();\n\treturn pact;\n}\n\n// Asynchronously iterate through an object's properties (including properties inherited from the prototype)\n// Uses a snapshot of the object's properties\nexport function _forIn(target, body, check) {\n\tvar keys = [];\n\tfor (var key in target) {\n\t\tkeys.push(key);\n\t}\n\treturn _forTo(keys, function(i) { return body(keys[i]); }, check);\n}\n\n// Asynchronously iterate through an object's own properties (excluding properties inherited from the prototype)\n// Uses a snapshot of the object's properties\nexport function _forOwn(target, body, check) {\n\tvar keys = [];\n\tfor (var key in target) {\n\t\tif (Object.prototype.hasOwnProperty.call(target, key)) {\n\t\t\tkeys.push(key);\n\t\t}\n\t}\n\treturn _forTo(keys, function(i) { return body(keys[i]); }, check);\n}\n\nexport const _iteratorSymbol = /*#__PURE__*/ typeof Symbol !== \"undefined\" ? (Symbol.iterator || (Symbol.iterator = Symbol(\"Symbol.iterator\"))) : \"@@iterator\";\n\n// Asynchronously iterate through an object's values\n// Uses for...of if the runtime supports it, otherwise iterates until length on a copy\nexport function _forOf(target, body, check) {\n\tif (typeof target[_iteratorSymbol] === \"function\") {\n\t\tvar iterator = target[_iteratorSymbol](), step, pact, reject;\n\t\tfunction _cycle(result) {\n\t\t\ttry {\n\t\t\t\twhile (!(step = iterator.next()).done && (!check || !check())) {\n\t\t\t\t\tresult = body(step.value);\n\t\t\t\t\tif (result && result.then) {\n\t\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresult.then(_cycle, reject || (reject = _settle.bind(null, pact = new _Pact(), 2)));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (pact) {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t} else {\n\t\t\t\t\tpact = result;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t_settle(pact || (pact = new _Pact()), 2, e);\n\t\t\t}\n\t\t}\n\t\t_cycle();\n\t\tif (iterator.return) {\n\t\t\tvar _fixup = function(value) {\n\t\t\t\ttry {\n\t\t\t\t\tif (!step.done) {\n\t\t\t\t\t\titerator.return();\n\t\t\t\t\t}\n\t\t\t\t} catch(e) {\n\t\t\t\t}\n\t\t\t\treturn value;\n\t\t\t}\n\t\t\tif (pact && pact.then) {\n\t\t\t\treturn pact.then(_fixup, function(e) {\n\t\t\t\t\tthrow _fixup(e);\n\t\t\t\t});\n\t\t\t}\n\t\t\t_fixup();\n\t\t}\n\t\treturn pact;\n\t}\n\t// No support for Symbol.iterator\n\tif (!(\"length\" in target)) {\n\t\tthrow new TypeError(\"Object is not iterable\");\n\t}\n\t// Handle live collections properly\n\tvar values = [];\n\tfor (var i = 0; i < target.length; i++) {\n\t\tvalues.push(target[i]);\n\t}\n\treturn _forTo(values, function(i) { return body(values[i]); }, check);\n}\n\nexport const _asyncIteratorSymbol = /*#__PURE__*/ typeof Symbol !== \"undefined\" ? (Symbol.asyncIterator || (Symbol.asyncIterator = Symbol(\"Symbol.asyncIterator\"))) : \"@@asyncIterator\";\n\n// Asynchronously iterate on a value using it's async iterator if present, or its synchronous iterator if missing\nexport function _forAwaitOf(target, body, check) {\n\tif (typeof target[_asyncIteratorSymbol] === \"function\") {\n\t\tvar pact = new _Pact();\n\t\tvar iterator = target[_asyncIteratorSymbol]();\n\t\titerator.next().then(_resumeAfterNext).then(void 0, _reject);\n\t\treturn pact;\n\t\tfunction _resumeAfterBody(result) {\n\t\t\tif (check && check()) {\n\t\t\t\treturn _settle(pact, 1, iterator.return ? iterator.return().then(function() { return result; }) : result);\n\t\t\t}\n\t\t\titerator.next().then(_resumeAfterNext).then(void 0, _reject);\n\t\t}\n\t\tfunction _resumeAfterNext(step) {\n\t\t\tif (step.done) {\n\t\t\t\t_settle(pact, 1);\n\t\t\t} else {\n\t\t\t\tPromise.resolve(body(step.value)).then(_resumeAfterBody).then(void 0, _reject);\n\t\t\t}\n\t\t}\n\t\tfunction _reject(error) {\n\t\t\t_settle(pact, 2, iterator.return ? iterator.return().then(function() { return error; }) : error);\n\t\t}\n\t}\n\treturn Promise.resolve(_forOf(target, function(value) { return Promise.resolve(value).then(body); }, check));\n}\n\n// Asynchronously implement a generic for loop\nexport function _for(test, update, body) {\n\tvar stage;\n\tfor (;;) {\n\t\tvar shouldContinue = test();\n\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\tshouldContinue = shouldContinue.v;\n\t\t}\n\t\tif (!shouldContinue) {\n\t\t\treturn result;\n\t\t}\n\t\tif (shouldContinue.then) {\n\t\t\tstage = 0;\n\t\t\tbreak;\n\t\t}\n\t\tvar result = body();\n\t\tif (result && result.then) {\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.s;\n\t\t\t} else {\n\t\t\t\tstage = 1;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (update) {\n\t\t\tvar updateValue = update();\n\t\t\tif (updateValue && updateValue.then && !_isSettledPact(updateValue)) {\n\t\t\t\tstage = 2;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\tvar pact = new _Pact();\n\tvar reject = _settle.bind(null, pact, 2);\n\t(stage === 0 ? shouldContinue.then(_resumeAfterTest) : stage === 1 ? result.then(_resumeAfterBody) : updateValue.then(_resumeAfterUpdate)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterBody(value) {\n\t\tresult = value;\n\t\tdo {\n\t\t\tif (update) {\n\t\t\t\tupdateValue = update();\n\t\t\t\tif (updateValue && updateValue.then && !_isSettledPact(updateValue)) {\n\t\t\t\t\tupdateValue.then(_resumeAfterUpdate).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\tshouldContinue = test();\n\t\t\tif (!shouldContinue || (_isSettledPact(shouldContinue) && !shouldContinue.v)) {\n\t\t\t\t_settle(pact, 1, result);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.v;\n\t\t\t}\n\t\t} while (!result || !result.then);\n\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t}\n\tfunction _resumeAfterTest(shouldContinue) {\n\t\tif (shouldContinue) {\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t} else {\n\t\t\t\t_resumeAfterBody(result);\n\t\t\t}\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n\tfunction _resumeAfterUpdate() {\n\t\tif (shouldContinue = test()) {\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t} else {\n\t\t\t\t_resumeAfterTest(shouldContinue);\n\t\t\t}\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n}\n\n// Asynchronously implement a do ... while loop\nexport function _do(body, test) {\n\tvar awaitBody;\n\tdo {\n\t\tvar result = body();\n\t\tif (result && result.then) {\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.v;\n\t\t\t} else {\n\t\t\t\tawaitBody = true;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tvar shouldContinue = test();\n\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\tshouldContinue = shouldContinue.v;\n\t\t}\n\t\tif (!shouldContinue) {\n\t\t\treturn result;\n\t\t}\n\t} while (!shouldContinue.then);\n\tconst pact = new _Pact();\n\tconst reject = _settle.bind(null, pact, 2);\n\t(awaitBody ? result.then(_resumeAfterBody) : shouldContinue.then(_resumeAfterTest)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterBody(value) {\n\t\tresult = value;\n\t\tfor (;;) {\n\t\t\tshouldContinue = test();\n\t\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\t\tshouldContinue = shouldContinue.v;\n\t\t\t}\n\t\t\tif (!shouldContinue) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\tresult = result.v;\n\t\t\t\t} else {\n\t\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t_settle(pact, 1, result);\n\t}\n\tfunction _resumeAfterTest(shouldContinue) {\n\t\tif (shouldContinue) {\n\t\t\tdo {\n\t\t\t\tresult = body();\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tshouldContinue = test();\n\t\t\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\t\t\tshouldContinue = shouldContinue.v;\n\t\t\t\t}\n\t\t\t\tif (!shouldContinue) {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} while (!shouldContinue.then);\n\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n}\n\n// Asynchronously implement a switch statement\nexport function _switch(discriminant, cases) {\n\tvar dispatchIndex = -1;\n\tvar awaitBody;\n\touter: {\n\t\tfor (var i = 0; i < cases.length; i++) {\n\t\t\tvar test = cases[i][0];\n\t\t\tif (test) {\n\t\t\t\tvar testValue = test();\n\t\t\t\tif (testValue && testValue.then) {\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t\tif (testValue === discriminant) {\n\t\t\t\t\tdispatchIndex = i;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Found the default case, set it as the pending dispatch case\n\t\t\t\tdispatchIndex = i;\n\t\t\t}\n\t\t}\n\t\tif (dispatchIndex !== -1) {\n\t\t\tdo {\n\t\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\t\twhile (!body) {\n\t\t\t\t\tdispatchIndex++;\n\t\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t\t}\n\t\t\t\tvar result = body();\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tawaitBody = true;\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\t\tdispatchIndex++;\n\t\t\t} while (fallthroughCheck && !fallthroughCheck());\n\t\t\treturn result;\n\t\t}\n\t}\n\tconst pact = new _Pact();\n\tconst reject = _settle.bind(null, pact, 2);\n\t(awaitBody ? result.then(_resumeAfterBody) : testValue.then(_resumeAfterTest)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterTest(value) {\n\t\tfor (;;) {\n\t\t\tif (value === discriminant) {\n\t\t\t\tdispatchIndex = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif (++i === cases.length) {\n\t\t\t\tif (dispatchIndex !== -1) {\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\ttest = cases[i][0];\n\t\t\tif (test) {\n\t\t\t\tvalue = test();\n\t\t\t\tif (value && value.then) {\n\t\t\t\t\tvalue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tdispatchIndex = i;\n\t\t\t}\n\t\t}\n\t\tdo {\n\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\twhile (!body) {\n\t\t\t\tdispatchIndex++;\n\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t}\n\t\t\tvar result = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\tdispatchIndex++;\n\t\t} while (fallthroughCheck && !fallthroughCheck());\n\t\t_settle(pact, 1, result);\n\t}\n\tfunction _resumeAfterBody(result) {\n\t\tfor (;;) {\n\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\tif (!fallthroughCheck || fallthroughCheck()) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tdispatchIndex++;\n\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\twhile (!body) {\n\t\t\t\tdispatchIndex++;\n\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\t_settle(pact, 1, result);\n\t}\n}\n\n// Asynchronously call a function and pass the result to explicitly passed continuations\nexport function _call(body, then, direct) {\n\tif (direct) {\n\t\treturn then ? then(body()) : body();\n\t}\n\ttry {\n\t\tvar result = Promise.resolve(body());\n\t\treturn then ? result.then(then) : result;\n\t} catch (e) {\n\t\treturn Promise.reject(e);\n\t}\n}\n\n// Asynchronously call a function and swallow the result\nexport function _callIgnored(body, direct) {\n\treturn _call(body, _empty, direct);\n}\n\n// Asynchronously call a function and pass the result to explicitly passed continuations\nexport function _invoke(body, then) {\n\tvar result = body();\n\tif (result && result.then) {\n\t\treturn result.then(then);\n\t}\n\treturn then(result);\n}\n\n// Asynchronously call a function and swallow the result\nexport function _invokeIgnored(body) {\n\tvar result = body();\n\tif (result && result.then) {\n\t\treturn result.then(_empty);\n\t}\n}\n\n// Asynchronously call a function and send errors to recovery continuation\nexport function _catch(body, recover) {\n\ttry {\n\t\tvar result = body();\n\t} catch(e) {\n\t\treturn recover(e);\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(void 0, recover);\n\t}\n\treturn result;\n}\n\n// Asynchronously await a promise and pass the result to a finally continuation\nexport function _finallyRethrows(body, finalizer) {\n\ttry {\n\t\tvar result = body();\n\t} catch (e) {\n\t\treturn finalizer(true, e);\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(finalizer.bind(null, false), finalizer.bind(null, true));\n\t}\n\treturn finalizer(false, result);\n}\n\n// Asynchronously await a promise and invoke a finally continuation that always overrides the result\nexport function _finally(body, finalizer) {\n\ttry {\n\t\tvar result = body();\n\t} catch (e) {\n\t\treturn finalizer();\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(finalizer, finalizer);\n\t}\n\treturn finalizer();\n}\n\n// Rethrow or return a value from a finally continuation\nexport function _rethrow(thrown, value) {\n\tif (thrown)\n\t\tthrow value;\n\treturn value;\n}\n\n// Empty function to implement break and other control flow that ignores asynchronous results\nexport function _empty() {\n}\n\n// Sentinel value for early returns in generators \nexport const _earlyReturn = /*#__PURE__*/ {};\n\n// Asynchronously call a function and send errors to recovery continuation, skipping early returns\nexport function _catchInGenerator(body, recover) {\n\treturn _catch(body, function(e) {\n\t\tif (e === _earlyReturn) {\n\t\t\tthrow e;\n\t\t}\n\t\treturn recover(e);\n\t});\n}\n\n// Asynchronous generator class; accepts the entrypoint of the generator, to which it passes itself when the generator should start\nexport const _AsyncGenerator = /*#__PURE__*/(function() {\n\tfunction _AsyncGenerator(entry) {\n\t\tthis._entry = entry;\n\t\tthis._pact = null;\n\t\tthis._resolve = null;\n\t\tthis._return = null;\n\t\tthis._promise = null;\n\t}\n\n\tfunction _wrapReturnedValue(value) {\n\t\treturn { value: value, done: true };\n\t}\n\tfunction _wrapYieldedValue(value) {\n\t\treturn { value: value, done: false };\n\t}\n\n\t_AsyncGenerator.prototype._yield = function(value) {\n\t\t// Yield the value to the pending next call\n\t\tthis._resolve(value && value.then ? value.then(_wrapYieldedValue) : _wrapYieldedValue(value));\n\t\t// Return a pact for an upcoming next/return/throw call\n\t\treturn this._pact = new _Pact();\n\t};\n\t_AsyncGenerator.prototype.next = function(value) {\n\t\t// Advance the generator, starting it if it has yet to be started\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tconst _entry = _this._entry;\n\t\t\t\tif (_entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the next call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Start the generator\n\t\t\t\t_this._entry = null;\n\t\t\t\t_this._resolve = resolve;\n\t\t\t\tfunction returnValue(value) {\n\t\t\t\t\t_this._resolve(value && value.then ? value.then(_wrapReturnedValue) : _wrapReturnedValue(value));\n\t\t\t\t\t_this._pact = null;\n\t\t\t\t\t_this._resolve = null;\n\t\t\t\t}\n\t\t\t\tvar result = _entry(_this);\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tresult.then(returnValue, function(error) {\n\t\t\t\t\t\tif (error === _earlyReturn) {\n\t\t\t\t\t\t\treturnValue(_this._return);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst pact = new _Pact();\n\t\t\t\t\t\t\t_this._resolve(pact);\n\t\t\t\t\t\t\t_this._pact = null;\n\t\t\t\t\t\t\t_this._resolve = null;\n\t\t\t\t\t\t\t_resolve(pact, 2, error);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\treturnValue(result);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Generator is started and a yield expression is pending, settle it\n\t\t\t\t_this._pact = null;\n\t\t\t\t_this._resolve = resolve;\n\t\t\t\t_settle(_pact, 1, value);\n\t\t\t}\n\t\t});\n\t};\n\t_AsyncGenerator.prototype.return = function(value) {\n\t\t// Early return from the generator if started, otherwise abandons the generator\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tif (_this._entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the return call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Generator is not started, abandon it and return the specified value\n\t\t\t\t_this._entry = null;\n\t\t\t\treturn resolve(value && value.then ? value.then(_wrapReturnedValue) : _wrapReturnedValue(value));\n\t\t\t}\n\t\t\t// Settle the yield expression with a rejected \"early return\" value\n\t\t\t_this._return = value;\n\t\t\t_this._resolve = resolve;\n\t\t\t_this._pact = null;\n\t\t\t_settle(_pact, 2, _earlyReturn);\n\t\t});\n\t};\n\t_AsyncGenerator.prototype.throw = function(error) {\n\t\t// Inject an exception into the pending yield expression\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve, reject) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tif (_this._entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the throw call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Generator is not started, abandon it and return a rejected Promise containing the error\n\t\t\t\t_this._entry = null;\n\t\t\t\treturn reject(error);\n\t\t\t}\n\t\t\t// Settle the yield expression with the value as a rejection\n\t\t\t_this._resolve = resolve;\n\t\t\t_this._pact = null;\n\t\t\t_settle(_pact, 2, error);\n\t\t});\n\t};\n\n\t_AsyncGenerator.prototype[_asyncIteratorSymbol] = function() {\n\t\treturn this;\n\t};\n\t\n\treturn _AsyncGenerator;\n})();\n", "export const WALLETCONNECT_LOGO_SVG_URL = `data:image/svg+xml,%3C?xml version='1.0' encoding='UTF-8'?%3E %3Csvg width='300px' height='185px' viewBox='0 0 300 185' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E %3C!-- Generator: Sketch 49.3 (51167) - http://www.bohemiancoding.com/sketch --%3E %3Ctitle%3EWalletConnect%3C/title%3E %3Cdesc%3ECreated with Sketch.%3C/desc%3E %3Cdefs%3E%3C/defs%3E %3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E %3Cg id='walletconnect-logo-alt' fill='%233B99FC' fill-rule='nonzero'%3E %3Cpath d='M61.4385429,36.2562612 C110.349767,-11.6319051 189.65053,-11.6319051 238.561752,36.2562612 L244.448297,42.0196786 C246.893858,44.4140867 246.893858,48.2961898 244.448297,50.690599 L224.311602,70.406102 C223.088821,71.6033071 221.106302,71.6033071 219.883521,70.406102 L211.782937,62.4749541 C177.661245,29.0669724 122.339051,29.0669724 88.2173582,62.4749541 L79.542302,70.9685592 C78.3195204,72.1657633 76.337001,72.1657633 75.1142214,70.9685592 L54.9775265,51.2530561 C52.5319653,48.8586469 52.5319653,44.9765439 54.9775265,42.5821357 L61.4385429,36.2562612 Z M280.206339,77.0300061 L298.128036,94.5769031 C300.573585,96.9713 300.573599,100.85338 298.128067,103.247793 L217.317896,182.368927 C214.872352,184.763353 210.907314,184.76338 208.461736,182.368989 C208.461726,182.368979 208.461714,182.368967 208.461704,182.368957 L151.107561,126.214385 C150.496171,125.615783 149.504911,125.615783 148.893521,126.214385 C148.893517,126.214389 148.893514,126.214393 148.89351,126.214396 L91.5405888,182.368927 C89.095052,184.763359 85.1300133,184.763399 82.6844276,182.369014 C82.6844133,182.369 82.684398,182.368986 82.6843827,182.36897 L1.87196327,103.246785 C-0.573596939,100.852377 -0.573596939,96.9702735 1.87196327,94.5758653 L19.7936929,77.028998 C22.2392531,74.6345898 26.2042918,74.6345898 28.6498531,77.028998 L86.0048306,133.184355 C86.6162214,133.782957 87.6074796,133.782957 88.2188704,133.184355 C88.2188796,133.184346 88.2188878,133.184338 88.2188969,133.184331 L145.571,77.028998 C148.016505,74.6345347 151.981544,74.6344449 154.427161,77.028798 C154.427195,77.0288316 154.427229,77.0288653 154.427262,77.028899 L211.782164,133.184331 C212.393554,133.782932 213.384814,133.782932 213.996204,133.184331 L271.350179,77.0300061 C273.79574,74.6355969 277.760778,74.6355969 280.206339,77.0300061 Z' id='WalletConnect'%3E%3C/path%3E %3C/g%3E %3C/g%3E %3C/svg%3E`;", "export const WALLETCONNECT_HEADER_TEXT = \"WalletConnect\";\n\nexport const ANIMATION_DURATION = 300;\nexport const DEFAULT_BUTTON_COLOR = \"rgb(64, 153, 255)\";\n\nexport const WALLETCONNECT_WRAPPER_ID = \"walletconnect-wrapper\";\nexport const WALLETCONNECT_STYLE_ID = \"walletconnect-style-sheet\";\nexport const WALLETCONNECT_MODAL_ID = \"walletconnect-qrcode-modal\";\nexport const WALLETCONNECT_CLOSE_BUTTON_ID = \"walletconnect-qrcode-close\";\nexport const WALLETCONNECT_CTA_TEXT_ID = \"walletconnect-qrcode-text\";\nexport const WALLETCONNECT_CONNECT_BUTTON_ID = \"walletconnect-connect-button\";\n", "// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport * as React from \"react\";\n\nimport { WALLETCONNECT_LOGO_SVG_URL } from \"../assets/logo\";\nimport { WALLETCONNECT_HEADER_TEXT, WALLETCONNECT_CLOSE_BUTTON_ID } from \"../constants\";\n\ninterface HeaderProps {\n  onClose: any;\n}\n\nfunction Header(props: HeaderProps) {\n  return (\n    <div className=\"walletconnect-modal__header\">\n      <img src={WALLETCONNECT_LOGO_SVG_URL} className=\"walletconnect-modal__headerLogo\" />\n      <p>{WALLETCONNECT_HEADER_TEXT}</p>\n      <div className=\"walletconnect-modal__close__wrapper\" onClick={props.onClose}>\n        <div id={WALLETCONNECT_CLOSE_BUTTON_ID} className=\"walletconnect-modal__close__icon\">\n          <div className=\"walletconnect-modal__close__line1\"></div>\n          <div className=\"walletconnect-modal__close__line2\"></div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default Header;\n", "import * as React from \"react\";\n\nimport { WALLETCONNECT_CONNECT_BUTTON_ID } from \"../constants\";\n\ninterface ConnectButtonProps {\n  name: string;\n  color: string;\n  href: string;\n  onClick: (event: React.MouseEvent<HTMLAnchorElement>) => void;\n}\n\nfunction ConnectButton(props: ConnectButtonProps) {\n  return (\n    <a\n      className=\"walletconnect-connect__button\"\n      href={props.href}\n      id={`${WALLETCONNECT_CONNECT_BUTTON_ID}-${props.name}`}\n      onClick={props.onClick}\n      rel=\"noopener noreferrer\"\n      style={{ backgroundColor: props.color }}\n      target=\"_blank\"\n    >\n      {props.name}\n    </a>\n  );\n}\n\nexport default ConnectButton;\n", "export const CARET_SVG_URL = `data:image/svg+xml,%3Csvg width='8' height='18' viewBox='0 0 8 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0.586301 0.213898C0.150354 0.552968 0.0718197 1.18124 0.41089 1.61719L5.2892 7.88931C5.57007 8.25042 5.57007 8.75608 5.2892 9.11719L0.410889 15.3893C0.071819 15.8253 0.150353 16.4535 0.586301 16.7926C1.02225 17.1317 1.65052 17.0531 1.98959 16.6172L6.86791 10.3451C7.7105 9.26174 7.7105 7.74476 6.86791 6.66143L1.98959 0.38931C1.65052 -0.0466374 1.02225 -0.125172 0.586301 0.213898Z' fill='%233C4252'/%3E %3C/svg%3E`;", "import * as React from \"react\";\nimport { CARET_SVG_URL } from \"../assets/caret\";\n\ninterface WalletButtonProps {\n  color: string;\n  name: string;\n  logo: string;\n  href: string;\n  onClick: (event: React.MouseEvent<HTMLAnchorElement>) => void;\n}\n\nfunction WalletButton(props: WalletButtonProps) {\n  const { color, href, name, logo, onClick } = props;\n  return (\n    <a\n      className=\"walletconnect-modal__base__row\"\n      href={href}\n      onClick={onClick}\n      rel=\"noopener noreferrer\"\n      target=\"_blank\"\n    >\n      <h3 className={\"walletconnect-modal__base__row__h3\"}>{name}</h3>\n      <div className=\"walletconnect-modal__base__row__right\">\n        <div\n          className={`walletconnect-modal__base__row__right__app-icon`}\n          style={{ background: `url('${logo}') ${color}`, backgroundSize: \"100%\" }}\n        ></div>\n        <img src={CARET_SVG_URL} className=\"walletconnect-modal__base__row__right__caret\" />\n      </div>\n    </a>\n  );\n}\n\nexport default WalletButton;\n", "import * as React from \"react\";\n\ninterface WalletIconProps {\n  color: string;\n  logo: string;\n  href: string;\n  name: string;\n  onClick: (event: React.MouseEvent<HTMLAnchorElement>) => void;\n}\n\nfunction WalletIcon(props: WalletIconProps) {\n  const { color, href, name, logo, onClick } = props;\n  const fontSize = window.innerWidth < 768 ? `${name.length > 8 ? 2.5 : 2.7}vw` : \"inherit\";\n  return (\n    <a\n      className=\"walletconnect-connect__button__icon_anchor\"\n      href={href}\n      onClick={onClick}\n      rel=\"noopener noreferrer\"\n      target=\"_blank\"\n    >\n      <div\n        className=\"walletconnect-connect__button__icon\"\n        style={{ background: `url('${logo}') ${color}`, backgroundSize: \"100%\" }}\n      ></div>\n      <div style={{ fontSize }} className={\"walletconnect-connect__button__text\"}>\n        {name}\n      </div>\n    </a>\n  );\n}\n\nexport default WalletIcon;\n", "import * as React from \"react\";\nimport { IMobileRegistryEntry, IQRCodeModalOptions } from \"@walletconnect/legacy-types\";\nimport { isAndroid, formatIOSMobile, saveMobileLinkInfo } from \"@walletconnect/legacy-utils\";\n\nimport { DEFAULT_BUTTON_COLOR, WALLETCONNECT_CTA_TEXT_ID } from \"../constants\";\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport ConnectButton from \"./ConnectButton\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport WalletButton from \"./WalletButton\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport WalletIcon from \"./WalletIcon\";\nimport { TextMap } from \"../types\";\n\ninterface LinkDisplayProps {\n  mobile: boolean;\n  text: TextMap;\n  uri: string;\n  qrcodeModalOptions?: IQRCodeModalOptions;\n  links: IMobileRegistryEntry[];\n  errorMessage: string;\n}\n\nconst GRID_MIN_COUNT = 5;\nconst LINKS_PER_PAGE = 12;\n\nfunction LinkDisplay(props: LinkDisplayProps) {\n  const android = isAndroid();\n  const [input, setInput] = React.useState(\"\");\n  const [filter, setFilter] = React.useState(\"\");\n  const [page, setPage] = React.useState(1);\n  const links = filter\n    ? props.links.filter(link => link.name.toLowerCase().includes(filter.toLowerCase()))\n    : props.links;\n  const errorMessage = props.errorMessage;\n  const grid = filter || links.length > GRID_MIN_COUNT;\n  const pages = Math.ceil(links.length / LINKS_PER_PAGE);\n  const range = [(page - 1) * LINKS_PER_PAGE + 1, page * LINKS_PER_PAGE];\n  const pageLinks = links.length\n    ? links.filter((_, index) => index + 1 >= range[0] && index + 1 <= range[1])\n    : [];\n  const hasPaging = !!(!android && pages > 1);\n  let filterTimeout: any = undefined;\n  function handleInput(e) {\n    setInput(e.target.value);\n    clearTimeout(filterTimeout);\n    if (e.target.value) {\n      filterTimeout = setTimeout(() => {\n        setFilter(e.target.value);\n        setPage(1);\n      }, 1000);\n    } else {\n      setInput(\"\");\n      setFilter(\"\");\n      setPage(1);\n    }\n  }\n\n  return (\n    <div>\n      <p id={WALLETCONNECT_CTA_TEXT_ID} className=\"walletconnect-qrcode__text\">\n        {android ? props.text.connect_mobile_wallet : props.text.choose_preferred_wallet}\n      </p>\n      {!android && (\n        <input\n          className={`walletconnect-search__input`}\n          placeholder=\"Search\"\n          value={input}\n          onChange={handleInput}\n        />\n      )}\n      <div\n        className={`walletconnect-connect__buttons__wrapper${\n          android ? \"__android\" : grid && links.length ? \"__wrap\" : \"\"\n        }`}\n      >\n        {!android ? (\n          pageLinks.length ? (\n            pageLinks.map((entry: IMobileRegistryEntry) => {\n              const { color, name, shortName, logo } = entry;\n              const href = formatIOSMobile(props.uri, entry);\n              const handleClickIOS = React.useCallback(() => {\n                saveMobileLinkInfo({\n                  name,\n                  href,\n                });\n              }, [pageLinks]);\n              return !grid ? (\n                <WalletButton\n                  color={color}\n                  href={href}\n                  name={name}\n                  logo={logo}\n                  onClick={handleClickIOS}\n                />\n              ) : (\n                <WalletIcon\n                  color={color}\n                  href={href}\n                  name={shortName || name}\n                  logo={logo}\n                  onClick={handleClickIOS}\n                />\n              );\n            })\n          ) : (\n            <>\n              <p>\n                {errorMessage.length\n                  ? props.errorMessage\n                  : !!props.links.length && !links.length\n                  ? props.text.no_wallets_found\n                  : props.text.loading}\n              </p>\n            </>\n          )\n        ) : (\n          <ConnectButton\n            name={props.text.connect}\n            color={DEFAULT_BUTTON_COLOR}\n            href={props.uri}\n            onClick={React.useCallback(() => {\n              saveMobileLinkInfo({\n                name: \"Unknown\",\n                href: props.uri,\n              });\n            }, [])}\n          />\n        )}\n      </div>\n      {hasPaging && (\n        <div className=\"walletconnect-modal__footer\">\n          {Array(pages)\n            .fill(0)\n            .map((_, index) => {\n              const pageNumber = index + 1;\n              const selected = page === pageNumber;\n              return (\n                <a\n                  style={{ margin: \"auto 10px\", fontWeight: selected ? \"bold\" : \"normal\" }}\n                  onClick={() => setPage(pageNumber)}\n                >\n                  {pageNumber}\n                </a>\n              );\n            })}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default LinkDisplay;\n", "// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport * as React from \"react\";\n\ninterface NotificationProps {\n  message: string;\n}\n\nfunction Notification(props: NotificationProps) {\n  const show = !!props.message.trim();\n  return (\n    <div className={`walletconnect-qrcode__notification${show ? \" notification__show\" : \"\"}`}>\n      {props.message}\n    </div>\n  );\n}\n\nexport default Notification;\n", "// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport * as React from \"react\";\nimport QRCode from \"qrcode\";\nimport copy from \"copy-to-clipboard\";\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport Notification from \"./Notification\";\n\nimport { WALLETCONNECT_CTA_TEXT_ID } from \"../constants\";\nimport { TextMap } from \"../types\";\n\nasync function formatQRCodeImage(data: string) {\n  let result = \"\";\n  const dataString = await QRCode.toString(data, { margin: 0, type: \"svg\" });\n  if (typeof dataString === \"string\") {\n    result = dataString.replace(\"<svg\", `<svg class=\"walletconnect-qrcode__image\"`);\n  }\n  return result;\n}\n\ninterface QRCodeDisplayProps {\n  text: TextMap;\n  uri: string;\n}\n\nfunction QRCodeDisplay(props: QRCodeDisplayProps) {\n  const [notification, setNotification] = React.useState(\"\");\n  const [svg, setSvg] = React.useState(\"\");\n\n  React.useEffect(() => {\n    (async () => {\n      setSvg(await formatQRCodeImage(props.uri));\n    })();\n  }, []);\n\n  const copyToClipboard = () => {\n    const success = copy(props.uri);\n    if (success) {\n      setNotification(props.text.copied_to_clipboard);\n      setInterval(() => setNotification(\"\"), 1200);\n    } else {\n      setNotification(\"Error\");\n      setInterval(() => setNotification(\"\"), 1200);\n    }\n  };\n\n  return (\n    <div>\n      <p id={WALLETCONNECT_CTA_TEXT_ID} className=\"walletconnect-qrcode__text\">\n        {props.text.scan_qrcode_with_wallet}\n      </p>\n      <div dangerouslySetInnerHTML={{ __html: svg }}></div>\n      <div className=\"walletconnect-modal__footer\">\n        <a onClick={copyToClipboard}>{props.text.copy_to_clipboard}</a>\n      </div>\n      <Notification message={notification} />\n    </div>\n  );\n}\n\nexport default QRCodeDisplay;\n", "import * as React from \"react\";\nimport {\n  IMobileRegistryEntry,\n  IQRCodeModalOptions,\n  IAppRegistry,\n  IMobileLinkInfo,\n} from \"@walletconnect/legacy-types\";\nimport {\n  isMobile,\n  isAndroid,\n  formatIOSMobile,\n  saveMobileLinkInfo,\n  getMobileLinkRegistry,\n  getWalletRegistryUrl,\n  formatMobileRegistry,\n} from \"@walletconnect/legacy-utils\";\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport Header from \"./Header\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport LinkDisplay from \"./LinkDisplay\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport QRCodeDisplay from \"./QRCodeDisplay\";\n\nimport { WALLETCONNECT_MODAL_ID } from \"../constants\";\nimport { TextMap } from \"../types\";\n\ninterface ModalProps {\n  text: TextMap;\n  uri: string;\n  onClose: any;\n  qrcodeModalOptions?: IQRCodeModalOptions;\n}\n\nfunction Modal(props: ModalProps) {\n  const android = isAndroid();\n  const mobile = isMobile();\n\n  const whitelist = mobile\n    ? props.qrcodeModalOptions && props.qrcodeModalOptions.mobileLinks\n      ? props.qrcodeModalOptions.mobileLinks\n      : undefined\n    : props.qrcodeModalOptions && props.qrcodeModalOptions.desktopLinks\n    ? props.qrcodeModalOptions.desktopLinks\n    : undefined;\n  const [loading, setLoading] = React.useState(false);\n  const [fetched, setFetched] = React.useState(false);\n  const [displayQRCode, setDisplayQRCode] = React.useState(!mobile);\n  const displayProps = {\n    mobile,\n    text: props.text,\n    uri: props.uri,\n    qrcodeModalOptions: props.qrcodeModalOptions,\n  };\n\n  const [singleLinkHref, setSingleLinkHref] = React.useState(\"\");\n  const [hasSingleLink, setHasSingleLink] = React.useState(false);\n  const [links, setLinks] = React.useState<IMobileRegistryEntry[]>([]);\n  const [errorMessage, setErrorMessage] = React.useState(\"\");\n\n  const getLinksIfNeeded = () => {\n    if (fetched || loading || (whitelist && !whitelist.length) || links.length > 0) {\n      return;\n    }\n\n    React.useEffect(() => {\n      const initLinks = async () => {\n        if (android) return;\n        setLoading(true);\n        try {\n          const url =\n            props.qrcodeModalOptions && props.qrcodeModalOptions.registryUrl\n              ? props.qrcodeModalOptions.registryUrl\n              : getWalletRegistryUrl();\n          const registryResponse = await fetch(url);\n          const registry = (await registryResponse.json()).listings as IAppRegistry;\n          const platform = mobile ? \"mobile\" : \"desktop\";\n          const _links = getMobileLinkRegistry(formatMobileRegistry(registry, platform), whitelist);\n          setLoading(false);\n          setFetched(true);\n          setErrorMessage(!_links.length ? props.text.no_supported_wallets : \"\");\n          setLinks(_links);\n          const hasSingleLink = _links.length === 1;\n          if (hasSingleLink) {\n            setSingleLinkHref(formatIOSMobile(props.uri, _links[0]));\n            setDisplayQRCode(true);\n          }\n          setHasSingleLink(hasSingleLink);\n        } catch (e) {\n          setLoading(false);\n          setFetched(true);\n          setErrorMessage(props.text.something_went_wrong);\n          console.error(e); // eslint-disable-line no-console\n        }\n      };\n      initLinks();\n    });\n  };\n\n  getLinksIfNeeded();\n\n  const rightSelected = mobile ? displayQRCode : !displayQRCode;\n  return (\n    <div id={WALLETCONNECT_MODAL_ID} className=\"walletconnect-qrcode__base animated fadeIn\">\n      <div className=\"walletconnect-modal__base\">\n        <Header onClose={props.onClose} />\n        {hasSingleLink && displayQRCode ? (\n          <div className=\"walletconnect-modal__single_wallet\">\n            <a\n              onClick={() => saveMobileLinkInfo({ name: links[0].name, href: singleLinkHref })}\n              href={singleLinkHref}\n              rel=\"noopener noreferrer\"\n              target=\"_blank\"\n            >\n              {props.text.connect_with + \" \" + (hasSingleLink ? links[0].name : \"\") + \" ›\"}\n            </a>\n          </div>\n        ) : android || loading || (!loading && links.length) ? (\n          <div\n            className={`walletconnect-modal__mobile__toggle${\n              rightSelected ? \" right__selected\" : \"\"\n            }`}\n          >\n            <div className=\"walletconnect-modal__mobile__toggle_selector\" />\n            {mobile ? (\n              <>\n                <a onClick={() => (setDisplayQRCode(false), getLinksIfNeeded())}>\n                  {props.text.mobile}\n                </a>\n                <a onClick={() => setDisplayQRCode(true)}>{props.text.qrcode}</a>\n              </>\n            ) : (\n              <>\n                <a onClick={() => setDisplayQRCode(true)}>{props.text.qrcode}</a>\n                <a onClick={() => (setDisplayQRCode(false), getLinksIfNeeded())}>\n                  {props.text.desktop}\n                </a>\n              </>\n            )}\n          </div>\n        ) : null}\n\n        <div>\n          {displayQRCode || (!android && !loading && !links.length) ? (\n            <QRCodeDisplay {...displayProps} />\n          ) : (\n            <LinkDisplay {...displayProps} links={links} errorMessage={errorMessage} />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default Modal;\n", "import { TextMap } from \"../types\";\n\nconst de: TextMap = {\n  choose_preferred_wallet: \"Wähle bevorzugte Wallet\",\n  connect_mobile_wallet: \"Verbinde mit Mobile Wallet\",\n  scan_qrcode_with_wallet: \"Scanne den QR-code mit einer WalletConnect kompatiblen Wallet\",\n  connect: \"Verbinden\",\n  qrcode: \"QR-Code\",\n  mobile: \"Mobile\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"In die Zwischenablage kopieren\",\n  copied_to_clipboard: \"In die Zwischenablage kopiert!\",\n  connect_with: \"Verbinden mit Hilfe von\",\n  loading: \"Laden...\",\n  something_went_wrong: \"Etwas ist schief gelaufen\",\n  no_supported_wallets: \"Es gibt noch keine unterstützten Wallet\",\n  no_wallets_found: \"keine Wallet gefunden\",\n};\n\nexport default de;\n", "import { TextMap } from \"../types\";\n\nconst en: TextMap = {\n  choose_preferred_wallet: \"Choose your preferred wallet\",\n  connect_mobile_wallet: \"Connect to Mobile Wallet\",\n  scan_qrcode_with_wallet: \"Scan QR code with a WalletConnect-compatible wallet\",\n  connect: \"Connect\",\n  qrcode: \"QR Code\",\n  mobile: \"Mobile\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"Copy to clipboard\",\n  copied_to_clipboard: \"Copied to clipboard!\",\n  connect_with: \"Connect with\",\n  loading: \"Loading...\",\n  something_went_wrong: \"Something went wrong\",\n  no_supported_wallets: \"There are no supported wallets yet\",\n  no_wallets_found: \"No wallets found\",\n};\n\nexport default en;\n", "import { TextMap } from \"../types\";\n\nconst es: TextMap = {\n  choose_preferred_wallet: \"Elige tu billetera preferida\",\n  connect_mobile_wallet: \"Conectar a billetera móvil\",\n  scan_qrcode_with_wallet: \"Escanea el código QR con una billetera compatible con WalletConnect\",\n  connect: \"Conectar\",\n  qrcode: \"Código QR\",\n  mobile: \"Móvil\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"Copiar\",\n  copied_to_clipboard: \"Copiado!\",\n  connect_with: \"Conectar mediante\",\n  loading: \"Cargando...\",\n  something_went_wrong: \"Algo salió mal\",\n  no_supported_wallets: \"Todavía no hay billeteras compatibles\",\n  no_wallets_found: \"No se encontraron billeteras\",\n};\n\nexport default es;\n", "import { TextMap } from \"../types\";\n\nconst fr: TextMap = {\n  choose_preferred_wallet: \"Choisissez votre portefeuille préféré\",\n  connect_mobile_wallet: \"Se connecter au portefeuille mobile\",\n  scan_qrcode_with_wallet: \"Scannez le QR code avec un portefeuille compatible WalletConnect\",\n  connect: \"Se connecter\",\n  qrcode: \"QR Code\",\n  mobile: \"Mobile\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"Copier\",\n  copied_to_clipboard: \"Copié!\",\n  connect_with: \"Connectez-vous à l'aide de\",\n  loading: \"Chargement...\",\n  something_went_wrong: \"<PERSON><PERSON><PERSON> chose a mal tourné\",\n  no_supported_wallets: \"Il n'y a pas encore de portefeuilles pris en charge\",\n  no_wallets_found: \"Aucun portefeuille trouvé\",\n};\n\nexport default fr;\n", "import { TextMap } from \"../types\";\n\nconst ko: TextMap = {\n  choose_preferred_wallet: \"원하는 지갑을 선택하세요\",\n  connect_mobile_wallet: \"모바일 지갑과 연결\",\n  scan_qrcode_with_wallet: \"WalletConnect 지원 지갑에서 QR코드를 스캔하세요\",\n  connect: \"연결\",\n  qrcode: \"QR 코드\",\n  mobile: \"모바일\",\n  desktop: \"데스크탑\",\n  copy_to_clipboard: \"클립보드에 복사\",\n  copied_to_clipboard: \"클립보드에 복사되었습니다!\",\n  connect_with: \"와 연결하다\",\n  loading: \"로드 중...\",\n  something_went_wrong: \"문제가 발생했습니다.\",\n  no_supported_wallets: \"아직 지원되는 지갑이 없습니다\",\n  no_wallets_found: \"지갑을 찾을 수 없습니다\",\n};\n\nexport default ko;\n", "import { TextMap } from \"../types\";\n\nconst pt: TextMap = {\n  choose_preferred_wallet: \"Escolha sua carteira preferida\",\n  connect_mobile_wallet: \"Conectar-se à carteira móvel\",\n  scan_qrcode_with_wallet: \"Ler o código QR com uma carteira compatível com WalletConnect\",\n  connect: \"Conectar\",\n  qrcode: \"Código QR\",\n  mobile: \"Móvel\",\n  desktop: \"Desktop\",\n  copy_to_clipboard: \"Copiar\",\n  copied_to_clipboard: \"Copiado!\",\n  connect_with: \"Ligar por meio de\",\n  loading: \"Carregamento...\",\n  something_went_wrong: \"Algo correu mal\",\n  no_supported_wallets: \"Ainda não há carteiras suportadas\",\n  no_wallets_found: \"Nenhuma carteira encontrada\",\n};\n\nexport default pt;\n", "import { TextMap } from \"../types\";\n\nconst zh: TextMap = {\n  choose_preferred_wallet: \"选择你的钱包\",\n  connect_mobile_wallet: \"连接至移动端钱包\",\n  scan_qrcode_with_wallet: \"使用兼容 WalletConnect 的钱包扫描二维码\",\n  connect: \"连接\",\n  qrcode: \"二维码\",\n  mobile: \"移动\",\n  desktop: \"桌面\",\n  copy_to_clipboard: \"复制到剪贴板\",\n  copied_to_clipboard: \"复制到剪贴板成功！\",\n  connect_with: \"通过以下方式连接\",\n  loading: \"正在加载...\",\n  something_went_wrong: \"出了问题\",\n  no_supported_wallets: \"目前还没有支持的钱包\",\n  no_wallets_found: \"没有找到钱包\",\n};\n\nexport default zh;\n", "import { TextMap } from \"../types\";\n\nconst fa: TextMap = {\n  choose_preferred_wallet: \"کیف پول مورد نظر خود را انتخاب کنید\",\n  connect_mobile_wallet: \"به کیف پول موبایل وصل شوید\",\n  scan_qrcode_with_wallet: \"کد QR را با یک کیف پول سازگار با WalletConnect اسکن کنید\",\n  connect: \"اتصال\",\n  qrcode: \"کد QR\",\n  mobile: \"سیار\",\n  desktop: \"دسکتاپ\",\n  copy_to_clipboard: \"کپی به کلیپ بورد\",\n  copied_to_clipboard: \"در کلیپ بورد کپی شد!\",\n  connect_with: \"ارتباط با\",\n  loading: \"...بارگذاری\",\n  something_went_wrong: \"مشکلی پیش آمد\",\n  no_supported_wallets: \"هنوز هیچ کیف پول پشتیبانی شده ای وجود ندارد\",\n  no_wallets_found: \"هیچ کیف پولی پیدا نشد\",\n};\n\nexport default fa;\n", "import { TextMap } from \"../types\";\n\nimport de from \"./de\";\nimport en from \"./en\";\nimport es from \"./es\";\nimport fr from \"./fr\";\nimport ko from \"./ko\";\nimport pt from \"./pt\";\nimport zh from \"./zh\";\nimport fa from \"./fa\";\n\nconst languages: { [lang: string]: TextMap } = { de, en, es, fr, ko, pt, zh, fa };\n\nexport default languages;\n", "// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport * as React from \"react\";\n// @ts-ignore\nimport * as ReactDOM from \"react-dom\";\nimport { getDocumentOrThrow, getNavigatorOrThrow } from \"@walletconnect/legacy-utils\";\nimport { IQRCodeModalOptions } from \"@walletconnect/legacy-types\";\n\nimport { WALLETCONNECT_STYLE_SHEET } from \"./assets/style\";\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nimport Modal from \"./components/Modal\";\nimport Languages from \"./languages\";\nimport {\n  ANIMATION_DURATION,\n  WALLETCONNECT_WRAPPER_ID,\n  WALLETCONNECT_MODAL_ID,\n  WALLETCONNECT_STYLE_ID,\n} from \"./constants\";\nimport { TextMap } from \"./types\";\n\nfunction injectStyleSheet() {\n  const doc = getDocumentOrThrow();\n  const prev = doc.getElementById(WALLETCONNECT_STYLE_ID);\n  if (prev) {\n    doc.head.removeChild(prev);\n  }\n  const style = doc.createElement(\"style\");\n  style.setAttribute(\"id\", WALLETCONNECT_STYLE_ID);\n  style.innerText = WALLETCONNECT_STYLE_SHEET;\n  doc.head.appendChild(style);\n}\n\nfunction renderWrapper(): HTMLDivElement {\n  const doc = getDocumentOrThrow();\n  const wrapper = doc.createElement(\"div\");\n  wrapper.setAttribute(\"id\", WALLETCONNECT_WRAPPER_ID);\n  doc.body.appendChild(wrapper);\n  return wrapper;\n}\n\nfunction triggerCloseAnimation(): void {\n  const doc = getDocumentOrThrow();\n  const modal = doc.getElementById(WALLETCONNECT_MODAL_ID);\n  if (modal) {\n    modal.className = modal.className.replace(\"fadeIn\", \"fadeOut\");\n    setTimeout(() => {\n      const wrapper = doc.getElementById(WALLETCONNECT_WRAPPER_ID);\n      if (wrapper) {\n        doc.body.removeChild(wrapper);\n      }\n    }, ANIMATION_DURATION);\n  }\n}\n\nfunction getWrappedCallback(cb: any): any {\n  return () => {\n    triggerCloseAnimation();\n    if (cb) {\n      cb();\n    }\n  };\n}\n\nfunction getText(): TextMap {\n  const lang = getNavigatorOrThrow().language.split(\"-\")[0] || \"en\";\n  return Languages[lang] || Languages[\"en\"];\n}\n\nexport function open(uri: string, cb: any, qrcodeModalOptions?: IQRCodeModalOptions) {\n  injectStyleSheet();\n  const wrapper = renderWrapper();\n  ReactDOM.render(\n    <Modal\n      text={getText()}\n      uri={uri}\n      onClose={getWrappedCallback(cb)}\n      qrcodeModalOptions={qrcodeModalOptions}\n    />,\n    wrapper,\n  );\n}\n\nexport function close() {\n  triggerCloseAnimation();\n}\n", "import { IQRCodeModalOptions } from \"@walletconnect/legacy-types\";\n\nimport * as nodeLib from \"./node\";\nimport * as browserLib from \"./browser\";\n\nconst isNode = () =>\n  typeof process !== \"undefined\" &&\n  typeof process.versions !== \"undefined\" &&\n  typeof process.versions.node !== \"undefined\";\n\nfunction open(uri: string, cb: any, qrcodeModalOptions?: IQRCodeModalOptions) {\n  // eslint-disable-next-line no-console\n  console.log(uri);\n  if (isNode()) {\n    nodeLib.open(uri);\n  } else {\n    browserLib.open(uri, cb, qrcodeModalOptions);\n  }\n}\n\nfunction close() {\n  if (isNode()) {\n    nodeLib.close();\n  } else {\n    browserLib.close();\n  }\n}\n\nexport default { open, close };\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAa,yBACA,4BACA,wBAEA,wBACA,sBACA,qBACA,sBACA,kBACA,wBAGA,wBACA,mBAEA,iCACA;AAhBb;;AAAO,IAAM,0BAA0B;AAChC,IAAM,6BAA6B;AACnC,IAAM,yBAAyB;AAE/B,IAAM,yBAAyB;AAC/B,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,mBAAmB;AACzB,IAAM,yBACX;AAEK,IAAM,yBAAyB;AAC/B,IAAM,oBAAoB;AAE1B,IAAM,kCAAkC;AACxC,IAAM,iCAAiC;;;;;AChB9C,IAAa;AAAb;;AAAO,IAAM,kBAAkB;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;;;;ACVF,IAAa,gBAUA;AAVb;;AAAO,IAAM,iBAAiB;MAC5B;MACA;MACA;MACA;MACA;MACA;MACA;;AAGK,IAAM,kBAAkB;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;;;;;;ACpBL,IAAa;AAAb;;AAAO,IAAM,yBAAyB;;;;;ACAtC,IAAa;AAAb;;AAAO,IAAM,kBAAkB;MAC7B,GAAG;MACH,GAAG;MACH,GAAG;MACH,GAAG;MACH,IAAI;;;;;;ACLN;;;AACA;AACA;AACA;AACA;;;;;ACJA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,eAAA;;;;;;ACAA,IAAAC,gBAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,YAAA;;;AACA;AACA;AACA,IAAAC;AACA,IAAAC;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACVA;AAAA;AAAA,WAAO,UAAeC;AACtB,IAAAA,cAAa,SAAS;AACtB,IAAAA,cAAa,QAAS;AAEtB,QAAI,WAAW,OAAO,UAAU;AAChC,QAAI,QAAQ;AAAA,MACR,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,8BAA8B;AAAA,MAC9B,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,yBAAyB;AAAA,MACzB,yBAAyB;AAAA,IAC7B;AAEA,aAASA,cAAa,KAAK;AACzB,aACK,mBAAmB,GAAG,KACtB,kBAAkB,GAAG;AAAA,IAE5B;AAEA,aAAS,mBAAmB,KAAK;AAC/B,aACK,eAAe,aACf,eAAe,cACf,eAAe,cACf,eAAe,cACf,eAAe,qBACf,eAAe,eACf,eAAe,eACf,eAAe,gBACf,eAAe;AAAA,IAEtB;AAEA,aAAS,kBAAkB,KAAK;AAC9B,aAAO,MAAM,SAAS,KAAK,GAAG,CAAC;AAAA,IACjC;AAAA;AAAA;;;ACxCA;AAAA;AASA,QAAIC,gBAAe,wBAAyB;AAE5C,WAAO,UAAU,SAAS,mBAAoB,KAAK;AACjD,UAAIA,cAAa,GAAG,GAAG;AAErB,YAAI,MAAM,OAAO,KAAK,IAAI,MAAM;AAChC,YAAI,IAAI,eAAe,IAAI,OAAO,YAAY;AAE5C,gBAAM,IAAI,MAAM,IAAI,YAAY,IAAI,aAAa,IAAI,UAAU;AAAA,QACjE;AACA,eAAO;AAAA,MACT,OAAO;AAEL,eAAO,OAAO,KAAK,GAAG;AAAA,MACxB;AAAA,IACF;AAAA;AAAA;;;ACNM,SAAU,cAAc,KAAW;AACvC,SAAO,IAAI,WAAW,GAAG;AAC3B;AAEM,SAAU,YAAY,KAAa,WAAW,OAAK;AACvD,QAAM,MAAM,IAAI,SAAS,OAAO;AAChC,SAAO,WAAW,aAAa,GAAG,IAAI;AACxC;AAEM,SAAU,aAAa,KAAW;AACtC,SAAO,IAAI,SAAS,QAAQ;AAC9B;AAEM,SAAU,eAAe,KAAW;AACxC,SAAO,IAAI,WAAW,GAAG,IAAI,MAAM;AACrC;AAQM,SAAU,cAAc,KAAe;AAC3C,aAAO,4BAAAC,SAAmB,GAAG;AAC/B;AAEM,SAAU,WAAW,KAAiB,WAAW,OAAK;AAC1D,SAAO,YAAY,cAAc,GAAG,GAAG,QAAQ;AACjD;AAEM,SAAU,YAAY,KAAe;AACzC,SAAO,aAAa,cAAc,GAAG,CAAC;AACxC;AAEM,SAAU,cAAc,KAAe;AAC3C,SAAO,eAAe,cAAc,GAAG,CAAC;AAC1C;AAUM,SAAU,YAAY,KAAW;AACrC,SAAO,OAAO,KAAK,gBAAgB,GAAG,GAAG,OAAO;AAClD;AAEM,SAAU,WAAW,KAAW;AACpC,SAAO,cAAc,YAAY,GAAG,CAAC;AACvC;AAEM,SAAU,UAAU,KAAW;AACnC,SAAO,aAAa,YAAY,GAAG,CAAC;AACtC;AAEM,SAAU,YAAY,KAAW;AACrC,SAAO,cAAc,WAAW,GAAG,CAAC;AACtC;AAQM,SAAU,aAAa,MAAY;AACvC,SAAO,OAAO,KAAK,MAAM,QAAQ;AACnC;AAEM,SAAU,YAAY,MAAY;AACtC,SAAO,cAAc,aAAa,IAAI,CAAC;AACzC;AAEM,SAAU,UAAU,MAAc,WAAW,OAAK;AACtD,SAAO,YAAY,aAAa,IAAI,GAAG,QAAQ;AACjD;AAEM,SAAU,aAAa,MAAY;AACvC,QAAM,MAAM,SAAS,MAAM,EAAE;AAC7B,SAAO,UAAU,GAAG,GAAG,4CAA4C;AACnE,SAAO;AACT;AAQM,SAAU,eAAe,KAAW;AACxC,SAAO,eAAe,eAAe,GAAG,CAAC;AAC3C;AAEM,SAAU,cAAc,KAAW;AACvC,SAAO,cAAc,eAAe,GAAG,CAAC;AAC1C;AAEM,SAAU,YAAY,KAAa,UAAkB;AACzD,SAAO,YAAY,eAAe,GAAG,GAAG,QAAQ;AAClD;AAEM,SAAU,aAAa,KAAW;AACtC,SAAO,GAAG,GAAG;AACf;AAEM,SAAU,eAAe,KAAW;AACxC,QAAM,OAAO,QAAQ,GAAG,SAAS,CAAC;AAClC,SAAO,cAAc,GAAG;AAC1B;AAIM,SAAU,eAAe,KAAW;AACxC,SAAO,cAAc,cAAc,GAAG,CAAC;AACzC;AAEM,SAAU,cAAc,KAAW;AACvC,SAAO,IAAI,WAAW,WAAW,GAAG,EAAE,IAAI,CAAAC,OAAK,SAASA,IAAG,CAAC,CAAC,CAAC;AAChE;AAEM,SAAU,YAAY,KAAsB,UAAkB;AAClE,SAAO,WAAW,cAAc,GAAG,GAAG,QAAQ;AAChD;AAYM,SAAU,eAAe,KAAQ;AACrC,MAAI,OAAO,QAAQ,YAAY,CAAC,IAAI,OAAO,SAAS,EAAE,KAAK,GAAG,GAAG;AAC/D,WAAO;;AAET,MAAI,IAAI,SAAS,MAAM,GAAG;AACxB,WAAO;;AAET,SAAO;AACT;AAEM,SAAU,YAAY,KAAU,QAAe;AACnD,MAAI,OAAO,QAAQ,YAAY,CAAC,IAAI,MAAM,kBAAkB,GAAG;AAC7D,WAAO;;AAET,MAAI,UAAU,IAAI,WAAW,IAAI,IAAI,QAAQ;AAC3C,WAAO;;AAET,SAAO;AACT;AAEM,SAAU,SAAS,KAAQ;AAC/B,SAAO,OAAO,SAAS,GAAG;AAC5B;AAEM,SAAU,aAAa,KAAQ;AACnC,SAAO,qBAAAC,QAAc,OAAO,GAAG,KAAK,CAAC,SAAS,GAAG;AACnD;AAEM,SAAU,cAAc,KAAQ;AACpC,SACE,CAAC,aAAa,GAAG,KACjB,CAAC,SAAS,GAAG,KACb,OAAO,IAAI,eAAe;AAE9B;AAEM,SAAU,QAAQ,KAAQ;AAC9B,MAAI,SAAS,GAAG,GAAG;AACjB,WAAO;aACE,aAAa,GAAG,GAAG;AAC5B,WAAO;aACE,cAAc,GAAG,GAAG;AAC7B,WAAO;aACE,MAAM,QAAQ,GAAG,GAAG;AAC7B,WAAO;SACF;AACL,WAAO,OAAO;;AAElB;AAEM,SAAU,YAAY,KAAW;AACrC,MAAI,eAAe,GAAG,GAAG;AACvB,WAAO;;AAET,MAAI,YAAY,GAAG,GAAG;AACpB,WAAO;;AAET,SAAO;AACT;AAIM,SAAU,iBAAiB,MAAc;AAC7C,QAAM,SAAS,OAAO,OAAO,IAAI;AACjC,SAAO;AACT;AAEM,SAAU,gBAAgB,MAAkB;AAChD,MAAI,SAAmB,CAAA;AACvB,OAAK,QAAQ,SAAQ,SAAS,OAAO,OAAO,MAAM,KAAK,GAAG,CAAC,CAAE;AAC7D,SAAO,IAAI,WAAW,CAAC,GAAG,MAAM,CAAC;AACnC;AAcM,SAAU,eAAe,QAAgB,WAAW,GAAC;AACzD,QAAM,YAAY,SAAS;AAC3B,SAAO,aACD,SAAS,aAAa,WAAY,WAAW,WAC/C;AACN;AAEM,SAAU,WAAW,KAAa,WAAW,GAAC;AAClD,QAAM,QAAQ,cAAc,GAAG,EAAE,MAAM,IAAI,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC;AACzE,SAAO,MAAM,KAAK,SAAS,CAAA,CAAE;AAC/B;AAYM,SAAU,cACd,KACA,WAAW,GACX,UAAU,aAAW;AAErB,SAAO,QAAQ,KAAK,eAAe,IAAI,QAAQ,QAAQ,GAAG,OAAO;AACnE;AAEM,SAAU,QACd,KACA,QACA,UAAU,aAAW;AAErB,SAAO,UAAU,KAAK,QAAQ,MAAM,OAAO;AAC7C;AAUM,SAAU,gBAAgB,KAAW;AACzC,SAAO,IAAI,QAAQ,OAAO,EAAE;AAC9B;AAEM,SAAU,aAAa,KAAW;AACtC,SAAO,IAAI,WAAW,IAAI,IAAI,MAAM,KAAK,GAAG;AAC9C;AAEM,SAAU,YAAY,KAAW;AACrC,QAAM,gBAAgB,GAAG;AACzB,QAAM,cAAc,KAAK,CAAC;AAC1B,MAAI,KAAK;AACP,UAAM,aAAa,GAAG;;AAExB,SAAO;AACT;AAEM,SAAU,sBAAsB,KAAW;AAC/C,QAAM,WAAW,IAAI,WAAW,IAAI;AACpC,QAAM,gBAAgB,GAAG;AACzB,QAAM,IAAI,WAAW,WAAW,IAAI,IAAI,UAAU,CAAC,IAAI;AACvD,SAAO,WAAW,aAAa,GAAG,IAAI;AACxC;AAIA,SAAS,YAAY,OAAU;AAC7B,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,UAAU,OAAU;AAC3B,SAAO,CAAC,YAAY,KAAK;AAC3B;AAEA,SAAS,OAAO,WAAoB,cAAoB;AACtD,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,YAAY;;AAEhC;AASA,SAAS,UACP,KACA,QACA,MACA,UAAU,aAAW;AAErB,QAAM,OAAO,SAAS,IAAI;AAC1B,MAAI,SAAS;AACb,MAAI,OAAO,GAAG;AACZ,UAAM,MAAM,QAAQ,OAAO,IAAI;AAC/B,aAAS,OAAO,MAAM,MAAM,MAAM;;AAEpC,SAAO;AACT;AA3VA,0BACA,6BAIM,SACA,UACA,SAEA,aACA,YACA,kBACA,mBAEA;AAdN,IAAAC,YAAA;;2BAA0B;AAC1B,kCAA+B;AAI/B,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,UAAU;AAEhB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAE1B,IAAM,cAAc;;;;;ACVd,SAAU,2BAA2B,QAAmB;AAC5D,SAAgB,cAAc,IAAI,WAAW,MAAM,CAAC;AACtD;AAEM,SAAU,yBAAyB,QAAmB;AAC1D,SAAgB,YAAY,IAAI,WAAW,MAAM,CAAC;AACpD;AAEM,SAAU,wBAAwB,QAAqB,UAAkB;AAC7E,SAAgB,WAAW,IAAI,WAAW,MAAM,GAAG,CAAC,QAAQ;AAC9D;AAEM,SAAU,2BAA2B,QAAmB;AAC5D,SAAgB,cAAc,IAAI,WAAW,MAAM,CAAC;AACtD;AAEM,SAAU,sBAAsB,MAAmB;AACvD,SAAgB,WAAW,KAAK,IAAI,CAAAC,OAAc,WAAW,IAAI,WAAWA,EAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;AAC7F;AAIM,SAAU,2BAA2B,KAAW;AACpD,SAAgB,cAAc,GAAG,EAAE;AACrC;AAEM,SAAU,oBAAoB,KAAW;AAC7C,SAAgB,aAAa,GAAG;AAClC;AAEM,SAAU,mBAAmB,KAAa,UAAkB;AAChE,SAAgB,YAAY,KAAK,CAAC,QAAQ;AAC5C;AAEM,SAAU,sBAAsB,KAAW;AAC/C,SAAgB,eAAe,GAAG;AACpC;AAEM,SAAUC,kBAAiB,MAAc;AAC7C,SAAgB,cAAc,GAAG,IAAI;AACvC;AAIM,SAAU,yBAAyB,MAAY;AACnD,SAAgB,YAAY,IAAI,EAAE;AACpC;AAEM,SAAU,oBAAoB,MAAY;AAC9C,SAAgB,aAAa,IAAI;AACnC;AAEM,SAAU,iBAAiB,MAAc,UAAkB;AAC/D,SAAgB,UAAU,MAAM,CAAC,QAAQ;AAC3C;AAEM,SAAU,oBAAoB,MAAY;AAC9C,SAAgB,aAAa,IAAI;AACnC;AAIM,SAAU,mBAAmB,KAAW;AAC5C,SAAgB,YAAY,GAAG;AACjC;AAEM,SAAU,wBAAwB,KAAW;AACjD,SAAgB,WAAW,GAAG,EAAE;AAClC;AAEM,SAAU,iBAAiB,KAAW;AAC1C,SAAgB,UAAU,GAAG;AAC/B;AAEM,SAAU,mBAAmB,KAAW;AAC5C,SAAgB,YAAY,GAAG;AACjC;AAIM,SAAU,sBAAsB,KAAW;AAC/C,SAAgB,eAAe,GAAG;AACpC;AAEM,SAAU,2BAA2B,KAAW;AACpD,SAAgB,cAAc,GAAG,EAAE;AACrC;AAEM,SAAU,oBAAoB,KAAW;AAC7C,SAAgB,aAAa,GAAG;AAClC;AAEM,SAAU,mBAAmB,KAAsB,UAAkB;AACzE,SAAgB,YAAY,OAAO,GAAG,GAAG,CAAC,QAAQ;AACpD;AAlGA;;IAAAC;;;;;ACAA,mBAEaC,gBAEAC,uBAEAC,qBAEAC,cAEAC,sBAEAC,eAEAC,qBAEAC,cAEAC,mBAEAC,YAEAC,yBAEAC;AAxBb;;oBAA+B;AAExB,IAAMX,iBAA8B;AAEpC,IAAMC,wBAAqC;AAE3C,IAAMC,sBAAmC;AAEzC,IAAMC,eAA4B;AAElC,IAAMC,uBAAoC;AAE1C,IAAMC,gBAA6B;AAEnC,IAAMC,sBAAmC;AAEzC,IAAMC,eAA4B;AAElC,IAAMC,oBAAiC;AAEvC,IAAMC,aAA0B;AAEhC,IAAMC,0BAAuC;AAE7C,IAAMC,mBAAgC;;;;;ACbvC,SAAU,UACd,WAAkB;AAElB,SAAO,OAAO,SAAS;AACzB;AAEM,SAAU,WAAQ;AACtB,QAAMC,OAAM,UAAS;AACrB,SAAOA,QAAOA,KAAI,KAAKA,KAAI,KAAK;AAClC;AAEM,SAAU,YAAS;AACvB,QAAM,KAAK,SAAQ;AACnB,SAAO,KAAK,GAAG,YAAW,EAAG,SAAS,SAAS,IAAI;AACrD;AAEM,SAAU,QAAK;AACnB,QAAM,KAAK,SAAQ;AACnB,SAAO,KACH,GAAG,YAAW,EAAG,SAAS,KAAK,KAC5B,GAAG,YAAW,EAAG,SAAS,KAAK,KAAK,UAAU,iBAAiB,IAClE;AACN;AAEM,SAAU,WAAQ;AACtB,QAAM,KAAK,SAAQ;AACnB,SAAO,KAAK,UAAS,KAAM,MAAK,IAAK;AACvC;AAEM,SAAU,SAAM;AACpB,QAAMA,OAAM,UAAS;AACrB,QAAM,SAASA,QAAOA,KAAI,OAAOA,KAAI,KAAK,YAAW,MAAO,SAAS;AACrE,SAAO;AACT;AAEM,SAAU,YAAS;AACvB,QAAM,SAAS,CAAC,OAAM,KAAM,CAAC,CAACC,cAAY;AAC1C,SAAO;AACT;AAjDA;;;AASA;;;;;ACTA,IAEaC,gBAEAC;AAJb;;;AAEO,IAAMD,iBAAyB;AAE/B,IAAMC,qBAA6B;;;;;ACDpC,SAAU,SAAS,KAAa,MAAS;AAC7C,QAAM,MAAMC,mBAAkB,IAAI;AAClC,QAAM,QAAQC,iBAAe;AAC7B,MAAI,OAAO;AACT,UAAM,QAAQ,KAAK,GAAG;;AAE1B;AAEM,SAAU,SAAS,KAAW;AAClC,MAAI,OAAY;AAChB,MAAI,MAAqB;AACzB,QAAM,QAAQA,iBAAe;AAC7B,MAAI,OAAO;AACT,UAAM,MAAM,QAAQ,GAAG;;AAEzB,SAAO,MAAMC,eAAc,GAAG,IAAI;AAClC,SAAO;AACT;AAEM,SAAU,YAAY,KAAW;AACrC,QAAM,QAAQD,iBAAe;AAC7B,MAAI,OAAO;AACT,UAAM,WAAW,GAAG;;AAExB;AA3BA;;;AACA;;;;;ACGM,SAAU,gBAAa;AAC3B,SAAsB,iCAAiB;AACzC;AANA;;;qBAAgC;;;;;ACO1B,SAAUE,aAAY,KAAW;AACrC,SAAgB,YAAY,GAAG;AACjC;AAEM,SAAUC,cAAa,KAAW;AACtC,SAAgB,aAAa,GAAG;AAClC;AAEM,SAAUC,iBAAgB,KAAW;AACzC,SAAgB,gBAAgB,GAAG;AACrC;AAEM,SAAUC,uBAAsB,KAAW;AAC/C,SAAgB,sBAA+B,aAAa,GAAG,CAAC;AAClE;AAMM,SAAU,OAAI;AAClB,QAAM,UAAkB,CAAC,GAASC,OAAW;AAC3C,SACEA,KAAI,IAAI,IACR,MAAM,IACNA,MAAM,IAAI,KAAM,MAAM,IAAI,KAAK,IAAK,KAAK,OAAM,KAAM,IAAI,KAAK,KAAK,KAAM,GAAG,SAAS,EAAE,IAAI,KAC3F;;AAGF,WAAOA;EACT,GAAE;AACF,SAAO;AACT;AAIM,SAAU,wBAAqB;AAEnC,UAAQ,KACN,sLAAsL;AAE1L;AAIM,SAAU,gBAAgB,SAAiB,UAAiB;AAChE,MAAI;AACJ,QAAM,UAAU,gBAAgB,OAAO;AACvC,MAAI,SAAS;AACX,aAAS,WAAW,OAAO,iBAAiB,QAAQ;;AAEtD,SAAO;AACT;AAEM,SAAU,UAAU,SAAiB,KAAe;AACxD,MAAI;AACJ,QAAM,YAAY,gBAAgB,SAAS,IAAI,QAAQ;AACvD,MAAI,IAAI,UAAU,IAAI,OAAO,OAAO,GAAG;AACrC,aAAS,IAAI,OAAO,OAAO;aAClB,WAAW;AACpB,aAAS;;AAEX,SAAO;AACT;AAtEA,IAyBaC;AAzBb;;IAAAC;AACA,IAAAA;AAEA,IAAAA;AAsBO,IAAMD,aAAyB;;;;;AChBhC,SAAU,gBAAgB,KAAa,OAA2B;AACtE,QAAM,aAAqB,mBAAmB,GAAG;AACjD,SAAO,MAAM,gBACT,GAAG,MAAM,aAAa,WAAW,UAAU,KAC3C,MAAM,WACN,GAAG,MAAM,QAAQ,GAAG,MAAM,SAAS,SAAS,GAAG,IAAI,OAAO,GAAG,UAAU,UAAU,KACjF;AACN;AAEM,SAAU,mBAAmB,MAAqB;AACtD,QAAM,WAAW,KAAK,KAAK,MAAM,GAAG,EAAE,CAAC;AACvC,WAAS,wBAAsB,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,IAAI,GAAA,EAAE,MAAM,SAAQ,CAAA,CAAA;AAC5D;AAEM,SAAU,uBACd,UACA,MAAY;AAEZ,SAAO,SAAS,OAAO,CAAC,UACtB,MAAM,KAAK,YAAW,EAAG,SAAS,KAAK,YAAW,CAAE,CAAC,EACrD,CAAC;AACL;AAEM,SAAU,sBAAsB,UAA2B,WAAoB;AACnF,MAAI,QAAQ;AACZ,MAAI,WAAW;AACb,YAAQ,UAAU,IAAI,CAAC,SAAiB,uBAAuB,UAAU,IAAI,CAAC,EAAE,OAAO,OAAO;;AAEhG,SAAO;AACT;AAtCA,IAAAE,eAAA;;IAAAC;AAOA;;;;;ACDM,SAAU,UACd,YACA,SAAa;AAEb,QAAM,sBAAsB,UACvB,aACyD;AAC5D,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,YAAM,WAAW,CACf,KACA,SACE;AACF,YAAI,QAAQ,QAAQ,OAAO,QAAQ,aAAa;AAC9C,iBAAO,GAAG;;AAEZ,gBAAQ,IAAI;MACd;AACA,iBAAW,MAAM,SAAS,CAAC,GAAG,UAAU,QAAQ,CAAC;IACnD,CAAC;EACH;AACA,SAAO;AACT;AAEM,SAAU,eACd,OAAoC;AAEpC,QAAM,UAAU,MAAM,WAAW;AACjC,MAAI,OAAO;AACX,MAAI,SAAS,CAAC,MAAM,MAAM;AACxB,YAAQ,SAAS;MACf,KAAK;AACH,eAAO;AACP;MACF,KAAK;AACH,eAAO;AACP;MACF,KAAK;AACH,eAAO;AACP;MACF,KAAK;AACH,eAAO;AACP;MACF,KAAK;AACH,eAAO;AACP;MACF;AACE,eAAO;AACP;;;AAGN,QAAM,SAA2D;IAC/D;IACA;;AAEF,MAAI,MAAM,MAAM;AACd,WAAO,OAAO,MAAM;;AAEtB,SAAO;AACT;AA1DA;;;;;;ACFM,SAAU,uBAAoB;AAClC,SAAO,UAAU;AACnB;AAEM,SAAU,qBAAkB;AAChC,SAAO,UAAU;AACnB;AAEM,SAAU,0BACd,OACA,WAAiC,UAAQ;;AAEzC,SAAO;IACL,MAAM,MAAM,QAAQ;IACpB,WAAW,MAAM,SAAS,aAAa;IACvC,OAAO,MAAM,SAAS,OAAO,WAAW;IACxC,OAAI,KAAE,MAAM,UAAU,QAAE,QAAA,OAAA,SAAA,KAAI;IAC5B,eAAe,MAAM,QAAQ,EAAE,aAAa;IAC5C,UAAU,MAAM,QAAQ,EAAE,UAAU;;AAExC;AAEM,SAAU,qBACd,UACA,WAAiC,UAAQ;AAEzC,SAAO,OAAO,OAAY,QAAQ,EAC/B,OAAO,WAAS,CAAC,CAAC,MAAM,QAAQ,EAAE,aAAa,CAAC,CAAC,MAAM,QAAQ,EAAE,MAAM,EACvE,IAAI,WAAS,0BAA0B,OAAO,QAAQ,CAAC;AAC5D;AA/BA,IAAM;AAAN,IAAAC,iBAAA;;IAAM,UAAU;;;;;ACFhB;AAAA;AAAA;AACA,QAAM,kBAAkB;AACxB,QAAM,kBAAkB;AACxB,QAAM,eAAe;AACrB,QAAM,eAAe;AAErB,QAAM,oBAAoB,WAAS,UAAU,QAAQ,UAAU;AAE/D,aAAS,sBAAsB,SAAS;AACvC,cAAQ,QAAQ,aAAa;AAAA,QAC5B,KAAK;AACJ,iBAAO,SAAO,CAAC,QAAQ,UAAU;AAChC,kBAAM,QAAQ,OAAO;AAErB,gBACC,UAAU,UACT,QAAQ,YAAY,UAAU,QAC9B,QAAQ,mBAAmB,UAAU,IACrC;AACD,qBAAO;AAAA,YACR;AAEA,gBAAI,UAAU,MAAM;AACnB,qBAAO,CAAC,GAAG,QAAQ,CAAC,OAAO,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,EAAE,KAAK,EAAE,CAAC;AAAA,YACpE;AAEA,mBAAO;AAAA,cACN,GAAG;AAAA,cACH,CAAC,OAAO,KAAK,OAAO,GAAG,KAAK,OAAO,OAAO,OAAO,GAAG,MAAM,OAAO,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE;AAAA,YAC1F;AAAA,UACD;AAAA,QAED,KAAK;AACJ,iBAAO,SAAO,CAAC,QAAQ,UAAU;AAChC,gBACC,UAAU,UACT,QAAQ,YAAY,UAAU,QAC9B,QAAQ,mBAAmB,UAAU,IACrC;AACD,qBAAO;AAAA,YACR;AAEA,gBAAI,UAAU,MAAM;AACnB,qBAAO,CAAC,GAAG,QAAQ,CAAC,OAAO,KAAK,OAAO,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC;AAAA,YACzD;AAEA,mBAAO,CAAC,GAAG,QAAQ,CAAC,OAAO,KAAK,OAAO,GAAG,OAAO,OAAO,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,UAClF;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO,SAAO,CAAC,QAAQ,UAAU;AAChC,gBAAI,UAAU,QAAQ,UAAU,UAAa,MAAM,WAAW,GAAG;AAChE,qBAAO;AAAA,YACR;AAEA,gBAAI,OAAO,WAAW,GAAG;AACxB,qBAAO,CAAC,CAAC,OAAO,KAAK,OAAO,GAAG,KAAK,OAAO,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,YACrE;AAEA,mBAAO,CAAC,CAAC,QAAQ,OAAO,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,oBAAoB,CAAC;AAAA,UAC5E;AAAA,QAED;AACC,iBAAO,SAAO,CAAC,QAAQ,UAAU;AAChC,gBACC,UAAU,UACT,QAAQ,YAAY,UAAU,QAC9B,QAAQ,mBAAmB,UAAU,IACrC;AACD,qBAAO;AAAA,YACR;AAEA,gBAAI,UAAU,MAAM;AACnB,qBAAO,CAAC,GAAG,QAAQ,OAAO,KAAK,OAAO,CAAC;AAAA,YACxC;AAEA,mBAAO,CAAC,GAAG,QAAQ,CAAC,OAAO,KAAK,OAAO,GAAG,KAAK,OAAO,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,UAChF;AAAA,MACF;AAAA,IACD;AAEA,aAAS,qBAAqB,SAAS;AACtC,UAAI;AAEJ,cAAQ,QAAQ,aAAa;AAAA,QAC5B,KAAK;AACJ,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,qBAAS,aAAa,KAAK,GAAG;AAE9B,kBAAM,IAAI,QAAQ,YAAY,EAAE;AAEhC,gBAAI,CAAC,QAAQ;AACZ,0BAAY,GAAG,IAAI;AACnB;AAAA,YACD;AAEA,gBAAI,YAAY,GAAG,MAAM,QAAW;AACnC,0BAAY,GAAG,IAAI,CAAC;AAAA,YACrB;AAEA,wBAAY,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI;AAAA,UAC/B;AAAA,QAED,KAAK;AACJ,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,qBAAS,UAAU,KAAK,GAAG;AAC3B,kBAAM,IAAI,QAAQ,SAAS,EAAE;AAE7B,gBAAI,CAAC,QAAQ;AACZ,0BAAY,GAAG,IAAI;AACnB;AAAA,YACD;AAEA,gBAAI,YAAY,GAAG,MAAM,QAAW;AACnC,0BAAY,GAAG,IAAI,CAAC,KAAK;AACzB;AAAA,YACD;AAEA,wBAAY,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,GAAG,GAAG,KAAK;AAAA,UACrD;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,kBAAM,UAAU,OAAO,UAAU,YAAY,MAAM,SAAS,QAAQ,oBAAoB;AACxF,kBAAM,iBAAkB,OAAO,UAAU,YAAY,CAAC,WAAW,OAAO,OAAO,OAAO,EAAE,SAAS,QAAQ,oBAAoB;AAC7H,oBAAQ,iBAAiB,OAAO,OAAO,OAAO,IAAI;AAClD,kBAAM,WAAW,WAAW,iBAAiB,MAAM,MAAM,QAAQ,oBAAoB,EAAE,IAAI,UAAQ,OAAO,MAAM,OAAO,CAAC,IAAI,UAAU,OAAO,QAAQ,OAAO,OAAO,OAAO;AAC1K,wBAAY,GAAG,IAAI;AAAA,UACpB;AAAA,QAED;AACC,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,gBAAI,YAAY,GAAG,MAAM,QAAW;AACnC,0BAAY,GAAG,IAAI;AACnB;AAAA,YACD;AAEA,wBAAY,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,GAAG,GAAG,KAAK;AAAA,UACrD;AAAA,MACF;AAAA,IACD;AAEA,aAAS,6BAA6B,OAAO;AAC5C,UAAI,OAAO,UAAU,YAAY,MAAM,WAAW,GAAG;AACpD,cAAM,IAAI,UAAU,sDAAsD;AAAA,MAC3E;AAAA,IACD;AAEA,aAAS,OAAO,OAAO,SAAS;AAC/B,UAAI,QAAQ,QAAQ;AACnB,eAAO,QAAQ,SAAS,gBAAgB,KAAK,IAAI,mBAAmB,KAAK;AAAA,MAC1E;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,OAAO,OAAO,SAAS;AAC/B,UAAI,QAAQ,QAAQ;AACnB,eAAO,gBAAgB,KAAK;AAAA,MAC7B;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,WAAW,OAAO;AAC1B,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,eAAO,MAAM,KAAK;AAAA,MACnB;AAEA,UAAI,OAAO,UAAU,UAAU;AAC9B,eAAO,WAAW,OAAO,KAAK,KAAK,CAAC,EAClC,KAAK,CAAC,GAAGC,OAAM,OAAO,CAAC,IAAI,OAAOA,EAAC,CAAC,EACpC,IAAI,SAAO,MAAM,GAAG,CAAC;AAAA,MACxB;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,WAAW,OAAO;AAC1B,YAAM,YAAY,MAAM,QAAQ,GAAG;AACnC,UAAI,cAAc,IAAI;AACrB,gBAAQ,MAAM,MAAM,GAAG,SAAS;AAAA,MACjC;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,QAAQ,KAAK;AACrB,UAAI,OAAO;AACX,YAAM,YAAY,IAAI,QAAQ,GAAG;AACjC,UAAI,cAAc,IAAI;AACrB,eAAO,IAAI,MAAM,SAAS;AAAA,MAC3B;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,QAAQ,OAAO;AACvB,cAAQ,WAAW,KAAK;AACxB,YAAM,aAAa,MAAM,QAAQ,GAAG;AACpC,UAAI,eAAe,IAAI;AACtB,eAAO;AAAA,MACR;AAEA,aAAO,MAAM,MAAM,aAAa,CAAC;AAAA,IAClC;AAEA,aAAS,WAAW,OAAO,SAAS;AACnC,UAAI,QAAQ,gBAAgB,CAAC,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,OAAO,UAAU,YAAY,MAAM,KAAK,MAAM,KAAK;AAC/G,gBAAQ,OAAO,KAAK;AAAA,MACrB,WAAW,QAAQ,iBAAiB,UAAU,SAAS,MAAM,YAAY,MAAM,UAAU,MAAM,YAAY,MAAM,UAAU;AAC1H,gBAAQ,MAAM,YAAY,MAAM;AAAA,MACjC;AAEA,aAAO;AAAA,IACR;AAEA,aAASC,OAAM,OAAO,SAAS;AAC9B,gBAAU,OAAO,OAAO;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,aAAa;AAAA,QACb,sBAAsB;AAAA,QACtB,cAAc;AAAA,QACd,eAAe;AAAA,MAChB,GAAG,OAAO;AAEV,mCAA6B,QAAQ,oBAAoB;AAEzD,YAAM,YAAY,qBAAqB,OAAO;AAG9C,YAAM,MAAM,uBAAO,OAAO,IAAI;AAE9B,UAAI,OAAO,UAAU,UAAU;AAC9B,eAAO;AAAA,MACR;AAEA,cAAQ,MAAM,KAAK,EAAE,QAAQ,UAAU,EAAE;AAEzC,UAAI,CAAC,OAAO;AACX,eAAO;AAAA,MACR;AAEA,iBAAW,SAAS,MAAM,MAAM,GAAG,GAAG;AACrC,YAAI,UAAU,IAAI;AACjB;AAAA,QACD;AAEA,YAAI,CAAC,KAAK,KAAK,IAAI,aAAa,QAAQ,SAAS,MAAM,QAAQ,OAAO,GAAG,IAAI,OAAO,GAAG;AAIvF,gBAAQ,UAAU,SAAY,OAAO,CAAC,SAAS,WAAW,EAAE,SAAS,QAAQ,WAAW,IAAI,QAAQ,OAAO,OAAO,OAAO;AACzH,kBAAU,OAAO,KAAK,OAAO,GAAG,OAAO,GAAG;AAAA,MAC3C;AAEA,iBAAW,OAAO,OAAO,KAAK,GAAG,GAAG;AACnC,cAAM,QAAQ,IAAI,GAAG;AACrB,YAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,qBAAWC,MAAK,OAAO,KAAK,KAAK,GAAG;AACnC,kBAAMA,EAAC,IAAI,WAAW,MAAMA,EAAC,GAAG,OAAO;AAAA,UACxC;AAAA,QACD,OAAO;AACN,cAAI,GAAG,IAAI,WAAW,OAAO,OAAO;AAAA,QACrC;AAAA,MACD;AAEA,UAAI,QAAQ,SAAS,OAAO;AAC3B,eAAO;AAAA,MACR;AAEA,cAAQ,QAAQ,SAAS,OAAO,OAAO,KAAK,GAAG,EAAE,KAAK,IAAI,OAAO,KAAK,GAAG,EAAE,KAAK,QAAQ,IAAI,GAAG,OAAO,CAAC,QAAQ,QAAQ;AACtH,cAAM,QAAQ,IAAI,GAAG;AACrB,YAAI,QAAQ,KAAK,KAAK,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AAEzE,iBAAO,GAAG,IAAI,WAAW,KAAK;AAAA,QAC/B,OAAO;AACN,iBAAO,GAAG,IAAI;AAAA,QACf;AAEA,eAAO;AAAA,MACR,GAAG,uBAAO,OAAO,IAAI,CAAC;AAAA,IACvB;AAEA,YAAQ,UAAU;AAClB,YAAQ,QAAQD;AAEhB,YAAQ,YAAY,CAAC,QAAQ,YAAY;AACxC,UAAI,CAAC,QAAQ;AACZ,eAAO;AAAA,MACR;AAEA,gBAAU,OAAO,OAAO;AAAA,QACvB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,sBAAsB;AAAA,MACvB,GAAG,OAAO;AAEV,mCAA6B,QAAQ,oBAAoB;AAEzD,YAAM,eAAe,SACnB,QAAQ,YAAY,kBAAkB,OAAO,GAAG,CAAC,KACjD,QAAQ,mBAAmB,OAAO,GAAG,MAAM;AAG7C,YAAM,YAAY,sBAAsB,OAAO;AAE/C,YAAM,aAAa,CAAC;AAEpB,iBAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACtC,YAAI,CAAC,aAAa,GAAG,GAAG;AACvB,qBAAW,GAAG,IAAI,OAAO,GAAG;AAAA,QAC7B;AAAA,MACD;AAEA,YAAM,OAAO,OAAO,KAAK,UAAU;AAEnC,UAAI,QAAQ,SAAS,OAAO;AAC3B,aAAK,KAAK,QAAQ,IAAI;AAAA,MACvB;AAEA,aAAO,KAAK,IAAI,SAAO;AACtB,cAAM,QAAQ,OAAO,GAAG;AAExB,YAAI,UAAU,QAAW;AACxB,iBAAO;AAAA,QACR;AAEA,YAAI,UAAU,MAAM;AACnB,iBAAO,OAAO,KAAK,OAAO;AAAA,QAC3B;AAEA,YAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,iBAAO,MACL,OAAO,UAAU,GAAG,GAAG,CAAC,CAAC,EACzB,KAAK,GAAG;AAAA,QACX;AAEA,eAAO,OAAO,KAAK,OAAO,IAAI,MAAM,OAAO,OAAO,OAAO;AAAA,MAC1D,CAAC,EAAE,OAAO,CAAAE,OAAKA,GAAE,SAAS,CAAC,EAAE,KAAK,GAAG;AAAA,IACtC;AAEA,YAAQ,WAAW,CAAC,KAAK,YAAY;AACpC,gBAAU,OAAO,OAAO;AAAA,QACvB,QAAQ;AAAA,MACT,GAAG,OAAO;AAEV,YAAM,CAAC,MAAM,IAAI,IAAI,aAAa,KAAK,GAAG;AAE1C,aAAO,OAAO;AAAA,QACb;AAAA,UACC,KAAK,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK;AAAA,UAC3B,OAAOF,OAAM,QAAQ,GAAG,GAAG,OAAO;AAAA,QACnC;AAAA,QACA,WAAW,QAAQ,2BAA2B,OAAO,EAAC,oBAAoB,OAAO,MAAM,OAAO,EAAC,IAAI,CAAC;AAAA,MACrG;AAAA,IACD;AAEA,YAAQ,eAAe,CAAC,QAAQ,YAAY;AAC3C,gBAAU,OAAO,OAAO;AAAA,QACvB,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT,GAAG,OAAO;AAEV,YAAM,MAAM,WAAW,OAAO,GAAG,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK;AACpD,YAAM,eAAe,QAAQ,QAAQ,OAAO,GAAG;AAC/C,YAAM,qBAAqB,QAAQ,MAAM,cAAc,EAAC,MAAM,MAAK,CAAC;AAEpE,YAAM,QAAQ,OAAO,OAAO,oBAAoB,OAAO,KAAK;AAC5D,UAAI,cAAc,QAAQ,UAAU,OAAO,OAAO;AAClD,UAAI,aAAa;AAChB,sBAAc,IAAI,WAAW;AAAA,MAC9B;AAEA,UAAI,OAAO,QAAQ,OAAO,GAAG;AAC7B,UAAI,OAAO,oBAAoB;AAC9B,eAAO,IAAI,OAAO,OAAO,oBAAoB,OAAO,CAAC;AAAA,MACtD;AAEA,aAAO,GAAG,GAAG,GAAG,WAAW,GAAG,IAAI;AAAA,IACnC;AAEA,YAAQ,OAAO,CAAC,OAAO,QAAQ,YAAY;AAC1C,gBAAU,OAAO,OAAO;AAAA,QACvB,yBAAyB;AAAA,MAC1B,GAAG,OAAO;AAEV,YAAM,EAAC,KAAK,OAAO,mBAAkB,IAAI,QAAQ,SAAS,OAAO,OAAO;AACxE,aAAO,QAAQ,aAAa;AAAA,QAC3B;AAAA,QACA,OAAO,aAAa,OAAO,MAAM;AAAA,QACjC;AAAA,MACD,GAAG,OAAO;AAAA,IACX;AAEA,YAAQ,UAAU,CAAC,OAAO,QAAQ,YAAY;AAC7C,YAAM,kBAAkB,MAAM,QAAQ,MAAM,IAAI,SAAO,CAAC,OAAO,SAAS,GAAG,IAAI,CAAC,KAAK,UAAU,CAAC,OAAO,KAAK,KAAK;AAEjH,aAAO,QAAQ,KAAK,OAAO,iBAAiB,OAAO;AAAA,IACpD;AAAA;AAAA;;;ACjZM,SAAU,eAAe,KAAW;AACxC,QAAM,UAA8B,IAAI,QAAQ,GAAG,MAAM,KAAK,IAAI,QAAQ,GAAG,IAAI;AAEjF,QAAM,cAAsB,OAAO,YAAY,cAAc,IAAI,OAAO,OAAO,IAAI;AAEnF,SAAO;AACT;AAEM,SAAU,oBAAoB,aAAqB,gBAAmB;AAC1E,MAAI,cAAc,iBAAiB,WAAW;AAE9C,gBAAW,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,WAAW,GAAK,cAAc;AAEjD,gBAAc,kBAAkB,WAAW;AAE3C,SAAO;AACT;AAEM,SAAU,iBAAiB,aAAmB;AAClD,SAAwB,uBAAM,WAAW;AAC3C;AAEM,SAAU,kBAAkB,aAAgB;AAChD,SAAwB,2BAAU,WAAW;AAC/C;AA1BA;;;uBAAkC;;;;;ACS5B,SAAU,uBAAuB,QAAW;AAChD,SAAO,OAAO,OAAO,WAAW;AAClC;AAEM,SAAU,sBAAsB,KAAW;AAC/C,QAAM,YAAoB,IAAI,QAAQ,GAAG;AAEzC,QAAM,UAA8B,IAAI,QAAQ,GAAG,MAAM,KAAK,IAAI,QAAQ,GAAG,IAAI;AAEjF,QAAM,WAAmB,IAAI,UAAU,GAAG,SAAS;AAEnD,QAAM,OAAe,IAAI,UAAU,YAAY,GAAG,OAAO;AAEzD,WAAS,oBAAoBG,OAAY;AACvC,UAAM,YAAY;AAElB,UAAM,SAASA,MAAK,MAAM,SAAS;AAEnC,UAAMC,kBAAiB;MACrB,gBAAgB,OAAO,CAAC;MACxB,SAAS,SAAS,OAAO,CAAC,GAAG,EAAE;;AAGjC,WAAOA;EACT;AAEA,QAAM,iBAAwC,oBAAoB,IAAI;AAEtE,QAAM,cAAsB,OAAO,YAAY,cAAc,IAAI,OAAO,OAAO,IAAI;AAEnF,WAAS,iBAAiBC,cAAmB;AAC3C,UAAMC,UAAS,iBAAiBD,YAAW;AAE3C,UAAM,aAAiC;MACrC,KAAKC,QAAO,OAAO;MACnB,QAAQA,QAAO,UAAU;;AAG3B,WAAO;EACT;AAEA,QAAM,cAAkC,iBAAiB,WAAW;AAEpE,QAAM,SAAM,OAAA,OAAA,OAAA,OAAA,EACV,SAAQ,GACL,cAAc,GACd,WAAW;AAGhB,SAAO;AACT;AApDA;;;;;;;ACIM,SAAU,cAAc,OAAa;AACzC,SAAO,UAAU,MAAO,OAAO,UAAU,YAAY,MAAM,KAAI,MAAO;AACxE;AAEM,SAAU,aAAa,OAAY;AACvC,SAAO,EAAE,SAAS,MAAM;AAC1B;AAEM,SAAUC,UAAS,KAAQ;AAC/B,SAAgB,SAAS,GAAG;AAC9B;AAEM,SAAUC,cAAa,KAAQ;AACnC,SAAgB,aAAa,GAAG;AAClC;AAEM,SAAUC,eAAc,KAAQ;AACpC,SAAgB,cAAc,GAAG;AACnC;AAEM,SAAUC,SAAQ,KAAQ;AAC9B,SAAgB,QAAQ,GAAG;AAC7B;AAEM,SAAUC,aAAY,KAAQ;AAClC,SAAgB,YAAY,GAAG;AACjC;AAEM,SAAUC,aAAY,OAAY,QAAe;AACrD,SAAgB,YAAY,OAAO,MAAM;AAC3C;AAEM,SAAU,sBAAsB,QAAW;AAC/C,SAAO,OAAO,OAAO,WAAW;AAClC;AAEM,SAAU,iBAAiB,QAAW;AAC1C,SAAO,OAAO,OAAO,WAAW;AAClC;AAEM,SAAU,yBAAyB,QAAW;AAClD,SAAO,OAAO,OAAO,WAAW;AAClC;AAEM,SAAU,uBAAuB,QAAW;AAChD,SAAO,OAAO,OAAO,UAAU;AACjC;AAEM,SAAU,gBAAgB,QAAW;AACzC,SAAO,OAAO,OAAO,UAAU;AACjC;AAEM,SAAU,gBAAgB,OAAa;AAC3C,SAAO,gBAAgB,SAAS,KAAK,KAAK,MAAM,WAAW,KAAK;AAClE;AAEM,SAAU,gBAAgB,SAAwB;AACtD,MAAI,QAAQ,OAAO,WAAW,KAAK,GAAG;AACpC,WAAO;;AAET,MAAI,gBAAgB,SAAS,QAAQ,MAAM,GAAG;AAC5C,WAAO;;AAET,SAAO;AACT;AA3EA;;IAAAC;AACA,IAAAA;;;;;ACDA,IAAAC,eAAA;SAAAA,cAAA;sBAAAC;EAAA;;uBAAAC;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBAAAC;EAAA,wBAAAC;EAAA;qBAAAC;EAAA,0BAAAC;EAAA,mBAAAC;EAAA,qBAAAC;EAAA,4BAAAC;EAAA;;yBAAAC;EAAA,8BAAAC;EAAA,mBAAAC;EAAA,0BAAAC;EAAA;;sBAAAC;EAAA,2BAAAC;EAAA;;iBAAAC;EAAA;;uBAAAC;EAAA;kBAAAC;EAAA;;qBAAAC;EAAA;;;;;;;;;;sBAAAC;EAAA;;;;mBAAAC;EAAA;+BAAAC;EAAA,uBAAAC;EAAA;uBAAAC;EAAA,yBAAAC;EAAA,mBAAAC;EAAA;;;;IAAAC,YAAA;;;AACA;AACA;AACA;AACA;AACA;AACA,IAAAC;AACA;AACA,IAAAC;AACA;AACA;AACA;AACA;;;;;ACZA,IAAAC,mBAAA;AAAA;AAAA;AAEA,WAAO,UAAU,WAAY;AAC3B,YAAM,IAAI;AAAA,QACR;AAAA,MAEF;AAAA,IACF;AAAA;AAAA;;;ACPA;AAAA;AACA,WAAO,UAAU,WAAY;AAC3B,UAAI,YAAY,SAAS,aAAa;AACtC,UAAI,CAAC,UAAU,YAAY;AACzB,eAAO,WAAY;AAAA,QAAC;AAAA,MACtB;AACA,UAAI,SAAS,SAAS;AAEtB,UAAI,SAAS,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,UAAU,YAAY,KAAK;AAC7C,eAAO,KAAK,UAAU,WAAW,CAAC,CAAC;AAAA,MACrC;AAEA,cAAQ,OAAO,QAAQ,YAAY,GAAG;AAAA,QACpC,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,KAAK;AACZ;AAAA,QAEF;AACE,mBAAS;AACT;AAAA,MACJ;AAEA,gBAAU,gBAAgB;AAC1B,aAAO,WAAY;AACjB,kBAAU,SAAS,WACnB,UAAU,gBAAgB;AAE1B,YAAI,CAAC,UAAU,YAAY;AACzB,iBAAO,QAAQ,SAAS,OAAO;AAC7B,sBAAU,SAAS,KAAK;AAAA,UAC1B,CAAC;AAAA,QACH;AAEA,kBACA,OAAO,MAAM;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;ACtCA;AAAA;AAAA;AAEA,QAAI,kBAAkB;AAEtB,QAAI,4BAA4B;AAAA,MAC9B,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AAEA,QAAI,iBAAiB;AAErB,aAAS,OAAO,SAAS;AACvB,UAAI,WAAW,YAAY,KAAK,UAAU,SAAS,IAAI,MAAM,UAAU;AACvE,aAAO,QAAQ,QAAQ,iBAAiB,OAAO;AAAA,IACjD;AAEA,aAAS,KAAK,MAAM,SAAS;AAC3B,UAAI,OACF,SACA,kBACA,OACA,WACA,MACA,UAAU;AACZ,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AAAA,MACb;AACA,cAAQ,QAAQ,SAAS;AACzB,UAAI;AACF,2BAAmB,gBAAgB;AAEnC,gBAAQ,SAAS,YAAY;AAC7B,oBAAY,SAAS,aAAa;AAElC,eAAO,SAAS,cAAc,MAAM;AACpC,aAAK,cAAc;AAEnB,aAAK,aAAa;AAElB,aAAK,MAAM,MAAM;AAEjB,aAAK,MAAM,WAAW;AACtB,aAAK,MAAM,MAAM;AACjB,aAAK,MAAM,OAAO;AAElB,aAAK,MAAM,aAAa;AAExB,aAAK,MAAM,mBAAmB;AAC9B,aAAK,MAAM,gBAAgB;AAC3B,aAAK,MAAM,eAAe;AAC1B,aAAK,MAAM,aAAa;AACxB,aAAK,iBAAiB,QAAQ,SAAS,GAAG;AACxC,YAAE,gBAAgB;AAClB,cAAI,QAAQ,QAAQ;AAClB,cAAE,eAAe;AACjB,gBAAI,OAAO,EAAE,kBAAkB,aAAa;AAC1C,uBAAS,QAAQ,KAAK,+BAA+B;AACrD,uBAAS,QAAQ,KAAK,0BAA0B;AAChD,qBAAO,cAAc,UAAU;AAC/B,kBAAIC,UAAS,0BAA0B,QAAQ,MAAM,KAAK,0BAA0B,SAAS;AAC7F,qBAAO,cAAc,QAAQA,SAAQ,IAAI;AAAA,YAC3C,OAAO;AACL,gBAAE,cAAc,UAAU;AAC1B,gBAAE,cAAc,QAAQ,QAAQ,QAAQ,IAAI;AAAA,YAC9C;AAAA,UACF;AACA,cAAI,QAAQ,QAAQ;AAClB,cAAE,eAAe;AACjB,oBAAQ,OAAO,EAAE,aAAa;AAAA,UAChC;AAAA,QACF,CAAC;AAED,iBAAS,KAAK,YAAY,IAAI;AAE9B,cAAM,mBAAmB,IAAI;AAC7B,kBAAU,SAAS,KAAK;AAExB,YAAI,aAAa,SAAS,YAAY,MAAM;AAC5C,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MAAM,+BAA+B;AAAA,QACjD;AACA,kBAAU;AAAA,MACZ,SAAS,KAAK;AACZ,iBAAS,QAAQ,MAAM,sCAAsC,GAAG;AAChE,iBAAS,QAAQ,KAAK,0BAA0B;AAChD,YAAI;AACF,iBAAO,cAAc,QAAQ,QAAQ,UAAU,QAAQ,IAAI;AAC3D,kBAAQ,UAAU,QAAQ,OAAO,OAAO,aAAa;AACrD,oBAAU;AAAA,QACZ,SAASC,MAAK;AACZ,mBAAS,QAAQ,MAAM,wCAAwCA,IAAG;AAClE,mBAAS,QAAQ,MAAM,wBAAwB;AAC/C,oBAAU,OAAO,aAAa,UAAU,QAAQ,UAAU,cAAc;AACxE,iBAAO,OAAO,SAAS,IAAI;AAAA,QAC7B;AAAA,MACF,UAAE;AACA,YAAI,WAAW;AACb,cAAI,OAAO,UAAU,eAAe,YAAY;AAC9C,sBAAU,YAAY,KAAK;AAAA,UAC7B,OAAO;AACL,sBAAU,gBAAgB;AAAA,UAC5B;AAAA,QACF;AAEA,YAAI,MAAM;AACR,mBAAS,KAAK,YAAY,IAAI;AAAA,QAChC;AACA,yBAAiB;AAAA,MACnB;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3GD,SAAAC,GAAOC,GAAKC,GAAAA;AAC3B,WAASC,KAAKD;AAAOD,MAAIE,CAAAA,IAAKD,EAAMC,CAAAA;AACpC,SAA6BF;AAC9B;AAQO,SAASG,GAAeC,GAAGC,GAAAA;AACjC,WAASH,KAAKE;AAAG,QAAU,eAANF,KAAAA,EAAsBA,KAAKG;AAAI,aAAA;AACpD,WAASH,MAAKG;AAAG,QAAU,eAANH,MAAoBE,EAAEF,EAAAA,MAAOG,EAAEH,EAAAA;AAAI,aAAA;AACxD,SAAA;AACD;ACdgB,SAAAI,EAAqBC,GAAWC,GAAAA;AAC/C,MAAMC,IAAQD,EAAAA,GAMdE,KAAqCC,EAAS,EAC7CC,GAAW,EAAEC,IAAQJ,GAAOK,GAAcN,EAAAA,EAAAA,CAAAA,GADlCI,IAASF,GAATE,CAAAA,EAAAA,GAAaG,KAAWL,GAIjCM,CAAAA;AAqBA,SArBAA,GAAgB,WAAA;AACfJ,MAASC,KAAUJ,GACnBG,EAAUE,IAAeN,GAErBS,GAAkBL,CAAAA,KACrBG,GAAY,EAAEH,GAAAA,EAAAA,CAAAA;EAEhB,GAAG,CAACL,GAAWE,GAAOD,CAAAA,CAAAA,GAEtBU,EAAU,WAAA;AAKT,WAJID,GAAkBL,CAAAA,KACrBG,GAAY,EAAEH,GAAAA,EAAAA,CAAAA,GAGRL,EAAU,WAAA;AACZU,MAAAA,GAAkBL,CAAAA,KACrBG,GAAY,EAAEH,GAAAA,EAAAA,CAAAA;IAEhB,CAAA;EACD,GAAG,CAACL,CAAAA,CAAAA,GAEGE;AACR;AAGA,SAASQ,GAAkBE,GAAAA;AAC1B,MDfkBC,GAAGC,GCefC,KAAoBH,EAAKL,GACzBS,IAAYJ,EAAIN;AACtB,MAAA;AACC,QAAMW,KAAYF,GAAAA;AAClB,WAAA,GDnBiBF,ICmBNG,QDnBSF,ICmBEG,QDlBG,MAANJ,KAAW,IAAIA,KAAM,IAAIC,MAAQD,KAAMA,KAAKC,KAAMA;ECqBtE,SAFSI,IAAAA;AACR,WAAA;EACD;AACD;AAEgB,SAAAC,EAAgBC,GAAAA;AAC/BA,IAAAA;AACD;AAEgB,SAAAC,EAAiBC,GAAAA;AAChC,SAAOA;AACR;AAEgB,SAAAC,KAAAA;AACf,SAAO,CAAA,OAAQJ,CAAAA;AAChB;AAIkCV,SC/DlBe,EAAcC,GAAGC,GAAAA;AAChCC,OAAKjC,QAAQ+B,GACbE,KAAKC,UAAUF;AAChB;ACCgB,SAAAG,EAAKH,GAAGI,GAAAA;AACvB,WAASC,GAAaC,IAAAA;AACrB,QAAIC,IAAMN,KAAKjC,MAAMuC,KACjBC,KAAYD,KAAOD,GAAUC;AAKjC,WAAA,CAJKC,MAAaD,MACjBA,EAAIE,OAAOF,EAAI,IAAA,IAASA,EAAIG,UAAU,OAGlCN,IAAAA,CAIGA,EAASH,KAAKjC,OAAOsC,EAAAA,KAAAA,CAAeE,KAHpCtC,GAAe+B,KAAKjC,OAAOsC,EAAAA;EAIpC;AAEA,WAASK,EAAO3C,IAAAA;AAEf,WADAiC,KAAKW,wBAAwBP,IACtBQ,EAAcb,GAAGhC,EAAAA;EACzB;AAIA,SAHA2C,EAAOG,cAAc,WAAWd,EAAEc,eAAed,EAAEe,QAAQ,KAC3DJ,EAAOK,UAAUC,mBAAAA,MACjBN,EAAMO,MAAAA,MACCP;AACR;ACjBC,SASeQ,EAAWC,GAAAA;AAC1B,WAASC,EAAUrD,IAAAA;AAClB,QAAIsD,IAAQxD,GAAO,CAAE,GAAEE,EAAAA;AAEvB,WAAA,OADOsD,EAAMf,KACNa,EAAGE,GAAOtD,GAAMuC,OAAO,IAAA;EAC/B;AAYA,SATAc,EAAUE,WAAWC,IAKrBH,EAAUI,SAASJ,GAEnBA,EAAUL,UAAUC,mBAAmBI,EAASH,MAAAA,MAChDG,EAAUP,cAAc,iBAAiBM,EAAGN,eAAeM,EAAGL,QAAQ,KAC/DM;AACR;AECA,SAASK,EAAcC,GAAOC,GAAgBC,GAAAA;AA4B7C,SA3BIF,MACCA,EAAKG,OAAeH,EAAKG,IAAAC,QAC5BJ,EAAKG,IAAAC,IAAAnD,GAA0BoD,QAAQ,SAAAC,IAAAA;AACR,kBAAA,OAAnBA,GAAMH,OAAyBG,GAAMH,IAAAA;EACjD,CAAA,GAEAH,EAAKG,IAAAC,MAAsB,OAIJ,SADxBJ,IAAQ7D,GAAO,CAAA,GAAI6D,CAAAA,GACVG,QACJH,EAAKG,IAAAI,QAA2BL,MACnCF,EAAKG,IAAAI,MAAyBN,IAG/BD,EAAKG,IAAAK,MAAAA,MAELR,EAAKG,MAAc,OAGpBH,EAAKS,MACJT,EAAKS,OACLT,EAAKS,IAAWC,IAAI,SAAAC,IAAAA;AAAK,WACxBZ,EAAcY,IAAOV,GAAgBC,CAAAA;EAAU,CAAA,IAI3CF;AACR;AAEA,SAASY,EAAeZ,GAAOC,GAAgBY,GAAAA;AAoB9C,SAnBIb,KAASa,MACZb,EAAKc,MAAa,MAClBd,EAAKS,MACJT,EAAKS,OACLT,EAAKS,IAAWC,IAAI,SAAAC,IAAAA;AAAK,WACxBC,EAAeD,IAAOV,GAAgBY,CAAAA;EAAe,CAAA,GAGnDb,EAAKG,OACJH,EAAKG,IAAAI,QAA2BN,MAC/BD,EAAKQ,OACRK,EAAeE,YAAYf,EAAKQ,GAAAA,GAEjCR,EAAKG,IAAAK,MAAAA,MACLR,EAAKG,IAAAI,MAAyBM,KAK1Bb;AACR;AAGO,SAASgB,KAAAA;AAEf1C,OAAI2C,MAA2B,GAC/B3C,KAAK4C,IAAc,MACnB5C,KAAI6C,MAAuB;AAC5B;AAqIgB,SAAAC,EAAUpB,GAAAA;AAEzB,MAAIqB,IAAYrB,EAAK/C,GAAAkD;AACrB,SAAOkB,KAAaA,EAASC,OAAeD,EAASC,IAAYtB,CAAAA;AAClE;AAEgB,SAAAuB,EAAKC,GAAAA;AACpB,MAAIC,GACAJ,IACAxD;AAEJ,WAAS6D,GAAKrF,IAAAA;AAab,QAZKoF,MACJA,IAAOD,EAAAA,GACFG,KACJ,SAAAC,IAAAA;AACCP,MAAAA,KAAYO,GAAQC,WAAWD;IAChC,GACA,SAAAE,IAAAA;AACCjE,UAAQiE;IACT,CAAA,GAIEjE;AACH,YAAMA;AAGP,QAAA,CAAKwD;AACJ,YAAMI;AAGP,WAAOvC,EAAcmC,IAAWhF,EAAAA;EACjC;AAIA,SAFAqF,GAAKvC,cAAc,QACnBuC,GAAInC,MAAAA,MACGmC;AACR;AAAA,SC1QgBK,IAAAA;AACfzD,OAAK0D,IAAQ,MACb1D,KAAK2D,IAAO;AACb;AEPA,SAASC,EAAgB7F,GAAAA;AAExB,SADAiC,KAAK6D,kBAAkB,WAAA;AAAM,WAAA9F,EAAMkC;EAAO,GACnClC,EAAM+F;AACd;AASA,SAASC,EAAOhG,GAAAA;AACf,MAAMiG,IAAQhE,MACViE,KAAYlG,EAAMmG;AActB,MAZAF,EAAMG,uBAAuB,WAAA;AAC5B3C,MAAO,MAAMwC,EAAMI,CAAAA,GACnBJ,EAAMI,IAAQ,MACdJ,EAAME,IAAa;EACpB,GAIIF,EAAME,KAAcF,EAAME,MAAeD,MAC5CD,EAAMG,qBAAAA,GAAAA,CAGFH,EAAMI,GAAO;AAGjB,aADIC,IAAOL,EAAKxB,KACA,SAAT6B,KAAAA,CAAkBA,EAAIC,OAA2B,SAAjBD,EAAI1F;AAC1C0F,UAAOA,EAAI1F;AAGZqF,MAAME,IAAaD,IAGnBD,EAAMI,IAAQ,EACbG,UAAU,GACVC,YAAYP,IACZQ,YAAY,CAAA,GACZtC,KAAW,EAAEmC,KAAOD,EAAIC,IAAAA,GACxBI,UAAU,WAAA;AAAF,aAAA;IAAY,GACpBC,cAAA,SAAatC,IAAOuC,GAAAA;AACnB5E,WAAKyE,WAAWI,KAAKxC,EAAAA,GACrB2B,EAAME,EAAWS,aAAatC,IAAOuC,CAAAA;IACtC,GACAE,aAAAA,SAAYzC,IAAAA;AACXrC,WAAKyE,WAAWM,OAAO/E,KAAKyE,WAAWO,QAAQ3C,EAAAA,MAAW,GAAG,CAAA,GAC7D2B,EAAME,EAAWY,YAAYzC,EAAAA;IAC9B,EAAA;EAEF;AAGAb,IACCZ,EAAcgD,GAAiB,EAAE3D,SAAS+D,EAAM/D,QAAAA,GAAWlC,EAAKyE,GAAAA,GAChEwB,EAAMI,CAAAA;AAER;AAOO,SAASa,EAAavD,GAAOuC,GAAAA;AACnC,MAAMiB,KAAKtE,EAAcmD,GAAQ,EAAEvB,KAAQd,GAAOwC,GAAYD,EAAAA,CAAAA;AAE9D,SADAiB,GAAGC,gBAAgBlB,GACZiB;AACR;ACOgB,SAAA1D,GAAOE,GAAO0D,GAAQC,GAAAA;AAUrC,SAPwB,QAApBD,EAAMjD,QACTiD,EAAOE,cAAc,KAGtBC,EAAa7D,GAAO0D,CAAAA,GACG,cAAA,OAAZC,KAAwBA,EAAAA,GAE5B3D,IAAQA,EAAKG,MAAc;AACnC;AAEgB,SAAA2D,GAAQ9D,GAAO0D,GAAQC,GAAAA;AAItC,SAHAI,EAAc/D,GAAO0D,CAAAA,GACE,cAAA,OAAZC,KAAwBA,EAAAA,GAE5B3D,IAAQA,EAAKG,MAAc;AACnC;AAYA,SAAS6D,KAAAA;AAAQ;AAEjB,SAASC,KAAAA;AACR,SAAW3F,KAAC4F;AACb;AAEA,SAASC,KAAAA;AACR,SAAO7F,KAAK8F;AACb;ACxEA,SAASC,GAAcC,GAAAA;AACtB,SAAOpF,EAAcqF,KAAK,MAAMD,CAAAA;AACjC;AAOA,SAASE,GAAeC,GAAAA;AACvB,SAAA,CAAA,CAASA,KAAWA,EAAQ7E,aAAa8E;AAC1C;AAOA,SAASC,GAAWF,GAAAA;AACnB,SAAOD,GAAeC,CAAAA,KAAYA,EAAQH,SAASM;AACpD;AAOA,SAASC,GAAOJ,GAAAA;AACf,SAAA,CAAA,CACGA,KAAAA,CAAAA,CACAA,EAAQtF,gBACsB,YAAA,OAAxBsF,EAAQtF,eACfsF,EAAQtF,uBAAuB2F,WAChCL,EAAQtF,YAAY4F,WAAW,OAAA;AAEjC;AASA,SAASC,GAAaP,GAAAA;AACrB,SAAKD,GAAeC,CAAAA,IACbQ,EAAmBC,MAAM,MAAMC,SAAAA,IADDV;AAEtC;AAOA,SAASW,GAAuB7C,GAAAA;AAC/B,SAAA,CAAA,CAAIA,EAAS9B,QACZoD,EAAa,MAAMtB,CAAAA,GAAAA;AAIrB;AAOA,SAAS8C,GAAYhE,GAAAA;AACpB,SACEA,MACCA,EAAUiE,QAAgC,MAAvBjE,EAAUwB,YAAkBxB,MACjD;AAEF;IVrDakE,GGlETC,IASS3F,ICVP4F,GAMOC,GCJPC,IAqBAC,GCPAC,IGSOnB,IAIPoB,IAEAC,IACAC,IACAC,GAKAC,GA+DFC,IAwJAC,IApIEC,IAuHFC,IAcEC,IAQAC,IAwBOC,ICrPPC,IAyFAC,IAWAC,IAMAC,IAGOC,IAwCbC;;;;;;;AV3Ha,IAAAxB,IAAqBnI;AEpClC,KDvBAe,EAAckB,YAAY,IAAI2H,KAENC,uBAAAA,MACxB9I,EAAckB,UAAUJ,wBAAwB,SAAU5C,GAAO6K,GAAAA;AAChE,aAAO3K,GAAe+B,KAAKjC,OAAOA,CAAAA,KAAUE,GAAe+B,KAAK4I,OAAOA,CAAAA;IACxE;AEZA,IAAI1B,KAAc2B,EAAOhG;AACzBgG,MAAOhG,MAAS,SAAAnB,GAAAA;AACXA,QAAMsE,QAAQtE,EAAMsE,KAAI/E,OAAeS,EAAMpB,QAChDoB,EAAM3D,MAAMuC,MAAMoB,EAAMpB,KACxBoB,EAAMpB,MAAM,OAET4G,MAAaA,GAAYxF,CAAAA;IAC9B;AAEO,IAAMH,KACM,eAAA,OAAVuH,UACPA,OAAOC,OACPD,OAAOC,IAAI,mBAAA,KACZ;ACdD,IAAM5B,IAAQ,SAACrD,GAAU3C,GAAAA;AACxB,aAAgB,QAAZ2C,IAA6B,OAC1BkF,EAAaA,EAAalF,CAAAA,EAAU1B,IAAIjB,CAAAA,CAAAA;IAChD;AAHA,IAMaiG,IAAW,EACvBhF,KAAK+E,GACLpF,SAASoF,GACT8B,OAAK,SAACnF,GAAAA;AACL,aAAOA,IAAWkF,EAAalF,CAAAA,EAAUoF,SAAS;IACnD,GACAC,MAAI,SAACrF,GAAAA;AACJ,UAAMsF,IAAaJ,EAAalF,CAAAA;AAChC,UAA0B,MAAtBsF,EAAWF;AAAc,cAAM;AACnC,aAAOE,EAAW,CAAA;IACnB,GACAC,SAASL,EAAAA;AAjBV,ICEM3B,KAAgBwB,EAAO3G;AAC7B2G,MAAO3G,MAAe,SAAU3C,GAAO+J,GAAUC,GAAUC,IAAAA;AAC1D,UAAIjK,EAAM8D;AAKT,iBAHIN,GACArB,KAAQ4H,GAEJ5H,KAAQA,GAAK/C;AACpB,eAAKoE,IAAYrB,GAAKG,QAAgBkB,EAASlB;AAM9C,mBALqB,QAAjByH,EAAQpH,QACXoH,EAAQpH,MAAQqH,EAAQrH,KACxBoH,EAAQnH,MAAaoH,EAAQpH,MAGvBY,EAASlB,IAAkBtC,GAAO+J,CAAAA;;AAI5CjC,MAAAA,GAAc9H,GAAO+J,GAAUC,GAAUC,EAAAA;IAC1C;AAEA,IAAMlC,IAAauB,EAAQY;AAC3BZ,MAAQY,UAAU,SAAU/H,GAAAA;AAE3B,UAAMqB,IAAYrB,EAAKG;AACnBkB,WAAaA,EAAS2G,OACzB3G,EAAS2G,IAAAA,GAON3G,KEpCuB,KFoCVrB,EAAKiB,QACrBjB,EAAMsE,OAAO,OAGVsB,KAAYA,EAAW5F,CAAAA;IAC5B,IAmEAgB,GAAS3B,YAAY,IAAI2H,KAOP7G,MAAoB,SAAU8H,GAASC,GAAAA;AACxD,UAAMC,IAAsBD,EAAe/H,KAGrC9B,KAAIC;AAEW,cAAjBD,GAAE6C,MACL7C,GAAE6C,IAAc,CAAA,IAEjB7C,GAAE6C,EAAYiC,KAAKgF,CAAAA;AAEnB,UAAMtC,IAAUzE,EAAU/C,GAACyC,GAAAA,GAEvBsH,KAAAA,OACEC,IAAa,WAAA;AACdD,QAAAA,OAEJA,KAAAA,MACAD,EAAmBH,MAAc,MAE7BnC,IACHA,EAAQyC,EAAAA,IAERA,GAAAA;MAEF;AAEAH,QAAmBH,MAAcK;AAEjC,UAAMC,KAAuB,WAAA;AAC5B,YAAA,CAAA,EAAOjK,GAAC4C,KAA0B;AAGjC,cAAI5C,GAAE6I,MAAK5F,KAAa;AACvB,gBAAMiH,KAAiBlK,GAAE6I,MAAK5F;AAC9BjD,YAAAA,GAACyC,IAAAL,IAAkB,CAAA,IAAKG,EACvB2H,IACAA,GAAcpI,IAAAI,KACdgI,GAAcpI,IAAAqI,GAAAA;UAEhB;AAIA,cAAIpH;AACJ,eAHA/C,GAAEoK,SAAS,EAAEnH,KAAajD,GAAC8C,MAAuB,KAAA,CAAA,GAG1CC,KAAY/C,GAAE6C,EAAYwH,IAAAA;AACjCtH,YAAAA,GAAUjE,YAAAA;QAEZ;MACD;AAQEkB,MAAAA,GAAC4C,SE5KwB,KF6KxBiH,EAAejH,OAEjB5C,GAAEoK,SAAS,EAAEnH,KAAajD,GAAC8C,MAAuB9C,GAACyC,IAAAL,IAAkB,CAAA,EAAA,CAAA,GAEtEwH,EAAQtG,KAAK0G,GAAYA,CAAAA;IAC1B,GAEArH,GAAS3B,UAAUoD,uBAAuB,WAAA;AACzCnE,WAAK4C,IAAc,CAAA;IACpB,GAOAF,GAAS3B,UAAUS,SAAS,SAAUzD,GAAO6K,GAAAA;AAC5C,UAAI5I,KAAI6C,KAAsB;AAI7B,YAAI7C,KAAIwC,IAAAL,KAAmB;AAC1B,cAAMR,KAAiB0I,SAASzJ,cAAc,KAAA,GACxC0J,KAAoBtK,KAAIwC,IAAAL,IAAkB,CAAA,EAAEN;AAClD7B,eAAIwC,IAAAL,IAAkB,CAAA,IAAKV,EAC1BzB,KAAI6C,KACJlB,IACC2I,GAAiBJ,MAAsBI,GAAiBrI,GAAAA;QAE3D;AAEAjC,aAAI6C,MAAuB;MAC5B;AAIA,UAAM0H,IACL3B,EAAK5F,OAAepC,EAAc0F,GAAU,MAAMvI,EAAMwM,QAAAA;AAGzD,aAFIA,MAAUA,EAAQ5H,OAAAA,MAEf,CACN/B,EAAc0F,GAAU,MAAMsC,EAAK5F,MAAc,OAAOjF,EAAM+F,QAAAA,GAC9DyG,CAAAA;IAEF;ACxMA,IAAMhD,KAAU,SAACiD,GAAMnI,GAAOoI,GAAAA;AAc7B,UAAA,EAbMA,EAdgB,CAAA,MAcSA,EAfR,CAAA,KAqBtBD,EAAK7G,EAAK+G,OAAOrI,CAAAA,GAQhBmI,EAAKzM,MAAM4M,gBACmB,QAA9BH,EAAKzM,MAAM4M,YAAY,CAAA,KAAA,CAAcH,EAAK7G,EAAKiH;AASjD,aADAH,IAAOD,EAAK9G,GACL+G,KAAM;AACZ,iBAAOA,EAAKvB,SAAS;AACpBuB,cAAKL,IAAAA,EAALK;AAED,cAAIA,EA1CiB,CAAA,IA0CMA,EA3CL,CAAA;AA4CrB;AAEDD,YAAK9G,IAAQ+G,IAAOA,EA5CJ,CAAA;QA6CjB;IACD;AEwBA,KFnBAhH,EAAa1C,YAAY,IAAI2H,KAEP1F,MAAc,SAAUX,GAAAA;AAC7C,UAAMmI,IAAOxK,MACP6K,IAAY/H,EAAU0H,EAAIhI,GAAAA,GAE5BiI,KAAOD,EAAK7G,EAAKmH,IAAIzI,CAAAA;AAGzB,aAFAoI,GA5DuB,CAAA,KA8DhB,SAAAM,GAAAA;AACN,YAAMC,KAAmB,WAAA;AACnBR,YAAKzM,MAAM4M,eAKfF,GAAK5F,KAAKkG,CAAAA,GACVxD,GAAQiD,GAAMnI,GAAOoI,EAAAA,KAHrBM,EAAAA;QAKF;AACIF,YACHA,EAAUG,EAAAA,IAEVA,GAAAA;MAEF;IACD,GAEAvH,EAAa1C,UAAUS,SAAS,SAAUzD,GAAAA;AACzCiC,WAAK0D,IAAQ,MACb1D,KAAK2D,IAAO,oBAAIsH;AAEhB,UAAMnH,IAAWkF,EAAajL,EAAM+F,QAAAA;AAChC/F,QAAM4M,eAAwC,QAAzB5M,EAAM4M,YAAY,CAAA,KAI1C7G,EAASoH,QAAAA;AAIV,eAASlN,IAAI8F,EAASoF,QAAQlL;AAY7BgC,aAAK2D,EAAKwH,IAAIrH,EAAS9F,CAAAA,GAAKgC,KAAK0D,IAAQ,CAAC,GAAG,GAAG1D,KAAK0D,CAAAA,CAAAA;AAEtD,aAAO3F,EAAM+F;IACd,GAEAL,EAAa1C,UAAUqK,qBACtB3H,EAAa1C,UAAUsK,oBAAoB,WAAA;AAAA,UAAYrH,IAAAhE;AAOtDA,WAAK2D,EAAK5B,QAAQ,SAAC0I,GAAMpI,GAAAA;AACxBkF,QAAAA,GAAQvD,GAAM3B,GAAOoI,CAAAA;MACtB,CAAA;IACD;AGnGY,IAAArE,KACM,eAAA,OAAV0C,UAAyBA,OAAOC,OAAOD,OAAOC,IAAI,eAAA,KAC1D;AAFY,IAIPvB,KACL;AALY,IAMPC,KAAS;AANF,IAOPC,KAAgB;AAPT,IAQPC,IAA6B,eAAA,OAAb0C;AART,IAaPzC,IAAoB,SAAA5B,GAAAA;AACzB,cAAkB,eAAA,OAAV8C,UAA4C,YAAA,OAAZA,OAAAA,IACrC,gBACA,cACDwC,KAAKtF,CAAAA;IAAK;AAGb0C,MAAU3H,UAAUC,mBAAmB,CAAA,GASvC,CACC,sBACA,6BACA,qBAAA,EACCe,QAAQ,SAAAwJ,GAAAA;AACTC,aAAOC,eAAe/C,EAAU3H,WAAWwK,GAAK,EAC/CG,cAAAA,MACAZ,KAAG,WAAA;AACF,eAAO9K,KAAK,YAAYuL,CAAAA;MACzB,GACAJ,KAAG,SAACQ,GAAAA;AACHH,eAAOC,eAAezL,MAAMuL,GAAK,EAChCG,cAAAA,MACAE,UAAAA,MACArN,OAAOoN,EAAAA,CAAAA;MAET,EAAA,CAAA;IAEF,CAAA;AA6BA,IAAI9D,KAAegB,EAAQgD;AAC3BhD,MAAQgD,QAAQ,SAAArI,GAAAA;AAMf,aALIqE,OAAcrE,IAAIqE,GAAarE,CAAAA,IAEnCA,EAAEsI,UAAUpG,IACZlC,EAAEmC,uBAAuBA,IACzBnC,EAAEqC,qBAAqBA,IACfrC,EAAEuI,cAAcvI;IACzB;AAYA,IAAMuE,KAAoC,EACzCiE,YAAAA,OACAN,cAAAA,MACAZ,KAAAA,WAAAA;AACC,aAAW9K,KAACiM;IACb,EAAA;AALD,IAuHIjE,KAAea,EAAQnH;AAC3BmH,MAAQnH,QAAQ,SAAAA,GAAAA;AAEW,kBAAA,OAAfA,EAAMsE,QAlHlB,SAAwBtE,IAAAA;AACvB,YAAI3D,IAAQ2D,GAAM3D,OACjBiI,IAAOtE,GAAMsE,MACbkG,IAAkB,CAAE,GAEjBC,KAAAA,OAAkBnG,EAAKhB,QAAQ,GAAA;AACnC,iBAAShH,KAAKD,GAAO;AACpB,cAAIQ,KAAQR,EAAMC,CAAAA;AAElB,cAAA,EACQ,YAANA,KAAiB,kBAAkBD,KAAkB,QAATQ,MAE5CoJ,KAAgB,eAAN3J,KAA6B,eAATgI,KACzB,YAANhI,KACM,gBAANA,IALD;AAYA,gBAAIoO,KAAapO,EAAEqO,YAAAA;AACT,+BAANrO,KAAwB,WAAWD,KAAwB,QAAfA,EAAMQ,QAGrDP,IAAI,UACY,eAANA,KAAAA,SAAoBO,KAM9BA,KAAQ,KACiB,gBAAf6N,MAAwC,SAAV7N,KACxCA,KAAAA,QAC4B,QAAlB6N,GAAW,CAAA,KAAgC,QAAlBA,GAAW,CAAA,IAC3B,oBAAfA,KACHpO,IAAI,eAEW,eAAfoO,MACU,YAATpG,KAA6B,eAATA,KACpB4B,EAAkB7J,EAAMiI,IAAAA,IAGA,cAAfoG,KACVpO,IAAI,cACqB,aAAfoO,KACVpO,IAAI,eACMyJ,GAAO6D,KAAKtN,CAAAA,MACtBA,IAAIoO,MANJA,KAAapO,IAAI,YAQRmO,MAAmB3E,GAAY8D,KAAKtN,CAAAA,IAC9CA,IAAIA,EAAEsO,QAAQ5E,IAAe,KAAA,EAAO2E,YAAAA,IAChB,SAAV9N,OACVA,KAAAA,SAKkB,cAAf6N,MAECF,EADJlO,IAAIoO,EAAAA,MAEHpO,IAAI,mBAINkO,EAAgBlO,CAAAA,IAAKO;UA/CrB;QAgDD;AAIS,oBAARyH,KACAkG,EAAgBK,YAChBC,MAAMC,QAAQP,EAAgB3N,KAAAA,MAG9B2N,EAAgB3N,QAAQyK,EAAajL,EAAM+F,QAAAA,EAAU/B,QAAQ,SAAAM,IAAAA;AAC5DA,UAAAA,GAAMtE,MAAM2O,WAAAA,MACXR,EAAgB3N,MAAMyG,QAAQ3C,GAAMtE,MAAMQ,KAAAA;QAC5C,CAAA,IAIW,YAARyH,KAAoD,QAAhCkG,EAAgBS,iBACvCT,EAAgB3N,QAAQyK,EAAajL,EAAM+F,QAAAA,EAAU/B,QAAQ,SAAAM,IAAAA;AAE3DA,UAAAA,GAAMtE,MAAM2O,WADTR,EAAgBK,WAAAA,MAElBL,EAAgBS,aAAa3H,QAAQ3C,GAAMtE,MAAMQ,KAAAA,IAGjD2N,EAAgBS,gBAAgBtK,GAAMtE,MAAMQ;QAE/C,CAAA,IAGGR,EAAMkO,SAAAA,CAAUlO,EAAM6O,aACzBV,EAAgBD,QAAQlO,EAAMkO,OAC9BT,OAAOC,eACNS,GACA,aACAnE,EAAAA,MAEShK,EAAM6O,aAAAA,CAAc7O,EAAMkO,SAE1BlO,EAAMkO,SAASlO,EAAM6O,eAD/BV,EAAgBD,QAAQC,EAAgBU,YAAY7O,EAAM6O,YAK3DlL,GAAM3D,QAAQmO;MACf,EAMiBxK,CAAAA,GAGhBA,EAAMJ,WAAW8E,IAEb4B,MAAcA,GAAatG,CAAAA;IAChC;AAIA,IAAMuG,KAAkBY,EAAOgE;AAC/BhE,MAAOgE,MAAW,SAAUnL,GAAAA;AACvBuG,YACHA,GAAgBvG,CAAAA,GAEjBoG,KAAmBpG,EAAKG;IACzB;AAEA,IAAMqG,KAAYW,EAAQiE;AAE1BjE,MAAQiE,SAAS,SAAUpL,GAAAA;AACtBwG,YACHA,GAAUxG,CAAAA;AAGX,UAAM3D,IAAQ2D,EAAM3D,OACdgP,IAAMrL,EAAKQ;AAGT,cAAP6K,KACe,eAAfrL,EAAMsE,QACN,WAAWjI,KACXA,EAAMQ,UAAUwO,EAAIxO,UAEpBwO,EAAIxO,QAAuB,QAAfR,EAAMQ,QAAgB,KAAKR,EAAMQ,QAG9CuJ,KAAmB;IACpB;AAIa,IAAAK,KAAqD,EACjE6E,wBAAwB,EACvBvM,SAAS,EACRwM,aAAAA,SAAYhN,GAAAA;AACX,aAAO6H,GAAgBoF,IAAgBjN,EAAO4B,GAAAA,EAAM9D,MAAMQ;IAC3D,GACA4O,aAAAA,GACAC,YAAAA,IACAC,eAAAA,GACA3N,kBAAAA,GACAV,WAAAA,GACAsO,OAAAA,GACAC,qBAAAA,GACAtG,oBAAAA,GACAnI,iBAAAA,IACA0O,SAAAA,GAEAC,YAAAA,GACAC,QAAAA,GACAjP,UAAAA,GACAL,sBAAAA,GACAwB,eAAAA,GAAAA,EAAAA,EAAAA;AArBU,ICrPPwI,KAAU;AAyFV,IAAAC,KAA0B,SAAChD,GAAUsI,GAAAA;AAAQ,aAAAtI,EAASsI,CAAAA;IAAI;AAA1D,IAWArF,KAAY,SAACjD,GAAUsI,GAAAA;AAAAA,aAAQtI,EAASsI,CAAAA;IAAI;AAX5C,IAiBApF,KAAajC;AAjBb,IAoBOkC,KAAYtC;AApBnB,IA4DNuC,KAAe,EACdhK,UAAAA,GACA6O,OAAAA,GACAG,YAAAA,GACAzO,WAAAA,GACAF,iBAAAA,IACAmI,oBAAAA,GACArH,eAAAA,IACAF,kBAAAA,GACAtB,sBAAAA,GACAoB,iBAAAA,GACAkO,QAAAA,GACAH,qBAAAA,GACAC,SAAAA,GACAL,aAAAA,GACAC,YAAAA,IACAC,eAAAA,GACAjF,SAtKe,UAuKfhB,UAAAA,GACA5F,QAAAA,IACAgE,SAAAA,IACAsB,wBAAAA,IACA7B,cAAAA,GACArE,eAAAA,GACAgN,eAAAA,GACA7H,eAAAA,IACAW,cAAAA,IACAmH,WAAAA,GACAvH,UAAAA,GACAJ,gBAAAA,IACAsC,WAAAA,IACAnC,YAAAA,IACAE,QAAAA,IACAQ,aAAAA,IACA2B,WAAAA,GACA7I,eAAAA,GACAK,MAAAA,GACAgB,YAAAA,GACAoH,WAAAA,IACAD,yBAAAA,IACAE,YAAAA,IACA7F,UAAAA,IACAe,cAAAA,GACAR,MAAAA,GACAkF,oDAAAA,GAAAA;;;;;;;;;;;;;;aC1Oe2F,KAAKC,KAAW;AAE9BC,aAAOC,SAASF,KAAK;QAAEG,MAAM;OAAY,EAAEC,KAAKC,QAAQC,GAAG;;ACJtDC,QAAMC,4BAAyB;ACoK/BD,QAAME,kBAAgC,OAAOC,WAAW,cAAeA,OAAOC,aAAaD,OAAOC,WAAWD,OAAO,iBAAiB,KAAM;AA6D3IH,QAAMK,uBAAqC,OAAOF,WAAW,cAAeA,OAAOG,kBAAkBH,OAAOG,gBAAgBH,OAAO,sBAAsB,KAAM;AAiV/J,aAASI,OAAOC,MAAMC,SAAS;AACrC,UAAI;AACH,YAAIC,SAASF,KAAI;eACVG,GAAG;AACV,eAAOF,QAAQE,CAAC;;AAEjB,UAAID,UAAUA,OAAOb,MAAM;AAC1B,eAAOa,OAAOb,KAAK,QAAQY,OAAO;;AAEnC,aAAOC;;AC3jBDV,QAAMY,6BAA0B;ACAhCZ,QAAMa,4BAA4B;AAElCb,QAAMc,qBAAqB;AAC3Bd,QAAMe,uBAAuB;AAE7Bf,QAAMgB,2BAA2B;AACjChB,QAAMiB,yBAAyB;AAC/BjB,QAAMkB,yBAAyB;AAC/BlB,QAAMmB,gCAAgC;AACtCnB,QAAMoB,4BAA4B;AAClCpB,QAAMqB,kCAAkC;ACA/C,aAASC,OAAOC,OAAkB;AAChC,aACEC,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;SACbD,MAAAA,cAAAA,OAAAA;QAAKE,KAAKd;QAA4Ba,WAAU;UAChDD,MAAAA,cAAAA,KAAAA,MAAIX,yBAAyB,GAC7BW,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;QAAsCE,SAASJ,MAAMK;SAClEJ,MAAAA,cAAAA,OAAAA;QAAKK,IAAIV;QAA+BM,WAAU;SAChDD,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;UACfD,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;QAA0C,CACrD,CACF;;ACTZ,aAASK,cAAcP,OAAyB;AAC9C,aACEC,MAAAA,cAAAA,KAAAA;QACEC,WAAU;QACVM,MAAMR,MAAMQ;QACZF,IAAOR,kCAAAA,MAAmCE,MAAMS;QAChDL,SAASJ,MAAMI;QACfM,KAAI;QACJC,OAAO;UAAEC,iBAAiBZ,MAAMa;;QAChCC,QAAO;SAENd,MAAMS,IAAI;;ACtBVhC,QAAMsC,gBAAa;ACW1B,aAASC,aAAahB,OAAwB;AACpCa,UAAAA,QAAAA,MAAAA;AAAOL,UAAAA,OAAAA,MAAAA;AAAMC,UAAAA,OAAAA,MAAAA;AAAMQ,UAAAA,OAAAA,MAAAA;AAAMb,UAAAA,UAAAA,MAAAA;AACjC,aACEH,MAAAA,cAAAA,KAAAA;QACEC,WAAU;QACVM;QACAJ;QACAM,KAAI;QACJI,QAAO;SAEPb,MAAAA,cAAAA,MAAAA;QAAIC,WAAW;SAAuCO,IAAI,GAC1DR,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;SACbD,MAAAA,cAAAA,OAAAA;QACEC,WAAS;QACTS,OAAO;UAAEO,YAAU,UAAUD,OAAAA,QAAUJ;UAASM,gBAAgB;;UAElElB,MAAAA,cAAAA,OAAAA;QAAKE,KAAKY;QAAeb,WAAU;QAAiD,CAChF;;AClBZ,aAASkB,WAAWpB,OAAsB;AAChCa,UAAAA,QAAAA,MAAAA;AAAOL,UAAAA,OAAAA,MAAAA;AAAMC,UAAAA,OAAAA,MAAAA;AAAMQ,UAAAA,OAAAA,MAAAA;AAAMb,UAAAA,UAAAA,MAAAA;AACjC3B,UAAM4C,WAAWC,OAAOC,aAAa,OAASd,KAAKe,SAAS,IAAI,MAAM,OAAA,OAAU;AAChF,aACEvB,MAAAA,cAAAA,KAAAA;QACEC,WAAU;QACVM;QACAJ;QACAM,KAAI;QACJI,QAAO;SAEPb,MAAAA,cAAAA,OAAAA;QACEC,WAAU;QACVS,OAAO;UAAEO,YAAU,UAAUD,OAAAA,QAAUJ;UAASM,gBAAgB;;UAElElB,MAAAA,cAAAA,OAAAA;QAAKU,OAAO;;;QAAcT,WAAW;SAClCO,IAAI,CACD;;ACJZhC,QAAMgD,iBAAiB;AACvBhD,QAAMiD,iBAAiB;AAEvB,aAASC,YAAY3B,OAAuB;AAC1CvB,UAAMmD,UAAUC,YAAAA,UAAS;gBACC5B,MAAAA,SAAe,EAAE;AAApC6B,UAAAA,QAAAA,IAAAA,CAAAA;AAAOC,UAAAA,WAAAA,IAAAA,CAAAA;kBACc9B,MAAAA,SAAe,EAAE;AAAtC+B,UAAAA,SAAAA,MAAAA,CAAAA;AAAQC,UAAAA,YAAAA,MAAAA,CAAAA;kBACShC,MAAAA,SAAe,CAAC;AAAjCiC,UAAAA,OAAAA,MAAAA,CAAAA;AAAMC,UAAAA,UAAAA,MAAAA,CAAAA;AACb1D,UAAM2D,QAAQJ,SACVhC,MAAMoC,MAAMJ,OAAM,SAACK,MAAAA;AAAAA,eAAQA,KAAK5B,KAAK6B,YAAW,EAAGC,SAASP,OAAOM,YAAW,CAAE;MAAA,CAAC,IACjFtC,MAAMoC;AACV3D,UAAM+D,eAAexC,MAAMwC;AAC3B/D,UAAMgE,OAAOT,UAAUI,MAAMZ,SAASC;AACtChD,UAAMiE,QAAQC,KAAKC,KAAKR,MAAMZ,SAASE,cAAc;AACrDjD,UAAMoE,QAAQ,EAAEX,OAAO,KAAKR,iBAAiB,GAAGQ,OAAOR,cAAc;AACrEjD,UAAMqE,YAAYV,MAAMZ,SACpBY,MAAMJ,OAAM,SAAEe,IAAGC,QAAK;AAAA,eAAKA,SAAQ,KAAKH,MAAM,CAAC,KAAKG,SAAQ,KAAKH,MAAM,CAAC;MAAA,CAAC,IACzE,CAAA;AACJpE,UAAMwE,YAAY,CAAC,EAAE,CAACrB,WAAWc,QAAQ;AACzCQ,UAAIC,gBAAqBC;AACzB,eAASC,YAAYjE,GAAC;AACpB2C,iBAAS3C,EAAE0B,OAAOwC,KAAK;AACvBC,qBAAaJ,aAAa;AAC1B,YAAI/D,EAAE0B,OAAOwC,OAAO;AAClBH,0BAAgBK,WAAU,WAAA;AACxBvB,sBAAU7C,EAAE0B,OAAOwC,KAAK;AACxBnB,oBAAQ,CAAC;aACR,GAAI;eACF;AACLJ,mBAAS,EAAE;AACXE,oBAAU,EAAE;AACZE,kBAAQ,CAAC;;;AAIb,aACElC,MAAAA,cAAAA,OAAAA,MACEA,MAAAA,cAAAA,KAAAA;QAAGK,IAAIT;QAA2BK,WAAU;SACzC0B,UAAU5B,MAAMyD,KAAKC,wBAAwB1D,MAAMyD,KAAKE,uBAAuB,GAEjF,CAAC/B,WACA3B,MAAAA,cAAAA,SAAAA;QACEC,WAAS;QACT0D,aAAY;QACZN,OAAOxB;QACP+B,UAAUR;UAGdpD,MAAAA,cAAAA,OAAAA;QACEC,WAAS,6CACP0B,UAAU,cAAca,QAAQL,MAAMZ,SAAS,WAAW;SAG3D,CAACI,UACAkB,UAAUtB,SACRsB,UAAUgB,IAAG,SAAEC,OAAAA;AACLlD,YAAAA,QAAAA,MAAAA;AAAOJ,YAAAA,OAAAA,MAAAA;AAAMuD,YAAAA,YAAAA,MAAAA;AAAW/C,YAAAA,OAAAA,MAAAA;AAChCxC,YAAM+B,OAAOyD,YAAAA,gBAAgBjE,MAAM9B,KAAK6F,KAAK;AAC7CtF,YAAMyF,iBAAiBjE,MAAAA,YAAiB,WAAA;AACtCkE,sBAAAA,mBAAmB;;;WAGlB;WACA,CAACrB,SAAS,CAAC;AACd,eAAO,CAACL,OACNxC,MAAAA,cAACe,cAAY;UACXH;UACAL;UACAC;UACAQ;UACAb,SAAS8D;aAGXjE,MAAAA,cAACmB,YAAU;UACTP;UACAL;UACAC,MAAMuD,aAAavD;UACnBQ;UACAb,SAAS8D;;OAGd,IAEDjE,MAAAA,cAAAA,MAAAA,UAAAA,MACEA,MAAAA,cAAAA,KAAAA,MACGuC,aAAahB,SACVxB,MAAMwC,eACN,CAAC,CAACxC,MAAMoC,MAAMZ,UAAU,CAACY,MAAMZ,SAC/BxB,MAAMyD,KAAKW,mBACXpE,MAAMyD,KAAKY,OAAO,CACpB,IAIRpE,MAAAA,cAACM,eAAa;QACZE,MAAMT,MAAMyD,KAAKa;QACjBzD,OAAOrB;QACPgB,MAAMR,MAAM9B;QACZkC,SAASH,MAAAA,YAAiB,WAAA;AACxBkE,sBAAAA,mBAAmB;YACjB1D,MAAM;YACND,MAAMR,MAAM9B;WACb;WACA,CAAA,CAAE;QAER,GAEF+E,aACChD,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;SACZqE,MAAM7B,KAAK,EACT8B,KAAK,CAAC,EACNV,IAAG,SAAEf,IAAGC,QAAK;AACZvE,YAAMgG,aAAazB,SAAQ;AAC3BvE,YAAMiG,WAAWxC,SAASuC;AAC1B,eACExE,MAAAA,cAAAA,KAAAA;UACEU,OAAO;YAAEgE,QAAQ;YAAaC,YAAYF,WAAW,SAAS;;UAC9DtE,SAAO,WAAA;AAAA,mBAAQ+B,QAAQsC,UAAU;UAAA;WAEhCA,UAAU;OAGhB,CAAC,CAEP;;AC5IP,aAASI,aAAa7E,OAAwB;AAC5CvB,UAAMqG,OAAO,CAAC,CAAC9E,MAAM+E,QAAQC,KAAI;AACjC,aACE/E,MAAAA,cAAAA,OAAAA;QAAKC,WAAS,wCAAuC4E,OAAO,wBAAwB;SACjF9E,MAAM+E,OAAO;;ACHqCtG,QAG1CwG,oBAAiB,SAACC,MAAY;AAAA,UAAA;AAC3ChC,YAAI/D,SAAS;AAAG,eAAA,QAAA,QACShB,OAAOC,SAAS8G,MAAM;UAAEP,QAAQ;UAAGtG,MAAM;SAAO,CAAC,EAAA,KAAA,SAApE8G,YAAU;AAChB,cAAI,OAAOA,eAAe,UAAU;AAClChG,qBAASgG,WAAWC,QAAQ,QAAM,0CAA4C;;AAEhF,iBAAOjG;;eACR,GAAA;AAAA,eAAA,QAAA,OAAA,CAAA;;;AAOD,aAASkG,cAAcrF,OAAyB;gBACNC,MAAAA,SAAe,EAAE;AAAlDqF,UAAAA,eAAAA,IAAAA,CAAAA;AAAcC,UAAAA,kBAAAA,IAAAA,CAAAA;kBACCtF,MAAAA,SAAe,EAAE;AAAhCuF,UAAAA,MAAAA,MAAAA,CAAAA;AAAKC,UAAAA,SAAAA,MAAAA,CAAAA;AAEZxF,YAAAA,UAAe,WAAA;;iCAEEgF,kBAAkBjF,MAAM9B,GAAG,CAAC,EAAA,KAAA,SAAA,oBAAA;AAAzCuH,mBAAM,kBAAA;;iBACP,GAAA;AAAA,kBAAA,OAAA,CAAA;;SACA,CAAA,CAAE;AAELhH,UAAMiH,kBAAe,WAAA;AACnBjH,YAAMkH,UAAUC,KAAK5F,MAAM9B,GAAG;AAC9B,YAAIyH,SAAS;AACXJ,0BAAgBvF,MAAMyD,KAAKoC,mBAAmB;AAC9CC,sBAAW,WAAA;AAAA,mBAAOP,gBAAgB,EAAE;UAAA,GAAG,IAAI;eACtC;AACLA,0BAAgB,OAAO;AACvBO,sBAAW,WAAA;AAAA,mBAAOP,gBAAgB,EAAE;UAAA,GAAG,IAAI;;;AAI/C,aACEtF,MAAAA,cAAAA,OAAAA,MACEA,MAAAA,cAAAA,KAAAA;QAAGK,IAAIT;QAA2BK,WAAU;SACzCF,MAAMyD,KAAKsC,uBAAuB,GAErC9F,MAAAA,cAAAA,OAAAA;QAAK+F,yBAAyB;UAAEC,QAAQT;;UACxCvF,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;SACbD,MAAAA,cAAAA,KAAAA;QAAGG,SAASsF;SAAkB1F,MAAMyD,KAAKyC,iBAAiB,CAAK,GAEjEjG,MAAAA,cAAC4E,cAAY;QAACE,SAASO;QAAgB;;ACrB7C,aAASa,MAAMnG,OAAiB;AAC9BvB,UAAMmD,UAAUC,YAAAA,UAAS;AACzBpD,UAAM2H,SAASC,YAAAA,SAAQ;AAEvB5H,UAAM6H,YAAYF,SACdpG,MAAMuG,sBAAsBvG,MAAMuG,mBAAmBC,cACnDxG,MAAMuG,mBAAmBC,cACzBpD,SACFpD,MAAMuG,sBAAsBvG,MAAMuG,mBAAmBE,eACrDzG,MAAMuG,mBAAmBE,eACzBrD;gBAC0BnD,MAAAA,SAAe,KAAK;AAA3CoE,UAAAA,UAAAA,IAAAA,CAAAA;AAASqC,UAAAA,aAAAA,IAAAA,CAAAA;kBACczG,MAAAA,SAAe,KAAK;AAA3C0G,UAAAA,UAAAA,MAAAA,CAAAA;AAASC,UAAAA,aAAAA,MAAAA,CAAAA;kBAC0B3G,MAAAA,SAAe,CAACmG,MAAM;AAAzDS,UAAAA,gBAAAA,MAAAA,CAAAA;AAAeC,UAAAA,mBAAAA,MAAAA,CAAAA;AACtBrI,UAAMsI,eAAe;;QAEnBtD,MAAMzD,MAAMyD;QACZvF,KAAK8B,MAAM9B;QACXqI,oBAAoBvG,MAAMuG;;kBAGgBtG,MAAAA,SAAe,EAAE;AAAtD+G,UAAAA,iBAAAA,MAAAA,CAAAA;AAAgBC,UAAAA,oBAAAA,MAAAA,CAAAA;kBACmBhH,MAAAA,SAAe,KAAK;AAAvDiH,UAAAA,gBAAAA,MAAAA,CAAAA;AAAeC,UAAAA,mBAAAA,MAAAA,CAAAA;kBACIlH,MAAAA,SAAuC,CAAA,CAAE;AAA5DmC,UAAAA,QAAAA,MAAAA,CAAAA;AAAOgF,UAAAA,WAAAA,MAAAA,CAAAA;kBAC0BnH,MAAAA,SAAe,EAAE;AAAlDuC,UAAAA,eAAAA,MAAAA,CAAAA;AAAc6E,UAAAA,kBAAAA,MAAAA,CAAAA;AAErB5I,UAAM6I,mBAAgB,WAAA;AACpB,YAAIX,WAAWtC,WAAYiC,aAAa,CAACA,UAAU9E,UAAWY,MAAMZ,SAAS,GAAG;AAC9E;;AAGFvB,cAAAA,UAAe,WAAA;AACbxB,cAAM8I,YAAS,WAAA;AAAA,gBAAA;AACb,kBAAI3F,SAAO;AAAE,uBAAA,QAAA,QAAA;cAAA;AACb8E,yBAAW,IAAI;AAAEjI,kBAAAA,QAAAA,OAAAA,WACb;AACFA,oBAAM+I,MACJxH,MAAMuG,sBAAsBvG,MAAMuG,mBAAmBkB,cACjDzH,MAAMuG,mBAAmBkB,cACzBC,YAAAA,qBAAoB;AAAG,uBAAA,QAAA,QACEC,MAAMH,GAAG,CAAC,EAAA,KAAA,SAAnCI,kBAAgB;AAAA,yBAAA,QAAA,QACEA,iBAAiBC,KAAI,CAAE,EAAA,KAAA,SAAA,uBAAA;AAA/CpJ,wBAAMqJ,WAAW,sBAAgCC;AACjDtJ,wBAAMuJ,WAAW5B,SAAS,WAAW;AACrC3H,wBAAMwJ,SAASC,YAAAA,sBAAsBC,YAAAA,qBAAqBL,UAAUE,QAAQ,GAAG1B,SAAS;AACxFI,+BAAW,KAAK;AAChBE,+BAAW,IAAI;AACfS,oCAAgB,CAACY,OAAOzG,SAASxB,MAAMyD,KAAK2E,uBAAuB,EAAE;AACrEhB,6BAASa,MAAM;AACfxJ,wBAAMyI,iBAAgBe,OAAOzG,WAAW;AACxC,wBAAI0F,gBAAe;AACjBD,wCAAkBhD,YAAAA,gBAAgBjE,MAAM9B,KAAK+J,OAAO,CAAC,CAAC,CAAC;AACvDnB,uCAAiB,IAAI;;AAEvBK,qCAAiBD,cAAa;;;iBAC/B,SAAQ9H,GAAG;AACVsH,2BAAW,KAAK;AAChBE,2BAAW,IAAI;AACfS,gCAAgBrH,MAAMyD,KAAK4E,oBAAoB;AAC/C9J,wBAAQ+J,MAAMlJ,CAAC;eAChB;AAAA,qBAAA,QAAA,QAAA,SAAA,MAAA,OAAA,MAAA,KAAA,WAAA;cAAA,CAAA,IAAA,MAAA;qBACF,GAAA;AAAA,qBAAA,QAAA,OAAA,CAAA;;;AACDmI,oBAAS;SACV;;AAGHD,uBAAgB;AAEhB7I,UAAM8J,gBAAgBnC,SAASS,gBAAgB,CAACA;AAChD,aACE5G,MAAAA,cAAAA,OAAAA;QAAKK,IAAIX;QAAwBO,WAAU;SACzCD,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;SACbD,MAAAA,cAACF,QAAM;QAACM,SAASL,MAAMK;UACtB6G,iBAAiBL,gBAChB5G,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;SACbD,MAAAA,cAAAA,KAAAA;QACEG,SAAO,WAAA;AAAA,iBAAQ+D,YAAAA,mBAAmB;YAAE1D,MAAM2B,MAAM,CAAC,EAAE3B;YAAMD,MAAMwG;WAAgB;QAAA;QAC/ExG,MAAMwG;QACNtG,KAAI;QACJI,QAAO;SAENd,MAAMyD,KAAK+E,eAAe,OAAOtB,gBAAgB9E,MAAM,CAAC,EAAE3B,OAAO,MAAM,IAAI,CAC1E,IAEJmB,WAAWyC,WAAY,CAACA,WAAWjC,MAAMZ,SAC3CvB,MAAAA,cAAAA,OAAAA;QACEC,WAAS,yCACPqI,gBAAgB,qBAAqB;SAGvCtI,MAAAA,cAAAA,OAAAA;QAAKC,WAAU;UACdkG,SACCnG,MAAAA,cAAAA,MAAAA,UAAAA,MACEA,MAAAA,cAAAA,KAAAA;QAAGG,SAAO,WAAA;AAAA,iBAAS0G,iBAAiB,KAAK,GAAGQ,iBAAgB;QAAE;SAC3DtH,MAAMyD,KAAK2C,MAAM,GAEpBnG,MAAAA,cAAAA,KAAAA;QAAGG,SAAO,WAAA;AAAA,iBAAQ0G,iBAAiB,IAAI;QAAA;SAAI9G,MAAMyD,KAAKgF,MAAM,CAAK,IAGnExI,MAAAA,cAAAA,MAAAA,UAAAA,MACEA,MAAAA,cAAAA,KAAAA;QAAGG,SAAO,WAAA;AAAA,iBAAQ0G,iBAAiB,IAAI;QAAA;SAAI9G,MAAMyD,KAAKgF,MAAM,GAC5DxI,MAAAA,cAAAA,KAAAA;QAAGG,SAAO,WAAA;AAAA,iBAAS0G,iBAAiB,KAAK,GAAGQ,iBAAgB;QAAE;SAC3DtH,MAAMyD,KAAKiF,OAAO,CACjB,CAEP,IAED,MAEJzI,MAAAA,cAAAA,OAAAA,MACG4G,iBAAkB,CAACjF,WAAW,CAACyC,WAAW,CAACjC,MAAMZ,SAChDvB,MAAAA,cAACoF,eAAa,OAAA,OAAA,CAAA,GAAK0B,YAAY,CAAA,IAE/B9G,MAAAA,cAAC0B,aAAW,OAAA,OAAA,CAAA,GAAKoF,cAAY;QAAE3E;QAAcI;SAC9C,CACG,CACF;;ACnJZ/D,QAAMkK,KAAc;MAClBhF,yBAAyB;MACzBD,uBAAuB;MACvBqC,yBAAyB;MACzBzB,SAAS;MACTmE,QAAQ;MACRrC,QAAQ;MACRsC,SAAS;MACTxC,mBAAmB;MACnBL,qBAAqB;MACrB2C,cAAc;MACdnE,SAAS;MACTgE,sBAAsB;MACtBD,sBAAsB;MACtBhE,kBAAkB;;ACdpB3F,QAAMmK,MAAc;MAClBjF,yBAAyB;MACzBD,uBAAuB;MACvBqC,yBAAyB;MACzBzB,SAAS;MACTmE,QAAQ;MACRrC,QAAQ;MACRsC,SAAS;MACTxC,mBAAmB;MACnBL,qBAAqB;MACrB2C,cAAc;MACdnE,SAAS;MACTgE,sBAAsB;MACtBD,sBAAsB;MACtBhE,kBAAkB;;ACdpB3F,QAAMoK,KAAc;MAClBlF,yBAAyB;MACzBD,uBAAuB;MACvBqC,yBAAyB;MACzBzB,SAAS;MACTmE,QAAQ;MACRrC,QAAQ;MACRsC,SAAS;MACTxC,mBAAmB;MACnBL,qBAAqB;MACrB2C,cAAc;MACdnE,SAAS;MACTgE,sBAAsB;MACtBD,sBAAsB;MACtBhE,kBAAkB;;ACdpB3F,QAAMqK,KAAc;MAClBnF,yBAAyB;MACzBD,uBAAuB;MACvBqC,yBAAyB;MACzBzB,SAAS;MACTmE,QAAQ;MACRrC,QAAQ;MACRsC,SAAS;MACTxC,mBAAmB;MACnBL,qBAAqB;MACrB2C,cAAc;MACdnE,SAAS;MACTgE,sBAAsB;MACtBD,sBAAsB;MACtBhE,kBAAkB;;ACdpB3F,QAAMsK,KAAc;MAClBpF,yBAAyB;MACzBD,uBAAuB;MACvBqC,yBAAyB;MACzBzB,SAAS;MACTmE,QAAQ;MACRrC,QAAQ;MACRsC,SAAS;MACTxC,mBAAmB;MACnBL,qBAAqB;MACrB2C,cAAc;MACdnE,SAAS;MACTgE,sBAAsB;MACtBD,sBAAsB;MACtBhE,kBAAkB;;ACdpB3F,QAAMuK,KAAc;MAClBrF,yBAAyB;MACzBD,uBAAuB;MACvBqC,yBAAyB;MACzBzB,SAAS;MACTmE,QAAQ;MACRrC,QAAQ;MACRsC,SAAS;MACTxC,mBAAmB;MACnBL,qBAAqB;MACrB2C,cAAc;MACdnE,SAAS;MACTgE,sBAAsB;MACtBD,sBAAsB;MACtBhE,kBAAkB;;ACdpB3F,QAAMwK,KAAc;MAClBtF,yBAAyB;MACzBD,uBAAuB;MACvBqC,yBAAyB;MACzBzB,SAAS;MACTmE,QAAQ;MACRrC,QAAQ;MACRsC,SAAS;MACTxC,mBAAmB;MACnBL,qBAAqB;MACrB2C,cAAc;MACdnE,SAAS;MACTgE,sBAAsB;MACtBD,sBAAsB;MACtBhE,kBAAkB;;ACdpB3F,QAAMyK,KAAc;MAClBvF,yBAAyB;MACzBD,uBAAuB;MACvBqC,yBAAyB;MACzBzB,SAAS;MACTmE,QAAQ;MACRrC,QAAQ;MACRsC,SAAS;MACTxC,mBAAmB;MACnBL,qBAAqB;MACrB2C,cAAc;MACdnE,SAAS;MACTgE,sBAAsB;MACtBD,sBAAsB;MACtBhE,kBAAkB;;ACLpB3F,QAAM0K,YAAyC;;UAAMP;;;;;;;;ACQrD,aAASQ,mBAAgB;AACvB3K,UAAM4K,MAAMC,YAAAA,mBAAkB;AAC9B7K,UAAM8K,OAAOF,IAAIG,eAAe9J,sBAAsB;AACtD,UAAI6J,MAAM;AACRF,YAAII,KAAKC,YAAYH,IAAI;;AAE3B9K,UAAMkC,QAAQ0I,IAAIM,cAAc,OAAO;AACvChJ,YAAMiJ,aAAa,MAAMlK,sBAAsB;AAC/CiB,YAAMkJ,YAAYnL;AAClB2K,UAAII,KAAKK,YAAYnJ,KAAK;;AAG5B,aAASoJ,gBAAa;AACpBtL,UAAM4K,MAAMC,YAAAA,mBAAkB;AAC9B7K,UAAMuL,UAAUX,IAAIM,cAAc,KAAK;AACvCK,cAAQJ,aAAa,MAAMnK,wBAAwB;AACnD4J,UAAIpK,KAAK6K,YAAYE,OAAO;AAC5B,aAAOA;;AAGT,aAASC,wBAAqB;AAC5BxL,UAAM4K,MAAMC,YAAAA,mBAAkB;AAC9B7K,UAAMyL,QAAQb,IAAIG,eAAe7J,sBAAsB;AACvD,UAAIuK,OAAO;AACTA,cAAMhK,YAAYgK,MAAMhK,UAAUkF,QAAQ,UAAU,SAAS;AAC7D5B,mBAAU,WAAA;AACR/E,cAAMuL,UAAUX,IAAIG,eAAe/J,wBAAwB;AAC3D,cAAIuK,SAAS;AACXX,gBAAIpK,KAAKyK,YAAYM,OAAO;;WAE7BzK,kBAAkB;;;AAIzB,aAAS4K,mBAAmBC,IAAO;AACjC,aAAA,WAAA;AACEH,8BAAqB;AACrB,YAAIG,IAAI;AACNA,aAAE;;;;AAKR,aAASC,UAAO;AACd5L,UAAM6L,OAAOC,YAAAA,oBAAmB,EAAGC,SAASC,MAAM,GAAG,EAAE,CAAC,KAAK;AAC7D,aAAOC,UAAUJ,IAAI,KAAKI,UAAU,IAAI;;AAG1C,aAAgBzM,OAAKC,KAAakM,IAAS7D,oBAAwC;AACjF6C,uBAAgB;AAChB3K,UAAMuL,UAAUD,cAAa;AAC7BY,YAAAA,OACE1K,MAAAA,cAACkG,OAAK;QACJ1C,MAAM4G,QAAO;QACbnM;QACAmC,SAAS8J,mBAAmBC,EAAE;QAC9B7D;UAEFyD,OAAO;;AAIX,aAAgBY,UAAK;AACnBX,4BAAqB;;AC7EvBxL,QAAMoM,UAAM,WAAA;AAAA,aACV,OAAOC,YAAY,eACnB,OAAOA,QAAQC,aAAa,eAC5B,OAAOD,QAAQC,SAASC,SAAS;IAAA;AAEnC,aAAS/M,OAAKC,KAAakM,IAAS7D,oBAAwC;AAE1EhI,cAAQC,IAAIN,GAAG;AACf,UAAI2M,QAAM,GAAI;AACZI,aAAa/M,GAAG;aACX;AACLgN,eAAgBhN,KAAKkM,IAAI7D,kBAAkB;;;AAI/C,aAASqE,UAAK;AACZ,UAAIC,QAAM;AAAI;WAEP;AACLK,gBAAgB;;;AAIpB,QAAA,QAAe;YAAEjN;aAAM2M;;;;;;;AC5BvB,IAAAO,iBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGzBC;AAMAA;;;ACTA,IAAAC,iBAAyB;;;ACAzBC;AA0CAA;;;ACnCAC;;;ACHA,IAAM,iBAAN,MAAoB;EAGlB,cAAA;AACE,SAAK,iBAAiB,CAAA;AAEtB,QAAI,OAAO,WAAW,eAAe,OAAQ,OAAe,qBAAqB,aAAa;AAC5F,aAAO,iBAAiB,UAAU,MAAM,KAAK,QAAQ,QAAQ,CAAC;AAC9D,aAAO,iBAAiB,WAAW,MAAM,KAAK,QAAQ,SAAS,CAAC;;EAEpE;EAEO,GAAG,OAAqB,UAAoB;AACjD,SAAK,eAAe,KAAK;MACvB;MACA;KACD;EACH;EAEO,QAAQ,OAAmB;AAChC,QAAI,gBAAwC,CAAA;AAE5C,QAAI,OAAO;AACT,sBAAgB,KAAK,eAAe,OAClC,CAAC,iBAAuC,aAAa,UAAU,KAAK;;AAIxE,kBAAc,QAAQ,CAAC,iBAAsC;AAC3D,mBAAa,SAAQ;IACvB,CAAC;EACH;;AAGF,IAAA,kBAAe;;;ADpBf,IAAM,KAAK,OAAO,OAAO,cAAc,cAAc,OAAO,YAAY;AAIxE,IAAM,kBAAN,MAAqB;EAanB,YAAoB,MAA6B;AAA7B,SAAA,OAAA;AANZ,SAAA,SAA2B,CAAA;AAC3B,SAAA,UAA6B,CAAA;AAC7B,SAAA,iBAA2B,CAAA;AAKjC,SAAK,YAAY,KAAK;AACtB,SAAK,WAAW,KAAK;AACrB,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,iBAAiB,KAAK,iBAAiB,CAAA;AAC5C,SAAK,cAAc,KAAK,cAAc,IAAI,gBAAc;AAExD,QAAI,CAAC,KAAK,OAAO,OAAO,KAAK,QAAQ,UAAU;AAC7C,YAAM,IAAI,MAAM,kCAAkC;;AAGpD,SAAK,OAAO,KAAK;AAEjB,SAAK,YAAY,GAAG,UAAU,MAAM,KAAK,cAAa,CAAE;EAC1D;EAEA,IAAI,WAAW,OAAK;EAEpB;EAEA,IAAI,aAAU;AACZ,WAAO,KAAK,UAAU,KAAK,QAAQ,aAAa;EAClD;EAEA,IAAI,WAAW,OAAK;EAEpB;EAEA,IAAI,aAAU;AACZ,WAAO,KAAK,eAAe;EAC7B;EAEA,IAAI,UAAU,OAAK;EAEnB;EAEA,IAAI,YAAS;AACX,WAAO,KAAK,eAAe;EAC7B;EAEA,IAAI,QAAQ,OAAK;EAEjB;EAEA,IAAI,UAAO;AACT,WAAO,KAAK,eAAe;EAC7B;EAEA,IAAI,OAAO,OAAK;EAEhB;EAEA,IAAI,SAAM;AACR,WAAO,KAAK,eAAe;EAC7B;EAIO,OAAI;AACT,SAAK,cAAa;EACpB;EAEO,QAAK;AACV,SAAK,aAAY;EACnB;EAEO,KAAK,SAAiB,OAAgB,QAAgB;AAC3D,QAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,YAAM,IAAI,MAAM,gCAAgC;;AAGlD,SAAK,YAAY;MACf;MACA,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,CAAC;KACX;EACH;EAEO,UAAU,OAAa;AAC5B,SAAK,YAAY;MACf;MACA,MAAM;MACN,SAAS;MACT,QAAQ;KACT;EACH;EAEO,GAAG,OAAe,UAAgC;AACvD,SAAK,QAAQ,KAAK,EAAE,OAAO,SAAQ,CAAE;EACvC;EAIQ,gBAAa;AACnB,QAAI,KAAK,aAAa;AACpB;;AAGF,UAAM,MAAM,gBAAgB,KAAK,MAAM,KAAK,WAAW,KAAK,QAAQ;AAEpE,SAAK,cAAc,IAAI,GAAG,GAAG;AAE7B,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,IAAI,MAAM,yBAAyB;;AAG3C,SAAK,YAAY,YAAY,CAAC,UAAwB,KAAK,eAAe,KAAK;AAE/E,SAAK,YAAY,SAAS,MAAM,KAAK,YAAW;AAEhD,SAAK,YAAY,UAAU,CAAC,UAAiB,KAAK,aAAa,KAAK;AAEpE,SAAK,YAAY,UAAU,MAAK;AAC9B,iBAAW,MAAK;AACd,aAAK,cAAc;AACnB,aAAK,cAAa;MACpB,GAAG,GAAI;IACT;EACF;EAEQ,cAAW;AACjB,SAAK,aAAY;AACjB,SAAK,UAAU,KAAK;AACpB,SAAK,cAAc;AACnB,SAAK,oBAAmB;AACxB,SAAK,WAAU;EACjB;EAEQ,eAAY;AAClB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,UAAU,MAAK;MAE5B;AACA,WAAK,QAAQ,MAAK;;EAEtB;EAEQ,YAAY,eAA6B;AAC/C,UAAM,UAAkB,KAAK,UAAU,aAAa;AAEpD,QAAI,KAAK,WAAW,KAAK,QAAQ,eAAe,GAAG;AACjD,WAAK,QAAQ,KAAK,OAAO;WACpB;AACL,WAAK,YAAY,aAAa;AAC9B,WAAK,cAAa;;EAEtB;EAEQ,MAAM,eAAe,OAAmB;AAC9C,QAAI;AAEJ,QAAI;AACF,sBAAgB,KAAK,MAAM,MAAM,IAAI;aAC9B,OAAO;AACd;;AAGF,SAAK,YAAY;MACf,OAAO,cAAc;MACrB,MAAM;MACN,SAAS;MACT,QAAQ;KACT;AAED,QAAI,KAAK,WAAW,KAAK,QAAQ,eAAe,GAAG;AACjD,YAAM,SAAS,KAAK,QAAQ,OAAO,CAAAC,WAASA,OAAM,UAAU,SAAS;AACrE,UAAI,UAAU,OAAO,QAAQ;AAC3B,eAAO,QAAQ,CAAAA,WAASA,OAAM,SAAS,aAAa,CAAC;;;EAG3D;EAEQ,aAAa,GAAQ;AAC3B,UAAM,SAAS,KAAK,QAAQ,OAAO,WAAS,MAAM,UAAU,OAAO;AACnE,QAAI,UAAU,OAAO,QAAQ;AAC3B,aAAO,QAAQ,WAAS,MAAM,SAAS,CAAC,CAAC;;EAE7C;EAEQ,sBAAmB;AACzB,UAAM,gBAAgB,KAAK;AAE3B,kBAAc,QAAQ,CAAC,UACrB,KAAK,OAAO,KAAK;MACf;MACA,MAAM;MACN,SAAS;MACT,QAAQ;KACT,CAAC;AAGJ,SAAK,iBAAiB,KAAK,KAAK,iBAAiB,CAAA;EACnD;EAEQ,YAAY,eAA6B;AAC/C,SAAK,OAAO,KAAK,aAAa;EAChC;EAEQ,aAAU;AAChB,UAAM,QAAQ,KAAK;AAEnB,UAAM,QAAQ,CAAC,kBAAkC,KAAK,YAAY,aAAa,CAAC;AAEhF,SAAK,SAAS,CAAA;EAChB;;AAGF,SAAS,gBAAgB,MAAc,UAAkB,SAAe;;AACtE,QAAM,MAAM,KAAK,WAAW,OAAO,IAC/B,KAAK,QAAQ,SAAS,KAAK,IAC3B,KAAK,WAAW,MAAM,IACtB,KAAK,QAAQ,QAAQ,IAAI,IACzB;AACJ,QAAM,WAAW,IAAI,MAAM,GAAG;AAC9B,QAAM,SAAS,UAAS,IACpB;IACE;IACA;IACA,KAAK;IACL,QAAM,KAAAC,aAAW,OAAE,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ;MAE/B;IACE;IACA;IACA,OAAK,KAAA,UAAS,OAAE,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ;;AAEhC,QAAM,cAAc,oBAAoB,eAAe,SAAS,CAAC,KAAK,EAAE,GAAG,MAAM;AACjF,SAAO,SAAS,CAAC,IAAI,MAAM;AAC7B;AAEA,IAAA,iBAAe;;;AEtQfC;AAUA,IAAM,eAAN,MAAkB;EAGhB,cAAA;AACE,SAAK,iBAAiB,CAAA;EACxB;EAEO,UAAU,cAA2B;AAC1C,SAAK,eAAe,KAAK,YAAY;EACvC;EAEO,YAAY,OAAa;AAC9B,SAAK,iBAAiB,KAAK,eAAe,OAAO,CAAAC,OAAKA,GAAE,UAAU,KAAK;EACzE;EAEO,QACL,SAA2F;AAE3F,QAAI,gBAAiC,CAAA;AACrC,QAAI;AAEJ,QAAI,iBAAiB,OAAO,GAAG;AAC7B,cAAQ,QAAQ;eACP,yBAAyB,OAAO,KAAK,uBAAuB,OAAO,GAAG;AAC/E,cAAQ,YAAY,QAAQ,EAAE;eACrB,gBAAgB,OAAO,GAAG;AACnC,cAAQ,QAAQ;WACX;AACL,cAAQ;;AAGV,QAAI,OAAO;AACT,sBAAgB,KAAK,eAAe,OAClC,CAAC,iBAAgC,aAAa,UAAU,KAAK;;AAIjE,SACG,CAAC,iBAAiB,CAAC,cAAc,WAClC,CAAC,gBAAgB,KAAK,KACtB,CAAC,gBAAgB,KAAK,GACtB;AACA,sBAAgB,KAAK,eAAe,OAClC,CAAC,iBAAgC,aAAa,UAAU,cAAc;;AAI1E,kBAAc,QAAQ,CAAC,iBAA+B;AACpD,UAAI,uBAAuB,OAAO,GAAG;AACnC,cAAM,QAAQ,IAAI,MAAM,QAAQ,MAAM,OAAO;AAC7C,qBAAa,SAAS,OAAO,IAAI;aAC5B;AACL,qBAAa,SAAS,MAAM,OAAO;;IAEvC,CAAC;EACH;;AAGF,IAAA,iBAAe;;;AC1EfC;AAOA,IAAM,iBAAN,MAAoB;EAClB,YAAmB,YAAoB,iBAAe;AAAnC,SAAA,YAAA;EAAsC;EAElD,aAAU;AACf,QAAI,UAAwC;AAC5C,UAAM,OAAO,SAAS,KAAK,SAAS;AACpC,QAAI,QAAQ,uBAAuB,IAAI,GAAG;AACxC,gBAAU;;AAEZ,WAAO;EACT;EAEO,WAAW,SAA8B;AAC9C,aAAS,KAAK,WAAW,OAAO;AAChC,WAAO;EACT;EAEO,gBAAa;AAClB,gBAAY,KAAK,SAAS;EAC5B;;AAGF,IAAA,kBAAe;;;AC9Bf,IAAM,SAAS;AAEf,IAAM,iBAAiB;AAEvB,IAAM,UAAU,eAAe,MAAM,EAAE,EAAE,IAAI,UAAQ,WAAW,IAAI,2BAA2B;AAEzF,SAAU,gBAAgB,KAAW;AAEzC,MAAI,WAAW,IAAI,QAAQ,IAAI,IAAI,KAAK,IAAI,MAAM,GAAG,EAAE,CAAC,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AAE5E,aAAW,SAAS,MAAM,GAAG,EAAE,CAAC;AAEhC,aAAW,SAAS,MAAM,GAAG,EAAE,CAAC;AAChC,SAAO;AACT;AAEM,SAAU,kBAAkB,KAAW;AAC3C,SAAO,gBAAgB,GAAG,EACvB,MAAM,GAAG,EACT,MAAM,EAAE,EACR,KAAK,GAAG;AACb;AAEM,SAAU,oBAAiB;AAC/B,SAAO,KAAK,MAAM,KAAK,OAAM,IAAK,QAAQ,MAAM;AAClD;AAEM,SAAU,wBAAqB;AACnC,SAAO,QAAQ,kBAAiB,CAAE;AACpC;AAEM,SAAU,qBAAqB,KAAW;AAC9C,SAAO,kBAAkB,GAAG,MAAM;AACpC;AAEM,SAAU,aAAa,KAAW;AACtC,MAAI,qBAAqB,GAAG,GAAG;AAC7B,WAAO,sBAAqB;;AAE9B,SAAO;AACT;;;AL0BA,IAAM,YAAN,MAAe;EAkDb,YAAY,MAAoB;AAjDhB,SAAA,WAAW;AACX,SAAA,UAAU;AAIlB,SAAA,UAAU;AACV,SAAA,OAA2B;AAI3B,SAAA,YAAY;AACZ,SAAA,cAAkC;AAIlC,SAAA,UAAU;AACV,SAAA,YAAgC;AAIhC,SAAA,eAAe;AACf,SAAA,kBAAkB;AAIlB,SAAA,aAAa;AACb,SAAA,YAAsB,CAAA;AACtB,SAAA,WAAW;AACX,SAAA,aAAa;AACb,SAAA,UAAU;AAMV,SAAA,gBAA8B,IAAI,eAAY;AAepD,SAAK,cAAc,cAAa,KAAM,KAAK,cAAc,cAAc;AACvE,SAAK,aAAa,KAAK;AACvB,SAAK,kBAAkB,KAAK,kBAAkB,IAAI,gBAAe,KAAK,cAAc,SAAS;AAC7F,SAAK,eAAe,KAAK,cAAc;AACvC,SAAK,sBAAsB,KAAK,cAAc;AAC9C,SAAK,kBAAkB,CAAC,GAAG,iBAAiB,GAAI,KAAK,cAAc,kBAAkB,CAAA,CAAG;AAExF,QAAI,CAAC,KAAK,cAAc,UAAU,CAAC,KAAK,cAAc,OAAO,CAAC,KAAK,cAAc,SAAS;AACxF,YAAM,IAAI,MAAM,sBAAsB;;AAGxC,QAAI,KAAK,cAAc,QAAQ;AAC7B,WAAK,SAAS,aAAa,KAAK,cAAc,MAAM;;AAGtD,QAAI,KAAK,cAAc,KAAK;AAC1B,WAAK,MAAM,KAAK,cAAc;;AAGhC,UAAM,UAAU,KAAK,cAAc,WAAW,KAAK,mBAAkB;AAErE,QAAI,SAAS;AACX,WAAK,UAAU;;AAGjB,QAAI,KAAK,aAAa;AACpB,WAAK,4BAA4B,KAAK,aAAa,0BAA0B;;AAG/E,SAAK,aACH,KAAK,aACL,IAAI,eAAgB;MAClB,UAAU,KAAK;MACf,SAAS,KAAK;MACd,KAAK,KAAK;MACV,eAAe,CAAC,KAAK,QAAQ;KAC9B;AAEH,SAAK,2BAA0B;AAC/B,SAAK,eAAc;AAEnB,QAAI,KAAK,cAAc,KAAK;AAC1B,WAAK,2BAA0B;;AAGjC,QAAI,KAAK,gBAAgB;AACvB,WAAK,oBAAoB,KAAK,cAAc;;EAEhD;EAIA,IAAI,OAAO,OAAa;AACtB,QAAI,CAAC,OAAO;AACV;;AAEF,SAAK,UAAU;EACjB;EAEA,IAAI,SAAM;AACR,WAAO,KAAK;EACd;EAEA,IAAI,IAAI,OAAa;AACnB,QAAI,CAAC,OAAO;AACV;;AAEF,UAAM,MAAmB,wBAAwB,KAAK;AACtD,SAAK,OAAO;EACd;EAEA,IAAI,MAAG;AACL,QAAI,KAAK,MAAM;AACb,YAAM,MAAc,wBAAwB,KAAK,MAAM,IAAI;AAC3D,aAAO;;AAET,WAAO;EACT;EAEA,IAAI,SAAS,OAAa;AACxB,QAAI,CAAC,OAAO;AACV;;AAEF,SAAK,YAAY;EACnB;EAEA,IAAI,WAAQ;AACV,QAAI,WAA0B,KAAK;AACnC,QAAI,CAAC,UAAU;AACb,iBAAW,KAAK,YAAY,KAAI;;AAGlC,WAAO,KAAK;EACd;EAEA,IAAI,OAAO,OAAK;AACd,QAAI,CAAC,OAAO;AACV;;AAEF,SAAK,UAAU;EACjB;EAEA,IAAI,SAAM;AACR,WAAO,KAAK;EACd;EAEA,IAAI,WAAW,OAAK;EAEpB;EAEA,IAAI,aAAU;AACZ,QAAI,aAAiC,KAAK;AAC1C,QAAI,CAAC,YAAY;AACf,mBAAa,KAAK,cAAc,cAAa;;AAE/C,WAAO;EACT;EAEA,IAAI,SAAS,OAAK;AAChB,SAAK,YAAY;EACnB;EAEA,IAAI,WAAQ;AACV,UAAM,WAA+B,KAAK;AAC1C,WAAO;EACT;EAEA,IAAI,eAAe,OAAK;AACtB,QAAI,CAAC,OAAO;AACV;;AAEF,SAAK,kBAAkB;EACzB;EAEA,IAAI,iBAAc;AAChB,WAAO,KAAK;EACd;EAEA,IAAI,YAAY,OAAK;AACnB,QAAI,CAAC,OAAO;AACV;;AAEF,SAAK,eAAe;EACtB;EAEA,IAAI,cAAW;AACb,WAAO,KAAK;EACd;EAEA,IAAI,MAAG;AACL,UAAM,OAAO,KAAK,WAAU;AAC5B,WAAO;EACT;EAEA,IAAI,IAAI,OAAK;AACX,QAAI,CAAC,OAAO;AACV;;AAEF,UAAM,EAAE,gBAAgB,QAAQ,IAAG,IAAK,KAAK,UAAU,KAAK;AAC5D,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,MAAM;EACb;EAEA,IAAI,QAAQ,OAAK;AACf,SAAK,WAAW;EAClB;EAEA,IAAI,UAAO;AACT,UAAM,UAAyB,KAAK;AACpC,WAAO;EACT;EAEA,IAAI,UAAU,OAAK;AACjB,SAAK,aAAa;EACpB;EAEA,IAAI,YAAS;AACX,UAAM,YAA2B,KAAK;AACtC,WAAO;EACT;EAEA,IAAI,SAAS,OAAK;AAChB,SAAK,YAAY;EACnB;EAEA,IAAI,WAAQ;AACV,UAAM,WAA4B,KAAK;AACvC,WAAO;EACT;EAEA,IAAI,OAAO,OAAK;AACd,SAAK,UAAU;EACjB;EAEA,IAAI,SAAM;AACR,UAAM,SAAwB,KAAK;AACnC,WAAO;EACT;EAEA,IAAI,UAAU,OAAK;EAEnB;EAEA,IAAI,YAAS;AACX,WAAO,KAAK;EACd;EAEA,IAAI,QAAQ,OAAK;EAEjB;EAEA,IAAI,UAAO;AACT,WAAO,CAAC,CAAC,KAAK;EAChB;EAEA,IAAI,UAAO;AACT,WAAO;MACL,WAAW,KAAK;MAChB,UAAU,KAAK;MACf,SAAS,KAAK;MACd,QAAQ,KAAK;MACb,KAAK,KAAK;MACV,UAAU,KAAK;MACf,YAAY,KAAK;MACjB,QAAQ,KAAK;MACb,UAAU,KAAK;MACf,aAAa,KAAK;MAClB,gBAAgB,KAAK;;EAEzB;EAEA,IAAI,QAAQ,OAAK;AACf,QAAI,CAAC,OAAO;AACV;;AAEF,SAAK,aAAa,MAAM;AACxB,SAAK,WAAW,MAAM;AACtB,SAAK,UAAU,MAAM;AACrB,SAAK,SAAS,MAAM;AACpB,SAAK,MAAM,MAAM;AACjB,SAAK,WAAW,MAAM;AACtB,SAAK,aAAa,MAAM;AACxB,SAAK,SAAS,MAAM;AACpB,SAAK,WAAW,MAAM;AACtB,SAAK,cAAc,MAAM;AACzB,SAAK,iBAAiB,MAAM;EAC9B;EAIO,GAAG,OAAe,UAA4D;AACnF,UAAM,eAAe;MACnB;MACA;;AAEF,SAAK,cAAc,UAAU,YAAY;EAC3C;EAEO,IAAI,OAAa;AACtB,SAAK,cAAc,YAAY,KAAK;EACtC;EAEO,MAAM,qBAAqB,gBAAwC;AACxE,SAAK,OAAO,MAAM,KAAK,aAAY;AAEnC,UAAM,UAA2B,KAAK,eAAe;MACnD,QAAQ;MACR,QAAQ;QACN;UACE,QAAQ,KAAK;UACb,UAAU,KAAK;UACf,SAAS,KAAK,eAAe,cAAc;;;KAGhD;AAED,SAAK,cAAc,QAAQ;AAC3B,SAAK,iBAAiB,KAAI;AAE1B,SAAK,cAAc,QAAQ;MACzB,OAAO;MACP,QAAQ,CAAC,KAAK,GAAG;KAClB;AAED,SAAK,GAAG,gBAAgB,MAAK;AAC3B,YAAM,IAAI,MAAM,8BAA8B;IAChD,CAAC;AAED,UAAM,oBAAoB,MAAK;AAC7B,WAAK,YAAW;IAClB;AAEA,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,iBAAiB,OAAO;AAElD,UAAI,QAAQ;AACV,0BAAiB;;AAGnB,aAAO;aACA,OAAO;AACd,wBAAiB;AACjB,YAAM;;EAEV;EAEO,MAAM,QAAQ,MAA4B;AAC/C,QAAI,CAAC,KAAK,cAAc;AACtB,YAAM,IAAI,MAAM,+BAA+B;;AAGjD,QAAI,KAAK,WAAW;AAClB,aAAO;QACL,SAAS,KAAK;QACd,UAAU,KAAK;;;AAInB,UAAM,KAAK,cAAc,IAAI;AAE7B,WAAO,IAAI,QAAwB,OAAO,SAAS,WAAU;AAC3D,WAAK,GAAG,gBAAgB,MAAM,OAAO,IAAI,MAAM,8BAA8B,CAAC,CAAC;AAE/E,WAAK,GAAG,WAAW,CAAC,OAAO,YAAW;AACpC,YAAI,OAAO;AACT,iBAAO,OAAO,KAAK;;AAGrB,gBAAQ,QAAQ,OAAO,CAAC,CAAC;MAC3B,CAAC;IACH,CAAC;EACH;EAEO,MAAM,cAAc,MAA4B;AACrD,QAAI,KAAK,YAAY;AACnB,YAAM,IAAI,MAAM,uBAAuB;;AAGzC,QAAI,KAAK,SAAS;AAChB;;AAGF,SAAK,OAAO,MAAM,KAAK,aAAY;AAEnC,UAAM,UAA2B,KAAK,eAAe;MACnD,QAAQ;MACR,QAAQ;QACN;UACE,QAAQ,KAAK;UACb,UAAU,KAAK;UACf,SAAS,QAAQ,KAAK,UAAU,KAAK,UAAU;;;KAGpD;AAED,SAAK,cAAc,QAAQ;AAC3B,SAAK,iBAAiB,KAAI;AAE1B,SAAK,oBAAoB,SAAS,2BAA2B;MAC3D,OAAO,KAAK;KACb;AAED,SAAK,cAAc,QAAQ;MACzB,OAAO;MACP,QAAQ,CAAC,KAAK,GAAG;KAClB;EACH;EAEO,eAAe,eAA6B;AACjD,QAAI,KAAK,YAAY;AACnB,YAAM,IAAI,MAAM,uBAAuB;;AAGzC,SAAK,UAAU,cAAc;AAC7B,SAAK,WAAW,cAAc;AAC9B,SAAK,YAAY,cAAc,aAAa;AAC5C,SAAK,SAAS,cAAc,UAAU;AAEtC,UAAM,gBAAgC;MACpC,UAAU;MACV,SAAS,KAAK;MACd,WAAW,KAAK;MAChB,UAAU,KAAK;MACf,QAAQ,KAAK;MACb,QAAQ,KAAK;MACb,UAAU,KAAK;;AAGjB,UAAM,WAAW;MACf,IAAI,KAAK;MACT,SAAS;MACT,QAAQ;;AAGV,SAAK,cAAc,QAAQ;AAE3B,SAAK,aAAa;AAClB,SAAK,mBAAkB;AACvB,SAAK,cAAc,QAAQ;MACzB,OAAO;MACP,QAAQ;QACN;UACE,QAAQ,KAAK;UACb,UAAU,KAAK;UACf,SAAS,KAAK;UACd,UAAU,KAAK;;;KAGpB;EACH;EAEO,cAAc,cAA4B;AAC/C,QAAI,KAAK,YAAY;AACnB,YAAM,IAAI,MAAM,uBAAuB;;AAGzC,UAAM,UACJ,gBAAgB,aAAa,UAAU,aAAa,UAAU;AAEhE,UAAM,WAAW,KAAK,gBAAgB;MACpC,IAAI,KAAK;MACT,OAAO,EAAE,QAAO;KACjB;AAED,SAAK,cAAc,QAAQ;AAE3B,SAAK,aAAa;AAClB,SAAK,cAAc,QAAQ;MACzB,OAAO;MACP,QAAQ,CAAC,EAAE,QAAO,CAAE;KACrB;AACD,SAAK,sBAAqB;EAC5B;EAEO,cAAc,eAA6B;AAChD,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,0BAA0B;;AAG5C,SAAK,UAAU,cAAc;AAC7B,SAAK,WAAW,cAAc;AAC9B,SAAK,YAAY,cAAc,aAAa;AAC5C,SAAK,SAAS,cAAc,UAAU;AAEtC,UAAM,gBAAgC;MACpC,UAAU;MACV,SAAS,KAAK;MACd,WAAW,KAAK;MAChB,UAAU,KAAK;MACf,QAAQ,KAAK;;AAGf,UAAM,UAAU,KAAK,eAAe;MAClC,QAAQ;MACR,QAAQ,CAAC,aAAa;KACvB;AAED,SAAK,oBAAoB,SAAS,yBAAyB;AAE3D,SAAK,cAAc,QAAQ;MACzB,OAAO;MACP,QAAQ;QACN;UACE,SAAS,KAAK;UACd,UAAU,KAAK;;;KAGpB;AAED,SAAK,sBAAqB;EAC5B;EAEO,MAAM,YAAY,cAA4B;AACnD,UAAM,UAAU,eAAe,aAAa,UAAU;AAEtD,UAAM,gBAAgC;MACpC,UAAU;MACV,SAAS;MACT,WAAW;MACX,UAAU;;AAGZ,UAAM,UAAU,KAAK,eAAe;MAClC,QAAQ;MACR,QAAQ,CAAC,aAAa;KACvB;AAED,UAAM,KAAK,aAAa,OAAO;AAE/B,SAAK,yBAAyB,OAAO;EACvC;EAEO,MAAM,gBAAgB,IAAW;AACtC,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,0BAA0B;;AAI5C,UAAM,WAAW;AAEjB,UAAM,UAAU,KAAK,eAAe;MAClC,QAAQ;MACR,QAAQ,CAAC,QAAQ;KAClB;AAED,UAAM,SAAS,MAAM,KAAK,iBAAiB,OAAO;AAClD,WAAO;EACT;EAEO,MAAM,gBAAgB,IAAW;AACtC,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,0BAA0B;;AAI5C,UAAM,WAAW;AAEjB,UAAM,UAAU,KAAK,eAAe;MAClC,QAAQ;MACR,QAAQ,CAAC,QAAQ;KAClB;AAED,UAAM,SAAS,MAAM,KAAK,iBAAiB,OAAO;AAClD,WAAO;EACT;EAEO,MAAM,YAAY,QAAa;AACpC,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,0BAA0B;;AAG5C,UAAM,UAAU,KAAK,eAAe;MAClC,QAAQ;MACR;KACD;AAED,UAAM,SAAS,MAAM,KAAK,iBAAiB,OAAO;AAClD,WAAO;EACT;EAEO,MAAM,oBAAoB,QAAa;AAC5C,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,0BAA0B;;AAM5C,UAAM,UAAU,KAAK,eAAe;MAClC,QAAQ;MACR;KACD;AAED,UAAM,SAAS,MAAM,KAAK,iBAAiB,OAAO;AAClD,WAAO;EACT;EAEO,MAAM,cAAc,QAAa;AACtC,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,0BAA0B;;AAG5C,UAAM,UAAU,KAAK,eAAe;MAClC,QAAQ;MACR;KACD;AAED,UAAM,SAAS,MAAM,KAAK,iBAAiB,OAAO;AAClD,WAAO;EACT;EAEO,MAAM,YAAY,aAA+B;AACtD,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,gCAAgC;;AAGlD,UAAM,UAAU,KAAK,eAAe;MAClC,QAAQ;MACR,QAAQ,CAAC,WAAW;KACrB;AAED,UAAM,SAAS,MAAM,KAAK,iBAAiB,OAAO;AAClD,WAAO;EACT;EAEO,WACL,SACA,SAAyB;AAEzB,SAAK,aAAa,SAAS,OAAO;AAElC,SAAK,cAAc,QAAQ;MACzB,OAAO;MACP,QAAQ,CAAC,EAAE,SAAS,QAAO,CAAE;KAC9B;AAED,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,WAAK,qBAAqB,QAAQ,IAAI,CAAC,OAAqB,YAAuB;AACjF,YAAI,OAAO;AACT,iBAAO,KAAK;AACZ;;AAEF,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,sBAAsB;;AAExC,gBAAQ,OAAO;MACjB,CAAC;IACH,CAAC;EACH;EAEO,MAAM,kBAAkB,SAAmC,SAAyB;AACzF,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM,0BAA0B;;AAG5C,YAAQ,QAAQ,QAAQ;MACtB,KAAK;AACH,eAAO,KAAK;MACd,KAAK;AACH,eAAO,mBAAmB,KAAK,OAAO;MACxC,KAAK;MACL,KAAK;AACH,YAAI,QAAQ,QAAQ;;AAIpB;MACF,KAAK;AACH,YAAI,QAAQ,QAAQ;;AAIpB;MACF;AACE;;AAGJ,UAAM,mBAAmB,KAAK,eAAe,OAAO;AAEpD,UAAM,SAAS,MAAM,KAAK,iBAAiB,kBAAkB,OAAO;AACpE,WAAO;EACT;EAEO,eAAe,UAA0C;AAC9D,QAAI,yBAAyB,QAAQ,GAAG;AACtC,YAAM,oBAAoB,KAAK,gBAAgB,QAAQ;AACvD,WAAK,cAAc,iBAAiB;WAC/B;AACL,YAAM,IAAI,MAAM,oBAAoB;;EAExC;EAEO,cAAc,UAAwC;AAC3D,QAAI,uBAAuB,QAAQ,GAAG;AACpC,YAAM,oBAAoB,KAAK,gBAAgB,QAAQ;AACvD,WAAK,cAAc,iBAAiB;WAC/B;AACL,YAAM,IAAI,MAAM,mBAAmB;;EAEvC;EAEO,iBAAc;AACnB,SAAK,WAAW,MAAK;EACvB;EAIU,MAAM,aACd,SACA,SAA0C;AAE1C,UAAM,cAA+B,KAAK,eAAe,OAAO;AAEhE,UAAM,oBAA+C,MAAM,KAAK,SAAS,WAAW;AAEpF,UAAM,QAAgB,QAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,WAAU,cAAc,QAAQ,QAAQ,KAAK;AACnF,UAAM,UAAkB,KAAK,UAAU,iBAAiB;AACxD,UAAM,SACJ,QAAO,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,2BAA0B,cACtC,CAAC,QAAQ,wBACT,gBAAgB,WAAW;AAEjC,SAAK,WAAW,KAAK,SAAS,OAAO,MAAM;EAC7C;EAEU,MAAM,cAAc,UAAyD;AACrF,UAAM,oBAA+C,MAAM,KAAK,SAAS,QAAQ;AAEjF,UAAM,QAAgB,KAAK;AAC3B,UAAM,UAAkB,KAAK,UAAU,iBAAiB;AACxD,UAAM,SAAS;AAEf,SAAK,WAAW,KAAK,SAAS,OAAO,MAAM;EAC7C;EAEU,MAAM,oBACd,SACA,UACA,SAAiC;AAEjC,SAAK,aAAa,SAAS,OAAO;AAClC,SAAK,4BAA4B,QAAQ,IAAI,QAAQ;EACvD;EAEU,iBAAiB,SAA0B,SAAyB;AAC5E,SAAK,aAAa,SAAS,OAAO;AAElC,SAAK,cAAc,QAAQ;MACzB,OAAO;MACP,QAAQ,CAAC,EAAE,SAAS,QAAO,CAAE;KAC9B;AAED,WAAO,KAAK,yBAAyB,QAAQ,EAAE;EACjD;EAEU,eAAe,SAAiC;AACxD,QAAI,OAAO,QAAQ,WAAW,aAAa;AACzC,YAAM,IAAI,MAAM,oBAAoB;;AAEtC,UAAM,mBAAoC;MACxC,IAAI,OAAO,QAAQ,OAAO,cAAcC,WAAS,IAAK,QAAQ;MAC9D,SAAS;MACT,QAAQ,QAAQ;MAChB,QAAQ,OAAO,QAAQ,WAAW,cAAc,CAAA,IAAK,QAAQ;;AAE/D,WAAO;EACT;EAEU,gBACR,UAAkE;AAElE,QAAI,OAAO,SAAS,OAAO,aAAa;AACtC,YAAM,IAAI,MAAM,gBAAgB;;AAGlC,UAAM,eAAe,EAAE,IAAI,SAAS,IAAI,SAAS,MAAK;AAEtD,QAAI,uBAAuB,QAAQ,GAAG;AACpC,YAAM,QAAQ,eAAe,SAAS,KAAK;AAE3C,YAAM,gBAAa,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACd,YAAY,GACZ,QAAQ,GAAA,EACX,MAAK,CAAA;AAEP,aAAO;eACE,yBAAyB,QAAQ,GAAG;AAC7C,YAAM,kBAAe,OAAA,OAAA,OAAA,OAAA,CAAA,GAChB,YAAY,GACZ,QAAQ;AAGb,aAAO;;AAGT,UAAM,IAAI,MAAM,sBAAsB;EACxC;EAEQ,yBAAyB,UAAiB;AAChD,UAAM,UAAU,YAAY;AAC5B,QAAI,CAAC,KAAK,YAAY;AACpB,UAAI,KAAK,cAAc;AACrB,aAAK,aAAa,MAAK;;AAEzB,kBAAY,sBAAsB;;AAEpC,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa;;AAEpB,QAAI,KAAK,cAAc;AACrB,WAAK,eAAe;;AAEtB,QAAI,KAAK,iBAAiB;AACxB,WAAK,kBAAkB;;AAEzB,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU;;AAEjB,SAAK,cAAc,QAAQ;MACzB,OAAO;MACP,QAAQ,CAAC,EAAE,QAAO,CAAE;KACrB;AACD,SAAK,sBAAqB;AAC1B,SAAK,eAAc;EACrB;EAEQ,uBAAuB,UAAkB,eAA8B;AAC7E,QAAI,eAAe;AACjB,UAAI,cAAc,UAAU;AAC1B,YAAI,CAAC,KAAK,YAAY;AACpB,eAAK,aAAa;AAElB,cAAI,cAAc,SAAS;AACzB,iBAAK,UAAU,cAAc;;AAG/B,cAAI,cAAc,UAAU;AAC1B,iBAAK,WAAW,cAAc;;AAGhC,cAAI,cAAc,UAAU,CAAC,KAAK,QAAQ;AACxC,iBAAK,SAAS,cAAc;;AAG9B,cAAI,cAAc,YAAY,CAAC,KAAK,UAAU;AAC5C,iBAAK,WAAW,cAAc;;AAGhC,eAAK,cAAc,QAAQ;YACzB,OAAO;YACP,QAAQ;cACN;gBACE,QAAQ,KAAK;gBACb,UAAU,KAAK;gBACf,SAAS,KAAK;gBACd,UAAU,KAAK;;;WAGpB;eACI;AACL,cAAI,cAAc,SAAS;AACzB,iBAAK,UAAU,cAAc;;AAE/B,cAAI,cAAc,UAAU;AAC1B,iBAAK,WAAW,cAAc;;AAGhC,eAAK,cAAc,QAAQ;YACzB,OAAO;YACP,QAAQ;cACN;gBACE,SAAS,KAAK;gBACd,UAAU,KAAK;;;WAGpB;;AAGH,aAAK,sBAAqB;aACrB;AACL,aAAK,yBAAyB,QAAQ;;WAEnC;AACL,WAAK,yBAAyB,QAAQ;;EAE1C;EAEQ,MAAM,wBAAwB,eAA6B;AACjE,UAAM,eAAe,CAAC,KAAK,UAAU,KAAK,cAAc;AAExD,QAAI,CAAC,aAAa,SAAS,cAAc,KAAK,GAAG;AAC/C;;AAGF,QAAI;AACJ,QAAI;AACF,0BAAoB,KAAK,MAAM,cAAc,OAAO;aAC7C,OAAO;AACd;;AAGF,UAAM,UAIK,MAAM,KAAK,SAAS,iBAAiB;AAEhD,QAAI,SAAS;AACX,WAAK,cAAc,QAAQ,OAAO;;EAEtC;EAEQ,6BAA0B;AAChC,SAAK,WAAW,UAAU,KAAK,cAAc;EAC/C;EAEQ,qBACN,IACA,UAA4D;AAE5D,SAAK,GAAG,YAAY,EAAE,IAAI,QAAQ;EACpC;EAEQ,4BAA4B,IAAY,UAAgB;AAC9D,SAAK,qBAAqB,IAAI,CAAC,OAAO,YAAW;AAC/C,UAAI,OAAO;AACT,aAAK,uBAAuB,MAAM,OAAO;AACzC;;AAEF,UAAI,yBAAyB,OAAO,GAAG;AACrC,aAAK,uBAAuB,UAAU,QAAQ,MAAM;iBAC3C,QAAQ,SAAS,QAAQ,MAAM,SAAS;AACjD,aAAK,uBAAuB,QAAQ,MAAM,OAAO;aAC5C;AACL,aAAK,uBAAuB,QAAQ;;IAExC,CAAC;EACH;EAEQ,yBAAyB,IAAU;AACzC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,WAAK,qBAAqB,IAAI,CAAC,OAAO,YAAW;AAC/C,YAAI,OAAO;AACT,iBAAO,KAAK;AACZ;;AAEF,YAAI,yBAAyB,OAAO,GAAG;AACrC,kBAAQ,QAAQ,MAAM;mBACb,QAAQ,SAAS,QAAQ,MAAM,SAAS;AACjD,iBAAO,QAAQ,KAAK;eACf;AACL,iBAAO,IAAI,MAAM,sBAAsB,CAAC;;MAE5C,CAAC;IACH,CAAC;EACH;EAEQ,6BAA0B;AAChC,SAAK,GAAG,eAAe,MAAK;AAC1B,UAAI,KAAK,cAAc;AACrB,aAAK,aAAa,KAChB,KAAK,KACL,MAAK;AACH,eAAK,cAAc,QAAQ;YACzB,OAAO;YACP,QAAQ,CAAA;WACT;QACH,GACA,KAAK,mBAAmB;;IAG9B,CAAC;AAED,SAAK,GAAG,WAAW,MAAK;AACtB,UAAI,KAAK,cAAc;AACrB,aAAK,aAAa,MAAK;;IAE3B,CAAC;AAED,SAAK,GAAG,qBAAqB,CAAC,OAAO,YAAW;AAC9C,YAAM,EAAE,QAAO,IAAK,QAAQ,OAAO,CAAC;AACpC,UAAI,SAAQ,KAAM,KAAK,gBAAgB,SAAS,QAAQ,MAAM,GAAG;AAC/D,cAAM,gBAAgB,SAAS,sBAAsB;AACrD,YAAI,eAAe;AACjB,iBAAO,SAAS,OAAO,cAAc;;;IAG3C,CAAC;AAED,SAAK,GAAG,qBAAqB,CAAC,OAAO,YAAW;AAC9C,UAAI,OAAO;AACT,aAAK,cAAc,QAAQ;UACzB,OAAO;UACP,QAAQ;YACN;cACE,MAAM;cACN,SAAS,MAAM,SAAQ;;;SAG5B;;AAEH,WAAK,cAAc,QAAQ;AAC3B,WAAK,SAAS,QAAQ,OAAO,CAAC,EAAE;AAChC,WAAK,WAAW,QAAQ,OAAO,CAAC,EAAE;AAElC,YAAM,kBAAe,OAAA,OAAA,OAAA,OAAA,CAAA,GAChB,OAAO,GAAA,EACV,QAAQ,kBAAiB,CAAA;AAE3B,WAAK,cAAc,QAAQ,eAAe;IAC5C,CAAC;AAED,SAAK,GAAG,oBAAoB,CAAC,OAAO,YAAW;AAC7C,UAAI,OAAO;AACT,aAAK,uBAAuB,MAAM,OAAO;;AAE3C,WAAK,uBAAuB,wBAAwB,QAAQ,OAAO,CAAC,CAAC;IACvE,CAAC;EACH;EAEQ,iBAAc;AACpB,SAAK,WAAW,GAAG,WAAW,CAAC,kBAC7B,KAAK,wBAAwB,aAAa,CAAC;AAG7C,SAAK,WAAW,GAAG,QAAQ,MACzB,KAAK,cAAc,QAAQ,EAAE,OAAO,kBAAkB,QAAQ,CAAA,EAAE,CAAE,CAAC;AAGrE,SAAK,WAAW,GAAG,SAAS,MAC1B,KAAK,cAAc,QAAQ,EAAE,OAAO,mBAAmB,QAAQ,CAAA,EAAE,CAAE,CAAC;AAGtE,SAAK,WAAW,GAAG,SAAS,MAC1B,KAAK,cAAc,QAAQ;MACzB,OAAO;MACP,QAAQ,CAAC,6BAA6B;KACvC,CAAC;AAGJ,SAAK,WAAW,KAAI;EACtB;EAIQ,aAAU;AAChB,UAAM,WAAW,KAAK;AACtB,UAAM,iBAAiB,KAAK;AAC5B,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,mBAAmB,KAAK,MAAM;AAC7C,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,GAAG,QAAQ,IAAI,cAAc,IAAI,OAAO,WAAW,MAAM,QAAQ,GAAG;AAChF,WAAO;EACT;EAEQ,UAAU,KAAW;AAC3B,UAAM,SAA0B,sBAAsB,GAAG;AAEzD,QAAI,OAAO,aAAa,KAAK,UAAU;AACrC,UAAI,CAAC,OAAO,gBAAgB;AAC1B,cAAM,MAAM,mDAAmD;;AAEjE,YAAM,iBAAiB,OAAO;AAE9B,UAAI,CAAC,OAAO,QAAQ;AAClB,cAAM,MAAM,+CAA+C;;AAE7D,YAAM,SAAS,mBAAmB,OAAO,MAAM;AAE/C,UAAI,CAAC,OAAO,KAAK;AACf,cAAM,MAAM,wCAAwC;;AAEtD,YAAM,MAAM,OAAO;AAEnB,aAAO,EAAE,gBAAgB,QAAQ,IAAG;WAC/B;AACL,YAAM,IAAI,MAAM,iBAAiB;;EAErC;EAIQ,MAAM,eAAY;AACxB,QAAI,KAAK,YAAY;AACnB,YAAM,SAAS,MAAM,KAAK,WAAW,YAAW;AAChD,aAAO;;AAET,WAAO;EACT;EAEQ,MAAM,SACZ,MAAuE;AAEvE,UAAM,MAA0B,KAAK;AACrC,QAAI,KAAK,cAAc,KAAK;AAC1B,YAAM,SAA6B,MAAM,KAAK,WAAW,QAAQ,MAAM,GAAG;AAC1E,aAAO;;AAET,WAAO;EACT;EAEQ,MAAM,SACZ,SAA2B;AAE3B,UAAM,MAA0B,KAAK;AACrC,QAAI,KAAK,cAAc,KAAK;AAC1B,YAAM,SAIK,MAAM,KAAK,WAAW,QAAQ,SAAS,GAAG;AACrD,aAAO;;AAET,WAAO;EACT;EAIQ,qBAAkB;AACxB,QAAI,SAAuC;AAC3C,QAAI,KAAK,iBAAiB;AACxB,eAAS,KAAK,gBAAgB,WAAU;;AAE1C,WAAO;EACT;EAEQ,qBAAkB;AACxB,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,WAAW,KAAK,OAAO;;EAEhD;EAEQ,wBAAqB;AAC3B,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,cAAa;;EAEtC;EAEQ,wBAAqB;AAC3B,QAAI,KAAK,YAAY;AACnB,WAAK,mBAAkB;WAClB;AACL,WAAK,sBAAqB;;EAE9B;EAIQ,oBAAoB,gBAAkC;AAC5D,QAAI,CAAC,eAAe,OAAO,OAAO,eAAe,QAAQ,UAAU;AACjE,YAAM,MAAM,uDAAuD;;AAGrE,QAAI,CAAC,eAAe,QAAQ,OAAO,eAAe,SAAS,UAAU;AACnE,YAAM,MAAM,wDAAwD;;AAGtE,QAAI,CAAC,eAAe,SAAS,OAAO,eAAe,UAAU,UAAU;AACrE,YAAM,MAAM,yDAAyD;;AAGvE,UAAM,mBAAsC;MAC1C,QAAQ,KAAK;MACb,OAAO,KAAK;MACZ,MAAM,eAAe;MACrB,OAAO,eAAe;MACtB,UAAU;MACV,UAAU,eAAe,YAAY;;AAGvC,SAAK,GAAG,WAAW,OAAO,OAAqB,YAAgB;AAC7D,UAAI,OAAO;AACT,cAAM;;AAGR,UAAI,eAAe,UAAU;AAC3B,cAAM,WAAW,QAAQ,OAAO,CAAC,EAAE,SAAS;AAC5C,yBAAiB,WAAW;;AAG9B,UAAI;AACF,cAAM,WAAW,MAAM,MAAM,GAAG,eAAe,GAAG,QAAQ;UACxD,QAAQ;UACR,SAAS;YACP,QAAQ;YACR,gBAAgB;;UAElB,MAAM,KAAK,UAAU,gBAAgB;SACtC;AAED,cAAM,OAAO,MAAM,SAAS,KAAI;AAChC,YAAI,CAAC,KAAK,SAAS;AACjB,gBAAM,MAAM,mCAAmC;;eAE1CC,QAAO;AACd,cAAM,MAAM,mCAAmC;;IAEnD,CAAC;EACH;;AAEF,IAAA,eAAe;;;AM9vCf;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAAAC;EAAA;;;;;;;;;;;;;ACAA,UAAqB;AAEf,SAAU,YAAY,QAAc;AACxC,QAAM,gBAAoB,oBAAe;AACzC,SAAO,cAAc,gBAAgB,IAAI,WAAW,MAAM,CAAC;AAC7D;;;ACLA,IAAAC,OAAqB;;;ACAd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,cAAc;;;ACNpB,IAAM,aAAa;AACnB,IAAM,cAAc;AAEpB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB,OAAO,UAAU;AAC3C,IAAM,eAAe;AAErB,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AAE5B,IAAM,gBAAgB,OAAO,UAAU;AACvC,IAAM,iBAAiB,MAAM,WAAW;AAExC,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAE5B,IAAM,gBAAgB;AACtB,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,aAAa;;;ACtBnB,IAAM,UAAU;AAChB,IAAM,WAAW;;;ACDjB,IAAM,gBAAgB;;;ACAtB,IAAM,aAAa;AACnB,IAAM,aAAa;AAEnB,IAAM,UAAU;AAChB,IAAM,YAAY;;;ALanB,SAAU,QAAQ,MAAY;AAClC,SAAO,SAAS,mBACZ,EAAE,QAAQ,YAAY,MAAM,iBAAgB,IAC5C;IACE,MAAM,EAAE,MAAM,kBAAiB;IAC/B,MAAM;;AAEd;AAEM,SAAU,OAAO,MAAY;AACjC,SAAO,SAAS,mBAAmB,CAAC,YAAY,UAAU,IAAI,CAAC,SAAS,SAAS;AACnF;AAUA,eAAsB,iBACpB,QACA,OAAe,kBAAgB;AAE/B,SAAY,qBAAe,EAAW,UAAU,OAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,OAAO,IAAI,CAAC;AAClG;AAEA,eAAsB,kBACpB,IACA,KACA,MAAgB;AAEhB,QAAM,SAAa,qBAAe;AAClC,QAAM,YAAY,MAAM,iBAAiB,KAAK,gBAAgB;AAC9D,QAAM,SAAS,MAAM,OAAO,QAC1B;IACE;IACA,MAAM;KAER,WACA,IAAI;AAEN,SAAO,IAAI,WAAW,MAAM;AAC9B;AAEA,eAAsB,kBACpB,IACA,KACA,MAAgB;AAEhB,QAAM,SAAa,qBAAe;AAClC,QAAM,YAAY,MAAM,iBAAiB,KAAK,gBAAgB;AAC9D,QAAM,SAAS,MAAM,OAAO,QAC1B;IACE;IACA,MAAM;KAER,WACA,IAAI;AAEN,SAAO,IAAI,WAAW,MAAM;AAC9B;AAEA,eAAsB,sBACpB,KACA,MAAgB;AAEhB,QAAM,SAAa,qBAAe;AAClC,QAAM,YAAY,MAAM,iBAAiB,KAAK,YAAY;AAC1D,QAAM,YAAY,MAAM,OAAO,KAC7B;IAEE,QAAQ;IACR,MAAM;KAER,WACA,IAAI;AAEN,SAAO,IAAI,WAAW,SAAS;AACjC;AAEA,eAAsB,sBACpB,KACA,MAAgB;AAEhB,QAAM,SAAa,qBAAe;AAClC,QAAM,YAAY,MAAM,iBAAiB,KAAK,YAAY;AAC1D,QAAM,YAAY,MAAM,OAAO,KAC7B;IAEE,QAAQ;IACR,MAAM;KAER,WACA,IAAI;AAEN,SAAO,IAAI,WAAW,SAAS;AACjC;AAEA,eAAsB,cAAc,MAAgB;AAClD,QAAM,SAAa,qBAAe;AAClC,QAAM,SAAS,MAAM,OAAO,OAC1B;IACE,MAAM;KAER,IAAI;AAEN,SAAO,IAAI,WAAW,MAAM;AAC9B;AAEA,eAAsB,cAAc,MAAgB;AAClD,QAAM,SAAa,qBAAe;AAClC,QAAM,SAAS,MAAM,OAAO,OAC1B;IACE,MAAM;KAER,IAAI;AAEN,SAAO,IAAI,WAAW,MAAM;AAC9B;;;AMvIM,SAAU,cACd,IACA,KACA,MAAgB;AAEhB,SAAO,kBAAkB,IAAI,KAAK,IAAI;AACxC;AAEM,SAAU,cACd,IACA,KACA,MAAgB;AAEhB,SAAO,kBAAkB,IAAI,KAAK,IAAI;AACxC;;;AChBA;;gBAAAC;EAAA;;;;;ACAA;wBAAc;;;ADAd,4BAAc;;;AEQd,IAAM,UAAsB;EAC1B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EAC/D,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EAC3D,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvD,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACnD,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EAC/C,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EAC3C,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACvC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAC1B,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACvB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACpB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACjB,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;EACd,CAAC,GAAG,GAAG,GAAG,CAAC;EACX,CAAC,GAAG,GAAG,CAAC;EACR,CAAC,GAAG,CAAC;EACL,CAAC,CAAC;;AAGG,IAAM,QAAQ;EAQnB,IAAI,WAAqB;AACvB,UAAM,UAAU,QAAQ,UAAU,aAAa,MAAM,CAAC;AACtD,UAAM,SAAS,IAAI,WAAW,UAAU,aAAa,QAAQ,MAAM;AAEnE,WAAO,IAAI,SAAS;AACpB,WAAO,IAAI,SAAS,UAAU,UAAU;AAExC,WAAO;EACT;EAQA,MAAM,QAAkB;AACtB,WAAO,OAAO,SAAS,GAAG,OAAO,aAAa,OAAO,OAAO,aAAa,CAAC,CAAC;EAC7E;;;;ACrDI,SAAUC,QAAO,WAAoB,SAAe;AACxD,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,WAAW,kBAAkB;;AAEjD;AAEM,SAAU,eAAe,MAAkB,MAAgB;AAC/D,MAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,WAAO;;AAET,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,WAAO,KAAK,CAAC,IAAI,KAAK,CAAC;;AAEzB,SAAO,QAAQ;AACjB;;;ACXA,eAAsB,eAAe,KAAiB,KAAe;AACnE,QAAM,SAAS,MAAM,sBAAsB,KAAK,GAAG;AACnD,SAAO;AACT;AAEA,eAAsB,iBACpB,KACA,KACA,KAAe;AAEf,QAAM,cAAc,MAAM,sBAAsB,KAAK,GAAG;AACxD,QAAM,SAAS,eAAe,aAAa,GAAG;AAC9C,SAAO;AACT;AAEA,eAAsB,eAAe,KAAiB,KAAe;AACnE,QAAM,SAAS,MAAM,sBAAsB,KAAK,GAAG;AACnD,SAAO;AACT;AAEA,eAAsB,iBACpB,KACA,KACA,KAAe;AAEf,QAAM,cAAc,MAAM,sBAAsB,KAAK,GAAG;AACxD,QAAM,SAAS,eAAe,aAAa,GAAG;AAC9C,SAAO;AACT;;;AC9BA,eAAsB,OAAO,KAAe;AAC1C,QAAM,SAAS,MAAM,cAAc,GAAG;AACtC,SAAO;AACT;AAEA,eAAsB,OAAO,KAAe;AAC1C,QAAM,SAAS,MAAM,cAAc,GAAG;AACtC,SAAO;AACT;AAEA,eAAsB,UAAU,MAAgB;AAC9C,QAAM,IAAI,MAAM,4DAA4D;AAC9E;;;AdRA,4BAAc;;;ADLdC;AAQAA;AAKA,eAAsB,YAAY,QAAe;AAC/C,QAAM,WAAW,UAAU,OAAO;AAClC,QAAM,QAAe,YAAY,OAAO;AACxC,QAAM,SAAS,2BAAoC,cAAc,KAAK,CAAC;AAEvE,SAAO;AACT;AAEA,eAAsB,WAAW,SAA6B,KAAe;AAC3E,QAAM,aAAsB,WAAW,QAAQ,IAAI;AACnD,QAAM,KAAc,WAAW,QAAQ,EAAE;AACzC,QAAM,OAAgB,WAAW,QAAQ,IAAI;AAC7C,QAAM,UAA2B,WAAW,MAAM,KAAK;AACvD,QAAM,WAAoB,aAAa,YAAY,EAAE;AACrD,QAAM,QAAQ,MAAa,eAAe,KAAK,QAAQ;AACvD,QAAM,WAA4B,WAAW,OAAO,KAAK;AAEzD,MAAa,gBAAgB,OAAO,MAAe,gBAAgB,QAAQ,GAAG;AAC5E,WAAO;;AAGT,SAAO;AACT;AAEA,eAAsB,QACpB,MACA,KACA,YAAwB;AAExB,QAAM,OAAgB,cAAc,2BAA2B,GAAG,CAAC;AAEnE,QAAM,gBAA6B,cAAe,MAAM,YAAY,GAAG;AACvE,QAAM,KAAc,cAAc,2BAA2B,aAAa,CAAC;AAC3E,QAAM,QAAyB,WAAW,IAAI,KAAK;AAEnD,QAAM,gBAAwB,KAAK,UAAU,IAAI;AACjD,QAAM,UAAmB,YAAY,aAAa;AAElD,QAAM,aAAa,MAAa,cAAc,IAAI,MAAM,OAAO;AAC/D,QAAM,gBAAiC,WAAW,YAAY,KAAK;AAEnE,QAAM,WAAoB,aAAa,YAAY,EAAE;AACrD,QAAM,OAAO,MAAa,eAAe,MAAM,QAAQ;AACvD,QAAM,UAA2B,WAAW,MAAM,KAAK;AAEvD,SAAO;IACL,MAAM;IACN,MAAM;IACN,IAAI;;AAER;AAEA,eAAsB,QACpB,SACA,KAAgB;AAEhB,QAAM,OAAgB,cAAc,2BAA2B,GAAG,CAAC;AAEnE,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,sCAAsC;;AAGxD,QAAM,WAAoB,MAAM,WAAW,SAAS,IAAI;AACxD,MAAI,CAAC,UAAU;AACb,WAAO;;AAGT,QAAM,aAAsB,WAAW,QAAQ,IAAI;AACnD,QAAM,KAAc,WAAW,QAAQ,EAAE;AACzC,QAAM,SAAS,MAAa,cAAc,IAAI,MAAM,UAAU;AAC9D,QAAM,OAAwB,YAAY,MAAM;AAChD,MAAI;AACJ,MAAI;AACF,WAAO,KAAK,MAAM,IAAI;WACf,OAAO;AACd,WAAO;;AAGT,SAAO;AACT;;;AgBxFA,IAAM,gBAAN,cAA4B,aAAS;EACnC,YAAY,eAAsC,gBAAmC;AACnF,UAAM;MACJ;MACA;MACA;KACD;EACH;;AAGF,IAAA,cAAe;;;AvBbf,0BAAwB;AACxB;AACAC;AASM,IAAO,mBAAP,cAAgC,EAAkB;EAatD,YAAY,MAAiC;AAC3C,UAAK;AAbA,SAAA,SAAc,IAAI,eAAAC,QAAY;AAE9B,SAAA,WAAqB,CAAA;AACrB,SAAA,UAAU;AAET,SAAA,UAAU;AAEV,SAAA,SAAS;AACT,SAAA,SAAS;AACT,SAAA,qBAAsD;AAK5D,SAAK,OAAO;AACZ,SAAK,WAAU,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAW,KAAK;AACrC,SAAK,KAAK,KAAK,SAAS,IAAI;EAC9B;EAEA,IAAI,YAAS;AACX,WAAO,OAAO,KAAK,OAAO,eAAe,KAAK,GAAG;EACnD;EAEA,IAAI,aAAU;AACZ,WAAO,KAAK;EACd;EAEA,IAAI,YAAS;AACX,SAAK,KAAK,KAAK,SAAS,KAAK,IAAI;AACjC,WAAO,KAAK;EACd;EAEO,GAAG,OAAe,UAAa;AACpC,SAAK,OAAO,GAAG,OAAO,QAAQ;EAChC;EAEO,KAAK,OAAe,UAAa;AACtC,SAAK,OAAO,KAAK,OAAO,QAAQ;EAClC;EAEO,IAAI,OAAe,UAAa;AACrC,SAAK,OAAO,IAAI,OAAO,QAAQ;EACjC;EAEO,eAAe,OAAe,UAAa;AAChD,SAAK,OAAO,eAAe,OAAO,QAAQ;EAC5C;EAEO,MAAM,KAAK,SAAgB;AAChC,QAAI,KAAK,WAAW;AAClB,WAAK,OAAM;AACX;;AAEF,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAgB;AAC3C,WAAK,GAAG,SAAS,SAAM;AACrB,eAAO,GAAG;MACZ,CAAC;AAED,WAAK,GAAG,QAAQ,MAAK;AACnB,gBAAO;MACT,CAAC;AAED,WAAK,OAAO,OAAO;IACrB,CAAC;EACH;EAEO,MAAM,QAAK;AAChB,QAAI,OAAO,KAAK,OAAO;AAAa;AACpC,QAAI,KAAK,GAAG,WAAW;AACrB,WAAK,GAAG,YAAW;;AAErB,SAAK,QAAO;EACd;EAEO,MAAM,KAAK,SAAY;AAC5B,SAAK,KAAK,KAAK,SAAS,KAAK,IAAI;AAEjC,QAAI,CAAC,KAAK;AAAW,YAAM,KAAK,KAAI;AACpC,SAAK,YAAY,OAAO,EACrB,KAAK,CAAC,QAAa,KAAK,OAAO,KAAK,WAAW,GAAG,CAAC,EACnD,MAAM,OAAK,KAAK,OAAO,KAAK,WAAW,mBAAmB,QAAQ,IAAI,EAAE,OAAO,CAAC,CAAC;EACtF;EAIQ,SAAS,MAAiC;AAChD,QAAI,KAAK;AAAI,aAAO,KAAK;AACzB,SAAK,OAAO,QAAQ,KAAK;AACzB,SAAK,UAAS,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,aAChB,KAAK,UAAU,UACf,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,WAAU;AAEpB,SAAK,SAAS,QAAO,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAW,eAAe,KAAK,WAAW;AACrE,SAAK,UAAU,QAAO,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,aAAY,cAAc,KAAK,UAAU,KAAK;AAC1E,SAAK,qBAAqB,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;AAChC,UAAM,gBAAgB;MACpB,QAAQ,KAAK;MACb,aAAa,KAAK,SAAS,oBAAAC,UAAc;MACzC,oBAAoB,KAAK;MACzB,WAAW,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;MACjB,gBAAgB,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;MACtB,YAAY,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;;AAEpB,SAAK,KACH,QAAO,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,eAAc,cAAc,KAAK,YAAY,IAAI,YAAc,aAAa;AAC3F,QAAI,OAAO,KAAK,OAAO,aAAa;AAClC,YAAM,IAAI,MAAM,4CAA4C;;AAE9D,QAAI,KAAK,GAAG,SAAS,QAAQ;AAC3B,WAAK,WAAW,KAAK,GAAG;;AAE1B,QAAI,KAAK,GAAG,SAAS;AACnB,WAAK,UAAU,KAAK,GAAG;;AAIzB,SAAK,wBAAuB;AAC5B,WAAO,KAAK;EACd;EAEQ,OAAO,IAAe;AAC5B,SAAK,UAAU;AACf,QAAI,IAAI;AACN,WAAK,KAAK;;AAEZ,SAAK,OAAO,KAAK,MAAM;EACzB;EAEQ,UAAO;AACb,SAAK,UAAU;AACf,QAAI,KAAK,IAAI;AACX,WAAK,KAAK;;AAEZ,SAAK,OAAO,KAAK,OAAO;EAC1B;EAEO,QACL,SACA,UAAU,8BACV,OAAO,OAAM;AAEb,UAAM,eAA6B;MACjC,IAAI,QAAQ;MACZ,SAAS,QAAQ;MACjB,OAAO,EAAE,MAAM,QAAO;;AAExB,SAAK,OAAO,KAAK,WAAW,YAAY;AACxC,WAAO;EACT;EAEQ,OAAO,SAAgB;AAC7B,SAAK,KAAK,KAAK,SAAS,KAAK,IAAI;AACjC,SAAK,UAAU,WAAW,KAAK;AAC/B,QAAI,KAAK,aAAa,KAAK;AAAS;AACpC,SAAK,UAAU;AACf,SAAK,wBAAuB;AAC5B,SAAK,GACF,cAAc,EAAE,SAAS,KAAK,QAAO,CAAE,EACvC,KAAK,MAAM,KAAK,OAAO,KAAK,SAAS,CAAC,EACtC,MAAM,CAAC,MAAa,KAAK,OAAO,KAAK,SAAS,CAAC,CAAC;EACrD;EAEQ,0BAAuB;AAC7B,SAAK,KAAK,KAAK,SAAS,KAAK,IAAI;AAEjC,SAAK,GAAG,GAAG,WAAW,CAAC,QAAqB;;AAC1C,UAAI,KAAK;AACP,aAAK,OAAO,KAAK,SAAS,GAAG;AAC7B;;AAEF,WAAK,aAAW,KAAA,KAAK,QAAE,QAAA,OAAA,SAAA,SAAA,GAAE,aAAY,CAAA;AACrC,WAAK,YAAU,KAAA,KAAK,QAAE,QAAA,OAAA,SAAA,SAAA,GAAE,YAAW,KAAK;AAExC,WAAK,OAAM;IACb,CAAC;AAED,SAAK,GAAG,GAAG,cAAc,CAAC,QAAqB;AAC7C,UAAI,KAAK;AACP,aAAK,OAAO,KAAK,SAAS,GAAG;AAC7B;;AAGF,WAAK,QAAO;IACd,CAAC;AAED,SAAK,GAAG,GAAG,gBAAgB,MAAK;AAC9B,WAAK,OAAO,KAAK,SAAS,IAAI,MAAM,mBAAmB,CAAC;IAC1D,CAAC;AAED,SAAK,GAAG,GAAG,kBAAkB,CAAC,OAAO,YAAW;AAC9C,YAAM,EAAE,UAAU,QAAO,IAAK,QAAQ,OAAO,CAAC;AAC9C,UAAI,CAAC,KAAK,YAAa,YAAY,KAAK,aAAa,UAAW;AAC9D,aAAK,WAAW;AAChB,aAAK,OAAO,KAAK,mBAAmB,QAAQ;;AAE9C,UAAI,CAAC,KAAK,WAAY,WAAW,KAAK,YAAY,SAAU;AAC1D,aAAK,UAAU;AACf,aAAK,OAAO,KAAK,gBAAgB,OAAO;;IAE5C,CAAC;EACH;EAEQ,MAAM,YAAY,SAAY;AACpC,SAAK,KAAK,KAAK,SAAS,KAAK,IAAI;AACjC,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,GAAG,WAAW,OAAO;AACjD,aAAO,KAAK,iBAAiB,QAAQ;aAC9B,OAAO;AACd,aAAO,KAAK,QAAQ,SAAU,MAAc,OAAO;;EAEvD;EAEQ,iBACN,UAAyD;AAEzD,WAAO,OAAQ,SAAmC,UAAU,eAC1D,OAAQ,SAAmC,MAAM,SAAS,cACxD,mBACE,SAAS,IACR,SAAmC,MAAM,SACzC,SAAmC,MAAM,IAAI,IAE/C;EACP;;;;AD/NF,IAAM,wBAAN,MAA2B;EAOzB,YAAY,MAAiC;AANtC,SAAA,SAAc,IAAI,eAAAC,QAAY;AAOnC,SAAK,MAAM,EAAE,UAAU,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,UAAU,QAAQ,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,IAAG;AACxD,SAAK,SAAS,IAAIC,GAAgB,IAAI,iBAAiB,IAAI,CAAC;AAC5D,UAAM,UAAW,KAAK,OAAO,WAAgC,YAAW,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAW;AACzF,SAAK,OAAO,KAAK,gBAAgB,OAAO;AACxC,SAAK,uBAAsB;EAC7B;EAEA,IAAI,YAAS;AACX,WAAQ,KAAK,OAAO,WAAgC;EACtD;EAEA,IAAI,YAAS;AACX,WAAQ,KAAK,OAAO,WAAgC;EACtD;EAEA,IAAI,WAAQ;AACV,WAAQ,KAAK,OAAO,WAAgC;EACtD;EAEA,IAAI,UAAO;AACT,WAAQ,KAAK,OAAO,WAAgC;EACtD;EAEA,IAAI,SAAM;;AACR,aAAQ,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,YAA8B,OAAO;EAC1D;EAEO,MAAM,QAAqB,MAAsB;AACtD,YAAQ,KAAK,QAAQ;MACnB,KAAK;AACH,cAAM,KAAK,QAAO;AAClB,eAAQ,KAAK,OAAO,WAAmB;MACzC,KAAK;AACH,eAAQ,KAAK,OAAO,WAAmB;MACzC,KAAK;AACH,eAAQ,KAAK,OAAO,WAAmB;MACzC;AACE;;AAEJ,QAAI,gBAAgB,SAAS,KAAK,MAAM,GAAG;AACzC,aAAO,KAAK,OAAO,QAAQ,IAAI;;AAEjC,QAAI,OAAO,KAAK,SAAS,aAAa;AACpC,YAAM,IAAI,MAAM,mCAAmC,KAAK,MAAM,4BAA4B;;AAE5F,WAAO,KAAK,KAAK,QAAQ,IAAI;EAC/B;EAEO,UACL,MACA,UAAsD;AAEtD,SAAK,QAAQ,IAAI,EACd,KAAK,cAAY,SAAS,MAAM,QAAQ,CAAC,EACzC,MAAM,WAAS,SAAS,OAAO,MAAS,CAAC;EAC9C;EAEO,MAAM,SAAM;AACjB,UAAM,WAAW,MAAM,KAAK,QAAQ,EAAE,QAAQ,sBAAqB,CAAE;AACrE,WAAO;EACT;EAEO,MAAM,UAAO;AAClB,QAAI,CAAC,KAAK,OAAO,WAAW,WAAW;AACrC,YAAM,KAAK,OAAO,QAAO;;EAE7B;EAEO,MAAM,aAAU;AACrB,QAAI,KAAK,OAAO,WAAW,WAAW;AACpC,YAAM,KAAK,OAAO,WAAU;;EAEhC;EAEO,GAAG,OAAY,UAAa;AACjC,SAAK,OAAO,GAAG,OAAO,QAAQ;EAChC;EACO,KAAK,OAAe,UAAa;AACtC,SAAK,OAAO,KAAK,OAAO,QAAQ;EAClC;EACO,eAAe,OAAe,UAAa;AAChD,SAAK,OAAO,eAAe,OAAO,QAAQ;EAC5C;EACO,IAAI,OAAe,UAAa;AACrC,SAAK,OAAO,IAAI,OAAO,QAAQ;EACjC;EAEA,IAAI,kBAAe;AACjB,WAAO;EACT;EAIQ,yBAAsB;AAC5B,SAAK,OAAO,WAAW,GAAG,mBAAmB,cAAW;AACtD,WAAK,OAAO,KAAK,mBAAmB,QAAQ;IAC9C,CAAC;AACD,SAAK,OAAO,WAAW,GAAG,gBAAgB,aAAU;AAClD,WAAK,OAAO,KAAK,gBAAgB,OAAO;AACxC,WAAK,OAAO,KAAK,gBAAgB,OAAO;IAC1C,CAAC;AACD,SAAK,OAAO,GAAG,cAAc,MAAK;AAChC,WAAK,OAAO,KAAK,YAAY;IAC/B,CAAC;EACH;EAEQ,gBAAgB,SAAe;AACrC,UAAM,SAAS,UAAU,SAAS,KAAK,GAAG;AAC1C,QAAI,OAAO,WAAW;AAAa,aAAO;AAC1C,UAAM,OAAO,IAAIA,GAAgB,IAAI,EAAe,MAAM,CAAC;AAC3D,WAAO;EACT;;AAGF,IAAAC,eAAe;", "names": ["init_events", "init_jsonrpc", "init_esm", "init_events", "init_jsonrpc", "isTypedArray", "isTypedArray", "typedA<PERSON>y<PERSON>o<PERSON><PERSON>er", "x", "_isTypedArray", "init_esm", "b", "concatBuffers", "init_esm", "getFromWindow", "getFromWindowOrThrow", "getDocumentOrThrow", "getDocument", "getNavigatorOrThrow", "getNavigator", "getLocationOrThrow", "getLocation", "getCryptoOrThrow", "getCrypto", "getLocalStorageOrThrow", "getLocalStorage", "env", "getNavigator", "safeJsonParse", "safeJsonStringify", "safeJsonStringify", "getLocalStorage", "safeJsonParse", "sanitizeHex", "addHexPrefix", "removeHexPrefix", "removeHexLeadingZeros", "b", "payloadId", "init_esm", "init_mobile", "init_esm", "init_registry", "b", "parse", "k", "x", "path", "requiredParams", "queryString", "result", "<PERSON><PERSON><PERSON><PERSON>", "isTypedArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getType", "getEncoding", "isHexString", "init_esm", "esm_exports", "addHexPrefix", "concatBuffers", "getCrypto", "getCryptoOrThrow", "getDocument", "getDocumentOrThrow", "getEncoding", "getFromWindow", "getFromWindowOrThrow", "getLocalStorage", "getLocalStorageOrThrow", "getLocation", "getLocationOrThrow", "getNavigator", "getNavigatorOrThrow", "getType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isHexString", "isTypedArray", "payloadId", "removeHexLeadingZeros", "removeHexPrefix", "safeJsonParse", "safeJsonStringify", "sanitizeHex", "init_esm", "init_mobile", "init_registry", "require_browser", "format", "err", "assign", "obj", "props", "i", "shallow<PERSON>iffers", "a", "b", "useSyncExternalStore", "subscribe", "getSnapshot", "value", "_useState", "useState", "_instance", "__", "_getSnapshot", "forceUpdate", "useLayoutEffect", "didSnapshotChange", "useEffect", "inst", "x", "y", "latestGetSnapshot", "prevValue", "nextValue", "error", "startTransition", "cb", "useDeferredValue", "val", "useTransition", "PureComponent", "p", "c", "this", "context", "memo", "comparer", "shouldUpdate", "nextProps", "ref", "updateRef", "call", "current", "Memoed", "shouldComponentUpdate", "createElement", "displayName", "name", "prototype", "isReactComponent", "__f", "forwardRef", "fn", "Forwarded", "clone", "$$typeof", "REACT_FORWARD_SYMBOL", "render", "detachedClone", "vnode", "detachedParent", "parentDom", "__c", "__H", "for<PERSON>ach", "effect", "__P", "__e", "__k", "map", "child", "removeOriginal", "originalParent", "__v", "append<PERSON><PERSON><PERSON>", "Suspense", "__u", "_suspenders", "__b", "suspended", "component", "__a", "lazy", "loader", "prom", "Lazy", "then", "exports", "default", "e", "SuspenseList", "_next", "_map", "ContextProvider", "getChildContext", "children", "Portal", "_this", "container", "_container", "componentWillUnmount", "_temp", "root", "__m", "nodeType", "parentNode", "childNodes", "contains", "insertBefore", "before", "push", "<PERSON><PERSON><PERSON><PERSON>", "splice", "indexOf", "createPortal", "el", "containerInfo", "parent", "callback", "textContent", "preactRender", "hydrate", "preactHydrate", "empty", "isPropagationStopped", "cancelBubble", "isDefaultPrevented", "defaultPrevented", "createFactory", "type", "bind", "isValidElement", "element", "REACT_ELEMENT_TYPE", "isFragment", "Fragment", "isMemo", "String", "startsWith", "cloneElement", "preactCloneElement", "apply", "arguments", "unmountComponentAtNode", "findDOMNode", "base", "useInsertionEffect", "oldDiffHook", "mapFn", "Children", "oldCatchError", "oldUnmount", "resolve", "CAMEL_PROPS", "ON_ANI", "CAMEL_REPLACE", "IS_DOM", "onChangeInputType", "oldEventHook", "currentComponent", "classNameDescriptorNonEnumberable", "oldVNodeHook", "oldBeforeRender", "oldDiffed", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "version", "unstable_batchedUpdates", "flushSync", "StrictMode", "isElement", "index", "Component", "isPureReactComponent", "state", "options", "Symbol", "for", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "count", "length", "only", "normalized", "toArray", "newVNode", "oldVNode", "errorInfo", "unmount", "__R", "promise", "suspendingVNode", "suspendingComponent", "resolved", "onResolved", "onSuspensionComplete", "suspendedVNode", "__O", "setState", "pop", "document", "detachedComponent", "fallback", "list", "node", "delete", "revealOrder", "size", "delegated", "get", "unsuspend", "wrappedUnsuspend", "Map", "reverse", "set", "componentDidUpdate", "componentDidMount", "test", "key", "Object", "defineProperty", "configurable", "v", "writable", "event", "persist", "nativeEvent", "enumerable", "class", "normalizedProps", "isNonDashedType", "lowerCased", "toLowerCase", "replace", "multiple", "Array", "isArray", "selected", "defaultValue", "className", "__r", "diffed", "dom", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readContext", "__n", "useCallback", "useContext", "useDebugValue", "useId", "useImperativeHandle", "useMemo", "useReducer", "useRef", "arg", "createContext", "createRef", "open", "uri", "QRCode", "toString", "type", "then", "console", "log", "const", "WALLETCONNECT_STYLE_SHEET", "_iteratorSymbol", "Symbol", "iterator", "_asyncIteratorSymbol", "asyncIterator", "_catch", "body", "recover", "result", "e", "WALLETCONNECT_LOGO_SVG_URL", "WALLETCONNECT_HEADER_TEXT", "ANIMATION_DURATION", "DEFAULT_BUTTON_COLOR", "WALLETCONNECT_WRAPPER_ID", "WALLETCONNECT_STYLE_ID", "WALLETCONNECT_MODAL_ID", "WALLETCONNECT_CLOSE_BUTTON_ID", "WALLETCONNECT_CTA_TEXT_ID", "WALLETCONNECT_CONNECT_BUTTON_ID", "Header", "props", "React", "className", "src", "onClick", "onClose", "id", "ConnectButton", "href", "name", "rel", "style", "backgroundColor", "color", "target", "CARET_SVG_URL", "WalletButton", "logo", "background", "backgroundSize", "WalletIcon", "fontSize", "window", "innerWidth", "length", "GRID_MIN_COUNT", "LINKS_PER_PAGE", "LinkDisplay", "android", "isAndroid", "input", "setInput", "filter", "setFilter", "page", "setPage", "links", "link", "toLowerCase", "includes", "errorMessage", "grid", "pages", "Math", "ceil", "range", "pageLinks", "_", "index", "hasPaging", "let", "filterTimeout", "undefined", "handleInput", "value", "clearTimeout", "setTimeout", "text", "connect_mobile_wallet", "choose_preferred_wallet", "placeholder", "onChange", "map", "entry", "shortName", "formatIOSMobile", "handleClickIOS", "saveMobileLinkInfo", "no_wallets_found", "loading", "connect", "Array", "fill", "pageNumber", "selected", "margin", "fontWeight", "Notification", "show", "message", "trim", "formatQRCodeImage", "data", "dataString", "replace", "QRCodeDisplay", "notification", "setNotification", "svg", "setSvg", "copyToClipboard", "success", "copy", "copied_to_clipboard", "setInterval", "scan_qrcode_with_wallet", "dangerouslySetInnerHTML", "__html", "copy_to_clipboard", "Modal", "mobile", "isMobile", "whitelist", "qrcodeModalOptions", "mobileLinks", "desktopLinks", "setLoading", "fetched", "setFetched", "displayQRCode", "setDisplayQRCode", "displayProps", "singleLinkHref", "setSingleLinkHref", "hasSingleLink", "setHasSingleLink", "setLinks", "setErrorMessage", "getLinksIfNeeded", "initLinks", "url", "registryUrl", "getWalletRegistryUrl", "fetch", "registryResponse", "json", "registry", "listings", "platform", "_links", "getMobileLinkRegistry", "formatMobileRegistry", "no_supported_wallets", "something_went_wrong", "error", "rightSelected", "connect_with", "qrcode", "desktop", "de", "en", "es", "fr", "ko", "pt", "zh", "fa", "languages", "injectStyleSheet", "doc", "getDocumentOrThrow", "prev", "getElementById", "head", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "setAttribute", "innerText", "append<PERSON><PERSON><PERSON>", "renderWrapper", "wrapper", "triggerCloseAnimation", "modal", "getWrappedCallback", "cb", "getText", "lang", "getNavigatorOrThrow", "language", "split", "Languages", "ReactDOM", "close", "isNode", "process", "versions", "node", "nodeLib", "browserLib", "import_events", "init_esm", "import_events", "init_esm", "init_esm", "event", "getLocation", "init_esm", "x", "init_esm", "payloadId", "error", "assert", "env", "assert", "assert", "init_esm", "init_esm", "EventEmitter", "QRCodeModal", "EventEmitter", "o", "esm_default"]}