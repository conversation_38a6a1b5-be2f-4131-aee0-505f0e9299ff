{"version": 3, "sources": ["../../@stablelib/int/int.ts", "../../@stablelib/binary/binary.ts", "../../@stablelib/wipe/wipe.ts", "../../@stablelib/chacha/chacha.ts", "../../@stablelib/constant-time/constant-time.ts", "../../@stablelib/poly1305/poly1305.ts", "../../@stablelib/chacha20poly1305/chacha20poly1305.ts", "../../@stablelib/hash/hash.ts", "../../@stablelib/hmac/hmac.ts", "../../@stablelib/hkdf/hkdf.ts", "../../@stablelib/random/source/browser.ts", "browser-external:crypto", "../../@stablelib/random/source/node.ts", "../../@stablelib/random/source/system.ts", "../../@stablelib/random/random.ts", "../../@stablelib/sha256/sha256.ts", "../../@stablelib/x25519/x25519.ts", "../../@walletconnect/time/node_modules/tslib/tslib.es6.js", "../../@walletconnect/time/src/utils/delay.ts", "../../@walletconnect/time/src/constants/misc.ts", "../../@walletconnect/time/src/constants/time.ts", "../../@walletconnect/time/src/constants/index.ts", "../../@walletconnect/time/src/utils/convert.ts", "../../@walletconnect/time/src/utils/index.ts", "../../@walletconnect/time/src/watch.ts", "../../@walletconnect/time/src/types/watch.ts", "../../@walletconnect/time/src/types/index.ts", "../../@walletconnect/time/src/index.ts", "../../@walletconnect/utils/node_modules/query-string/index.js", "../../uint8arrays/esm/src/util/as-uint8array.js", "../../uint8arrays/esm/src/alloc.js", "../../uint8arrays/esm/src/concat.js", "../../multiformats/esm/src/bases/identity.js", "../../multiformats/esm/vendor/base-x.js", "../../multiformats/esm/src/bytes.js", "../../multiformats/esm/src/bases/base.js", "../../multiformats/esm/src/bases/base2.js", "../../multiformats/esm/src/bases/base8.js", "../../multiformats/esm/src/bases/base10.js", "../../multiformats/esm/src/bases/base16.js", "../../multiformats/esm/src/bases/base32.js", "../../multiformats/esm/src/bases/base36.js", "../../multiformats/esm/src/bases/base58.js", "../../multiformats/esm/src/bases/base64.js", "../../multiformats/esm/src/bases/base256emoji.js", "../../multiformats/esm/src/hashes/sha2-browser.js", "../../multiformats/esm/vendor/varint.js", "../../multiformats/esm/src/varint.js", "../../multiformats/esm/src/hashes/digest.js", "../../multiformats/esm/src/hashes/hasher.js", "../../multiformats/esm/src/hashes/identity.js", "../../multiformats/esm/src/codecs/json.js", "../../multiformats/esm/src/cid.js", "../../multiformats/esm/src/basics.js", "../../uint8arrays/esm/src/util/bases.js", "../../uint8arrays/esm/src/from-string.js", "../../uint8arrays/esm/src/to-string.js", "../../@walletconnect/relay-api/src/misc.ts", "../../@walletconnect/relay-api/src/validators.ts", "../../@walletconnect/relay-api/src/parsers.ts", "../../@walletconnect/relay-api/src/jsonrpc.ts"], "sourcesContent": ["// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package int provides helper functions for integerss.\n */\n\n// Shim using 16-bit pieces.\nfunction imulShim(a: number, b: number): number {\n    const ah = (a >>> 16) & 0xffff, al = a & 0xffff;\n    const bh = (b >>> 16) & 0xffff, bl = b & 0xffff;\n    return ((al * bl) + (((ah * bl + al * bh) << 16) >>> 0) | 0);\n}\n\n/** 32-bit integer multiplication.  */\n// Use system Math.imul if available, otherwise use our shim.\nexport const mul = (Math as { imul?(a: number, b: number): number }).imul || imulShim;\n\n/** 32-bit integer addition.  */\nexport function add(a: number, b: number): number {\n    return (a + b) | 0;\n}\n\n/**  32-bit integer subtraction.  */\nexport function sub(a: number, b: number): number {\n    return (a - b) | 0;\n}\n\n/** 32-bit integer left rotation */\nexport function rotl(x: number, n: number): number {\n    return x << n | x >>> (32 - n);\n}\n\n/** 32-bit integer left rotation */\nexport function rotr(x: number, n: number): number {\n    return x << (32 - n) | x >>> n;\n}\n\nfunction isIntegerShim(n: number): boolean {\n    return typeof n === \"number\" && isFinite(n) && Math.floor(n) === n;\n}\n\n/**\n * Returns true if the argument is an integer number.\n *\n * In ES2015, Number.isInteger.\n */\nexport const isInteger = (Number as { isInteger?(n: number): boolean }).isInteger || isIntegerShim;\n\n/**\n *  Math.pow(2, 53) - 1\n *\n *  In ES2015 Number.MAX_SAFE_INTEGER.\n */\nexport const MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * Returns true if the argument is a safe integer number\n * (-MIN_SAFE_INTEGER < number <= MAX_SAFE_INTEGER)\n *\n * In ES2015, Number.isSafeInteger.\n */\nexport const isSafeInteger = (n: number): boolean =>\n    isInteger(n) && (n >= -MAX_SAFE_INTEGER && n <= MAX_SAFE_INTEGER);\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package binary provides functions for encoding and decoding numbers in byte arrays.\n */\n\nimport { isSafeInteger } from \"@stablelib/int\";\n\n// TODO(dchest): add asserts for correct value ranges and array offsets.\n\n/**\n * Reads 2 bytes from array starting at offset as big-endian\n * signed 16-bit integer and returns it.\n */\nexport function readInt16BE(array: Uint8Array, offset = 0): number {\n    return (((array[offset + 0] << 8) | array[offset + 1]) << 16) >> 16;\n}\n\n/**\n * Reads 2 bytes from array starting at offset as big-endian\n * unsigned 16-bit integer and returns it.\n */\nexport function readUint16BE(array: Uint8Array, offset = 0): number {\n    return ((array[offset + 0] << 8) | array[offset + 1]) >>> 0;\n}\n\n/**\n * Reads 2 bytes from array starting at offset as little-endian\n * signed 16-bit integer and returns it.\n */\nexport function readInt16LE(array: Uint8Array, offset = 0): number {\n    return (((array[offset + 1] << 8) | array[offset]) << 16) >> 16;\n}\n\n/**\n * Reads 2 bytes from array starting at offset as little-endian\n * unsigned 16-bit integer and returns it.\n */\nexport function readUint16LE(array: Uint8Array, offset = 0): number {\n    return ((array[offset + 1] << 8) | array[offset]) >>> 0;\n}\n\n/**\n * Writes 2-byte big-endian representation of 16-bit unsigned\n * value to byte array starting at offset.\n *\n * If byte array is not given, creates a new 2-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeUint16BE(value: number, out = new Uint8Array(2), offset = 0): Uint8Array {\n    out[offset + 0] = value >>> 8;\n    out[offset + 1] = value >>> 0;\n    return out;\n}\n\nexport const writeInt16BE = writeUint16BE;\n\n/**\n * Writes 2-byte little-endian representation of 16-bit unsigned\n * value to array starting at offset.\n *\n * If byte array is not given, creates a new 2-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeUint16LE(value: number, out = new Uint8Array(2), offset = 0): Uint8Array {\n    out[offset + 0] = value >>> 0;\n    out[offset + 1] = value >>> 8;\n    return out;\n}\n\nexport const writeInt16LE = writeUint16LE;\n\n/**\n * Reads 4 bytes from array starting at offset as big-endian\n * signed 32-bit integer and returns it.\n */\nexport function readInt32BE(array: Uint8Array, offset = 0): number {\n    return (array[offset] << 24) |\n        (array[offset + 1] << 16) |\n        (array[offset + 2] << 8) |\n        array[offset + 3];\n}\n\n/**\n * Reads 4 bytes from array starting at offset as big-endian\n * unsigned 32-bit integer and returns it.\n */\nexport function readUint32BE(array: Uint8Array, offset = 0): number {\n    return ((array[offset] << 24) |\n        (array[offset + 1] << 16) |\n        (array[offset + 2] << 8) |\n        array[offset + 3]) >>> 0;\n}\n\n/**\n * Reads 4 bytes from array starting at offset as little-endian\n * signed 32-bit integer and returns it.\n */\nexport function readInt32LE(array: Uint8Array, offset = 0): number {\n    return (array[offset + 3] << 24) |\n        (array[offset + 2] << 16) |\n        (array[offset + 1] << 8) |\n        array[offset];\n}\n\n/**\n * Reads 4 bytes from array starting at offset as little-endian\n * unsigned 32-bit integer and returns it.\n */\nexport function readUint32LE(array: Uint8Array, offset = 0): number {\n    return ((array[offset + 3] << 24) |\n        (array[offset + 2] << 16) |\n        (array[offset + 1] << 8) |\n        array[offset]) >>> 0;\n}\n\n/**\n * Writes 4-byte big-endian representation of 32-bit unsigned\n * value to byte array starting at offset.\n *\n * If byte array is not given, creates a new 4-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeUint32BE(value: number, out = new Uint8Array(4), offset = 0): Uint8Array {\n    out[offset + 0] = value >>> 24;\n    out[offset + 1] = value >>> 16;\n    out[offset + 2] = value >>> 8;\n    out[offset + 3] = value >>> 0;\n    return out;\n}\n\nexport const writeInt32BE = writeUint32BE;\n\n/**\n * Writes 4-byte little-endian representation of 32-bit unsigned\n * value to array starting at offset.\n *\n * If byte array is not given, creates a new 4-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeUint32LE(value: number, out = new Uint8Array(4), offset = 0): Uint8Array {\n    out[offset + 0] = value >>> 0;\n    out[offset + 1] = value >>> 8;\n    out[offset + 2] = value >>> 16;\n    out[offset + 3] = value >>> 24;\n    return out;\n}\n\n\nexport const writeInt32LE = writeUint32LE;\n\n/**\n * Reads 8 bytes from array starting at offset as big-endian\n * signed 64-bit integer and returns it.\n *\n * IMPORTANT: due to JavaScript limitation, supports exact\n * numbers in range -9007199254740991 to 9007199254740991.\n * If the number stored in the byte array is outside this range,\n * the result is not exact.\n */\nexport function readInt64BE(array: Uint8Array, offset = 0): number {\n    const hi = readInt32BE(array, offset);\n    const lo = readInt32BE(array, offset + 4);\n    return hi * 0x100000000 + lo - ((lo>>31) * 0x100000000);\n}\n\n/**\n * Reads 8 bytes from array starting at offset as big-endian\n * unsigned 64-bit integer and returns it.\n *\n * IMPORTANT: due to JavaScript limitation, supports values up to 2^53-1.\n */\nexport function readUint64BE(array: Uint8Array, offset = 0): number {\n    const hi = readUint32BE(array, offset);\n    const lo = readUint32BE(array, offset + 4);\n    return hi * 0x100000000 + lo;\n}\n\n/**\n * Reads 8 bytes from array starting at offset as little-endian\n * signed 64-bit integer and returns it.\n *\n * IMPORTANT: due to JavaScript limitation, supports exact\n * numbers in range -9007199254740991 to 9007199254740991.\n * If the number stored in the byte array is outside this range,\n * the result is not exact.\n */\nexport function readInt64LE(array: Uint8Array, offset = 0): number {\n    const lo = readInt32LE(array, offset);\n    const hi = readInt32LE(array, offset + 4);\n    return hi * 0x100000000 + lo - ((lo>>31) * 0x100000000);\n}\n\n\n/**\n * Reads 8 bytes from array starting at offset as little-endian\n * unsigned 64-bit integer and returns it.\n *\n * IMPORTANT: due to JavaScript limitation, supports values up to 2^53-1.\n */\nexport function readUint64LE(array: Uint8Array, offset = 0): number {\n    const lo = readUint32LE(array, offset);\n    const hi = readUint32LE(array, offset + 4);\n    return hi * 0x100000000 + lo;\n}\n\n/**\n * Writes 8-byte big-endian representation of 64-bit unsigned\n * value to byte array starting at offset.\n *\n * Due to JavaScript limitation, supports values up to 2^53-1.\n *\n * If byte array is not given, creates a new 8-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeUint64BE(value: number, out = new Uint8Array(8), offset = 0): Uint8Array {\n    writeUint32BE(value / 0x100000000 >>> 0, out, offset);\n    writeUint32BE(value >>> 0, out, offset + 4);\n    return out;\n}\n\nexport const writeInt64BE = writeUint64BE;\n\n/**\n * Writes 8-byte little-endian representation of 64-bit unsigned\n * value to byte array starting at offset.\n *\n * Due to JavaScript limitation, supports values up to 2^53-1.\n *\n * If byte array is not given, creates a new 8-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeUint64LE(value: number, out = new Uint8Array(8), offset = 0): Uint8Array {\n    writeUint32LE(value >>> 0, out, offset);\n    writeUint32LE(value / 0x100000000 >>> 0, out, offset + 4);\n    return out;\n}\n\nexport const writeInt64LE = writeUint64LE;\n\n/**\n * Reads bytes from array starting at offset as big-endian\n * unsigned bitLen-bit integer and returns it.\n *\n * Supports bit lengths divisible by 8, up to 48.\n */\nexport function readUintBE(bitLength: number, array: Uint8Array, offset = 0): number {\n    // TODO(dchest): implement support for bitLengths non-divisible by 8\n    if (bitLength % 8 !== 0) {\n        throw new Error(\"readUintBE supports only bitLengths divisible by 8\");\n    }\n    if (bitLength / 8 > array.length - offset) {\n        throw new Error(\"readUintBE: array is too short for the given bitLength\");\n    }\n    let result = 0;\n    let mul = 1;\n    for (let i = bitLength / 8 + offset - 1; i >= offset; i--) {\n        result += array[i] * mul;\n        mul *= 256;\n    }\n    return result;\n}\n\n/**\n * Reads bytes from array starting at offset as little-endian\n * unsigned bitLen-bit integer and returns it.\n *\n * Supports bit lengths divisible by 8, up to 48.\n */\nexport function readUintLE(bitLength: number, array: Uint8Array, offset = 0): number {\n    // TODO(dchest): implement support for bitLengths non-divisible by 8\n    if (bitLength % 8 !== 0) {\n        throw new Error(\"readUintLE supports only bitLengths divisible by 8\");\n    }\n    if (bitLength / 8 > array.length - offset) {\n        throw new Error(\"readUintLE: array is too short for the given bitLength\");\n    }\n    let result = 0;\n    let mul = 1;\n    for (let i = offset; i < offset + bitLength / 8; i++) {\n        result += array[i] * mul;\n        mul *= 256;\n    }\n    return result;\n}\n\n/**\n * Writes a big-endian representation of bitLen-bit unsigned\n * value to array starting at offset.\n *\n * Supports bit lengths divisible by 8, up to 48.\n *\n * If byte array is not given, creates a new one.\n *\n * Returns the output byte array.\n */\nexport function writeUintBE(bitLength: number, value: number,\n    out = new Uint8Array(bitLength / 8), offset = 0): Uint8Array {\n    // TODO(dchest): implement support for bitLengths non-divisible by 8\n    if (bitLength % 8 !== 0) {\n        throw new Error(\"writeUintBE supports only bitLengths divisible by 8\");\n    }\n    if (!isSafeInteger(value)) {\n        throw new Error(\"writeUintBE value must be an integer\");\n    }\n    let div = 1;\n    for (let i = bitLength / 8 + offset - 1; i >= offset; i--) {\n        out[i] = (value / div) & 0xff;\n        div *= 256;\n    }\n    return out;\n}\n\n/**\n * Writes a little-endian representation of bitLen-bit unsigned\n * value to array starting at offset.\n *\n * Supports bit lengths divisible by 8, up to 48.\n *\n * If byte array is not given, creates a new one.\n *\n * Returns the output byte array.\n */\nexport function writeUintLE(bitLength: number, value: number,\n    out = new Uint8Array(bitLength / 8), offset = 0): Uint8Array {\n    // TODO(dchest): implement support for bitLengths non-divisible by 8\n    if (bitLength % 8 !== 0) {\n        throw new Error(\"writeUintLE supports only bitLengths divisible by 8\");\n    }\n    if (!isSafeInteger(value)) {\n        throw new Error(\"writeUintLE value must be an integer\");\n    }\n    let div = 1;\n    for (let i = offset; i < offset + bitLength / 8; i++) {\n        out[i] = (value / div) & 0xff;\n        div *= 256;\n    }\n    return out;\n}\n\n/**\n * Reads 4 bytes from array starting at offset as big-endian\n * 32-bit floating-point number and returns it.\n */\nexport function readFloat32BE(array: Uint8Array, offset = 0): number {\n    const view = new DataView(array.buffer, array.byteOffset, array.byteLength);\n    return view.getFloat32(offset);\n}\n\n/**\n * Reads 4 bytes from array starting at offset as little-endian\n * 32-bit floating-point number and returns it.\n */\nexport function readFloat32LE(array: Uint8Array, offset = 0): number {\n    const view = new DataView(array.buffer, array.byteOffset, array.byteLength);\n    return view.getFloat32(offset, true);\n}\n\n/**\n * Reads 8 bytes from array starting at offset as big-endian\n * 64-bit floating-point number (\"double\") and returns it.\n */\nexport function readFloat64BE(array: Uint8Array, offset = 0): number {\n    const view = new DataView(array.buffer, array.byteOffset, array.byteLength);\n    return view.getFloat64(offset);\n}\n\n/**\n * Reads 8 bytes from array starting at offset as little-endian\n * 64-bit floating-point number (\"double\") and returns it.\n */\nexport function readFloat64LE(array: Uint8Array, offset = 0): number {\n    const view = new DataView(array.buffer, array.byteOffset, array.byteLength);\n    return view.getFloat64(offset, true);\n}\n\n/**\n * Writes 4-byte big-endian floating-point representation of value\n * to byte array starting at offset.\n *\n * If byte array is not given, creates a new 4-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeFloat32BE(value: number, out = new Uint8Array(4), offset = 0): Uint8Array {\n    const view = new DataView(out.buffer, out.byteOffset, out.byteLength);\n    view.setFloat32(offset, value);\n    return out;\n}\n\n/**\n * Writes 4-byte little-endian floating-point representation of value\n * to byte array starting at offset.\n *\n * If byte array is not given, creates a new 4-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeFloat32LE(value: number, out = new Uint8Array(4), offset = 0): Uint8Array {\n    const view = new DataView(out.buffer, out.byteOffset, out.byteLength);\n    view.setFloat32(offset, value, true);\n    return out;\n}\n\n/**\n * Writes 8-byte big-endian floating-point representation of value\n * to byte array starting at offset.\n *\n * If byte array is not given, creates a new 8-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeFloat64BE(value: number, out = new Uint8Array(8), offset = 0): Uint8Array {\n    const view = new DataView(out.buffer, out.byteOffset, out.byteLength);\n    view.setFloat64(offset, value);\n    return out;\n}\n\n/**\n * Writes 8-byte little-endian floating-point representation of value\n * to byte array starting at offset.\n *\n * If byte array is not given, creates a new 8-byte one.\n *\n * Returns the output byte array.\n */\nexport function writeFloat64LE(value: number, out = new Uint8Array(8), offset = 0): Uint8Array {\n    const view = new DataView(out.buffer, out.byteOffset, out.byteLength);\n    view.setFloat64(offset, value, true);\n    return out;\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package wipe implements functions for zeroing arrays.\n */\n\nexport type NumericArray = number[] | Uint8Array | Int8Array | Uint16Array\n    | Int16Array | Uint32Array | Int32Array | Float32Array | Float64Array;\n\n/**\n * Sets all values in the given array to zero and returns it.\n *\n * The fact that it sets bytes to zero can be relied on.\n *\n * There is no guarantee that this function makes data disappear from memory,\n * as runtime implementation can, for example, have copying garbage collector\n * that will make copies of sensitive data before we wipe it. Or that an\n * operating system will write our data to swap or sleep image. Another thing\n * is that an optimizing compiler can remove calls to this function or make it\n * no-op. There's nothing we can do with it, so we just do our best and hope\n * that everything will be okay and good will triumph over evil.\n */\nexport function wipe(array: NumericArray): NumericArray {\n    // Right now it's similar to array.fill(0). If it turns\n    // out that runtimes optimize this call away, maybe\n    // we can try something else.\n    for (let i = 0; i < array.length; i++) {\n        array[i] = 0;\n    }\n    return array;\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package chacha implements ChaCha stream cipher.\n */\n\nimport { writeUint32LE } from \"@stablelib/binary\";\nimport { wipe } from \"@stablelib/wipe\";\n\n// Number of ChaCha rounds (ChaCha20).\nconst ROUNDS = 20;\n\n// Applies the ChaCha core function to 16-byte input,\n// 32-byte key key, and puts the result into 64-byte array out.\nfunction core(out: Uint8Array, input: Uint8Array, key: Uint8Array): void {\n    let j0 = 0x61707865; // \"expa\"  -- ChaCha's \"sigma\" constant\n    let j1 = 0x3320646E; // \"nd 3\"     for 32-byte keys\n    let j2 = 0x79622D32; // \"2-by\"\n    let j3 = 0x6B206574; // \"te k\"\n    let j4 = (key[3] << 24) | (key[2] << 16) | (key[1] << 8) | key[0];\n    let j5 = (key[7] << 24) | (key[6] << 16) | (key[5] << 8) | key[4];\n    let j6 = (key[11] << 24) | (key[10] << 16) | (key[9] << 8) | key[8];\n    let j7 = (key[15] << 24) | (key[14] << 16) | (key[13] << 8) | key[12];\n    let j8 = (key[19] << 24) | (key[18] << 16) | (key[17] << 8) | key[16];\n    let j9 = (key[23] << 24) | (key[22] << 16) | (key[21] << 8) | key[20];\n    let j10 = (key[27] << 24) | (key[26] << 16) | (key[25] << 8) | key[24];\n    let j11 = (key[31] << 24) | (key[30] << 16) | (key[29] << 8) | key[28];\n    let j12 = (input[3] << 24) | (input[2] << 16) | (input[1] << 8) | input[0];\n    let j13 = (input[7] << 24) | (input[6] << 16) | (input[5] << 8) | input[4];\n    let j14 = (input[11] << 24) | (input[10] << 16) | (input[9] << 8) | input[8];\n    let j15 = (input[15] << 24) | (input[14] << 16) | (input[13] << 8) | input[12];\n\n    let x0 = j0;\n    let x1 = j1;\n    let x2 = j2;\n    let x3 = j3;\n    let x4 = j4;\n    let x5 = j5;\n    let x6 = j6;\n    let x7 = j7;\n    let x8 = j8;\n    let x9 = j9;\n    let x10 = j10;\n    let x11 = j11;\n    let x12 = j12;\n    let x13 = j13;\n    let x14 = j14;\n    let x15 = j15;\n\n    for (let i = 0; i < ROUNDS; i += 2) {\n        x0 = x0 + x4 | 0; x12 ^= x0; x12 = x12 >>> (32 - 16) | x12 << 16;\n        x8 = x8 + x12 | 0; x4 ^= x8; x4 = x4 >>> (32 - 12) | x4 << 12;\n        x1 = x1 + x5 | 0; x13 ^= x1; x13 = x13 >>> (32 - 16) | x13 << 16;\n        x9 = x9 + x13 | 0; x5 ^= x9; x5 = x5 >>> (32 - 12) | x5 << 12;\n\n        x2 = x2 + x6 | 0; x14 ^= x2; x14 = x14 >>> (32 - 16) | x14 << 16;\n        x10 = x10 + x14 | 0; x6 ^= x10; x6 = x6 >>> (32 - 12) | x6 << 12;\n        x3 = x3 + x7 | 0; x15 ^= x3; x15 = x15 >>> (32 - 16) | x15 << 16;\n        x11 = x11 + x15 | 0; x7 ^= x11; x7 = x7 >>> (32 - 12) | x7 << 12;\n\n        x2 = x2 + x6 | 0; x14 ^= x2; x14 = x14 >>> (32 - 8) | x14 << 8;\n        x10 = x10 + x14 | 0; x6 ^= x10; x6 = x6 >>> (32 - 7) | x6 << 7;\n        x3 = x3 + x7 | 0; x15 ^= x3; x15 = x15 >>> (32 - 8) | x15 << 8;\n        x11 = x11 + x15 | 0; x7 ^= x11; x7 = x7 >>> (32 - 7) | x7 << 7;\n\n        x1 = x1 + x5 | 0; x13 ^= x1; x13 = x13 >>> (32 - 8) | x13 << 8;\n        x9 = x9 + x13 | 0; x5 ^= x9; x5 = x5 >>> (32 - 7) | x5 << 7;\n        x0 = x0 + x4 | 0; x12 ^= x0; x12 = x12 >>> (32 - 8) | x12 << 8;\n        x8 = x8 + x12 | 0; x4 ^= x8; x4 = x4 >>> (32 - 7) | x4 << 7;\n\n        x0 = x0 + x5 | 0; x15 ^= x0; x15 = x15 >>> (32 - 16) | x15 << 16;\n        x10 = x10 + x15 | 0; x5 ^= x10; x5 = x5 >>> (32 - 12) | x5 << 12;\n        x1 = x1 + x6 | 0; x12 ^= x1; x12 = x12 >>> (32 - 16) | x12 << 16;\n        x11 = x11 + x12 | 0; x6 ^= x11; x6 = x6 >>> (32 - 12) | x6 << 12;\n\n        x2 = x2 + x7 | 0; x13 ^= x2; x13 = x13 >>> (32 - 16) | x13 << 16;\n        x8 = x8 + x13 | 0; x7 ^= x8; x7 = x7 >>> (32 - 12) | x7 << 12;\n        x3 = x3 + x4 | 0; x14 ^= x3; x14 = x14 >>> (32 - 16) | x14 << 16;\n        x9 = x9 + x14 | 0; x4 ^= x9; x4 = x4 >>> (32 - 12) | x4 << 12;\n\n        x2 = x2 + x7 | 0; x13 ^= x2; x13 = x13 >>> (32 - 8) | x13 << 8;\n        x8 = x8 + x13 | 0; x7 ^= x8; x7 = x7 >>> (32 - 7) | x7 << 7;\n        x3 = x3 + x4 | 0; x14 ^= x3; x14 = x14 >>> (32 - 8) | x14 << 8;\n        x9 = x9 + x14 | 0; x4 ^= x9; x4 = x4 >>> (32 - 7) | x4 << 7;\n\n        x1 = x1 + x6 | 0; x12 ^= x1; x12 = x12 >>> (32 - 8) | x12 << 8;\n        x11 = x11 + x12 | 0; x6 ^= x11; x6 = x6 >>> (32 - 7) | x6 << 7;\n        x0 = x0 + x5 | 0; x15 ^= x0; x15 = x15 >>> (32 - 8) | x15 << 8;\n        x10 = x10 + x15 | 0; x5 ^= x10; x5 = x5 >>> (32 - 7) | x5 << 7;\n    }\n    writeUint32LE(x0 + j0 | 0, out, 0);\n    writeUint32LE(x1 + j1 | 0, out, 4);\n    writeUint32LE(x2 + j2 | 0, out, 8);\n    writeUint32LE(x3 + j3 | 0, out, 12);\n    writeUint32LE(x4 + j4 | 0, out, 16);\n    writeUint32LE(x5 + j5 | 0, out, 20);\n    writeUint32LE(x6 + j6 | 0, out, 24);\n    writeUint32LE(x7 + j7 | 0, out, 28);\n    writeUint32LE(x8 + j8 | 0, out, 32);\n    writeUint32LE(x9 + j9 | 0, out, 36);\n    writeUint32LE(x10 + j10 | 0, out, 40);\n    writeUint32LE(x11 + j11 | 0, out, 44);\n    writeUint32LE(x12 + j12 | 0, out, 48);\n    writeUint32LE(x13 + j13 | 0, out, 52);\n    writeUint32LE(x14 + j14 | 0, out, 56);\n    writeUint32LE(x15 + j15 | 0, out, 60);\n}\n\n/**\n * Encrypt src with ChaCha20 stream generated for the given 32-byte key and\n * 8-byte (as in original implementation) or 12-byte (as in RFC7539) nonce and\n * write the result into dst and return it.\n *\n * dst and src may be the same, but otherwise must not overlap.\n *\n * If nonce is 12 bytes, users should not encrypt more than 256 GiB with the\n * same key and nonce, otherwise the stream will repeat. The function will\n * throw error if counter overflows to prevent this.\n *\n * If nonce is 8 bytes, the output is practically unlimited (2^70 bytes, which\n * is more than a million petabytes). However, it is not recommended to\n * generate 8-byte nonces randomly, as the chance of collision is high.\n *\n * Never use the same key and nonce to encrypt more than one message.\n *\n * If nonceInplaceCounterLength is not 0, the nonce is assumed to be a 16-byte\n * array with stream counter in first nonceInplaceCounterLength bytes and nonce\n * in the last remaining bytes. The counter will be incremented inplace for\n * each ChaCha block. This is useful if you need to encrypt one stream of data\n * in chunks.\n */\nexport function streamXOR(key: Uint8Array, nonce: Uint8Array,\n    src: Uint8Array, dst: Uint8Array, nonceInplaceCounterLength = 0): Uint8Array {\n    // We only support 256-bit keys.\n    if (key.length !== 32) {\n        throw new Error(\"ChaCha: key size must be 32 bytes\");\n    }\n\n    if (dst.length < src.length) {\n        throw new Error(\"ChaCha: destination is shorter than source\");\n    }\n\n    let nc: Uint8Array;\n    let counterLength: number;\n\n    if (nonceInplaceCounterLength === 0) {\n        if (nonce.length !== 8 && nonce.length !== 12) {\n            throw new Error(\"ChaCha nonce must be 8 or 12 bytes\");\n        }\n        nc = new Uint8Array(16);\n        // First counterLength bytes of nc are counter, starting with zero.\n        counterLength = nc.length - nonce.length;\n        // Last bytes of nc after counterLength are nonce, set them.\n        nc.set(nonce, counterLength);\n    } else {\n        if (nonce.length !== 16) {\n            throw new Error(\"ChaCha nonce with counter must be 16 bytes\");\n        }\n        // This will update passed nonce with counter inplace.\n        nc = nonce;\n        counterLength = nonceInplaceCounterLength;\n    }\n\n    // Allocate temporary space for ChaCha block.\n    const block = new Uint8Array(64);\n\n    for (let i = 0; i < src.length; i += 64) {\n        // Generate a block.\n        core(block, nc, key);\n\n        // XOR block bytes with src into dst.\n        for (let j = i; j < i + 64 && j < src.length; j++) {\n            dst[j] = src[j] ^ block[j - i];\n        }\n\n        // Increment counter.\n        incrementCounter(nc, 0, counterLength);\n    }\n\n    // Cleanup temporary space.\n    wipe(block);\n\n    if (nonceInplaceCounterLength === 0) {\n        // Cleanup counter.\n        wipe(nc);\n    }\n\n    return dst;\n}\n\n/**\n * Generate ChaCha20 stream for the given 32-byte key and 8-byte or 12-byte\n * nonce and write it into dst and return it.\n *\n * Never use the same key and nonce to generate more than one stream.\n *\n * If nonceInplaceCounterLength is not 0, it behaves the same with respect to\n * the nonce as described in the streamXOR documentation.\n *\n * stream is like streamXOR with all-zero src.\n */\nexport function stream(key: Uint8Array, nonce: Uint8Array,\n    dst: Uint8Array, nonceInplaceCounterLength = 0): Uint8Array {\n    wipe(dst);\n    return streamXOR(key, nonce, dst, dst, nonceInplaceCounterLength);\n}\n\nfunction incrementCounter(counter: Uint8Array, pos: number, len: number) {\n    let carry = 1;\n    while (len--) {\n        carry = carry + (counter[pos] & 0xff) | 0;\n        counter[pos] = carry & 0xff;\n        carry >>>= 8;\n        pos++;\n    }\n    if (carry > 0) {\n        throw new Error(\"ChaCha: counter overflow\");\n    }\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package constant-time provides functions for performing algorithmically constant-time operations.\n */\n\n/**\n * NOTE! Due to the inability to guarantee real constant time evaluation of\n * anything in JavaScript VM, this is module is the best effort.\n */\n\n/**\n * Returns resultIfOne if subject is 1, or resultIfZero if subject is 0.\n *\n * Supports only 32-bit integers, so resultIfOne or resultIfZero are not\n * integers, they'll be converted to them with bitwise operations.\n */\nexport function select(subject: number, resultIfOne: number, resultIfZero: number): number {\n    return (~(subject - 1) & resultIfOne) | ((subject - 1) & resultIfZero);\n}\n\n/**\n * Returns 1 if a <= b, or 0 if not.\n * Arguments must be positive 32-bit integers less than or equal to 2^31 - 1.\n */\nexport function lessOrEqual(a: number, b: number): number {\n    return (((a | 0) - (b | 0) - 1) >>> 31) & 1;\n}\n\n/**\n * Returns 1 if a and b are of equal length and their contents\n * are equal, or 0 otherwise.\n *\n * Note that unlike in equal(), zero-length inputs are considered\n * the same, so this function will return 1.\n */\nexport function compare(a: Uint8Array, b: Uint8Array): number {\n    if (a.length !== b.length) {\n        return 0;\n    }\n    let result = 0;\n    for (let i = 0; i < a.length; i++) {\n        result |= a[i] ^ b[i];\n    }\n    return (1 & ((result - 1) >>> 8));\n}\n\n/**\n * Returns true if a and b are of equal non-zero length,\n * and their contents are equal, or false otherwise.\n *\n * Note that unlike in compare() zero-length inputs are considered\n * _not_ equal, so this function will return false.\n */\nexport function equal(a: Uint8Array, b: Uint8Array): boolean {\n    if (a.length === 0 || b.length === 0) {\n        return false;\n    }\n    return compare(a, b) !== 0;\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package poly1305 implements Poly1305 one-time message authentication algorithm.\n */\n\nimport { equal as constantTimeEqual } from \"@stablelib/constant-time\";\nimport { wipe } from \"@stablelib/wipe\";\n\nexport const DIGEST_LENGTH = 16;\n\n// Port of Andrew Moon's Poly1305-donna-16. Public domain.\n// https://github.com/floodyberry/poly1305-donna\n\n/**\n * Poly1305 computes 16-byte authenticator of message using\n * a one-time 32-byte key.\n *\n * Important: key should be used for only one message,\n * it should never repeat.\n */\nexport class Poly1305 {\n    readonly digestLength = DIGEST_LENGTH;\n\n    private _buffer = new Uint8Array(16);\n    private _r = new Uint16Array(10);\n    private _h = new Uint16Array(10);\n    private _pad = new Uint16Array(8);\n    private _leftover = 0;\n    private _fin = 0;\n    private _finished = false;\n\n    constructor(key: Uint8Array) {\n        let t0 = key[0] | key[1] << 8; this._r[0] = (t0) & 0x1fff;\n        let t1 = key[2] | key[3] << 8; this._r[1] = ((t0 >>> 13) | (t1 << 3)) & 0x1fff;\n        let t2 = key[4] | key[5] << 8; this._r[2] = ((t1 >>> 10) | (t2 << 6)) & 0x1f03;\n        let t3 = key[6] | key[7] << 8; this._r[3] = ((t2 >>> 7) | (t3 << 9)) & 0x1fff;\n        let t4 = key[8] | key[9] << 8; this._r[4] = ((t3 >>> 4) | (t4 << 12)) & 0x00ff;\n        this._r[5] = ((t4 >>> 1)) & 0x1ffe;\n        let t5 = key[10] | key[11] << 8; this._r[6] = ((t4 >>> 14) | (t5 << 2)) & 0x1fff;\n        let t6 = key[12] | key[13] << 8; this._r[7] = ((t5 >>> 11) | (t6 << 5)) & 0x1f81;\n        let t7 = key[14] | key[15] << 8; this._r[8] = ((t6 >>> 8) | (t7 << 8)) & 0x1fff;\n        this._r[9] = ((t7 >>> 5)) & 0x007f;\n\n        this._pad[0] = key[16] | key[17] << 8;\n        this._pad[1] = key[18] | key[19] << 8;\n        this._pad[2] = key[20] | key[21] << 8;\n        this._pad[3] = key[22] | key[23] << 8;\n        this._pad[4] = key[24] | key[25] << 8;\n        this._pad[5] = key[26] | key[27] << 8;\n        this._pad[6] = key[28] | key[29] << 8;\n        this._pad[7] = key[30] | key[31] << 8;\n    }\n\n    private _blocks(m: Uint8Array, mpos: number, bytes: number) {\n        let hibit = this._fin ? 0 : 1 << 11;\n\n        let h0 = this._h[0],\n            h1 = this._h[1],\n            h2 = this._h[2],\n            h3 = this._h[3],\n            h4 = this._h[4],\n            h5 = this._h[5],\n            h6 = this._h[6],\n            h7 = this._h[7],\n            h8 = this._h[8],\n            h9 = this._h[9];\n\n        let r0 = this._r[0],\n            r1 = this._r[1],\n            r2 = this._r[2],\n            r3 = this._r[3],\n            r4 = this._r[4],\n            r5 = this._r[5],\n            r6 = this._r[6],\n            r7 = this._r[7],\n            r8 = this._r[8],\n            r9 = this._r[9];\n\n        while (bytes >= 16) {\n            let t0 = m[mpos + 0] | m[mpos + 1] << 8; h0 += (t0) & 0x1fff;\n            let t1 = m[mpos + 2] | m[mpos + 3] << 8; h1 += ((t0 >>> 13) | (t1 << 3)) & 0x1fff;\n            let t2 = m[mpos + 4] | m[mpos + 5] << 8; h2 += ((t1 >>> 10) | (t2 << 6)) & 0x1fff;\n            let t3 = m[mpos + 6] | m[mpos + 7] << 8; h3 += ((t2 >>> 7) | (t3 << 9)) & 0x1fff;\n            let t4 = m[mpos + 8] | m[mpos + 9] << 8; h4 += ((t3 >>> 4) | (t4 << 12)) & 0x1fff;\n            h5 += ((t4 >>> 1)) & 0x1fff;\n            let t5 = m[mpos + 10] | m[mpos + 11] << 8; h6 += ((t4 >>> 14) | (t5 << 2)) & 0x1fff;\n            let t6 = m[mpos + 12] | m[mpos + 13] << 8; h7 += ((t5 >>> 11) | (t6 << 5)) & 0x1fff;\n            let t7 = m[mpos + 14] | m[mpos + 15] << 8; h8 += ((t6 >>> 8) | (t7 << 8)) & 0x1fff;\n            h9 += ((t7 >>> 5)) | hibit;\n\n            let c = 0;\n\n            let d0 = c;\n            d0 += h0 * r0;\n            d0 += h1 * (5 * r9);\n            d0 += h2 * (5 * r8);\n            d0 += h3 * (5 * r7);\n            d0 += h4 * (5 * r6);\n            c = (d0 >>> 13); d0 &= 0x1fff;\n            d0 += h5 * (5 * r5);\n            d0 += h6 * (5 * r4);\n            d0 += h7 * (5 * r3);\n            d0 += h8 * (5 * r2);\n            d0 += h9 * (5 * r1);\n            c += (d0 >>> 13); d0 &= 0x1fff;\n\n            let d1 = c;\n            d1 += h0 * r1;\n            d1 += h1 * r0;\n            d1 += h2 * (5 * r9);\n            d1 += h3 * (5 * r8);\n            d1 += h4 * (5 * r7);\n            c = (d1 >>> 13); d1 &= 0x1fff;\n            d1 += h5 * (5 * r6);\n            d1 += h6 * (5 * r5);\n            d1 += h7 * (5 * r4);\n            d1 += h8 * (5 * r3);\n            d1 += h9 * (5 * r2);\n            c += (d1 >>> 13); d1 &= 0x1fff;\n\n            let d2 = c;\n            d2 += h0 * r2;\n            d2 += h1 * r1;\n            d2 += h2 * r0;\n            d2 += h3 * (5 * r9);\n            d2 += h4 * (5 * r8);\n            c = (d2 >>> 13); d2 &= 0x1fff;\n            d2 += h5 * (5 * r7);\n            d2 += h6 * (5 * r6);\n            d2 += h7 * (5 * r5);\n            d2 += h8 * (5 * r4);\n            d2 += h9 * (5 * r3);\n            c += (d2 >>> 13); d2 &= 0x1fff;\n\n            let d3 = c;\n            d3 += h0 * r3;\n            d3 += h1 * r2;\n            d3 += h2 * r1;\n            d3 += h3 * r0;\n            d3 += h4 * (5 * r9);\n            c = (d3 >>> 13); d3 &= 0x1fff;\n            d3 += h5 * (5 * r8);\n            d3 += h6 * (5 * r7);\n            d3 += h7 * (5 * r6);\n            d3 += h8 * (5 * r5);\n            d3 += h9 * (5 * r4);\n            c += (d3 >>> 13); d3 &= 0x1fff;\n\n            let d4 = c;\n            d4 += h0 * r4;\n            d4 += h1 * r3;\n            d4 += h2 * r2;\n            d4 += h3 * r1;\n            d4 += h4 * r0;\n            c = (d4 >>> 13); d4 &= 0x1fff;\n            d4 += h5 * (5 * r9);\n            d4 += h6 * (5 * r8);\n            d4 += h7 * (5 * r7);\n            d4 += h8 * (5 * r6);\n            d4 += h9 * (5 * r5);\n            c += (d4 >>> 13); d4 &= 0x1fff;\n\n            let d5 = c;\n            d5 += h0 * r5;\n            d5 += h1 * r4;\n            d5 += h2 * r3;\n            d5 += h3 * r2;\n            d5 += h4 * r1;\n            c = (d5 >>> 13); d5 &= 0x1fff;\n            d5 += h5 * r0;\n            d5 += h6 * (5 * r9);\n            d5 += h7 * (5 * r8);\n            d5 += h8 * (5 * r7);\n            d5 += h9 * (5 * r6);\n            c += (d5 >>> 13); d5 &= 0x1fff;\n\n            let d6 = c;\n            d6 += h0 * r6;\n            d6 += h1 * r5;\n            d6 += h2 * r4;\n            d6 += h3 * r3;\n            d6 += h4 * r2;\n            c = (d6 >>> 13); d6 &= 0x1fff;\n            d6 += h5 * r1;\n            d6 += h6 * r0;\n            d6 += h7 * (5 * r9);\n            d6 += h8 * (5 * r8);\n            d6 += h9 * (5 * r7);\n            c += (d6 >>> 13); d6 &= 0x1fff;\n\n            let d7 = c;\n            d7 += h0 * r7;\n            d7 += h1 * r6;\n            d7 += h2 * r5;\n            d7 += h3 * r4;\n            d7 += h4 * r3;\n            c = (d7 >>> 13); d7 &= 0x1fff;\n            d7 += h5 * r2;\n            d7 += h6 * r1;\n            d7 += h7 * r0;\n            d7 += h8 * (5 * r9);\n            d7 += h9 * (5 * r8);\n            c += (d7 >>> 13); d7 &= 0x1fff;\n\n            let d8 = c;\n            d8 += h0 * r8;\n            d8 += h1 * r7;\n            d8 += h2 * r6;\n            d8 += h3 * r5;\n            d8 += h4 * r4;\n            c = (d8 >>> 13); d8 &= 0x1fff;\n            d8 += h5 * r3;\n            d8 += h6 * r2;\n            d8 += h7 * r1;\n            d8 += h8 * r0;\n            d8 += h9 * (5 * r9);\n            c += (d8 >>> 13); d8 &= 0x1fff;\n\n            let d9 = c;\n            d9 += h0 * r9;\n            d9 += h1 * r8;\n            d9 += h2 * r7;\n            d9 += h3 * r6;\n            d9 += h4 * r5;\n            c = (d9 >>> 13); d9 &= 0x1fff;\n            d9 += h5 * r4;\n            d9 += h6 * r3;\n            d9 += h7 * r2;\n            d9 += h8 * r1;\n            d9 += h9 * r0;\n            c += (d9 >>> 13); d9 &= 0x1fff;\n\n            c = (((c << 2) + c)) | 0;\n            c = (c + d0) | 0;\n            d0 = c & 0x1fff;\n            c = (c >>> 13);\n            d1 += c;\n\n            h0 = d0;\n            h1 = d1;\n            h2 = d2;\n            h3 = d3;\n            h4 = d4;\n            h5 = d5;\n            h6 = d6;\n            h7 = d7;\n            h8 = d8;\n            h9 = d9;\n\n            mpos += 16;\n            bytes -= 16;\n        }\n        this._h[0] = h0;\n        this._h[1] = h1;\n        this._h[2] = h2;\n        this._h[3] = h3;\n        this._h[4] = h4;\n        this._h[5] = h5;\n        this._h[6] = h6;\n        this._h[7] = h7;\n        this._h[8] = h8;\n        this._h[9] = h9;\n    }\n\n    finish(mac: Uint8Array, macpos = 0): this {\n        const g = new Uint16Array(10);\n        let c: number;\n        let mask: number;\n        let f: number;\n        let i: number;\n\n        if (this._leftover) {\n            i = this._leftover;\n            this._buffer[i++] = 1;\n            for (; i < 16; i++) {\n                this._buffer[i] = 0;\n            }\n            this._fin = 1;\n            this._blocks(this._buffer, 0, 16);\n        }\n\n        c = this._h[1] >>> 13;\n        this._h[1] &= 0x1fff;\n        for (i = 2; i < 10; i++) {\n            this._h[i] += c;\n            c = this._h[i] >>> 13;\n            this._h[i] &= 0x1fff;\n        }\n        this._h[0] += (c * 5);\n        c = this._h[0] >>> 13;\n        this._h[0] &= 0x1fff;\n        this._h[1] += c;\n        c = this._h[1] >>> 13;\n        this._h[1] &= 0x1fff;\n        this._h[2] += c;\n\n        g[0] = this._h[0] + 5;\n        c = g[0] >>> 13;\n        g[0] &= 0x1fff;\n        for (i = 1; i < 10; i++) {\n            g[i] = this._h[i] + c;\n            c = g[i] >>> 13;\n            g[i] &= 0x1fff;\n        }\n        g[9] -= (1 << 13);\n\n        mask = (c ^ 1) - 1;\n        for (i = 0; i < 10; i++) {\n            g[i] &= mask;\n        }\n        mask = ~mask;\n        for (i = 0; i < 10; i++) {\n            this._h[i] = (this._h[i] & mask) | g[i];\n        }\n\n        this._h[0] = ((this._h[0]) | (this._h[1] << 13)) & 0xffff;\n        this._h[1] = ((this._h[1] >>> 3) | (this._h[2] << 10)) & 0xffff;\n        this._h[2] = ((this._h[2] >>> 6) | (this._h[3] << 7)) & 0xffff;\n        this._h[3] = ((this._h[3] >>> 9) | (this._h[4] << 4)) & 0xffff;\n        this._h[4] = ((this._h[4] >>> 12) | (this._h[5] << 1) | (this._h[6] << 14)) & 0xffff;\n        this._h[5] = ((this._h[6] >>> 2) | (this._h[7] << 11)) & 0xffff;\n        this._h[6] = ((this._h[7] >>> 5) | (this._h[8] << 8)) & 0xffff;\n        this._h[7] = ((this._h[8] >>> 8) | (this._h[9] << 5)) & 0xffff;\n\n        f = this._h[0] + this._pad[0];\n        this._h[0] = f & 0xffff;\n        for (i = 1; i < 8; i++) {\n            f = (((this._h[i] + this._pad[i]) | 0) + (f >>> 16)) | 0;\n            this._h[i] = f & 0xffff;\n        }\n\n        mac[macpos + 0] = this._h[0] >>> 0;\n        mac[macpos + 1] = this._h[0] >>> 8;\n        mac[macpos + 2] = this._h[1] >>> 0;\n        mac[macpos + 3] = this._h[1] >>> 8;\n        mac[macpos + 4] = this._h[2] >>> 0;\n        mac[macpos + 5] = this._h[2] >>> 8;\n        mac[macpos + 6] = this._h[3] >>> 0;\n        mac[macpos + 7] = this._h[3] >>> 8;\n        mac[macpos + 8] = this._h[4] >>> 0;\n        mac[macpos + 9] = this._h[4] >>> 8;\n        mac[macpos + 10] = this._h[5] >>> 0;\n        mac[macpos + 11] = this._h[5] >>> 8;\n        mac[macpos + 12] = this._h[6] >>> 0;\n        mac[macpos + 13] = this._h[6] >>> 8;\n        mac[macpos + 14] = this._h[7] >>> 0;\n        mac[macpos + 15] = this._h[7] >>> 8;\n\n        this._finished = true;\n        return this;\n    }\n\n    update(m: Uint8Array): this {\n        let mpos = 0;\n        let bytes = m.length;\n        let want: number;\n\n        if (this._leftover) {\n            want = (16 - this._leftover);\n            if (want > bytes) {\n                want = bytes;\n            }\n            for (let i = 0; i < want; i++) {\n                this._buffer[this._leftover + i] = m[mpos + i];\n            }\n            bytes -= want;\n            mpos += want;\n            this._leftover += want;\n            if (this._leftover < 16) {\n                return this;\n            }\n            this._blocks(this._buffer, 0, 16);\n            this._leftover = 0;\n        }\n\n        if (bytes >= 16) {\n            want = bytes - (bytes % 16);\n            this._blocks(m, mpos, want);\n            mpos += want;\n            bytes -= want;\n        }\n\n        if (bytes) {\n            for (let i = 0; i < bytes; i++) {\n                this._buffer[this._leftover + i] = m[mpos + i];\n            }\n            this._leftover += bytes;\n        }\n\n        return this;\n    }\n\n    digest(): Uint8Array {\n        // TODO(dchest): it behaves differently than other hashes/HMAC,\n        // because it throws when finished — others just return saved result.\n        if (this._finished) {\n            throw new Error(\"Poly1305 was finished\");\n        }\n        let mac = new Uint8Array(16);\n        this.finish(mac);\n        return mac;\n    }\n\n    clean(): this {\n        wipe(this._buffer);\n        wipe(this._r);\n        wipe(this._h);\n        wipe(this._pad);\n        this._leftover = 0;\n        this._fin = 0;\n        this._finished = true; // mark as finished even if not\n        return this;\n    }\n}\n\n/**\n * Returns 16-byte authenticator of data using a one-time 32-byte key.\n *\n * Important: key should be used for only one message, it should never repeat.\n */\nexport function oneTimeAuth(key: Uint8Array, data: Uint8Array): Uint8Array {\n    const h = new Poly1305(key);\n    h.update(data);\n    const digest = h.digest();\n    h.clean();\n    return digest;\n}\n\n/**\n * Returns true if two authenticators are 16-byte long and equal.\n * Uses contant-time comparison to avoid leaking timing information.\n */\nexport function equal(a: Uint8Array, b: Uint8Array): boolean {\n    if (a.length !== DIGEST_LENGTH || b.length !== DIGEST_LENGTH) {\n        return false;\n    }\n    return constantTimeEqual(a, b);\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package chacha20poly1305 implements ChaCha20-Poly1305 AEAD.\n */\n\nimport { AEAD } from \"@stablelib/aead\";\nimport { streamXOR, stream } from \"@stablelib/chacha\";\nimport { Poly1305 } from \"@stablelib/poly1305\";\nimport { wipe } from \"@stablelib/wipe\";\nimport { writeUint64LE } from \"@stablelib/binary\";\nimport { equal } from \"@stablelib/constant-time\";\n\nexport const KEY_LENGTH = 32;\nexport const NONCE_LENGTH = 12;\nexport const TAG_LENGTH = 16;\n\nconst ZEROS = new Uint8Array(16);\n\n/**\n * ChaCha20-Poly1305 Authenticated Encryption with Associated Data.\n *\n * Defined in RFC7539.\n */\nexport class ChaCha20Poly1305 implements AEAD {\n    readonly nonceLength = NONCE_LENGTH;\n    readonly tagLength = TAG_LENGTH;\n\n    private _key: Uint8Array;\n\n    /**\n     * Creates a new instance with the given 32-byte key.\n     */\n    constructor(key: Uint8Array) {\n        if (key.length !== KEY_LENGTH) {\n            throw new Error(\"ChaCha20Poly1305 needs 32-byte key\");\n        }\n        // Copy key.\n        this._key = new Uint8Array(key);\n    }\n\n    /**\n     * Encrypts and authenticates plaintext, authenticates associated data,\n     * and returns sealed ciphertext, which includes authentication tag.\n     *\n     * RFC7539 specifies 12 bytes for nonce. It may be this 12-byte nonce\n     * (\"IV\"), or full 16-byte counter (called \"32-bit fixed-common part\")\n     * and nonce.\n     *\n     * If dst is given (it must be the size of plaintext + the size of tag\n     * length) the result will be put into it. Dst and plaintext must not\n     * overlap.\n     */\n    seal(nonce: Uint8Array, plaintext: Uint8Array, associatedData?: Uint8Array,\n        dst?: Uint8Array): Uint8Array {\n        if (nonce.length > 16) {\n            throw new Error(\"ChaCha20Poly1305: incorrect nonce length\");\n        }\n\n        // Allocate space for counter, and set nonce as last bytes of it.\n        const counter = new Uint8Array(16);\n        counter.set(nonce, counter.length - nonce.length);\n\n        // Generate authentication key by taking first 32-bytes of stream.\n        // We pass full counter, which has 12-byte nonce and 4-byte block counter,\n        // and it will get incremented after generating the block, which is\n        // exactly what we need: we only use the first 32 bytes of 64-byte\n        // ChaCha block and discard the next 32 bytes.\n        const authKey = new Uint8Array(32);\n        stream(this._key, counter, authKey, 4);\n\n        // Allocate space for sealed ciphertext.\n        const resultLength = plaintext.length + this.tagLength;\n        let result;\n        if (dst) {\n            if (dst.length !== resultLength) {\n                throw new Error(\"ChaCha20Poly1305: incorrect destination length\");\n            }\n            result = dst;\n        } else {\n            result = new Uint8Array(resultLength);\n        }\n\n        // Encrypt plaintext.\n        streamXOR(this._key, counter, plaintext, result, 4);\n\n        // Authenticate.\n        // XXX: can \"simplify\" here: pass full result (which is already padded\n        // due to zeroes prepared for tag), and ciphertext length instead of\n        // subarray of result.\n        this._authenticate(result.subarray(result.length - this.tagLength, result.length),\n            authKey, result.subarray(0, result.length - this.tagLength), associatedData);\n\n        // Cleanup.\n        wipe(counter);\n\n        return result;\n    }\n\n    /**\n     * Authenticates sealed ciphertext (which includes authentication tag) and\n     * associated data, decrypts ciphertext and returns decrypted plaintext.\n     *\n     * RFC7539 specifies 12 bytes for nonce. It may be this 12-byte nonce\n     * (\"IV\"), or full 16-byte counter (called \"32-bit fixed-common part\")\n     * and nonce.\n     *\n     * If authentication fails, it returns null.\n     *\n     * If dst is given (it must be of ciphertext length minus tag length),\n     * the result will be put into it. Dst and plaintext must not overlap.\n     */\n    open(nonce: Uint8Array, sealed: Uint8Array, associatedData?: Uint8Array,\n        dst?: Uint8Array): Uint8Array | null {\n        if (nonce.length > 16) {\n            throw new Error(\"ChaCha20Poly1305: incorrect nonce length\");\n        }\n\n        // Sealed ciphertext should at least contain tag.\n        if (sealed.length < this.tagLength) {\n            // TODO(dchest): should we throw here instead?\n            return null;\n        }\n\n        // Allocate space for counter, and set nonce as last bytes of it.\n        const counter = new Uint8Array(16);\n        counter.set(nonce, counter.length - nonce.length);\n\n        // Generate authentication key by taking first 32-bytes of stream.\n        const authKey = new Uint8Array(32);\n        stream(this._key, counter, authKey, 4);\n\n        // Authenticate.\n        // XXX: can simplify and avoid allocation: since authenticate()\n        // already allocates tag (from Poly1305.digest(), it can return)\n        // it instead of copying to calculatedTag. But then in seal()\n        // we'll need to copy it.\n        const calculatedTag = new Uint8Array(this.tagLength);\n        this._authenticate(calculatedTag, authKey,\n            sealed.subarray(0, sealed.length - this.tagLength), associatedData);\n\n        // Constant-time compare tags and return null if they differ.\n        if (!equal(calculatedTag,\n            sealed.subarray(sealed.length - this.tagLength, sealed.length))) {\n            return null;\n        }\n\n        // Allocate space for decrypted plaintext.\n        const resultLength = sealed.length - this.tagLength;\n        let result;\n        if (dst) {\n            if (dst.length !== resultLength) {\n                throw new Error(\"ChaCha20Poly1305: incorrect destination length\");\n            }\n            result = dst;\n        } else {\n            result = new Uint8Array(resultLength);\n        }\n\n        // Decrypt.\n        streamXOR(this._key, counter,\n            sealed.subarray(0, sealed.length - this.tagLength), result, 4);\n\n        // Cleanup.\n        wipe(counter);\n\n        return result;\n    }\n\n    clean(): this {\n        wipe(this._key);\n        return this;\n    }\n\n    private _authenticate(tagOut: Uint8Array, authKey: Uint8Array,\n        ciphertext: Uint8Array, associatedData?: Uint8Array) {\n\n        // Initialize Poly1305 with authKey.\n        const h = new Poly1305(authKey);\n\n        // Authenticate padded associated data.\n        if (associatedData) {\n            h.update(associatedData);\n            if (associatedData.length % 16 > 0) {\n                h.update(ZEROS.subarray(associatedData.length % 16));\n            }\n        }\n\n        // Authenticate padded ciphertext.\n        h.update(ciphertext);\n        if (ciphertext.length % 16 > 0) {\n            h.update(ZEROS.subarray(ciphertext.length % 16));\n        }\n\n        // Authenticate length of associated data.\n        // XXX: can avoid allocation here?\n        const length = new Uint8Array(8);\n        if (associatedData) {\n            writeUint64LE(associatedData.length, length);\n        }\n        h.update(length);\n\n        // Authenticate length of ciphertext.\n        writeUint64LE(ciphertext.length, length);\n        h.update(length);\n\n        // Get tag and copy it into tagOut.\n        const tag = h.digest();\n        for (let i = 0; i < tag.length; i++) {\n            tagOut[i] = tag[i];\n        }\n\n        // Cleanup.\n        h.clean();\n        wipe(tag);\n        wipe(length);\n    }\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package hash provides interface for hash functions.\n */\n\n/**\n * Hash interface describes properties of\n * cryptographic hash functions.\n */\nexport interface Hash {\n    readonly digestLength: number;\n    readonly blockSize: number;\n    update(data: Uint8Array): this;\n    reset(): this;\n    finish(out: Uint8Array): this;\n    digest(): Uint8Array;\n    clean(): void;\n}\n\nexport interface SerializableHash extends Hash {\n    saveState(): any;\n    restoreState(savedState: any): this;\n    cleanSavedState(savedState: any): void;\n}\n\nexport function isSerializableHash(h: Hash): h is SerializableHash {\n    return (\n        typeof (h as SerializableHash).saveState !== \"undefined\" &&\n        typeof (h as SerializableHash).restoreState !== \"undefined\" &&\n        typeof (h as SerializableHash).cleanSavedState !== \"undefined\"\n    );\n}\n\n// TODO(dchest): figure out the standardized interface for XOF such as\n// SHAKE and BLAKE2X.\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package hmac implements HMAC algorithm.\n */\n\nimport { Hash, SerializableHash, isSerializableHash } from \"@stablelib/hash\";\nimport { equal as constantTimeEqual } from \"@stablelib/constant-time\";\nimport { wipe } from \"@stablelib/wipe\";\n\n/**\n *  HMAC implements hash-based message authentication algorithm.\n */\nexport class HMAC implements SerializableHash {\n    readonly blockSize: number;\n    readonly digestLength: number;\n\n    private _inner: Hash; // inner hash\n    private _outer: Hash; // outer hash\n\n    private _finished = false; // true if HMAC was finalized\n\n    // Copies of hash states after keying.\n    // Need for quick reset without hashing the key again.\n    private _innerKeyedState: any | undefined;\n    private _outerKeyedState: any | undefined;\n\n    /**\n     * Constructs a new HMAC with the given Hash and secret key.\n     */\n    constructor(hash: new () => Hash | SerializableHash, key: Uint8Array) {\n        // Initialize inner and outer hashes.\n        this._inner = new hash();\n        this._outer = new hash();\n\n        // Set block and digest sizes for this HMAC\n        // instance to values from the hash.\n        this.blockSize = this._outer.blockSize;\n        this.digestLength = this._outer.digestLength;\n\n        // Pad temporary stores a key (or its hash) padded with zeroes.\n        const pad = new Uint8Array(this.blockSize);\n\n        if (key.length > this.blockSize) {\n            // If key is bigger than hash block size, it must be\n            // hashed and this hash is used as a key instead.\n            this._inner.update(key).finish(pad).clean();\n        } else {\n            // Otherwise, copy the key into pad.\n            pad.set(key);\n        }\n\n        // Now two different keys are derived from padded key\n        // by xoring a different byte value to each.\n\n        // To make inner hash key, xor byte 0x36 into pad.\n        for (let i = 0; i < pad.length; i++) {\n            pad[i] ^= 0x36;\n        }\n        // Update inner hash with the result.\n        this._inner.update(pad);\n\n        // To make outer hash key, xor byte 0x5c into pad.\n        // But since we already xored 0x36 there, we must\n        // first undo this by xoring it again.\n        for (let i = 0; i < pad.length; i++) {\n            pad[i] ^= 0x36 ^ 0x5c;\n        }\n        // Update outer hash with the result.\n        this._outer.update(pad);\n\n        // Save states of both hashes, so that we can quickly restore\n        // them later in reset() without the need to remember the actual\n        // key and perform this initialization again.\n        if (isSerializableHash(this._inner) && isSerializableHash(this._outer)) {\n            this._innerKeyedState = this._inner.saveState();\n            this._outerKeyedState = this._outer.saveState();\n        }\n\n        // Clean pad.\n        wipe(pad);\n    }\n\n    /**\n     * Returns HMAC state to the state initialized with key\n     * to make it possible to run HMAC over the other data with the same\n     * key without creating a new instance.\n     */\n    reset(): this {\n        if (!isSerializableHash(this._inner) || !isSerializableHash(this._outer)) {\n            throw new Error(\"hmac: can't reset() because hash doesn't implement restoreState()\");\n        }\n        // Restore keyed states of inner and outer hashes.\n        this._inner.restoreState(this._innerKeyedState);\n        this._outer.restoreState(this._outerKeyedState);\n        this._finished = false;\n        return this;\n    }\n\n    /**\n     * Cleans HMAC state.\n     */\n    clean() {\n        if (isSerializableHash(this._inner)) {\n            this._inner.cleanSavedState(this._innerKeyedState);\n        }\n        if (isSerializableHash(this._outer)) {\n            this._outer.cleanSavedState(this._outerKeyedState);\n        }\n        this._inner.clean();\n        this._outer.clean();\n    }\n\n    /**\n     * Updates state with provided data.\n     */\n    update(data: Uint8Array): this {\n        this._inner.update(data);\n        return this;\n    }\n\n    /**\n     * Finalizes HMAC and puts the result in out.\n     */\n    finish(out: Uint8Array): this {\n        if (this._finished) {\n            // If HMAC was finalized, outer hash is also finalized,\n            // so it produces the same digest it produced when it\n            // was finalized.\n            this._outer.finish(out);\n            return this;\n        }\n\n        // Finalize inner hash and store the result temporarily.\n        this._inner.finish(out);\n\n        // Update outer hash with digest of inner hash and and finalize it.\n        this._outer.update(out.subarray(0, this.digestLength)).finish(out);\n        this._finished = true;\n\n        return this;\n    }\n\n    /**\n     * Returns the computed message authentication code.\n     */\n    digest(): Uint8Array {\n        const out = new Uint8Array(this.digestLength);\n        this.finish(out);\n        return out;\n    }\n\n    /**\n     * Saves HMAC state.\n     * This function is needed for PBKDF2 optimization.\n     */\n    saveState(): any {\n        if (!isSerializableHash(this._inner)) {\n            throw new Error(\"hmac: can't saveState() because hash doesn't implement it\");\n        }\n        return this._inner.saveState();\n    }\n\n    restoreState(savedState: any): this {\n        if (!isSerializableHash(this._inner) || !isSerializableHash(this._outer)) {\n            throw new Error(\"hmac: can't restoreState() because hash doesn't implement it\");\n        }\n        this._inner.restoreState(savedState);\n        this._outer.restoreState(this._outerKeyedState);\n        this._finished = false;\n        return this;\n    }\n\n    cleanSavedState(savedState: any) {\n        if (!isSerializableHash(this._inner)) {\n            throw new Error(\"hmac: can't cleanSavedState() because hash doesn't implement it\");\n        }\n        this._inner.cleanSavedState(savedState);\n    }\n}\n\n/**\n * Returns HMAC using the given hash constructor for the key over data.\n */\nexport function hmac(hash: new () => Hash, key: Uint8Array, data: Uint8Array): Uint8Array {\n    const h = new HMAC(hash, key);\n    h.update(data);\n    const digest = h.digest();\n    h.clean();\n    return digest;\n}\n\n/**\n * Returns true if two HMAC digests are equal.\n * Uses constant-time comparison to avoid leaking timing information.\n *\n * Example:\n *\n *    const receivedDigest = ...\n *    const realDigest = hmac(SHA256, key, data);\n *    if (!equal(receivedDigest, realDigest)) {\n *        throw new Error(\"Authentication error\");\n *    }\n */\nexport const equal = constantTimeEqual;\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package hkdf implements HKDF key derivation function.\n */\n\nimport { Hash } from \"@stablelib/hash\";\nimport { HMAC, hmac } from \"@stablelib/hmac\";\nimport { wipe } from \"@stablelib/wipe\";\n\n/**\n * HMAC-based Extract-and-Expand Key Derivation Function.\n *\n * Implements HKDF from RFC5869.\n *\n * Expands the given master key with salt and info into\n * a limited stream of key material.\n */\nexport class HKDF {\n    private _hmac: HMAC;\n    private _buffer: Uint8Array;\n    private _bufpos: number;\n    private _counter = new Uint8Array(1); // starts with zero\n    private _hash: new () => Hash;\n    private _info?: Uint8Array;\n\n    /**\n     * Create a new HKDF instance for the given hash function\n     * with the master key, optional salt, and info.\n     *\n     * - Master key is a high-entropy secret key (not a password).\n     * - Salt is a non-secret random value.\n     * - Info is application- and/or context-specific information.\n     */\n    constructor(hash: new () => Hash,\n        key: Uint8Array,\n        salt = new Uint8Array(0),\n        info?: Uint8Array) {\n\n        this._hash = hash;\n        this._info = info;\n\n        // HKDF-Extract uses salt as HMAC key, and key as data.\n        const okm = hmac(this._hash, salt, key);\n\n        // Initialize HMAC for expanding with extracted key.\n        this._hmac = new HMAC(hash, okm);\n\n        // Allocate buffer.\n        this._buffer = new Uint8Array(this._hmac.digestLength);\n        this._bufpos = this._buffer.length;\n    }\n\n    // Fill buffer with new block of HKDF-Extract output.\n    private _fillBuffer(): void {\n        // Increment counter.\n        this._counter[0]++;\n\n        const ctr = this._counter[0];\n\n        // Check if counter overflowed.\n        if (ctr === 0) {\n            throw new Error(\"hkdf: cannot expand more\");\n        }\n\n        // Prepare HMAC instance for new data with old key.\n        this._hmac.reset();\n\n        // Hash in previous output if it was generated\n        // (i.e. counter is greater than 1).\n        if (ctr > 1) {\n            this._hmac.update(this._buffer);\n        }\n\n        // Hash in info if it exists.\n        if (this._info) {\n            this._hmac.update(this._info);\n        }\n\n        // Hash in the counter.\n        this._hmac.update(this._counter);\n\n        // Output result to buffer and clean HMAC instance.\n        this._hmac.finish(this._buffer);\n\n        // Reset buffer position.\n        this._bufpos = 0;\n    }\n\n    /**\n     * Expand returns next key material of the given length.\n     *\n     * It throws if expansion limit is reached (which is\n     * 254 digests of the underlying HMAC function).\n     */\n    expand(length: number): Uint8Array {\n        const out = new Uint8Array(length);\n        for (let i = 0; i < out.length; i++) {\n            if (this._bufpos === this._buffer.length) {\n                this._fillBuffer();\n            }\n            out[i] = this._buffer[this._bufpos++];\n        }\n        return out;\n    }\n\n    clean(): void {\n        this._hmac.clean();\n        wipe(this._buffer);\n        wipe(this._counter);\n        this._bufpos = 0;\n    }\n}\n\n// TODO(dchest): maybe implement deriveKey?\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\nimport { RandomSource } from \"./\";\n\nconst QUOTA = 65536;\n\nexport class BrowserRandomSource implements RandomSource {\n    isAvailable = false;\n    isInstantiated = false;\n\n    private _crypto?: { getRandomValues: typeof window.crypto.getRandomValues };\n\n    constructor() {\n        const browserCrypto = typeof self !== 'undefined'\n            ? (self.crypto || (self as { msCrypto?: any }).msCrypto) // IE11 has msCrypto\n            : null;\n\n        if (browserCrypto && browserCrypto.getRandomValues !== undefined) {\n            this._crypto = browserCrypto;\n            this.isAvailable = true;\n            this.isInstantiated = true;\n        }\n    }\n\n    randomBytes(length: number): Uint8Array {\n        if (!this.isAvailable || !this._crypto) {\n            throw new Error(\"Browser random byte generator is not available.\");\n        }\n        const out = new Uint8Array(length);\n        for (let i = 0; i < out.length; i += QUOTA) {\n            this._crypto.getRandomValues(out.subarray(i, i + Math.min(out.length - i, QUOTA)));\n        }\n        return out;\n    }\n}\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"crypto\" has been externalized for browser compatibility. Cannot access \"crypto.${key}\" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\nimport { RandomSource } from \"./\";\nimport { wipe } from \"@stablelib/wipe\";\n\ndeclare function require(name: string): any;\n\nexport class NodeRandomSource implements RandomSource {\n    isAvailable = false;\n    isInstantiated = false;\n\n    private _crypto: { randomBytes(n: number): Uint8Array } | undefined;\n\n    constructor() {\n        if (typeof require !== \"undefined\") {\n            const nodeCrypto = require(\"crypto\");\n            if (nodeCrypto && nodeCrypto.randomBytes) {\n                this._crypto = nodeCrypto;\n                this.isAvailable = true;\n                this.isInstantiated = true;\n            }\n        }\n    }\n\n    randomBytes(length: number): Uint8Array {\n        if (!this.isAvailable || !this._crypto) {\n            throw new Error(\"Node.js random byte generator is not available.\");\n        }\n\n        // Get random bytes (result is Buffer).\n        let buffer = this._crypto.randomBytes(length);\n\n        // Make sure we got the length that we requested.\n        if (buffer.length !== length) {\n            throw new Error(\"NodeRandomSource: got fewer bytes than requested\");\n        }\n\n        // Allocate output array.\n        const out = new Uint8Array(length);\n\n        // Copy bytes from buffer to output.\n        for (let i = 0; i < out.length; i++) {\n            out[i] = buffer[i];\n        }\n\n        // Cleanup.\n        wipe(buffer);\n\n        return out;\n    }\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\nimport { RandomSource } from \"./\";\nimport { BrowserRandomSource } from \"./browser\";\nimport { NodeRandomSource } from \"./node\";\n\nexport class SystemRandomSource implements RandomSource {\n    isAvailable = false;\n    name = \"\";\n    private _source: RandomSource;\n\n    constructor() {\n        // Try browser.\n        this._source = new BrowserRandomSource();\n        if (this._source.isAvailable) {\n            this.isAvailable = true;\n            this.name = \"Browser\";\n            return;\n        }\n\n        // If no browser source, try Node.\n        this._source = new NodeRandomSource();\n        if (this._source.isAvailable) {\n            this.isAvailable = true;\n            this.name = \"Node\";\n            return;\n        }\n\n        // No sources, we're out of options.\n    }\n\n    randomBytes(length: number): Uint8Array {\n        if (!this.isAvailable) {\n            throw new Error(\"System random byte generator is not available.\");\n        }\n        return this._source.randomBytes(length);\n    }\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package random provides functions to access system's\n * cryptographically secure random byte generator.\n */\n\nimport { RandomSource } from \"./source\";\nimport { SystemRandomSource } from \"./source/system\";\nimport { readUint32LE } from \"@stablelib/binary\";\nimport { wipe } from \"@stablelib/wipe\";\n\nexport { RandomSource } from \"./source\";\n\nexport const defaultRandomSource = new SystemRandomSource();\n\nexport function randomBytes(length: number, prng: RandomSource = defaultRandomSource): Uint8Array {\n    return prng.randomBytes(length);\n}\n\n/**\n * Returns a uniformly random unsigned 32-bit integer.\n */\nexport function randomUint32(prng: RandomSource = defaultRandomSource): number {\n    // Generate 4-byte random buffer.\n    const buf = randomBytes(4, prng);\n\n    // Convert bytes from buffer into a 32-bit integer.\n    // It's not important which byte order to use, since\n    // the result is random.\n    const result = readUint32LE(buf);\n\n    // Clean the buffer.\n    wipe(buf);\n\n    return result;\n}\n\n/** 62 alphanumeric characters for default charset of randomString() */\nconst ALPHANUMERIC = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\";\n\n/**\n * Returns a uniform random string of the given length\n * with characters from the given charset.\n *\n * Charset must not have more than 256 characters.\n *\n * Default charset generates case-sensitive alphanumeric\n * strings (0-9, A-Z, a-z).\n */\nexport function randomString(\n    length: number,\n    charset = ALPHANUMERIC,\n    prng: RandomSource = defaultRandomSource\n): string {\n    if (charset.length < 2) {\n        throw new Error(\"randomString charset is too short\");\n    }\n    if (charset.length > 256) {\n        throw new Error(\"randomString charset is too long\");\n    }\n    let out = '';\n    const charsLen = charset.length;\n    const maxByte = 256 - (256 % charsLen);\n    while (length > 0) {\n        const buf = randomBytes(Math.ceil(length * 256 / maxByte), prng);\n        for (let i = 0; i < buf.length && length > 0; i++) {\n            const randomByte = buf[i];\n            if (randomByte < maxByte) {\n                out += charset.charAt(randomByte % charsLen);\n                length--;\n            }\n        }\n        wipe(buf);\n    }\n    return out;\n}\n\n/**\n * Returns uniform random string containing at least the given\n * number of bits of entropy.\n *\n * For example, randomStringForEntropy(128) will return a 22-character\n * alphanumeric string, while randomStringForEntropy(128, \"0123456789\")\n * will return a 39-character numeric string, both will contain at\n * least 128 bits of entropy.\n *\n * Default charset generates case-sensitive alphanumeric\n * strings (0-9, A-Z, a-z).\n */\nexport function randomStringForEntropy(\n    bits: number,\n    charset = ALPHANUMERIC,\n    prng: RandomSource = defaultRandomSource\n): string {\n    const length = Math.ceil(bits / (Math.log(charset.length) / Math.LN2));\n    return randomString(length, charset, prng);\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package sha256 implements SHA-2-256 cryptographic hash function.\n */\n\nimport { SerializableHash } from \"@stablelib/hash\";\nimport { readUint32BE, writeUint32BE } from \"@stablelib/binary\";\nimport { wipe } from \"@stablelib/wipe\";\n\nexport const DIGEST_LENGTH = 32;\nexport const BLOCK_SIZE = 64;\n\n/**\n * SHA2-256 cryptographic hash algorithm.\n */\nexport class SHA256 implements SerializableHash {\n    /** Length of hash output */\n    readonly digestLength: number = DIGEST_LENGTH;\n\n    /** Block size */\n    readonly blockSize: number = BLOCK_SIZE;\n\n    // Note: Int32Array is used instead of Uint32Array for performance reasons.\n    protected _state = new Int32Array(8); // hash state\n    private _temp = new Int32Array(64); // temporary state\n    private _buffer = new Uint8Array(128); // buffer for data to hash\n    private _bufferLength = 0; // number of bytes in buffer\n    private _bytesHashed = 0; // number of total bytes hashed\n    private _finished = false; // indicates whether the hash was finalized\n\n    constructor() {\n        this.reset();\n    }\n\n    protected _initState() {\n        this._state[0] = 0x6a09e667;\n        this._state[1] = 0xbb67ae85;\n        this._state[2] = 0x3c6ef372;\n        this._state[3] = 0xa54ff53a;\n        this._state[4] = 0x510e527f;\n        this._state[5] = 0x9b05688c;\n        this._state[6] = 0x1f83d9ab;\n        this._state[7] = 0x5be0cd19;\n    }\n\n    /**\n     * Resets hash state making it possible\n     * to re-use this instance to hash other data.\n     */\n    reset(): this {\n        this._initState();\n        this._bufferLength = 0;\n        this._bytesHashed = 0;\n        this._finished = false;\n        return this;\n    }\n\n    /**\n     * Cleans internal buffers and resets hash state.\n     */\n    clean() {\n        wipe(this._buffer);\n        wipe(this._temp);\n        this.reset();\n    }\n\n    /**\n     * Updates hash state with the given data.\n     *\n     * Throws error when trying to update already finalized hash:\n     * instance must be reset to update it again.\n     */\n    update(data: Uint8Array, dataLength: number = data.length): this {\n        if (this._finished) {\n            throw new Error(\"SHA256: can't update because hash was finished.\");\n        }\n        let dataPos = 0;\n        this._bytesHashed += dataLength;\n        if (this._bufferLength > 0) {\n            while (this._bufferLength < this.blockSize && dataLength > 0) {\n                this._buffer[this._bufferLength++] = data[dataPos++];\n                dataLength--;\n            }\n            if (this._bufferLength === this.blockSize) {\n                hashBlocks(this._temp, this._state, this._buffer, 0, this.blockSize);\n                this._bufferLength = 0;\n            }\n        }\n        if (dataLength >= this.blockSize) {\n            dataPos = hashBlocks(this._temp, this._state, data, dataPos, dataLength);\n            dataLength %= this.blockSize;\n        }\n        while (dataLength > 0) {\n            this._buffer[this._bufferLength++] = data[dataPos++];\n            dataLength--;\n        }\n        return this;\n    }\n\n    /**\n     * Finalizes hash state and puts hash into out.\n     * If hash was already finalized, puts the same value.\n     */\n    finish(out: Uint8Array): this {\n        if (!this._finished) {\n            const bytesHashed = this._bytesHashed;\n            const left = this._bufferLength;\n            const bitLenHi = (bytesHashed / 0x20000000) | 0;\n            const bitLenLo = bytesHashed << 3;\n            const padLength = (bytesHashed % 64 < 56) ? 64 : 128;\n\n            this._buffer[left] = 0x80;\n            for (let i = left + 1; i < padLength - 8; i++) {\n                this._buffer[i] = 0;\n            }\n            writeUint32BE(bitLenHi, this._buffer, padLength - 8);\n            writeUint32BE(bitLenLo, this._buffer, padLength - 4);\n\n            hashBlocks(this._temp, this._state, this._buffer, 0, padLength);\n\n            this._finished = true;\n        }\n\n        for (let i = 0; i < this.digestLength / 4; i++) {\n            writeUint32BE(this._state[i], out, i * 4);\n        }\n\n        return this;\n    }\n\n    /**\n     * Returns the final hash digest.\n     */\n    digest(): Uint8Array {\n        const out = new Uint8Array(this.digestLength);\n        this.finish(out);\n        return out;\n    }\n\n    /**\n     * Function useful for HMAC/PBKDF2 optimization.\n     * Returns hash state to be used with restoreState().\n     * Only chain value is saved, not buffers or other\n     * state variables.\n     */\n    saveState(): SavedState {\n        if (this._finished) {\n            throw new Error(\"SHA256: cannot save finished state\");\n        }\n        return {\n            state: new Int32Array(this._state),\n            buffer: this._bufferLength > 0 ? new Uint8Array(this._buffer) : undefined,\n            bufferLength: this._bufferLength,\n            bytesHashed: this._bytesHashed\n        };\n    }\n\n    /**\n     * Function useful for HMAC/PBKDF2 optimization.\n     * Restores state saved by saveState() and sets bytesHashed\n     * to the given value.\n     */\n    restoreState(savedState: SavedState): this {\n        this._state.set(savedState.state);\n        this._bufferLength = savedState.bufferLength;\n        if (savedState.buffer) {\n            this._buffer.set(savedState.buffer);\n        }\n        this._bytesHashed = savedState.bytesHashed;\n        this._finished = false;\n        return this;\n    }\n\n    /**\n     * Cleans state returned by saveState().\n     */\n    cleanSavedState(savedState: SavedState) {\n        wipe(savedState.state);\n        if (savedState.buffer) {\n            wipe(savedState.buffer);\n        }\n        savedState.bufferLength = 0;\n        savedState.bytesHashed = 0;\n    }\n}\n\nexport type SavedState = {\n    state: Int32Array;\n    buffer: Uint8Array | undefined;\n    bufferLength: number;\n    bytesHashed: number;\n};\n\n// Constants\nconst K = new Int32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b,\n    0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01,\n    0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7,\n    0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc,\n    0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152,\n    0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147,\n    0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc,\n    0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819,\n    0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08,\n    0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f,\n    0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208,\n    0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n\nfunction hashBlocks(w: Int32Array, v: Int32Array, p: Uint8Array, pos: number, len: number): number {\n    while (len >= 64) {\n        let a = v[0];\n        let b = v[1];\n        let c = v[2];\n        let d = v[3];\n        let e = v[4];\n        let f = v[5];\n        let g = v[6];\n        let h = v[7];\n\n        for (let i = 0; i < 16; i++) {\n            let j = pos + i * 4;\n            w[i] = readUint32BE(p, j);\n        }\n\n        for (let i = 16; i < 64; i++) {\n            let u = w[i - 2];\n            let t1 = (u >>> 17 | u << (32 - 17)) ^ (u >>> 19 | u << (32 - 19)) ^ (u >>> 10);\n\n            u = w[i - 15];\n            let t2 = (u >>> 7 | u << (32 - 7)) ^ (u >>> 18 | u << (32 - 18)) ^ (u >>> 3);\n\n            w[i] = (t1 + w[i - 7] | 0) + (t2 + w[i - 16] | 0);\n        }\n\n        for (let i = 0; i < 64; i++) {\n            let t1 = (((((e >>> 6 | e << (32 - 6)) ^ (e >>> 11 | e << (32 - 11)) ^\n                (e >>> 25 | e << (32 - 25))) + ((e & f) ^ (~e & g))) | 0) +\n                ((h + ((K[i] + w[i]) | 0)) | 0)) | 0;\n\n            let t2 = (((a >>> 2 | a << (32 - 2)) ^ (a >>> 13 | a << (32 - 13)) ^\n                (a >>> 22 | a << (32 - 22))) + ((a & b) ^ (a & c) ^ (b & c))) | 0;\n\n            h = g;\n            g = f;\n            f = e;\n            e = (d + t1) | 0;\n            d = c;\n            c = b;\n            b = a;\n            a = (t1 + t2) | 0;\n        }\n\n        v[0] += a;\n        v[1] += b;\n        v[2] += c;\n        v[3] += d;\n        v[4] += e;\n        v[5] += f;\n        v[6] += g;\n        v[7] += h;\n\n        pos += 64;\n        len -= 64;\n    }\n    return pos;\n}\n\nexport function hash(data: Uint8Array): Uint8Array {\n    const h = new SHA256();\n    h.update(data);\n    const digest = h.digest();\n    h.clean();\n    return digest;\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package x25519 implements X25519 key agreement.\n */\n\nimport { randomBytes, RandomSource } from \"@stablelib/random\";\nimport { wipe } from \"@stablelib/wipe\";\n\nexport const PUBLIC_KEY_LENGTH = 32;\nexport const SECRET_KEY_LENGTH = 32;\nexport const SHARED_KEY_LENGTH = 32;\n\n// TODO(dchest): some functions are copies of ../sign/ed25519.\n// Find a way to combine them without opening up to public.\n\n// Ported from TweetNaCl.js, which is ported from TweetNaCl\n// by <PERSON> and <PERSON>.\n// Public domain.\n// https://tweetnacl.js.org\n\n// TweetNaCl contributors:\n// <PERSON>, <PERSON>, <PERSON>,\n// <PERSON><PERSON>, <PERSON>, Sja<PERSON>.\n// Public domain.\n// https://tweetnacl.cr.yp.to/\n\ntype GF = Float64Array;\n\n// Returns new zero-filled 16-element GF (Float64Array).\n// If passed an array of numbers, prefills the returned\n// array with them.\n//\n// We use Float64Array, because we need 48-bit numbers\n// for this implementation.\nfunction gf(init?: number[]): GF {\n    const r = new Float64Array(16);\n    if (init) {\n        for (let i = 0; i < init.length; i++) {\n            r[i] = init[i];\n        }\n    }\n    return r;\n}\n\n// Base point.\nconst _9 = new Uint8Array(32); _9[0] = 9;\n\nconst _121665 = gf([0xdb41, 1]);\n\nfunction car25519(o: GF) {\n    let c = 1;\n    for (let i = 0; i < 16; i++) {\n        let v = o[i] + c + 65535;\n        c = Math.floor(v / 65536);\n        o[i] = v - c * 65536;\n    }\n    o[0] += c - 1 + 37 * (c - 1);\n}\n\nfunction sel25519(p: GF, q: GF, b: number) {\n    const c = ~(b - 1);\n    for (let i = 0; i < 16; i++) {\n        const t = c & (p[i] ^ q[i]);\n        p[i] ^= t;\n        q[i] ^= t;\n    }\n}\n\nfunction pack25519(o: Uint8Array, n: GF) {\n    const m = gf();\n    const t = gf();\n    for (let i = 0; i < 16; i++) {\n        t[i] = n[i];\n    }\n    car25519(t);\n    car25519(t);\n    car25519(t);\n    for (let j = 0; j < 2; j++) {\n        m[0] = t[0] - 0xffed;\n        for (let i = 1; i < 15; i++) {\n            m[i] = t[i] - 0xffff - ((m[i - 1] >> 16) & 1);\n            m[i - 1] &= 0xffff;\n        }\n        m[15] = t[15] - 0x7fff - ((m[14] >> 16) & 1);\n        const b = (m[15] >> 16) & 1;\n        m[14] &= 0xffff;\n        sel25519(t, m, 1 - b);\n    }\n    for (let i = 0; i < 16; i++) {\n        o[2 * i] = t[i] & 0xff;\n        o[2 * i + 1] = t[i] >> 8;\n    }\n}\n\nfunction unpack25519(o: GF, n: Uint8Array) {\n    for (let i = 0; i < 16; i++) {\n        o[i] = n[2 * i] + (n[2 * i + 1] << 8);\n    }\n    o[15] &= 0x7fff;\n}\n\nfunction add(o: GF, a: GF, b: GF) {\n    for (let i = 0; i < 16; i++) {\n        o[i] = a[i] + b[i];\n    }\n}\n\nfunction sub(o: GF, a: GF, b: GF) {\n    for (let i = 0; i < 16; i++) {\n        o[i] = a[i] - b[i];\n    }\n}\n\nfunction mul(o: GF, a: GF, b: GF) {\n    let v: number, c: number,\n        t0 = 0, t1 = 0, t2 = 0, t3 = 0, t4 = 0, t5 = 0, t6 = 0, t7 = 0,\n        t8 = 0, t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n        t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n        t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n        b0 = b[0],\n        b1 = b[1],\n        b2 = b[2],\n        b3 = b[3],\n        b4 = b[4],\n        b5 = b[5],\n        b6 = b[6],\n        b7 = b[7],\n        b8 = b[8],\n        b9 = b[9],\n        b10 = b[10],\n        b11 = b[11],\n        b12 = b[12],\n        b13 = b[13],\n        b14 = b[14],\n        b15 = b[15];\n\n    v = a[0];\n    t0 += v * b0;\n    t1 += v * b1;\n    t2 += v * b2;\n    t3 += v * b3;\n    t4 += v * b4;\n    t5 += v * b5;\n    t6 += v * b6;\n    t7 += v * b7;\n    t8 += v * b8;\n    t9 += v * b9;\n    t10 += v * b10;\n    t11 += v * b11;\n    t12 += v * b12;\n    t13 += v * b13;\n    t14 += v * b14;\n    t15 += v * b15;\n    v = a[1];\n    t1 += v * b0;\n    t2 += v * b1;\n    t3 += v * b2;\n    t4 += v * b3;\n    t5 += v * b4;\n    t6 += v * b5;\n    t7 += v * b6;\n    t8 += v * b7;\n    t9 += v * b8;\n    t10 += v * b9;\n    t11 += v * b10;\n    t12 += v * b11;\n    t13 += v * b12;\n    t14 += v * b13;\n    t15 += v * b14;\n    t16 += v * b15;\n    v = a[2];\n    t2 += v * b0;\n    t3 += v * b1;\n    t4 += v * b2;\n    t5 += v * b3;\n    t6 += v * b4;\n    t7 += v * b5;\n    t8 += v * b6;\n    t9 += v * b7;\n    t10 += v * b8;\n    t11 += v * b9;\n    t12 += v * b10;\n    t13 += v * b11;\n    t14 += v * b12;\n    t15 += v * b13;\n    t16 += v * b14;\n    t17 += v * b15;\n    v = a[3];\n    t3 += v * b0;\n    t4 += v * b1;\n    t5 += v * b2;\n    t6 += v * b3;\n    t7 += v * b4;\n    t8 += v * b5;\n    t9 += v * b6;\n    t10 += v * b7;\n    t11 += v * b8;\n    t12 += v * b9;\n    t13 += v * b10;\n    t14 += v * b11;\n    t15 += v * b12;\n    t16 += v * b13;\n    t17 += v * b14;\n    t18 += v * b15;\n    v = a[4];\n    t4 += v * b0;\n    t5 += v * b1;\n    t6 += v * b2;\n    t7 += v * b3;\n    t8 += v * b4;\n    t9 += v * b5;\n    t10 += v * b6;\n    t11 += v * b7;\n    t12 += v * b8;\n    t13 += v * b9;\n    t14 += v * b10;\n    t15 += v * b11;\n    t16 += v * b12;\n    t17 += v * b13;\n    t18 += v * b14;\n    t19 += v * b15;\n    v = a[5];\n    t5 += v * b0;\n    t6 += v * b1;\n    t7 += v * b2;\n    t8 += v * b3;\n    t9 += v * b4;\n    t10 += v * b5;\n    t11 += v * b6;\n    t12 += v * b7;\n    t13 += v * b8;\n    t14 += v * b9;\n    t15 += v * b10;\n    t16 += v * b11;\n    t17 += v * b12;\n    t18 += v * b13;\n    t19 += v * b14;\n    t20 += v * b15;\n    v = a[6];\n    t6 += v * b0;\n    t7 += v * b1;\n    t8 += v * b2;\n    t9 += v * b3;\n    t10 += v * b4;\n    t11 += v * b5;\n    t12 += v * b6;\n    t13 += v * b7;\n    t14 += v * b8;\n    t15 += v * b9;\n    t16 += v * b10;\n    t17 += v * b11;\n    t18 += v * b12;\n    t19 += v * b13;\n    t20 += v * b14;\n    t21 += v * b15;\n    v = a[7];\n    t7 += v * b0;\n    t8 += v * b1;\n    t9 += v * b2;\n    t10 += v * b3;\n    t11 += v * b4;\n    t12 += v * b5;\n    t13 += v * b6;\n    t14 += v * b7;\n    t15 += v * b8;\n    t16 += v * b9;\n    t17 += v * b10;\n    t18 += v * b11;\n    t19 += v * b12;\n    t20 += v * b13;\n    t21 += v * b14;\n    t22 += v * b15;\n    v = a[8];\n    t8 += v * b0;\n    t9 += v * b1;\n    t10 += v * b2;\n    t11 += v * b3;\n    t12 += v * b4;\n    t13 += v * b5;\n    t14 += v * b6;\n    t15 += v * b7;\n    t16 += v * b8;\n    t17 += v * b9;\n    t18 += v * b10;\n    t19 += v * b11;\n    t20 += v * b12;\n    t21 += v * b13;\n    t22 += v * b14;\n    t23 += v * b15;\n    v = a[9];\n    t9 += v * b0;\n    t10 += v * b1;\n    t11 += v * b2;\n    t12 += v * b3;\n    t13 += v * b4;\n    t14 += v * b5;\n    t15 += v * b6;\n    t16 += v * b7;\n    t17 += v * b8;\n    t18 += v * b9;\n    t19 += v * b10;\n    t20 += v * b11;\n    t21 += v * b12;\n    t22 += v * b13;\n    t23 += v * b14;\n    t24 += v * b15;\n    v = a[10];\n    t10 += v * b0;\n    t11 += v * b1;\n    t12 += v * b2;\n    t13 += v * b3;\n    t14 += v * b4;\n    t15 += v * b5;\n    t16 += v * b6;\n    t17 += v * b7;\n    t18 += v * b8;\n    t19 += v * b9;\n    t20 += v * b10;\n    t21 += v * b11;\n    t22 += v * b12;\n    t23 += v * b13;\n    t24 += v * b14;\n    t25 += v * b15;\n    v = a[11];\n    t11 += v * b0;\n    t12 += v * b1;\n    t13 += v * b2;\n    t14 += v * b3;\n    t15 += v * b4;\n    t16 += v * b5;\n    t17 += v * b6;\n    t18 += v * b7;\n    t19 += v * b8;\n    t20 += v * b9;\n    t21 += v * b10;\n    t22 += v * b11;\n    t23 += v * b12;\n    t24 += v * b13;\n    t25 += v * b14;\n    t26 += v * b15;\n    v = a[12];\n    t12 += v * b0;\n    t13 += v * b1;\n    t14 += v * b2;\n    t15 += v * b3;\n    t16 += v * b4;\n    t17 += v * b5;\n    t18 += v * b6;\n    t19 += v * b7;\n    t20 += v * b8;\n    t21 += v * b9;\n    t22 += v * b10;\n    t23 += v * b11;\n    t24 += v * b12;\n    t25 += v * b13;\n    t26 += v * b14;\n    t27 += v * b15;\n    v = a[13];\n    t13 += v * b0;\n    t14 += v * b1;\n    t15 += v * b2;\n    t16 += v * b3;\n    t17 += v * b4;\n    t18 += v * b5;\n    t19 += v * b6;\n    t20 += v * b7;\n    t21 += v * b8;\n    t22 += v * b9;\n    t23 += v * b10;\n    t24 += v * b11;\n    t25 += v * b12;\n    t26 += v * b13;\n    t27 += v * b14;\n    t28 += v * b15;\n    v = a[14];\n    t14 += v * b0;\n    t15 += v * b1;\n    t16 += v * b2;\n    t17 += v * b3;\n    t18 += v * b4;\n    t19 += v * b5;\n    t20 += v * b6;\n    t21 += v * b7;\n    t22 += v * b8;\n    t23 += v * b9;\n    t24 += v * b10;\n    t25 += v * b11;\n    t26 += v * b12;\n    t27 += v * b13;\n    t28 += v * b14;\n    t29 += v * b15;\n    v = a[15];\n    t15 += v * b0;\n    t16 += v * b1;\n    t17 += v * b2;\n    t18 += v * b3;\n    t19 += v * b4;\n    t20 += v * b5;\n    t21 += v * b6;\n    t22 += v * b7;\n    t23 += v * b8;\n    t24 += v * b9;\n    t25 += v * b10;\n    t26 += v * b11;\n    t27 += v * b12;\n    t28 += v * b13;\n    t29 += v * b14;\n    t30 += v * b15;\n\n    t0 += 38 * t16;\n    t1 += 38 * t17;\n    t2 += 38 * t18;\n    t3 += 38 * t19;\n    t4 += 38 * t20;\n    t5 += 38 * t21;\n    t6 += 38 * t22;\n    t7 += 38 * t23;\n    t8 += 38 * t24;\n    t9 += 38 * t25;\n    t10 += 38 * t26;\n    t11 += 38 * t27;\n    t12 += 38 * t28;\n    t13 += 38 * t29;\n    t14 += 38 * t30;\n    // t15 left as is\n\n    // first car\n    c = 1;\n    v = t0 + c + 65535; c = Math.floor(v / 65536); t0 = v - c * 65536;\n    v = t1 + c + 65535; c = Math.floor(v / 65536); t1 = v - c * 65536;\n    v = t2 + c + 65535; c = Math.floor(v / 65536); t2 = v - c * 65536;\n    v = t3 + c + 65535; c = Math.floor(v / 65536); t3 = v - c * 65536;\n    v = t4 + c + 65535; c = Math.floor(v / 65536); t4 = v - c * 65536;\n    v = t5 + c + 65535; c = Math.floor(v / 65536); t5 = v - c * 65536;\n    v = t6 + c + 65535; c = Math.floor(v / 65536); t6 = v - c * 65536;\n    v = t7 + c + 65535; c = Math.floor(v / 65536); t7 = v - c * 65536;\n    v = t8 + c + 65535; c = Math.floor(v / 65536); t8 = v - c * 65536;\n    v = t9 + c + 65535; c = Math.floor(v / 65536); t9 = v - c * 65536;\n    v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n    v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n    v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n    v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n    v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n    v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n    t0 += c - 1 + 37 * (c - 1);\n\n    // second car\n    c = 1;\n    v = t0 + c + 65535; c = Math.floor(v / 65536); t0 = v - c * 65536;\n    v = t1 + c + 65535; c = Math.floor(v / 65536); t1 = v - c * 65536;\n    v = t2 + c + 65535; c = Math.floor(v / 65536); t2 = v - c * 65536;\n    v = t3 + c + 65535; c = Math.floor(v / 65536); t3 = v - c * 65536;\n    v = t4 + c + 65535; c = Math.floor(v / 65536); t4 = v - c * 65536;\n    v = t5 + c + 65535; c = Math.floor(v / 65536); t5 = v - c * 65536;\n    v = t6 + c + 65535; c = Math.floor(v / 65536); t6 = v - c * 65536;\n    v = t7 + c + 65535; c = Math.floor(v / 65536); t7 = v - c * 65536;\n    v = t8 + c + 65535; c = Math.floor(v / 65536); t8 = v - c * 65536;\n    v = t9 + c + 65535; c = Math.floor(v / 65536); t9 = v - c * 65536;\n    v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n    v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n    v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n    v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n    v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n    v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n    t0 += c - 1 + 37 * (c - 1);\n\n    o[0] = t0;\n    o[1] = t1;\n    o[2] = t2;\n    o[3] = t3;\n    o[4] = t4;\n    o[5] = t5;\n    o[6] = t6;\n    o[7] = t7;\n    o[8] = t8;\n    o[9] = t9;\n    o[10] = t10;\n    o[11] = t11;\n    o[12] = t12;\n    o[13] = t13;\n    o[14] = t14;\n    o[15] = t15;\n}\n\nfunction square(o: GF, a: GF) {\n    mul(o, a, a);\n}\n\nfunction inv25519(o: GF, inp: GF) {\n    const c = gf();\n    for (let i = 0; i < 16; i++) {\n        c[i] = inp[i];\n    }\n    for (let i = 253; i >= 0; i--) {\n        square(c, c);\n        if (i !== 2 && i !== 4) {\n            mul(c, c, inp);\n        }\n    }\n    for (let i = 0; i < 16; i++) {\n        o[i] = c[i];\n    }\n}\n\nexport function scalarMult(n: Uint8Array, p: Uint8Array): Uint8Array {\n    const z = new Uint8Array(32);\n    const x = new Float64Array(80);\n    const a = gf(), b = gf(), c = gf(),\n        d = gf(), e = gf(), f = gf();\n\n    for (let i = 0; i < 31; i++) {\n        z[i] = n[i];\n    }\n    z[31] = (n[31] & 127) | 64;\n    z[0] &= 248;\n\n    unpack25519(x, p);\n\n    for (let i = 0; i < 16; i++) {\n        b[i] = x[i];\n    }\n\n    a[0] = d[0] = 1;\n\n    for (let i = 254; i >= 0; --i) {\n        const r = (z[i >>> 3] >>> (i & 7)) & 1;\n        sel25519(a, b, r);\n        sel25519(c, d, r);\n        add(e, a, c);\n        sub(a, a, c);\n        add(c, b, d);\n        sub(b, b, d);\n        square(d, e);\n        square(f, a);\n        mul(a, c, a);\n        mul(c, b, e);\n        add(e, a, c);\n        sub(a, a, c);\n        square(b, a);\n        sub(c, d, f);\n        mul(a, c, _121665);\n        add(a, a, d);\n        mul(c, c, a);\n        mul(a, d, f);\n        mul(d, b, x);\n        square(b, e);\n        sel25519(a, b, r);\n        sel25519(c, d, r);\n    }\n    for (let i = 0; i < 16; i++) {\n        x[i + 16] = a[i];\n        x[i + 32] = c[i];\n        x[i + 48] = b[i];\n        x[i + 64] = d[i];\n    }\n    const x32 = x.subarray(32);\n    const x16 = x.subarray(16);\n    inv25519(x32, x32);\n    mul(x16, x16, x32);\n    const q = new Uint8Array(32);\n    pack25519(q, x16);\n    return q;\n}\n\nexport function scalarMultBase(n: Uint8Array): Uint8Array {\n    return scalarMult(n, _9);\n}\n\nexport interface KeyPair {\n    publicKey: Uint8Array;\n    secretKey: Uint8Array;\n}\n\nexport function generateKeyPairFromSeed(seed: Uint8Array): KeyPair {\n    if (seed.length !== SECRET_KEY_LENGTH) {\n        throw new Error(`x25519: seed must be ${SECRET_KEY_LENGTH} bytes`);\n    }\n    const secretKey = new Uint8Array(seed);\n    const publicKey = scalarMultBase(secretKey);\n    return {\n        publicKey,\n        secretKey\n    };\n}\n\nexport function generateKeyPair(prng?: RandomSource): KeyPair {\n    const seed = randomBytes(32, prng);\n    const result = generateKeyPairFromSeed(seed);\n    wipe(seed);\n    return result;\n}\n\n/**\n * Returns a shared key between our secret key and a peer's public key.\n *\n * Throws an error if the given keys are of wrong length.\n *\n * If rejectZero is true throws if the calculated shared key is all-zero.\n * From RFC 7748:\n *\n * > Protocol designers using Diffie-Hellman over the curves defined in\n * > this document must not assume \"contributory behavior\".  Specially,\n * > contributory behavior means that both parties' private keys\n * > contribute to the resulting shared key.  Since curve25519 and\n * > curve448 have cofactors of 8 and 4 (respectively), an input point of\n * > small order will eliminate any contribution from the other party's\n * > private key.  This situation can be detected by checking for the all-\n * > zero output, which implementations MAY do, as specified in Section 6.\n * > However, a large number of existing implementations do not do this.\n *\n * IMPORTANT: the returned key is a raw result of scalar multiplication.\n * To use it as a key material, hash it with a cryptographic hash function.\n */\nexport function sharedKey(mySecretKey: Uint8Array, theirPublicKey: Uint8Array, rejectZero = false): Uint8Array {\n    if (mySecretKey.length !== PUBLIC_KEY_LENGTH) {\n        throw new Error(\"X25519: incorrect secret key length\");\n    }\n    if (theirPublicKey.length !== PUBLIC_KEY_LENGTH) {\n        throw new Error(\"X25519: incorrect public key length\");\n    }\n\n    const result = scalarMult(mySecretKey, theirPublicKey);\n\n    if (rejectZero) {\n        let zeros = 0;\n        for (let i = 0; i < result.length; i++) {\n            zeros |= result[i];\n        }\n        if (zeros === 0) {\n            throw new Error(\"X25519: invalid shared key\");\n        }\n    }\n\n    return result;\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", null, null, null, null, null, null, null, null, null, null, "'use strict';\nconst strictUriEncode = require('strict-uri-encode');\nconst decodeComponent = require('decode-uri-component');\nconst splitOnFirst = require('split-on-first');\nconst filterObject = require('filter-obj');\n\nconst isNullOrUndefined = value => value === null || value === undefined;\n\nconst encodeFragmentIdentifier = Symbol('encodeFragmentIdentifier');\n\nfunction encoderForArrayFormat(options) {\n\tswitch (options.arrayFormat) {\n\t\tcase 'index':\n\t\t\treturn key => (result, value) => {\n\t\t\t\tconst index = result.length;\n\n\t\t\t\tif (\n\t\t\t\t\tvalue === undefined ||\n\t\t\t\t\t(options.skipNull && value === null) ||\n\t\t\t\t\t(options.skipEmptyString && value === '')\n\t\t\t\t) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\n\t\t\t\tif (value === null) {\n\t\t\t\t\treturn [...result, [encode(key, options), '[', index, ']'].join('')];\n\t\t\t\t}\n\n\t\t\t\treturn [\n\t\t\t\t\t...result,\n\t\t\t\t\t[encode(key, options), '[', encode(index, options), ']=', encode(value, options)].join('')\n\t\t\t\t];\n\t\t\t};\n\n\t\tcase 'bracket':\n\t\t\treturn key => (result, value) => {\n\t\t\t\tif (\n\t\t\t\t\tvalue === undefined ||\n\t\t\t\t\t(options.skipNull && value === null) ||\n\t\t\t\t\t(options.skipEmptyString && value === '')\n\t\t\t\t) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\n\t\t\t\tif (value === null) {\n\t\t\t\t\treturn [...result, [encode(key, options), '[]'].join('')];\n\t\t\t\t}\n\n\t\t\t\treturn [...result, [encode(key, options), '[]=', encode(value, options)].join('')];\n\t\t\t};\n\n\t\tcase 'colon-list-separator':\n\t\t\treturn key => (result, value) => {\n\t\t\t\tif (\n\t\t\t\t\tvalue === undefined ||\n\t\t\t\t\t(options.skipNull && value === null) ||\n\t\t\t\t\t(options.skipEmptyString && value === '')\n\t\t\t\t) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\n\t\t\t\tif (value === null) {\n\t\t\t\t\treturn [...result, [encode(key, options), ':list='].join('')];\n\t\t\t\t}\n\n\t\t\t\treturn [...result, [encode(key, options), ':list=', encode(value, options)].join('')];\n\t\t\t};\n\n\t\tcase 'comma':\n\t\tcase 'separator':\n\t\tcase 'bracket-separator': {\n\t\t\tconst keyValueSep = options.arrayFormat === 'bracket-separator' ?\n\t\t\t\t'[]=' :\n\t\t\t\t'=';\n\n\t\t\treturn key => (result, value) => {\n\t\t\t\tif (\n\t\t\t\t\tvalue === undefined ||\n\t\t\t\t\t(options.skipNull && value === null) ||\n\t\t\t\t\t(options.skipEmptyString && value === '')\n\t\t\t\t) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\n\t\t\t\t// Translate null to an empty string so that it doesn't serialize as 'null'\n\t\t\t\tvalue = value === null ? '' : value;\n\n\t\t\t\tif (result.length === 0) {\n\t\t\t\t\treturn [[encode(key, options), keyValueSep, encode(value, options)].join('')];\n\t\t\t\t}\n\n\t\t\t\treturn [[result, encode(value, options)].join(options.arrayFormatSeparator)];\n\t\t\t};\n\t\t}\n\n\t\tdefault:\n\t\t\treturn key => (result, value) => {\n\t\t\t\tif (\n\t\t\t\t\tvalue === undefined ||\n\t\t\t\t\t(options.skipNull && value === null) ||\n\t\t\t\t\t(options.skipEmptyString && value === '')\n\t\t\t\t) {\n\t\t\t\t\treturn result;\n\t\t\t\t}\n\n\t\t\t\tif (value === null) {\n\t\t\t\t\treturn [...result, encode(key, options)];\n\t\t\t\t}\n\n\t\t\t\treturn [...result, [encode(key, options), '=', encode(value, options)].join('')];\n\t\t\t};\n\t}\n}\n\nfunction parserForArrayFormat(options) {\n\tlet result;\n\n\tswitch (options.arrayFormat) {\n\t\tcase 'index':\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tresult = /\\[(\\d*)\\]$/.exec(key);\n\n\t\t\t\tkey = key.replace(/\\[\\d*\\]$/, '');\n\n\t\t\t\tif (!result) {\n\t\t\t\t\taccumulator[key] = value;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (accumulator[key] === undefined) {\n\t\t\t\t\taccumulator[key] = {};\n\t\t\t\t}\n\n\t\t\t\taccumulator[key][result[1]] = value;\n\t\t\t};\n\n\t\tcase 'bracket':\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tresult = /(\\[\\])$/.exec(key);\n\t\t\t\tkey = key.replace(/\\[\\]$/, '');\n\n\t\t\t\tif (!result) {\n\t\t\t\t\taccumulator[key] = value;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (accumulator[key] === undefined) {\n\t\t\t\t\taccumulator[key] = [value];\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\taccumulator[key] = [].concat(accumulator[key], value);\n\t\t\t};\n\n\t\tcase 'colon-list-separator':\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tresult = /(:list)$/.exec(key);\n\t\t\t\tkey = key.replace(/:list$/, '');\n\n\t\t\t\tif (!result) {\n\t\t\t\t\taccumulator[key] = value;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (accumulator[key] === undefined) {\n\t\t\t\t\taccumulator[key] = [value];\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\taccumulator[key] = [].concat(accumulator[key], value);\n\t\t\t};\n\n\t\tcase 'comma':\n\t\tcase 'separator':\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tconst isArray = typeof value === 'string' && value.includes(options.arrayFormatSeparator);\n\t\t\t\tconst isEncodedArray = (typeof value === 'string' && !isArray && decode(value, options).includes(options.arrayFormatSeparator));\n\t\t\t\tvalue = isEncodedArray ? decode(value, options) : value;\n\t\t\t\tconst newValue = isArray || isEncodedArray ? value.split(options.arrayFormatSeparator).map(item => decode(item, options)) : value === null ? value : decode(value, options);\n\t\t\t\taccumulator[key] = newValue;\n\t\t\t};\n\n\t\tcase 'bracket-separator':\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tconst isArray = /(\\[\\])$/.test(key);\n\t\t\t\tkey = key.replace(/\\[\\]$/, '');\n\n\t\t\t\tif (!isArray) {\n\t\t\t\t\taccumulator[key] = value ? decode(value, options) : value;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst arrayValue = value === null ?\n\t\t\t\t\t[] :\n\t\t\t\t\tvalue.split(options.arrayFormatSeparator).map(item => decode(item, options));\n\n\t\t\t\tif (accumulator[key] === undefined) {\n\t\t\t\t\taccumulator[key] = arrayValue;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\taccumulator[key] = [].concat(accumulator[key], arrayValue);\n\t\t\t};\n\n\t\tdefault:\n\t\t\treturn (key, value, accumulator) => {\n\t\t\t\tif (accumulator[key] === undefined) {\n\t\t\t\t\taccumulator[key] = value;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\taccumulator[key] = [].concat(accumulator[key], value);\n\t\t\t};\n\t}\n}\n\nfunction validateArrayFormatSeparator(value) {\n\tif (typeof value !== 'string' || value.length !== 1) {\n\t\tthrow new TypeError('arrayFormatSeparator must be single character string');\n\t}\n}\n\nfunction encode(value, options) {\n\tif (options.encode) {\n\t\treturn options.strict ? strictUriEncode(value) : encodeURIComponent(value);\n\t}\n\n\treturn value;\n}\n\nfunction decode(value, options) {\n\tif (options.decode) {\n\t\treturn decodeComponent(value);\n\t}\n\n\treturn value;\n}\n\nfunction keysSorter(input) {\n\tif (Array.isArray(input)) {\n\t\treturn input.sort();\n\t}\n\n\tif (typeof input === 'object') {\n\t\treturn keysSorter(Object.keys(input))\n\t\t\t.sort((a, b) => Number(a) - Number(b))\n\t\t\t.map(key => input[key]);\n\t}\n\n\treturn input;\n}\n\nfunction removeHash(input) {\n\tconst hashStart = input.indexOf('#');\n\tif (hashStart !== -1) {\n\t\tinput = input.slice(0, hashStart);\n\t}\n\n\treturn input;\n}\n\nfunction getHash(url) {\n\tlet hash = '';\n\tconst hashStart = url.indexOf('#');\n\tif (hashStart !== -1) {\n\t\thash = url.slice(hashStart);\n\t}\n\n\treturn hash;\n}\n\nfunction extract(input) {\n\tinput = removeHash(input);\n\tconst queryStart = input.indexOf('?');\n\tif (queryStart === -1) {\n\t\treturn '';\n\t}\n\n\treturn input.slice(queryStart + 1);\n}\n\nfunction parseValue(value, options) {\n\tif (options.parseNumbers && !Number.isNaN(Number(value)) && (typeof value === 'string' && value.trim() !== '')) {\n\t\tvalue = Number(value);\n\t} else if (options.parseBooleans && value !== null && (value.toLowerCase() === 'true' || value.toLowerCase() === 'false')) {\n\t\tvalue = value.toLowerCase() === 'true';\n\t}\n\n\treturn value;\n}\n\nfunction parse(query, options) {\n\toptions = Object.assign({\n\t\tdecode: true,\n\t\tsort: true,\n\t\tarrayFormat: 'none',\n\t\tarrayFormatSeparator: ',',\n\t\tparseNumbers: false,\n\t\tparseBooleans: false\n\t}, options);\n\n\tvalidateArrayFormatSeparator(options.arrayFormatSeparator);\n\n\tconst formatter = parserForArrayFormat(options);\n\n\t// Create an object with no prototype\n\tconst ret = Object.create(null);\n\n\tif (typeof query !== 'string') {\n\t\treturn ret;\n\t}\n\n\tquery = query.trim().replace(/^[?#&]/, '');\n\n\tif (!query) {\n\t\treturn ret;\n\t}\n\n\tfor (const param of query.split('&')) {\n\t\tif (param === '') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet [key, value] = splitOnFirst(options.decode ? param.replace(/\\+/g, ' ') : param, '=');\n\n\t\t// Missing `=` should be `null`:\n\t\t// http://w3.org/TR/2012/WD-url-20120524/#collect-url-parameters\n\t\tvalue = value === undefined ? null : ['comma', 'separator', 'bracket-separator'].includes(options.arrayFormat) ? value : decode(value, options);\n\t\tformatter(decode(key, options), value, ret);\n\t}\n\n\tfor (const key of Object.keys(ret)) {\n\t\tconst value = ret[key];\n\t\tif (typeof value === 'object' && value !== null) {\n\t\t\tfor (const k of Object.keys(value)) {\n\t\t\t\tvalue[k] = parseValue(value[k], options);\n\t\t\t}\n\t\t} else {\n\t\t\tret[key] = parseValue(value, options);\n\t\t}\n\t}\n\n\tif (options.sort === false) {\n\t\treturn ret;\n\t}\n\n\treturn (options.sort === true ? Object.keys(ret).sort() : Object.keys(ret).sort(options.sort)).reduce((result, key) => {\n\t\tconst value = ret[key];\n\t\tif (Boolean(value) && typeof value === 'object' && !Array.isArray(value)) {\n\t\t\t// Sort object keys, not values\n\t\t\tresult[key] = keysSorter(value);\n\t\t} else {\n\t\t\tresult[key] = value;\n\t\t}\n\n\t\treturn result;\n\t}, Object.create(null));\n}\n\nexports.extract = extract;\nexports.parse = parse;\n\nexports.stringify = (object, options) => {\n\tif (!object) {\n\t\treturn '';\n\t}\n\n\toptions = Object.assign({\n\t\tencode: true,\n\t\tstrict: true,\n\t\tarrayFormat: 'none',\n\t\tarrayFormatSeparator: ','\n\t}, options);\n\n\tvalidateArrayFormatSeparator(options.arrayFormatSeparator);\n\n\tconst shouldFilter = key => (\n\t\t(options.skipNull && isNullOrUndefined(object[key])) ||\n\t\t(options.skipEmptyString && object[key] === '')\n\t);\n\n\tconst formatter = encoderForArrayFormat(options);\n\n\tconst objectCopy = {};\n\n\tfor (const key of Object.keys(object)) {\n\t\tif (!shouldFilter(key)) {\n\t\t\tobjectCopy[key] = object[key];\n\t\t}\n\t}\n\n\tconst keys = Object.keys(objectCopy);\n\n\tif (options.sort !== false) {\n\t\tkeys.sort(options.sort);\n\t}\n\n\treturn keys.map(key => {\n\t\tconst value = object[key];\n\n\t\tif (value === undefined) {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (value === null) {\n\t\t\treturn encode(key, options);\n\t\t}\n\n\t\tif (Array.isArray(value)) {\n\t\t\tif (value.length === 0 && options.arrayFormat === 'bracket-separator') {\n\t\t\t\treturn encode(key, options) + '[]';\n\t\t\t}\n\n\t\t\treturn value\n\t\t\t\t.reduce(formatter(key), [])\n\t\t\t\t.join('&');\n\t\t}\n\n\t\treturn encode(key, options) + '=' + encode(value, options);\n\t}).filter(x => x.length > 0).join('&');\n};\n\nexports.parseUrl = (url, options) => {\n\toptions = Object.assign({\n\t\tdecode: true\n\t}, options);\n\n\tconst [url_, hash] = splitOnFirst(url, '#');\n\n\treturn Object.assign(\n\t\t{\n\t\t\turl: url_.split('?')[0] || '',\n\t\t\tquery: parse(extract(url), options)\n\t\t},\n\t\toptions && options.parseFragmentIdentifier && hash ? {fragmentIdentifier: decode(hash, options)} : {}\n\t);\n};\n\nexports.stringifyUrl = (object, options) => {\n\toptions = Object.assign({\n\t\tencode: true,\n\t\tstrict: true,\n\t\t[encodeFragmentIdentifier]: true\n\t}, options);\n\n\tconst url = removeHash(object.url).split('?')[0] || '';\n\tconst queryFromUrl = exports.extract(object.url);\n\tconst parsedQueryFromUrl = exports.parse(queryFromUrl, {sort: false});\n\n\tconst query = Object.assign(parsedQueryFromUrl, object.query);\n\tlet queryString = exports.stringify(query, options);\n\tif (queryString) {\n\t\tqueryString = `?${queryString}`;\n\t}\n\n\tlet hash = getHash(object.url);\n\tif (object.fragmentIdentifier) {\n\t\thash = `#${options[encodeFragmentIdentifier] ? encode(object.fragmentIdentifier, options) : object.fragmentIdentifier}`;\n\t}\n\n\treturn `${url}${queryString}${hash}`;\n};\n\nexports.pick = (input, filter, options) => {\n\toptions = Object.assign({\n\t\tparseFragmentIdentifier: true,\n\t\t[encodeFragmentIdentifier]: false\n\t}, options);\n\n\tconst {url, query, fragmentIdentifier} = exports.parseUrl(input, options);\n\treturn exports.stringifyUrl({\n\t\turl,\n\t\tquery: filterObject(query, filter),\n\t\tfragmentIdentifier\n\t}, options);\n};\n\nexports.exclude = (input, filter, options) => {\n\tconst exclusionFilter = Array.isArray(filter) ? key => !filter.includes(key) : (key, value) => !filter(key, value);\n\n\treturn exports.pick(input, exclusionFilter, options);\n};\n", "export function asUint8Array(buf) {\n  if (globalThis.Buffer != null) {\n    return new Uint8Array(buf.buffer, buf.byteOffset, buf.byteLength);\n  }\n  return buf;\n}", "import { asUint8Array } from './util/as-uint8array.js';\nexport function alloc(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.alloc != null) {\n    return asUint8Array(globalThis.Buffer.alloc(size));\n  }\n  return new Uint8Array(size);\n}\nexport function allocUnsafe(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.allocUnsafe != null) {\n    return asUint8Array(globalThis.Buffer.allocUnsafe(size));\n  }\n  return new Uint8Array(size);\n}", "import { allocUnsafe } from './alloc.js';\nimport { asUint8Array } from './util/as-uint8array.js';\nexport function concat(arrays, length) {\n  if (!length) {\n    length = arrays.reduce((acc, curr) => acc + curr.length, 0);\n  }\n  const output = allocUnsafe(length);\n  let offset = 0;\n  for (const arr of arrays) {\n    output.set(arr, offset);\n    offset += arr.length;\n  }\n  return asUint8Array(output);\n}", "import { from } from './base.js';\nimport {\n  fromString,\n  toString\n} from '../bytes.js';\nexport const identity = from({\n  prefix: '\\0',\n  name: 'identity',\n  encode: buf => toString(buf),\n  decode: str => fromString(str)\n});", "function base(ALPHABET, name) {\n  if (ALPHABET.length >= 255) {\n    throw new TypeError('Alphabet too long');\n  }\n  var BASE_MAP = new Uint8Array(256);\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255;\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i);\n    var xc = x.charCodeAt(0);\n    if (BASE_MAP[xc] !== 255) {\n      throw new TypeError(x + ' is ambiguous');\n    }\n    BASE_MAP[xc] = i;\n  }\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0);\n  var FACTOR = Math.log(BASE) / Math.log(256);\n  var iFACTOR = Math.log(256) / Math.log(BASE);\n  function encode(source) {\n    if (source instanceof Uint8Array);\n    else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength);\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source);\n    }\n    if (!(source instanceof Uint8Array)) {\n      throw new TypeError('Expected Uint8Array');\n    }\n    if (source.length === 0) {\n      return '';\n    }\n    var zeroes = 0;\n    var length = 0;\n    var pbegin = 0;\n    var pend = source.length;\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++;\n      zeroes++;\n    }\n    var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n    var b58 = new Uint8Array(size);\n    while (pbegin !== pend) {\n      var carry = source[pbegin];\n      var i = 0;\n      for (var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++) {\n        carry += 256 * b58[it1] >>> 0;\n        b58[it1] = carry % BASE >>> 0;\n        carry = carry / BASE >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      pbegin++;\n    }\n    var it2 = size - length;\n    while (it2 !== size && b58[it2] === 0) {\n      it2++;\n    }\n    var str = LEADER.repeat(zeroes);\n    for (; it2 < size; ++it2) {\n      str += ALPHABET.charAt(b58[it2]);\n    }\n    return str;\n  }\n  function decodeUnsafe(source) {\n    if (typeof source !== 'string') {\n      throw new TypeError('Expected String');\n    }\n    if (source.length === 0) {\n      return new Uint8Array();\n    }\n    var psz = 0;\n    if (source[psz] === ' ') {\n      return;\n    }\n    var zeroes = 0;\n    var length = 0;\n    while (source[psz] === LEADER) {\n      zeroes++;\n      psz++;\n    }\n    var size = (source.length - psz) * FACTOR + 1 >>> 0;\n    var b256 = new Uint8Array(size);\n    while (source[psz]) {\n      var carry = BASE_MAP[source.charCodeAt(psz)];\n      if (carry === 255) {\n        return;\n      }\n      var i = 0;\n      for (var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++) {\n        carry += BASE * b256[it3] >>> 0;\n        b256[it3] = carry % 256 >>> 0;\n        carry = carry / 256 >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      psz++;\n    }\n    if (source[psz] === ' ') {\n      return;\n    }\n    var it4 = size - length;\n    while (it4 !== size && b256[it4] === 0) {\n      it4++;\n    }\n    var vch = new Uint8Array(zeroes + (size - it4));\n    var j = zeroes;\n    while (it4 !== size) {\n      vch[j++] = b256[it4++];\n    }\n    return vch;\n  }\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n    if (buffer) {\n      return buffer;\n    }\n    throw new Error(`Non-${ name } character`);\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n}\nvar src = base;\nvar _brrp__multiformats_scope_baseX = src;\nexport default _brrp__multiformats_scope_baseX;", "const empty = new Uint8Array(0);\nconst toHex = d => d.reduce((hex, byte) => hex + byte.toString(16).padStart(2, '0'), '');\nconst fromHex = hex => {\n  const hexes = hex.match(/../g);\n  return hexes ? new Uint8Array(hexes.map(b => parseInt(b, 16))) : empty;\n};\nconst equals = (aa, bb) => {\n  if (aa === bb)\n    return true;\n  if (aa.byteLength !== bb.byteLength) {\n    return false;\n  }\n  for (let ii = 0; ii < aa.byteLength; ii++) {\n    if (aa[ii] !== bb[ii]) {\n      return false;\n    }\n  }\n  return true;\n};\nconst coerce = o => {\n  if (o instanceof Uint8Array && o.constructor.name === 'Uint8Array')\n    return o;\n  if (o instanceof ArrayBuffer)\n    return new Uint8Array(o);\n  if (ArrayBuffer.isView(o)) {\n    return new Uint8Array(o.buffer, o.byteOffset, o.byteLength);\n  }\n  throw new Error('Unknown type, must be binary type');\n};\nconst isBinary = o => o instanceof ArrayBuffer || ArrayBuffer.isView(o);\nconst fromString = str => new TextEncoder().encode(str);\nconst toString = b => new TextDecoder().decode(b);\nexport {\n  equals,\n  coerce,\n  isBinary,\n  fromHex,\n  toHex,\n  fromString,\n  toString,\n  empty\n};", "import basex from '../../vendor/base-x.js';\nimport { coerce } from '../bytes.js';\nclass Encoder {\n  constructor(name, prefix, baseEncode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n  }\n  encode(bytes) {\n    if (bytes instanceof Uint8Array) {\n      return `${ this.prefix }${ this.baseEncode(bytes) }`;\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}\nclass Decoder {\n  constructor(name, prefix, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    if (prefix.codePointAt(0) === undefined) {\n      throw new Error('Invalid prefix character');\n    }\n    this.prefixCodePoint = prefix.codePointAt(0);\n    this.baseDecode = baseDecode;\n  }\n  decode(text) {\n    if (typeof text === 'string') {\n      if (text.codePointAt(0) !== this.prefixCodePoint) {\n        throw Error(`Unable to decode multibase string ${ JSON.stringify(text) }, ${ this.name } decoder only supports inputs prefixed with ${ this.prefix }`);\n      }\n      return this.baseDecode(text.slice(this.prefix.length));\n    } else {\n      throw Error('Can only multibase decode strings');\n    }\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n}\nclass ComposedDecoder {\n  constructor(decoders) {\n    this.decoders = decoders;\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n  decode(input) {\n    const prefix = input[0];\n    const decoder = this.decoders[prefix];\n    if (decoder) {\n      return decoder.decode(input);\n    } else {\n      throw RangeError(`Unable to decode multibase string ${ JSON.stringify(input) }, only inputs prefixed with ${ Object.keys(this.decoders) } are supported`);\n    }\n  }\n}\nexport const or = (left, right) => new ComposedDecoder({\n  ...left.decoders || { [left.prefix]: left },\n  ...right.decoders || { [right.prefix]: right }\n});\nexport class Codec {\n  constructor(name, prefix, baseEncode, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n    this.baseDecode = baseDecode;\n    this.encoder = new Encoder(name, prefix, baseEncode);\n    this.decoder = new Decoder(name, prefix, baseDecode);\n  }\n  encode(input) {\n    return this.encoder.encode(input);\n  }\n  decode(input) {\n    return this.decoder.decode(input);\n  }\n}\nexport const from = ({name, prefix, encode, decode}) => new Codec(name, prefix, encode, decode);\nexport const baseX = ({prefix, name, alphabet}) => {\n  const {encode, decode} = basex(alphabet, name);\n  return from({\n    prefix,\n    name,\n    encode,\n    decode: text => coerce(decode(text))\n  });\n};\nconst decode = (string, alphabet, bitsPerChar, name) => {\n  const codes = {};\n  for (let i = 0; i < alphabet.length; ++i) {\n    codes[alphabet[i]] = i;\n  }\n  let end = string.length;\n  while (string[end - 1] === '=') {\n    --end;\n  }\n  const out = new Uint8Array(end * bitsPerChar / 8 | 0);\n  let bits = 0;\n  let buffer = 0;\n  let written = 0;\n  for (let i = 0; i < end; ++i) {\n    const value = codes[string[i]];\n    if (value === undefined) {\n      throw new SyntaxError(`Non-${ name } character`);\n    }\n    buffer = buffer << bitsPerChar | value;\n    bits += bitsPerChar;\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 255 & buffer >> bits;\n    }\n  }\n  if (bits >= bitsPerChar || 255 & buffer << 8 - bits) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n  return out;\n};\nconst encode = (data, alphabet, bitsPerChar) => {\n  const pad = alphabet[alphabet.length - 1] === '=';\n  const mask = (1 << bitsPerChar) - 1;\n  let out = '';\n  let bits = 0;\n  let buffer = 0;\n  for (let i = 0; i < data.length; ++i) {\n    buffer = buffer << 8 | data[i];\n    bits += 8;\n    while (bits > bitsPerChar) {\n      bits -= bitsPerChar;\n      out += alphabet[mask & buffer >> bits];\n    }\n  }\n  if (bits) {\n    out += alphabet[mask & buffer << bitsPerChar - bits];\n  }\n  if (pad) {\n    while (out.length * bitsPerChar & 7) {\n      out += '=';\n    }\n  }\n  return out;\n};\nexport const rfc4648 = ({name, prefix, bitsPerChar, alphabet}) => {\n  return from({\n    prefix,\n    name,\n    encode(input) {\n      return encode(input, alphabet, bitsPerChar);\n    },\n    decode(input) {\n      return decode(input, alphabet, bitsPerChar, name);\n    }\n  });\n};", "import { rfc4648 } from './base.js';\nexport const base2 = rfc4648({\n  prefix: '0',\n  name: 'base2',\n  alphabet: '01',\n  bitsPerChar: 1\n});", "import { rfc4648 } from './base.js';\nexport const base8 = rfc4648({\n  prefix: '7',\n  name: 'base8',\n  alphabet: '01234567',\n  bitsPerChar: 3\n});", "import { baseX } from './base.js';\nexport const base10 = baseX({\n  prefix: '9',\n  name: 'base10',\n  alphabet: '0123456789'\n});", "import { rfc4648 } from './base.js';\nexport const base16 = rfc4648({\n  prefix: 'f',\n  name: 'base16',\n  alphabet: '0123456789abcdef',\n  bitsPerChar: 4\n});\nexport const base16upper = rfc4648({\n  prefix: 'F',\n  name: 'base16upper',\n  alphabet: '0123456789ABCDEF',\n  bitsPerChar: 4\n});", "import { rfc4648 } from './base.js';\nexport const base32 = rfc4648({\n  prefix: 'b',\n  name: 'base32',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567',\n  bitsPerChar: 5\n});\nexport const base32upper = rfc4648({\n  prefix: 'B',\n  name: 'base32upper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567',\n  bitsPerChar: 5\n});\nexport const base32pad = rfc4648({\n  prefix: 'c',\n  name: 'base32pad',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567=',\n  bitsPerChar: 5\n});\nexport const base32padupper = rfc4648({\n  prefix: 'C',\n  name: 'base32padupper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=',\n  bitsPerChar: 5\n});\nexport const base32hex = rfc4648({\n  prefix: 'v',\n  name: 'base32hex',\n  alphabet: '0123456789abcdefghijklmnopqrstuv',\n  bitsPerChar: 5\n});\nexport const base32hexupper = rfc4648({\n  prefix: 'V',\n  name: 'base32hexupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV',\n  bitsPerChar: 5\n});\nexport const base32hexpad = rfc4648({\n  prefix: 't',\n  name: 'base32hexpad',\n  alphabet: '0123456789abcdefghijklmnopqrstuv=',\n  bitsPerChar: 5\n});\nexport const base32hexpadupper = rfc4648({\n  prefix: 'T',\n  name: 'base32hexpadupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV=',\n  bitsPerChar: 5\n});\nexport const base32z = rfc4648({\n  prefix: 'h',\n  name: 'base32z',\n  alphabet: 'ybndrfg8ejkmcpqxot1uwisza345h769',\n  bitsPerChar: 5\n});", "import { baseX } from './base.js';\nexport const base36 = baseX({\n  prefix: 'k',\n  name: 'base36',\n  alphabet: '0123456789abcdefghijklmnopqrstuvwxyz'\n});\nexport const base36upper = baseX({\n  prefix: 'K',\n  name: 'base36upper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n});", "import { baseX } from './base.js';\nexport const base58btc = baseX({\n  name: 'base58btc',\n  prefix: 'z',\n  alphabet: '**********************************************************'\n});\nexport const base58flickr = baseX({\n  name: 'base58flickr',\n  prefix: 'Z',\n  alphabet: '**********************************************************'\n});", "import { rfc4648 } from './base.js';\nexport const base64 = rfc4648({\n  prefix: 'm',\n  name: 'base64',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',\n  bitsPerChar: 6\n});\nexport const base64pad = rfc4648({\n  prefix: 'M',\n  name: 'base64pad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',\n  bitsPerChar: 6\n});\nexport const base64url = rfc4648({\n  prefix: 'u',\n  name: 'base64url',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bitsPerChar: 6\n});\nexport const base64urlpad = rfc4648({\n  prefix: 'U',\n  name: 'base64urlpad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=',\n  bitsPerChar: 6\n});", "import { from } from './base.js';\nconst alphabet = Array.from('\\uD83D\\uDE80\\uD83E\\uDE90\\u2604\\uD83D\\uDEF0\\uD83C\\uDF0C\\uD83C\\uDF11\\uD83C\\uDF12\\uD83C\\uDF13\\uD83C\\uDF14\\uD83C\\uDF15\\uD83C\\uDF16\\uD83C\\uDF17\\uD83C\\uDF18\\uD83C\\uDF0D\\uD83C\\uDF0F\\uD83C\\uDF0E\\uD83D\\uDC09\\u2600\\uD83D\\uDCBB\\uD83D\\uDDA5\\uD83D\\uDCBE\\uD83D\\uDCBF\\uD83D\\uDE02\\u2764\\uD83D\\uDE0D\\uD83E\\uDD23\\uD83D\\uDE0A\\uD83D\\uDE4F\\uD83D\\uDC95\\uD83D\\uDE2D\\uD83D\\uDE18\\uD83D\\uDC4D\\uD83D\\uDE05\\uD83D\\uDC4F\\uD83D\\uDE01\\uD83D\\uDD25\\uD83E\\uDD70\\uD83D\\uDC94\\uD83D\\uDC96\\uD83D\\uDC99\\uD83D\\uDE22\\uD83E\\uDD14\\uD83D\\uDE06\\uD83D\\uDE44\\uD83D\\uDCAA\\uD83D\\uDE09\\u263A\\uD83D\\uDC4C\\uD83E\\uDD17\\uD83D\\uDC9C\\uD83D\\uDE14\\uD83D\\uDE0E\\uD83D\\uDE07\\uD83C\\uDF39\\uD83E\\uDD26\\uD83C\\uDF89\\uD83D\\uDC9E\\u270C\\u2728\\uD83E\\uDD37\\uD83D\\uDE31\\uD83D\\uDE0C\\uD83C\\uDF38\\uD83D\\uDE4C\\uD83D\\uDE0B\\uD83D\\uDC97\\uD83D\\uDC9A\\uD83D\\uDE0F\\uD83D\\uDC9B\\uD83D\\uDE42\\uD83D\\uDC93\\uD83E\\uDD29\\uD83D\\uDE04\\uD83D\\uDE00\\uD83D\\uDDA4\\uD83D\\uDE03\\uD83D\\uDCAF\\uD83D\\uDE48\\uD83D\\uDC47\\uD83C\\uDFB6\\uD83D\\uDE12\\uD83E\\uDD2D\\u2763\\uD83D\\uDE1C\\uD83D\\uDC8B\\uD83D\\uDC40\\uD83D\\uDE2A\\uD83D\\uDE11\\uD83D\\uDCA5\\uD83D\\uDE4B\\uD83D\\uDE1E\\uD83D\\uDE29\\uD83D\\uDE21\\uD83E\\uDD2A\\uD83D\\uDC4A\\uD83E\\uDD73\\uD83D\\uDE25\\uD83E\\uDD24\\uD83D\\uDC49\\uD83D\\uDC83\\uD83D\\uDE33\\u270B\\uD83D\\uDE1A\\uD83D\\uDE1D\\uD83D\\uDE34\\uD83C\\uDF1F\\uD83D\\uDE2C\\uD83D\\uDE43\\uD83C\\uDF40\\uD83C\\uDF37\\uD83D\\uDE3B\\uD83D\\uDE13\\u2B50\\u2705\\uD83E\\uDD7A\\uD83C\\uDF08\\uD83D\\uDE08\\uD83E\\uDD18\\uD83D\\uDCA6\\u2714\\uD83D\\uDE23\\uD83C\\uDFC3\\uD83D\\uDC90\\u2639\\uD83C\\uDF8A\\uD83D\\uDC98\\uD83D\\uDE20\\u261D\\uD83D\\uDE15\\uD83C\\uDF3A\\uD83C\\uDF82\\uD83C\\uDF3B\\uD83D\\uDE10\\uD83D\\uDD95\\uD83D\\uDC9D\\uD83D\\uDE4A\\uD83D\\uDE39\\uD83D\\uDDE3\\uD83D\\uDCAB\\uD83D\\uDC80\\uD83D\\uDC51\\uD83C\\uDFB5\\uD83E\\uDD1E\\uD83D\\uDE1B\\uD83D\\uDD34\\uD83D\\uDE24\\uD83C\\uDF3C\\uD83D\\uDE2B\\u26BD\\uD83E\\uDD19\\u2615\\uD83C\\uDFC6\\uD83E\\uDD2B\\uD83D\\uDC48\\uD83D\\uDE2E\\uD83D\\uDE46\\uD83C\\uDF7B\\uD83C\\uDF43\\uD83D\\uDC36\\uD83D\\uDC81\\uD83D\\uDE32\\uD83C\\uDF3F\\uD83E\\uDDE1\\uD83C\\uDF81\\u26A1\\uD83C\\uDF1E\\uD83C\\uDF88\\u274C\\u270A\\uD83D\\uDC4B\\uD83D\\uDE30\\uD83E\\uDD28\\uD83D\\uDE36\\uD83E\\uDD1D\\uD83D\\uDEB6\\uD83D\\uDCB0\\uD83C\\uDF53\\uD83D\\uDCA2\\uD83E\\uDD1F\\uD83D\\uDE41\\uD83D\\uDEA8\\uD83D\\uDCA8\\uD83E\\uDD2C\\u2708\\uD83C\\uDF80\\uD83C\\uDF7A\\uD83E\\uDD13\\uD83D\\uDE19\\uD83D\\uDC9F\\uD83C\\uDF31\\uD83D\\uDE16\\uD83D\\uDC76\\uD83E\\uDD74\\u25B6\\u27A1\\u2753\\uD83D\\uDC8E\\uD83D\\uDCB8\\u2B07\\uD83D\\uDE28\\uD83C\\uDF1A\\uD83E\\uDD8B\\uD83D\\uDE37\\uD83D\\uDD7A\\u26A0\\uD83D\\uDE45\\uD83D\\uDE1F\\uD83D\\uDE35\\uD83D\\uDC4E\\uD83E\\uDD32\\uD83E\\uDD20\\uD83E\\uDD27\\uD83D\\uDCCC\\uD83D\\uDD35\\uD83D\\uDC85\\uD83E\\uDDD0\\uD83D\\uDC3E\\uD83C\\uDF52\\uD83D\\uDE17\\uD83E\\uDD11\\uD83C\\uDF0A\\uD83E\\uDD2F\\uD83D\\uDC37\\u260E\\uD83D\\uDCA7\\uD83D\\uDE2F\\uD83D\\uDC86\\uD83D\\uDC46\\uD83C\\uDFA4\\uD83D\\uDE47\\uD83C\\uDF51\\u2744\\uD83C\\uDF34\\uD83D\\uDCA3\\uD83D\\uDC38\\uD83D\\uDC8C\\uD83D\\uDCCD\\uD83E\\uDD40\\uD83E\\uDD22\\uD83D\\uDC45\\uD83D\\uDCA1\\uD83D\\uDCA9\\uD83D\\uDC50\\uD83D\\uDCF8\\uD83D\\uDC7B\\uD83E\\uDD10\\uD83E\\uDD2E\\uD83C\\uDFBC\\uD83E\\uDD75\\uD83D\\uDEA9\\uD83C\\uDF4E\\uD83C\\uDF4A\\uD83D\\uDC7C\\uD83D\\uDC8D\\uD83D\\uDCE3\\uD83E\\uDD42');\nconst alphabetBytesToChars = alphabet.reduce((p, c, i) => {\n  p[i] = c;\n  return p;\n}, []);\nconst alphabetCharsToBytes = alphabet.reduce((p, c, i) => {\n  p[c.codePointAt(0)] = i;\n  return p;\n}, []);\nfunction encode(data) {\n  return data.reduce((p, c) => {\n    p += alphabetBytesToChars[c];\n    return p;\n  }, '');\n}\nfunction decode(str) {\n  const byts = [];\n  for (const char of str) {\n    const byt = alphabetCharsToBytes[char.codePointAt(0)];\n    if (byt === undefined) {\n      throw new Error(`Non-base256emoji character: ${ char }`);\n    }\n    byts.push(byt);\n  }\n  return new Uint8Array(byts);\n}\nexport const base256emoji = from({\n  prefix: '\\uD83D\\uDE80',\n  name: 'base256emoji',\n  encode,\n  decode\n});", "import { from } from './hasher.js';\nconst sha = name => async data => new Uint8Array(await crypto.subtle.digest(name, data));\nexport const sha256 = from({\n  name: 'sha2-256',\n  code: 18,\n  encode: sha('SHA-256')\n});\nexport const sha512 = from({\n  name: 'sha2-512',\n  code: 19,\n  encode: sha('SHA-512')\n});", "var encode_1 = encode;\nvar MSB = 128, REST = 127, MSBALL = ~REST, INT = Math.pow(2, 31);\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n  while (num >= INT) {\n    out[offset++] = num & 255 | MSB;\n    num /= 128;\n  }\n  while (num & MSBALL) {\n    out[offset++] = num & 255 | MSB;\n    num >>>= 7;\n  }\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}\nvar decode = read;\nvar MSB$1 = 128, REST$1 = 127;\nfunction read(buf, offset) {\n  var res = 0, offset = offset || 0, shift = 0, counter = offset, b, l = buf.length;\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST$1) << shift : (b & REST$1) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB$1);\n  read.bytes = counter - offset;\n  return res;\n}\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\nvar length = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};\nvar varint = {\n  encode: encode_1,\n  decode: decode,\n  encodingLength: length\n};\nvar _brrp_varint = varint;\nexport default _brrp_varint;", "import varint from '../vendor/varint.js';\nexport const decode = (data, offset = 0) => {\n  const code = varint.decode(data, offset);\n  return [\n    code,\n    varint.decode.bytes\n  ];\n};\nexport const encodeTo = (int, target, offset = 0) => {\n  varint.encode(int, target, offset);\n  return target;\n};\nexport const encodingLength = int => {\n  return varint.encodingLength(int);\n};", "import {\n  coerce,\n  equals as equalBytes\n} from '../bytes.js';\nimport * as varint from '../varint.js';\nexport const create = (code, digest) => {\n  const size = digest.byteLength;\n  const sizeOffset = varint.encodingLength(code);\n  const digestOffset = sizeOffset + varint.encodingLength(size);\n  const bytes = new Uint8Array(digestOffset + size);\n  varint.encodeTo(code, bytes, 0);\n  varint.encodeTo(size, bytes, sizeOffset);\n  bytes.set(digest, digestOffset);\n  return new Digest(code, size, digest, bytes);\n};\nexport const decode = multihash => {\n  const bytes = coerce(multihash);\n  const [code, sizeOffset] = varint.decode(bytes);\n  const [size, digestOffset] = varint.decode(bytes.subarray(sizeOffset));\n  const digest = bytes.subarray(sizeOffset + digestOffset);\n  if (digest.byteLength !== size) {\n    throw new Error('Incorrect length');\n  }\n  return new Digest(code, size, digest, bytes);\n};\nexport const equals = (a, b) => {\n  if (a === b) {\n    return true;\n  } else {\n    return a.code === b.code && a.size === b.size && equalBytes(a.bytes, b.bytes);\n  }\n};\nexport class Digest {\n  constructor(code, size, digest, bytes) {\n    this.code = code;\n    this.size = size;\n    this.digest = digest;\n    this.bytes = bytes;\n  }\n}", "import * as Digest from './digest.js';\nexport const from = ({name, code, encode}) => new Hasher(name, code, encode);\nexport class Hasher {\n  constructor(name, code, encode) {\n    this.name = name;\n    this.code = code;\n    this.encode = encode;\n  }\n  digest(input) {\n    if (input instanceof Uint8Array) {\n      const result = this.encode(input);\n      return result instanceof Uint8Array ? Digest.create(this.code, result) : result.then(digest => Digest.create(this.code, digest));\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}", "import { coerce } from '../bytes.js';\nimport * as Digest from './digest.js';\nconst code = 0;\nconst name = 'identity';\nconst encode = coerce;\nconst digest = input => Digest.create(code, encode(input));\nexport const identity = {\n  code,\n  name,\n  encode,\n  digest\n};", "const textEncoder = new TextEncoder();\nconst textDecoder = new TextDecoder();\nexport const name = 'json';\nexport const code = 512;\nexport const encode = node => textEncoder.encode(JSON.stringify(node));\nexport const decode = data => JSON.parse(textDecoder.decode(data));", "import * as varint from './varint.js';\nimport * as Digest from './hashes/digest.js';\nimport { base58btc } from './bases/base58.js';\nimport { base32 } from './bases/base32.js';\nimport { coerce } from './bytes.js';\nexport class CID {\n  constructor(version, code, multihash, bytes) {\n    this.code = code;\n    this.version = version;\n    this.multihash = multihash;\n    this.bytes = bytes;\n    this.byteOffset = bytes.byteOffset;\n    this.byteLength = bytes.byteLength;\n    this.asCID = this;\n    this._baseCache = new Map();\n    Object.defineProperties(this, {\n      byteOffset: hidden,\n      byteLength: hidden,\n      code: readonly,\n      version: readonly,\n      multihash: readonly,\n      bytes: readonly,\n      _baseCache: hidden,\n      asCID: hidden\n    });\n  }\n  toV0() {\n    switch (this.version) {\n    case 0: {\n        return this;\n      }\n    default: {\n        const {code, multihash} = this;\n        if (code !== DAG_PB_CODE) {\n          throw new Error('Cannot convert a non dag-pb CID to CIDv0');\n        }\n        if (multihash.code !== SHA_256_CODE) {\n          throw new Error('Cannot convert non sha2-256 multihash CID to CIDv0');\n        }\n        return CID.createV0(multihash);\n      }\n    }\n  }\n  toV1() {\n    switch (this.version) {\n    case 0: {\n        const {code, digest} = this.multihash;\n        const multihash = Digest.create(code, digest);\n        return CID.createV1(this.code, multihash);\n      }\n    case 1: {\n        return this;\n      }\n    default: {\n        throw Error(`Can not convert CID version ${ this.version } to version 0. This is a bug please report`);\n      }\n    }\n  }\n  equals(other) {\n    return other && this.code === other.code && this.version === other.version && Digest.equals(this.multihash, other.multihash);\n  }\n  toString(base) {\n    const {bytes, version, _baseCache} = this;\n    switch (version) {\n    case 0:\n      return toStringV0(bytes, _baseCache, base || base58btc.encoder);\n    default:\n      return toStringV1(bytes, _baseCache, base || base32.encoder);\n    }\n  }\n  toJSON() {\n    return {\n      code: this.code,\n      version: this.version,\n      hash: this.multihash.bytes\n    };\n  }\n  get [Symbol.toStringTag]() {\n    return 'CID';\n  }\n  [Symbol.for('nodejs.util.inspect.custom')]() {\n    return 'CID(' + this.toString() + ')';\n  }\n  static isCID(value) {\n    deprecate(/^0\\.0/, IS_CID_DEPRECATION);\n    return !!(value && (value[cidSymbol] || value.asCID === value));\n  }\n  get toBaseEncodedString() {\n    throw new Error('Deprecated, use .toString()');\n  }\n  get codec() {\n    throw new Error('\"codec\" property is deprecated, use integer \"code\" property instead');\n  }\n  get buffer() {\n    throw new Error('Deprecated .buffer property, use .bytes to get Uint8Array instead');\n  }\n  get multibaseName() {\n    throw new Error('\"multibaseName\" property is deprecated');\n  }\n  get prefix() {\n    throw new Error('\"prefix\" property is deprecated');\n  }\n  static asCID(value) {\n    if (value instanceof CID) {\n      return value;\n    } else if (value != null && value.asCID === value) {\n      const {version, code, multihash, bytes} = value;\n      return new CID(version, code, multihash, bytes || encodeCID(version, code, multihash.bytes));\n    } else if (value != null && value[cidSymbol] === true) {\n      const {version, multihash, code} = value;\n      const digest = Digest.decode(multihash);\n      return CID.create(version, code, digest);\n    } else {\n      return null;\n    }\n  }\n  static create(version, code, digest) {\n    if (typeof code !== 'number') {\n      throw new Error('String codecs are no longer supported');\n    }\n    switch (version) {\n    case 0: {\n        if (code !== DAG_PB_CODE) {\n          throw new Error(`Version 0 CID must use dag-pb (code: ${ DAG_PB_CODE }) block encoding`);\n        } else {\n          return new CID(version, code, digest, digest.bytes);\n        }\n      }\n    case 1: {\n        const bytes = encodeCID(version, code, digest.bytes);\n        return new CID(version, code, digest, bytes);\n      }\n    default: {\n        throw new Error('Invalid version');\n      }\n    }\n  }\n  static createV0(digest) {\n    return CID.create(0, DAG_PB_CODE, digest);\n  }\n  static createV1(code, digest) {\n    return CID.create(1, code, digest);\n  }\n  static decode(bytes) {\n    const [cid, remainder] = CID.decodeFirst(bytes);\n    if (remainder.length) {\n      throw new Error('Incorrect length');\n    }\n    return cid;\n  }\n  static decodeFirst(bytes) {\n    const specs = CID.inspectBytes(bytes);\n    const prefixSize = specs.size - specs.multihashSize;\n    const multihashBytes = coerce(bytes.subarray(prefixSize, prefixSize + specs.multihashSize));\n    if (multihashBytes.byteLength !== specs.multihashSize) {\n      throw new Error('Incorrect length');\n    }\n    const digestBytes = multihashBytes.subarray(specs.multihashSize - specs.digestSize);\n    const digest = new Digest.Digest(specs.multihashCode, specs.digestSize, digestBytes, multihashBytes);\n    const cid = specs.version === 0 ? CID.createV0(digest) : CID.createV1(specs.codec, digest);\n    return [\n      cid,\n      bytes.subarray(specs.size)\n    ];\n  }\n  static inspectBytes(initialBytes) {\n    let offset = 0;\n    const next = () => {\n      const [i, length] = varint.decode(initialBytes.subarray(offset));\n      offset += length;\n      return i;\n    };\n    let version = next();\n    let codec = DAG_PB_CODE;\n    if (version === 18) {\n      version = 0;\n      offset = 0;\n    } else if (version === 1) {\n      codec = next();\n    }\n    if (version !== 0 && version !== 1) {\n      throw new RangeError(`Invalid CID version ${ version }`);\n    }\n    const prefixSize = offset;\n    const multihashCode = next();\n    const digestSize = next();\n    const size = offset + digestSize;\n    const multihashSize = size - prefixSize;\n    return {\n      version,\n      codec,\n      multihashCode,\n      digestSize,\n      multihashSize,\n      size\n    };\n  }\n  static parse(source, base) {\n    const [prefix, bytes] = parseCIDtoBytes(source, base);\n    const cid = CID.decode(bytes);\n    cid._baseCache.set(prefix, source);\n    return cid;\n  }\n}\nconst parseCIDtoBytes = (source, base) => {\n  switch (source[0]) {\n  case 'Q': {\n      const decoder = base || base58btc;\n      return [\n        base58btc.prefix,\n        decoder.decode(`${ base58btc.prefix }${ source }`)\n      ];\n    }\n  case base58btc.prefix: {\n      const decoder = base || base58btc;\n      return [\n        base58btc.prefix,\n        decoder.decode(source)\n      ];\n    }\n  case base32.prefix: {\n      const decoder = base || base32;\n      return [\n        base32.prefix,\n        decoder.decode(source)\n      ];\n    }\n  default: {\n      if (base == null) {\n        throw Error('To parse non base32 or base58btc encoded CID multibase decoder must be provided');\n      }\n      return [\n        source[0],\n        base.decode(source)\n      ];\n    }\n  }\n};\nconst toStringV0 = (bytes, cache, base) => {\n  const {prefix} = base;\n  if (prefix !== base58btc.prefix) {\n    throw Error(`Cannot string encode V0 in ${ base.name } encoding`);\n  }\n  const cid = cache.get(prefix);\n  if (cid == null) {\n    const cid = base.encode(bytes).slice(1);\n    cache.set(prefix, cid);\n    return cid;\n  } else {\n    return cid;\n  }\n};\nconst toStringV1 = (bytes, cache, base) => {\n  const {prefix} = base;\n  const cid = cache.get(prefix);\n  if (cid == null) {\n    const cid = base.encode(bytes);\n    cache.set(prefix, cid);\n    return cid;\n  } else {\n    return cid;\n  }\n};\nconst DAG_PB_CODE = 112;\nconst SHA_256_CODE = 18;\nconst encodeCID = (version, code, multihash) => {\n  const codeOffset = varint.encodingLength(version);\n  const hashOffset = codeOffset + varint.encodingLength(code);\n  const bytes = new Uint8Array(hashOffset + multihash.byteLength);\n  varint.encodeTo(version, bytes, 0);\n  varint.encodeTo(code, bytes, codeOffset);\n  bytes.set(multihash, hashOffset);\n  return bytes;\n};\nconst cidSymbol = Symbol.for('@ipld/js-cid/CID');\nconst readonly = {\n  writable: false,\n  configurable: false,\n  enumerable: true\n};\nconst hidden = {\n  writable: false,\n  enumerable: false,\n  configurable: false\n};\nconst version = '0.0.0-dev';\nconst deprecate = (range, message) => {\n  if (range.test(version)) {\n    console.warn(message);\n  } else {\n    throw new Error(message);\n  }\n};\nconst IS_CID_DEPRECATION = `CID.isCID(v) is deprecated and will be removed in the next major release.\nFollowing code pattern:\n\nif (CID.isCID(value)) {\n  doSomethingWithCID(value)\n}\n\nIs replaced with:\n\nconst cid = CID.asCID(value)\nif (cid) {\n  // Make sure to use cid instead of value\n  doSomethingWithCID(cid)\n}\n`;", "import * as identityBase from './bases/identity.js';\nimport * as base2 from './bases/base2.js';\nimport * as base8 from './bases/base8.js';\nimport * as base10 from './bases/base10.js';\nimport * as base16 from './bases/base16.js';\nimport * as base32 from './bases/base32.js';\nimport * as base36 from './bases/base36.js';\nimport * as base58 from './bases/base58.js';\nimport * as base64 from './bases/base64.js';\nimport * as base256emoji from './bases/base256emoji.js';\nimport * as sha2 from './hashes/sha2.js';\nimport * as identity from './hashes/identity.js';\nimport * as raw from './codecs/raw.js';\nimport * as json from './codecs/json.js';\nimport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes\n} from './index.js';\nconst bases = {\n  ...identityBase,\n  ...base2,\n  ...base8,\n  ...base10,\n  ...base16,\n  ...base32,\n  ...base36,\n  ...base58,\n  ...base64,\n  ...base256emoji\n};\nconst hashes = {\n  ...sha2,\n  ...identity\n};\nconst codecs = {\n  raw,\n  json\n};\nexport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes,\n  hashes,\n  bases,\n  codecs\n};", "import { bases } from 'multiformats/basics';\nimport { allocUnsafe } from '../alloc.js';\nfunction createCodec(name, prefix, encode, decode) {\n  return {\n    name,\n    prefix,\n    encoder: {\n      name,\n      prefix,\n      encode\n    },\n    decoder: { decode }\n  };\n}\nconst string = createCodec('utf8', 'u', buf => {\n  const decoder = new TextDecoder('utf8');\n  return 'u' + decoder.decode(buf);\n}, str => {\n  const encoder = new TextEncoder();\n  return encoder.encode(str.substring(1));\n});\nconst ascii = createCodec('ascii', 'a', buf => {\n  let string = 'a';\n  for (let i = 0; i < buf.length; i++) {\n    string += String.fromCharCode(buf[i]);\n  }\n  return string;\n}, str => {\n  str = str.substring(1);\n  const buf = allocUnsafe(str.length);\n  for (let i = 0; i < str.length; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n});\nconst BASES = {\n  utf8: string,\n  'utf-8': string,\n  hex: bases.base16,\n  latin1: ascii,\n  ascii: ascii,\n  binary: ascii,\n  ...bases\n};\nexport default BASES;", "import bases from './util/bases.js';\nimport { asUint8Array } from './util/as-uint8array.js';\nexport function fromString(string, encoding = 'utf8') {\n  const base = bases[encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return asUint8Array(globalThis.Buffer.from(string, 'utf-8'));\n  }\n  return base.decoder.decode(`${ base.prefix }${ string }`);\n}", "import bases from './util/bases.js';\nexport function toString(array, encoding = 'utf8') {\n  const base = bases[encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return globalThis.Buffer.from(array.buffer, array.byteOffset, array.byteLength).toString('utf8');\n  }\n  return base.encoder.encode(array).substring(1);\n}", "export function assertType(obj: any, key: string, type = \"string\") {\n  if (!obj[key] || typeof obj[key] !== type) {\n    throw new Error(`Missing or invalid \"${key}\" param`);\n  }\n}\n\nexport function hasRequiredParams(params: any, required: string[]) {\n  let matches = true;\n  required.forEach((key) => {\n    const exists = key in params;\n    if (!exists) {\n      matches = false;\n    }\n  });\n  return matches;\n}\n\nexport function hasExactParamsLength(params: any, length: number): boolean {\n  return Array.isArray(params) ? params.length === length : Object.keys(params).length === length;\n}\n\nexport function hasRequiredParamsLength(params: any, minLength: number): boolean {\n  return Array.isArray(params)\n    ? params.length >= minLength\n    : Object.keys(params).length >= minLength;\n}\n\nexport function checkParams(params: any, required: string[], optional: string[]) {\n  const exact = !optional.length;\n  const matchesLength = exact\n    ? hasExactParamsLength(params, required.length)\n    : hasRequiredParamsLength(params, required.length);\n  if (!matchesLength) return false;\n  return hasRequiredParams(params, required);\n}\n\nexport function methodEndsWith(method: string, expected: string, separator = \"_\") {\n  const split = method.split(separator);\n  return split[split.length - 1].trim().toLowerCase() === expected.trim().toLowerCase();\n}\n", "import { JsonRpcRequest } from \"@walletconnect/jsonrpc-types\";\n\nimport { checkParams, methodEndsWith } from \"./misc\";\nimport { RelayJsonRpc } from \"./types\";\n\n// ---------- Subscribe ----------------------------------------------- //\n\nexport function isSubscribeRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.SubscribeParams> {\n  return isSubscribeMethod(request.method) && isSubscribeParams(request.params);\n}\n\nexport function isSubscribeMethod(method: string): boolean {\n  return methodEndsWith(method, \"subscribe\");\n}\n\nexport function isSubscribeParams(params: any): params is RelayJsonRpc.SubscribeParams {\n  const required = [\"topic\"];\n  const optional: string[] = [];\n  return checkParams(params, required, optional);\n}\n\n// ---------- Publish ----------------------------------------------- //\n\nexport function isPublishRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.PublishParams> {\n  return isPublishMethod(request.method) && isPublishParams(request.params);\n}\n\nexport function isPublishMethod(method: string): boolean {\n  return methodEndsWith(method, \"publish\");\n}\n\nexport function isPublishParams(params: any): params is RelayJsonRpc.PublishParams {\n  const required = [\"message\", \"topic\", \"ttl\"];\n  const optional = [\"prompt\", \"tag\"];\n  return checkParams(params, required, optional);\n}\n\n// ---------- Unsubscribe ----------------------------------------------- //\n\nexport function isUnsubscribeRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.UnsubscribeParams> {\n  return isUnsubscribeMethod(request.method) && isUnsubscribeParams(request.params);\n}\n\nexport function isUnsubscribeMethod(method: string): boolean {\n  return methodEndsWith(method, \"unsubscribe\");\n}\n\nexport function isUnsubscribeParams(params: any): params is RelayJsonRpc.UnsubscribeParams {\n  const required = [\"id\", \"topic\"];\n  const optional: string[] = [];\n  return checkParams(params, required, optional);\n}\n\n// ---------- Subscription ----------------------------------------------- //\n\nexport function isSubscriptionRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.SubscriptionParams> {\n  return isSubscriptionMethod(request.method) && isSubscriptionParams(request.params);\n}\n\nexport function isSubscriptionMethod(method: string): boolean {\n  return methodEndsWith(method, \"subscription\");\n}\n\nexport function isSubscriptionParams(params: any): params is RelayJsonRpc.SubscriptionParams {\n  const required = [\"id\", \"data\"];\n  const optional: string[] = [];\n  return checkParams(params, required, optional);\n}\n", "import { JsonRpcRequest } from \"@walletconnect/jsonrpc-types\";\n\nimport { RelayJsonRpc } from \"./types\";\nimport { assertType } from \"./misc\";\nimport {\n  isPublishMethod,\n  isPublishParams,\n  isSubscribeMethod,\n  isSubscribeParams,\n  isSubscriptionMethod,\n  isSubscriptionParams,\n  isUnsubscribeMethod,\n  isUnsubscribeParams,\n} from \"./validators\";\n\nexport function parseSubscribeRequest(request: JsonRpcRequest): RelayJsonRpc.SubscribeParams {\n  if (!isSubscribeMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid subscribe method\");\n  }\n  if (!isSubscribeParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid subscribe params\");\n  }\n  const params = request.params as RelayJsonRpc.SubscribeParams;\n\n  assertType(params, \"topic\");\n\n  return params;\n}\n\nexport function parsePublishRequest(request: JsonRpcRequest): RelayJsonRpc.PublishParams {\n  if (!isPublishMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid publish method\");\n  }\n  if (!isPublishParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid publish params\");\n  }\n  const params = request.params as RelayJsonRpc.PublishParams;\n\n  assertType(params, \"topic\");\n  assertType(params, \"message\");\n  assertType(params, \"ttl\", \"number\");\n\n  return params;\n}\n\nexport function parseUnsubscribeRequest(request: JsonRpcRequest): RelayJsonRpc.UnsubscribeParams {\n  if (!isUnsubscribeMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid unsubscribe method\");\n  }\n  if (!isUnsubscribeParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid unsubscribe params\");\n  }\n  const params = request.params as RelayJsonRpc.UnsubscribeParams;\n\n  assertType(params, \"id\");\n\n  return params;\n}\n\nexport function parseSubscriptionRequest(request: JsonRpcRequest): RelayJsonRpc.SubscriptionParams {\n  if (!isSubscriptionMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid subscription method\");\n  }\n  if (!isSubscriptionParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid subscription params\");\n  }\n  const params = request.params as RelayJsonRpc.SubscriptionParams;\n\n  assertType(params, \"id\");\n  assertType(params, \"data\");\n\n  return params;\n}\n", "import { RelayJsonRpc } from \"./types\";\n\nexport const RELAY_JSONRPC: { [protocol: string]: RelayJsonRpc.Methods } = {\n  waku: {\n    publish: \"waku_publish\",\n    batchPublish: \"waku_batchPublish\",\n    subscribe: \"waku_subscribe\",\n    batchSubscribe: \"waku_batchSubscribe\",\n    subscription: \"waku_subscription\",\n    unsubscribe: \"waku_unsubscribe\",\n    batchUnsubscribe: \"waku_batchUnsubscribe\",\n    batchFetchMessages: \"waku_batchFetchMessages\",\n  },\n  irn: {\n    publish: \"irn_publish\",\n    batchPublish: \"irn_batchPublish\",\n    subscribe: \"irn_subscribe\",\n    batchSubscribe: \"irn_batchSubscribe\",\n    subscription: \"irn_subscription\",\n    unsubscribe: \"irn_unsubscribe\",\n    batchUnsubscribe: \"irn_batchUnsubscribe\",\n    batchFetchMessages: \"irn_batchFetchMessages\",\n  },\n  iridium: {\n    publish: \"iridium_publish\",\n    batchPublish: \"iridium_batchPublish\",\n    subscribe: \"iridium_subscribe\",\n    batchSubscribe: \"iridium_batchSubscribe\",\n    subscription: \"iridium_subscription\",\n    unsubscribe: \"iridium_unsubscribe\",\n    batchUnsubscribe: \"iridium_batchUnsubscribe\",\n    batchFetchMessages: \"iridium_batchFetchMessages\",\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAQA,aAAS,SAAS,GAAW,GAAS;AAClC,UAAM,KAAM,MAAM,KAAM,OAAQ,KAAK,IAAI;AACzC,UAAM,KAAM,MAAM,KAAM,OAAQ,KAAK,IAAI;AACzC,aAAS,KAAK,MAAS,KAAK,KAAK,KAAK,MAAO,OAAQ,KAAK;IAC9D;AAIa,YAAA,MAAO,KAAiD,QAAQ;AAG7E,aAAgB,IAAI,GAAW,GAAS;AACpC,aAAQ,IAAI,IAAK;IACrB;AAFA,YAAA,MAAA;AAKA,aAAgB,IAAI,GAAW,GAAS;AACpC,aAAQ,IAAI,IAAK;IACrB;AAFA,YAAA,MAAA;AAKA,aAAgB,KAAK,GAAW,GAAS;AACrC,aAAO,KAAK,IAAI,MAAO,KAAK;IAChC;AAFA,YAAA,OAAA;AAKA,aAAgB,KAAK,GAAW,GAAS;AACrC,aAAO,KAAM,KAAK,IAAK,MAAM;IACjC;AAFA,YAAA,OAAA;AAIA,aAAS,cAAc,GAAS;AAC5B,aAAO,OAAO,MAAM,YAAY,SAAS,CAAC,KAAK,KAAK,MAAM,CAAC,MAAM;IACrE;AAOa,YAAA,YAAa,OAA8C,aAAa;AAOxE,YAAA,mBAAmB;AAQnB,YAAA,gBAAgB,SAAC,GAAS;AACnC,aAAA,QAAA,UAAU,CAAC,MAAM,KAAK,CAAC,QAAA,oBAAoB,KAAK,QAAA;IAAhD;;;;;;;;;ACxDJ,QAAA,QAAA;AAQA,aAAgB,YAAY,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACrD,cAAU,MAAM,SAAS,CAAC,KAAK,IAAK,MAAM,SAAS,CAAC,MAAM,MAAO;IACrE;AAFA,YAAA,cAAA;AAQA,aAAgB,aAAa,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACtD,cAAS,MAAM,SAAS,CAAC,KAAK,IAAK,MAAM,SAAS,CAAC,OAAO;IAC9D;AAFA,YAAA,eAAA;AAQA,aAAgB,YAAY,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACrD,cAAU,MAAM,SAAS,CAAC,KAAK,IAAK,MAAM,MAAM,MAAM,MAAO;IACjE;AAFA,YAAA,cAAA;AAQA,aAAgB,aAAa,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACtD,cAAS,MAAM,SAAS,CAAC,KAAK,IAAK,MAAM,MAAM,OAAO;IAC1D;AAFA,YAAA,eAAA;AAYA,aAAgB,cAAc,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC5E,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,aAAO;IACX;AAJA,YAAA,gBAAA;AAMa,YAAA,eAAe;AAU5B,aAAgB,cAAc,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC5E,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,aAAO;IACX;AAJA,YAAA,gBAAA;AAMa,YAAA,eAAe;AAM5B,aAAgB,YAAY,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACrD,aAAQ,MAAM,MAAM,KAAK,KACpB,MAAM,SAAS,CAAC,KAAK,KACrB,MAAM,SAAS,CAAC,KAAK,IACtB,MAAM,SAAS,CAAC;IACxB;AALA,YAAA,cAAA;AAWA,aAAgB,aAAa,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACtD,cAAS,MAAM,MAAM,KAAK,KACrB,MAAM,SAAS,CAAC,KAAK,KACrB,MAAM,SAAS,CAAC,KAAK,IACtB,MAAM,SAAS,CAAC,OAAO;IAC/B;AALA,YAAA,eAAA;AAWA,aAAgB,YAAY,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACrD,aAAQ,MAAM,SAAS,CAAC,KAAK,KACxB,MAAM,SAAS,CAAC,KAAK,KACrB,MAAM,SAAS,CAAC,KAAK,IACtB,MAAM,MAAM;IACpB;AALA,YAAA,cAAA;AAWA,aAAgB,aAAa,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACtD,cAAS,MAAM,SAAS,CAAC,KAAK,KACzB,MAAM,SAAS,CAAC,KAAK,KACrB,MAAM,SAAS,CAAC,KAAK,IACtB,MAAM,MAAM,OAAO;IAC3B;AALA,YAAA,eAAA;AAeA,aAAgB,cAAc,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC5E,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,aAAO;IACX;AANA,YAAA,gBAAA;AAQa,YAAA,eAAe;AAU5B,aAAgB,cAAc,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC5E,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,UAAI,SAAS,CAAC,IAAI,UAAU;AAC5B,aAAO;IACX;AANA,YAAA,gBAAA;AASa,YAAA,eAAe;AAW5B,aAAgB,YAAY,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACrD,UAAM,KAAK,YAAY,OAAO,MAAM;AACpC,UAAM,KAAK,YAAY,OAAO,SAAS,CAAC;AACxC,aAAO,KAAK,aAAc,MAAO,MAAI,MAAM;IAC/C;AAJA,YAAA,cAAA;AAYA,aAAgB,aAAa,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACtD,UAAM,KAAK,aAAa,OAAO,MAAM;AACrC,UAAM,KAAK,aAAa,OAAO,SAAS,CAAC;AACzC,aAAO,KAAK,aAAc;IAC9B;AAJA,YAAA,eAAA;AAeA,aAAgB,YAAY,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACrD,UAAM,KAAK,YAAY,OAAO,MAAM;AACpC,UAAM,KAAK,YAAY,OAAO,SAAS,CAAC;AACxC,aAAO,KAAK,aAAc,MAAO,MAAI,MAAM;IAC/C;AAJA,YAAA,cAAA;AAaA,aAAgB,aAAa,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACtD,UAAM,KAAK,aAAa,OAAO,MAAM;AACrC,UAAM,KAAK,aAAa,OAAO,SAAS,CAAC;AACzC,aAAO,KAAK,aAAc;IAC9B;AAJA,YAAA,eAAA;AAgBA,aAAgB,cAAc,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC5E,oBAAc,QAAQ,eAAgB,GAAG,KAAK,MAAM;AACpD,oBAAc,UAAU,GAAG,KAAK,SAAS,CAAC;AAC1C,aAAO;IACX;AAJA,YAAA,gBAAA;AAMa,YAAA,eAAe;AAY5B,aAAgB,cAAc,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC5E,oBAAc,UAAU,GAAG,KAAK,MAAM;AACtC,oBAAc,QAAQ,eAAgB,GAAG,KAAK,SAAS,CAAC;AACxD,aAAO;IACX;AAJA,YAAA,gBAAA;AAMa,YAAA,eAAe;AAQ5B,aAAgB,WAAW,WAAmB,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAEvE,UAAI,YAAY,MAAM,GAAG;AACrB,cAAM,IAAI,MAAM,oDAAoD;;AAExE,UAAI,YAAY,IAAI,MAAM,SAAS,QAAQ;AACvC,cAAM,IAAI,MAAM,wDAAwD;;AAE5E,UAAI,SAAS;AACb,UAAI,MAAM;AACV,eAAS,IAAI,YAAY,IAAI,SAAS,GAAG,KAAK,QAAQ,KAAK;AACvD,kBAAU,MAAM,CAAC,IAAI;AACrB,eAAO;;AAEX,aAAO;IACX;AAfA,YAAA,aAAA;AAuBA,aAAgB,WAAW,WAAmB,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAEvE,UAAI,YAAY,MAAM,GAAG;AACrB,cAAM,IAAI,MAAM,oDAAoD;;AAExE,UAAI,YAAY,IAAI,MAAM,SAAS,QAAQ;AACvC,cAAM,IAAI,MAAM,wDAAwD;;AAE5E,UAAI,SAAS;AACb,UAAI,MAAM;AACV,eAAS,IAAI,QAAQ,IAAI,SAAS,YAAY,GAAG,KAAK;AAClD,kBAAU,MAAM,CAAC,IAAI;AACrB,eAAO;;AAEX,aAAO;IACX;AAfA,YAAA,aAAA;AA2BA,aAAgB,YAAY,WAAmB,OAC3C,KAAqC,QAAU;AAA/C,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,YAAY,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAE/C,UAAI,YAAY,MAAM,GAAG;AACrB,cAAM,IAAI,MAAM,qDAAqD;;AAEzE,UAAI,CAAC,MAAA,cAAc,KAAK,GAAG;AACvB,cAAM,IAAI,MAAM,sCAAsC;;AAE1D,UAAI,MAAM;AACV,eAAS,IAAI,YAAY,IAAI,SAAS,GAAG,KAAK,QAAQ,KAAK;AACvD,YAAI,CAAC,IAAK,QAAQ,MAAO;AACzB,eAAO;;AAEX,aAAO;IACX;AAfA,YAAA,cAAA;AA2BA,aAAgB,YAAY,WAAmB,OAC3C,KAAqC,QAAU;AAA/C,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,YAAY,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAE/C,UAAI,YAAY,MAAM,GAAG;AACrB,cAAM,IAAI,MAAM,qDAAqD;;AAEzE,UAAI,CAAC,MAAA,cAAc,KAAK,GAAG;AACvB,cAAM,IAAI,MAAM,sCAAsC;;AAE1D,UAAI,MAAM;AACV,eAAS,IAAI,QAAQ,IAAI,SAAS,YAAY,GAAG,KAAK;AAClD,YAAI,CAAC,IAAK,QAAQ,MAAO;AACzB,eAAO;;AAEX,aAAO;IACX;AAfA,YAAA,cAAA;AAqBA,aAAgB,cAAc,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACvD,UAAM,OAAO,IAAI,SAAS,MAAM,QAAQ,MAAM,YAAY,MAAM,UAAU;AAC1E,aAAO,KAAK,WAAW,MAAM;IACjC;AAHA,YAAA,gBAAA;AASA,aAAgB,cAAc,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACvD,UAAM,OAAO,IAAI,SAAS,MAAM,QAAQ,MAAM,YAAY,MAAM,UAAU;AAC1E,aAAO,KAAK,WAAW,QAAQ,IAAI;IACvC;AAHA,YAAA,gBAAA;AASA,aAAgB,cAAc,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACvD,UAAM,OAAO,IAAI,SAAS,MAAM,QAAQ,MAAM,YAAY,MAAM,UAAU;AAC1E,aAAO,KAAK,WAAW,MAAM;IACjC;AAHA,YAAA,gBAAA;AASA,aAAgB,cAAc,OAAmB,QAAU;AAAV,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AACvD,UAAM,OAAO,IAAI,SAAS,MAAM,QAAQ,MAAM,YAAY,MAAM,UAAU;AAC1E,aAAO,KAAK,WAAW,QAAQ,IAAI;IACvC;AAHA,YAAA,gBAAA;AAaA,aAAgB,eAAe,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC7E,UAAM,OAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AACpE,WAAK,WAAW,QAAQ,KAAK;AAC7B,aAAO;IACX;AAJA,YAAA,iBAAA;AAcA,aAAgB,eAAe,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC7E,UAAM,OAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AACpE,WAAK,WAAW,QAAQ,OAAO,IAAI;AACnC,aAAO;IACX;AAJA,YAAA,iBAAA;AAcA,aAAgB,eAAe,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC7E,UAAM,OAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AACpE,WAAK,WAAW,QAAQ,KAAK;AAC7B,aAAO;IACX;AAJA,YAAA,iBAAA;AAcA,aAAgB,eAAe,OAAe,KAAyB,QAAU;AAAnC,UAAA,QAAA,QAAA;AAAA,cAAA,IAAU,WAAW,CAAC;MAAC;AAAE,UAAA,WAAA,QAAA;AAAA,iBAAA;MAAU;AAC7E,UAAM,OAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AACpE,WAAK,WAAW,QAAQ,OAAO,IAAI;AACnC,aAAO;IACX;AAJA,YAAA,iBAAA;;;;;;;;;AC1ZA,aAAgB,KAAK,OAAmB;AAIpC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,cAAM,CAAC,IAAI;;AAEf,aAAO;IACX;AARA,YAAA,OAAA;;;;;;;;;AChBA,QAAA,WAAA;AACA,QAAA,SAAA;AAGA,QAAM,SAAS;AAIf,aAAS,KAAK,KAAiB,OAAmB,KAAe;AAC7D,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAM,IAAI,CAAC,KAAK,KAAO,IAAI,CAAC,KAAK,KAAO,IAAI,CAAC,KAAK,IAAK,IAAI,CAAC;AAChE,UAAI,KAAM,IAAI,CAAC,KAAK,KAAO,IAAI,CAAC,KAAK,KAAO,IAAI,CAAC,KAAK,IAAK,IAAI,CAAC;AAChE,UAAI,KAAM,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,KAAO,IAAI,CAAC,KAAK,IAAK,IAAI,CAAC;AAClE,UAAI,KAAM,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,IAAK,IAAI,EAAE;AACpE,UAAI,KAAM,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,IAAK,IAAI,EAAE;AACpE,UAAI,KAAM,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,IAAK,IAAI,EAAE;AACpE,UAAI,MAAO,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,IAAK,IAAI,EAAE;AACrE,UAAI,MAAO,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,KAAO,IAAI,EAAE,KAAK,IAAK,IAAI,EAAE;AACrE,UAAI,MAAO,MAAM,CAAC,KAAK,KAAO,MAAM,CAAC,KAAK,KAAO,MAAM,CAAC,KAAK,IAAK,MAAM,CAAC;AACzE,UAAI,MAAO,MAAM,CAAC,KAAK,KAAO,MAAM,CAAC,KAAK,KAAO,MAAM,CAAC,KAAK,IAAK,MAAM,CAAC;AACzE,UAAI,MAAO,MAAM,EAAE,KAAK,KAAO,MAAM,EAAE,KAAK,KAAO,MAAM,CAAC,KAAK,IAAK,MAAM,CAAC;AAC3E,UAAI,MAAO,MAAM,EAAE,KAAK,KAAO,MAAM,EAAE,KAAK,KAAO,MAAM,EAAE,KAAK,IAAK,MAAM,EAAE;AAE7E,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AAEV,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAChC,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,KAAM,OAAO;AAC9D,aAAK,KAAK,MAAM;AAAG,cAAM;AAAI,aAAK,OAAQ,KAAK,KAAM,MAAM;AAC3D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,KAAM,OAAO;AAC9D,aAAK,KAAK,MAAM;AAAG,cAAM;AAAI,aAAK,OAAQ,KAAK,KAAM,MAAM;AAE3D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,KAAM,OAAO;AAC9D,cAAM,MAAM,MAAM;AAAG,cAAM;AAAK,aAAK,OAAQ,KAAK,KAAM,MAAM;AAC9D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,KAAM,OAAO;AAC9D,cAAM,MAAM,MAAM;AAAG,cAAM;AAAK,aAAK,OAAQ,KAAK,KAAM,MAAM;AAE9D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,IAAK,OAAO;AAC7D,cAAM,MAAM,MAAM;AAAG,cAAM;AAAK,aAAK,OAAQ,KAAK,IAAK,MAAM;AAC7D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,IAAK,OAAO;AAC7D,cAAM,MAAM,MAAM;AAAG,cAAM;AAAK,aAAK,OAAQ,KAAK,IAAK,MAAM;AAE7D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,IAAK,OAAO;AAC7D,aAAK,KAAK,MAAM;AAAG,cAAM;AAAI,aAAK,OAAQ,KAAK,IAAK,MAAM;AAC1D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,IAAK,OAAO;AAC7D,aAAK,KAAK,MAAM;AAAG,cAAM;AAAI,aAAK,OAAQ,KAAK,IAAK,MAAM;AAE1D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,KAAM,OAAO;AAC9D,cAAM,MAAM,MAAM;AAAG,cAAM;AAAK,aAAK,OAAQ,KAAK,KAAM,MAAM;AAC9D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,KAAM,OAAO;AAC9D,cAAM,MAAM,MAAM;AAAG,cAAM;AAAK,aAAK,OAAQ,KAAK,KAAM,MAAM;AAE9D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,KAAM,OAAO;AAC9D,aAAK,KAAK,MAAM;AAAG,cAAM;AAAI,aAAK,OAAQ,KAAK,KAAM,MAAM;AAC3D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,KAAM,OAAO;AAC9D,aAAK,KAAK,MAAM;AAAG,cAAM;AAAI,aAAK,OAAQ,KAAK,KAAM,MAAM;AAE3D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,IAAK,OAAO;AAC7D,aAAK,KAAK,MAAM;AAAG,cAAM;AAAI,aAAK,OAAQ,KAAK,IAAK,MAAM;AAC1D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,IAAK,OAAO;AAC7D,aAAK,KAAK,MAAM;AAAG,cAAM;AAAI,aAAK,OAAQ,KAAK,IAAK,MAAM;AAE1D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,IAAK,OAAO;AAC7D,cAAM,MAAM,MAAM;AAAG,cAAM;AAAK,aAAK,OAAQ,KAAK,IAAK,MAAM;AAC7D,aAAK,KAAK,KAAK;AAAG,eAAO;AAAI,cAAM,QAAS,KAAK,IAAK,OAAO;AAC7D,cAAM,MAAM,MAAM;AAAG,cAAM;AAAK,aAAK,OAAQ,KAAK,IAAK,MAAM;;AAEjE,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,CAAC;AACjC,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,CAAC;AACjC,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,CAAC;AACjC,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,EAAE;AAClC,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,EAAE;AAClC,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,EAAE;AAClC,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,EAAE;AAClC,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,EAAE;AAClC,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,EAAE;AAClC,eAAA,cAAc,KAAK,KAAK,GAAG,KAAK,EAAE;AAClC,eAAA,cAAc,MAAM,MAAM,GAAG,KAAK,EAAE;AACpC,eAAA,cAAc,MAAM,MAAM,GAAG,KAAK,EAAE;AACpC,eAAA,cAAc,MAAM,MAAM,GAAG,KAAK,EAAE;AACpC,eAAA,cAAc,MAAM,MAAM,GAAG,KAAK,EAAE;AACpC,eAAA,cAAc,MAAM,MAAM,GAAG,KAAK,EAAE;AACpC,eAAA,cAAc,MAAM,MAAM,GAAG,KAAK,EAAE;IACxC;AAyBA,aAAgB,UAAU,KAAiB,OACvCA,MAAiB,KAAiB,2BAA6B;AAA7B,UAAA,8BAAA,QAAA;AAAA,oCAAA;MAA6B;AAE/D,UAAI,IAAI,WAAW,IAAI;AACnB,cAAM,IAAI,MAAM,mCAAmC;;AAGvD,UAAI,IAAI,SAASA,KAAI,QAAQ;AACzB,cAAM,IAAI,MAAM,4CAA4C;;AAGhE,UAAI;AACJ,UAAI;AAEJ,UAAI,8BAA8B,GAAG;AACjC,YAAI,MAAM,WAAW,KAAK,MAAM,WAAW,IAAI;AAC3C,gBAAM,IAAI,MAAM,oCAAoC;;AAExD,aAAK,IAAI,WAAW,EAAE;AAEtB,wBAAgB,GAAG,SAAS,MAAM;AAElC,WAAG,IAAI,OAAO,aAAa;aACxB;AACH,YAAI,MAAM,WAAW,IAAI;AACrB,gBAAM,IAAI,MAAM,4CAA4C;;AAGhE,aAAK;AACL,wBAAgB;;AAIpB,UAAM,QAAQ,IAAI,WAAW,EAAE;AAE/B,eAAS,IAAI,GAAG,IAAIA,KAAI,QAAQ,KAAK,IAAI;AAErC,aAAK,OAAO,IAAI,GAAG;AAGnB,iBAAS,IAAI,GAAG,IAAI,IAAI,MAAM,IAAIA,KAAI,QAAQ,KAAK;AAC/C,cAAI,CAAC,IAAIA,KAAI,CAAC,IAAI,MAAM,IAAI,CAAC;;AAIjC,yBAAiB,IAAI,GAAG,aAAa;;AAIzC,aAAA,KAAK,KAAK;AAEV,UAAI,8BAA8B,GAAG;AAEjC,eAAA,KAAK,EAAE;;AAGX,aAAO;IACX;AAzDA,YAAA,YAAA;AAsEA,aAAgB,OAAO,KAAiB,OACpC,KAAiB,2BAA6B;AAA7B,UAAA,8BAAA,QAAA;AAAA,oCAAA;MAA6B;AAC9C,aAAA,KAAK,GAAG;AACR,aAAO,UAAU,KAAK,OAAO,KAAK,KAAK,yBAAyB;IACpE;AAJA,YAAA,SAAA;AAMA,aAAS,iBAAiB,SAAqB,KAAa,KAAW;AACnE,UAAI,QAAQ;AACZ,aAAO,OAAO;AACV,gBAAQ,SAAS,QAAQ,GAAG,IAAI,OAAQ;AACxC,gBAAQ,GAAG,IAAI,QAAQ;AACvB,mBAAW;AACX;;AAEJ,UAAI,QAAQ,GAAG;AACX,cAAM,IAAI,MAAM,0BAA0B;;IAElD;;;;;;;;;ACzMA,aAAgB,OAAO,SAAiB,aAAqB,cAAoB;AAC7E,aAAQ,EAAE,UAAU,KAAK,cAAiB,UAAU,IAAK;IAC7D;AAFA,YAAA,SAAA;AAQA,aAAgB,YAAY,GAAW,GAAS;AAC5C,cAAU,IAAI,MAAM,IAAI,KAAK,MAAO,KAAM;IAC9C;AAFA,YAAA,cAAA;AAWA,aAAgBC,SAAQ,GAAe,GAAa;AAChD,UAAI,EAAE,WAAW,EAAE,QAAQ;AACvB,eAAO;;AAEX,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC/B,kBAAU,EAAE,CAAC,IAAI,EAAE,CAAC;;AAExB,aAAQ,IAAM,SAAS,MAAO;IAClC;AATA,YAAA,UAAAA;AAkBA,aAAgB,MAAM,GAAe,GAAa;AAC9C,UAAI,EAAE,WAAW,KAAK,EAAE,WAAW,GAAG;AAClC,eAAO;;AAEX,aAAOA,SAAQ,GAAG,CAAC,MAAM;IAC7B;AALA,YAAA,QAAA;;;;;;;;;AChDA,QAAA,kBAAA;AACA,QAAA,SAAA;AAEa,YAAA,gBAAgB;AAY7B,QAAA;;MAAA,WAAA;AAWI,iBAAAC,UAAY,KAAe;AAVlB,eAAA,eAAe,QAAA;AAEhB,eAAA,UAAU,IAAI,WAAW,EAAE;AAC3B,eAAA,KAAK,IAAI,YAAY,EAAE;AACvB,eAAA,KAAK,IAAI,YAAY,EAAE;AACvB,eAAA,OAAO,IAAI,YAAY,CAAC;AACxB,eAAA,YAAY;AACZ,eAAA,OAAO;AACP,eAAA,YAAY;AAGhB,cAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAG,eAAK,GAAG,CAAC,IAAK,KAAM;AACnD,cAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAG,eAAK,GAAG,CAAC,KAAM,OAAO,KAAO,MAAM,KAAM;AACxE,cAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAG,eAAK,GAAG,CAAC,KAAM,OAAO,KAAO,MAAM,KAAM;AACxE,cAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAG,eAAK,GAAG,CAAC,KAAM,OAAO,IAAM,MAAM,KAAM;AACvE,cAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AAAG,eAAK,GAAG,CAAC,KAAM,OAAO,IAAM,MAAM,MAAO;AACxE,eAAK,GAAG,CAAC,IAAM,OAAO,IAAM;AAC5B,cAAI,KAAK,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AAAG,eAAK,GAAG,CAAC,KAAM,OAAO,KAAO,MAAM,KAAM;AAC1E,cAAI,KAAK,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AAAG,eAAK,GAAG,CAAC,KAAM,OAAO,KAAO,MAAM,KAAM;AAC1E,cAAI,KAAK,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AAAG,eAAK,GAAG,CAAC,KAAM,OAAO,IAAM,MAAM,KAAM;AACzE,eAAK,GAAG,CAAC,IAAM,OAAO,IAAM;AAE5B,eAAK,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AACpC,eAAK,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AACpC,eAAK,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AACpC,eAAK,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AACpC,eAAK,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AACpC,eAAK,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AACpC,eAAK,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;AACpC,eAAK,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;QACxC;AAEQ,QAAAA,UAAA,UAAA,UAAR,SAAgBC,IAAe,MAAc,OAAa;AACtD,cAAI,QAAQ,KAAK,OAAO,IAAI,KAAK;AAEjC,cAAI,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC;AAElB,cAAI,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC,GACd,KAAK,KAAK,GAAG,CAAC;AAElB,iBAAO,SAAS,IAAI;AAChB,gBAAI,KAAKA,GAAE,OAAO,CAAC,IAAIA,GAAE,OAAO,CAAC,KAAK;AAAG,kBAAO,KAAM;AACtD,gBAAI,KAAKA,GAAE,OAAO,CAAC,IAAIA,GAAE,OAAO,CAAC,KAAK;AAAG,mBAAQ,OAAO,KAAO,MAAM,KAAM;AAC3E,gBAAI,KAAKA,GAAE,OAAO,CAAC,IAAIA,GAAE,OAAO,CAAC,KAAK;AAAG,mBAAQ,OAAO,KAAO,MAAM,KAAM;AAC3E,gBAAI,KAAKA,GAAE,OAAO,CAAC,IAAIA,GAAE,OAAO,CAAC,KAAK;AAAG,mBAAQ,OAAO,IAAM,MAAM,KAAM;AAC1E,gBAAI,KAAKA,GAAE,OAAO,CAAC,IAAIA,GAAE,OAAO,CAAC,KAAK;AAAG,mBAAQ,OAAO,IAAM,MAAM,MAAO;AAC3E,kBAAQ,OAAO,IAAM;AACrB,gBAAI,KAAKA,GAAE,OAAO,EAAE,IAAIA,GAAE,OAAO,EAAE,KAAK;AAAG,mBAAQ,OAAO,KAAO,MAAM,KAAM;AAC7E,gBAAI,KAAKA,GAAE,OAAO,EAAE,IAAIA,GAAE,OAAO,EAAE,KAAK;AAAG,mBAAQ,OAAO,KAAO,MAAM,KAAM;AAC7E,gBAAI,KAAKA,GAAE,OAAO,EAAE,IAAIA,GAAE,OAAO,EAAE,KAAK;AAAG,mBAAQ,OAAO,IAAM,MAAM,KAAM;AAC5E,kBAAQ,OAAO,IAAM;AAErB,gBAAI,IAAI;AAER,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,iBAAM,OAAO;AAAK,kBAAM;AAExB,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,iBAAM,OAAO;AAAK,kBAAM;AAExB,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,iBAAM,OAAO;AAAK,kBAAM;AAExB,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,MAAM,IAAI;AAChB,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,iBAAM,OAAO;AAAK,kBAAM;AAExB,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,iBAAM,OAAO;AAAK,kBAAM;AAExB,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,KAAK;AACX,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,iBAAM,OAAO;AAAK,kBAAM;AAExB,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,iBAAM,OAAO;AAAK,kBAAM;AAExB,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,iBAAM,OAAO;AAAK,kBAAM;AAExB,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,MAAM,IAAI;AAChB,iBAAM,OAAO;AAAK,kBAAM;AAExB,gBAAI,KAAK;AACT,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,gBAAK,OAAO;AAAK,kBAAM;AACvB,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,kBAAM,KAAK;AACX,iBAAM,OAAO;AAAK,kBAAM;AAExB,iBAAO,KAAK,KAAK,IAAM;AACvB,gBAAK,IAAI,KAAM;AACf,iBAAK,IAAI;AACT,gBAAK,MAAM;AACX,kBAAM;AAEN,iBAAK;AACL,iBAAK;AACL,iBAAK;AACL,iBAAK;AACL,iBAAK;AACL,iBAAK;AACL,iBAAK;AACL,iBAAK;AACL,iBAAK;AACL,iBAAK;AAEL,oBAAQ;AACR,qBAAS;;AAEb,eAAK,GAAG,CAAC,IAAI;AACb,eAAK,GAAG,CAAC,IAAI;AACb,eAAK,GAAG,CAAC,IAAI;AACb,eAAK,GAAG,CAAC,IAAI;AACb,eAAK,GAAG,CAAC,IAAI;AACb,eAAK,GAAG,CAAC,IAAI;AACb,eAAK,GAAG,CAAC,IAAI;AACb,eAAK,GAAG,CAAC,IAAI;AACb,eAAK,GAAG,CAAC,IAAI;AACb,eAAK,GAAG,CAAC,IAAI;QACjB;AAEA,QAAAD,UAAA,UAAA,SAAA,SAAO,KAAiB,QAAU;AAAV,cAAA,WAAA,QAAA;AAAA,qBAAA;UAAU;AAC9B,cAAM,IAAI,IAAI,YAAY,EAAE;AAC5B,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AAEJ,cAAI,KAAK,WAAW;AAChB,gBAAI,KAAK;AACT,iBAAK,QAAQ,GAAG,IAAI;AACpB,mBAAO,IAAI,IAAI,KAAK;AAChB,mBAAK,QAAQ,CAAC,IAAI;;AAEtB,iBAAK,OAAO;AACZ,iBAAK,QAAQ,KAAK,SAAS,GAAG,EAAE;;AAGpC,cAAI,KAAK,GAAG,CAAC,MAAM;AACnB,eAAK,GAAG,CAAC,KAAK;AACd,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACrB,iBAAK,GAAG,CAAC,KAAK;AACd,gBAAI,KAAK,GAAG,CAAC,MAAM;AACnB,iBAAK,GAAG,CAAC,KAAK;;AAElB,eAAK,GAAG,CAAC,KAAM,IAAI;AACnB,cAAI,KAAK,GAAG,CAAC,MAAM;AACnB,eAAK,GAAG,CAAC,KAAK;AACd,eAAK,GAAG,CAAC,KAAK;AACd,cAAI,KAAK,GAAG,CAAC,MAAM;AACnB,eAAK,GAAG,CAAC,KAAK;AACd,eAAK,GAAG,CAAC,KAAK;AAEd,YAAE,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;AACpB,cAAI,EAAE,CAAC,MAAM;AACb,YAAE,CAAC,KAAK;AACR,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACrB,cAAE,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;AACpB,gBAAI,EAAE,CAAC,MAAM;AACb,cAAE,CAAC,KAAK;;AAEZ,YAAE,CAAC,KAAM,KAAK;AAEd,kBAAQ,IAAI,KAAK;AACjB,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACrB,cAAE,CAAC,KAAK;;AAEZ,iBAAO,CAAC;AACR,eAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACrB,iBAAK,GAAG,CAAC,IAAK,KAAK,GAAG,CAAC,IAAI,OAAQ,EAAE,CAAC;;AAG1C,eAAK,GAAG,CAAC,KAAM,KAAK,GAAG,CAAC,IAAM,KAAK,GAAG,CAAC,KAAK,MAAO;AACnD,eAAK,GAAG,CAAC,KAAM,KAAK,GAAG,CAAC,MAAM,IAAM,KAAK,GAAG,CAAC,KAAK,MAAO;AACzD,eAAK,GAAG,CAAC,KAAM,KAAK,GAAG,CAAC,MAAM,IAAM,KAAK,GAAG,CAAC,KAAK,KAAM;AACxD,eAAK,GAAG,CAAC,KAAM,KAAK,GAAG,CAAC,MAAM,IAAM,KAAK,GAAG,CAAC,KAAK,KAAM;AACxD,eAAK,GAAG,CAAC,KAAM,KAAK,GAAG,CAAC,MAAM,KAAO,KAAK,GAAG,CAAC,KAAK,IAAM,KAAK,GAAG,CAAC,KAAK,MAAO;AAC9E,eAAK,GAAG,CAAC,KAAM,KAAK,GAAG,CAAC,MAAM,IAAM,KAAK,GAAG,CAAC,KAAK,MAAO;AACzD,eAAK,GAAG,CAAC,KAAM,KAAK,GAAG,CAAC,MAAM,IAAM,KAAK,GAAG,CAAC,KAAK,KAAM;AACxD,eAAK,GAAG,CAAC,KAAM,KAAK,GAAG,CAAC,MAAM,IAAM,KAAK,GAAG,CAAC,KAAK,KAAM;AAExD,cAAI,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC;AAC5B,eAAK,GAAG,CAAC,IAAI,IAAI;AACjB,eAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACpB,iBAAO,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,IAAK,MAAM,MAAM,MAAO;AACvD,iBAAK,GAAG,CAAC,IAAI,IAAI;;AAGrB,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM;AACjC,cAAI,SAAS,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM;AAClC,cAAI,SAAS,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM;AAClC,cAAI,SAAS,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM;AAClC,cAAI,SAAS,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM;AAClC,cAAI,SAAS,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM;AAClC,cAAI,SAAS,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM;AAElC,eAAK,YAAY;AACjB,iBAAO;QACX;AAEA,QAAAA,UAAA,UAAA,SAAA,SAAOC,IAAa;AAChB,cAAI,OAAO;AACX,cAAI,QAAQA,GAAE;AACd,cAAI;AAEJ,cAAI,KAAK,WAAW;AAChB,mBAAQ,KAAK,KAAK;AAClB,gBAAI,OAAO,OAAO;AACd,qBAAO;;AAEX,qBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,mBAAK,QAAQ,KAAK,YAAY,CAAC,IAAIA,GAAE,OAAO,CAAC;;AAEjD,qBAAS;AACT,oBAAQ;AACR,iBAAK,aAAa;AAClB,gBAAI,KAAK,YAAY,IAAI;AACrB,qBAAO;;AAEX,iBAAK,QAAQ,KAAK,SAAS,GAAG,EAAE;AAChC,iBAAK,YAAY;;AAGrB,cAAI,SAAS,IAAI;AACb,mBAAO,QAAS,QAAQ;AACxB,iBAAK,QAAQA,IAAG,MAAM,IAAI;AAC1B,oBAAQ;AACR,qBAAS;;AAGb,cAAI,OAAO;AACP,qBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,mBAAK,QAAQ,KAAK,YAAY,CAAC,IAAIA,GAAE,OAAO,CAAC;;AAEjD,iBAAK,aAAa;;AAGtB,iBAAO;QACX;AAEA,QAAAD,UAAA,UAAA,SAAA,WAAA;AAGI,cAAI,KAAK,WAAW;AAChB,kBAAM,IAAI,MAAM,uBAAuB;;AAE3C,cAAI,MAAM,IAAI,WAAW,EAAE;AAC3B,eAAK,OAAO,GAAG;AACf,iBAAO;QACX;AAEA,QAAAA,UAAA,UAAA,QAAA,WAAA;AACI,iBAAA,KAAK,KAAK,OAAO;AACjB,iBAAA,KAAK,KAAK,EAAE;AACZ,iBAAA,KAAK,KAAK,EAAE;AACZ,iBAAA,KAAK,KAAK,IAAI;AACd,eAAK,YAAY;AACjB,eAAK,OAAO;AACZ,eAAK,YAAY;AACjB,iBAAO;QACX;AACJ,eAAAA;MAAA,EAzYA;;AAAa,YAAA,WAAA;AAgZb,aAAgB,YAAY,KAAiB,MAAgB;AACzD,UAAME,KAAI,IAAI,SAAS,GAAG;AAC1B,MAAAA,GAAE,OAAO,IAAI;AACb,UAAMC,UAASD,GAAE,OAAM;AACvB,MAAAA,GAAE,MAAK;AACP,aAAOC;IACX;AANA,YAAA,cAAA;AAYA,aAAgB,MAAM,GAAe,GAAa;AAC9C,UAAI,EAAE,WAAW,QAAA,iBAAiB,EAAE,WAAW,QAAA,eAAe;AAC1D,eAAO;;AAEX,aAAO,gBAAA,MAAkB,GAAG,CAAC;IACjC;AALA,YAAA,QAAA;;;;;;;;;AC1aA,QAAA,WAAA;AACA,QAAA,aAAA;AACA,QAAA,SAAA;AACA,QAAA,WAAA;AACA,QAAA,kBAAA;AAEa,YAAA,aAAa;AACb,YAAA,eAAe;AACf,YAAA,aAAa;AAE1B,QAAM,QAAQ,IAAI,WAAW,EAAE;AAO/B,QAAA;;MAAA,WAAA;AASI,iBAAAC,kBAAY,KAAe;AARlB,eAAA,cAAc,QAAA;AACd,eAAA,YAAY,QAAA;AAQjB,cAAI,IAAI,WAAW,QAAA,YAAY;AAC3B,kBAAM,IAAI,MAAM,oCAAoC;;AAGxD,eAAK,OAAO,IAAI,WAAW,GAAG;QAClC;AAcA,QAAAA,kBAAA,UAAA,OAAA,SAAK,OAAmB,WAAuB,gBAC3C,KAAgB;AAChB,cAAI,MAAM,SAAS,IAAI;AACnB,kBAAM,IAAI,MAAM,0CAA0C;;AAI9D,cAAM,UAAU,IAAI,WAAW,EAAE;AACjC,kBAAQ,IAAI,OAAO,QAAQ,SAAS,MAAM,MAAM;AAOhD,cAAM,UAAU,IAAI,WAAW,EAAE;AACjC,mBAAA,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC;AAGrC,cAAM,eAAe,UAAU,SAAS,KAAK;AAC7C,cAAI;AACJ,cAAI,KAAK;AACL,gBAAI,IAAI,WAAW,cAAc;AAC7B,oBAAM,IAAI,MAAM,gDAAgD;;AAEpE,qBAAS;iBACN;AACH,qBAAS,IAAI,WAAW,YAAY;;AAIxC,mBAAA,UAAU,KAAK,MAAM,SAAS,WAAW,QAAQ,CAAC;AAMlD,eAAK,cAAc,OAAO,SAAS,OAAO,SAAS,KAAK,WAAW,OAAO,MAAM,GAC5E,SAAS,OAAO,SAAS,GAAG,OAAO,SAAS,KAAK,SAAS,GAAG,cAAc;AAG/E,iBAAA,KAAK,OAAO;AAEZ,iBAAO;QACX;AAeA,QAAAA,kBAAA,UAAA,OAAA,SAAK,OAAmB,QAAoB,gBACxC,KAAgB;AAChB,cAAI,MAAM,SAAS,IAAI;AACnB,kBAAM,IAAI,MAAM,0CAA0C;;AAI9D,cAAI,OAAO,SAAS,KAAK,WAAW;AAEhC,mBAAO;;AAIX,cAAM,UAAU,IAAI,WAAW,EAAE;AACjC,kBAAQ,IAAI,OAAO,QAAQ,SAAS,MAAM,MAAM;AAGhD,cAAM,UAAU,IAAI,WAAW,EAAE;AACjC,mBAAA,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC;AAOrC,cAAM,gBAAgB,IAAI,WAAW,KAAK,SAAS;AACnD,eAAK,cAAc,eAAe,SAC9B,OAAO,SAAS,GAAG,OAAO,SAAS,KAAK,SAAS,GAAG,cAAc;AAGtE,cAAI,CAAC,gBAAA,MAAM,eACP,OAAO,SAAS,OAAO,SAAS,KAAK,WAAW,OAAO,MAAM,CAAC,GAAG;AACjE,mBAAO;;AAIX,cAAM,eAAe,OAAO,SAAS,KAAK;AAC1C,cAAI;AACJ,cAAI,KAAK;AACL,gBAAI,IAAI,WAAW,cAAc;AAC7B,oBAAM,IAAI,MAAM,gDAAgD;;AAEpE,qBAAS;iBACN;AACH,qBAAS,IAAI,WAAW,YAAY;;AAIxC,mBAAA,UAAU,KAAK,MAAM,SACjB,OAAO,SAAS,GAAG,OAAO,SAAS,KAAK,SAAS,GAAG,QAAQ,CAAC;AAGjE,iBAAA,KAAK,OAAO;AAEZ,iBAAO;QACX;AAEA,QAAAA,kBAAA,UAAA,QAAA,WAAA;AACI,iBAAA,KAAK,KAAK,IAAI;AACd,iBAAO;QACX;AAEQ,QAAAA,kBAAA,UAAA,gBAAR,SAAsB,QAAoB,SACtC,YAAwB,gBAA2B;AAGnD,cAAMC,KAAI,IAAI,WAAA,SAAS,OAAO;AAG9B,cAAI,gBAAgB;AAChB,YAAAA,GAAE,OAAO,cAAc;AACvB,gBAAI,eAAe,SAAS,KAAK,GAAG;AAChC,cAAAA,GAAE,OAAO,MAAM,SAAS,eAAe,SAAS,EAAE,CAAC;;;AAK3D,UAAAA,GAAE,OAAO,UAAU;AACnB,cAAI,WAAW,SAAS,KAAK,GAAG;AAC5B,YAAAA,GAAE,OAAO,MAAM,SAAS,WAAW,SAAS,EAAE,CAAC;;AAKnD,cAAMC,UAAS,IAAI,WAAW,CAAC;AAC/B,cAAI,gBAAgB;AAChB,qBAAA,cAAc,eAAe,QAAQA,OAAM;;AAE/C,UAAAD,GAAE,OAAOC,OAAM;AAGf,mBAAA,cAAc,WAAW,QAAQA,OAAM;AACvC,UAAAD,GAAE,OAAOC,OAAM;AAGf,cAAM,MAAMD,GAAE,OAAM;AACpB,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,mBAAO,CAAC,IAAI,IAAI,CAAC;;AAIrB,UAAAA,GAAE,MAAK;AACP,iBAAA,KAAK,GAAG;AACR,iBAAA,KAAKC,OAAM;QACf;AACJ,eAAAF;MAAA,EAjMA;;AAAa,YAAA,mBAAA;;;;;;;;;ACEb,aAAgB,mBAAmBG,IAAO;AACtC,aACI,OAAQA,GAAuB,cAAc,eAC7C,OAAQA,GAAuB,iBAAiB,eAChD,OAAQA,GAAuB,oBAAoB;IAE3D;AANA,YAAA,qBAAA;;;;;;;;;ACpBA,QAAA,SAAA;AACA,QAAA,kBAAA;AACA,QAAA,SAAA;AAKA,QAAA;;MAAA,WAAA;AAiBI,iBAAAC,MAAY,MAAyC,KAAe;AAV5D,eAAA,YAAY;AAYhB,eAAK,SAAS,IAAI,KAAI;AACtB,eAAK,SAAS,IAAI,KAAI;AAItB,eAAK,YAAY,KAAK,OAAO;AAC7B,eAAK,eAAe,KAAK,OAAO;AAGhC,cAAM,MAAM,IAAI,WAAW,KAAK,SAAS;AAEzC,cAAI,IAAI,SAAS,KAAK,WAAW;AAG7B,iBAAK,OAAO,OAAO,GAAG,EAAE,OAAO,GAAG,EAAE,MAAK;iBACtC;AAEH,gBAAI,IAAI,GAAG;;AAOf,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,gBAAI,CAAC,KAAK;;AAGd,eAAK,OAAO,OAAO,GAAG;AAKtB,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,gBAAI,CAAC,KAAK,KAAO;;AAGrB,eAAK,OAAO,OAAO,GAAG;AAKtB,cAAI,OAAA,mBAAmB,KAAK,MAAM,KAAK,OAAA,mBAAmB,KAAK,MAAM,GAAG;AACpE,iBAAK,mBAAmB,KAAK,OAAO,UAAS;AAC7C,iBAAK,mBAAmB,KAAK,OAAO,UAAS;;AAIjD,iBAAA,KAAK,GAAG;QACZ;AAOA,QAAAA,MAAA,UAAA,QAAA,WAAA;AACI,cAAI,CAAC,OAAA,mBAAmB,KAAK,MAAM,KAAK,CAAC,OAAA,mBAAmB,KAAK,MAAM,GAAG;AACtE,kBAAM,IAAI,MAAM,mEAAmE;;AAGvF,eAAK,OAAO,aAAa,KAAK,gBAAgB;AAC9C,eAAK,OAAO,aAAa,KAAK,gBAAgB;AAC9C,eAAK,YAAY;AACjB,iBAAO;QACX;AAKA,QAAAA,MAAA,UAAA,QAAA,WAAA;AACI,cAAI,OAAA,mBAAmB,KAAK,MAAM,GAAG;AACjC,iBAAK,OAAO,gBAAgB,KAAK,gBAAgB;;AAErD,cAAI,OAAA,mBAAmB,KAAK,MAAM,GAAG;AACjC,iBAAK,OAAO,gBAAgB,KAAK,gBAAgB;;AAErD,eAAK,OAAO,MAAK;AACjB,eAAK,OAAO,MAAK;QACrB;AAKA,QAAAA,MAAA,UAAA,SAAA,SAAO,MAAgB;AACnB,eAAK,OAAO,OAAO,IAAI;AACvB,iBAAO;QACX;AAKA,QAAAA,MAAA,UAAA,SAAA,SAAO,KAAe;AAClB,cAAI,KAAK,WAAW;AAIhB,iBAAK,OAAO,OAAO,GAAG;AACtB,mBAAO;;AAIX,eAAK,OAAO,OAAO,GAAG;AAGtB,eAAK,OAAO,OAAO,IAAI,SAAS,GAAG,KAAK,YAAY,CAAC,EAAE,OAAO,GAAG;AACjE,eAAK,YAAY;AAEjB,iBAAO;QACX;AAKA,QAAAA,MAAA,UAAA,SAAA,WAAA;AACI,cAAM,MAAM,IAAI,WAAW,KAAK,YAAY;AAC5C,eAAK,OAAO,GAAG;AACf,iBAAO;QACX;AAMA,QAAAA,MAAA,UAAA,YAAA,WAAA;AACI,cAAI,CAAC,OAAA,mBAAmB,KAAK,MAAM,GAAG;AAClC,kBAAM,IAAI,MAAM,2DAA2D;;AAE/E,iBAAO,KAAK,OAAO,UAAS;QAChC;AAEA,QAAAA,MAAA,UAAA,eAAA,SAAa,YAAe;AACxB,cAAI,CAAC,OAAA,mBAAmB,KAAK,MAAM,KAAK,CAAC,OAAA,mBAAmB,KAAK,MAAM,GAAG;AACtE,kBAAM,IAAI,MAAM,8DAA8D;;AAElF,eAAK,OAAO,aAAa,UAAU;AACnC,eAAK,OAAO,aAAa,KAAK,gBAAgB;AAC9C,eAAK,YAAY;AACjB,iBAAO;QACX;AAEA,QAAAA,MAAA,UAAA,kBAAA,SAAgB,YAAe;AAC3B,cAAI,CAAC,OAAA,mBAAmB,KAAK,MAAM,GAAG;AAClC,kBAAM,IAAI,MAAM,iEAAiE;;AAErF,eAAK,OAAO,gBAAgB,UAAU;QAC1C;AACJ,eAAAA;MAAA,EAtKA;;AAAa,YAAA,OAAA;AA2Kb,aAAgB,KAAK,MAAsB,KAAiB,MAAgB;AACxE,UAAMC,KAAI,IAAI,KAAK,MAAM,GAAG;AAC5B,MAAAA,GAAE,OAAO,IAAI;AACb,UAAMC,UAASD,GAAE,OAAM;AACvB,MAAAA,GAAE,MAAK;AACP,aAAOC;IACX;AANA,YAAA,OAAA;AAoBa,YAAA,QAAQ,gBAAA;;;;;;;;;ACrMrB,QAAA,SAAA;AACA,QAAA,SAAA;AAUA,QAAA;;MAAA,WAAA;AAgBI,iBAAAC,MAAY,MACR,KACA,MACA,MAAiB;AADjB,cAAA,SAAA,QAAA;AAAA,mBAAA,IAAW,WAAW,CAAC;UAAC;AAdpB,eAAA,WAAW,IAAI,WAAW,CAAC;AAiB/B,eAAK,QAAQ;AACb,eAAK,QAAQ;AAGb,cAAM,MAAM,OAAA,KAAK,KAAK,OAAO,MAAM,GAAG;AAGtC,eAAK,QAAQ,IAAI,OAAA,KAAK,MAAM,GAAG;AAG/B,eAAK,UAAU,IAAI,WAAW,KAAK,MAAM,YAAY;AACrD,eAAK,UAAU,KAAK,QAAQ;QAChC;AAGQ,QAAAA,MAAA,UAAA,cAAR,WAAA;AAEI,eAAK,SAAS,CAAC;AAEf,cAAM,MAAM,KAAK,SAAS,CAAC;AAG3B,cAAI,QAAQ,GAAG;AACX,kBAAM,IAAI,MAAM,0BAA0B;;AAI9C,eAAK,MAAM,MAAK;AAIhB,cAAI,MAAM,GAAG;AACT,iBAAK,MAAM,OAAO,KAAK,OAAO;;AAIlC,cAAI,KAAK,OAAO;AACZ,iBAAK,MAAM,OAAO,KAAK,KAAK;;AAIhC,eAAK,MAAM,OAAO,KAAK,QAAQ;AAG/B,eAAK,MAAM,OAAO,KAAK,OAAO;AAG9B,eAAK,UAAU;QACnB;AAQA,QAAAA,MAAA,UAAA,SAAA,SAAOC,SAAc;AACjB,cAAM,MAAM,IAAI,WAAWA,OAAM;AACjC,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,gBAAI,KAAK,YAAY,KAAK,QAAQ,QAAQ;AACtC,mBAAK,YAAW;;AAEpB,gBAAI,CAAC,IAAI,KAAK,QAAQ,KAAK,SAAS;;AAExC,iBAAO;QACX;AAEA,QAAAD,MAAA,UAAA,QAAA,WAAA;AACI,eAAK,MAAM,MAAK;AAChB,iBAAA,KAAK,KAAK,OAAO;AACjB,iBAAA,KAAK,KAAK,QAAQ;AAClB,eAAK,UAAU;QACnB;AACJ,eAAAA;MAAA,EA9FA;;AAAa,YAAA,OAAA;;;;;;;;;;ACdb,QAAM,QAAQ;AAEd,QAAa,sBAAb,MAAgC;MAM5B,cAAA;AALA,aAAA,cAAc;AACd,aAAA,iBAAiB;AAKb,cAAM,gBAAgB,OAAO,SAAS,cAC/B,KAAK,UAAW,KAA4B,WAC7C;AAEN,YAAI,iBAAiB,cAAc,oBAAoB,QAAW;AAC9D,eAAK,UAAU;AACf,eAAK,cAAc;AACnB,eAAK,iBAAiB;;MAE9B;MAEA,YAAYE,SAAc;AACtB,YAAI,CAAC,KAAK,eAAe,CAAC,KAAK,SAAS;AACpC,gBAAM,IAAI,MAAM,iDAAiD;;AAErE,cAAM,MAAM,IAAI,WAAWA,OAAM;AACjC,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,OAAO;AACxC,eAAK,QAAQ,gBAAgB,IAAI,SAAS,GAAG,IAAI,KAAK,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,CAAC;;AAErF,eAAO;MACX;;AA3BJ,YAAA,sBAAA;;;;;ACPA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAIC,IAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0FAA0F,GAAG,oIAAoI;AAAA,QAChP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;;;;;;ACPF,QAAA,SAAA;AAIA,QAAa,mBAAb,MAA6B;MAMzB,cAAA;AALA,aAAA,cAAc;AACd,aAAA,iBAAiB;AAKb,YAAI,OAAO,cAAY,aAAa;AAChC,gBAAM,aAAa;AACnB,cAAI,cAAc,WAAW,aAAa;AACtC,iBAAK,UAAU;AACf,iBAAK,cAAc;AACnB,iBAAK,iBAAiB;;;MAGlC;MAEA,YAAYC,SAAc;AACtB,YAAI,CAAC,KAAK,eAAe,CAAC,KAAK,SAAS;AACpC,gBAAM,IAAI,MAAM,iDAAiD;;AAIrE,YAAI,SAAS,KAAK,QAAQ,YAAYA,OAAM;AAG5C,YAAI,OAAO,WAAWA,SAAQ;AAC1B,gBAAM,IAAI,MAAM,kDAAkD;;AAItE,cAAM,MAAM,IAAI,WAAWA,OAAM;AAGjC,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,cAAI,CAAC,IAAI,OAAO,CAAC;;AAIrB,SAAA,GAAA,OAAA,MAAK,MAAM;AAEX,eAAO;MACX;;AA1CJ,YAAA,mBAAA;;;;;;;;;;ACJA,QAAA,YAAA;AACA,QAAA,SAAA;AAEA,QAAa,qBAAb,MAA+B;MAK3B,cAAA;AAJA,aAAA,cAAc;AACd,aAAA,OAAO;AAKH,aAAK,UAAU,IAAI,UAAA,oBAAmB;AACtC,YAAI,KAAK,QAAQ,aAAa;AAC1B,eAAK,cAAc;AACnB,eAAK,OAAO;AACZ;;AAIJ,aAAK,UAAU,IAAI,OAAA,iBAAgB;AACnC,YAAI,KAAK,QAAQ,aAAa;AAC1B,eAAK,cAAc;AACnB,eAAK,OAAO;AACZ;;MAIR;MAEA,YAAYC,SAAc;AACtB,YAAI,CAAC,KAAK,aAAa;AACnB,gBAAM,IAAI,MAAM,gDAAgD;;AAEpE,eAAO,KAAK,QAAQ,YAAYA,OAAM;MAC1C;;AA9BJ,YAAA,qBAAA;;;;;;;;;;ACEA,QAAA,WAAA;AACA,QAAA,WAAA;AACA,QAAA,SAAA;AAIa,YAAA,sBAAsB,IAAI,SAAA,mBAAkB;AAEzD,aAAgB,YAAYC,SAAgB,OAAqB,QAAA,qBAAmB;AAChF,aAAO,KAAK,YAAYA,OAAM;IAClC;AAFA,YAAA,cAAA;AAOA,aAAgB,aAAa,OAAqB,QAAA,qBAAmB;AAEjE,YAAM,MAAM,YAAY,GAAG,IAAI;AAK/B,YAAM,UAAS,GAAA,SAAA,cAAa,GAAG;AAG/B,OAAA,GAAA,OAAA,MAAK,GAAG;AAER,aAAO;IACX;AAbA,YAAA,eAAA;AAgBA,QAAM,eAAe;AAWrB,aAAgB,aACZA,SACA,UAAU,cACV,OAAqB,QAAA,qBAAmB;AAExC,UAAI,QAAQ,SAAS,GAAG;AACpB,cAAM,IAAI,MAAM,mCAAmC;;AAEvD,UAAI,QAAQ,SAAS,KAAK;AACtB,cAAM,IAAI,MAAM,kCAAkC;;AAEtD,UAAI,MAAM;AACV,YAAM,WAAW,QAAQ;AACzB,YAAM,UAAU,MAAO,MAAM;AAC7B,aAAOA,UAAS,GAAG;AACf,cAAM,MAAM,YAAY,KAAK,KAAKA,UAAS,MAAM,OAAO,GAAG,IAAI;AAC/D,iBAAS,IAAI,GAAG,IAAI,IAAI,UAAUA,UAAS,GAAG,KAAK;AAC/C,gBAAM,aAAa,IAAI,CAAC;AACxB,cAAI,aAAa,SAAS;AACtB,mBAAO,QAAQ,OAAO,aAAa,QAAQ;AAC3C,YAAAA;;;AAGR,SAAA,GAAA,OAAA,MAAK,GAAG;;AAEZ,aAAO;IACX;AA1BA,YAAA,eAAA;AAwCA,aAAgB,uBACZ,MACA,UAAU,cACV,OAAqB,QAAA,qBAAmB;AAExC,YAAMA,UAAS,KAAK,KAAK,QAAQ,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK,IAAI;AACrE,aAAO,aAAaA,SAAQ,SAAS,IAAI;IAC7C;AAPA,YAAA,yBAAA;;;;;;;;;ACnFA,QAAA,WAAA;AACA,QAAA,SAAA;AAEa,YAAA,gBAAgB;AAChB,YAAA,aAAa;AAK1B,QAAA;;MAAA,WAAA;AAeI,iBAAAC,UAAA;AAbS,eAAA,eAAuB,QAAA;AAGvB,eAAA,YAAoB,QAAA;AAGnB,eAAA,SAAS,IAAI,WAAW,CAAC;AAC3B,eAAA,QAAQ,IAAI,WAAW,EAAE;AACzB,eAAA,UAAU,IAAI,WAAW,GAAG;AAC5B,eAAA,gBAAgB;AAChB,eAAA,eAAe;AACf,eAAA,YAAY;AAGhB,eAAK,MAAK;QACd;AAEU,QAAAA,QAAA,UAAA,aAAV,WAAA;AACI,eAAK,OAAO,CAAC,IAAI;AACjB,eAAK,OAAO,CAAC,IAAI;AACjB,eAAK,OAAO,CAAC,IAAI;AACjB,eAAK,OAAO,CAAC,IAAI;AACjB,eAAK,OAAO,CAAC,IAAI;AACjB,eAAK,OAAO,CAAC,IAAI;AACjB,eAAK,OAAO,CAAC,IAAI;AACjB,eAAK,OAAO,CAAC,IAAI;QACrB;AAMA,QAAAA,QAAA,UAAA,QAAA,WAAA;AACI,eAAK,WAAU;AACf,eAAK,gBAAgB;AACrB,eAAK,eAAe;AACpB,eAAK,YAAY;AACjB,iBAAO;QACX;AAKA,QAAAA,QAAA,UAAA,QAAA,WAAA;AACI,iBAAA,KAAK,KAAK,OAAO;AACjB,iBAAA,KAAK,KAAK,KAAK;AACf,eAAK,MAAK;QACd;AAQA,QAAAA,QAAA,UAAA,SAAA,SAAO,MAAkB,YAAgC;AAAhC,cAAA,eAAA,QAAA;AAAA,yBAAqB,KAAK;UAAM;AACrD,cAAI,KAAK,WAAW;AAChB,kBAAM,IAAI,MAAM,iDAAiD;;AAErE,cAAI,UAAU;AACd,eAAK,gBAAgB;AACrB,cAAI,KAAK,gBAAgB,GAAG;AACxB,mBAAO,KAAK,gBAAgB,KAAK,aAAa,aAAa,GAAG;AAC1D,mBAAK,QAAQ,KAAK,eAAe,IAAI,KAAK,SAAS;AACnD;;AAEJ,gBAAI,KAAK,kBAAkB,KAAK,WAAW;AACvC,yBAAW,KAAK,OAAO,KAAK,QAAQ,KAAK,SAAS,GAAG,KAAK,SAAS;AACnE,mBAAK,gBAAgB;;;AAG7B,cAAI,cAAc,KAAK,WAAW;AAC9B,sBAAU,WAAW,KAAK,OAAO,KAAK,QAAQ,MAAM,SAAS,UAAU;AACvE,0BAAc,KAAK;;AAEvB,iBAAO,aAAa,GAAG;AACnB,iBAAK,QAAQ,KAAK,eAAe,IAAI,KAAK,SAAS;AACnD;;AAEJ,iBAAO;QACX;AAMA,QAAAA,QAAA,UAAA,SAAA,SAAO,KAAe;AAClB,cAAI,CAAC,KAAK,WAAW;AACjB,gBAAM,cAAc,KAAK;AACzB,gBAAM,OAAO,KAAK;AAClB,gBAAM,WAAY,cAAc,YAAc;AAC9C,gBAAM,WAAW,eAAe;AAChC,gBAAM,YAAa,cAAc,KAAK,KAAM,KAAK;AAEjD,iBAAK,QAAQ,IAAI,IAAI;AACrB,qBAAS,IAAI,OAAO,GAAG,IAAI,YAAY,GAAG,KAAK;AAC3C,mBAAK,QAAQ,CAAC,IAAI;;AAEtB,qBAAA,cAAc,UAAU,KAAK,SAAS,YAAY,CAAC;AACnD,qBAAA,cAAc,UAAU,KAAK,SAAS,YAAY,CAAC;AAEnD,uBAAW,KAAK,OAAO,KAAK,QAAQ,KAAK,SAAS,GAAG,SAAS;AAE9D,iBAAK,YAAY;;AAGrB,mBAAS,IAAI,GAAG,IAAI,KAAK,eAAe,GAAG,KAAK;AAC5C,qBAAA,cAAc,KAAK,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC;;AAG5C,iBAAO;QACX;AAKA,QAAAA,QAAA,UAAA,SAAA,WAAA;AACI,cAAM,MAAM,IAAI,WAAW,KAAK,YAAY;AAC5C,eAAK,OAAO,GAAG;AACf,iBAAO;QACX;AAQA,QAAAA,QAAA,UAAA,YAAA,WAAA;AACI,cAAI,KAAK,WAAW;AAChB,kBAAM,IAAI,MAAM,oCAAoC;;AAExD,iBAAO;YACH,OAAO,IAAI,WAAW,KAAK,MAAM;YACjC,QAAQ,KAAK,gBAAgB,IAAI,IAAI,WAAW,KAAK,OAAO,IAAI;YAChE,cAAc,KAAK;YACnB,aAAa,KAAK;;QAE1B;AAOA,QAAAA,QAAA,UAAA,eAAA,SAAa,YAAsB;AAC/B,eAAK,OAAO,IAAI,WAAW,KAAK;AAChC,eAAK,gBAAgB,WAAW;AAChC,cAAI,WAAW,QAAQ;AACnB,iBAAK,QAAQ,IAAI,WAAW,MAAM;;AAEtC,eAAK,eAAe,WAAW;AAC/B,eAAK,YAAY;AACjB,iBAAO;QACX;AAKA,QAAAA,QAAA,UAAA,kBAAA,SAAgB,YAAsB;AAClC,iBAAA,KAAK,WAAW,KAAK;AACrB,cAAI,WAAW,QAAQ;AACnB,mBAAA,KAAK,WAAW,MAAM;;AAE1B,qBAAW,eAAe;AAC1B,qBAAW,cAAc;QAC7B;AACJ,eAAAA;MAAA,EAzKA;;AAAa,YAAA,SAAA;AAmLb,QAAMC,KAAI,IAAI,WAAW;MACrB;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;MAAY;MAChD;MAAY;MAAY;MAAY;KACvC;AAED,aAAS,WAAWC,IAAe,GAAeC,IAAe,KAAa,KAAW;AACrF,aAAO,OAAO,IAAI;AACd,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAI,IAAI,EAAE,CAAC;AACX,YAAIC,KAAI,EAAE,CAAC;AAEX,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,cAAI,IAAI,MAAM,IAAI;AAClB,UAAAF,GAAE,CAAC,IAAI,SAAA,aAAaC,IAAG,CAAC;;AAG5B,iBAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAC1B,cAAI,IAAID,GAAE,IAAI,CAAC;AACf,cAAI,MAAM,MAAM,KAAK,KAAM,KAAK,OAAQ,MAAM,KAAK,KAAM,KAAK,MAAQ,MAAM;AAE5E,cAAIA,GAAE,IAAI,EAAE;AACZ,cAAI,MAAM,MAAM,IAAI,KAAM,KAAK,MAAO,MAAM,KAAK,KAAM,KAAK,MAAQ,MAAM;AAE1E,UAAAA,GAAE,CAAC,KAAK,KAAKA,GAAE,IAAI,CAAC,IAAI,MAAM,KAAKA,GAAE,IAAI,EAAE,IAAI;;AAGnD,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,cAAI,QAAU,MAAM,IAAI,KAAM,KAAK,MAAO,MAAM,KAAK,KAAM,KAAK,OAC3D,MAAM,KAAK,KAAM,KAAK,QAAU,IAAI,IAAM,CAAC,IAAI,KAAO,MACrDE,MAAMH,GAAE,CAAC,IAAIC,GAAE,CAAC,IAAK,KAAM,KAAM;AAEvC,cAAI,OAAQ,MAAM,IAAI,KAAM,KAAK,MAAO,MAAM,KAAK,KAAM,KAAK,OACzD,MAAM,KAAK,KAAM,KAAK,QAAU,IAAI,IAAM,IAAI,IAAM,IAAI,KAAO;AAEpE,UAAAE,KAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAK,IAAI,KAAM;AACf,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAK,KAAK,KAAM;;AAGpB,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAKA;AAER,eAAO;AACP,eAAO;;AAEX,aAAO;IACX;AAEA,aAAgB,KAAK,MAAgB;AACjC,UAAMA,KAAI,IAAI,OAAM;AACpB,MAAAA,GAAE,OAAO,IAAI;AACb,UAAMC,UAASD,GAAE,OAAM;AACvB,MAAAA,GAAE,MAAK;AACP,aAAOC;IACX;AANA,YAAA,OAAA;;;;;;;;;;ACxQA,QAAA,WAAA;AACA,QAAA,SAAA;AAEa,YAAA,oBAAoB;AACpB,YAAA,oBAAoB;AACpB,YAAA,oBAAoB;AAwBjC,aAAS,GAAG,MAAe;AACvB,YAAM,IAAI,IAAI,aAAa,EAAE;AAC7B,UAAI,MAAM;AACN,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,YAAE,CAAC,IAAI,KAAK,CAAC;;;AAGrB,aAAO;IACX;AAGA,QAAM,KAAK,IAAI,WAAW,EAAE;AAAG,OAAG,CAAC,IAAI;AAEvC,QAAM,UAAU,GAAG,CAAC,OAAQ,CAAC,CAAC;AAE9B,aAAS,SAAS,GAAK;AACnB,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,YAAI,IAAI,EAAE,CAAC,IAAI,IAAI;AACnB,YAAI,KAAK,MAAM,IAAI,KAAK;AACxB,UAAE,CAAC,IAAI,IAAI,IAAI;;AAEnB,QAAE,CAAC,KAAK,IAAI,IAAI,MAAM,IAAI;IAC9B;AAEA,aAAS,SAASC,IAAO,GAAO,GAAS;AACrC,YAAM,IAAI,EAAE,IAAI;AAChB,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,cAAM,IAAI,KAAKA,GAAE,CAAC,IAAI,EAAE,CAAC;AACzB,QAAAA,GAAE,CAAC,KAAK;AACR,UAAE,CAAC,KAAK;;IAEhB;AAEA,aAAS,UAAU,GAAe,GAAK;AACnC,YAAMC,KAAI,GAAE;AACZ,YAAM,IAAI,GAAE;AACZ,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,CAAC,IAAI,EAAE,CAAC;;AAEd,eAAS,CAAC;AACV,eAAS,CAAC;AACV,eAAS,CAAC;AACV,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,QAAAA,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAAA,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI,SAAWA,GAAE,IAAI,CAAC,KAAK,KAAM;AAC3C,UAAAA,GAAE,IAAI,CAAC,KAAK;;AAEhB,QAAAA,GAAE,EAAE,IAAI,EAAE,EAAE,IAAI,SAAWA,GAAE,EAAE,KAAK,KAAM;AAC1C,cAAM,IAAKA,GAAE,EAAE,KAAK,KAAM;AAC1B,QAAAA,GAAE,EAAE,KAAK;AACT,iBAAS,GAAGA,IAAG,IAAI,CAAC;;AAExB,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI;AAClB,UAAE,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK;;IAE/B;AAEA,aAAS,YAAY,GAAO,GAAa;AACrC,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK;;AAEvC,QAAE,EAAE,KAAK;IACb;AAEA,aAAS,IAAI,GAAO,GAAO,GAAK;AAC5B,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;;IAEzB;AAEA,aAAS,IAAI,GAAO,GAAO,GAAK;AAC5B,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;;IAEzB;AAEA,aAAS,IAAI,GAAO,GAAO,GAAK;AAC5B,UAAI,GAAW,GACX,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAC7D,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GACnE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GACrE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAC5D,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE,GACV,MAAM,EAAE,EAAE;AAEd,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,CAAC;AACP,YAAM,IAAI;AACV,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,EAAE;AACR,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,EAAE;AACR,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,EAAE;AACR,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,EAAE;AACR,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,EAAE;AACR,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,UAAI,EAAE,EAAE;AACR,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AACX,aAAO,IAAI;AAEX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,YAAM,KAAK;AACX,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AAIZ,UAAI;AACJ,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,YAAM,IAAI,IAAI,MAAM,IAAI;AAGxB,UAAI;AACJ,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,KAAK,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,WAAK,IAAI,IAAI;AAC5D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,UAAI,MAAM,IAAI;AAAO,UAAI,KAAK,MAAM,IAAI,KAAK;AAAG,YAAM,IAAI,IAAI;AAC9D,YAAM,IAAI,IAAI,MAAM,IAAI;AAExB,QAAE,CAAC,IAAI;AACP,QAAE,CAAC,IAAI;AACP,QAAE,CAAC,IAAI;AACP,QAAE,CAAC,IAAI;AACP,QAAE,CAAC,IAAI;AACP,QAAE,CAAC,IAAI;AACP,QAAE,CAAC,IAAI;AACP,QAAE,CAAC,IAAI;AACP,QAAE,CAAC,IAAI;AACP,QAAE,CAAC,IAAI;AACP,QAAE,EAAE,IAAI;AACR,QAAE,EAAE,IAAI;AACR,QAAE,EAAE,IAAI;AACR,QAAE,EAAE,IAAI;AACR,QAAE,EAAE,IAAI;AACR,QAAE,EAAE,IAAI;IACZ;AAEA,aAAS,OAAO,GAAO,GAAK;AACxB,UAAI,GAAG,GAAG,CAAC;IACf;AAEA,aAAS,SAAS,GAAO,KAAO;AAC5B,YAAM,IAAI,GAAE;AACZ,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,CAAC,IAAI,IAAI,CAAC;;AAEhB,eAAS,IAAI,KAAK,KAAK,GAAG,KAAK;AAC3B,eAAO,GAAG,CAAC;AACX,YAAI,MAAM,KAAK,MAAM,GAAG;AACpB,cAAI,GAAG,GAAG,GAAG;;;AAGrB,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,CAAC,IAAI,EAAE,CAAC;;IAElB;AAEA,aAAgB,WAAW,GAAeD,IAAa;AACnD,YAAM,IAAI,IAAI,WAAW,EAAE;AAC3B,YAAM,IAAI,IAAI,aAAa,EAAE;AAC7B,YAAM,IAAI,GAAE,GAAI,IAAI,GAAE,GAAI,IAAI,GAAE,GAC5B,IAAI,GAAE,GAAI,IAAI,GAAE,GAAI,IAAI,GAAE;AAE9B,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,CAAC,IAAI,EAAE,CAAC;;AAEd,QAAE,EAAE,IAAK,EAAE,EAAE,IAAI,MAAO;AACxB,QAAE,CAAC,KAAK;AAER,kBAAY,GAAGA,EAAC;AAEhB,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,CAAC,IAAI,EAAE,CAAC;;AAGd,QAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAEd,eAAS,IAAI,KAAK,KAAK,GAAG,EAAE,GAAG;AAC3B,cAAM,IAAK,EAAE,MAAM,CAAC,OAAO,IAAI,KAAM;AACrC,iBAAS,GAAG,GAAG,CAAC;AAChB,iBAAS,GAAG,GAAG,CAAC;AAChB,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,eAAO,GAAG,CAAC;AACX,eAAO,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,eAAO,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,OAAO;AACjB,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,YAAI,GAAG,GAAG,CAAC;AACX,eAAO,GAAG,CAAC;AACX,iBAAS,GAAG,GAAG,CAAC;AAChB,iBAAS,GAAG,GAAG,CAAC;;AAEpB,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACf,UAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACf,UAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACf,UAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;AAEnB,YAAM,MAAM,EAAE,SAAS,EAAE;AACzB,YAAM,MAAM,EAAE,SAAS,EAAE;AACzB,eAAS,KAAK,GAAG;AACjB,UAAI,KAAK,KAAK,GAAG;AACjB,YAAM,IAAI,IAAI,WAAW,EAAE;AAC3B,gBAAU,GAAG,GAAG;AAChB,aAAO;IACX;AA1DA,YAAA,aAAA;AA4DA,aAAgB,eAAe,GAAa;AACxC,aAAO,WAAW,GAAG,EAAE;IAC3B;AAFA,YAAA,iBAAA;AASA,aAAgB,wBAAwB,MAAgB;AACpD,UAAI,KAAK,WAAW,QAAA,mBAAmB;AACnC,cAAM,IAAI,MAAM,wBAAwB,QAAA,iBAAiB,QAAQ;;AAErE,YAAM,YAAY,IAAI,WAAW,IAAI;AACrC,YAAM,YAAY,eAAe,SAAS;AAC1C,aAAO;QACH;QACA;;IAER;AAVA,YAAA,0BAAA;AAYA,aAAgBE,iBAAgB,MAAmB;AAC/C,YAAM,QAAO,GAAA,SAAA,aAAY,IAAI,IAAI;AACjC,YAAM,SAAS,wBAAwB,IAAI;AAC3C,OAAA,GAAA,OAAA,MAAK,IAAI;AACT,aAAO;IACX;AALA,YAAA,kBAAAA;AA4BA,aAAgBC,WAAU,aAAyB,gBAA4B,aAAa,OAAK;AAC7F,UAAI,YAAY,WAAW,QAAA,mBAAmB;AAC1C,cAAM,IAAI,MAAM,qCAAqC;;AAEzD,UAAI,eAAe,WAAW,QAAA,mBAAmB;AAC7C,cAAM,IAAI,MAAM,qCAAqC;;AAGzD,YAAM,SAAS,WAAW,aAAa,cAAc;AAErD,UAAI,YAAY;AACZ,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,mBAAS,OAAO,CAAC;;AAErB,YAAI,UAAU,GAAG;AACb,gBAAM,IAAI,MAAM,4BAA4B;;;AAIpD,aAAO;IACX;AArBA,YAAA,YAAAA;;;;;ACvmBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBO,SAAS,UAAU,GAAG,GAAG;AAC5B,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAaO,SAAS,OAAO,GAAG,GAAG;AACzB,MAAI,IAAI,CAAC;AACT,WAASC,MAAK;AAAG,QAAI,OAAO,UAAU,eAAe,KAAK,GAAGA,EAAC,KAAK,EAAE,QAAQA,EAAC,IAAI;AAC9E,QAAEA,EAAC,IAAI,EAAEA,EAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAGA,KAAI,OAAO,sBAAsB,CAAC,GAAG,IAAIA,GAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQA,GAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAGA,GAAE,CAAC,CAAC;AACzE,UAAEA,GAAE,CAAC,CAAC,IAAI,EAAEA,GAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAEO,SAAS,WAAW,YAAY,QAAQ,KAAK,MAAM;AACtD,MAAI,IAAI,UAAU,QAAQ,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAM;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,QAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA;AACxH,aAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG;AAAK,UAAI,IAAI,WAAW,CAAC;AAAG,aAAK,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM;AAChJ,SAAO,IAAI,KAAK,KAAK,OAAO,eAAe,QAAQ,KAAK,CAAC,GAAG;AAChE;AAEO,SAAS,QAAQ,YAAY,WAAW;AAC3C,SAAO,SAAU,QAAQ,KAAK;AAAE,cAAU,QAAQ,KAAK,UAAU;AAAA,EAAG;AACxE;AAEO,SAAS,WAAW,aAAa,eAAe;AACnD,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,WAAO,QAAQ,SAAS,aAAa,aAAa;AACjI;AAEO,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AACzD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEO,SAAS,YAAY,SAAS,MAAM;AACvC,MAAIC,KAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAOA;AAAG,UAAI;AACV,YAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,YAAAA,GAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,YAAAA,GAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAKA,GAAE,IAAI,IAAI;AAAG,YAAAA,GAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAIA,GAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,cAAAA,KAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAAA,GAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAKA,GAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAAA,GAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAKA,GAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAAA,GAAE,QAAQ,EAAE,CAAC;AAAG,cAAAA,GAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,cAAAA,GAAE,IAAI,IAAI;AACpB,YAAAA,GAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAASA,EAAC;AAAA,MAC7B,SAAS,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,YAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAEO,SAAS,gBAAgB,GAAGC,IAAGC,IAAGC,KAAI;AACzC,MAAIA,QAAO;AAAW,IAAAA,MAAKD;AAC3B,IAAEC,GAAE,IAAIF,GAAEC,EAAC;AACf;AAEO,SAAS,aAAaD,IAAG,SAAS;AACrC,WAASF,MAAKE;AAAG,QAAIF,OAAM,aAAa,CAAC,QAAQ,eAAeA,EAAC;AAAG,cAAQA,EAAC,IAAIE,GAAEF,EAAC;AACxF;AAEO,SAAS,SAAS,GAAG;AACxB,MAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAUE,KAAI,KAAK,EAAE,CAAC,GAAG,IAAI;AAC5E,MAAIA;AAAG,WAAOA,GAAE,KAAK,CAAC;AACtB,MAAI,KAAK,OAAO,EAAE,WAAW;AAAU,WAAO;AAAA,MAC1C,MAAM,WAAY;AACd,YAAI,KAAK,KAAK,EAAE;AAAQ,cAAI;AAC5B,eAAO,EAAE,OAAO,KAAK,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,MAC1C;AAAA,IACJ;AACA,QAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AACzF;AAEO,SAAS,OAAO,GAAG,GAAG;AACzB,MAAIA,KAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAACA;AAAG,WAAO;AACf,MAAI,IAAIA,GAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,MAAI;AACA,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG;AAAM,SAAG,KAAK,EAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,QAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAI,KAAK,CAAC,EAAE,SAASA,KAAI,EAAE,QAAQ;AAAI,QAAAA,GAAE,KAAK,CAAC;AAAA,IACnD,UACA;AAAU,UAAI;AAAG,cAAM,EAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACX;AAEO,SAAS,WAAW;AACvB,WAAS,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ;AAC3C,SAAK,GAAG,OAAO,OAAO,UAAU,CAAC,CAAC,CAAC;AACvC,SAAO;AACX;AAEO,SAAS,iBAAiB;AAC7B,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI;AAAK,SAAK,UAAU,CAAC,EAAE;AAC7E,WAAS,IAAI,MAAM,CAAC,GAAGC,KAAI,GAAG,IAAI,GAAG,IAAI,IAAI;AACzC,aAAS,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,KAAKA;AAC1D,QAAEA,EAAC,IAAI,EAAE,CAAC;AAClB,SAAO;AACX;AAEO,SAAS,QAAQ,GAAG;AACvB,SAAO,gBAAgB,WAAW,KAAK,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC;AACvE;AAEO,SAAS,iBAAiB,SAAS,YAAY,WAAW;AAC7D,MAAI,CAAC,OAAO;AAAe,UAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5D,SAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AACpH,WAAS,KAAK,GAAG;AAAE,QAAI,EAAE,CAAC;AAAG,QAAE,CAAC,IAAI,SAAU,GAAG;AAAE,eAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AAAE,YAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAA,EAAG;AACzI,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI;AAAE,WAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,IAAG,SAAS,GAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAK,GAAG;AAAE,MAAE,iBAAiB,UAAU,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE;AAAQ,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACrF;AAEO,SAAS,iBAAiB,GAAG;AAChC,MAAI,GAAGH;AACP,SAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,SAAS,SAAU,GAAG;AAAE,UAAM;AAAA,EAAG,CAAC,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AAC1I,WAAS,KAAK,GAAG,GAAG;AAAE,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,SAAU,GAAG;AAAE,cAAQA,KAAI,CAACA,MAAK,EAAE,OAAO,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,MAAM,SAAS,IAAI,IAAI,EAAE,CAAC,IAAI;AAAA,IAAG,IAAI;AAAA,EAAG;AAClJ;AAEO,SAAS,cAAc,GAAG;AAC7B,MAAI,CAAC,OAAO;AAAe,UAAM,IAAI,UAAU,sCAAsC;AACrF,MAAIE,KAAI,EAAE,OAAO,aAAa,GAAG;AACjC,SAAOA,KAAIA,GAAE,KAAK,CAAC,KAAK,IAAI,OAAO,aAAa,aAAa,SAAS,CAAC,IAAI,EAAE,OAAO,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AAC9M,WAAS,KAAK,GAAG;AAAE,MAAE,CAAC,IAAI,EAAE,CAAC,KAAK,SAAU,GAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,YAAI,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,SAAS,QAAQ,EAAE,MAAM,EAAE,KAAK;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AAC/J,WAAS,OAAO,SAAS,QAAQ,GAAG,GAAG;AAAE,YAAQ,QAAQ,CAAC,EAAE,KAAK,SAASG,IAAG;AAAE,cAAQ,EAAE,OAAOA,IAAG,MAAM,EAAE,CAAC;AAAA,IAAG,GAAG,MAAM;AAAA,EAAG;AAC/H;AAEO,SAAS,qBAAqB,QAAQ,KAAK;AAC9C,MAAI,OAAO,gBAAgB;AAAE,WAAO,eAAe,QAAQ,OAAO,EAAE,OAAO,IAAI,CAAC;AAAA,EAAG,OAAO;AAAE,WAAO,MAAM;AAAA,EAAK;AAC9G,SAAO;AACX;AAEO,SAAS,aAAa,KAAK;AAC9B,MAAI,OAAO,IAAI;AAAY,WAAO;AAClC,MAAI,SAAS,CAAC;AACd,MAAI,OAAO;AAAM,aAASF,MAAK;AAAK,UAAI,OAAO,eAAe,KAAK,KAAKA,EAAC;AAAG,eAAOA,EAAC,IAAI,IAAIA,EAAC;AAAA;AAC7F,SAAO,UAAU;AACjB,SAAO;AACX;AAEO,SAAS,gBAAgB,KAAK;AACjC,SAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,SAAS,IAAI;AAC1D;AAEO,SAAS,uBAAuB,UAAU,YAAY;AACzD,MAAI,CAAC,WAAW,IAAI,QAAQ,GAAG;AAC3B,UAAM,IAAI,UAAU,gDAAgD;AAAA,EACxE;AACA,SAAO,WAAW,IAAI,QAAQ;AAClC;AAEO,SAAS,uBAAuB,UAAU,YAAY,OAAO;AAChE,MAAI,CAAC,WAAW,IAAI,QAAQ,GAAG;AAC3B,UAAM,IAAI,UAAU,gDAAgD;AAAA,EACxE;AACA,aAAW,IAAI,UAAU,KAAK;AAC9B,SAAO;AACX;AAzNA,IAgBI,eAaO;AA7BX;AAAA;AAgBA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,sBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUG,IAAGC,IAAG;AAAE,QAAAD,GAAE,YAAYC;AAAA,MAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,iBAASP,MAAKO;AAAG,cAAIA,GAAE,eAAeP,EAAC;AAAG,YAAAM,GAAEN,EAAC,IAAIO,GAAEP,EAAC;AAAA,MAAG;AAC7E,aAAO,cAAc,GAAG,CAAC;AAAA,IAC7B;AAQO,IAAI,WAAW,WAAW;AAC7B,iBAAW,OAAO,UAAU,SAASQ,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAASR,MAAK;AAAG,gBAAI,OAAO,UAAU,eAAe,KAAK,GAAGA,EAAC;AAAG,gBAAEA,EAAC,IAAI,EAAEA,EAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAAA;AAAA;;;;;;;;ACtCA,aAAgB,MAAM,SAAe;AACnC,aAAO,IAAI,QAAQ,aAAU;AAC3B,mBAAW,MAAK;AACd,kBAAQ,IAAI;QACd,GAAG,OAAO;MACZ,CAAC;IACH;AANA,YAAA,QAAA;;;;;;;;;;ACEa,YAAA,cAAc;AAEd,YAAA,eAAe;;;;;;;;;;ACFf,YAAA,aAAa;AAEb,YAAA,eAAe;AAEf,YAAA,cAAc;AAEd,YAAA,iBAAiB;AAEjB,YAAA,gBAAgB;AAIhB,YAAA,aAAa,QAAA;AAEb,YAAA,eAAe,QAAA,aAAa;AAE5B,YAAA,cAAc,QAAA,aAAa;AAE3B,YAAA,iBAAiB,QAAA,aAAa;AAE9B,YAAA,gBAAgB,QAAA,aAAa;AAI7B,YAAA,WAAW,QAAA;AAEX,YAAA,cAAc,QAAA,WAAW;AAEzB,YAAA,YAAY,QAAA,WAAW;AAEvB,YAAA,eAAe,QAAA,WAAW;AAE1B,YAAA,oBAAoB,QAAA,WAAW;AAI/B,YAAA,UAAU,QAAA;AAEV,YAAA,aAAa,QAAA,UAAU;AAEvB,YAAA,YAAY,QAAA,UAAU;AAEtB,YAAA,aAAa,QAAA,UAAU;AAEvB,YAAA,cAAc,QAAA,UAAU;AAIxB,YAAA,WAAW,QAAA;AAEX,YAAA,YAAY,QAAA,WAAW;AAEvB,YAAA,cAAc,QAAA,WAAW;AAEzB,YAAA,aAAa,QAAA,WAAW;AAIxB,YAAA,WAAW,QAAA,UAAU;;;;;;;;;;AC5DlC,YAAA,aAAA,gBAAA,OAAA;AACA,YAAA,aAAA,gBAAA,OAAA;;;;;;;;;;ACDA,QAAA,cAAA;AAEA,aAAgB,cAAc,SAAe;AAC3C,aAAO,UAAU,YAAA;IACnB;AAFA,YAAA,gBAAA;AAIA,aAAgB,gBAAgB,aAAmB;AACjD,aAAO,KAAK,MAAM,cAAc,YAAA,YAAY;IAC9C;AAFA,YAAA,kBAAA;;;;;;;;;;ACNA,YAAA,aAAA,iBAAA,OAAA;AACA,YAAA,aAAA,mBAAA,OAAA;;;;;;;;;;ACCA,QAAa,QAAb,MAAkB;MAAlB,cAAA;AACS,aAAA,aAAa,oBAAI,IAAG;MA+B7B;MA7BS,MAAM,OAAa;AACxB,YAAI,KAAK,WAAW,IAAI,KAAK,GAAG;AAC9B,gBAAM,IAAI,MAAM,oCAAoC,KAAK,EAAE;;AAE7D,aAAK,WAAW,IAAI,OAAO,EAAE,SAAS,KAAK,IAAG,EAAE,CAAE;MACpD;MAEO,KAAK,OAAa;AACvB,cAAM,YAAY,KAAK,IAAI,KAAK;AAChC,YAAI,OAAO,UAAU,YAAY,aAAa;AAC5C,gBAAM,IAAI,MAAM,oCAAoC,KAAK,EAAE;;AAE7D,cAAM,UAAU,KAAK,IAAG,IAAK,UAAU;AACvC,aAAK,WAAW,IAAI,OAAO,EAAE,SAAS,UAAU,SAAS,QAAO,CAAE;MACpE;MAEO,IAAI,OAAa;AACtB,cAAM,YAAY,KAAK,WAAW,IAAI,KAAK;AAC3C,YAAI,OAAO,cAAc,aAAa;AACpC,gBAAM,IAAI,MAAM,iCAAiC,KAAK,EAAE;;AAE1D,eAAO;MACT;MAEO,QAAQ,OAAa;AAC1B,cAAM,YAAY,KAAK,IAAI,KAAK;AAChC,cAAM,UAAU,UAAU,WAAW,KAAK,IAAG,IAAK,UAAU;AAC5D,eAAO;MACT;;AA/BF,YAAA,QAAA;AAkCA,YAAA,UAAe;;;;;;;;;;AC/Bf,QAAsB,SAAtB,MAA4B;;AAA5B,YAAA,SAAA;;;;;;;;;;ACLA,YAAA,aAAA,kBAAA,OAAA;;;;;;;;;;ACAA,YAAA,aAAA,iBAAA,OAAA;AACA,YAAA,aAAA,iBAAA,OAAA;AACA,YAAA,aAAA,iBAAA,OAAA;AACA,YAAA,aAAA,qBAAA,OAAA;;;;;ACHA;AAAA;AAAA;AACA,QAAM,kBAAkB;AACxB,QAAM,kBAAkB;AACxB,QAAM,eAAe;AACrB,QAAM,eAAe;AAErB,QAAM,oBAAoB,WAAS,UAAU,QAAQ,UAAU;AAE/D,QAAM,2BAA2B,OAAO,0BAA0B;AAElE,aAAS,sBAAsB,SAAS;AACvC,cAAQ,QAAQ,aAAa;AAAA,QAC5B,KAAK;AACJ,iBAAO,SAAO,CAAC,QAAQ,UAAU;AAChC,kBAAM,QAAQ,OAAO;AAErB,gBACC,UAAU,UACT,QAAQ,YAAY,UAAU,QAC9B,QAAQ,mBAAmB,UAAU,IACrC;AACD,qBAAO;AAAA,YACR;AAEA,gBAAI,UAAU,MAAM;AACnB,qBAAO,CAAC,GAAG,QAAQ,CAACS,QAAO,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,EAAE,KAAK,EAAE,CAAC;AAAA,YACpE;AAEA,mBAAO;AAAA,cACN,GAAG;AAAA,cACH,CAACA,QAAO,KAAK,OAAO,GAAG,KAAKA,QAAO,OAAO,OAAO,GAAG,MAAMA,QAAO,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE;AAAA,YAC1F;AAAA,UACD;AAAA,QAED,KAAK;AACJ,iBAAO,SAAO,CAAC,QAAQ,UAAU;AAChC,gBACC,UAAU,UACT,QAAQ,YAAY,UAAU,QAC9B,QAAQ,mBAAmB,UAAU,IACrC;AACD,qBAAO;AAAA,YACR;AAEA,gBAAI,UAAU,MAAM;AACnB,qBAAO,CAAC,GAAG,QAAQ,CAACA,QAAO,KAAK,OAAO,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC;AAAA,YACzD;AAEA,mBAAO,CAAC,GAAG,QAAQ,CAACA,QAAO,KAAK,OAAO,GAAG,OAAOA,QAAO,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,UAClF;AAAA,QAED,KAAK;AACJ,iBAAO,SAAO,CAAC,QAAQ,UAAU;AAChC,gBACC,UAAU,UACT,QAAQ,YAAY,UAAU,QAC9B,QAAQ,mBAAmB,UAAU,IACrC;AACD,qBAAO;AAAA,YACR;AAEA,gBAAI,UAAU,MAAM;AACnB,qBAAO,CAAC,GAAG,QAAQ,CAACA,QAAO,KAAK,OAAO,GAAG,QAAQ,EAAE,KAAK,EAAE,CAAC;AAAA,YAC7D;AAEA,mBAAO,CAAC,GAAG,QAAQ,CAACA,QAAO,KAAK,OAAO,GAAG,UAAUA,QAAO,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,UACrF;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,qBAAqB;AACzB,gBAAM,cAAc,QAAQ,gBAAgB,sBAC3C,QACA;AAED,iBAAO,SAAO,CAAC,QAAQ,UAAU;AAChC,gBACC,UAAU,UACT,QAAQ,YAAY,UAAU,QAC9B,QAAQ,mBAAmB,UAAU,IACrC;AACD,qBAAO;AAAA,YACR;AAGA,oBAAQ,UAAU,OAAO,KAAK;AAE9B,gBAAI,OAAO,WAAW,GAAG;AACxB,qBAAO,CAAC,CAACA,QAAO,KAAK,OAAO,GAAG,aAAaA,QAAO,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,YAC7E;AAEA,mBAAO,CAAC,CAAC,QAAQA,QAAO,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,oBAAoB,CAAC;AAAA,UAC5E;AAAA,QACD;AAAA,QAEA;AACC,iBAAO,SAAO,CAAC,QAAQ,UAAU;AAChC,gBACC,UAAU,UACT,QAAQ,YAAY,UAAU,QAC9B,QAAQ,mBAAmB,UAAU,IACrC;AACD,qBAAO;AAAA,YACR;AAEA,gBAAI,UAAU,MAAM;AACnB,qBAAO,CAAC,GAAG,QAAQA,QAAO,KAAK,OAAO,CAAC;AAAA,YACxC;AAEA,mBAAO,CAAC,GAAG,QAAQ,CAACA,QAAO,KAAK,OAAO,GAAG,KAAKA,QAAO,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,UAChF;AAAA,MACF;AAAA,IACD;AAEA,aAAS,qBAAqB,SAAS;AACtC,UAAI;AAEJ,cAAQ,QAAQ,aAAa;AAAA,QAC5B,KAAK;AACJ,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,qBAAS,aAAa,KAAK,GAAG;AAE9B,kBAAM,IAAI,QAAQ,YAAY,EAAE;AAEhC,gBAAI,CAAC,QAAQ;AACZ,0BAAY,GAAG,IAAI;AACnB;AAAA,YACD;AAEA,gBAAI,YAAY,GAAG,MAAM,QAAW;AACnC,0BAAY,GAAG,IAAI,CAAC;AAAA,YACrB;AAEA,wBAAY,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI;AAAA,UAC/B;AAAA,QAED,KAAK;AACJ,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,qBAAS,UAAU,KAAK,GAAG;AAC3B,kBAAM,IAAI,QAAQ,SAAS,EAAE;AAE7B,gBAAI,CAAC,QAAQ;AACZ,0BAAY,GAAG,IAAI;AACnB;AAAA,YACD;AAEA,gBAAI,YAAY,GAAG,MAAM,QAAW;AACnC,0BAAY,GAAG,IAAI,CAAC,KAAK;AACzB;AAAA,YACD;AAEA,wBAAY,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,GAAG,GAAG,KAAK;AAAA,UACrD;AAAA,QAED,KAAK;AACJ,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,qBAAS,WAAW,KAAK,GAAG;AAC5B,kBAAM,IAAI,QAAQ,UAAU,EAAE;AAE9B,gBAAI,CAAC,QAAQ;AACZ,0BAAY,GAAG,IAAI;AACnB;AAAA,YACD;AAEA,gBAAI,YAAY,GAAG,MAAM,QAAW;AACnC,0BAAY,GAAG,IAAI,CAAC,KAAK;AACzB;AAAA,YACD;AAEA,wBAAY,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,GAAG,GAAG,KAAK;AAAA,UACrD;AAAA,QAED,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,kBAAM,UAAU,OAAO,UAAU,YAAY,MAAM,SAAS,QAAQ,oBAAoB;AACxF,kBAAM,iBAAkB,OAAO,UAAU,YAAY,CAAC,WAAWC,QAAO,OAAO,OAAO,EAAE,SAAS,QAAQ,oBAAoB;AAC7H,oBAAQ,iBAAiBA,QAAO,OAAO,OAAO,IAAI;AAClD,kBAAM,WAAW,WAAW,iBAAiB,MAAM,MAAM,QAAQ,oBAAoB,EAAE,IAAI,UAAQA,QAAO,MAAM,OAAO,CAAC,IAAI,UAAU,OAAO,QAAQA,QAAO,OAAO,OAAO;AAC1K,wBAAY,GAAG,IAAI;AAAA,UACpB;AAAA,QAED,KAAK;AACJ,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,kBAAM,UAAU,UAAU,KAAK,GAAG;AAClC,kBAAM,IAAI,QAAQ,SAAS,EAAE;AAE7B,gBAAI,CAAC,SAAS;AACb,0BAAY,GAAG,IAAI,QAAQA,QAAO,OAAO,OAAO,IAAI;AACpD;AAAA,YACD;AAEA,kBAAM,aAAa,UAAU,OAC5B,CAAC,IACD,MAAM,MAAM,QAAQ,oBAAoB,EAAE,IAAI,UAAQA,QAAO,MAAM,OAAO,CAAC;AAE5E,gBAAI,YAAY,GAAG,MAAM,QAAW;AACnC,0BAAY,GAAG,IAAI;AACnB;AAAA,YACD;AAEA,wBAAY,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,GAAG,GAAG,UAAU;AAAA,UAC1D;AAAA,QAED;AACC,iBAAO,CAAC,KAAK,OAAO,gBAAgB;AACnC,gBAAI,YAAY,GAAG,MAAM,QAAW;AACnC,0BAAY,GAAG,IAAI;AACnB;AAAA,YACD;AAEA,wBAAY,GAAG,IAAI,CAAC,EAAE,OAAO,YAAY,GAAG,GAAG,KAAK;AAAA,UACrD;AAAA,MACF;AAAA,IACD;AAEA,aAAS,6BAA6B,OAAO;AAC5C,UAAI,OAAO,UAAU,YAAY,MAAM,WAAW,GAAG;AACpD,cAAM,IAAI,UAAU,sDAAsD;AAAA,MAC3E;AAAA,IACD;AAEA,aAASD,QAAO,OAAO,SAAS;AAC/B,UAAI,QAAQ,QAAQ;AACnB,eAAO,QAAQ,SAAS,gBAAgB,KAAK,IAAI,mBAAmB,KAAK;AAAA,MAC1E;AAEA,aAAO;AAAA,IACR;AAEA,aAASC,QAAO,OAAO,SAAS;AAC/B,UAAI,QAAQ,QAAQ;AACnB,eAAO,gBAAgB,KAAK;AAAA,MAC7B;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,WAAW,OAAO;AAC1B,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,eAAO,MAAM,KAAK;AAAA,MACnB;AAEA,UAAI,OAAO,UAAU,UAAU;AAC9B,eAAO,WAAW,OAAO,KAAK,KAAK,CAAC,EAClC,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,EACpC,IAAI,SAAO,MAAM,GAAG,CAAC;AAAA,MACxB;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,WAAW,OAAO;AAC1B,YAAM,YAAY,MAAM,QAAQ,GAAG;AACnC,UAAI,cAAc,IAAI;AACrB,gBAAQ,MAAM,MAAM,GAAG,SAAS;AAAA,MACjC;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,QAAQ,KAAK;AACrB,UAAI,OAAO;AACX,YAAM,YAAY,IAAI,QAAQ,GAAG;AACjC,UAAI,cAAc,IAAI;AACrB,eAAO,IAAI,MAAM,SAAS;AAAA,MAC3B;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,QAAQ,OAAO;AACvB,cAAQ,WAAW,KAAK;AACxB,YAAM,aAAa,MAAM,QAAQ,GAAG;AACpC,UAAI,eAAe,IAAI;AACtB,eAAO;AAAA,MACR;AAEA,aAAO,MAAM,MAAM,aAAa,CAAC;AAAA,IAClC;AAEA,aAAS,WAAW,OAAO,SAAS;AACnC,UAAI,QAAQ,gBAAgB,CAAC,OAAO,MAAM,OAAO,KAAK,CAAC,MAAM,OAAO,UAAU,YAAY,MAAM,KAAK,MAAM,KAAK;AAC/G,gBAAQ,OAAO,KAAK;AAAA,MACrB,WAAW,QAAQ,iBAAiB,UAAU,SAAS,MAAM,YAAY,MAAM,UAAU,MAAM,YAAY,MAAM,UAAU;AAC1H,gBAAQ,MAAM,YAAY,MAAM;AAAA,MACjC;AAEA,aAAO;AAAA,IACR;AAEA,aAASC,OAAM,OAAO,SAAS;AAC9B,gBAAU,OAAO,OAAO;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,aAAa;AAAA,QACb,sBAAsB;AAAA,QACtB,cAAc;AAAA,QACd,eAAe;AAAA,MAChB,GAAG,OAAO;AAEV,mCAA6B,QAAQ,oBAAoB;AAEzD,YAAM,YAAY,qBAAqB,OAAO;AAG9C,YAAM,MAAM,uBAAO,OAAO,IAAI;AAE9B,UAAI,OAAO,UAAU,UAAU;AAC9B,eAAO;AAAA,MACR;AAEA,cAAQ,MAAM,KAAK,EAAE,QAAQ,UAAU,EAAE;AAEzC,UAAI,CAAC,OAAO;AACX,eAAO;AAAA,MACR;AAEA,iBAAW,SAAS,MAAM,MAAM,GAAG,GAAG;AACrC,YAAI,UAAU,IAAI;AACjB;AAAA,QACD;AAEA,YAAI,CAAC,KAAK,KAAK,IAAI,aAAa,QAAQ,SAAS,MAAM,QAAQ,OAAO,GAAG,IAAI,OAAO,GAAG;AAIvF,gBAAQ,UAAU,SAAY,OAAO,CAAC,SAAS,aAAa,mBAAmB,EAAE,SAAS,QAAQ,WAAW,IAAI,QAAQD,QAAO,OAAO,OAAO;AAC9I,kBAAUA,QAAO,KAAK,OAAO,GAAG,OAAO,GAAG;AAAA,MAC3C;AAEA,iBAAW,OAAO,OAAO,KAAK,GAAG,GAAG;AACnC,cAAM,QAAQ,IAAI,GAAG;AACrB,YAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,qBAAWE,MAAK,OAAO,KAAK,KAAK,GAAG;AACnC,kBAAMA,EAAC,IAAI,WAAW,MAAMA,EAAC,GAAG,OAAO;AAAA,UACxC;AAAA,QACD,OAAO;AACN,cAAI,GAAG,IAAI,WAAW,OAAO,OAAO;AAAA,QACrC;AAAA,MACD;AAEA,UAAI,QAAQ,SAAS,OAAO;AAC3B,eAAO;AAAA,MACR;AAEA,cAAQ,QAAQ,SAAS,OAAO,OAAO,KAAK,GAAG,EAAE,KAAK,IAAI,OAAO,KAAK,GAAG,EAAE,KAAK,QAAQ,IAAI,GAAG,OAAO,CAAC,QAAQ,QAAQ;AACtH,cAAM,QAAQ,IAAI,GAAG;AACrB,YAAI,QAAQ,KAAK,KAAK,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AAEzE,iBAAO,GAAG,IAAI,WAAW,KAAK;AAAA,QAC/B,OAAO;AACN,iBAAO,GAAG,IAAI;AAAA,QACf;AAEA,eAAO;AAAA,MACR,GAAG,uBAAO,OAAO,IAAI,CAAC;AAAA,IACvB;AAEA,YAAQ,UAAU;AAClB,YAAQ,QAAQD;AAEhB,YAAQ,YAAY,CAAC,QAAQ,YAAY;AACxC,UAAI,CAAC,QAAQ;AACZ,eAAO;AAAA,MACR;AAEA,gBAAU,OAAO,OAAO;AAAA,QACvB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,sBAAsB;AAAA,MACvB,GAAG,OAAO;AAEV,mCAA6B,QAAQ,oBAAoB;AAEzD,YAAM,eAAe,SACnB,QAAQ,YAAY,kBAAkB,OAAO,GAAG,CAAC,KACjD,QAAQ,mBAAmB,OAAO,GAAG,MAAM;AAG7C,YAAM,YAAY,sBAAsB,OAAO;AAE/C,YAAM,aAAa,CAAC;AAEpB,iBAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACtC,YAAI,CAAC,aAAa,GAAG,GAAG;AACvB,qBAAW,GAAG,IAAI,OAAO,GAAG;AAAA,QAC7B;AAAA,MACD;AAEA,YAAM,OAAO,OAAO,KAAK,UAAU;AAEnC,UAAI,QAAQ,SAAS,OAAO;AAC3B,aAAK,KAAK,QAAQ,IAAI;AAAA,MACvB;AAEA,aAAO,KAAK,IAAI,SAAO;AACtB,cAAM,QAAQ,OAAO,GAAG;AAExB,YAAI,UAAU,QAAW;AACxB,iBAAO;AAAA,QACR;AAEA,YAAI,UAAU,MAAM;AACnB,iBAAOF,QAAO,KAAK,OAAO;AAAA,QAC3B;AAEA,YAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,cAAI,MAAM,WAAW,KAAK,QAAQ,gBAAgB,qBAAqB;AACtE,mBAAOA,QAAO,KAAK,OAAO,IAAI;AAAA,UAC/B;AAEA,iBAAO,MACL,OAAO,UAAU,GAAG,GAAG,CAAC,CAAC,EACzB,KAAK,GAAG;AAAA,QACX;AAEA,eAAOA,QAAO,KAAK,OAAO,IAAI,MAAMA,QAAO,OAAO,OAAO;AAAA,MAC1D,CAAC,EAAE,OAAO,OAAK,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG;AAAA,IACtC;AAEA,YAAQ,WAAW,CAAC,KAAK,YAAY;AACpC,gBAAU,OAAO,OAAO;AAAA,QACvB,QAAQ;AAAA,MACT,GAAG,OAAO;AAEV,YAAM,CAAC,MAAM,IAAI,IAAI,aAAa,KAAK,GAAG;AAE1C,aAAO,OAAO;AAAA,QACb;AAAA,UACC,KAAK,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK;AAAA,UAC3B,OAAOE,OAAM,QAAQ,GAAG,GAAG,OAAO;AAAA,QACnC;AAAA,QACA,WAAW,QAAQ,2BAA2B,OAAO,EAAC,oBAAoBD,QAAO,MAAM,OAAO,EAAC,IAAI,CAAC;AAAA,MACrG;AAAA,IACD;AAEA,YAAQ,eAAe,CAAC,QAAQ,YAAY;AAC3C,gBAAU,OAAO,OAAO;AAAA,QACvB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,CAAC,wBAAwB,GAAG;AAAA,MAC7B,GAAG,OAAO;AAEV,YAAM,MAAM,WAAW,OAAO,GAAG,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK;AACpD,YAAM,eAAe,QAAQ,QAAQ,OAAO,GAAG;AAC/C,YAAM,qBAAqB,QAAQ,MAAM,cAAc,EAAC,MAAM,MAAK,CAAC;AAEpE,YAAM,QAAQ,OAAO,OAAO,oBAAoB,OAAO,KAAK;AAC5D,UAAI,cAAc,QAAQ,UAAU,OAAO,OAAO;AAClD,UAAI,aAAa;AAChB,sBAAc,IAAI,WAAW;AAAA,MAC9B;AAEA,UAAI,OAAO,QAAQ,OAAO,GAAG;AAC7B,UAAI,OAAO,oBAAoB;AAC9B,eAAO,IAAI,QAAQ,wBAAwB,IAAID,QAAO,OAAO,oBAAoB,OAAO,IAAI,OAAO,kBAAkB;AAAA,MACtH;AAEA,aAAO,GAAG,GAAG,GAAG,WAAW,GAAG,IAAI;AAAA,IACnC;AAEA,YAAQ,OAAO,CAAC,OAAO,QAAQ,YAAY;AAC1C,gBAAU,OAAO,OAAO;AAAA,QACvB,yBAAyB;AAAA,QACzB,CAAC,wBAAwB,GAAG;AAAA,MAC7B,GAAG,OAAO;AAEV,YAAM,EAAC,KAAK,OAAO,mBAAkB,IAAI,QAAQ,SAAS,OAAO,OAAO;AACxE,aAAO,QAAQ,aAAa;AAAA,QAC3B;AAAA,QACA,OAAO,aAAa,OAAO,MAAM;AAAA,QACjC;AAAA,MACD,GAAG,OAAO;AAAA,IACX;AAEA,YAAQ,UAAU,CAAC,OAAO,QAAQ,YAAY;AAC7C,YAAM,kBAAkB,MAAM,QAAQ,MAAM,IAAI,SAAO,CAAC,OAAO,SAAS,GAAG,IAAI,CAAC,KAAK,UAAU,CAAC,OAAO,KAAK,KAAK;AAEjH,aAAO,QAAQ,KAAK,OAAO,iBAAiB,OAAO;AAAA,IACpD;AAAA;AAAA;;;;;;;;;;ACjeO,SAAS,aAAa,KAAK;AAChC,MAAI,WAAW,UAAU,MAAM;AAC7B,WAAO,IAAI,WAAW,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAAA,EAClE;AACA,SAAO;AACT;;;ACEO,SAAS,YAAY,OAAO,GAAG;AACpC,MAAI,WAAW,UAAU,QAAQ,WAAW,OAAO,eAAe,MAAM;AACtE,WAAO,aAAa,WAAW,OAAO,YAAY,IAAI,CAAC;AAAA,EACzD;AACA,SAAO,IAAI,WAAW,IAAI;AAC5B;;;ACVO,SAAS,OAAO,QAAQI,SAAQ;AACrC,MAAI,CAACA,SAAQ;AACX,IAAAA,UAAS,OAAO,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,QAAQ,CAAC;AAAA,EAC5D;AACA,QAAM,SAAS,YAAYA,OAAM;AACjC,MAAI,SAAS;AACb,aAAW,OAAO,QAAQ;AACxB,WAAO,IAAI,KAAK,MAAM;AACtB,cAAU,IAAI;AAAA,EAChB;AACA,SAAO,aAAa,MAAM;AAC5B;;;ACbA;AAAA;AAAA;AAAA;;;ACAA,SAAS,KAAK,UAAUC,OAAM;AAC5B,MAAI,SAAS,UAAU,KAAK;AAC1B,UAAM,IAAI,UAAU,mBAAmB;AAAA,EACzC;AACA,MAAI,WAAW,IAAI,WAAW,GAAG;AACjC,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,aAAS,CAAC,IAAI;AAAA,EAChB;AACA,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,IAAI,SAAS,OAAO,CAAC;AACzB,QAAI,KAAK,EAAE,WAAW,CAAC;AACvB,QAAI,SAAS,EAAE,MAAM,KAAK;AACxB,YAAM,IAAI,UAAU,IAAI,eAAe;AAAA,IACzC;AACA,aAAS,EAAE,IAAI;AAAA,EACjB;AACA,MAAI,OAAO,SAAS;AACpB,MAAI,SAAS,SAAS,OAAO,CAAC;AAC9B,MAAI,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC1C,MAAI,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC3C,WAASC,QAAO,QAAQ;AACtB,QAAI,kBAAkB;AAAW;AAAA,aACxB,YAAY,OAAO,MAAM,GAAG;AACnC,eAAS,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,IAC7E,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,eAAS,WAAW,KAAK,MAAM;AAAA,IACjC;AACA,QAAI,EAAE,kBAAkB,aAAa;AACnC,YAAM,IAAI,UAAU,qBAAqB;AAAA,IAC3C;AACA,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAI,SAAS;AACb,QAAIC,UAAS;AACb,QAAI,SAAS;AACb,QAAI,OAAO,OAAO;AAClB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,IACF;AACA,QAAI,QAAQ,OAAO,UAAU,UAAU,MAAM;AAC7C,QAAI,MAAM,IAAI,WAAW,IAAI;AAC7B,WAAO,WAAW,MAAM;AACtB,UAAI,QAAQ,OAAO,MAAM;AACzB,UAAIC,KAAI;AACR,eAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAID,YAAW,QAAQ,IAAI,OAAOC,MAAK;AAC9E,iBAAS,MAAM,IAAI,GAAG,MAAM;AAC5B,YAAI,GAAG,IAAI,QAAQ,SAAS;AAC5B,gBAAQ,QAAQ,SAAS;AAAA,MAC3B;AACA,UAAI,UAAU,GAAG;AACf,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAClC;AACA,MAAAD,UAASC;AACT;AAAA,IACF;AACA,QAAI,MAAM,OAAOD;AACjB,WAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,IACF;AACA,QAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,WAAO,MAAM,MAAM,EAAE,KAAK;AACxB,aAAO,SAAS,OAAO,IAAI,GAAG,CAAC;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AACA,WAAS,aAAa,QAAQ;AAC5B,QAAI,OAAO,WAAW,UAAU;AAC9B,YAAM,IAAI,UAAU,iBAAiB;AAAA,IACvC;AACA,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO,IAAI,WAAW;AAAA,IACxB;AACA,QAAI,MAAM;AACV,QAAI,OAAO,GAAG,MAAM,KAAK;AACvB;AAAA,IACF;AACA,QAAI,SAAS;AACb,QAAIA,UAAS;AACb,WAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,IACF;AACA,QAAI,QAAQ,OAAO,SAAS,OAAO,SAAS,MAAM;AAClD,QAAI,OAAO,IAAI,WAAW,IAAI;AAC9B,WAAO,OAAO,GAAG,GAAG;AAClB,UAAI,QAAQ,SAAS,OAAO,WAAW,GAAG,CAAC;AAC3C,UAAI,UAAU,KAAK;AACjB;AAAA,MACF;AACA,UAAIC,KAAI;AACR,eAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAID,YAAW,QAAQ,IAAI,OAAOC,MAAK;AAC9E,iBAAS,OAAO,KAAK,GAAG,MAAM;AAC9B,aAAK,GAAG,IAAI,QAAQ,QAAQ;AAC5B,gBAAQ,QAAQ,QAAQ;AAAA,MAC1B;AACA,UAAI,UAAU,GAAG;AACf,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAClC;AACA,MAAAD,UAASC;AACT;AAAA,IACF;AACA,QAAI,OAAO,GAAG,MAAM,KAAK;AACvB;AAAA,IACF;AACA,QAAI,MAAM,OAAOD;AACjB,WAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,IACF;AACA,QAAI,MAAM,IAAI,WAAW,UAAU,OAAO,IAAI;AAC9C,QAAIE,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,UAAIA,IAAG,IAAI,KAAK,KAAK;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,WAASC,QAAOC,SAAQ;AACtB,QAAI,SAAS,aAAaA,OAAM;AAChC,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,UAAM,IAAI,MAAM,OAAQN,KAAK,YAAY;AAAA,EAC3C;AACA,SAAO;AAAA,IACL,QAAQC;AAAA,IACR;AAAA,IACA,QAAQI;AAAA,EACV;AACF;AACA,IAAI,MAAM;AACV,IAAI,kCAAkC;AACtC,IAAO,iBAAQ;;;ACpIf,IAAM,QAAQ,IAAI,WAAW,CAAC;AAM9B,IAAM,SAAS,CAAC,IAAI,OAAO;AACzB,MAAI,OAAO;AACT,WAAO;AACT,MAAI,GAAG,eAAe,GAAG,YAAY;AACnC,WAAO;AAAA,EACT;AACA,WAAS,KAAK,GAAG,KAAK,GAAG,YAAY,MAAM;AACzC,QAAI,GAAG,EAAE,MAAM,GAAG,EAAE,GAAG;AACrB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,SAAS,OAAK;AAClB,MAAI,aAAa,cAAc,EAAE,YAAY,SAAS;AACpD,WAAO;AACT,MAAI,aAAa;AACf,WAAO,IAAI,WAAW,CAAC;AACzB,MAAI,YAAY,OAAO,CAAC,GAAG;AACzB,WAAO,IAAI,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU;AAAA,EAC5D;AACA,QAAM,IAAI,MAAM,mCAAmC;AACrD;AAEA,IAAM,aAAa,SAAO,IAAI,YAAY,EAAE,OAAO,GAAG;AACtD,IAAM,WAAW,OAAK,IAAI,YAAY,EAAE,OAAO,CAAC;;;AC7BhD,IAAM,UAAN,MAAc;AAAA,EACZ,YAAYE,OAAM,QAAQ,YAAY;AACpC,SAAK,OAAOA;AACZ,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,iBAAiB,YAAY;AAC/B,aAAO,GAAI,KAAK,MAAO,GAAI,KAAK,WAAW,KAAK,CAAE;AAAA,IACpD,OAAO;AACL,YAAM,MAAM,mCAAmC;AAAA,IACjD;AAAA,EACF;AACF;AACA,IAAM,UAAN,MAAc;AAAA,EACZ,YAAYA,OAAM,QAAQ,YAAY;AACpC,SAAK,OAAOA;AACZ,SAAK,SAAS;AACd,QAAI,OAAO,YAAY,CAAC,MAAM,QAAW;AACvC,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC5C;AACA,SAAK,kBAAkB,OAAO,YAAY,CAAC;AAC3C,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO,MAAM;AACX,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,KAAK,YAAY,CAAC,MAAM,KAAK,iBAAiB;AAChD,cAAM,MAAM,qCAAsC,KAAK,UAAU,IAAI,CAAE,KAAM,KAAK,IAAK,+CAAgD,KAAK,MAAO,EAAE;AAAA,MACvJ;AACA,aAAO,KAAK,WAAW,KAAK,MAAM,KAAK,OAAO,MAAM,CAAC;AAAA,IACvD,OAAO;AACL,YAAM,MAAM,mCAAmC;AAAA,IACjD;AAAA,EACF;AAAA,EACA,GAAG,SAAS;AACV,WAAO,GAAG,MAAM,OAAO;AAAA,EACzB;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,GAAG,SAAS;AACV,WAAO,GAAG,MAAM,OAAO;AAAA,EACzB;AAAA,EACA,OAAO,OAAO;AACZ,UAAM,SAAS,MAAM,CAAC;AACtB,UAAM,UAAU,KAAK,SAAS,MAAM;AACpC,QAAI,SAAS;AACX,aAAO,QAAQ,OAAO,KAAK;AAAA,IAC7B,OAAO;AACL,YAAM,WAAW,qCAAsC,KAAK,UAAU,KAAK,CAAE,+BAAgC,OAAO,KAAK,KAAK,QAAQ,CAAE,gBAAgB;AAAA,IAC1J;AAAA,EACF;AACF;AACO,IAAM,KAAK,CAAC,MAAM,UAAU,IAAI,gBAAgB;AAAA,EACrD,GAAG,KAAK,YAAY,EAAE,CAAC,KAAK,MAAM,GAAG,KAAK;AAAA,EAC1C,GAAG,MAAM,YAAY,EAAE,CAAC,MAAM,MAAM,GAAG,MAAM;AAC/C,CAAC;AACM,IAAM,QAAN,MAAY;AAAA,EACjB,YAAYA,OAAM,QAAQ,YAAY,YAAY;AAChD,SAAK,OAAOA;AACZ,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,UAAU,IAAI,QAAQA,OAAM,QAAQ,UAAU;AACnD,SAAK,UAAU,IAAI,QAAQA,OAAM,QAAQ,UAAU;AAAA,EACrD;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,KAAK,QAAQ,OAAO,KAAK;AAAA,EAClC;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,KAAK,QAAQ,OAAO,KAAK;AAAA,EAClC;AACF;AACO,IAAM,OAAO,CAAC,EAAC,MAAAA,OAAM,QAAQ,QAAAC,SAAQ,QAAAC,QAAM,MAAM,IAAI,MAAMF,OAAM,QAAQC,SAAQC,OAAM;AACvF,IAAM,QAAQ,CAAC,EAAC,QAAQ,MAAAF,OAAM,UAAAG,UAAQ,MAAM;AACjD,QAAM,EAAC,QAAAF,SAAQ,QAAAC,QAAM,IAAI,eAAMC,WAAUH,KAAI;AAC7C,SAAO,KAAK;AAAA,IACV;AAAA,IACA,MAAAA;AAAA,IACA,QAAAC;AAAA,IACA,QAAQ,UAAQ,OAAOC,QAAO,IAAI,CAAC;AAAA,EACrC,CAAC;AACH;AACA,IAAM,SAAS,CAACE,SAAQD,WAAU,aAAaH,UAAS;AACtD,QAAM,QAAQ,CAAC;AACf,WAAS,IAAI,GAAG,IAAIG,UAAS,QAAQ,EAAE,GAAG;AACxC,UAAMA,UAAS,CAAC,CAAC,IAAI;AAAA,EACvB;AACA,MAAI,MAAMC,QAAO;AACjB,SAAOA,QAAO,MAAM,CAAC,MAAM,KAAK;AAC9B,MAAE;AAAA,EACJ;AACA,QAAM,MAAM,IAAI,WAAW,MAAM,cAAc,IAAI,CAAC;AACpD,MAAI,OAAO;AACX,MAAI,SAAS;AACb,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,UAAM,QAAQ,MAAMA,QAAO,CAAC,CAAC;AAC7B,QAAI,UAAU,QAAW;AACvB,YAAM,IAAI,YAAY,OAAQJ,KAAK,YAAY;AAAA,IACjD;AACA,aAAS,UAAU,cAAc;AACjC,YAAQ;AACR,QAAI,QAAQ,GAAG;AACb,cAAQ;AACR,UAAI,SAAS,IAAI,MAAM,UAAU;AAAA,IACnC;AAAA,EACF;AACA,MAAI,QAAQ,eAAe,MAAM,UAAU,IAAI,MAAM;AACnD,UAAM,IAAI,YAAY,wBAAwB;AAAA,EAChD;AACA,SAAO;AACT;AACA,IAAM,SAAS,CAAC,MAAMG,WAAU,gBAAgB;AAC9C,QAAM,MAAMA,UAASA,UAAS,SAAS,CAAC,MAAM;AAC9C,QAAM,QAAQ,KAAK,eAAe;AAClC,MAAI,MAAM;AACV,MAAI,OAAO;AACX,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,aAAS,UAAU,IAAI,KAAK,CAAC;AAC7B,YAAQ;AACR,WAAO,OAAO,aAAa;AACzB,cAAQ;AACR,aAAOA,UAAS,OAAO,UAAU,IAAI;AAAA,IACvC;AAAA,EACF;AACA,MAAI,MAAM;AACR,WAAOA,UAAS,OAAO,UAAU,cAAc,IAAI;AAAA,EACrD;AACA,MAAI,KAAK;AACP,WAAO,IAAI,SAAS,cAAc,GAAG;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACO,IAAM,UAAU,CAAC,EAAC,MAAAH,OAAM,QAAQ,aAAa,UAAAG,UAAQ,MAAM;AAChE,SAAO,KAAK;AAAA,IACV;AAAA,IACA,MAAAH;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,OAAO,OAAOG,WAAU,WAAW;AAAA,IAC5C;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,OAAO,OAAOA,WAAU,aAAaH,KAAI;AAAA,IAClD;AAAA,EACF,CAAC;AACH;;;AHnJO,IAAM,WAAW,KAAK;AAAA,EAC3B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ,SAAO,SAAS,GAAG;AAAA,EAC3B,QAAQ,SAAO,WAAW,GAAG;AAC/B,CAAC;;;AIVD;AAAA;AAAA;AAAA;AACO,IAAM,QAAQ,QAAQ;AAAA,EAC3B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACND;AAAA;AAAA;AAAA;AACO,IAAM,QAAQ,QAAQ;AAAA,EAC3B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACND;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,MAAM;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AACZ,CAAC;;;ACLD;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,QAAQ;AAAA,EAC5B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,cAAc,QAAQ;AAAA,EACjC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACZD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,QAAQ;AAAA,EAC5B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,cAAc,QAAQ;AAAA,EACjC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,YAAY,QAAQ;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,iBAAiB,QAAQ;AAAA,EACpC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,YAAY,QAAQ;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,iBAAiB,QAAQ;AAAA,EACpC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,eAAe,QAAQ;AAAA,EAClC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,oBAAoB,QAAQ;AAAA,EACvC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,UAAU,QAAQ;AAAA,EAC7B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACtDD;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,MAAM;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AACZ,CAAC;AACM,IAAM,cAAc,MAAM;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AACZ,CAAC;;;ACVD;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,YAAY,MAAM;AAAA,EAC7B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC;AACM,IAAM,eAAe,MAAM;AAAA,EAChC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC;;;ACVD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,QAAQ;AAAA,EAC5B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,YAAY,QAAQ;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,YAAY,QAAQ;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,eAAe,QAAQ;AAAA,EAClC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACxBD;AAAA;AAAA;AAAA;AACA,IAAM,WAAW,MAAM,KAAK,weAAs2F;AACl4F,IAAM,uBAAuB,SAAS,OAAO,CAACK,IAAG,GAAG,MAAM;AACxD,EAAAA,GAAE,CAAC,IAAI;AACP,SAAOA;AACT,GAAG,CAAC,CAAC;AACL,IAAM,uBAAuB,SAAS,OAAO,CAACA,IAAG,GAAG,MAAM;AACxD,EAAAA,GAAE,EAAE,YAAY,CAAC,CAAC,IAAI;AACtB,SAAOA;AACT,GAAG,CAAC,CAAC;AACL,SAASC,QAAO,MAAM;AACpB,SAAO,KAAK,OAAO,CAACD,IAAG,MAAM;AAC3B,IAAAA,MAAK,qBAAqB,CAAC;AAC3B,WAAOA;AAAA,EACT,GAAG,EAAE;AACP;AACA,SAASE,QAAO,KAAK;AACnB,QAAM,OAAO,CAAC;AACd,aAAW,QAAQ,KAAK;AACtB,UAAM,MAAM,qBAAqB,KAAK,YAAY,CAAC,CAAC;AACpD,QAAI,QAAQ,QAAW;AACrB,YAAM,IAAI,MAAM,+BAAgC,IAAK,EAAE;AAAA,IACzD;AACA,SAAK,KAAK,GAAG;AAAA,EACf;AACA,SAAO,IAAI,WAAW,IAAI;AAC5B;AACO,IAAM,eAAe,KAAK;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAAD;AAAA,EACA,QAAAC;AACF,CAAC;;;AChCD;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAI,WAAWC;AACf,IAAI,MAAM;AAAV,IAAe,OAAO;AAAtB,IAA2B,SAAS,CAAC;AAArC,IAA2C,MAAM,KAAK,IAAI,GAAG,EAAE;AAC/D,SAASA,QAAO,KAAK,KAAK,QAAQ;AAChC,QAAM,OAAO,CAAC;AACd,WAAS,UAAU;AACnB,MAAI,YAAY;AAChB,SAAO,OAAO,KAAK;AACjB,QAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,MAAM,QAAQ;AACnB,QAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,aAAS;AAAA,EACX;AACA,MAAI,MAAM,IAAI,MAAM;AACpB,EAAAA,QAAO,QAAQ,SAAS,YAAY;AACpC,SAAO;AACT;AACA,IAAIC,UAAS;AACb,IAAI,QAAQ;AAAZ,IAAiB,SAAS;AAC1B,SAAS,KAAK,KAAK,QAAQ;AACzB,MAAI,MAAM,GAAG,SAAS,UAAU,GAAG,QAAQ,GAAG,UAAU,QAAQ,GAAG,IAAI,IAAI;AAC3E,KAAG;AACD,QAAI,WAAW,GAAG;AAChB,WAAK,QAAQ;AACb,YAAM,IAAI,WAAW,yBAAyB;AAAA,IAChD;AACA,QAAI,IAAI,SAAS;AACjB,WAAO,QAAQ,MAAM,IAAI,WAAW,SAAS,IAAI,UAAU,KAAK,IAAI,GAAG,KAAK;AAC5E,aAAS;AAAA,EACX,SAAS,KAAK;AACd,OAAK,QAAQ,UAAU;AACvB,SAAO;AACT;AACA,IAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,SAAS,SAAU,OAAO;AAC5B,SAAO,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI;AAClK;AACA,IAAI,SAAS;AAAA,EACX,QAAQ;AAAA,EACR,QAAQA;AAAA,EACR,gBAAgB;AAClB;AACA,IAAI,eAAe;AACnB,IAAO,iBAAQ;;;ACnDR,IAAMC,UAAS,CAAC,MAAM,SAAS,MAAM;AAC1C,QAAMC,QAAO,eAAO,OAAO,MAAM,MAAM;AACvC,SAAO;AAAA,IACLA;AAAA,IACA,eAAO,OAAO;AAAA,EAChB;AACF;AACO,IAAM,WAAW,CAAC,KAAK,QAAQ,SAAS,MAAM;AACnD,iBAAO,OAAO,KAAK,QAAQ,MAAM;AACjC,SAAO;AACT;AACO,IAAM,iBAAiB,SAAO;AACnC,SAAO,eAAO,eAAe,GAAG;AAClC;;;ACTO,IAAM,SAAS,CAACC,OAAMC,YAAW;AACtC,QAAM,OAAOA,QAAO;AACpB,QAAM,aAAoB,eAAeD,KAAI;AAC7C,QAAM,eAAe,aAAoB,eAAe,IAAI;AAC5D,QAAM,QAAQ,IAAI,WAAW,eAAe,IAAI;AAChD,EAAO,SAASA,OAAM,OAAO,CAAC;AAC9B,EAAO,SAAS,MAAM,OAAO,UAAU;AACvC,QAAM,IAAIC,SAAQ,YAAY;AAC9B,SAAO,IAAI,OAAOD,OAAM,MAAMC,SAAQ,KAAK;AAC7C;AACO,IAAMC,UAAS,eAAa;AACjC,QAAM,QAAQ,OAAO,SAAS;AAC9B,QAAM,CAACF,OAAM,UAAU,IAAWE,QAAO,KAAK;AAC9C,QAAM,CAAC,MAAM,YAAY,IAAWA,QAAO,MAAM,SAAS,UAAU,CAAC;AACrE,QAAMD,UAAS,MAAM,SAAS,aAAa,YAAY;AACvD,MAAIA,QAAO,eAAe,MAAM;AAC9B,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACpC;AACA,SAAO,IAAI,OAAOD,OAAM,MAAMC,SAAQ,KAAK;AAC7C;AACO,IAAME,UAAS,CAAC,GAAG,MAAM;AAC9B,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT,OAAO;AACL,WAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,OAAW,EAAE,OAAO,EAAE,KAAK;AAAA,EAC9E;AACF;AACO,IAAM,SAAN,MAAa;AAAA,EAClB,YAAYH,OAAM,MAAMC,SAAQ,OAAO;AACrC,SAAK,OAAOD;AACZ,SAAK,OAAO;AACZ,SAAK,SAASC;AACd,SAAK,QAAQ;AAAA,EACf;AACF;;;ACtCO,IAAMG,QAAO,CAAC,EAAC,MAAAC,OAAM,MAAAC,OAAM,QAAAC,QAAM,MAAM,IAAI,OAAOF,OAAMC,OAAMC,OAAM;AACpE,IAAM,SAAN,MAAa;AAAA,EAClB,YAAYF,OAAMC,OAAMC,SAAQ;AAC9B,SAAK,OAAOF;AACZ,SAAK,OAAOC;AACZ,SAAK,SAASC;AAAA,EAChB;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,iBAAiB,YAAY;AAC/B,YAAM,SAAS,KAAK,OAAO,KAAK;AAChC,aAAO,kBAAkB,aAAoB,OAAO,KAAK,MAAM,MAAM,IAAI,OAAO,KAAK,CAAAC,YAAiB,OAAO,KAAK,MAAMA,OAAM,CAAC;AAAA,IACjI,OAAO;AACL,YAAM,MAAM,mCAAmC;AAAA,IACjD;AAAA,EACF;AACF;;;AJfA,IAAM,MAAM,CAAAC,UAAQ,OAAM,SAAQ,IAAI,WAAW,MAAM,OAAO,OAAO,OAAOA,OAAM,IAAI,CAAC;AAChF,IAAM,SAASC,MAAK;AAAA,EACzB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ,IAAI,SAAS;AACvB,CAAC;AACM,IAAM,SAASA,MAAK;AAAA,EACzB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ,IAAI,SAAS;AACvB,CAAC;;;AKXD,IAAAC,oBAAA;AAAA,SAAAA,mBAAA;AAAA,kBAAAC;AAAA;AAEA,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAMC,UAAS;AACf,IAAM,SAAS,WAAgB,OAAO,MAAMA,QAAO,KAAK,CAAC;AAClD,IAAMC,YAAW;AAAA,EACtB;AAAA,EACA;AAAA,EACA,QAAAD;AAAA,EACA;AACF;;;ACXA,IAAM,cAAc,IAAI,YAAY;AACpC,IAAM,cAAc,IAAI,YAAY;;;ACI7B,IAAM,MAAN,MAAM,KAAI;AAAA,EACf,YAAYE,UAASC,OAAM,WAAW,OAAO;AAC3C,SAAK,OAAOA;AACZ,SAAK,UAAUD;AACf,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,aAAa,MAAM;AACxB,SAAK,aAAa,MAAM;AACxB,SAAK,QAAQ;AACb,SAAK,aAAa,oBAAI,IAAI;AAC1B,WAAO,iBAAiB,MAAM;AAAA,MAC5B,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,YAAQ,KAAK,SAAS;AAAA,MACtB,KAAK,GAAG;AACJ,eAAO;AAAA,MACT;AAAA,MACF,SAAS;AACL,cAAM,EAAC,MAAAC,OAAM,UAAS,IAAI;AAC1B,YAAIA,UAAS,aAAa;AACxB,gBAAM,IAAI,MAAM,0CAA0C;AAAA,QAC5D;AACA,YAAI,UAAU,SAAS,cAAc;AACnC,gBAAM,IAAI,MAAM,oDAAoD;AAAA,QACtE;AACA,eAAO,KAAI,SAAS,SAAS;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,YAAQ,KAAK,SAAS;AAAA,MACtB,KAAK,GAAG;AACJ,cAAM,EAAC,MAAAA,OAAM,QAAAC,QAAM,IAAI,KAAK;AAC5B,cAAM,YAAmB,OAAOD,OAAMC,OAAM;AAC5C,eAAO,KAAI,SAAS,KAAK,MAAM,SAAS;AAAA,MAC1C;AAAA,MACF,KAAK,GAAG;AACJ,eAAO;AAAA,MACT;AAAA,MACF,SAAS;AACL,cAAM,MAAM,+BAAgC,KAAK,OAAQ,4CAA4C;AAAA,MACvG;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,SAAS,KAAK,SAAS,MAAM,QAAQ,KAAK,YAAY,MAAM,WAAkBC,QAAO,KAAK,WAAW,MAAM,SAAS;AAAA,EAC7H;AAAA,EACA,SAASC,OAAM;AACb,UAAM,EAAC,OAAO,SAAAJ,UAAS,WAAU,IAAI;AACrC,YAAQA,UAAS;AAAA,MACjB,KAAK;AACH,eAAO,WAAW,OAAO,YAAYI,SAAQ,UAAU,OAAO;AAAA,MAChE;AACE,eAAO,WAAW,OAAO,YAAYA,SAAQ,OAAO,OAAO;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,MAAM,KAAK,UAAU;AAAA,IACvB;AAAA,EACF;AAAA,EACA,KAAK,OAAO,WAAW,IAAI;AACzB,WAAO;AAAA,EACT;AAAA,EACA,CAAC,OAAO,IAAI,4BAA4B,CAAC,IAAI;AAC3C,WAAO,SAAS,KAAK,SAAS,IAAI;AAAA,EACpC;AAAA,EACA,OAAO,MAAM,OAAO;AAClB,cAAU,SAAS,kBAAkB;AACrC,WAAO,CAAC,EAAE,UAAU,MAAM,SAAS,KAAK,MAAM,UAAU;AAAA,EAC1D;AAAA,EACA,IAAI,sBAAsB;AACxB,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AAAA,EACA,IAAI,QAAQ;AACV,UAAM,IAAI,MAAM,qEAAqE;AAAA,EACvF;AAAA,EACA,IAAI,SAAS;AACX,UAAM,IAAI,MAAM,mEAAmE;AAAA,EACrF;AAAA,EACA,IAAI,gBAAgB;AAClB,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC1D;AAAA,EACA,IAAI,SAAS;AACX,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACnD;AAAA,EACA,OAAO,MAAM,OAAO;AAClB,QAAI,iBAAiB,MAAK;AACxB,aAAO;AAAA,IACT,WAAW,SAAS,QAAQ,MAAM,UAAU,OAAO;AACjD,YAAM,EAAC,SAAAJ,UAAS,MAAAC,OAAM,WAAW,MAAK,IAAI;AAC1C,aAAO,IAAI,KAAID,UAASC,OAAM,WAAW,SAAS,UAAUD,UAASC,OAAM,UAAU,KAAK,CAAC;AAAA,IAC7F,WAAW,SAAS,QAAQ,MAAM,SAAS,MAAM,MAAM;AACrD,YAAM,EAAC,SAAAD,UAAS,WAAW,MAAAC,MAAI,IAAI;AACnC,YAAMC,UAAgBG,QAAO,SAAS;AACtC,aAAO,KAAI,OAAOL,UAASC,OAAMC,OAAM;AAAA,IACzC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,OAAO,OAAOF,UAASC,OAAMC,SAAQ;AACnC,QAAI,OAAOD,UAAS,UAAU;AAC5B,YAAM,IAAI,MAAM,uCAAuC;AAAA,IACzD;AACA,YAAQD,UAAS;AAAA,MACjB,KAAK,GAAG;AACJ,YAAIC,UAAS,aAAa;AACxB,gBAAM,IAAI,MAAM,wCAAyC,WAAY,kBAAkB;AAAA,QACzF,OAAO;AACL,iBAAO,IAAI,KAAID,UAASC,OAAMC,SAAQA,QAAO,KAAK;AAAA,QACpD;AAAA,MACF;AAAA,MACF,KAAK,GAAG;AACJ,cAAM,QAAQ,UAAUF,UAASC,OAAMC,QAAO,KAAK;AACnD,eAAO,IAAI,KAAIF,UAASC,OAAMC,SAAQ,KAAK;AAAA,MAC7C;AAAA,MACF,SAAS;AACL,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASA,SAAQ;AACtB,WAAO,KAAI,OAAO,GAAG,aAAaA,OAAM;AAAA,EAC1C;AAAA,EACA,OAAO,SAASD,OAAMC,SAAQ;AAC5B,WAAO,KAAI,OAAO,GAAGD,OAAMC,OAAM;AAAA,EACnC;AAAA,EACA,OAAO,OAAO,OAAO;AACnB,UAAM,CAAC,KAAK,SAAS,IAAI,KAAI,YAAY,KAAK;AAC9C,QAAI,UAAU,QAAQ;AACpB,YAAM,IAAI,MAAM,kBAAkB;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,YAAY,OAAO;AACxB,UAAM,QAAQ,KAAI,aAAa,KAAK;AACpC,UAAM,aAAa,MAAM,OAAO,MAAM;AACtC,UAAM,iBAAiB,OAAO,MAAM,SAAS,YAAY,aAAa,MAAM,aAAa,CAAC;AAC1F,QAAI,eAAe,eAAe,MAAM,eAAe;AACrD,YAAM,IAAI,MAAM,kBAAkB;AAAA,IACpC;AACA,UAAM,cAAc,eAAe,SAAS,MAAM,gBAAgB,MAAM,UAAU;AAClF,UAAMA,UAAS,IAAW,OAAO,MAAM,eAAe,MAAM,YAAY,aAAa,cAAc;AACnG,UAAM,MAAM,MAAM,YAAY,IAAI,KAAI,SAASA,OAAM,IAAI,KAAI,SAAS,MAAM,OAAOA,OAAM;AACzF,WAAO;AAAA,MACL;AAAA,MACA,MAAM,SAAS,MAAM,IAAI;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,OAAO,aAAa,cAAc;AAChC,QAAI,SAAS;AACb,UAAM,OAAO,MAAM;AACjB,YAAM,CAAC,GAAGI,OAAM,IAAWD,QAAO,aAAa,SAAS,MAAM,CAAC;AAC/D,gBAAUC;AACV,aAAO;AAAA,IACT;AACA,QAAIN,WAAU,KAAK;AACnB,QAAI,QAAQ;AACZ,QAAIA,aAAY,IAAI;AAClB,MAAAA,WAAU;AACV,eAAS;AAAA,IACX,WAAWA,aAAY,GAAG;AACxB,cAAQ,KAAK;AAAA,IACf;AACA,QAAIA,aAAY,KAAKA,aAAY,GAAG;AAClC,YAAM,IAAI,WAAW,uBAAwBA,QAAQ,EAAE;AAAA,IACzD;AACA,UAAM,aAAa;AACnB,UAAM,gBAAgB,KAAK;AAC3B,UAAM,aAAa,KAAK;AACxB,UAAM,OAAO,SAAS;AACtB,UAAM,gBAAgB,OAAO;AAC7B,WAAO;AAAA,MACL,SAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,MAAM,QAAQI,OAAM;AACzB,UAAM,CAAC,QAAQ,KAAK,IAAI,gBAAgB,QAAQA,KAAI;AACpD,UAAM,MAAM,KAAI,OAAO,KAAK;AAC5B,QAAI,WAAW,IAAI,QAAQ,MAAM;AACjC,WAAO;AAAA,EACT;AACF;AACA,IAAM,kBAAkB,CAAC,QAAQA,UAAS;AACxC,UAAQ,OAAO,CAAC,GAAG;AAAA,IACnB,KAAK,KAAK;AACN,YAAM,UAAUA,SAAQ;AACxB,aAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ,OAAO,GAAI,UAAU,MAAO,GAAI,MAAO,EAAE;AAAA,MACnD;AAAA,IACF;AAAA,IACF,KAAK,UAAU,QAAQ;AACnB,YAAM,UAAUA,SAAQ;AACxB,aAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ,OAAO,MAAM;AAAA,MACvB;AAAA,IACF;AAAA,IACF,KAAK,OAAO,QAAQ;AAChB,YAAM,UAAUA,SAAQ;AACxB,aAAO;AAAA,QACL,OAAO;AAAA,QACP,QAAQ,OAAO,MAAM;AAAA,MACvB;AAAA,IACF;AAAA,IACF,SAAS;AACL,UAAIA,SAAQ,MAAM;AAChB,cAAM,MAAM,iFAAiF;AAAA,MAC/F;AACA,aAAO;AAAA,QACL,OAAO,CAAC;AAAA,QACRA,MAAK,OAAO,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,aAAa,CAAC,OAAO,OAAOA,UAAS;AACzC,QAAM,EAAC,OAAM,IAAIA;AACjB,MAAI,WAAW,UAAU,QAAQ;AAC/B,UAAM,MAAM,8BAA+BA,MAAK,IAAK,WAAW;AAAA,EAClE;AACA,QAAM,MAAM,MAAM,IAAI,MAAM;AAC5B,MAAI,OAAO,MAAM;AACf,UAAMG,OAAMH,MAAK,OAAO,KAAK,EAAE,MAAM,CAAC;AACtC,UAAM,IAAI,QAAQG,IAAG;AACrB,WAAOA;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,aAAa,CAAC,OAAO,OAAOH,UAAS;AACzC,QAAM,EAAC,OAAM,IAAIA;AACjB,QAAM,MAAM,MAAM,IAAI,MAAM;AAC5B,MAAI,OAAO,MAAM;AACf,UAAMG,OAAMH,MAAK,OAAO,KAAK;AAC7B,UAAM,IAAI,QAAQG,IAAG;AACrB,WAAOA;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,YAAY,CAACP,UAASC,OAAM,cAAc;AAC9C,QAAM,aAAoB,eAAeD,QAAO;AAChD,QAAM,aAAa,aAAoB,eAAeC,KAAI;AAC1D,QAAM,QAAQ,IAAI,WAAW,aAAa,UAAU,UAAU;AAC9D,EAAO,SAASD,UAAS,OAAO,CAAC;AACjC,EAAO,SAASC,OAAM,OAAO,UAAU;AACvC,QAAM,IAAI,WAAW,UAAU;AAC/B,SAAO;AACT;AACA,IAAM,YAAY,OAAO,IAAI,kBAAkB;AAC/C,IAAM,WAAW;AAAA,EACf,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AACd;AACA,IAAM,SAAS;AAAA,EACb,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAChB;AACA,IAAM,UAAU;AAChB,IAAM,YAAY,CAAC,OAAO,YAAY;AACpC,MAAI,MAAM,KAAK,OAAO,GAAG;AACvB,YAAQ,KAAK,OAAO;AAAA,EACtB,OAAO;AACL,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACF;AACA,IAAM,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AChR3B,IAAM,QAAQ;AAAA,EACZ,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,SAAS;AAAA,EACb,GAAG;AAAA,EACH,GAAGO;AACL;;;AClCA,SAAS,YAAYC,OAAM,QAAQC,SAAQC,SAAQ;AACjD,SAAO;AAAA,IACL,MAAAF;AAAA,IACA;AAAA,IACA,SAAS;AAAA,MACP,MAAAA;AAAA,MACA;AAAA,MACA,QAAAC;AAAA,IACF;AAAA,IACA,SAAS,EAAE,QAAAC,QAAO;AAAA,EACpB;AACF;AACA,IAAM,SAAS,YAAY,QAAQ,KAAK,SAAO;AAC7C,QAAM,UAAU,IAAI,YAAY,MAAM;AACtC,SAAO,MAAM,QAAQ,OAAO,GAAG;AACjC,GAAG,SAAO;AACR,QAAM,UAAU,IAAI,YAAY;AAChC,SAAO,QAAQ,OAAO,IAAI,UAAU,CAAC,CAAC;AACxC,CAAC;AACD,IAAM,QAAQ,YAAY,SAAS,KAAK,SAAO;AAC7C,MAAIC,UAAS;AACb,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,IAAAA,WAAU,OAAO,aAAa,IAAI,CAAC,CAAC;AAAA,EACtC;AACA,SAAOA;AACT,GAAG,SAAO;AACR,QAAM,IAAI,UAAU,CAAC;AACrB,QAAM,MAAM,YAAY,IAAI,MAAM;AAClC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,EAC3B;AACA,SAAO;AACT,CAAC;AACD,IAAM,QAAQ;AAAA,EACZ,MAAM;AAAA,EACN,SAAS;AAAA,EACT,KAAK,MAAM;AAAA,EACX,QAAQ;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,EACR,GAAG;AACL;AACA,IAAO,gBAAQ;;;AC1CR,SAASC,YAAWC,SAAQ,WAAW,QAAQ;AACpD,QAAMC,QAAO,cAAM,QAAQ;AAC3B,MAAI,CAACA,OAAM;AACT,UAAM,IAAI,MAAM,yBAA0B,QAAS,GAAG;AAAA,EACxD;AACA,OAAK,aAAa,UAAU,aAAa,YAAY,WAAW,UAAU,QAAQ,WAAW,OAAO,QAAQ,MAAM;AAChH,WAAO,aAAa,WAAW,OAAO,KAAKD,SAAQ,OAAO,CAAC;AAAA,EAC7D;AACA,SAAOC,MAAK,QAAQ,OAAO,GAAIA,MAAK,MAAO,GAAID,OAAO,EAAE;AAC1D;;;ACVO,SAASE,UAAS,OAAO,WAAW,QAAQ;AACjD,QAAMC,QAAO,cAAM,QAAQ;AAC3B,MAAI,CAACA,OAAM;AACT,UAAM,IAAI,MAAM,yBAA0B,QAAS,GAAG;AAAA,EACxD;AACA,OAAK,aAAa,UAAU,aAAa,YAAY,WAAW,UAAU,QAAQ,WAAW,OAAO,QAAQ,MAAM;AAChH,WAAO,WAAW,OAAO,KAAK,MAAM,QAAQ,MAAM,YAAY,MAAM,UAAU,EAAE,SAAS,MAAM;AAAA,EACjG;AACA,SAAOA,MAAK,QAAQ,OAAO,KAAK,EAAE,UAAU,CAAC;AAC/C;;;;;;;;;;AC6BA,IAAA,IAAA,EAAA,MAAA,EAAA,SAAA,gBAAA,cAAA,qBAAA,WAAA,kBAAA,gBAAA,uBAAA,cAAA,qBAAA,aAAA,oBAAA,kBAAA,yBAAA,oBAAA,0BAAA,GAAA,KAAA,EAAA,SAAA,eAAA,cAAA,oBAAA,WAAA,iBAAA,gBAAA,sBAAA,cAAA,oBAAA,aAAA,mBAAA,kBAAA,wBAAA,oBAAA,yBAAA,GAAA,SAAA,EAAA,SAAA,mBAAA,cAAA,wBAAA,WAAA,qBAAA,gBAAA,0BAAA,cAAA,wBAAA,aAAA,uBAAA,kBAAA,4BAAA,oBAAA,6BAAA,EAAA;", "names": ["src", "compare", "Poly1305", "m", "h", "digest", "ChaCha20Poly1305", "h", "length", "h", "HMAC", "h", "digest", "HKDF", "length", "length", "_", "length", "length", "length", "SHA256", "K", "w", "p", "h", "digest", "p", "m", "generateKeyPair", "sharedKey", "p", "_", "m", "k", "k2", "v", "d", "b", "__assign", "encode", "decode", "parse", "k", "length", "name", "encode", "length", "i", "j", "decode", "string", "name", "encode", "decode", "alphabet", "string", "p", "encode", "decode", "encode", "decode", "decode", "code", "code", "digest", "decode", "equals", "from", "name", "code", "encode", "digest", "name", "from", "identity_exports", "identity", "encode", "identity", "version", "code", "digest", "equals", "base", "decode", "length", "cid", "identity_exports", "name", "encode", "decode", "string", "fromString", "string", "base", "toString", "base"]}