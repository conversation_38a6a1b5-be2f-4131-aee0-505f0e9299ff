{"version": 3, "sources": ["../../detect-browser/es/index.js", "../../@walletconnect/window-getters/src/index.ts", "../../@walletconnect/window-metadata/src/index.ts", "../../strict-uri-encode/index.js", "../../decode-uri-component/index.js", "../../split-on-first/index.js", "../../filter-obj/index.js"], "sourcesContent": ["var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar BrowserInfo = /** @class */ (function () {\n    function BrowserInfo(name, version, os) {\n        this.name = name;\n        this.version = version;\n        this.os = os;\n        this.type = 'browser';\n    }\n    return BrowserInfo;\n}());\nexport { BrowserInfo };\nvar NodeInfo = /** @class */ (function () {\n    function NodeInfo(version) {\n        this.version = version;\n        this.type = 'node';\n        this.name = 'node';\n        this.os = process.platform;\n    }\n    return NodeInfo;\n}());\nexport { NodeInfo };\nvar SearchBotDeviceInfo = /** @class */ (function () {\n    function SearchBotDeviceInfo(name, version, os, bot) {\n        this.name = name;\n        this.version = version;\n        this.os = os;\n        this.bot = bot;\n        this.type = 'bot-device';\n    }\n    return SearchBotDeviceInfo;\n}());\nexport { SearchBotDeviceInfo };\nvar BotInfo = /** @class */ (function () {\n    function BotInfo() {\n        this.type = 'bot';\n        this.bot = true; // NOTE: deprecated test name instead\n        this.name = 'bot';\n        this.version = null;\n        this.os = null;\n    }\n    return BotInfo;\n}());\nexport { BotInfo };\nvar ReactNativeInfo = /** @class */ (function () {\n    function ReactNativeInfo() {\n        this.type = 'react-native';\n        this.name = 'react-native';\n        this.version = null;\n        this.os = null;\n    }\n    return ReactNativeInfo;\n}());\nexport { ReactNativeInfo };\n// tslint:disable-next-line:max-line-length\nvar SEARCHBOX_UA_REGEX = /alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/;\nvar SEARCHBOT_OS_REGEX = /(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\\ Jeeves\\/Teoma|ia_archiver)/;\nvar REQUIRED_VERSION_PARTS = 3;\nvar userAgentRules = [\n    ['aol', /AOLShield\\/([0-9\\._]+)/],\n    ['edge', /Edge\\/([0-9\\._]+)/],\n    ['edge-ios', /EdgiOS\\/([0-9\\._]+)/],\n    ['yandexbrowser', /YaBrowser\\/([0-9\\._]+)/],\n    ['kakaotalk', /KAKAOTALK\\s([0-9\\.]+)/],\n    ['samsung', /SamsungBrowser\\/([0-9\\.]+)/],\n    ['silk', /\\bSilk\\/([0-9._-]+)\\b/],\n    ['miui', /MiuiBrowser\\/([0-9\\.]+)$/],\n    ['beaker', /BeakerBrowser\\/([0-9\\.]+)/],\n    ['edge-chromium', /EdgA?\\/([0-9\\.]+)/],\n    [\n        'chromium-webview',\n        /(?!Chrom.*OPR)wv\\).*Chrom(?:e|ium)\\/([0-9\\.]+)(:?\\s|$)/,\n    ],\n    ['chrome', /(?!Chrom.*OPR)Chrom(?:e|ium)\\/([0-9\\.]+)(:?\\s|$)/],\n    ['phantomjs', /PhantomJS\\/([0-9\\.]+)(:?\\s|$)/],\n    ['crios', /CriOS\\/([0-9\\.]+)(:?\\s|$)/],\n    ['firefox', /Firefox\\/([0-9\\.]+)(?:\\s|$)/],\n    ['fxios', /FxiOS\\/([0-9\\.]+)/],\n    ['opera-mini', /Opera Mini.*Version\\/([0-9\\.]+)/],\n    ['opera', /Opera\\/([0-9\\.]+)(?:\\s|$)/],\n    ['opera', /OPR\\/([0-9\\.]+)(:?\\s|$)/],\n    ['pie', /^Microsoft Pocket Internet Explorer\\/(\\d+\\.\\d+)$/],\n    ['pie', /^Mozilla\\/\\d\\.\\d+\\s\\(compatible;\\s(?:MSP?IE|MSInternet Explorer) (\\d+\\.\\d+);.*Windows CE.*\\)$/],\n    ['netfront', /^Mozilla\\/\\d\\.\\d+.*NetFront\\/(\\d.\\d)/],\n    ['ie', /Trident\\/7\\.0.*rv\\:([0-9\\.]+).*\\).*Gecko$/],\n    ['ie', /MSIE\\s([0-9\\.]+);.*Trident\\/[4-7].0/],\n    ['ie', /MSIE\\s(7\\.0)/],\n    ['bb10', /BB10;\\sTouch.*Version\\/([0-9\\.]+)/],\n    ['android', /Android\\s([0-9\\.]+)/],\n    ['ios', /Version\\/([0-9\\._]+).*Mobile.*Safari.*/],\n    ['safari', /Version\\/([0-9\\._]+).*Safari/],\n    ['facebook', /FB[AS]V\\/([0-9\\.]+)/],\n    ['instagram', /Instagram\\s([0-9\\.]+)/],\n    ['ios-webview', /AppleWebKit\\/([0-9\\.]+).*Mobile/],\n    ['ios-webview', /AppleWebKit\\/([0-9\\.]+).*Gecko\\)$/],\n    ['curl', /^curl\\/([0-9\\.]+)$/],\n    ['searchbot', SEARCHBOX_UA_REGEX],\n];\nvar operatingSystemRules = [\n    ['iOS', /iP(hone|od|ad)/],\n    ['Android OS', /Android/],\n    ['BlackBerry OS', /BlackBerry|BB10/],\n    ['Windows Mobile', /IEMobile/],\n    ['Amazon OS', /Kindle/],\n    ['Windows 3.11', /Win16/],\n    ['Windows 95', /(Windows 95)|(Win95)|(Windows_95)/],\n    ['Windows 98', /(Windows 98)|(Win98)/],\n    ['Windows 2000', /(Windows NT 5.0)|(Windows 2000)/],\n    ['Windows XP', /(Windows NT 5.1)|(Windows XP)/],\n    ['Windows Server 2003', /(Windows NT 5.2)/],\n    ['Windows Vista', /(Windows NT 6.0)/],\n    ['Windows 7', /(Windows NT 6.1)/],\n    ['Windows 8', /(Windows NT 6.2)/],\n    ['Windows 8.1', /(Windows NT 6.3)/],\n    ['Windows 10', /(Windows NT 10.0)/],\n    ['Windows ME', /Windows ME/],\n    ['Windows CE', /Windows CE|WinCE|Microsoft Pocket Internet Explorer/],\n    ['Open BSD', /OpenBSD/],\n    ['Sun OS', /SunOS/],\n    ['Chrome OS', /CrOS/],\n    ['Linux', /(Linux)|(X11)/],\n    ['Mac OS', /(Mac_PowerPC)|(Macintosh)/],\n    ['QNX', /QNX/],\n    ['BeOS', /BeOS/],\n    ['OS/2', /OS\\/2/],\n];\nexport function detect(userAgent) {\n    if (!!userAgent) {\n        return parseUserAgent(userAgent);\n    }\n    if (typeof document === 'undefined' &&\n        typeof navigator !== 'undefined' &&\n        navigator.product === 'ReactNative') {\n        return new ReactNativeInfo();\n    }\n    if (typeof navigator !== 'undefined') {\n        return parseUserAgent(navigator.userAgent);\n    }\n    return getNodeVersion();\n}\nfunction matchUserAgent(ua) {\n    // opted for using reduce here rather than Array#first with a regex.test call\n    // this is primarily because using the reduce we only perform the regex\n    // execution once rather than once for the test and for the exec again below\n    // probably something that needs to be benchmarked though\n    return (ua !== '' &&\n        userAgentRules.reduce(function (matched, _a) {\n            var browser = _a[0], regex = _a[1];\n            if (matched) {\n                return matched;\n            }\n            var uaMatch = regex.exec(ua);\n            return !!uaMatch && [browser, uaMatch];\n        }, false));\n}\nexport function browserName(ua) {\n    var data = matchUserAgent(ua);\n    return data ? data[0] : null;\n}\nexport function parseUserAgent(ua) {\n    var matchedRule = matchUserAgent(ua);\n    if (!matchedRule) {\n        return null;\n    }\n    var name = matchedRule[0], match = matchedRule[1];\n    if (name === 'searchbot') {\n        return new BotInfo();\n    }\n    // Do not use RegExp for split operation as some browser do not support it (See: http://blog.stevenlevithan.com/archives/cross-browser-split)\n    var versionParts = match[1] && match[1].split('.').join('_').split('_').slice(0, 3);\n    if (versionParts) {\n        if (versionParts.length < REQUIRED_VERSION_PARTS) {\n            versionParts = __spreadArray(__spreadArray([], versionParts, true), createVersionParts(REQUIRED_VERSION_PARTS - versionParts.length), true);\n        }\n    }\n    else {\n        versionParts = [];\n    }\n    var version = versionParts.join('.');\n    var os = detectOS(ua);\n    var searchBotMatch = SEARCHBOT_OS_REGEX.exec(ua);\n    if (searchBotMatch && searchBotMatch[1]) {\n        return new SearchBotDeviceInfo(name, version, os, searchBotMatch[1]);\n    }\n    return new BrowserInfo(name, version, os);\n}\nexport function detectOS(ua) {\n    for (var ii = 0, count = operatingSystemRules.length; ii < count; ii++) {\n        var _a = operatingSystemRules[ii], os = _a[0], regex = _a[1];\n        var match = regex.exec(ua);\n        if (match) {\n            return os;\n        }\n    }\n    return null;\n}\nexport function getNodeVersion() {\n    var isNode = typeof process !== 'undefined' && process.version;\n    return isNode ? new NodeInfo(process.version.slice(1)) : null;\n}\nfunction createVersionParts(count) {\n    var output = [];\n    for (var ii = 0; ii < count; ii++) {\n        output.push('0');\n    }\n    return output;\n}\n", null, null, "'use strict';\nmodule.exports = str => encodeURIComponent(str).replace(/[!'()*]/g, x => `%${x.charCodeAt(0).toString(16).toUpperCase()}`);\n", "'use strict';\nvar token = '%[a-f0-9]{2}';\nvar singleMatcher = new RegExp('(' + token + ')|([^%]+?)', 'gi');\nvar multiMatcher = new RegExp('(' + token + ')+', 'gi');\n\nfunction decodeComponents(components, split) {\n\ttry {\n\t\t// Try to decode the entire string first\n\t\treturn [decodeURIComponent(components.join(''))];\n\t} catch (err) {\n\t\t// Do nothing\n\t}\n\n\tif (components.length === 1) {\n\t\treturn components;\n\t}\n\n\tsplit = split || 1;\n\n\t// Split the array in 2 parts\n\tvar left = components.slice(0, split);\n\tvar right = components.slice(split);\n\n\treturn Array.prototype.concat.call([], decodeComponents(left), decodeComponents(right));\n}\n\nfunction decode(input) {\n\ttry {\n\t\treturn decodeURIComponent(input);\n\t} catch (err) {\n\t\tvar tokens = input.match(singleMatcher) || [];\n\n\t\tfor (var i = 1; i < tokens.length; i++) {\n\t\t\tinput = decodeComponents(tokens, i).join('');\n\n\t\t\ttokens = input.match(singleMatcher) || [];\n\t\t}\n\n\t\treturn input;\n\t}\n}\n\nfunction customDecodeURIComponent(input) {\n\t// Keep track of all the replacements and prefill the map with the `BOM`\n\tvar replaceMap = {\n\t\t'%FE%FF': '\\uFFFD\\uFFFD',\n\t\t'%FF%FE': '\\uFFFD\\uFFFD'\n\t};\n\n\tvar match = multiMatcher.exec(input);\n\twhile (match) {\n\t\ttry {\n\t\t\t// Decode as big chunks as possible\n\t\t\treplaceMap[match[0]] = decodeURIComponent(match[0]);\n\t\t} catch (err) {\n\t\t\tvar result = decode(match[0]);\n\n\t\t\tif (result !== match[0]) {\n\t\t\t\treplaceMap[match[0]] = result;\n\t\t\t}\n\t\t}\n\n\t\tmatch = multiMatcher.exec(input);\n\t}\n\n\t// Add `%C2` at the end of the map to make sure it does not replace the combinator before everything else\n\treplaceMap['%C2'] = '\\uFFFD';\n\n\tvar entries = Object.keys(replaceMap);\n\n\tfor (var i = 0; i < entries.length; i++) {\n\t\t// Replace all decoded components\n\t\tvar key = entries[i];\n\t\tinput = input.replace(new RegExp(key, 'g'), replaceMap[key]);\n\t}\n\n\treturn input;\n}\n\nmodule.exports = function (encodedURI) {\n\tif (typeof encodedURI !== 'string') {\n\t\tthrow new TypeError('Expected `encodedURI` to be of type `string`, got `' + typeof encodedURI + '`');\n\t}\n\n\ttry {\n\t\tencodedURI = encodedURI.replace(/\\+/g, ' ');\n\n\t\t// Try the built in decoder first\n\t\treturn decodeURIComponent(encodedURI);\n\t} catch (err) {\n\t\t// Fallback to a more advanced decoder\n\t\treturn customDecodeURIComponent(encodedURI);\n\t}\n};\n", "'use strict';\n\nmodule.exports = (string, separator) => {\n\tif (!(typeof string === 'string' && typeof separator === 'string')) {\n\t\tthrow new TypeError('Expected the arguments to be of type `string`');\n\t}\n\n\tif (separator === '') {\n\t\treturn [string];\n\t}\n\n\tconst separatorIndex = string.indexOf(separator);\n\n\tif (separatorIndex === -1) {\n\t\treturn [string];\n\t}\n\n\treturn [\n\t\tstring.slice(0, separatorIndex),\n\t\tstring.slice(separatorIndex + separator.length)\n\t];\n};\n", "'use strict';\nmodule.exports = function (obj, predicate) {\n\tvar ret = {};\n\tvar keys = Object.keys(obj);\n\tvar isArr = Array.isArray(predicate);\n\n\tfor (var i = 0; i < keys.length; i++) {\n\t\tvar key = keys[i];\n\t\tvar val = obj[key];\n\n\t\tif (isArr ? predicate.indexOf(key) !== -1 : predicate(key, val, obj)) {\n\t\t\tret[key] = val;\n\t\t}\n\t}\n\n\treturn ret;\n};\n"], "mappings": ";;;;;;AAqIO,SAAS,OAAO,WAAW;AAC9B,MAAI,CAAC,CAAC,WAAW;AACb,WAAO,eAAe,SAAS;AAAA,EACnC;AACA,MAAI,OAAO,aAAa,eACpB,OAAO,cAAc,eACrB,UAAU,YAAY,eAAe;AACrC,WAAO,IAAI,gBAAgB;AAAA,EAC/B;AACA,MAAI,OAAO,cAAc,aAAa;AAClC,WAAO,eAAe,UAAU,SAAS;AAAA,EAC7C;AACA,SAAO,eAAe;AAC1B;AACA,SAAS,eAAe,IAAI;AAKxB,SAAQ,OAAO,MACX,eAAe,OAAO,SAAU,SAAS,IAAI;AACzC,QAAI,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AACjC,QAAI,SAAS;AACT,aAAO;AAAA,IACX;AACA,QAAI,UAAU,MAAM,KAAK,EAAE;AAC3B,WAAO,CAAC,CAAC,WAAW,CAAC,SAAS,OAAO;AAAA,EACzC,GAAG,KAAK;AAChB;AAKO,SAAS,eAAe,IAAI;AAC/B,MAAI,cAAc,eAAe,EAAE;AACnC,MAAI,CAAC,aAAa;AACd,WAAO;AAAA,EACX;AACA,MAAI,OAAO,YAAY,CAAC,GAAG,QAAQ,YAAY,CAAC;AAChD,MAAI,SAAS,aAAa;AACtB,WAAO,IAAI,QAAQ;AAAA,EACvB;AAEA,MAAI,eAAe,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,GAAG,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG,CAAC;AAClF,MAAI,cAAc;AACd,QAAI,aAAa,SAAS,wBAAwB;AAC9C,qBAAe,cAAc,cAAc,CAAC,GAAG,cAAc,IAAI,GAAG,mBAAmB,yBAAyB,aAAa,MAAM,GAAG,IAAI;AAAA,IAC9I;AAAA,EACJ,OACK;AACD,mBAAe,CAAC;AAAA,EACpB;AACA,MAAI,UAAU,aAAa,KAAK,GAAG;AACnC,MAAI,KAAK,SAAS,EAAE;AACpB,MAAI,iBAAiB,mBAAmB,KAAK,EAAE;AAC/C,MAAI,kBAAkB,eAAe,CAAC,GAAG;AACrC,WAAO,IAAI,oBAAoB,MAAM,SAAS,IAAI,eAAe,CAAC,CAAC;AAAA,EACvE;AACA,SAAO,IAAI,YAAY,MAAM,SAAS,EAAE;AAC5C;AACO,SAAS,SAAS,IAAI;AACzB,WAAS,KAAK,GAAG,QAAQ,qBAAqB,QAAQ,KAAK,OAAO,MAAM;AACpE,QAAI,KAAK,qBAAqB,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAC3D,QAAI,QAAQ,MAAM,KAAK,EAAE;AACzB,QAAI,OAAO;AACP,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,iBAAiB;AAC7B,MAAI,SAAS,OAAO,YAAY,eAAe,QAAQ;AACvD,SAAO,SAAS,IAAI,SAAS,QAAQ,QAAQ,MAAM,CAAC,CAAC,IAAI;AAC7D;AACA,SAAS,mBAAmB,OAAO;AAC/B,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,KAAK,OAAO,MAAM;AAC/B,WAAO,KAAK,GAAG;AAAA,EACnB;AACA,SAAO;AACX;AArNA,IAAI,eASA,aAUA,UAUA,qBAWA,SAWA,iBAWA,oBACA,oBACA,wBACA,gBAwCA;AAzGJ;AAAA;AAAA,IAAI,gBAAgD,SAAU,IAAI,MAAM,MAAM;AAC1E,UAAI,QAAQ,UAAU,WAAW;AAAG,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,cAAI,MAAM,EAAE,KAAK,OAAO;AACpB,gBAAI,CAAC;AAAI,mBAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,eAAG,CAAC,IAAI,KAAK,CAAC;AAAA,UAClB;AAAA,QACJ;AACA,aAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AAAA,IAC3D;AACA,IAAI;AAAA,IAA6B,WAAY;AACzC,eAASA,aAAY,MAAM,SAAS,IAAI;AACpC,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,KAAK;AACV,aAAK,OAAO;AAAA,MAChB;AACA,aAAOA;AAAA,IACX,EAAE;AAEF,IAAI;AAAA,IAA0B,WAAY;AACtC,eAASC,UAAS,SAAS;AACvB,aAAK,UAAU;AACf,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,KAAK,QAAQ;AAAA,MACtB;AACA,aAAOA;AAAA,IACX,EAAE;AAEF,IAAI;AAAA,IAAqC,WAAY;AACjD,eAASC,qBAAoB,MAAM,SAAS,IAAI,KAAK;AACjD,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,KAAK;AACV,aAAK,MAAM;AACX,aAAK,OAAO;AAAA,MAChB;AACA,aAAOA;AAAA,IACX,EAAE;AAEF,IAAI;AAAA,IAAyB,WAAY;AACrC,eAASC,WAAU;AACf,aAAK,OAAO;AACZ,aAAK,MAAM;AACX,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,KAAK;AAAA,MACd;AACA,aAAOA;AAAA,IACX,EAAE;AAEF,IAAI;AAAA,IAAiC,WAAY;AAC7C,eAASC,mBAAkB;AACvB,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,KAAK;AAAA,MACd;AACA,aAAOA;AAAA,IACX,EAAE;AAGF,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,yBAAyB;AAC7B,IAAI,iBAAiB;AAAA,MACjB,CAAC,OAAO,wBAAwB;AAAA,MAChC,CAAC,QAAQ,mBAAmB;AAAA,MAC5B,CAAC,YAAY,qBAAqB;AAAA,MAClC,CAAC,iBAAiB,wBAAwB;AAAA,MAC1C,CAAC,aAAa,uBAAuB;AAAA,MACrC,CAAC,WAAW,4BAA4B;AAAA,MACxC,CAAC,QAAQ,uBAAuB;AAAA,MAChC,CAAC,QAAQ,0BAA0B;AAAA,MACnC,CAAC,UAAU,2BAA2B;AAAA,MACtC,CAAC,iBAAiB,mBAAmB;AAAA,MACrC;AAAA,QACI;AAAA,QACA;AAAA,MACJ;AAAA,MACA,CAAC,UAAU,kDAAkD;AAAA,MAC7D,CAAC,aAAa,+BAA+B;AAAA,MAC7C,CAAC,SAAS,2BAA2B;AAAA,MACrC,CAAC,WAAW,6BAA6B;AAAA,MACzC,CAAC,SAAS,mBAAmB;AAAA,MAC7B,CAAC,cAAc,iCAAiC;AAAA,MAChD,CAAC,SAAS,2BAA2B;AAAA,MACrC,CAAC,SAAS,yBAAyB;AAAA,MACnC,CAAC,OAAO,kDAAkD;AAAA,MAC1D,CAAC,OAAO,+FAA+F;AAAA,MACvG,CAAC,YAAY,sCAAsC;AAAA,MACnD,CAAC,MAAM,2CAA2C;AAAA,MAClD,CAAC,MAAM,qCAAqC;AAAA,MAC5C,CAAC,MAAM,cAAc;AAAA,MACrB,CAAC,QAAQ,mCAAmC;AAAA,MAC5C,CAAC,WAAW,qBAAqB;AAAA,MACjC,CAAC,OAAO,wCAAwC;AAAA,MAChD,CAAC,UAAU,8BAA8B;AAAA,MACzC,CAAC,YAAY,qBAAqB;AAAA,MAClC,CAAC,aAAa,uBAAuB;AAAA,MACrC,CAAC,eAAe,iCAAiC;AAAA,MACjD,CAAC,eAAe,mCAAmC;AAAA,MACnD,CAAC,QAAQ,oBAAoB;AAAA,MAC7B,CAAC,aAAa,kBAAkB;AAAA,IACpC;AACA,IAAI,uBAAuB;AAAA,MACvB,CAAC,OAAO,gBAAgB;AAAA,MACxB,CAAC,cAAc,SAAS;AAAA,MACxB,CAAC,iBAAiB,iBAAiB;AAAA,MACnC,CAAC,kBAAkB,UAAU;AAAA,MAC7B,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,gBAAgB,OAAO;AAAA,MACxB,CAAC,cAAc,mCAAmC;AAAA,MAClD,CAAC,cAAc,sBAAsB;AAAA,MACrC,CAAC,gBAAgB,iCAAiC;AAAA,MAClD,CAAC,cAAc,+BAA+B;AAAA,MAC9C,CAAC,uBAAuB,kBAAkB;AAAA,MAC1C,CAAC,iBAAiB,kBAAkB;AAAA,MACpC,CAAC,aAAa,kBAAkB;AAAA,MAChC,CAAC,aAAa,kBAAkB;AAAA,MAChC,CAAC,eAAe,kBAAkB;AAAA,MAClC,CAAC,cAAc,mBAAmB;AAAA,MAClC,CAAC,cAAc,YAAY;AAAA,MAC3B,CAAC,cAAc,qDAAqD;AAAA,MACpE,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,UAAU,OAAO;AAAA,MAClB,CAAC,aAAa,MAAM;AAAA,MACpB,CAAC,SAAS,eAAe;AAAA,MACzB,CAAC,UAAU,2BAA2B;AAAA,MACtC,CAAC,OAAO,KAAK;AAAA,MACb,CAAC,QAAQ,MAAM;AAAA,MACf,CAAC,QAAQ,OAAO;AAAA,IACpB;AAAA;AAAA;;;;;;;;ACpIA,aAAgB,cAAiB,MAAY;AAC3C,UAAI,MAAqB;AACzB,UAAI,OAAO,WAAW,eAAe,OAAO,OAAO,IAAI,MAAM,aAAa;AACxE,cAAM,OAAO,IAAI;;AAEnB,aAAO;IACT;AANA,YAAA,gBAAA;AAQA,aAAgB,qBAAwB,MAAY;AAClD,YAAM,MAAM,cAAiB,IAAI;AACjC,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,GAAG,IAAI,2BAA2B;;AAEpD,aAAO;IACT;AANA,YAAA,uBAAA;AAQA,aAAgB,qBAAkB;AAChC,aAAO,qBAA+B,UAAU;IAClD;AAFA,YAAA,qBAAA;AAIA,aAAgB,cAAW;AACzB,aAAO,cAAwB,UAAU;IAC3C;AAFA,YAAA,cAAA;AAIA,aAAgB,sBAAmB;AACjC,aAAO,qBAAgC,WAAW;IACpD;AAFA,YAAA,sBAAA;AAIA,aAAgB,eAAY;AAC1B,aAAO,cAAyB,WAAW;IAC7C;AAFA,YAAA,eAAA;AAIA,aAAgB,qBAAkB;AAChC,aAAO,qBAA+B,UAAU;IAClD;AAFA,YAAA,qBAAA;AAIA,aAAgB,cAAW;AACzB,aAAO,cAAwB,UAAU;IAC3C;AAFA,YAAA,cAAA;AAIA,aAAgB,mBAAgB;AAC9B,aAAO,qBAA6B,QAAQ;IAC9C;AAFA,YAAA,mBAAA;AAIA,aAAgB,YAAS;AACvB,aAAO,cAAsB,QAAQ;IACvC;AAFA,YAAA,YAAA;AAIA,aAAgB,yBAAsB;AACpC,aAAO,qBAA8B,cAAc;IACrD;AAFA,YAAA,yBAAA;AAIA,aAAgB,kBAAe;AAC7B,aAAO,cAAuB,cAAc;IAC9C;AAFA,YAAA,kBAAA;;;;;;;;;;ACpDA,QAAA,mBAAA;AAYA,aAAgB,oBAAiB;AAC/B,UAAI;AACJ,UAAI;AAEJ,UAAI;AACF,cAAM,iBAAA,mBAAkB;AACxB,cAAM,iBAAA,mBAAkB;eACjB,GAAG;AACV,eAAO;;AAGT,eAAS,WAAQ;AACf,cAAM,QAA2C,IAAI,qBACnD,MAAM;AAER,cAAMC,SAAkB,CAAA;AAExB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAM,OAAwB,MAAM,CAAC;AAErC,gBAAM,MAAqB,KAAK,aAAa,KAAK;AAClD,cAAI,KAAK;AACP,gBAAI,IAAI,YAAW,EAAG,QAAQ,MAAM,IAAI,IAAI;AAC1C,oBAAM,OAAsB,KAAK,aAAa,MAAM;AAEpD,kBAAI,MAAM;AACR,oBACE,KAAK,YAAW,EAAG,QAAQ,QAAQ,MAAM,MACzC,KAAK,YAAW,EAAG,QAAQ,OAAO,MAAM,MACxC,KAAK,QAAQ,IAAI,MAAM,GACvB;AACA,sBAAI,eAAuB,IAAI,WAAW,OAAO,IAAI;AAErD,sBAAI,KAAK,QAAQ,GAAG,MAAM,GAAG;AAC3B,oCAAgB;yBACX;AACL,0BAAM,OAAiB,IAAI,SAAS,MAAM,GAAG;AAC7C,yBAAK,IAAG;AACR,0BAAM,YAAoB,KAAK,KAAK,GAAG;AACvC,oCAAgB,YAAY,MAAM;;AAGpC,kBAAAA,OAAM,KAAK,YAAY;2BACd,KAAK,QAAQ,IAAI,MAAM,GAAG;AACnC,wBAAM,cAAsB,IAAI,WAAW;AAE3C,kBAAAA,OAAM,KAAK,WAAW;uBACjB;AACL,kBAAAA,OAAM,KAAK,IAAI;;;;;;AAOzB,eAAOA;MACT;AAEA,eAAS,0BAA0B,MAAc;AAC/C,cAAM,WAA8C,IAAI,qBACtD,MAAM;AAGR,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,MAAuB,SAAS,CAAC;AACvC,gBAAM,aAAmC,CAAC,YAAY,YAAY,MAAM,EACrE,IAAI,CAAC,WAAmB,IAAI,aAAa,MAAM,CAAC,EAChD,OAAO,CAAC,SAAuB;AAC9B,gBAAI,MAAM;AACR,qBAAO,KAAK,SAAS,IAAI;;AAE3B,mBAAO;UACT,CAAC;AAEH,cAAI,WAAW,UAAU,YAAY;AACnC,kBAAM,UAAyB,IAAI,aAAa,SAAS;AACzD,gBAAI,SAAS;AACX,qBAAO;;;;AAKb,eAAO;MACT;AAEA,eAAS,UAAO;AACd,YAAIC,QAAe,uBACjB,QACA,gBACA,YACA,eAAe;AAGjB,YAAI,CAACA,OAAM;AACT,UAAAA,QAAO,IAAI;;AAGb,eAAOA;MACT;AAEA,eAAS,iBAAc;AACrB,cAAMC,eAAsB,uBAC1B,eACA,kBACA,uBACA,UAAU;AAGZ,eAAOA;MACT;AAEA,YAAM,OAAe,QAAO;AAC5B,YAAM,cAAsB,eAAc;AAC1C,YAAM,MAAc,IAAI;AACxB,YAAM,QAAkB,SAAQ;AAEhC,YAAM,OAAyB;QAC7B;QACA;QACA;QACA;;AAGF,aAAO;IACT;AA5HA,YAAA,oBAAA;;;;;ACZA;AAAA;AAAA;AACA,WAAO,UAAU,SAAO,mBAAmB,GAAG,EAAE,QAAQ,YAAY,OAAK,IAAI,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY,CAAC,EAAE;AAAA;AAAA;;;ACDzH;AAAA;AAAA;AACA,QAAI,QAAQ;AACZ,QAAI,gBAAgB,IAAI,OAAO,MAAM,QAAQ,cAAc,IAAI;AAC/D,QAAI,eAAe,IAAI,OAAO,MAAM,QAAQ,MAAM,IAAI;AAEtD,aAAS,iBAAiB,YAAY,OAAO;AAC5C,UAAI;AAEH,eAAO,CAAC,mBAAmB,WAAW,KAAK,EAAE,CAAC,CAAC;AAAA,MAChD,SAAS,KAAK;AAAA,MAEd;AAEA,UAAI,WAAW,WAAW,GAAG;AAC5B,eAAO;AAAA,MACR;AAEA,cAAQ,SAAS;AAGjB,UAAI,OAAO,WAAW,MAAM,GAAG,KAAK;AACpC,UAAI,QAAQ,WAAW,MAAM,KAAK;AAElC,aAAO,MAAM,UAAU,OAAO,KAAK,CAAC,GAAG,iBAAiB,IAAI,GAAG,iBAAiB,KAAK,CAAC;AAAA,IACvF;AAEA,aAAS,OAAO,OAAO;AACtB,UAAI;AACH,eAAO,mBAAmB,KAAK;AAAA,MAChC,SAAS,KAAK;AACb,YAAI,SAAS,MAAM,MAAM,aAAa,KAAK,CAAC;AAE5C,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,kBAAQ,iBAAiB,QAAQ,CAAC,EAAE,KAAK,EAAE;AAE3C,mBAAS,MAAM,MAAM,aAAa,KAAK,CAAC;AAAA,QACzC;AAEA,eAAO;AAAA,MACR;AAAA,IACD;AAEA,aAAS,yBAAyB,OAAO;AAExC,UAAI,aAAa;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAEA,UAAI,QAAQ,aAAa,KAAK,KAAK;AACnC,aAAO,OAAO;AACb,YAAI;AAEH,qBAAW,MAAM,CAAC,CAAC,IAAI,mBAAmB,MAAM,CAAC,CAAC;AAAA,QACnD,SAAS,KAAK;AACb,cAAI,SAAS,OAAO,MAAM,CAAC,CAAC;AAE5B,cAAI,WAAW,MAAM,CAAC,GAAG;AACxB,uBAAW,MAAM,CAAC,CAAC,IAAI;AAAA,UACxB;AAAA,QACD;AAEA,gBAAQ,aAAa,KAAK,KAAK;AAAA,MAChC;AAGA,iBAAW,KAAK,IAAI;AAEpB,UAAI,UAAU,OAAO,KAAK,UAAU;AAEpC,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAExC,YAAI,MAAM,QAAQ,CAAC;AACnB,gBAAQ,MAAM,QAAQ,IAAI,OAAO,KAAK,GAAG,GAAG,WAAW,GAAG,CAAC;AAAA,MAC5D;AAEA,aAAO;AAAA,IACR;AAEA,WAAO,UAAU,SAAU,YAAY;AACtC,UAAI,OAAO,eAAe,UAAU;AACnC,cAAM,IAAI,UAAU,wDAAwD,OAAO,aAAa,GAAG;AAAA,MACpG;AAEA,UAAI;AACH,qBAAa,WAAW,QAAQ,OAAO,GAAG;AAG1C,eAAO,mBAAmB,UAAU;AAAA,MACrC,SAAS,KAAK;AAEb,eAAO,yBAAyB,UAAU;AAAA,MAC3C;AAAA,IACD;AAAA;AAAA;;;AC7FA;AAAA;AAAA;AAEA,WAAO,UAAU,CAAC,QAAQ,cAAc;AACvC,UAAI,EAAE,OAAO,WAAW,YAAY,OAAO,cAAc,WAAW;AACnE,cAAM,IAAI,UAAU,+CAA+C;AAAA,MACpE;AAEA,UAAI,cAAc,IAAI;AACrB,eAAO,CAAC,MAAM;AAAA,MACf;AAEA,YAAM,iBAAiB,OAAO,QAAQ,SAAS;AAE/C,UAAI,mBAAmB,IAAI;AAC1B,eAAO,CAAC,MAAM;AAAA,MACf;AAEA,aAAO;AAAA,QACN,OAAO,MAAM,GAAG,cAAc;AAAA,QAC9B,OAAO,MAAM,iBAAiB,UAAU,MAAM;AAAA,MAC/C;AAAA,IACD;AAAA;AAAA;;;ACrBA;AAAA;AAAA;AACA,WAAO,UAAU,SAAU,KAAK,WAAW;AAC1C,UAAI,MAAM,CAAC;AACX,UAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,UAAI,QAAQ,MAAM,QAAQ,SAAS;AAEnC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,YAAI,MAAM,KAAK,CAAC;AAChB,YAAI,MAAM,IAAI,GAAG;AAEjB,YAAI,QAAQ,UAAU,QAAQ,GAAG,MAAM,KAAK,UAAU,KAAK,KAAK,GAAG,GAAG;AACrE,cAAI,GAAG,IAAI;AAAA,QACZ;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;", "names": ["BrowserInfo", "NodeInfo", "SearchBotDeviceInfo", "BotInfo", "ReactNativeInfo", "icons", "name", "description"]}