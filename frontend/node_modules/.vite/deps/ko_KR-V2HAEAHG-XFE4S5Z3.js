"use client";
import "./chunk-W7S2ME4R.js";

// node_modules/@rainbow-me/rainbowkit/dist/ko_KR-V2HAEAHG.js
var ko_KR_default = '{\n  "connect_wallet": {\n    "label": "지갑 연결",\n    "wrong_network": {\n      "label": "잘못된 네트워크"\n    }\n  },\n  "intro": {\n    "title": "지갑이란 무엇인가요?",\n    "description": "지갑은 디지털 자산을 보내고, 받고, 저장하고, 표시하는 데 사용됩니다. 또한, 모든 웹 사이트에서 새 계정과 비밀번호를 생성할 필요 없이 로그인하는 새로운 방법입니다.",\n    "digital_asset": {\n      "title": "당신의 디지털 자산을 위한 집",\n      "description": "지갑은 이더리움 및 NFT와 같은 디지털 자산을 보내고, 받고, 저장하고, 표시하는데 사용됩니다."\n    },\n    "login": {\n      "title": "새로운 로그인 방식",\n      "description": "모든 웹사이트에서 새 계정과 비밀번호를 생성하는 대신, 당신의 지갑을 연결하기만 하면 됩니다."\n    },\n    "get": {\n      "label": "지갑 가져오기"\n    },\n    "learn_more": {\n      "label": "더 알아보기"\n    }\n  },\n  "sign_in": {\n    "label": "계정을 확인하세요",\n    "description": "연결을 완료하려면 이 계정의 소유자임을 확인하기 위해 지갑에 메시지에 서명해야 합니다.",\n    "message": {\n      "send": "메시지 보내기",\n      "preparing": "메시지 준비 중...",\n      "cancel": "취소",\n      "preparing_error": "메시지 준비 중 오류가 발생했습니다. 다시 시도하세요!"\n    },\n    "signature": {\n      "waiting": "서명을 기다리는 중...",\n      "verifying": "서명 검증 중...",\n      "signing_error": "메시지 서명 중 오류가 발생했습니다. 다시 시도하세요!",\n      "verifying_error": "서명 검증 중 오류가 발생했습니다. 다시 시도하세요!",\n      "oops_error": "앗, 문제가 발생했습니다!"\n    }\n  },\n  "connect": {\n    "label": "연결",\n    "title": "지갑 연결",\n    "new_to_ethereum": {\n      "description": "이더리움 지갑에 처음 접하시나요?",\n      "learn_more": {\n        "label": "더 알아보기"\n      }\n    },\n    "learn_more": {\n      "label": "더 알아보기"\n    },\n    "recent": "최근",\n    "status": {\n      "opening": "%{wallet}열기 ...",\n      "connecting": "연결 중",\n      "connect_mobile": "%{wallet}에서 계속 진행",\n      "not_installed": "%{wallet} 가 설치되어 있지 않습니다",\n      "not_available": "%{wallet} 를 사용할 수 없습니다",\n      "confirm": "확장 프로그램에서 연결을 확인하세요",\n      "confirm_mobile": "지갑에서 연결 요청을 수락하십시오"\n    },\n    "secondary_action": {\n      "get": {\n        "description": "%{wallet}가 없나요?",\n        "label": "GET"\n      },\n      "install": {\n        "label": "설치"\n      },\n      "retry": {\n        "label": "다시 시도"\n      }\n    },\n    "walletconnect": {\n      "description": {\n        "full": "공식 WalletConnect 모달이 필요한가요?",\n        "compact": "WalletConnect 모달이 필요한가요?"\n      },\n      "open": {\n        "label": "열기"\n      }\n    }\n  },\n  "connect_scan": {\n    "title": "%{wallet}로 스캔하기",\n    "fallback_title": "휴대폰으로 스캔하기"\n  },\n  "connector_group": {\n    "recommended": "추천",\n    "other": "기타",\n    "popular": "인기",\n    "more": "더 보기",\n    "others": "다른 지갑들"\n  },\n  "get": {\n    "title": "월렛 받기",\n    "action": {\n      "label": "받기"\n    },\n    "mobile": {\n      "description": "모바일 월렛"\n    },\n    "extension": {\n      "description": "브라우저 확장 프로그램"\n    },\n    "mobile_and_extension": {\n      "description": "모바일 지갑 및 확장 프로그램"\n    },\n    "mobile_and_desktop": {\n      "description": "모바일 및 데스크톱 지갑"\n    },\n    "looking_for": {\n      "title": "찾고 계신 것이 아닌가요?",\n      "mobile": {\n        "description": "메인 화면에서 다른 지갑 제공자를 사용하기 위해 지갑을 선택하세요."\n      },\n      "desktop": {\n        "compact_description": "메인 화면에서 다른 지갑 제공자를 사용하기 위해 지갑을 선택하세요.",\n        "wide_description": "왼쪽에서 지갑을 선택하여 다른 지갑 제공자를 사용하기 시작하세요."\n      }\n    }\n  },\n  "get_options": {\n    "title": "%{wallet}로 시작하세요",\n    "short_title": "%{wallet}얻기",\n    "mobile": {\n      "title": "모바일용 %{wallet}",\n      "description": "모바일 지갑으로 이더리움 세계를 탐험하세요.",\n      "download": {\n        "label": "앱 받기"\n      }\n    },\n    "extension": {\n      "title": "%{browser}용 %{wallet}",\n      "description": "가장 좋아하는 웹 브라우저에서 바로 지갑에 접근하세요.",\n      "download": {\n        "label": "추가하기 %{browser}"\n      }\n    },\n    "desktop": {\n      "title": "%{wallet} 용 %{platform}",\n      "description": "강력한 데스크톱에서 네이티브로 지갑에 접근하세요.",\n      "download": {\n        "label": "%{platform}에 추가"\n      }\n    }\n  },\n  "get_mobile": {\n    "title": "설치하기 %{wallet}",\n    "description": "iOS 또는 Android에서 다운로드하기 위해 휴대폰으로 스캔하세요",\n    "continue": {\n      "label": "계속"\n    }\n  },\n  "get_instructions": {\n    "mobile": {\n      "connect": {\n        "label": "연결"\n      },\n      "learn_more": {\n        "label": "더 알아보기"\n      }\n    },\n    "extension": {\n      "refresh": {\n        "label": "새로고침"\n      },\n      "learn_more": {\n        "label": "더 알아보기"\n      }\n    },\n    "desktop": {\n      "connect": {\n        "label": "연결"\n      },\n      "learn_more": {\n        "label": "더 알아보기"\n      }\n    }\n  },\n  "chains": {\n    "title": "네트워크 전환",\n    "wrong_network": "잘못된 네트워크를 탐지했습니다, 계속하려면 전환하거나 연결을 해제하세요.",\n    "confirm": "지갑에서 승인",\n    "switching_not_supported": "지갑에서 %{appName}네트워크를 전환하는 것은 지원되지 않습니다. 대신 지갑 내에서 네트워크를 전환해 보세요.",\n    "switching_not_supported_fallback": "당신의 지갑은 이 앱에서 네트워크를 바꾸는 것을 지원하지 않습니다. 대신 지갑 내에서 네트워크를 변경해 보세요.",\n    "disconnect": "연결 해제",\n    "connected": "연결됨"\n  },\n  "profile": {\n    "disconnect": {\n      "label": "연결 해제"\n    },\n    "copy_address": {\n      "label": "주소 복사",\n      "copied": "복사됨!"\n    },\n    "explorer": {\n      "label": "탐색기에서 더 보기"\n    },\n    "transactions": {\n      "description": "%{appName} 거래가 여기에 나타납니다...",\n      "description_fallback": "여기에 트랜잭션이 표시됩니다...",\n      "recent": {\n        "title": "최근 거래 내역"\n      },\n      "clear": {\n        "label": "모두 지우기"\n      }\n    }\n  },\n  "wallet_connectors": {\n    "argent": {\n      "qr_code": {\n        "step1": {\n          "description": "지갑에 더 빠르게 액세스하려면 Argent를 홈 화면에 놓으세요.",\n          "title": "Argent 앱을 열기"\n        },\n        "step2": {\n          "description": "지갑과 사용자 이름을 생성하거나 기존의 지갑을 가져옵니다.",\n          "title": "지갑 생성 또는 가져오기"\n        },\n        "step3": {\n          "description": "스캔 후에 지갑을 연결하기 위한 연결 요청이 표시됩니다.",\n          "title": "QR 코드 스캔 버튼을 누르기"\n        }\n      }\n    },\n    "bifrost": {\n      "qr_code": {\n        "step1": {\n          "description": "더 빠른 접근을 위해 홈 화면에 Bifrost Wallet을 놓는 것을 권장합니다.",\n          "title": "Bifrost 지갑 앱을 열어주세요"\n        },\n        "step2": {\n          "description": "복구 문구를 사용하여 지갑을 생성하거나 가져옵니다.",\n          "title": "지갑 생성 또는 가져오기"\n        },\n        "step3": {\n          "description": "스캔 후 연결 프롬프트가 나타나고 지갑을 연결할 수 있습니다.",\n          "title": "스캔 버튼을 누릅니다"\n        }\n      }\n    },\n    "bitget": {\n      "qr_code": {\n        "step1": {\n          "description": "더 빠른 접근을 위해 Bitget 지갑을 홈 화면에 두는 것을 권장합니다.",\n          "title": "Bitget 지갑 앱을 여세요"\n        },\n        "step2": {\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 비밀 구문을 누구와도 공유하지 마세요.",\n          "title": "지갑 생성 또는 가져오기"\n        },\n        "step3": {\n          "description": "스캔 후, 지갑을 연결하라는 연결 요청 메시지가 나타납니다.",\n          "title": "스캔 버튼을 누르세요"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "지갑에 빠르게 액세스하기 위해 Bitget Wallet을 작업 표시줄에 고정하는 것을 권장합니다.",\n          "title": "Bitget Wallet 확장 프로그램을 설치하세요"\n        },\n        "step2": {\n          "description": "지갑을 안전한 방법으로 백업하세요. 절대로 비밀 문구를 누구와도 공유하지 마세요.",\n          "title": "지갑 생성 또는 가져오기"\n        },\n        "step3": {\n          "description": "지갑 설정을 마친 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요.",\n          "title": "브라우저를 새로 고침하세요"\n        }\n      }\n    },\n    "bitski": {\n      "extension": {\n        "step1": {\n          "description": "지갑에 더 빠르게 액세스하기 위해 Bitski를 작업 표시줄에 고정하는 것을 권장합니다.",\n          "title": "Bitski 확장 프로그램을 설치합니다"\n        },\n        "step2": {\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 비밀 문구를 누구와도 공유하지 마세요.",\n          "title": "지갑 만들기 또는 가져오기"\n        },\n        "step3": {\n          "description": "지갑을 설정한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요.",\n          "title": "브라우저를 새로고침하세요"\n        }\n      }\n    },\n    "coin98": {\n      "qr_code": {\n        "step1": {\n          "description": "지갑에 빠르게 액세스하기 위해 Coin98 Wallet을 홈 화면에 두는 것을 권장합니다.",\n          "title": "Coin98 Wallet 앱을 열기"\n        },\n        "step2": {\n          "description": "휴대폰에서 백업 기능을 이용하여 지갑을 쉽게 백업할 수 있습니다.",\n          "title": "지갑 만들기 또는 가져오기"\n        },\n        "step3": {\n          "description": "스캔한 후 연결 프롬프트가 나타나 지갑을 연결하도록 합니다.",\n          "title": "WalletConnect 버튼을 누르세요"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "브라우저 오른쪽 상단을 클릭하고 쉽게 액세스할 수 있도록 Coin98 Wallet을 고정하세요.",\n          "title": "Coin98 Wallet 확장 프로그램을 설치하세요"\n        },\n        "step2": {\n          "description": "새로운 지갑을 만들거나 기존의 지갑을 가져옵니다.",\n          "title": "지갑을 만들거나 가져옵니다"\n        },\n        "step3": {\n          "description": "Coin98 Wallet을 설정하면 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요.",\n          "title": "브라우저를 새로고침 하세요"\n        }\n      }\n    },\n    "coinbase": {\n      "qr_code": {\n        "step1": {\n          "description": "더 빠른 액세스를 위해 Coinbase Wallet을 홈 화면에 두는 것을 권장합니다.",\n          "title": "Coinbase Wallet 앱을 엽니다"\n        },\n        "step2": {\n          "description": "클라우드 백업 기능을 사용하여 지갑을 쉽게 백업할 수 있습니다.",\n          "title": "지갑 생성 또는 가져오기"\n        },\n        "step3": {\n          "description": "스캔한 후에 지갑을 연결하라는 연결 프롬프트가 나타납니다.",\n          "title": "스캔 버튼을 탭하세요"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "지갑에 더 빠르게 접근할 수 있도록 Coinbase Wallet을 작업 표시줄에 고정하는 것을 권장합니다.",\n          "title": "Coinbase Wallet 확장 프로그램을 설치하세요"\n        },\n        "step2": {\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 비밀 문구는 절대로 누구와도 공유하지 마세요.",\n          "title": "지갑 만들기 또는 가져오기"\n        },\n        "step3": {\n          "description": "지갑을 설정한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요.",\n          "title": "브라우저 새로고침"\n        }\n      }\n    },\n    "core": {\n      "qr_code": {\n        "step1": {\n          "description": "지갑에 빠르게 액세스할 수 있도록 Core를 홈 화면에 두는 것을 추천드립니다.",\n          "title": "Core 앱 열기"\n        },\n        "step2": {\n          "description": "휴대폰에서 우리의 백업 기능을 이용해 지갑을 쉽게 백업할 수 있습니다.",\n          "title": "지갑 만들기 또는 가져오기"\n        },\n        "step3": {\n          "description": "스캔 한 후에는 지갑을 연결하라는 연결 요청이 표시됩니다.",\n          "title": "WalletConnect 버튼을 누르세요"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "지갑에 더 빠르게 액세스하기 위해 작업 표시줄에 Core를 고정하는 것을 권장합니다.",\n          "title": "Core 확장 프로그램을 설치하세요"\n        },\n        "step2": {\n          "description": "안전한 방법을 사용하여 지갑을 백업해야 합니다. 절대로 비밀 문구를 다른 사람과 공유하지 마세요.",\n          "title": "지갑 만들기 또는 가져오기"\n        },\n        "step3": {\n          "description": "지갑을 설정한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요.",\n          "title": "브라우저를 새로 고치세요"\n        }\n      }\n    },\n    "fox": {\n      "qr_code": {\n        "step1": {\n          "description": "FoxWallet을 홈 화면에 놓는 것을 추천합니다. 이렇게 하면 더 빠르게 접근할 수 있습니다.",\n          "title": "FoxWallet 앱을 열어주세요"\n        },\n        "step2": {\n          "description": "지갑을 안전한 방법으로 백업하세요. 절대로 비밀 문구를 다른 사람과 공유하지 마세요.",\n          "title": "지갑을 생성하거나 가져오기"\n        },\n        "step3": {\n          "description": "스캔 후, 지갑을 연결하라는 연결 프롬프트가 표시됩니다.",\n          "title": "스캔 버튼을 누르세요"\n        }\n      }\n    },\n    "frontier": {\n      "qr_code": {\n        "step1": {\n          "description": "Frontier Wallet을 홈 화면에 놓는 것을 추천합니다. 이렇게 하면 더 빠르게 접근할 수 있습니다.",\n          "title": "Frontier Wallet 앱을 열어주세요"\n        },\n        "step2": {\n          "description": "지갑을 안전한 방법으로 백업해야 합니다. 비밀 구문을 누구와도 공유하지 마세요.",\n          "title": "지갑 생성 또는 가져오기"\n        },\n        "step3": {\n          "description": "스캔 후에 지갑을 연결하라는 연결 프롬프트가 표시됩니다.",\n          "title": "스캔 버튼을 누르세요"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "지갑에 더 빠르게 액세스 할 수 있도록 Frontier Wallet을 작업 표시줄에 고정하는 것을 권장합니다.",\n          "title": "Frontier Wallet 확장 프로그램 설치"\n        },\n        "step2": {\n          "description": "지갑을 안전한 방법으로 백업해야 합니다. 비밀 구문을 누구와도 공유하지 마세요.",\n          "title": "지갑 생성 또는 가져오기"\n        },\n        "step3": {\n          "description": "지갑을 설정한 후에 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요.",\n          "title": "브라우저를 새로 고칩니다"\n        }\n      }\n    },\n    "im_token": {\n      "qr_code": {\n        "step1": {\n          "title": "imToken 앱을 연다",\n          "description": "당신의 지갑에 더 빠르게 접근하기 위해 imToken 앱을 홈 화면에 둡니다."\n        },\n        "step2": {\n          "title": "지갑을 만들거나 불러옵니다",\n          "description": "새 지갑을 생성하거나 기존의 것을 가져옵니다."\n        },\n        "step3": {\n          "title": "오른쪽 상단의 스캐너 아이콘을 누릅니다",\n          "description": "새 연결을 선택하고 QR 코드를 스캔한 뒤, 연결하려는 프롬프트를 확인합니다."\n        }\n      }\n    },\n    "metamask": {\n      "qr_code": {\n        "step1": {\n          "title": "MetaMask 앱을 엽니다",\n          "description": "빠른 액세스를 위해 MetaMask를 홈 화면에 두는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "당신의 지갑을 안전한 방법으로 백업하는 것을 잊지 마세요. 절대로 비밀 구절을 공유하지 마세요."\n        },\n        "step3": {\n          "title": "스캔 버튼을 누릅니다",\n          "description": "스캔한 후에 지갑을 연결하라는 연결 프롬프트가 나타납니다."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "MetaMask 확장 프로그램을 설치하세요",\n          "description": "지갑에 빠르게 접근하기 위해 MetaMask를 작업표시줄에 고정하는 것을 추천합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 결코 비밀 문구를 다른 사람과 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로 고치세요",\n          "description": "지갑 설정을 마친 후에는 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      }\n    },\n    "okx": {\n      "qr_code": {\n        "step1": {\n          "title": "OKX Wallet 앱을 열기",\n          "description": "더 빠른 접근을 위해 OKX 지갑을 홈 화면에 두는 것을 추천합니다."\n        },\n        "step2": {\n          "title": "지갑 만들기 또는 불러오기",\n          "description": "안전한 방법으로 지갑을 백업하세요. 절대 비밀 문구를 다른 사람과 공유하지 마세요."\n        },\n        "step3": {\n          "title": "스캔 버튼을 탭하세요",\n          "description": "스캔 후 연결 요청이 나타나며, 이를 통해 지갑을 연결할 수 있습니다."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "OKX 지갑 확장 프로그램 설치하기",\n          "description": "지갑에 빠르게 접근할 수 있도록 OKX 지갑을 작업 표시줄에 고정하는 것을 추천합니다."\n        },\n        "step2": {\n          "title": "지갑 만들기 또는 불러오기",\n          "description": "당신의 지갑을 안전한 방법으로 백업해야 합니다. 비밀 문구를 절대로 다른 사람과 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로 고치세요",\n          "description": "지갑을 설정한 후, 브라우저를 새로고침하고 확장 프로그램을 로드하기 위해 아래를 클릭하세요."\n        }\n      }\n    },\n    "omni": {\n      "qr_code": {\n        "step1": {\n          "title": "Omni 앱을 열기",\n          "description": "더 빠른 액세스를 위해 Omni를 홈 스크린에 추가하세요."\n        },\n        "step2": {\n          "title": "지갑 만들기 또는 가져오기",\n          "description": "새로운 지갑을 만들거나 기존의 하나를 가져옵니다."\n        },\n        "step3": {\n          "title": "QR 아이콘을 탭하고 스캔하기",\n          "description": "홈 화면의 QR 아이콘을 탭하고, 코드를 스캔하고 프롬프트를 확인하여 연결하세요."\n        }\n      }\n    },\n    "token_pocket": {\n      "qr_code": {\n        "step1": {\n          "title": "TokenPocket 앱을 열어주세요",\n          "description": "빠른 접근을 위해 홈 화면에 TokenPocket을 추가하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 누구에게도 비밀 문구를 공유하지 마세요."\n        },\n        "step3": {\n          "title": "스캔 버튼을 탭하세요",\n          "description": "스캔 후에 지갑을 연결하라는 프롬프트가 표시됩니다."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "TokenPocket 확장 프로그램을 설치하세요",\n          "description": "지갑에 빠르게 접근하기 위해 TokenPocket를 작업 표시줄에 고정하는 것을 추천합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 비밀 문구를 다른 사람과 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저 새로고침",\n          "description": "지갑을 설정하면 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드합니다."\n        }\n      }\n    },\n    "trust": {\n      "qr_code": {\n        "step1": {\n          "title": "Trust Wallet 앱을 열기",\n          "description": "지갑에 빠르게 접근하기 위해 Trust Wallet을 홈 스크린에 두세요."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "새로운 지갑을 생성하거나 기존의 것을 가져오세요."\n        },\n        "step3": {\n          "title": "설정에서 WalletConnect를 탭하세요",\n          "description": "새 연결을 선택한 다음 QR 코드를 스캔하고, 연결을 확인하는 프롬프트를 확인하세요."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "Trust Wallet 확장 프로그램을 설치하세요",\n          "description": "브라우저의 오른쪽 상단을 클릭하고 Trust Wallet을 고정하여 쉽게 접근하세요."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "새로운 지갑을 생성하거나 기존의 것을 가져오세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로고침하세요",\n          "description": "Trust Wallet을 설정한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드합니다."\n        }\n      }\n    },\n    "uniswap": {\n      "qr_code": {\n        "step1": {\n          "title": "Uniswap 앱을 엽니다",\n          "description": "Uniswap Wallet을 홈 화면에 추가하여 지갑에 더 빠르게 액세스하세요."\n        },\n        "step2": {\n          "title": "지갑을 만들거나 가져오기",\n          "description": "새 지갑을 생성하거나 기존의 것을 가져옵니다."\n        },\n        "step3": {\n          "title": "QR 아이콘을 누르고 스캔하기",\n          "description": "홈화면의 QR 아이콘을 누르고 코드를 스캔하고 프롬프트를 확인하여 연결하세요."\n        }\n      }\n    },\n    "zerion": {\n      "qr_code": {\n        "step1": {\n          "title": "Zerion 앱을 엽니다",\n          "description": "더 빠른 접근을 위해 Zerion을 홈 화면에 두는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 만들기 또는 가져오기",\n          "description": "안전한 방법으로 지갑을 백업하세요. 절대로 비밀 구절을 누군가와 공유하지 마세요."\n        },\n        "step3": {\n          "title": "스캔 버튼을 탭하세요",\n          "description": "스캔 후 연결 프롬프트가 나타나 지갑을 연결하세요."\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "Zerion 확장 프로그램을 설치하세요",\n          "description": "지갑에 더 빠르게 접근할 수 있도록 Zerion을 작업 표시줄에 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 비밀 구문을 절대로 다른 사람과 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로 고치세요",\n          "description": "지갑을 설정한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      }\n    },\n    "rainbow": {\n      "qr_code": {\n        "step1": {\n          "title": "Rainbow 앱 열기",\n          "description": "지갑에 더 빠르게 접근하기 위해 홈 화면에 Rainbow를 두는 것을 추천합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "휴대폰에 있는 백업 기능을 사용하여 지갑을 쉽게 백업할 수 있습니다."\n        },\n        "step3": {\n          "title": "스캔 버튼을 누르세요",\n          "description": "스캔 후, 지갑을 연결하라는 연결 프롬프트가 나타납니다."\n        }\n      }\n    },\n    "enkrypt": {\n      "extension": {\n        "step1": {\n          "description": "지갑에 더 빠르게 접근하기 위해 작업 표시줄에 Enkrypt Wallet를 고정하는 것을 추천합니다.",\n          "title": "Enkrypt Wallet 확장 프로그램을 설치하세요"\n        },\n        "step2": {\n          "description": "지갑을 안전한 방법으로 백업하세요. 절대로 비밀 문구를 다른 사람과 공유하지 마세요.",\n          "title": "지갑 생성 또는 가져오기"\n        },\n        "step3": {\n          "description": "지갑을 설정한 후에는 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요.",\n          "title": "브라우저 새로고침"\n        }\n      }\n    },\n    "frame": {\n      "extension": {\n        "step1": {\n          "description": "지갑에 더 빠르게 접근할 수 있도록 Frame을 작업 표시줄에 고정하는 것을 추천합니다.",\n          "title": "Frame 및 동반 확장 프로그램 설치"\n        },\n        "step2": {\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 비밀 구문을 다른 사람과 공유하지 마세요.",\n          "title": "지갑 생성 또는 가져오기"\n        },\n        "step3": {\n          "description": "지갑을 설정한 후에는 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요.",\n          "title": "브라우저 새로고침"\n        }\n      }\n    },\n    "one_key": {\n      "extension": {\n        "step1": {\n          "title": "OneKey Wallet 확장 프로그램을 설치하세요",\n          "description": "지갑에 빠르게 접근할 수 있도록 OneKey Wallet을 작업 표시줄에 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 불러오기",\n          "description": "지갑을 안전한 방법으로 백업하세요. 절대로 비밀 문구를 다른 사람과 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로고침 하세요",\n          "description": "지갑을 설정한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      }\n    },\n    "phantom": {\n      "extension": {\n        "step1": {\n          "title": "Phantom 확장 프로그램을 설치하세요",\n          "description": "지갑에 더 쉽게 접근할 수 있도록 Phantom을 작업 표시줄에 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 불러오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 누구와도 비밀 복구 구문을 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로고침 하세요",\n          "description": "지갑을 설정한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      }\n    },\n    "rabby": {\n      "extension": {\n        "step1": {\n          "title": "Rabby 확장 프로그램을 설치하세요",\n          "description": "지갑에 더 빠르게 액세스할 수 있도록 Rabby를 작업표시줄에 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 만들기 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 누구와도 비밀 구문을 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로고침 하세요",\n          "description": "지갑 설정을 완료하면 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드합니다."\n        }\n      }\n    },\n    "safeheron": {\n      "extension": {\n        "step1": {\n          "title": "코어 확장 프로그램 설치",\n          "description": "지갑에 빠르게 액세스하기 위해 Safeheron을 작업 표시줄에 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 만들기 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 비밀 문구를 절대 다른 사람과 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저 새로고침",\n          "description": "지갑 설정을 완료하면 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드합니다."\n        }\n      }\n    },\n    "taho": {\n      "extension": {\n        "step1": {\n          "title": "Taho 확장 프로그램 설치",\n          "description": "지갑에 더 빠르게 액세스하기 위해 Taho를 작업 표시줄에 고정하는 것을 추천합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 결코 비밀 문구를 누군가와 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로고침 하세요",\n          "description": "지갑을 설정한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      }\n    },\n    "talisman": {\n      "extension": {\n        "step1": {\n          "title": "탈리스만 확장 프로그램 설치",\n          "description": "지갑에 더 빠르게 접근하기 위해 Talisman을 작업 표시줄에 고정하는 것을 추천합니다."\n        },\n        "step2": {\n          "title": "이더리움 지갑 생성 또는 가져오기",\n          "description": "반드시 안전한 방법을 사용하여 지갑을 백업하세요. 복구 문구를 누구와도 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로고침 하세요",\n          "description": "지갑을 설정 한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      }\n    },\n    "xdefi": {\n      "extension": {\n        "step1": {\n          "title": "XDEFI 지갑 확장 프로그램을 설치하세요",\n          "description": "지갑에 빠르게 액세스하기 위해 작업 표시줄에 XDEFI Wallet을 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑을 만들거나 가져오기",\n          "description": "반드시 안전한 방법을 사용하여 지갑을 백업하세요. 비밀 문구를 누구와도 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로고침 하세요",\n          "description": "지갑을 설정한 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      }\n    },\n    "zeal": {\n      "extension": {\n        "step1": {\n          "title": "Zeal 확장 프로그램을 설치하세요",\n          "description": "월렛에 더 빠르게 액세스할 수 있도록 Zeal을 작업 표시 줄에 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 비밀 구문을 누구와도 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로 고침하세요",\n          "description": "지갑 설정을 마친 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      }\n    },\n    "safepal": {\n      "extension": {\n        "step1": {\n          "title": "SafePal Wallet 확장 프로그램을 설치하세요",\n          "description": "브라우저의 오른쪽 상단에서 클릭하고 SafePal Wallet을 고정하여 쉽게 접근하세요."\n        },\n        "step2": {\n          "title": "지갑을 만들거나 가져옵니다",\n          "description": "새로운 지갑을 만들거나 기존의 지갑을 가져옵니다."\n        },\n        "step3": {\n          "title": "브라우저를 새로 고침하세요",\n          "description": "SafePal Wallet을 설정한 후에는 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "SafePal Wallet 앱을 여세요",\n          "description": "월렛에 빠르게 액세스할 수 있도록 SafePal Wallet을 홈 화면에 두세요."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "새로운 지갑을 만들거나 기존의 지갑을 가져옵니다."\n        },\n        "step3": {\n          "title": "설정에서 WalletConnect를 탭하세요",\n          "description": "새 연결을 선택하고 QR 코드를 스캔한 뒤, 연결하려는 프롬프트를 확인합니다."\n        }\n      }\n    },\n    "desig": {\n      "extension": {\n        "step1": {\n          "title": "Desig 확장 프로그램 설치",\n          "description": "당신의 지갑에 더 쉽게 접근하기 위해 작업 표시줄에 Desig을 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 비밀 구문을 누구와도 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로 고침하세요",\n          "description": "지갑 설정을 마친 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      }\n    },\n    "subwallet": {\n      "extension": {\n        "step1": {\n          "title": "SubWallet 확장 프로그램 설치",\n          "description": "당신의 지갑에 더 빠르게 접근하기 위해 작업 표시줄에 SubWallet을 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "반드시 안전한 방법을 사용하여 지갑을 백업하세요. 복구 문구를 누구와도 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로 고침하세요",\n          "description": "지갑 설정을 마친 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "SubWallet 앱 열기",\n          "description": "더 빠른 접근을 위해 SubWallet을 홈 화면에 두는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 비밀 구문을 누구와도 공유하지 마세요."\n        },\n        "step3": {\n          "title": "스캔 버튼을 누릅니다",\n          "description": "스캔 후에 지갑을 연결하기 위한 연결 요청이 표시됩니다."\n        }\n      }\n    },\n    "clv": {\n      "extension": {\n        "step1": {\n          "title": "CLV Wallet 확장 프로그램 설치",\n          "description": "당신의 지갑에 더 빠르게 접근하기 위해 작업 표시줄에 CLV Wallet을 고정하는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 비밀 구문을 누구와도 공유하지 마세요."\n        },\n        "step3": {\n          "title": "브라우저를 새로 고침하세요",\n          "description": "지갑 설정을 마친 후 아래를 클릭하여 브라우저를 새로고침하고 확장 프로그램을 로드하세요."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "CLV Wallet 앱을 엽니다",\n          "description": "더 빠른 접근을 위해 CLV Wallet을 홈 화면에 놓는 것이 좋습니다."\n        },\n        "step2": {\n          "title": "지갑 생성 또는 가져오기",\n          "description": "안전한 방법을 사용하여 지갑을 백업하세요. 절대로 비밀 구문을 누구와도 공유하지 마세요."\n        },\n        "step3": {\n          "title": "스캔 버튼을 누릅니다",\n          "description": "스캔 후에 지갑을 연결하기 위한 연결 요청이 표시됩니다."\n        }\n      }\n    },\n    "okto": {\n      "qr_code": {\n        "step1": {\n          "title": "Okto 앱을 엽니다",\n          "description": "빠른 접근을 위해 Okto를 홈 화면에 추가합니다"\n        },\n        "step2": {\n          "title": "MPC Wallet을 만듭니다",\n          "description": "계정을 만들고 지갑을 생성합니다"\n        },\n        "step3": {\n          "title": "설정에서 WalletConnect를 탭하세요",\n          "description": "오른쪽 상단의 QR 아이콘을 탭하고 연결하려면 알림을 확인합니다."\n        }\n      }\n    },\n    "ledger": {\n      "desktop": {\n        "step1": {\n          "title": "Ledger Live 앱을 엽니다",\n          "description": "빠른 접근을 위해 Ledger Live를 홈화면에 두는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "Ledger 설정",\n          "description": "새 Ledger를 설정하거나 기존 Ledger에 연결하세요."\n        },\n        "step3": {\n          "title": "연결",\n          "description": "스캔 후 연결 요청이 나타나며, 이를 통해 지갑을 연결할 수 있습니다."\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "Ledger Live 앱을 엽니다",\n          "description": "빠른 접근을 위해 Ledger Live를 홈화면에 두는 것을 권장합니다."\n        },\n        "step2": {\n          "title": "Ledger 설정",\n          "description": "데스크톱 앱과 동기화하거나 Ledger를 연결할 수 있습니다."\n        },\n        "step3": {\n          "title": "코드를 스캔하세요",\n          "description": "WalletConnect를 탭하고 스캐너로 전환합니다. 스캔 후 연결 요청이 나타나며, 이를 통해 지갑을 연결할 수 있습니다."\n        }\n      }\n    }\n  }\n}\n';
export {
  ko_KR_default as default
};
//# sourceMappingURL=ko_KR-V2HAEAHG-XFE4S5Z3.js.map
