{"version": 3, "sources": ["../../@walletconnect/modal/src/client.ts"], "sourcesContent": ["import type { ConfigCtrlState, ThemeCtrlState } from '@walletconnect/modal-core'\nimport { ConfigCtrl, ModalCtrl, OptionsCtrl, ThemeCtrl } from '@walletconnect/modal-core'\n\n/**\n * Types\n */\nexport type WalletConnectModalConfig = ConfigCtrlState & ThemeCtrlState\n\n/**\n * Client\n */\nexport class WalletConnectModal {\n  public constructor(config: WalletConnectModalConfig) {\n    ThemeCtrl.setThemeConfig(config)\n    ConfigCtrl.setConfig(config)\n    this.initUi()\n  }\n\n  private async initUi() {\n    if (typeof window !== 'undefined') {\n      await import('@walletconnect/modal-ui')\n      const modal = document.createElement('wcm-modal')\n      document.body.insertAdjacentElement('beforeend', modal)\n      OptionsCtrl.setIsUiLoaded(true)\n    }\n  }\n\n  public openModal = ModalCtrl.open\n\n  public closeModal = ModalCtrl.close\n\n  public subscribeModal = ModalCtrl.subscribe\n\n  public setTheme = ThemeCtrl.setThemeConfig\n}\n"], "mappings": ";;;;;;;;;AAWa,IAAAA,IAAA,MAAmB;EACvB,YAAYC,GAAkC;AAerD,SAAO,YAAYC,GAAU,MAE7B,KAAO,aAAaA,GAAU,OAE9B,KAAO,iBAAiBA,GAAU,WAElC,KAAO,WAAWC,GAAU,gBApB1BA,GAAU,eAAeF,CAAM,GAC/BG,EAAW,UAAUH,CAAM,GAC3B,KAAK,OAAA;EACP;EAEA,MAAc,SAAS;AACrB,QAAI,OAAO,SAAW,KAAa;AACjC,YAAa,OAAA,oBAAyB;AACtC,YAAMI,IAAQ,SAAS,cAAc,WAAW;AAChD,eAAS,KAAK,sBAAsB,aAAaA,CAAK,GACtDC,EAAY,cAAc,IAAI;IAAA;EAElC;AASF;", "names": ["WalletConnectModal", "config", "ModalCtrl", "ThemeCtrl", "ConfigCtrl", "modal", "OptionsCtrl"]}