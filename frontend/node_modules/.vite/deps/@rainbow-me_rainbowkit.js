"use client";
import {
  __assign,
  __rest,
  __spreadArray,
  init_tslib_es6
} from "./chunk-LGAGBJFP.js";
import {
  require_browser
} from "./chunk-UGAR5Y5J.js";
import {
  en_US_default
} from "./chunk-GGLB2WJG.js";
import {
  useAccount,
  useBalance,
  useConnect,
  useDisconnect,
  useEnsAvatar,
  useEnsName,
  useNetwork,
  usePublicClient,
  useSignMessage,
  useSwitchNetwork
} from "./chunk-WRN6L2RZ.js";
import "./chunk-QE4SLGDL.js";
import "./chunk-C2QRFJSH.js";
import {
  require_react_dom
} from "./chunk-I2I5JHEW.js";
import {
  require_react
} from "./chunk-O6JANDIR.js";
import {
  CoinbaseWalletConnector,
  MetaMaskConnector,
  SafeConnector,
  WalletConnectConnector,
  WalletConnectLegacyConnector
} from "./chunk-DI232KXC.js";
import "./chunk-P5AQC5DX.js";
import "./chunk-YUA5JKFY.js";
import "./chunk-HK67DIC5.js";
import {
  InjectedConnector
} from "./chunk-HE7TYL65.js";
import {
  mainnet
} from "./chunk-5EBGCFML.js";
import "./chunk-JDCTZMVO.js";
import "./chunk-Z2Q63RMA.js";
import "./chunk-L3LPOETZ.js";
import "./chunk-P4NPJVRO.js";
import {
  UserRejectedRequestError
} from "./chunk-XLLWCG7E.js";
import "./chunk-W6I35MAG.js";
import "./chunk-2B3V2GUC.js";
import {
  __commonJS,
  __toESM
} from "./chunk-W7S2ME4R.js";

// node_modules/ua-parser-js/src/ua-parser.js
var require_ua_parser = __commonJS({
  "node_modules/ua-parser-js/src/ua-parser.js"(exports, module) {
    (function(window2, undefined2) {
      "use strict";
      var LIBVERSION = "1.0.40", EMPTY = "", UNKNOWN = "?", FUNC_TYPE = "function", UNDEF_TYPE = "undefined", OBJ_TYPE = "object", STR_TYPE = "string", MAJOR = "major", MODEL = "model", NAME = "name", TYPE = "type", VENDOR = "vendor", VERSION = "version", ARCHITECTURE = "architecture", CONSOLE = "console", MOBILE = "mobile", TABLET = "tablet", SMARTTV = "smarttv", WEARABLE = "wearable", EMBEDDED = "embedded", UA_MAX_LENGTH = 500;
      var AMAZON = "Amazon", APPLE = "Apple", ASUS = "ASUS", BLACKBERRY = "BlackBerry", BROWSER = "Browser", CHROME = "Chrome", EDGE = "Edge", FIREFOX = "Firefox", GOOGLE = "Google", HUAWEI = "Huawei", LG = "LG", MICROSOFT = "Microsoft", MOTOROLA = "Motorola", OPERA = "Opera", SAMSUNG = "Samsung", SHARP = "Sharp", SONY = "Sony", XIAOMI = "Xiaomi", ZEBRA = "Zebra", FACEBOOK = "Facebook", CHROMIUM_OS = "Chromium OS", MAC_OS = "Mac OS", SUFFIX_BROWSER = " Browser";
      var extend = function(regexes2, extensions) {
        var mergedRegexes = {};
        for (var i in regexes2) {
          if (extensions[i] && extensions[i].length % 2 === 0) {
            mergedRegexes[i] = extensions[i].concat(regexes2[i]);
          } else {
            mergedRegexes[i] = regexes2[i];
          }
        }
        return mergedRegexes;
      }, enumerize = function(arr) {
        var enums = {};
        for (var i = 0; i < arr.length; i++) {
          enums[arr[i].toUpperCase()] = arr[i];
        }
        return enums;
      }, has = function(str1, str2) {
        return typeof str1 === STR_TYPE ? lowerize(str2).indexOf(lowerize(str1)) !== -1 : false;
      }, lowerize = function(str) {
        return str.toLowerCase();
      }, majorize = function(version) {
        return typeof version === STR_TYPE ? version.replace(/[^\d\.]/g, EMPTY).split(".")[0] : undefined2;
      }, trim = function(str, len) {
        if (typeof str === STR_TYPE) {
          str = str.replace(/^\s\s*/, EMPTY);
          return typeof len === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);
        }
      };
      var rgxMapper = function(ua2, arrays) {
        var i = 0, j, k, p, q, matches, match;
        while (i < arrays.length && !matches) {
          var regex = arrays[i], props = arrays[i + 1];
          j = k = 0;
          while (j < regex.length && !matches) {
            if (!regex[j]) {
              break;
            }
            matches = regex[j++].exec(ua2);
            if (!!matches) {
              for (p = 0; p < props.length; p++) {
                match = matches[++k];
                q = props[p];
                if (typeof q === OBJ_TYPE && q.length > 0) {
                  if (q.length === 2) {
                    if (typeof q[1] == FUNC_TYPE) {
                      this[q[0]] = q[1].call(this, match);
                    } else {
                      this[q[0]] = q[1];
                    }
                  } else if (q.length === 3) {
                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {
                      this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined2;
                    } else {
                      this[q[0]] = match ? match.replace(q[1], q[2]) : undefined2;
                    }
                  } else if (q.length === 4) {
                    this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined2;
                  }
                } else {
                  this[q] = match ? match : undefined2;
                }
              }
            }
          }
          i += 2;
        }
      }, strMapper = function(str, map) {
        for (var i in map) {
          if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {
            for (var j = 0; j < map[i].length; j++) {
              if (has(map[i][j], str)) {
                return i === UNKNOWN ? undefined2 : i;
              }
            }
          } else if (has(map[i], str)) {
            return i === UNKNOWN ? undefined2 : i;
          }
        }
        return map.hasOwnProperty("*") ? map["*"] : str;
      };
      var oldSafariMap = {
        "1.0": "/8",
        "1.2": "/1",
        "1.3": "/3",
        "2.0": "/412",
        "2.0.2": "/416",
        "2.0.3": "/417",
        "2.0.4": "/419",
        "?": "/"
      }, windowsVersionMap = {
        "ME": "4.90",
        "NT 3.11": "NT3.51",
        "NT 4.0": "NT4.0",
        "2000": "NT 5.0",
        "XP": ["NT 5.1", "NT 5.2"],
        "Vista": "NT 6.0",
        "7": "NT 6.1",
        "8": "NT 6.2",
        "8.1": "NT 6.3",
        "10": ["NT 6.4", "NT 10.0"],
        "RT": "ARM"
      };
      var regexes = {
        browser: [
          [
            /\b(?:crmo|crios)\/([\w\.]+)/i
            // Chrome for Android/iOS
          ],
          [VERSION, [NAME, "Chrome"]],
          [
            /edg(?:e|ios|a)?\/([\w\.]+)/i
            // Microsoft Edge
          ],
          [VERSION, [NAME, "Edge"]],
          [
            // Presto based
            /(opera mini)\/([-\w\.]+)/i,
            // Opera Mini
            /(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,
            // Opera Mobi/Tablet
            /(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i
            // Opera
          ],
          [NAME, VERSION],
          [
            /opios[\/ ]+([\w\.]+)/i
            // Opera mini on iphone >= 8.0
          ],
          [VERSION, [NAME, OPERA + " Mini"]],
          [
            /\bop(?:rg)?x\/([\w\.]+)/i
            // Opera GX
          ],
          [VERSION, [NAME, OPERA + " GX"]],
          [
            /\bopr\/([\w\.]+)/i
            // Opera Webkit
          ],
          [VERSION, [NAME, OPERA]],
          [
            // Mixed
            /\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i
            // Baidu
          ],
          [VERSION, [NAME, "Baidu"]],
          [
            /\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i
            // Maxthon
          ],
          [VERSION, [NAME, "Maxthon"]],
          [
            /(kindle)\/([\w\.]+)/i,
            // Kindle
            /(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,
            // Lunascape/Maxthon/Netfront/Jasmine/Blazer/Sleipnir
            // Trident based
            /(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,
            // Avant/IEMobile/SlimBrowser/SlimBoat/Slimjet
            /(?:ms|\()(ie) ([\w\.]+)/i,
            // Internet Explorer
            // Blink/Webkit/KHTML based                                         // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon
            /(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,
            // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ//Vivaldi/DuckDuckGo/Klar/Helio/Dragon
            /(heytap|ovi|115)browser\/([\d\.]+)/i,
            // HeyTap/Ovi/115
            /(weibo)__([\d\.]+)/i
            // Weibo
          ],
          [NAME, VERSION],
          [
            /quark(?:pc)?\/([-\w\.]+)/i
            // Quark
          ],
          [VERSION, [NAME, "Quark"]],
          [
            /\bddg\/([\w\.]+)/i
            // DuckDuckGo
          ],
          [VERSION, [NAME, "DuckDuckGo"]],
          [
            /(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i
            // UCBrowser
          ],
          [VERSION, [NAME, "UC" + BROWSER]],
          [
            /microm.+\bqbcore\/([\w\.]+)/i,
            // WeChat Desktop for Windows Built-in Browser
            /\bqbcore\/([\w\.]+).+microm/i,
            /micromessenger\/([\w\.]+)/i
            // WeChat
          ],
          [VERSION, [NAME, "WeChat"]],
          [
            /konqueror\/([\w\.]+)/i
            // Konqueror
          ],
          [VERSION, [NAME, "Konqueror"]],
          [
            /trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i
            // IE11
          ],
          [VERSION, [NAME, "IE"]],
          [
            /ya(?:search)?browser\/([\w\.]+)/i
            // Yandex
          ],
          [VERSION, [NAME, "Yandex"]],
          [
            /slbrowser\/([\w\.]+)/i
            // Smart Lenovo Browser
          ],
          [VERSION, [NAME, "Smart Lenovo " + BROWSER]],
          [
            /(avast|avg)\/([\w\.]+)/i
            // Avast/AVG Secure Browser
          ],
          [[NAME, /(.+)/, "$1 Secure " + BROWSER], VERSION],
          [
            /\bfocus\/([\w\.]+)/i
            // Firefox Focus
          ],
          [VERSION, [NAME, FIREFOX + " Focus"]],
          [
            /\bopt\/([\w\.]+)/i
            // Opera Touch
          ],
          [VERSION, [NAME, OPERA + " Touch"]],
          [
            /coc_coc\w+\/([\w\.]+)/i
            // Coc Coc Browser
          ],
          [VERSION, [NAME, "Coc Coc"]],
          [
            /dolfin\/([\w\.]+)/i
            // Dolphin
          ],
          [VERSION, [NAME, "Dolphin"]],
          [
            /coast\/([\w\.]+)/i
            // Opera Coast
          ],
          [VERSION, [NAME, OPERA + " Coast"]],
          [
            /miuibrowser\/([\w\.]+)/i
            // MIUI Browser
          ],
          [VERSION, [NAME, "MIUI" + SUFFIX_BROWSER]],
          [
            /fxios\/([\w\.-]+)/i
            // Firefox for iOS
          ],
          [VERSION, [NAME, FIREFOX]],
          [
            /\bqihoobrowser\/?([\w\.]*)/i
            // 360
          ],
          [VERSION, [NAME, "360"]],
          [
            /\b(qq)\/([\w\.]+)/i
            // QQ
          ],
          [[NAME, /(.+)/, "$1Browser"], VERSION],
          [
            /(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i
          ],
          [[NAME, /(.+)/, "$1" + SUFFIX_BROWSER], VERSION],
          [
            // Oculus/Sailfish/HuaweiBrowser/VivoBrowser/PicoBrowser
            /samsungbrowser\/([\w\.]+)/i
            // Samsung Internet
          ],
          [VERSION, [NAME, SAMSUNG + " Internet"]],
          [
            /metasr[\/ ]?([\d\.]+)/i
            // Sogou Explorer
          ],
          [VERSION, [NAME, "Sogou Explorer"]],
          [
            /(sogou)mo\w+\/([\d\.]+)/i
            // Sogou Mobile
          ],
          [[NAME, "Sogou Mobile"], VERSION],
          [
            /(electron)\/([\w\.]+) safari/i,
            // Electron-based App
            /(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,
            // Tesla
            /m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i
            // QQ/2345
          ],
          [NAME, VERSION],
          [
            /(lbbrowser|rekonq)/i,
            // LieBao Browser/Rekonq
            /\[(linkedin)app\]/i
            // LinkedIn App for iOS & Android
          ],
          [NAME],
          [
            /ome\/([\w\.]+) \w* ?(iron) saf/i,
            // Iron
            /ome\/([\w\.]+).+qihu (360)[es]e/i
            // 360
          ],
          [VERSION, NAME],
          [
            // WebView
            /((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i
            // Facebook App for iOS & Android
          ],
          [[NAME, FACEBOOK], VERSION],
          [
            /(Klarna)\/([\w\.]+)/i,
            // Klarna Shopping Browser for iOS & Android
            /(kakao(?:talk|story))[\/ ]([\w\.]+)/i,
            // Kakao App
            /(naver)\(.*?(\d+\.[\w\.]+).*\)/i,
            // Naver InApp
            /safari (line)\/([\w\.]+)/i,
            // Line App for iOS
            /\b(line)\/([\w\.]+)\/iab/i,
            // Line App for Android
            /(alipay)client\/([\w\.]+)/i,
            // Alipay
            /(twitter)(?:and| f.+e\/([\w\.]+))/i,
            // Twitter
            /(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i
            // Chromium/Instagram/Snapchat
          ],
          [NAME, VERSION],
          [
            /\bgsa\/([\w\.]+) .*safari\//i
            // Google Search Appliance on iOS
          ],
          [VERSION, [NAME, "GSA"]],
          [
            /musical_ly(?:.+app_?version\/|_)([\w\.]+)/i
            // TikTok
          ],
          [VERSION, [NAME, "TikTok"]],
          [
            /headlesschrome(?:\/([\w\.]+)| )/i
            // Chrome Headless
          ],
          [VERSION, [NAME, CHROME + " Headless"]],
          [
            / wv\).+(chrome)\/([\w\.]+)/i
            // Chrome WebView
          ],
          [[NAME, CHROME + " WebView"], VERSION],
          [
            /droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i
            // Android Browser
          ],
          [VERSION, [NAME, "Android " + BROWSER]],
          [
            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i
            // Chrome/OmniWeb/Arora/Tizen/Nokia
          ],
          [NAME, VERSION],
          [
            /version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i
            // Mobile Safari
          ],
          [VERSION, [NAME, "Mobile Safari"]],
          [
            /version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i
            // Safari & Safari Mobile
          ],
          [VERSION, NAME],
          [
            /webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i
            // Safari < 3.0
          ],
          [NAME, [VERSION, strMapper, oldSafariMap]],
          [
            /(webkit|khtml)\/([\w\.]+)/i
          ],
          [NAME, VERSION],
          [
            // Gecko based
            /(navigator|netscape\d?)\/([-\w\.]+)/i
            // Netscape
          ],
          [[NAME, "Netscape"], VERSION],
          [
            /(wolvic|librewolf)\/([\w\.]+)/i
            // Wolvic/LibreWolf
          ],
          [NAME, VERSION],
          [
            /mobile vr; rv:([\w\.]+)\).+firefox/i
            // Firefox Reality
          ],
          [VERSION, [NAME, FIREFOX + " Reality"]],
          [
            /ekiohf.+(flow)\/([\w\.]+)/i,
            // Flow
            /(swiftfox)/i,
            // Swiftfox
            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,
            // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror
            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,
            // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix
            /(firefox)\/([\w\.]+)/i,
            // Other Firefox-based
            /(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,
            // Mozilla
            // Other
            /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,
            // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Obigo/Mosaic/Go/ICE/UP.Browser
            /(links) \(([\w\.]+)/i
            // Links
          ],
          [NAME, [VERSION, /_/g, "."]],
          [
            /(cobalt)\/([\w\.]+)/i
            // Cobalt
          ],
          [NAME, [VERSION, /master.|lts./, ""]]
        ],
        cpu: [
          [
            /(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i
            // AMD64 (x64)
          ],
          [[ARCHITECTURE, "amd64"]],
          [
            /(ia32(?=;))/i
            // IA32 (quicktime)
          ],
          [[ARCHITECTURE, lowerize]],
          [
            /((?:i[346]|x)86)[;\)]/i
            // IA32 (x86)
          ],
          [[ARCHITECTURE, "ia32"]],
          [
            /\b(aarch64|arm(v?8e?l?|_?64))\b/i
            // ARM64
          ],
          [[ARCHITECTURE, "arm64"]],
          [
            /\b(arm(?:v[67])?ht?n?[fl]p?)\b/i
            // ARMHF
          ],
          [[ARCHITECTURE, "armhf"]],
          [
            // PocketPC mistakenly identified as PowerPC
            /windows (ce|mobile); ppc;/i
          ],
          [[ARCHITECTURE, "arm"]],
          [
            /((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i
            // PowerPC
          ],
          [[ARCHITECTURE, /ower/, EMPTY, lowerize]],
          [
            /(sun4\w)[;\)]/i
            // SPARC
          ],
          [[ARCHITECTURE, "sparc"]],
          [
            /((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i
            // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC
          ],
          [[ARCHITECTURE, lowerize]]
        ],
        device: [
          [
            //////////////////////////
            // MOBILES & TABLETS
            /////////////////////////
            // Samsung
            /\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i
          ],
          [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]],
          [
            /\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,
            /samsung[- ]((?!sm-[lr])[-\w]+)/i,
            /sec-(sgh\w+)/i
          ],
          [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]],
          [
            // Apple
            /(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i
            // iPod/iPhone
          ],
          [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]],
          [
            /\((ipad);[-\w\),; ]+apple/i,
            // iPad
            /applecoremedia\/[\w\.]+ \((ipad)/i,
            /\b(ipad)\d\d?,\d\d?[;\]].+ios/i
          ],
          [MODEL, [VENDOR, APPLE], [TYPE, TABLET]],
          [
            /(macintosh);/i
          ],
          [MODEL, [VENDOR, APPLE]],
          [
            // Sharp
            /\b(sh-?[altvz]?\d\d[a-ekm]?)/i
          ],
          [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]],
          [
            // Honor
            /(?:honor)([-\w ]+)[;\)]/i
          ],
          [MODEL, [VENDOR, "Honor"], [TYPE, MOBILE]],
          [
            // Huawei
            /\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i
          ],
          [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]],
          [
            /(?:huawei)([-\w ]+)[;\)]/i,
            /\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i
          ],
          [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]],
          [
            // Xiaomi
            /\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,
            // Xiaomi POCO
            /\b; (\w+) build\/hm\1/i,
            // Xiaomi Hongmi 'numeric' models
            /\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,
            // Xiaomi Hongmi
            /\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,
            // Xiaomi Redmi
            /oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,
            // Xiaomi Redmi 'numeric' models
            /\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i
            // Xiaomi Mi
          ],
          [[MODEL, /_/g, " "], [VENDOR, XIAOMI], [TYPE, MOBILE]],
          [
            /oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,
            // Redmi Pad
            /\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i
            // Mi Pad tablets
          ],
          [[MODEL, /_/g, " "], [VENDOR, XIAOMI], [TYPE, TABLET]],
          [
            // OPPO
            /; (\w+) bui.+ oppo/i,
            /\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i
          ],
          [MODEL, [VENDOR, "OPPO"], [TYPE, MOBILE]],
          [
            /\b(opd2\d{3}a?) bui/i
          ],
          [MODEL, [VENDOR, "OPPO"], [TYPE, TABLET]],
          [
            // Vivo
            /vivo (\w+)(?: bui|\))/i,
            /\b(v[12]\d{3}\w?[at])(?: bui|;)/i
          ],
          [MODEL, [VENDOR, "Vivo"], [TYPE, MOBILE]],
          [
            // Realme
            /\b(rmx[1-3]\d{3})(?: bui|;|\))/i
          ],
          [MODEL, [VENDOR, "Realme"], [TYPE, MOBILE]],
          [
            // Motorola
            /\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,
            /\bmot(?:orola)?[- ](\w*)/i,
            /((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i
          ],
          [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]],
          [
            /\b(mz60\d|xoom[2 ]{0,2}) build\//i
          ],
          [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]],
          [
            // LG
            /((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i
          ],
          [MODEL, [VENDOR, LG], [TYPE, TABLET]],
          [
            /(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,
            /\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,
            /\blg-?([\d\w]+) bui/i
          ],
          [MODEL, [VENDOR, LG], [TYPE, MOBILE]],
          [
            // Lenovo
            /(ideatab[-\w ]+)/i,
            /lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i
          ],
          [MODEL, [VENDOR, "Lenovo"], [TYPE, TABLET]],
          [
            // Nokia
            /(?:maemo|nokia).*(n900|lumia \d+)/i,
            /nokia[-_ ]?([-\w\.]*)/i
          ],
          [[MODEL, /_/g, " "], [VENDOR, "Nokia"], [TYPE, MOBILE]],
          [
            // Google
            /(pixel c)\b/i
            // Google Pixel C
          ],
          [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]],
          [
            /droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i
            // Google Pixel
          ],
          [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]],
          [
            // Sony
            /droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i
          ],
          [MODEL, [VENDOR, SONY], [TYPE, MOBILE]],
          [
            /sony tablet [ps]/i,
            /\b(?:sony)?sgp\w+(?: bui|\))/i
          ],
          [[MODEL, "Xperia Tablet"], [VENDOR, SONY], [TYPE, TABLET]],
          [
            // OnePlus
            / (kb2005|in20[12]5|be20[12][59])\b/i,
            /(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i
          ],
          [MODEL, [VENDOR, "OnePlus"], [TYPE, MOBILE]],
          [
            // Amazon
            /(alexa)webm/i,
            /(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,
            // Kindle Fire without Silk / Echo Show
            /(kf[a-z]+)( bui|\)).+silk\//i
            // Kindle Fire HD
          ],
          [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]],
          [
            /((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i
            // Fire Phone
          ],
          [[MODEL, /(.+)/g, "Fire Phone $1"], [VENDOR, AMAZON], [TYPE, MOBILE]],
          [
            // BlackBerry
            /(playbook);[-\w\),; ]+(rim)/i
            // BlackBerry PlayBook
          ],
          [MODEL, VENDOR, [TYPE, TABLET]],
          [
            /\b((?:bb[a-f]|st[hv])100-\d)/i,
            /\(bb10; (\w+)/i
            // BlackBerry 10
          ],
          [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]],
          [
            // Asus
            /(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i
          ],
          [MODEL, [VENDOR, ASUS], [TYPE, TABLET]],
          [
            / (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i
          ],
          [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]],
          [
            // HTC
            /(nexus 9)/i
            // HTC Nexus 9
          ],
          [MODEL, [VENDOR, "HTC"], [TYPE, TABLET]],
          [
            /(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,
            // HTC
            // ZTE
            /(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,
            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i
            // Alcatel/GeeksPhone/Nexian/Panasonic/Sony
          ],
          [VENDOR, [MODEL, /_/g, " "], [TYPE, MOBILE]],
          [
            // TCL
            /droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i
          ],
          [MODEL, [VENDOR, "TCL"], [TYPE, TABLET]],
          [
            // itel
            /(itel) ((\w+))/i
          ],
          [[VENDOR, lowerize], MODEL, [TYPE, strMapper, { "tablet": ["p10001l", "w7001"], "*": "mobile" }]],
          [
            // Acer
            /droid.+; ([ab][1-7]-?[0178a]\d\d?)/i
          ],
          [MODEL, [VENDOR, "Acer"], [TYPE, TABLET]],
          [
            // Meizu
            /droid.+; (m[1-5] note) bui/i,
            /\bmz-([-\w]{2,})/i
          ],
          [MODEL, [VENDOR, "Meizu"], [TYPE, MOBILE]],
          [
            // Ulefone
            /; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i
          ],
          [MODEL, [VENDOR, "Ulefone"], [TYPE, MOBILE]],
          [
            // Energizer
            /; (energy ?\w+)(?: bui|\))/i,
            /; energizer ([\w ]+)(?: bui|\))/i
          ],
          [MODEL, [VENDOR, "Energizer"], [TYPE, MOBILE]],
          [
            // Cat
            /; cat (b35);/i,
            /; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i
          ],
          [MODEL, [VENDOR, "Cat"], [TYPE, MOBILE]],
          [
            // Smartfren
            /((?:new )?andromax[\w- ]+)(?: bui|\))/i
          ],
          [MODEL, [VENDOR, "Smartfren"], [TYPE, MOBILE]],
          [
            // Nothing
            /droid.+; (a(?:015|06[35]|142p?))/i
          ],
          [MODEL, [VENDOR, "Nothing"], [TYPE, MOBILE]],
          [
            // MIXED
            /(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,
            // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron/Infinix/Tecno/Micromax/Advan
            /; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,
            // IMO
            /(hp) ([\w ]+\w)/i,
            // HP iPAQ
            /(asus)-?(\w+)/i,
            // Asus
            /(microsoft); (lumia[\w ]+)/i,
            // Microsoft Lumia
            /(lenovo)[-_ ]?([-\w]+)/i,
            // Lenovo
            /(jolla)/i,
            // Jolla
            /(oppo) ?([\w ]+) bui/i
            // OPPO
          ],
          [VENDOR, MODEL, [TYPE, MOBILE]],
          [
            /(imo) (tab \w+)/i,
            // IMO
            /(kobo)\s(ereader|touch)/i,
            // Kobo
            /(archos) (gamepad2?)/i,
            // Archos
            /(hp).+(touchpad(?!.+tablet)|tablet)/i,
            // HP TouchPad
            /(kindle)\/([\w\.]+)/i,
            // Kindle
            /(nook)[\w ]+build\/(\w+)/i,
            // Nook
            /(dell) (strea[kpr\d ]*[\dko])/i,
            // Dell Streak
            /(le[- ]+pan)[- ]+(\w{1,9}) bui/i,
            // Le Pan Tablets
            /(trinity)[- ]*(t\d{3}) bui/i,
            // Trinity Tablets
            /(gigaset)[- ]+(q\w{1,9}) bui/i,
            // Gigaset Tablets
            /(vodafone) ([\w ]+)(?:\)| bui)/i
            // Vodafone
          ],
          [VENDOR, MODEL, [TYPE, TABLET]],
          [
            /(surface duo)/i
            // Surface Duo
          ],
          [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]],
          [
            /droid [\d\.]+; (fp\du?)(?: b|\))/i
            // Fairphone
          ],
          [MODEL, [VENDOR, "Fairphone"], [TYPE, MOBILE]],
          [
            /(u304aa)/i
            // AT&T
          ],
          [MODEL, [VENDOR, "AT&T"], [TYPE, MOBILE]],
          [
            /\bsie-(\w*)/i
            // Siemens
          ],
          [MODEL, [VENDOR, "Siemens"], [TYPE, MOBILE]],
          [
            /\b(rct\w+) b/i
            // RCA Tablets
          ],
          [MODEL, [VENDOR, "RCA"], [TYPE, TABLET]],
          [
            /\b(venue[\d ]{2,7}) b/i
            // Dell Venue Tablets
          ],
          [MODEL, [VENDOR, "Dell"], [TYPE, TABLET]],
          [
            /\b(q(?:mv|ta)\w+) b/i
            // Verizon Tablet
          ],
          [MODEL, [VENDOR, "Verizon"], [TYPE, TABLET]],
          [
            /\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i
            // Barnes & Noble Tablet
          ],
          [MODEL, [VENDOR, "Barnes & Noble"], [TYPE, TABLET]],
          [
            /\b(tm\d{3}\w+) b/i
          ],
          [MODEL, [VENDOR, "NuVision"], [TYPE, TABLET]],
          [
            /\b(k88) b/i
            // ZTE K Series Tablet
          ],
          [MODEL, [VENDOR, "ZTE"], [TYPE, TABLET]],
          [
            /\b(nx\d{3}j) b/i
            // ZTE Nubia
          ],
          [MODEL, [VENDOR, "ZTE"], [TYPE, MOBILE]],
          [
            /\b(gen\d{3}) b.+49h/i
            // Swiss GEN Mobile
          ],
          [MODEL, [VENDOR, "Swiss"], [TYPE, MOBILE]],
          [
            /\b(zur\d{3}) b/i
            // Swiss ZUR Tablet
          ],
          [MODEL, [VENDOR, "Swiss"], [TYPE, TABLET]],
          [
            /\b((zeki)?tb.*\b) b/i
            // Zeki Tablets
          ],
          [MODEL, [VENDOR, "Zeki"], [TYPE, TABLET]],
          [
            /\b([yr]\d{2}) b/i,
            /\b(dragon[- ]+touch |dt)(\w{5}) b/i
            // Dragon Touch Tablet
          ],
          [[VENDOR, "Dragon Touch"], MODEL, [TYPE, TABLET]],
          [
            /\b(ns-?\w{0,9}) b/i
            // Insignia Tablets
          ],
          [MODEL, [VENDOR, "Insignia"], [TYPE, TABLET]],
          [
            /\b((nxa|next)-?\w{0,9}) b/i
            // NextBook Tablets
          ],
          [MODEL, [VENDOR, "NextBook"], [TYPE, TABLET]],
          [
            /\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i
            // Voice Xtreme Phones
          ],
          [[VENDOR, "Voice"], MODEL, [TYPE, MOBILE]],
          [
            /\b(lvtel\-)?(v1[12]) b/i
            // LvTel Phones
          ],
          [[VENDOR, "LvTel"], MODEL, [TYPE, MOBILE]],
          [
            /\b(ph-1) /i
            // Essential PH-1
          ],
          [MODEL, [VENDOR, "Essential"], [TYPE, MOBILE]],
          [
            /\b(v(100md|700na|7011|917g).*\b) b/i
            // Envizen Tablets
          ],
          [MODEL, [VENDOR, "Envizen"], [TYPE, TABLET]],
          [
            /\b(trio[-\w\. ]+) b/i
            // MachSpeed Tablets
          ],
          [MODEL, [VENDOR, "MachSpeed"], [TYPE, TABLET]],
          [
            /\btu_(1491) b/i
            // Rotor Tablets
          ],
          [MODEL, [VENDOR, "Rotor"], [TYPE, TABLET]],
          [
            /(shield[\w ]+) b/i
            // Nvidia Shield Tablets
          ],
          [MODEL, [VENDOR, "Nvidia"], [TYPE, TABLET]],
          [
            /(sprint) (\w+)/i
            // Sprint Phones
          ],
          [VENDOR, MODEL, [TYPE, MOBILE]],
          [
            /(kin\.[onetw]{3})/i
            // Microsoft Kin
          ],
          [[MODEL, /\./g, " "], [VENDOR, MICROSOFT], [TYPE, MOBILE]],
          [
            /droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i
            // Zebra
          ],
          [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]],
          [
            /droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i
          ],
          [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]],
          [
            ///////////////////
            // SMARTTVS
            ///////////////////
            /smart-tv.+(samsung)/i
            // Samsung
          ],
          [VENDOR, [TYPE, SMARTTV]],
          [
            /hbbtv.+maple;(\d+)/i
          ],
          [[MODEL, /^/, "SmartTV"], [VENDOR, SAMSUNG], [TYPE, SMARTTV]],
          [
            /(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i
            // LG SmartTV
          ],
          [[VENDOR, LG], [TYPE, SMARTTV]],
          [
            /(apple) ?tv/i
            // Apple TV
          ],
          [VENDOR, [MODEL, APPLE + " TV"], [TYPE, SMARTTV]],
          [
            /crkey/i
            // Google Chromecast
          ],
          [[MODEL, CHROME + "cast"], [VENDOR, GOOGLE], [TYPE, SMARTTV]],
          [
            /droid.+aft(\w+)( bui|\))/i
            // Fire TV
          ],
          [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]],
          [
            /\(dtv[\);].+(aquos)/i,
            /(aquos-tv[\w ]+)\)/i
            // Sharp
          ],
          [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],
          [
            /(bravia[\w ]+)( bui|\))/i
            // Sony
          ],
          [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]],
          [
            /(mitv-\w{5}) bui/i
            // Xiaomi
          ],
          [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]],
          [
            /Hbbtv.*(technisat) (.*);/i
            // TechniSAT
          ],
          [VENDOR, MODEL, [TYPE, SMARTTV]],
          [
            /\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,
            // Roku
            /hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i
            // HbbTV devices
          ],
          [[VENDOR, trim], [MODEL, trim], [TYPE, SMARTTV]],
          [
            /\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i
            // SmartTV from Unidentified Vendors
          ],
          [[TYPE, SMARTTV]],
          [
            ///////////////////
            // CONSOLES
            ///////////////////
            /(ouya)/i,
            // Ouya
            /(nintendo) ([wids3utch]+)/i
            // Nintendo
          ],
          [VENDOR, MODEL, [TYPE, CONSOLE]],
          [
            /droid.+; (shield) bui/i
            // Nvidia
          ],
          [MODEL, [VENDOR, "Nvidia"], [TYPE, CONSOLE]],
          [
            /(playstation [345portablevi]+)/i
            // Playstation
          ],
          [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]],
          [
            /\b(xbox(?: one)?(?!; xbox))[\); ]/i
            // Microsoft Xbox
          ],
          [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]],
          [
            ///////////////////
            // WEARABLES
            ///////////////////
            /\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i
            // Samsung Galaxy Watch
          ],
          [MODEL, [VENDOR, SAMSUNG], [TYPE, WEARABLE]],
          [
            /((pebble))app/i
            // Pebble
          ],
          [VENDOR, MODEL, [TYPE, WEARABLE]],
          [
            /(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i
            // Apple Watch
          ],
          [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]],
          [
            /droid.+; (glass) \d/i
            // Google Glass
          ],
          [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]],
          [
            /droid.+; (wt63?0{2,3})\)/i
          ],
          [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]],
          [
            ///////////////////
            // XR
            ///////////////////
            /droid.+; (glass) \d/i
            // Google Glass
          ],
          [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]],
          [
            /(pico) (4|neo3(?: link|pro)?)/i
            // Pico
          ],
          [VENDOR, MODEL, [TYPE, WEARABLE]],
          [
            /; (quest( \d| pro)?)/i
            // Oculus Quest
          ],
          [MODEL, [VENDOR, FACEBOOK], [TYPE, WEARABLE]],
          [
            ///////////////////
            // EMBEDDED
            ///////////////////
            /(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i
            // Tesla
          ],
          [VENDOR, [TYPE, EMBEDDED]],
          [
            /(aeobc)\b/i
            // Echo Dot
          ],
          [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]],
          [
            ////////////////////
            // MIXED (GENERIC)
            ///////////////////
            /droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i
            // Android Phones from Unidentified Vendors
          ],
          [MODEL, [TYPE, MOBILE]],
          [
            /droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i
            // Android Tablets from Unidentified Vendors
          ],
          [MODEL, [TYPE, TABLET]],
          [
            /\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i
            // Unidentifiable Tablet
          ],
          [[TYPE, TABLET]],
          [
            /(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i
            // Unidentifiable Mobile
          ],
          [[TYPE, MOBILE]],
          [
            /(android[-\w\. ]{0,9});.+buil/i
            // Generic Android Device
          ],
          [MODEL, [VENDOR, "Generic"]]
        ],
        engine: [
          [
            /windows.+ edge\/([\w\.]+)/i
            // EdgeHTML
          ],
          [VERSION, [NAME, EDGE + "HTML"]],
          [
            /(arkweb)\/([\w\.]+)/i
            // ArkWeb
          ],
          [NAME, VERSION],
          [
            /webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i
            // Blink
          ],
          [VERSION, [NAME, "Blink"]],
          [
            /(presto)\/([\w\.]+)/i,
            // Presto
            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,
            // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna/Servo
            /ekioh(flow)\/([\w\.]+)/i,
            // Flow
            /(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,
            // KHTML/Tasman/Links
            /(icab)[\/ ]([23]\.[\d\.]+)/i,
            // iCab
            /\b(libweb)/i
          ],
          [NAME, VERSION],
          [
            /rv\:([\w\.]{1,9})\b.+(gecko)/i
            // Gecko
          ],
          [VERSION, NAME]
        ],
        os: [
          [
            // Windows
            /microsoft (windows) (vista|xp)/i
            // Windows (iTunes)
          ],
          [NAME, VERSION],
          [
            /(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i
            // Windows Phone
          ],
          [NAME, [VERSION, strMapper, windowsVersionMap]],
          [
            /windows nt 6\.2; (arm)/i,
            // Windows RT
            /windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,
            /(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i
          ],
          [[VERSION, strMapper, windowsVersionMap], [NAME, "Windows"]],
          [
            // iOS/macOS
            /ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,
            // iOS
            /(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,
            /cfnetwork\/.+darwin/i
          ],
          [[VERSION, /_/g, "."], [NAME, "iOS"]],
          [
            /(mac os x) ?([\w\. ]*)/i,
            /(macintosh|mac_powerpc\b)(?!.+haiku)/i
            // Mac OS
          ],
          [[NAME, MAC_OS], [VERSION, /_/g, "."]],
          [
            // Mobile OSes
            /droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i
            // Android-x86/HarmonyOS
          ],
          [VERSION, NAME],
          [
            // Android/WebOS/QNX/Bada/RIM/Maemo/MeeGo/Sailfish OS/OpenHarmony
            /(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,
            /(blackberry)\w*\/([\w\.]*)/i,
            // Blackberry
            /(tizen|kaios)[\/ ]([\w\.]+)/i,
            // Tizen/KaiOS
            /\((series40);/i
            // Series 40
          ],
          [NAME, VERSION],
          [
            /\(bb(10);/i
            // BlackBerry 10
          ],
          [VERSION, [NAME, BLACKBERRY]],
          [
            /(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i
            // Symbian
          ],
          [VERSION, [NAME, "Symbian"]],
          [
            /mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i
            // Firefox OS
          ],
          [VERSION, [NAME, FIREFOX + " OS"]],
          [
            /web0s;.+rt(tv)/i,
            /\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i
            // WebOS
          ],
          [VERSION, [NAME, "webOS"]],
          [
            /watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i
            // watchOS
          ],
          [VERSION, [NAME, "watchOS"]],
          [
            // Google Chromecast
            /crkey\/([\d\.]+)/i
            // Google Chromecast
          ],
          [VERSION, [NAME, CHROME + "cast"]],
          [
            /(cros) [\w]+(?:\)| ([\w\.]+)\b)/i
            // Chromium OS
          ],
          [[NAME, CHROMIUM_OS], VERSION],
          [
            // Smart TVs
            /panasonic;(viera)/i,
            // Panasonic Viera
            /(netrange)mmh/i,
            // Netrange
            /(nettv)\/(\d+\.[\w\.]+)/i,
            // NetTV
            // Console
            /(nintendo|playstation) ([wids345portablevuch]+)/i,
            // Nintendo/Playstation
            /(xbox); +xbox ([^\);]+)/i,
            // Microsoft Xbox (360, One, X, S, Series X, Series S)
            // Other
            /\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,
            // Joli/Palm
            /(mint)[\/\(\) ]?(\w*)/i,
            // Mint
            /(mageia|vectorlinux)[; ]/i,
            // Mageia/VectorLinux
            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,
            // Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire
            /(hurd|linux) ?([\w\.]*)/i,
            // Hurd/Linux
            /(gnu) ?([\w\.]*)/i,
            // GNU
            /\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,
            // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly
            /(haiku) (\w+)/i
            // Haiku
          ],
          [NAME, VERSION],
          [
            /(sunos) ?([\w\.\d]*)/i
            // Solaris
          ],
          [[NAME, "Solaris"], VERSION],
          [
            /((?:open)?solaris)[-\/ ]?([\w\.]*)/i,
            // Solaris
            /(aix) ((\d)(?=\.|\)| )[\w\.])*/i,
            // AIX
            /\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,
            // BeOS/OS2/AmigaOS/MorphOS/OpenVMS/Fuchsia/HP-UX/SerenityOS
            /(unix) ?([\w\.]*)/i
            // UNIX
          ],
          [NAME, VERSION]
        ]
      };
      var UAParser2 = function(ua2, extensions) {
        if (typeof ua2 === OBJ_TYPE) {
          extensions = ua2;
          ua2 = undefined2;
        }
        if (!(this instanceof UAParser2)) {
          return new UAParser2(ua2, extensions).getResult();
        }
        var _navigator = typeof window2 !== UNDEF_TYPE && window2.navigator ? window2.navigator : undefined2;
        var _ua = ua2 || (_navigator && _navigator.userAgent ? _navigator.userAgent : EMPTY);
        var _uach = _navigator && _navigator.userAgentData ? _navigator.userAgentData : undefined2;
        var _rgxmap = extensions ? extend(regexes, extensions) : regexes;
        var _isSelfNav = _navigator && _navigator.userAgent == _ua;
        this.getBrowser = function() {
          var _browser = {};
          _browser[NAME] = undefined2;
          _browser[VERSION] = undefined2;
          rgxMapper.call(_browser, _ua, _rgxmap.browser);
          _browser[MAJOR] = majorize(_browser[VERSION]);
          if (_isSelfNav && _navigator && _navigator.brave && typeof _navigator.brave.isBrave == FUNC_TYPE) {
            _browser[NAME] = "Brave";
          }
          return _browser;
        };
        this.getCPU = function() {
          var _cpu = {};
          _cpu[ARCHITECTURE] = undefined2;
          rgxMapper.call(_cpu, _ua, _rgxmap.cpu);
          return _cpu;
        };
        this.getDevice = function() {
          var _device = {};
          _device[VENDOR] = undefined2;
          _device[MODEL] = undefined2;
          _device[TYPE] = undefined2;
          rgxMapper.call(_device, _ua, _rgxmap.device);
          if (_isSelfNav && !_device[TYPE] && _uach && _uach.mobile) {
            _device[TYPE] = MOBILE;
          }
          if (_isSelfNav && _device[MODEL] == "Macintosh" && _navigator && typeof _navigator.standalone !== UNDEF_TYPE && _navigator.maxTouchPoints && _navigator.maxTouchPoints > 2) {
            _device[MODEL] = "iPad";
            _device[TYPE] = TABLET;
          }
          return _device;
        };
        this.getEngine = function() {
          var _engine = {};
          _engine[NAME] = undefined2;
          _engine[VERSION] = undefined2;
          rgxMapper.call(_engine, _ua, _rgxmap.engine);
          return _engine;
        };
        this.getOS = function() {
          var _os = {};
          _os[NAME] = undefined2;
          _os[VERSION] = undefined2;
          rgxMapper.call(_os, _ua, _rgxmap.os);
          if (_isSelfNav && !_os[NAME] && _uach && _uach.platform && _uach.platform != "Unknown") {
            _os[NAME] = _uach.platform.replace(/chrome os/i, CHROMIUM_OS).replace(/macos/i, MAC_OS);
          }
          return _os;
        };
        this.getResult = function() {
          return {
            ua: this.getUA(),
            browser: this.getBrowser(),
            engine: this.getEngine(),
            os: this.getOS(),
            device: this.getDevice(),
            cpu: this.getCPU()
          };
        };
        this.getUA = function() {
          return _ua;
        };
        this.setUA = function(ua3) {
          _ua = typeof ua3 === STR_TYPE && ua3.length > UA_MAX_LENGTH ? trim(ua3, UA_MAX_LENGTH) : ua3;
          return this;
        };
        this.setUA(_ua);
        return this;
      };
      UAParser2.VERSION = LIBVERSION;
      UAParser2.BROWSER = enumerize([NAME, VERSION, MAJOR]);
      UAParser2.CPU = enumerize([ARCHITECTURE]);
      UAParser2.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);
      UAParser2.ENGINE = UAParser2.OS = enumerize([NAME, VERSION]);
      if (typeof exports !== UNDEF_TYPE) {
        if (typeof module !== UNDEF_TYPE && module.exports) {
          exports = module.exports = UAParser2;
        }
        exports.UAParser = UAParser2;
      } else {
        if (typeof define === FUNC_TYPE && define.amd) {
          define(function() {
            return UAParser2;
          });
        } else if (typeof window2 !== UNDEF_TYPE) {
          window2.UAParser = UAParser2;
        }
      }
      var $ = typeof window2 !== UNDEF_TYPE && (window2.jQuery || window2.Zepto);
      if ($ && !$.ua) {
        var parser = new UAParser2();
        $.ua = parser.getResult();
        $.ua.get = function() {
          return parser.getUA();
        };
        $.ua.set = function(ua2) {
          parser.setUA(ua2);
          var result = parser.getResult();
          for (var prop in result) {
            $.ua[prop] = result[prop];
          }
        };
      }
    })(typeof window === "object" ? window : exports);
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/chunk-SWKUKXRF.js
var systemFontStack = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"';
var fontStacks = {
  rounded: `SFRounded, ui-rounded, "SF Pro Rounded", ${systemFontStack}`,
  system: systemFontStack
};
var radiusScales = {
  large: {
    actionButton: "9999px",
    connectButton: "12px",
    modal: "24px",
    modalMobile: "28px"
  },
  medium: {
    actionButton: "10px",
    connectButton: "8px",
    modal: "16px",
    modalMobile: "18px"
  },
  none: {
    actionButton: "0px",
    connectButton: "0px",
    modal: "0px",
    modalMobile: "0px"
  },
  small: {
    actionButton: "4px",
    connectButton: "4px",
    modal: "8px",
    modalMobile: "8px"
  }
};
var blurs = {
  large: {
    modalOverlay: "blur(20px)"
  },
  none: {
    modalOverlay: "blur(0px)"
  },
  small: {
    modalOverlay: "blur(4px)"
  }
};
var baseTheme = ({
  borderRadius = "large",
  fontStack = "rounded",
  overlayBlur = "none"
}) => ({
  blurs: {
    modalOverlay: blurs[overlayBlur].modalOverlay
  },
  fonts: {
    body: fontStacks[fontStack]
  },
  radii: {
    actionButton: radiusScales[borderRadius].actionButton,
    connectButton: radiusScales[borderRadius].connectButton,
    menuButton: radiusScales[borderRadius].connectButton,
    modal: radiusScales[borderRadius].modal,
    modalMobile: radiusScales[borderRadius].modalMobile
  }
});

// node_modules/@rainbow-me/rainbowkit/dist/chunk-BSMW2B2K.js
var darkGrey = "#1A1B1F";
var accentColors = {
  blue: { accentColor: "#3898FF", accentColorForeground: "#FFF" },
  green: { accentColor: "#4BD166", accentColorForeground: darkGrey },
  orange: { accentColor: "#FF983D", accentColorForeground: darkGrey },
  pink: { accentColor: "#FF7AB8", accentColorForeground: darkGrey },
  purple: { accentColor: "#7A70FF", accentColorForeground: "#FFF" },
  red: { accentColor: "#FF6257", accentColorForeground: "#FFF" }
};
var defaultAccentColor = accentColors.blue;
var darkTheme = ({
  accentColor = defaultAccentColor.accentColor,
  accentColorForeground = defaultAccentColor.accentColorForeground,
  ...baseThemeOptions
} = {}) => ({
  ...baseTheme(baseThemeOptions),
  colors: {
    accentColor,
    accentColorForeground,
    actionButtonBorder: "rgba(255, 255, 255, 0.04)",
    actionButtonBorderMobile: "rgba(255, 255, 255, 0.08)",
    actionButtonSecondaryBackground: "rgba(255, 255, 255, 0.08)",
    closeButton: "rgba(224, 232, 255, 0.6)",
    closeButtonBackground: "rgba(255, 255, 255, 0.08)",
    connectButtonBackground: darkGrey,
    connectButtonBackgroundError: "#FF494A",
    connectButtonInnerBackground: "linear-gradient(0deg, rgba(255, 255, 255, 0.075), rgba(255, 255, 255, 0.15))",
    connectButtonText: "#FFF",
    connectButtonTextError: "#FFF",
    connectionIndicator: "#30E000",
    downloadBottomCardBackground: "linear-gradient(126deg, rgba(0, 0, 0, 0) 9.49%, rgba(120, 120, 120, 0.2) 71.04%), #1A1B1F",
    downloadTopCardBackground: "linear-gradient(126deg, rgba(120, 120, 120, 0.2) 9.49%, rgba(0, 0, 0, 0) 71.04%), #1A1B1F",
    error: "#FF494A",
    generalBorder: "rgba(255, 255, 255, 0.08)",
    generalBorderDim: "rgba(255, 255, 255, 0.04)",
    menuItemBackground: "rgba(224, 232, 255, 0.1)",
    modalBackdrop: "rgba(0, 0, 0, 0.5)",
    modalBackground: "#1A1B1F",
    modalBorder: "rgba(255, 255, 255, 0.08)",
    modalText: "#FFF",
    modalTextDim: "rgba(224, 232, 255, 0.3)",
    modalTextSecondary: "rgba(255, 255, 255, 0.6)",
    profileAction: "rgba(224, 232, 255, 0.1)",
    profileActionHover: "rgba(224, 232, 255, 0.2)",
    profileForeground: "rgba(224, 232, 255, 0.05)",
    selectedOptionBorder: "rgba(224, 232, 255, 0.1)",
    standby: "#FFD641"
  },
  shadows: {
    connectButton: "0px 4px 12px rgba(0, 0, 0, 0.1)",
    dialog: "0px 8px 32px rgba(0, 0, 0, 0.32)",
    profileDetailsAction: "0px 2px 6px rgba(37, 41, 46, 0.04)",
    selectedOption: "0px 2px 6px rgba(0, 0, 0, 0.24)",
    selectedWallet: "0px 2px 6px rgba(0, 0, 0, 0.24)",
    walletLogo: "0px 2px 16px rgba(0, 0, 0, 0.16)"
  }
});
darkTheme.accentColors = accentColors;

// node_modules/@rainbow-me/rainbowkit/dist/chunk-Q4HVINFA.js
var accentColors2 = {
  blue: { accentColor: "#0E76FD", accentColorForeground: "#FFF" },
  green: { accentColor: "#1DB847", accentColorForeground: "#FFF" },
  orange: { accentColor: "#FF801F", accentColorForeground: "#FFF" },
  pink: { accentColor: "#FF5CA0", accentColorForeground: "#FFF" },
  purple: { accentColor: "#5F5AFA", accentColorForeground: "#FFF" },
  red: { accentColor: "#FA423C", accentColorForeground: "#FFF" }
};
var defaultAccentColor2 = accentColors2.blue;
var lightTheme = ({
  accentColor = defaultAccentColor2.accentColor,
  accentColorForeground = defaultAccentColor2.accentColorForeground,
  ...baseThemeOptions
} = {}) => ({
  ...baseTheme(baseThemeOptions),
  colors: {
    accentColor,
    accentColorForeground,
    actionButtonBorder: "rgba(0, 0, 0, 0.04)",
    actionButtonBorderMobile: "rgba(0, 0, 0, 0.06)",
    actionButtonSecondaryBackground: "rgba(0, 0, 0, 0.06)",
    closeButton: "rgba(60, 66, 66, 0.8)",
    closeButtonBackground: "rgba(0, 0, 0, 0.06)",
    connectButtonBackground: "#FFF",
    connectButtonBackgroundError: "#FF494A",
    connectButtonInnerBackground: "linear-gradient(0deg, rgba(0, 0, 0, 0.03), rgba(0, 0, 0, 0.06))",
    connectButtonText: "#25292E",
    connectButtonTextError: "#FFF",
    connectionIndicator: "#30E000",
    downloadBottomCardBackground: "linear-gradient(126deg, rgba(255, 255, 255, 0) 9.49%, rgba(171, 171, 171, 0.04) 71.04%), #FFFFFF",
    downloadTopCardBackground: "linear-gradient(126deg, rgba(171, 171, 171, 0.2) 9.49%, rgba(255, 255, 255, 0) 71.04%), #FFFFFF",
    error: "#FF494A",
    generalBorder: "rgba(0, 0, 0, 0.06)",
    generalBorderDim: "rgba(0, 0, 0, 0.03)",
    menuItemBackground: "rgba(60, 66, 66, 0.1)",
    modalBackdrop: "rgba(0, 0, 0, 0.3)",
    modalBackground: "#FFF",
    modalBorder: "transparent",
    modalText: "#25292E",
    modalTextDim: "rgba(60, 66, 66, 0.3)",
    modalTextSecondary: "rgba(60, 66, 66, 0.6)",
    profileAction: "#FFF",
    profileActionHover: "rgba(255, 255, 255, 0.5)",
    profileForeground: "rgba(60, 66, 66, 0.06)",
    selectedOptionBorder: "rgba(60, 66, 66, 0.1)",
    standby: "#FFD641"
  },
  shadows: {
    connectButton: "0px 4px 12px rgba(0, 0, 0, 0.1)",
    dialog: "0px 8px 32px rgba(0, 0, 0, 0.32)",
    profileDetailsAction: "0px 2px 6px rgba(37, 41, 46, 0.04)",
    selectedOption: "0px 2px 6px rgba(0, 0, 0, 0.24)",
    selectedWallet: "0px 2px 6px rgba(0, 0, 0, 0.12)",
    walletLogo: "0px 2px 16px rgba(0, 0, 0, 0.16)"
  }
});
lightTheme.accentColors = accentColors2;

// node_modules/@rainbow-me/rainbowkit/dist/chunk-WP6TGA7H.js
var accentColors3 = {
  blue: { accentColor: "#3898FF", accentColorForeground: "#FFF" },
  green: { accentColor: "#4BD166", accentColorForeground: "#000" },
  orange: { accentColor: "#FF983D", accentColorForeground: "#000" },
  pink: { accentColor: "#FF7AB8", accentColorForeground: "#000" },
  purple: { accentColor: "#7A70FF", accentColorForeground: "#FFF" },
  red: { accentColor: "#FF6257", accentColorForeground: "#FFF" }
};
var defaultAccentColor3 = accentColors3.blue;
var midnightTheme = ({
  accentColor = defaultAccentColor3.accentColor,
  accentColorForeground = defaultAccentColor3.accentColorForeground,
  ...baseThemeOptions
} = {}) => ({
  ...baseTheme(baseThemeOptions),
  colors: {
    accentColor,
    accentColorForeground,
    actionButtonBorder: "rgba(255, 255, 255, 0.04)",
    actionButtonBorderMobile: "rgba(255, 255, 255, 0.1)",
    actionButtonSecondaryBackground: "rgba(255, 255, 255, 0.08)",
    closeButton: "rgba(255, 255, 255, 0.7)",
    closeButtonBackground: "rgba(255, 255, 255, 0.08)",
    connectButtonBackground: "#000",
    connectButtonBackgroundError: "#FF494A",
    connectButtonInnerBackground: "linear-gradient(0deg, rgba(255, 255, 255, 0.06), rgba(255, 255, 255, 0.12))",
    connectButtonText: "#FFF",
    connectButtonTextError: "#FFF",
    connectionIndicator: "#30E000",
    downloadBottomCardBackground: "linear-gradient(126deg, rgba(0, 0, 0, 0) 9.49%, rgba(120, 120, 120, 0.1) 71.04%), #050505",
    downloadTopCardBackground: "linear-gradient(126deg, rgba(120, 120, 120, 0.1) 9.49%, rgba(0, 0, 0, 0) 71.04%), #050505",
    error: "#FF494A",
    generalBorder: "rgba(255, 255, 255, 0.08)",
    generalBorderDim: "rgba(255, 255, 255, 0.04)",
    menuItemBackground: "rgba(255, 255, 255, 0.08)",
    modalBackdrop: "rgba(0, 0, 0, 0.7)",
    modalBackground: "#000",
    modalBorder: "rgba(255, 255, 255, 0.08)",
    modalText: "#FFF",
    modalTextDim: "rgba(255, 255, 255, 0.2)",
    modalTextSecondary: "rgba(255, 255, 255, 0.6)",
    profileAction: "rgba(255, 255, 255, 0.1)",
    profileActionHover: "rgba(255, 255, 255, 0.2)",
    profileForeground: "rgba(255, 255, 255, 0.06)",
    selectedOptionBorder: "rgba(224, 232, 255, 0.1)",
    standby: "#FFD641"
  },
  shadows: {
    connectButton: "0px 4px 12px rgba(0, 0, 0, 0.1)",
    dialog: "0px 8px 32px rgba(0, 0, 0, 0.32)",
    profileDetailsAction: "0px 2px 6px rgba(37, 41, 46, 0.04)",
    selectedOption: "0px 2px 6px rgba(0, 0, 0, 0.24)",
    selectedWallet: "0px 2px 6px rgba(0, 0, 0, 0.24)",
    walletLogo: "0px 2px 16px rgba(0, 0, 0, 0.16)"
  }
});
midnightTheme.accentColors = accentColors3;

// node_modules/@rainbow-me/rainbowkit/dist/index.js
var import_react4 = __toESM(require_react());

// node_modules/@vanilla-extract/css/functionSerializer/dist/vanilla-extract-css-functionSerializer.browser.esm.js
function addFunctionSerializer(target, recipe) {
  Object.defineProperty(target, "__recipe__", {
    value: recipe,
    writable: false
  });
  return target;
}

// node_modules/@vanilla-extract/css/recipe/dist/vanilla-extract-css-recipe.browser.esm.js
var addRecipe = addFunctionSerializer;

// node_modules/@vanilla-extract/sprinkles/createUtils/dist/vanilla-extract-sprinkles-createUtils.esm.js
function createNormalizeValueFn(properties) {
  var {
    conditions
  } = properties;
  if (!conditions) {
    throw new Error("Styles have no conditions");
  }
  function normalizeValue(value) {
    if (typeof value === "string" || typeof value === "number" || typeof value === "boolean") {
      if (!conditions.defaultCondition) {
        throw new Error("No default condition");
      }
      return {
        [conditions.defaultCondition]: value
      };
    }
    if (Array.isArray(value)) {
      if (!("responsiveArray" in conditions)) {
        throw new Error("Responsive arrays are not supported");
      }
      var returnValue = {};
      for (var index in conditions.responsiveArray) {
        if (value[index] != null) {
          returnValue[conditions.responsiveArray[index]] = value[index];
        }
      }
      return returnValue;
    }
    return value;
  }
  return addRecipe(normalizeValue, {
    importPath: "@vanilla-extract/sprinkles/createUtils",
    importName: "createNormalizeValueFn",
    args: [{
      conditions: properties.conditions
    }]
  });
}
function createMapValueFn(properties) {
  var {
    conditions
  } = properties;
  if (!conditions) {
    throw new Error("Styles have no conditions");
  }
  var normalizeValue = createNormalizeValueFn(properties);
  function mapValue(value, mapFn) {
    if (typeof value === "string" || typeof value === "number" || typeof value === "boolean") {
      if (!conditions.defaultCondition) {
        throw new Error("No default condition");
      }
      return mapFn(value, conditions.defaultCondition);
    }
    var normalizedObject = Array.isArray(value) ? normalizeValue(value) : value;
    var mappedObject = {};
    for (var _key in normalizedObject) {
      if (normalizedObject[_key] != null) {
        mappedObject[_key] = mapFn(normalizedObject[_key], _key);
      }
    }
    return mappedObject;
  }
  return addRecipe(mapValue, {
    importPath: "@vanilla-extract/sprinkles/createUtils",
    importName: "createMapValueFn",
    args: [{
      conditions: properties.conditions
    }]
  });
}

// node_modules/@vanilla-extract/sprinkles/dist/createSprinkles-c8550e00.esm.js
function _toPrimitive(input, hint) {
  if (typeof input !== "object" || input === null)
    return input;
  var prim = input[Symbol.toPrimitive];
  if (prim !== void 0) {
    var res = prim.call(input, hint || "default");
    if (typeof res !== "object")
      return res;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (hint === "string" ? String : Number)(input);
}
function _toPropertyKey(arg) {
  var key = _toPrimitive(arg, "string");
  return typeof key === "symbol" ? key : String(key);
}
function _defineProperty(obj, key, value) {
  key = _toPropertyKey(key);
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
var createSprinkles = (composeStyles2) => function() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  var sprinklesStyles = Object.assign({}, ...args.map((a) => a.styles));
  var sprinklesKeys = Object.keys(sprinklesStyles);
  var shorthandNames = sprinklesKeys.filter((property) => "mappings" in sprinklesStyles[property]);
  var sprinklesFn = (props) => {
    var classNames = [];
    var shorthands = {};
    var nonShorthands = _objectSpread2({}, props);
    var hasShorthands = false;
    for (var shorthand of shorthandNames) {
      var value = props[shorthand];
      if (value != null) {
        var sprinkle = sprinklesStyles[shorthand];
        hasShorthands = true;
        for (var propMapping of sprinkle.mappings) {
          shorthands[propMapping] = value;
          if (nonShorthands[propMapping] == null) {
            delete nonShorthands[propMapping];
          }
        }
      }
    }
    var finalProps = hasShorthands ? _objectSpread2(_objectSpread2({}, shorthands), nonShorthands) : props;
    var _loop = function _loop2() {
      var propValue = finalProps[prop];
      var sprinkle2 = sprinklesStyles[prop];
      try {
        if (sprinkle2.mappings) {
          return "continue";
        }
        if (typeof propValue === "string" || typeof propValue === "number") {
          if (true) {
            if (!sprinkle2.values[propValue].defaultClass) {
              throw new Error();
            }
          }
          classNames.push(sprinkle2.values[propValue].defaultClass);
        } else if (Array.isArray(propValue)) {
          for (var responsiveIndex = 0; responsiveIndex < propValue.length; responsiveIndex++) {
            var responsiveValue = propValue[responsiveIndex];
            if (responsiveValue != null) {
              var conditionName = sprinkle2.responsiveArray[responsiveIndex];
              if (true) {
                if (!sprinkle2.values[responsiveValue].conditions[conditionName]) {
                  throw new Error();
                }
              }
              classNames.push(sprinkle2.values[responsiveValue].conditions[conditionName]);
            }
          }
        } else {
          for (var _conditionName in propValue) {
            var _value = propValue[_conditionName];
            if (_value != null) {
              if (true) {
                if (!sprinkle2.values[_value].conditions[_conditionName]) {
                  throw new Error();
                }
              }
              classNames.push(sprinkle2.values[_value].conditions[_conditionName]);
            }
          }
        }
      } catch (e) {
        if (true) {
          class SprinklesError extends Error {
            constructor(message) {
              super(message);
              this.name = "SprinklesError";
            }
          }
          var format = (v) => typeof v === "string" ? '"'.concat(v, '"') : v;
          var invalidPropValue = (prop2, value2, possibleValues) => {
            throw new SprinklesError('"'.concat(prop2, '" has no value ').concat(format(value2), ". Possible values are ").concat(Object.keys(possibleValues).map(format).join(", ")));
          };
          if (!sprinkle2) {
            throw new SprinklesError('"'.concat(prop, '" is not a valid sprinkle'));
          }
          if (typeof propValue === "string" || typeof propValue === "number") {
            if (!(propValue in sprinkle2.values)) {
              invalidPropValue(prop, propValue, sprinkle2.values);
            }
            if (!sprinkle2.values[propValue].defaultClass) {
              throw new SprinklesError('"'.concat(prop, '" has no default condition. You must specify which conditions to target explicitly. Possible options are ').concat(Object.keys(sprinkle2.values[propValue].conditions).map(format).join(", ")));
            }
          }
          if (typeof propValue === "object") {
            if (!("conditions" in sprinkle2.values[Object.keys(sprinkle2.values)[0]])) {
              throw new SprinklesError('"'.concat(prop, '" is not a conditional property'));
            }
            if (Array.isArray(propValue)) {
              if (!("responsiveArray" in sprinkle2)) {
                throw new SprinklesError('"'.concat(prop, '" does not support responsive arrays'));
              }
              var breakpointCount = sprinkle2.responsiveArray.length;
              if (breakpointCount < propValue.length) {
                throw new SprinklesError('"'.concat(prop, '" only supports up to ').concat(breakpointCount, " breakpoints. You passed ").concat(propValue.length));
              }
              for (var _responsiveValue of propValue) {
                if (!sprinkle2.values[_responsiveValue]) {
                  invalidPropValue(prop, _responsiveValue, sprinkle2.values);
                }
              }
            } else {
              for (var _conditionName2 in propValue) {
                var _value2 = propValue[_conditionName2];
                if (_value2 != null) {
                  if (!sprinkle2.values[_value2]) {
                    invalidPropValue(prop, _value2, sprinkle2.values);
                  }
                  if (!sprinkle2.values[_value2].conditions[_conditionName2]) {
                    throw new SprinklesError('"'.concat(prop, '" has no condition named ').concat(format(_conditionName2), ". Possible values are ").concat(Object.keys(sprinkle2.values[_value2].conditions).map(format).join(", ")));
                  }
                }
              }
            }
          }
        }
        throw e;
      }
    };
    for (var prop in finalProps) {
      var _ret = _loop();
      if (_ret === "continue")
        continue;
    }
    return composeStyles2(classNames.join(" "));
  };
  return Object.assign(sprinklesFn, {
    properties: new Set(sprinklesKeys)
  });
};

// node_modules/@vanilla-extract/sprinkles/createRuntimeSprinkles/dist/vanilla-extract-sprinkles-createRuntimeSprinkles.esm.js
var composeStyles = (classList) => classList;
var createSprinkles2 = function createSprinkles3() {
  return createSprinkles(composeStyles)(...arguments);
};

// node_modules/@rainbow-me/rainbowkit/dist/index.js
var import_react5 = __toESM(require_react());
var import_react6 = __toESM(require_react());

// node_modules/clsx/dist/clsx.mjs
function r(e) {
  var t, f, n = "";
  if ("string" == typeof e || "number" == typeof e)
    n += e;
  else if ("object" == typeof e)
    if (Array.isArray(e)) {
      var o = e.length;
      for (t = 0; t < o; t++)
        e[t] && (f = r(e[t])) && (n && (n += " "), n += f);
    } else
      for (f in e)
        e[f] && (n && (n += " "), n += f);
  return n;
}
function clsx() {
  for (var e, t, f = 0, n = "", o = arguments.length; f < o; f++)
    (e = arguments[f]) && (t = r(e)) && (n && (n += " "), n += t);
  return n;
}
var clsx_default = clsx;

// node_modules/@rainbow-me/rainbowkit/dist/index.js
var React22 = __toESM(require_react());
var import_react7 = __toESM(require_react());
var import_react8 = __toESM(require_react());
var import_react9 = __toESM(require_react());
var import_react10 = __toESM(require_react());
var import_react11 = __toESM(require_react());
var import_react12 = __toESM(require_react());
var import_react13 = __toESM(require_react());
var import_react14 = __toESM(require_react());
var import_react15 = __toESM(require_react());
var import_react16 = __toESM(require_react());
var import_react17 = __toESM(require_react());
var import_react18 = __toESM(require_react());
var import_react19 = __toESM(require_react());
var import_react20 = __toESM(require_react());
var import_react21 = __toESM(require_react());
var import_react22 = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());

// node_modules/react-remove-scroll/dist/es2015/Combination.js
init_tslib_es6();
var React9 = __toESM(require_react());

// node_modules/react-remove-scroll/dist/es2015/UI.js
init_tslib_es6();
var React5 = __toESM(require_react());

// node_modules/react-remove-scroll-bar/dist/es2015/constants.js
var zeroRightClassName = "right-scroll-bar-position";
var fullWidthClassName = "width-before-scroll-bar";
var noScrollbarsClassName = "with-scroll-bars-hidden";
var removedBarSizeVariable = "--removed-body-scroll-bar-size";

// node_modules/use-callback-ref/dist/es2015/assignRef.js
function assignRef(ref, value) {
  if (typeof ref === "function") {
    ref(value);
  } else if (ref) {
    ref.current = value;
  }
  return ref;
}

// node_modules/use-callback-ref/dist/es2015/useRef.js
var import_react = __toESM(require_react());
function useCallbackRef(initialValue, callback) {
  var ref = (0, import_react.useState)(function() {
    return {
      // value
      value: initialValue,
      // last callback
      callback,
      // "memoized" public interface
      facade: {
        get current() {
          return ref.value;
        },
        set current(value) {
          var last = ref.value;
          if (last !== value) {
            ref.value = value;
            ref.callback(value, last);
          }
        }
      }
    };
  })[0];
  ref.callback = callback;
  return ref.facade;
}

// node_modules/use-callback-ref/dist/es2015/useMergeRef.js
var React = __toESM(require_react());
var useIsomorphicLayoutEffect = typeof window !== "undefined" ? React.useLayoutEffect : React.useEffect;
var currentValues = /* @__PURE__ */ new WeakMap();
function useMergeRefs(refs, defaultValue) {
  var callbackRef = useCallbackRef(defaultValue || null, function(newValue) {
    return refs.forEach(function(ref) {
      return assignRef(ref, newValue);
    });
  });
  useIsomorphicLayoutEffect(function() {
    var oldValue = currentValues.get(callbackRef);
    if (oldValue) {
      var prevRefs_1 = new Set(oldValue);
      var nextRefs_1 = new Set(refs);
      var current_1 = callbackRef.current;
      prevRefs_1.forEach(function(ref) {
        if (!nextRefs_1.has(ref)) {
          assignRef(ref, null);
        }
      });
      nextRefs_1.forEach(function(ref) {
        if (!prevRefs_1.has(ref)) {
          assignRef(ref, current_1);
        }
      });
    }
    currentValues.set(callbackRef, refs);
  }, [refs]);
  return callbackRef;
}

// node_modules/use-sidecar/dist/es2015/hoc.js
init_tslib_es6();
var React2 = __toESM(require_react());

// node_modules/use-sidecar/dist/es2015/hook.js
var import_react2 = __toESM(require_react());

// node_modules/use-sidecar/dist/es2015/medium.js
init_tslib_es6();
function ItoI(a) {
  return a;
}
function innerCreateMedium(defaults, middleware) {
  if (middleware === void 0) {
    middleware = ItoI;
  }
  var buffer = [];
  var assigned = false;
  var medium = {
    read: function() {
      if (assigned) {
        throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");
      }
      if (buffer.length) {
        return buffer[buffer.length - 1];
      }
      return defaults;
    },
    useMedium: function(data) {
      var item = middleware(data, assigned);
      buffer.push(item);
      return function() {
        buffer = buffer.filter(function(x) {
          return x !== item;
        });
      };
    },
    assignSyncMedium: function(cb) {
      assigned = true;
      while (buffer.length) {
        var cbs = buffer;
        buffer = [];
        cbs.forEach(cb);
      }
      buffer = {
        push: function(x) {
          return cb(x);
        },
        filter: function() {
          return buffer;
        }
      };
    },
    assignMedium: function(cb) {
      assigned = true;
      var pendingQueue = [];
      if (buffer.length) {
        var cbs = buffer;
        buffer = [];
        cbs.forEach(cb);
        pendingQueue = buffer;
      }
      var executeQueue = function() {
        var cbs2 = pendingQueue;
        pendingQueue = [];
        cbs2.forEach(cb);
      };
      var cycle = function() {
        return Promise.resolve().then(executeQueue);
      };
      cycle();
      buffer = {
        push: function(x) {
          pendingQueue.push(x);
          cycle();
        },
        filter: function(filter) {
          pendingQueue = pendingQueue.filter(filter);
          return buffer;
        }
      };
    }
  };
  return medium;
}
function createSidecarMedium(options) {
  if (options === void 0) {
    options = {};
  }
  var medium = innerCreateMedium(null);
  medium.options = __assign({ async: true, ssr: false }, options);
  return medium;
}

// node_modules/use-sidecar/dist/es2015/renderProp.js
init_tslib_es6();
var React3 = __toESM(require_react());
var import_react3 = __toESM(require_react());

// node_modules/use-sidecar/dist/es2015/exports.js
init_tslib_es6();
var React4 = __toESM(require_react());
var SideCar = function(_a) {
  var sideCar = _a.sideCar, rest = __rest(_a, ["sideCar"]);
  if (!sideCar) {
    throw new Error("Sidecar: please provide `sideCar` property to import the right car");
  }
  var Target = sideCar.read();
  if (!Target) {
    throw new Error("Sidecar medium not found");
  }
  return React4.createElement(Target, __assign({}, rest));
};
SideCar.isSideCarExport = true;
function exportSidecar(medium, exported) {
  medium.useMedium(exported);
  return SideCar;
}

// node_modules/react-remove-scroll/dist/es2015/medium.js
var effectCar = createSidecarMedium();

// node_modules/react-remove-scroll/dist/es2015/UI.js
var nothing = function() {
  return;
};
var RemoveScroll = React5.forwardRef(function(props, parentRef) {
  var ref = React5.useRef(null);
  var _a = React5.useState({
    onScrollCapture: nothing,
    onWheelCapture: nothing,
    onTouchMoveCapture: nothing
  }), callbacks = _a[0], setCallbacks = _a[1];
  var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? "div" : _b, gapMode = props.gapMode, rest = __rest(props, ["forwardProps", "children", "className", "removeScrollBar", "enabled", "shards", "sideCar", "noIsolation", "inert", "allowPinchZoom", "as", "gapMode"]);
  var SideCar2 = sideCar;
  var containerRef = useMergeRefs([ref, parentRef]);
  var containerProps = __assign(__assign({}, rest), callbacks);
  return React5.createElement(
    React5.Fragment,
    null,
    enabled && React5.createElement(SideCar2, { sideCar: effectCar, removeScrollBar, shards, noIsolation, inert, setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode }),
    forwardProps ? React5.cloneElement(React5.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef })) : React5.createElement(Container, __assign({}, containerProps, { className, ref: containerRef }), children)
  );
});
RemoveScroll.defaultProps = {
  enabled: true,
  removeScrollBar: true,
  inert: false
};
RemoveScroll.classNames = {
  fullWidth: fullWidthClassName,
  zeroRight: zeroRightClassName
};

// node_modules/react-remove-scroll/dist/es2015/SideEffect.js
init_tslib_es6();
var React8 = __toESM(require_react());

// node_modules/react-remove-scroll-bar/dist/es2015/component.js
var React7 = __toESM(require_react());

// node_modules/react-style-singleton/dist/es2015/hook.js
var React6 = __toESM(require_react());

// node_modules/get-nonce/dist/es2015/index.js
var currentNonce;
var getNonce = function() {
  if (currentNonce) {
    return currentNonce;
  }
  if (typeof __webpack_nonce__ !== "undefined") {
    return __webpack_nonce__;
  }
  return void 0;
};

// node_modules/react-style-singleton/dist/es2015/singleton.js
function makeStyleTag() {
  if (!document)
    return null;
  var tag = document.createElement("style");
  tag.type = "text/css";
  var nonce = getNonce();
  if (nonce) {
    tag.setAttribute("nonce", nonce);
  }
  return tag;
}
function injectStyles(tag, css) {
  if (tag.styleSheet) {
    tag.styleSheet.cssText = css;
  } else {
    tag.appendChild(document.createTextNode(css));
  }
}
function insertStyleTag(tag) {
  var head = document.head || document.getElementsByTagName("head")[0];
  head.appendChild(tag);
}
var stylesheetSingleton = function() {
  var counter = 0;
  var stylesheet = null;
  return {
    add: function(style) {
      if (counter == 0) {
        if (stylesheet = makeStyleTag()) {
          injectStyles(stylesheet, style);
          insertStyleTag(stylesheet);
        }
      }
      counter++;
    },
    remove: function() {
      counter--;
      if (!counter && stylesheet) {
        stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);
        stylesheet = null;
      }
    }
  };
};

// node_modules/react-style-singleton/dist/es2015/hook.js
var styleHookSingleton = function() {
  var sheet = stylesheetSingleton();
  return function(styles, isDynamic) {
    React6.useEffect(function() {
      sheet.add(styles);
      return function() {
        sheet.remove();
      };
    }, [styles && isDynamic]);
  };
};

// node_modules/react-style-singleton/dist/es2015/component.js
var styleSingleton = function() {
  var useStyle = styleHookSingleton();
  var Sheet = function(_a) {
    var styles = _a.styles, dynamic = _a.dynamic;
    useStyle(styles, dynamic);
    return null;
  };
  return Sheet;
};

// node_modules/react-remove-scroll-bar/dist/es2015/utils.js
var zeroGap = {
  left: 0,
  top: 0,
  right: 0,
  gap: 0
};
var parse = function(x) {
  return parseInt(x || "", 10) || 0;
};
var getOffset = function(gapMode) {
  var cs = window.getComputedStyle(document.body);
  var left = cs[gapMode === "padding" ? "paddingLeft" : "marginLeft"];
  var top = cs[gapMode === "padding" ? "paddingTop" : "marginTop"];
  var right = cs[gapMode === "padding" ? "paddingRight" : "marginRight"];
  return [parse(left), parse(top), parse(right)];
};
var getGapWidth = function(gapMode) {
  if (gapMode === void 0) {
    gapMode = "margin";
  }
  if (typeof window === "undefined") {
    return zeroGap;
  }
  var offsets = getOffset(gapMode);
  var documentWidth = document.documentElement.clientWidth;
  var windowWidth = window.innerWidth;
  return {
    left: offsets[0],
    top: offsets[1],
    right: offsets[2],
    gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0])
  };
};

// node_modules/react-remove-scroll-bar/dist/es2015/component.js
var Style = styleSingleton();
var lockAttribute = "data-scroll-locked";
var getStyles = function(_a, allowRelative, gapMode, important) {
  var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;
  if (gapMode === void 0) {
    gapMode = "margin";
  }
  return "\n  .".concat(noScrollbarsClassName, " {\n   overflow: hidden ").concat(important, ";\n   padding-right: ").concat(gap, "px ").concat(important, ";\n  }\n  body[").concat(lockAttribute, "] {\n    overflow: hidden ").concat(important, ";\n    overscroll-behavior: contain;\n    ").concat([
    allowRelative && "position: relative ".concat(important, ";"),
    gapMode === "margin" && "\n    padding-left: ".concat(left, "px;\n    padding-top: ").concat(top, "px;\n    padding-right: ").concat(right, "px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(gap, "px ").concat(important, ";\n    "),
    gapMode === "padding" && "padding-right: ".concat(gap, "px ").concat(important, ";")
  ].filter(Boolean).join(""), "\n  }\n  \n  .").concat(zeroRightClassName, " {\n    right: ").concat(gap, "px ").concat(important, ";\n  }\n  \n  .").concat(fullWidthClassName, " {\n    margin-right: ").concat(gap, "px ").concat(important, ";\n  }\n  \n  .").concat(zeroRightClassName, " .").concat(zeroRightClassName, " {\n    right: 0 ").concat(important, ";\n  }\n  \n  .").concat(fullWidthClassName, " .").concat(fullWidthClassName, " {\n    margin-right: 0 ").concat(important, ";\n  }\n  \n  body[").concat(lockAttribute, "] {\n    ").concat(removedBarSizeVariable, ": ").concat(gap, "px;\n  }\n");
};
var getCurrentUseCounter = function() {
  var counter = parseInt(document.body.getAttribute(lockAttribute) || "0", 10);
  return isFinite(counter) ? counter : 0;
};
var useLockAttribute = function() {
  React7.useEffect(function() {
    document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());
    return function() {
      var newCounter = getCurrentUseCounter() - 1;
      if (newCounter <= 0) {
        document.body.removeAttribute(lockAttribute);
      } else {
        document.body.setAttribute(lockAttribute, newCounter.toString());
      }
    };
  }, []);
};
var RemoveScrollBar = function(_a) {
  var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? "margin" : _b;
  useLockAttribute();
  var gap = React7.useMemo(function() {
    return getGapWidth(gapMode);
  }, [gapMode]);
  return React7.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? "!important" : "") });
};

// node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js
var passiveSupported = false;
if (typeof window !== "undefined") {
  try {
    options = Object.defineProperty({}, "passive", {
      get: function() {
        passiveSupported = true;
        return true;
      }
    });
    window.addEventListener("test", options, options);
    window.removeEventListener("test", options, options);
  } catch (err) {
    passiveSupported = false;
  }
}
var options;
var nonPassive = passiveSupported ? { passive: false } : false;

// node_modules/react-remove-scroll/dist/es2015/handleScroll.js
var alwaysContainsScroll = function(node) {
  return node.tagName === "TEXTAREA";
};
var elementCanBeScrolled = function(node, overflow) {
  var styles = window.getComputedStyle(node);
  return (
    // not-not-scrollable
    styles[overflow] !== "hidden" && // contains scroll inside self
    !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === "visible")
  );
};
var elementCouldBeVScrolled = function(node) {
  return elementCanBeScrolled(node, "overflowY");
};
var elementCouldBeHScrolled = function(node) {
  return elementCanBeScrolled(node, "overflowX");
};
var locationCouldBeScrolled = function(axis, node) {
  var ownerDocument = node.ownerDocument;
  var current = node;
  do {
    if (typeof ShadowRoot !== "undefined" && current instanceof ShadowRoot) {
      current = current.host;
    }
    var isScrollable = elementCouldBeScrolled(axis, current);
    if (isScrollable) {
      var _a = getScrollVariables(axis, current), s = _a[1], d = _a[2];
      if (s > d) {
        return true;
      }
    }
    current = current.parentNode;
  } while (current && current !== ownerDocument.body);
  return false;
};
var getVScrollVariables = function(_a) {
  var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;
  return [
    scrollTop,
    scrollHeight,
    clientHeight
  ];
};
var getHScrollVariables = function(_a) {
  var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;
  return [
    scrollLeft,
    scrollWidth,
    clientWidth
  ];
};
var elementCouldBeScrolled = function(axis, node) {
  return axis === "v" ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);
};
var getScrollVariables = function(axis, node) {
  return axis === "v" ? getVScrollVariables(node) : getHScrollVariables(node);
};
var getDirectionFactor = function(axis, direction) {
  return axis === "h" && direction === "rtl" ? -1 : 1;
};
var handleScroll = function(axis, endTarget, event, sourceDelta, noOverscroll) {
  var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);
  var delta = directionFactor * sourceDelta;
  var target = event.target;
  var targetInLock = endTarget.contains(target);
  var shouldCancelScroll = false;
  var isDeltaPositive = delta > 0;
  var availableScroll = 0;
  var availableScrollTop = 0;
  do {
    var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];
    var elementScroll = scroll_1 - capacity - directionFactor * position;
    if (position || elementScroll) {
      if (elementCouldBeScrolled(axis, target)) {
        availableScroll += elementScroll;
        availableScrollTop += position;
      }
    }
    if (target instanceof ShadowRoot) {
      target = target.host;
    } else {
      target = target.parentNode;
    }
  } while (
    // portaled content
    !targetInLock && target !== document.body || // self content
    targetInLock && (endTarget.contains(target) || endTarget === target)
  );
  if (isDeltaPositive && (noOverscroll && Math.abs(availableScroll) < 1 || !noOverscroll && delta > availableScroll)) {
    shouldCancelScroll = true;
  } else if (!isDeltaPositive && (noOverscroll && Math.abs(availableScrollTop) < 1 || !noOverscroll && -delta > availableScrollTop)) {
    shouldCancelScroll = true;
  }
  return shouldCancelScroll;
};

// node_modules/react-remove-scroll/dist/es2015/SideEffect.js
var getTouchXY = function(event) {
  return "changedTouches" in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];
};
var getDeltaXY = function(event) {
  return [event.deltaX, event.deltaY];
};
var extractRef = function(ref) {
  return ref && "current" in ref ? ref.current : ref;
};
var deltaCompare = function(x, y) {
  return x[0] === y[0] && x[1] === y[1];
};
var generateStyle = function(id) {
  return "\n  .block-interactivity-".concat(id, " {pointer-events: none;}\n  .allow-interactivity-").concat(id, " {pointer-events: all;}\n");
};
var idCounter = 0;
var lockStack = [];
function RemoveScrollSideCar(props) {
  var shouldPreventQueue = React8.useRef([]);
  var touchStartRef = React8.useRef([0, 0]);
  var activeAxis = React8.useRef();
  var id = React8.useState(idCounter++)[0];
  var Style2 = React8.useState(styleSingleton)[0];
  var lastProps = React8.useRef(props);
  React8.useEffect(function() {
    lastProps.current = props;
  }, [props]);
  React8.useEffect(function() {
    if (props.inert) {
      document.body.classList.add("block-interactivity-".concat(id));
      var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);
      allow_1.forEach(function(el) {
        return el.classList.add("allow-interactivity-".concat(id));
      });
      return function() {
        document.body.classList.remove("block-interactivity-".concat(id));
        allow_1.forEach(function(el) {
          return el.classList.remove("allow-interactivity-".concat(id));
        });
      };
    }
    return;
  }, [props.inert, props.lockRef.current, props.shards]);
  var shouldCancelEvent = React8.useCallback(function(event, parent) {
    if ("touches" in event && event.touches.length === 2) {
      return !lastProps.current.allowPinchZoom;
    }
    var touch = getTouchXY(event);
    var touchStart = touchStartRef.current;
    var deltaX = "deltaX" in event ? event.deltaX : touchStart[0] - touch[0];
    var deltaY = "deltaY" in event ? event.deltaY : touchStart[1] - touch[1];
    var currentAxis;
    var target = event.target;
    var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? "h" : "v";
    if ("touches" in event && moveDirection === "h" && target.type === "range") {
      return false;
    }
    var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);
    if (!canBeScrolledInMainDirection) {
      return true;
    }
    if (canBeScrolledInMainDirection) {
      currentAxis = moveDirection;
    } else {
      currentAxis = moveDirection === "v" ? "h" : "v";
      canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);
    }
    if (!canBeScrolledInMainDirection) {
      return false;
    }
    if (!activeAxis.current && "changedTouches" in event && (deltaX || deltaY)) {
      activeAxis.current = currentAxis;
    }
    if (!currentAxis) {
      return true;
    }
    var cancelingAxis = activeAxis.current || currentAxis;
    return handleScroll(cancelingAxis, parent, event, cancelingAxis === "h" ? deltaX : deltaY, true);
  }, []);
  var shouldPrevent = React8.useCallback(function(_event) {
    var event = _event;
    if (!lockStack.length || lockStack[lockStack.length - 1] !== Style2) {
      return;
    }
    var delta = "deltaY" in event ? getDeltaXY(event) : getTouchXY(event);
    var sourceEvent = shouldPreventQueue.current.filter(function(e) {
      return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta);
    })[0];
    if (sourceEvent && sourceEvent.should) {
      if (event.cancelable) {
        event.preventDefault();
      }
      return;
    }
    if (!sourceEvent) {
      var shardNodes = (lastProps.current.shards || []).map(extractRef).filter(Boolean).filter(function(node) {
        return node.contains(event.target);
      });
      var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;
      if (shouldStop) {
        if (event.cancelable) {
          event.preventDefault();
        }
      }
    }
  }, []);
  var shouldCancel = React8.useCallback(function(name, delta, target, should) {
    var event = { name, delta, target, should, shadowParent: getOutermostShadowParent(target) };
    shouldPreventQueue.current.push(event);
    setTimeout(function() {
      shouldPreventQueue.current = shouldPreventQueue.current.filter(function(e) {
        return e !== event;
      });
    }, 1);
  }, []);
  var scrollTouchStart = React8.useCallback(function(event) {
    touchStartRef.current = getTouchXY(event);
    activeAxis.current = void 0;
  }, []);
  var scrollWheel = React8.useCallback(function(event) {
    shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));
  }, []);
  var scrollTouchMove = React8.useCallback(function(event) {
    shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));
  }, []);
  React8.useEffect(function() {
    lockStack.push(Style2);
    props.setCallbacks({
      onScrollCapture: scrollWheel,
      onWheelCapture: scrollWheel,
      onTouchMoveCapture: scrollTouchMove
    });
    document.addEventListener("wheel", shouldPrevent, nonPassive);
    document.addEventListener("touchmove", shouldPrevent, nonPassive);
    document.addEventListener("touchstart", scrollTouchStart, nonPassive);
    return function() {
      lockStack = lockStack.filter(function(inst) {
        return inst !== Style2;
      });
      document.removeEventListener("wheel", shouldPrevent, nonPassive);
      document.removeEventListener("touchmove", shouldPrevent, nonPassive);
      document.removeEventListener("touchstart", scrollTouchStart, nonPassive);
    };
  }, []);
  var removeScrollBar = props.removeScrollBar, inert = props.inert;
  return React8.createElement(
    React8.Fragment,
    null,
    inert ? React8.createElement(Style2, { styles: generateStyle(id) }) : null,
    removeScrollBar ? React8.createElement(RemoveScrollBar, { gapMode: props.gapMode }) : null
  );
}
function getOutermostShadowParent(node) {
  var shadowParent = null;
  while (node !== null) {
    if (node instanceof ShadowRoot) {
      shadowParent = node.host;
      node = node.host;
    }
    node = node.parentNode;
  }
  return shadowParent;
}

// node_modules/react-remove-scroll/dist/es2015/sidecar.js
var sidecar_default = exportSidecar(effectCar, RemoveScrollSideCar);

// node_modules/react-remove-scroll/dist/es2015/Combination.js
var ReactRemoveScroll = React9.forwardRef(function(props, ref) {
  return React9.createElement(RemoveScroll, __assign({}, props, { ref, sideCar: sidecar_default }));
});
ReactRemoveScroll.classNames = RemoveScroll.classNames;
var Combination_default = ReactRemoveScroll;

// node_modules/@rainbow-me/rainbowkit/dist/index.js
var import_react23 = __toESM(require_react());

// node_modules/@vanilla-extract/private/dist/vanilla-extract-private.esm.js
function getVarName(variable) {
  var matches = variable.match(/^var\((.*)\)$/);
  if (matches) {
    return matches[1];
  }
  return variable;
}
function get(obj, path) {
  var result = obj;
  for (var key of path) {
    if (!(key in result)) {
      throw new Error("Path ".concat(path.join(" -> "), " does not exist in object"));
    }
    result = result[key];
  }
  return result;
}
function walkObject(obj, fn) {
  var path = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];
  var clone = {};
  for (var key in obj) {
    var _value = obj[key];
    var currentPath = [...path, key];
    if (typeof _value === "string" || typeof _value === "number" || _value == null) {
      clone[key] = fn(_value, currentPath);
    } else if (typeof _value === "object" && !Array.isArray(_value)) {
      clone[key] = walkObject(_value, fn, currentPath);
    } else {
      console.warn('Skipping invalid key "'.concat(currentPath.join("."), '". Should be a string, number, null or object. Received: "').concat(Array.isArray(_value) ? "Array" : typeof _value, '"'));
    }
  }
  return clone;
}

// node_modules/@vanilla-extract/dynamic/dist/vanilla-extract-dynamic.esm.js
function assignInlineVars(varsOrContract, tokens) {
  var styles = {};
  if (typeof tokens === "object") {
    var _contract = varsOrContract;
    walkObject(tokens, (value2, path) => {
      if (value2 == null) {
        return;
      }
      var varName2 = get(_contract, path);
      styles[getVarName(varName2)] = String(value2);
    });
  } else {
    var _vars = varsOrContract;
    for (var varName in _vars) {
      var value = _vars[varName];
      if (value == null) {
        continue;
      }
      styles[getVarName(varName)] = value;
    }
  }
  Object.defineProperty(styles, "toString", {
    value: function value2() {
      return Object.keys(this).map((key) => "".concat(key, ":").concat(this[key])).join(";");
    },
    writable: false
  });
  return styles;
}

// node_modules/@rainbow-me/rainbowkit/dist/index.js
var import_react24 = __toESM(require_react());
var import_react25 = __toESM(require_react());
var import_react26 = __toESM(require_react());
var import_react27 = __toESM(require_react());
var import_react28 = __toESM(require_react());
var import_react29 = __toESM(require_react());
var import_react30 = __toESM(require_react());
var import_react31 = __toESM(require_react());
var import_ua_parser_js = __toESM(require_ua_parser());
var import_react32 = __toESM(require_react());
var import_react33 = __toESM(require_react());
var import_react34 = __toESM(require_react());
var import_react35 = __toESM(require_react());
var import_react36 = __toESM(require_react());
var import_react37 = __toESM(require_react());
var import_react38 = __toESM(require_react());
var import_react39 = __toESM(require_react());
var import_react40 = __toESM(require_react());
var import_react41 = __toESM(require_react());
var import_react42 = __toESM(require_react());
var import_react43 = __toESM(require_react());
var import_react44 = __toESM(require_react());
var import_react45 = __toESM(require_react());
var import_react46 = __toESM(require_react());
var import_react47 = __toESM(require_react());
var import_react48 = __toESM(require_react());
var import_react49 = __toESM(require_react());
var import_react50 = __toESM(require_react());
var import_react51 = __toESM(require_react());
var import_react52 = __toESM(require_react());
var import_react53 = __toESM(require_react());
var import_react54 = __toESM(require_react());
var import_react55 = __toESM(require_react());
var import_react56 = __toESM(require_react());
var import_react57 = __toESM(require_react());
var import_react58 = __toESM(require_react());
var import_react59 = __toESM(require_react());
var import_react60 = __toESM(require_react());
var import_react61 = __toESM(require_react());
var import_react62 = __toESM(require_react());
var import_react63 = __toESM(require_react());
var import_react64 = __toESM(require_react());
var import_react65 = __toESM(require_react());
var import_react66 = __toESM(require_react());
var import_react67 = __toESM(require_react());
var import_react68 = __toESM(require_react());
var import_react69 = __toESM(require_react());
var import_react70 = __toESM(require_react());
var import_qrcode = __toESM(require_browser());
var import_react71 = __toESM(require_react());
var import_react72 = __toESM(require_react());
var import_react73 = __toESM(require_react());
var import_react74 = __toESM(require_react());
var import_react75 = __toESM(require_react());
var import_react76 = __toESM(require_react());
var largeScreenMinWidth = 768;
var mapResponsiveValue = createMapValueFn({ conditions: { defaultCondition: "smallScreen", conditionNames: ["smallScreen", "largeScreen"], responsiveArray: void 0 } });
var normalizeResponsiveValue = createNormalizeValueFn({ conditions: { defaultCondition: "smallScreen", conditionNames: ["smallScreen", "largeScreen"], responsiveArray: void 0 } });
var sprinkles = createSprinkles2({ conditions: { defaultCondition: "base", conditionNames: ["base", "hover", "active"], responsiveArray: void 0 }, styles: { background: { values: { accentColor: { conditions: { base: "ju367v9i", hover: "ju367v9j", active: "ju367v9k" }, defaultClass: "ju367v9i" }, accentColorForeground: { conditions: { base: "ju367v9l", hover: "ju367v9m", active: "ju367v9n" }, defaultClass: "ju367v9l" }, actionButtonBorder: { conditions: { base: "ju367v9o", hover: "ju367v9p", active: "ju367v9q" }, defaultClass: "ju367v9o" }, actionButtonBorderMobile: { conditions: { base: "ju367v9r", hover: "ju367v9s", active: "ju367v9t" }, defaultClass: "ju367v9r" }, actionButtonSecondaryBackground: { conditions: { base: "ju367v9u", hover: "ju367v9v", active: "ju367v9w" }, defaultClass: "ju367v9u" }, closeButton: { conditions: { base: "ju367v9x", hover: "ju367v9y", active: "ju367v9z" }, defaultClass: "ju367v9x" }, closeButtonBackground: { conditions: { base: "ju367va0", hover: "ju367va1", active: "ju367va2" }, defaultClass: "ju367va0" }, connectButtonBackground: { conditions: { base: "ju367va3", hover: "ju367va4", active: "ju367va5" }, defaultClass: "ju367va3" }, connectButtonBackgroundError: { conditions: { base: "ju367va6", hover: "ju367va7", active: "ju367va8" }, defaultClass: "ju367va6" }, connectButtonInnerBackground: { conditions: { base: "ju367va9", hover: "ju367vaa", active: "ju367vab" }, defaultClass: "ju367va9" }, connectButtonText: { conditions: { base: "ju367vac", hover: "ju367vad", active: "ju367vae" }, defaultClass: "ju367vac" }, connectButtonTextError: { conditions: { base: "ju367vaf", hover: "ju367vag", active: "ju367vah" }, defaultClass: "ju367vaf" }, connectionIndicator: { conditions: { base: "ju367vai", hover: "ju367vaj", active: "ju367vak" }, defaultClass: "ju367vai" }, downloadBottomCardBackground: { conditions: { base: "ju367val", hover: "ju367vam", active: "ju367van" }, defaultClass: "ju367val" }, downloadTopCardBackground: { conditions: { base: "ju367vao", hover: "ju367vap", active: "ju367vaq" }, defaultClass: "ju367vao" }, error: { conditions: { base: "ju367var", hover: "ju367vas", active: "ju367vat" }, defaultClass: "ju367var" }, generalBorder: { conditions: { base: "ju367vau", hover: "ju367vav", active: "ju367vaw" }, defaultClass: "ju367vau" }, generalBorderDim: { conditions: { base: "ju367vax", hover: "ju367vay", active: "ju367vaz" }, defaultClass: "ju367vax" }, menuItemBackground: { conditions: { base: "ju367vb0", hover: "ju367vb1", active: "ju367vb2" }, defaultClass: "ju367vb0" }, modalBackdrop: { conditions: { base: "ju367vb3", hover: "ju367vb4", active: "ju367vb5" }, defaultClass: "ju367vb3" }, modalBackground: { conditions: { base: "ju367vb6", hover: "ju367vb7", active: "ju367vb8" }, defaultClass: "ju367vb6" }, modalBorder: { conditions: { base: "ju367vb9", hover: "ju367vba", active: "ju367vbb" }, defaultClass: "ju367vb9" }, modalText: { conditions: { base: "ju367vbc", hover: "ju367vbd", active: "ju367vbe" }, defaultClass: "ju367vbc" }, modalTextDim: { conditions: { base: "ju367vbf", hover: "ju367vbg", active: "ju367vbh" }, defaultClass: "ju367vbf" }, modalTextSecondary: { conditions: { base: "ju367vbi", hover: "ju367vbj", active: "ju367vbk" }, defaultClass: "ju367vbi" }, profileAction: { conditions: { base: "ju367vbl", hover: "ju367vbm", active: "ju367vbn" }, defaultClass: "ju367vbl" }, profileActionHover: { conditions: { base: "ju367vbo", hover: "ju367vbp", active: "ju367vbq" }, defaultClass: "ju367vbo" }, profileForeground: { conditions: { base: "ju367vbr", hover: "ju367vbs", active: "ju367vbt" }, defaultClass: "ju367vbr" }, selectedOptionBorder: { conditions: { base: "ju367vbu", hover: "ju367vbv", active: "ju367vbw" }, defaultClass: "ju367vbu" }, standby: { conditions: { base: "ju367vbx", hover: "ju367vby", active: "ju367vbz" }, defaultClass: "ju367vbx" } } }, borderColor: { values: { accentColor: { conditions: { base: "ju367vc0", hover: "ju367vc1", active: "ju367vc2" }, defaultClass: "ju367vc0" }, accentColorForeground: { conditions: { base: "ju367vc3", hover: "ju367vc4", active: "ju367vc5" }, defaultClass: "ju367vc3" }, actionButtonBorder: { conditions: { base: "ju367vc6", hover: "ju367vc7", active: "ju367vc8" }, defaultClass: "ju367vc6" }, actionButtonBorderMobile: { conditions: { base: "ju367vc9", hover: "ju367vca", active: "ju367vcb" }, defaultClass: "ju367vc9" }, actionButtonSecondaryBackground: { conditions: { base: "ju367vcc", hover: "ju367vcd", active: "ju367vce" }, defaultClass: "ju367vcc" }, closeButton: { conditions: { base: "ju367vcf", hover: "ju367vcg", active: "ju367vch" }, defaultClass: "ju367vcf" }, closeButtonBackground: { conditions: { base: "ju367vci", hover: "ju367vcj", active: "ju367vck" }, defaultClass: "ju367vci" }, connectButtonBackground: { conditions: { base: "ju367vcl", hover: "ju367vcm", active: "ju367vcn" }, defaultClass: "ju367vcl" }, connectButtonBackgroundError: { conditions: { base: "ju367vco", hover: "ju367vcp", active: "ju367vcq" }, defaultClass: "ju367vco" }, connectButtonInnerBackground: { conditions: { base: "ju367vcr", hover: "ju367vcs", active: "ju367vct" }, defaultClass: "ju367vcr" }, connectButtonText: { conditions: { base: "ju367vcu", hover: "ju367vcv", active: "ju367vcw" }, defaultClass: "ju367vcu" }, connectButtonTextError: { conditions: { base: "ju367vcx", hover: "ju367vcy", active: "ju367vcz" }, defaultClass: "ju367vcx" }, connectionIndicator: { conditions: { base: "ju367vd0", hover: "ju367vd1", active: "ju367vd2" }, defaultClass: "ju367vd0" }, downloadBottomCardBackground: { conditions: { base: "ju367vd3", hover: "ju367vd4", active: "ju367vd5" }, defaultClass: "ju367vd3" }, downloadTopCardBackground: { conditions: { base: "ju367vd6", hover: "ju367vd7", active: "ju367vd8" }, defaultClass: "ju367vd6" }, error: { conditions: { base: "ju367vd9", hover: "ju367vda", active: "ju367vdb" }, defaultClass: "ju367vd9" }, generalBorder: { conditions: { base: "ju367vdc", hover: "ju367vdd", active: "ju367vde" }, defaultClass: "ju367vdc" }, generalBorderDim: { conditions: { base: "ju367vdf", hover: "ju367vdg", active: "ju367vdh" }, defaultClass: "ju367vdf" }, menuItemBackground: { conditions: { base: "ju367vdi", hover: "ju367vdj", active: "ju367vdk" }, defaultClass: "ju367vdi" }, modalBackdrop: { conditions: { base: "ju367vdl", hover: "ju367vdm", active: "ju367vdn" }, defaultClass: "ju367vdl" }, modalBackground: { conditions: { base: "ju367vdo", hover: "ju367vdp", active: "ju367vdq" }, defaultClass: "ju367vdo" }, modalBorder: { conditions: { base: "ju367vdr", hover: "ju367vds", active: "ju367vdt" }, defaultClass: "ju367vdr" }, modalText: { conditions: { base: "ju367vdu", hover: "ju367vdv", active: "ju367vdw" }, defaultClass: "ju367vdu" }, modalTextDim: { conditions: { base: "ju367vdx", hover: "ju367vdy", active: "ju367vdz" }, defaultClass: "ju367vdx" }, modalTextSecondary: { conditions: { base: "ju367ve0", hover: "ju367ve1", active: "ju367ve2" }, defaultClass: "ju367ve0" }, profileAction: { conditions: { base: "ju367ve3", hover: "ju367ve4", active: "ju367ve5" }, defaultClass: "ju367ve3" }, profileActionHover: { conditions: { base: "ju367ve6", hover: "ju367ve7", active: "ju367ve8" }, defaultClass: "ju367ve6" }, profileForeground: { conditions: { base: "ju367ve9", hover: "ju367vea", active: "ju367veb" }, defaultClass: "ju367ve9" }, selectedOptionBorder: { conditions: { base: "ju367vec", hover: "ju367ved", active: "ju367vee" }, defaultClass: "ju367vec" }, standby: { conditions: { base: "ju367vef", hover: "ju367veg", active: "ju367veh" }, defaultClass: "ju367vef" } } }, boxShadow: { values: { connectButton: { conditions: { base: "ju367vei", hover: "ju367vej", active: "ju367vek" }, defaultClass: "ju367vei" }, dialog: { conditions: { base: "ju367vel", hover: "ju367vem", active: "ju367ven" }, defaultClass: "ju367vel" }, profileDetailsAction: { conditions: { base: "ju367veo", hover: "ju367vep", active: "ju367veq" }, defaultClass: "ju367veo" }, selectedOption: { conditions: { base: "ju367ver", hover: "ju367ves", active: "ju367vet" }, defaultClass: "ju367ver" }, selectedWallet: { conditions: { base: "ju367veu", hover: "ju367vev", active: "ju367vew" }, defaultClass: "ju367veu" }, walletLogo: { conditions: { base: "ju367vex", hover: "ju367vey", active: "ju367vez" }, defaultClass: "ju367vex" } } }, color: { values: { accentColor: { conditions: { base: "ju367vf0", hover: "ju367vf1", active: "ju367vf2" }, defaultClass: "ju367vf0" }, accentColorForeground: { conditions: { base: "ju367vf3", hover: "ju367vf4", active: "ju367vf5" }, defaultClass: "ju367vf3" }, actionButtonBorder: { conditions: { base: "ju367vf6", hover: "ju367vf7", active: "ju367vf8" }, defaultClass: "ju367vf6" }, actionButtonBorderMobile: { conditions: { base: "ju367vf9", hover: "ju367vfa", active: "ju367vfb" }, defaultClass: "ju367vf9" }, actionButtonSecondaryBackground: { conditions: { base: "ju367vfc", hover: "ju367vfd", active: "ju367vfe" }, defaultClass: "ju367vfc" }, closeButton: { conditions: { base: "ju367vff", hover: "ju367vfg", active: "ju367vfh" }, defaultClass: "ju367vff" }, closeButtonBackground: { conditions: { base: "ju367vfi", hover: "ju367vfj", active: "ju367vfk" }, defaultClass: "ju367vfi" }, connectButtonBackground: { conditions: { base: "ju367vfl", hover: "ju367vfm", active: "ju367vfn" }, defaultClass: "ju367vfl" }, connectButtonBackgroundError: { conditions: { base: "ju367vfo", hover: "ju367vfp", active: "ju367vfq" }, defaultClass: "ju367vfo" }, connectButtonInnerBackground: { conditions: { base: "ju367vfr", hover: "ju367vfs", active: "ju367vft" }, defaultClass: "ju367vfr" }, connectButtonText: { conditions: { base: "ju367vfu", hover: "ju367vfv", active: "ju367vfw" }, defaultClass: "ju367vfu" }, connectButtonTextError: { conditions: { base: "ju367vfx", hover: "ju367vfy", active: "ju367vfz" }, defaultClass: "ju367vfx" }, connectionIndicator: { conditions: { base: "ju367vg0", hover: "ju367vg1", active: "ju367vg2" }, defaultClass: "ju367vg0" }, downloadBottomCardBackground: { conditions: { base: "ju367vg3", hover: "ju367vg4", active: "ju367vg5" }, defaultClass: "ju367vg3" }, downloadTopCardBackground: { conditions: { base: "ju367vg6", hover: "ju367vg7", active: "ju367vg8" }, defaultClass: "ju367vg6" }, error: { conditions: { base: "ju367vg9", hover: "ju367vga", active: "ju367vgb" }, defaultClass: "ju367vg9" }, generalBorder: { conditions: { base: "ju367vgc", hover: "ju367vgd", active: "ju367vge" }, defaultClass: "ju367vgc" }, generalBorderDim: { conditions: { base: "ju367vgf", hover: "ju367vgg", active: "ju367vgh" }, defaultClass: "ju367vgf" }, menuItemBackground: { conditions: { base: "ju367vgi", hover: "ju367vgj", active: "ju367vgk" }, defaultClass: "ju367vgi" }, modalBackdrop: { conditions: { base: "ju367vgl", hover: "ju367vgm", active: "ju367vgn" }, defaultClass: "ju367vgl" }, modalBackground: { conditions: { base: "ju367vgo", hover: "ju367vgp", active: "ju367vgq" }, defaultClass: "ju367vgo" }, modalBorder: { conditions: { base: "ju367vgr", hover: "ju367vgs", active: "ju367vgt" }, defaultClass: "ju367vgr" }, modalText: { conditions: { base: "ju367vgu", hover: "ju367vgv", active: "ju367vgw" }, defaultClass: "ju367vgu" }, modalTextDim: { conditions: { base: "ju367vgx", hover: "ju367vgy", active: "ju367vgz" }, defaultClass: "ju367vgx" }, modalTextSecondary: { conditions: { base: "ju367vh0", hover: "ju367vh1", active: "ju367vh2" }, defaultClass: "ju367vh0" }, profileAction: { conditions: { base: "ju367vh3", hover: "ju367vh4", active: "ju367vh5" }, defaultClass: "ju367vh3" }, profileActionHover: { conditions: { base: "ju367vh6", hover: "ju367vh7", active: "ju367vh8" }, defaultClass: "ju367vh6" }, profileForeground: { conditions: { base: "ju367vh9", hover: "ju367vha", active: "ju367vhb" }, defaultClass: "ju367vh9" }, selectedOptionBorder: { conditions: { base: "ju367vhc", hover: "ju367vhd", active: "ju367vhe" }, defaultClass: "ju367vhc" }, standby: { conditions: { base: "ju367vhf", hover: "ju367vhg", active: "ju367vhh" }, defaultClass: "ju367vhf" } } } } }, { conditions: { defaultCondition: "smallScreen", conditionNames: ["smallScreen", "largeScreen"], responsiveArray: void 0 }, styles: { alignItems: { values: { "flex-start": { conditions: { smallScreen: "ju367v0", largeScreen: "ju367v1" }, defaultClass: "ju367v0" }, "flex-end": { conditions: { smallScreen: "ju367v2", largeScreen: "ju367v3" }, defaultClass: "ju367v2" }, center: { conditions: { smallScreen: "ju367v4", largeScreen: "ju367v5" }, defaultClass: "ju367v4" } } }, display: { values: { none: { conditions: { smallScreen: "ju367v6", largeScreen: "ju367v7" }, defaultClass: "ju367v6" }, block: { conditions: { smallScreen: "ju367v8", largeScreen: "ju367v9" }, defaultClass: "ju367v8" }, flex: { conditions: { smallScreen: "ju367va", largeScreen: "ju367vb" }, defaultClass: "ju367va" }, inline: { conditions: { smallScreen: "ju367vc", largeScreen: "ju367vd" }, defaultClass: "ju367vc" } } } } }, { conditions: void 0, styles: { margin: { mappings: ["marginTop", "marginBottom", "marginLeft", "marginRight"] }, marginX: { mappings: ["marginLeft", "marginRight"] }, marginY: { mappings: ["marginTop", "marginBottom"] }, padding: { mappings: ["paddingTop", "paddingBottom", "paddingLeft", "paddingRight"] }, paddingX: { mappings: ["paddingLeft", "paddingRight"] }, paddingY: { mappings: ["paddingTop", "paddingBottom"] }, alignSelf: { values: { "flex-start": { defaultClass: "ju367ve" }, "flex-end": { defaultClass: "ju367vf" }, center: { defaultClass: "ju367vg" } } }, backgroundSize: { values: { cover: { defaultClass: "ju367vh" } } }, borderRadius: { values: { "1": { defaultClass: "ju367vi" }, "6": { defaultClass: "ju367vj" }, "10": { defaultClass: "ju367vk" }, "13": { defaultClass: "ju367vl" }, actionButton: { defaultClass: "ju367vm" }, connectButton: { defaultClass: "ju367vn" }, menuButton: { defaultClass: "ju367vo" }, modal: { defaultClass: "ju367vp" }, modalMobile: { defaultClass: "ju367vq" }, "25%": { defaultClass: "ju367vr" }, full: { defaultClass: "ju367vs" } } }, borderStyle: { values: { solid: { defaultClass: "ju367vt" } } }, borderWidth: { values: { "0": { defaultClass: "ju367vu" }, "1": { defaultClass: "ju367vv" }, "2": { defaultClass: "ju367vw" }, "4": { defaultClass: "ju367vx" } } }, cursor: { values: { pointer: { defaultClass: "ju367vy" }, none: { defaultClass: "ju367vz" } } }, pointerEvents: { values: { none: { defaultClass: "ju367v10" }, all: { defaultClass: "ju367v11" } } }, minHeight: { values: { "8": { defaultClass: "ju367v12" }, "44": { defaultClass: "ju367v13" } } }, flexDirection: { values: { row: { defaultClass: "ju367v14" }, column: { defaultClass: "ju367v15" } } }, fontFamily: { values: { body: { defaultClass: "ju367v16" } } }, fontSize: { values: { "12": { defaultClass: "ju367v17" }, "13": { defaultClass: "ju367v18" }, "14": { defaultClass: "ju367v19" }, "16": { defaultClass: "ju367v1a" }, "18": { defaultClass: "ju367v1b" }, "20": { defaultClass: "ju367v1c" }, "23": { defaultClass: "ju367v1d" } } }, fontWeight: { values: { regular: { defaultClass: "ju367v1e" }, medium: { defaultClass: "ju367v1f" }, semibold: { defaultClass: "ju367v1g" }, bold: { defaultClass: "ju367v1h" }, heavy: { defaultClass: "ju367v1i" } } }, gap: { values: { "0": { defaultClass: "ju367v1j" }, "1": { defaultClass: "ju367v1k" }, "2": { defaultClass: "ju367v1l" }, "3": { defaultClass: "ju367v1m" }, "4": { defaultClass: "ju367v1n" }, "5": { defaultClass: "ju367v1o" }, "6": { defaultClass: "ju367v1p" }, "8": { defaultClass: "ju367v1q" }, "10": { defaultClass: "ju367v1r" }, "12": { defaultClass: "ju367v1s" }, "14": { defaultClass: "ju367v1t" }, "16": { defaultClass: "ju367v1u" }, "18": { defaultClass: "ju367v1v" }, "20": { defaultClass: "ju367v1w" }, "24": { defaultClass: "ju367v1x" }, "28": { defaultClass: "ju367v1y" }, "32": { defaultClass: "ju367v1z" }, "36": { defaultClass: "ju367v20" }, "44": { defaultClass: "ju367v21" }, "64": { defaultClass: "ju367v22" }, "-1": { defaultClass: "ju367v23" } } }, height: { values: { "1": { defaultClass: "ju367v24" }, "2": { defaultClass: "ju367v25" }, "4": { defaultClass: "ju367v26" }, "8": { defaultClass: "ju367v27" }, "12": { defaultClass: "ju367v28" }, "20": { defaultClass: "ju367v29" }, "24": { defaultClass: "ju367v2a" }, "28": { defaultClass: "ju367v2b" }, "30": { defaultClass: "ju367v2c" }, "32": { defaultClass: "ju367v2d" }, "34": { defaultClass: "ju367v2e" }, "36": { defaultClass: "ju367v2f" }, "40": { defaultClass: "ju367v2g" }, "44": { defaultClass: "ju367v2h" }, "48": { defaultClass: "ju367v2i" }, "54": { defaultClass: "ju367v2j" }, "60": { defaultClass: "ju367v2k" }, "200": { defaultClass: "ju367v2l" }, full: { defaultClass: "ju367v2m" }, max: { defaultClass: "ju367v2n" } } }, justifyContent: { values: { "flex-start": { defaultClass: "ju367v2o" }, "flex-end": { defaultClass: "ju367v2p" }, center: { defaultClass: "ju367v2q" }, "space-between": { defaultClass: "ju367v2r" }, "space-around": { defaultClass: "ju367v2s" } } }, textAlign: { values: { left: { defaultClass: "ju367v2t" }, center: { defaultClass: "ju367v2u" }, inherit: { defaultClass: "ju367v2v" } } }, marginBottom: { values: { "0": { defaultClass: "ju367v2w" }, "1": { defaultClass: "ju367v2x" }, "2": { defaultClass: "ju367v2y" }, "3": { defaultClass: "ju367v2z" }, "4": { defaultClass: "ju367v30" }, "5": { defaultClass: "ju367v31" }, "6": { defaultClass: "ju367v32" }, "8": { defaultClass: "ju367v33" }, "10": { defaultClass: "ju367v34" }, "12": { defaultClass: "ju367v35" }, "14": { defaultClass: "ju367v36" }, "16": { defaultClass: "ju367v37" }, "18": { defaultClass: "ju367v38" }, "20": { defaultClass: "ju367v39" }, "24": { defaultClass: "ju367v3a" }, "28": { defaultClass: "ju367v3b" }, "32": { defaultClass: "ju367v3c" }, "36": { defaultClass: "ju367v3d" }, "44": { defaultClass: "ju367v3e" }, "64": { defaultClass: "ju367v3f" }, "-1": { defaultClass: "ju367v3g" } } }, marginLeft: { values: { "0": { defaultClass: "ju367v3h" }, "1": { defaultClass: "ju367v3i" }, "2": { defaultClass: "ju367v3j" }, "3": { defaultClass: "ju367v3k" }, "4": { defaultClass: "ju367v3l" }, "5": { defaultClass: "ju367v3m" }, "6": { defaultClass: "ju367v3n" }, "8": { defaultClass: "ju367v3o" }, "10": { defaultClass: "ju367v3p" }, "12": { defaultClass: "ju367v3q" }, "14": { defaultClass: "ju367v3r" }, "16": { defaultClass: "ju367v3s" }, "18": { defaultClass: "ju367v3t" }, "20": { defaultClass: "ju367v3u" }, "24": { defaultClass: "ju367v3v" }, "28": { defaultClass: "ju367v3w" }, "32": { defaultClass: "ju367v3x" }, "36": { defaultClass: "ju367v3y" }, "44": { defaultClass: "ju367v3z" }, "64": { defaultClass: "ju367v40" }, "-1": { defaultClass: "ju367v41" } } }, marginRight: { values: { "0": { defaultClass: "ju367v42" }, "1": { defaultClass: "ju367v43" }, "2": { defaultClass: "ju367v44" }, "3": { defaultClass: "ju367v45" }, "4": { defaultClass: "ju367v46" }, "5": { defaultClass: "ju367v47" }, "6": { defaultClass: "ju367v48" }, "8": { defaultClass: "ju367v49" }, "10": { defaultClass: "ju367v4a" }, "12": { defaultClass: "ju367v4b" }, "14": { defaultClass: "ju367v4c" }, "16": { defaultClass: "ju367v4d" }, "18": { defaultClass: "ju367v4e" }, "20": { defaultClass: "ju367v4f" }, "24": { defaultClass: "ju367v4g" }, "28": { defaultClass: "ju367v4h" }, "32": { defaultClass: "ju367v4i" }, "36": { defaultClass: "ju367v4j" }, "44": { defaultClass: "ju367v4k" }, "64": { defaultClass: "ju367v4l" }, "-1": { defaultClass: "ju367v4m" } } }, marginTop: { values: { "0": { defaultClass: "ju367v4n" }, "1": { defaultClass: "ju367v4o" }, "2": { defaultClass: "ju367v4p" }, "3": { defaultClass: "ju367v4q" }, "4": { defaultClass: "ju367v4r" }, "5": { defaultClass: "ju367v4s" }, "6": { defaultClass: "ju367v4t" }, "8": { defaultClass: "ju367v4u" }, "10": { defaultClass: "ju367v4v" }, "12": { defaultClass: "ju367v4w" }, "14": { defaultClass: "ju367v4x" }, "16": { defaultClass: "ju367v4y" }, "18": { defaultClass: "ju367v4z" }, "20": { defaultClass: "ju367v50" }, "24": { defaultClass: "ju367v51" }, "28": { defaultClass: "ju367v52" }, "32": { defaultClass: "ju367v53" }, "36": { defaultClass: "ju367v54" }, "44": { defaultClass: "ju367v55" }, "64": { defaultClass: "ju367v56" }, "-1": { defaultClass: "ju367v57" } } }, maxWidth: { values: { "1": { defaultClass: "ju367v58" }, "2": { defaultClass: "ju367v59" }, "4": { defaultClass: "ju367v5a" }, "8": { defaultClass: "ju367v5b" }, "12": { defaultClass: "ju367v5c" }, "20": { defaultClass: "ju367v5d" }, "24": { defaultClass: "ju367v5e" }, "28": { defaultClass: "ju367v5f" }, "30": { defaultClass: "ju367v5g" }, "32": { defaultClass: "ju367v5h" }, "34": { defaultClass: "ju367v5i" }, "36": { defaultClass: "ju367v5j" }, "40": { defaultClass: "ju367v5k" }, "44": { defaultClass: "ju367v5l" }, "48": { defaultClass: "ju367v5m" }, "54": { defaultClass: "ju367v5n" }, "60": { defaultClass: "ju367v5o" }, "200": { defaultClass: "ju367v5p" }, full: { defaultClass: "ju367v5q" }, max: { defaultClass: "ju367v5r" } } }, minWidth: { values: { "1": { defaultClass: "ju367v5s" }, "2": { defaultClass: "ju367v5t" }, "4": { defaultClass: "ju367v5u" }, "8": { defaultClass: "ju367v5v" }, "12": { defaultClass: "ju367v5w" }, "20": { defaultClass: "ju367v5x" }, "24": { defaultClass: "ju367v5y" }, "28": { defaultClass: "ju367v5z" }, "30": { defaultClass: "ju367v60" }, "32": { defaultClass: "ju367v61" }, "34": { defaultClass: "ju367v62" }, "36": { defaultClass: "ju367v63" }, "40": { defaultClass: "ju367v64" }, "44": { defaultClass: "ju367v65" }, "48": { defaultClass: "ju367v66" }, "54": { defaultClass: "ju367v67" }, "60": { defaultClass: "ju367v68" }, "200": { defaultClass: "ju367v69" }, full: { defaultClass: "ju367v6a" }, max: { defaultClass: "ju367v6b" } } }, overflow: { values: { hidden: { defaultClass: "ju367v6c" } } }, paddingBottom: { values: { "0": { defaultClass: "ju367v6d" }, "1": { defaultClass: "ju367v6e" }, "2": { defaultClass: "ju367v6f" }, "3": { defaultClass: "ju367v6g" }, "4": { defaultClass: "ju367v6h" }, "5": { defaultClass: "ju367v6i" }, "6": { defaultClass: "ju367v6j" }, "8": { defaultClass: "ju367v6k" }, "10": { defaultClass: "ju367v6l" }, "12": { defaultClass: "ju367v6m" }, "14": { defaultClass: "ju367v6n" }, "16": { defaultClass: "ju367v6o" }, "18": { defaultClass: "ju367v6p" }, "20": { defaultClass: "ju367v6q" }, "24": { defaultClass: "ju367v6r" }, "28": { defaultClass: "ju367v6s" }, "32": { defaultClass: "ju367v6t" }, "36": { defaultClass: "ju367v6u" }, "44": { defaultClass: "ju367v6v" }, "64": { defaultClass: "ju367v6w" }, "-1": { defaultClass: "ju367v6x" } } }, paddingLeft: { values: { "0": { defaultClass: "ju367v6y" }, "1": { defaultClass: "ju367v6z" }, "2": { defaultClass: "ju367v70" }, "3": { defaultClass: "ju367v71" }, "4": { defaultClass: "ju367v72" }, "5": { defaultClass: "ju367v73" }, "6": { defaultClass: "ju367v74" }, "8": { defaultClass: "ju367v75" }, "10": { defaultClass: "ju367v76" }, "12": { defaultClass: "ju367v77" }, "14": { defaultClass: "ju367v78" }, "16": { defaultClass: "ju367v79" }, "18": { defaultClass: "ju367v7a" }, "20": { defaultClass: "ju367v7b" }, "24": { defaultClass: "ju367v7c" }, "28": { defaultClass: "ju367v7d" }, "32": { defaultClass: "ju367v7e" }, "36": { defaultClass: "ju367v7f" }, "44": { defaultClass: "ju367v7g" }, "64": { defaultClass: "ju367v7h" }, "-1": { defaultClass: "ju367v7i" } } }, paddingRight: { values: { "0": { defaultClass: "ju367v7j" }, "1": { defaultClass: "ju367v7k" }, "2": { defaultClass: "ju367v7l" }, "3": { defaultClass: "ju367v7m" }, "4": { defaultClass: "ju367v7n" }, "5": { defaultClass: "ju367v7o" }, "6": { defaultClass: "ju367v7p" }, "8": { defaultClass: "ju367v7q" }, "10": { defaultClass: "ju367v7r" }, "12": { defaultClass: "ju367v7s" }, "14": { defaultClass: "ju367v7t" }, "16": { defaultClass: "ju367v7u" }, "18": { defaultClass: "ju367v7v" }, "20": { defaultClass: "ju367v7w" }, "24": { defaultClass: "ju367v7x" }, "28": { defaultClass: "ju367v7y" }, "32": { defaultClass: "ju367v7z" }, "36": { defaultClass: "ju367v80" }, "44": { defaultClass: "ju367v81" }, "64": { defaultClass: "ju367v82" }, "-1": { defaultClass: "ju367v83" } } }, paddingTop: { values: { "0": { defaultClass: "ju367v84" }, "1": { defaultClass: "ju367v85" }, "2": { defaultClass: "ju367v86" }, "3": { defaultClass: "ju367v87" }, "4": { defaultClass: "ju367v88" }, "5": { defaultClass: "ju367v89" }, "6": { defaultClass: "ju367v8a" }, "8": { defaultClass: "ju367v8b" }, "10": { defaultClass: "ju367v8c" }, "12": { defaultClass: "ju367v8d" }, "14": { defaultClass: "ju367v8e" }, "16": { defaultClass: "ju367v8f" }, "18": { defaultClass: "ju367v8g" }, "20": { defaultClass: "ju367v8h" }, "24": { defaultClass: "ju367v8i" }, "28": { defaultClass: "ju367v8j" }, "32": { defaultClass: "ju367v8k" }, "36": { defaultClass: "ju367v8l" }, "44": { defaultClass: "ju367v8m" }, "64": { defaultClass: "ju367v8n" }, "-1": { defaultClass: "ju367v8o" } } }, position: { values: { absolute: { defaultClass: "ju367v8p" }, fixed: { defaultClass: "ju367v8q" }, relative: { defaultClass: "ju367v8r" } } }, WebkitUserSelect: { values: { none: { defaultClass: "ju367v8s" } } }, right: { values: { "0": { defaultClass: "ju367v8t" } } }, transition: { values: { "default": { defaultClass: "ju367v8u" }, transform: { defaultClass: "ju367v8v" } } }, userSelect: { values: { none: { defaultClass: "ju367v8w" } } }, width: { values: { "1": { defaultClass: "ju367v8x" }, "2": { defaultClass: "ju367v8y" }, "4": { defaultClass: "ju367v8z" }, "8": { defaultClass: "ju367v90" }, "12": { defaultClass: "ju367v91" }, "20": { defaultClass: "ju367v92" }, "24": { defaultClass: "ju367v93" }, "28": { defaultClass: "ju367v94" }, "30": { defaultClass: "ju367v95" }, "32": { defaultClass: "ju367v96" }, "34": { defaultClass: "ju367v97" }, "36": { defaultClass: "ju367v98" }, "40": { defaultClass: "ju367v99" }, "44": { defaultClass: "ju367v9a" }, "48": { defaultClass: "ju367v9b" }, "54": { defaultClass: "ju367v9c" }, "60": { defaultClass: "ju367v9d" }, "200": { defaultClass: "ju367v9e" }, full: { defaultClass: "ju367v9f" }, max: { defaultClass: "ju367v9g" } } }, backdropFilter: { values: { modalOverlay: { defaultClass: "ju367v9h" } } } } });
var themeVars = { colors: { accentColor: "var(--rk-colors-accentColor)", accentColorForeground: "var(--rk-colors-accentColorForeground)", actionButtonBorder: "var(--rk-colors-actionButtonBorder)", actionButtonBorderMobile: "var(--rk-colors-actionButtonBorderMobile)", actionButtonSecondaryBackground: "var(--rk-colors-actionButtonSecondaryBackground)", closeButton: "var(--rk-colors-closeButton)", closeButtonBackground: "var(--rk-colors-closeButtonBackground)", connectButtonBackground: "var(--rk-colors-connectButtonBackground)", connectButtonBackgroundError: "var(--rk-colors-connectButtonBackgroundError)", connectButtonInnerBackground: "var(--rk-colors-connectButtonInnerBackground)", connectButtonText: "var(--rk-colors-connectButtonText)", connectButtonTextError: "var(--rk-colors-connectButtonTextError)", connectionIndicator: "var(--rk-colors-connectionIndicator)", downloadBottomCardBackground: "var(--rk-colors-downloadBottomCardBackground)", downloadTopCardBackground: "var(--rk-colors-downloadTopCardBackground)", error: "var(--rk-colors-error)", generalBorder: "var(--rk-colors-generalBorder)", generalBorderDim: "var(--rk-colors-generalBorderDim)", menuItemBackground: "var(--rk-colors-menuItemBackground)", modalBackdrop: "var(--rk-colors-modalBackdrop)", modalBackground: "var(--rk-colors-modalBackground)", modalBorder: "var(--rk-colors-modalBorder)", modalText: "var(--rk-colors-modalText)", modalTextDim: "var(--rk-colors-modalTextDim)", modalTextSecondary: "var(--rk-colors-modalTextSecondary)", profileAction: "var(--rk-colors-profileAction)", profileActionHover: "var(--rk-colors-profileActionHover)", profileForeground: "var(--rk-colors-profileForeground)", selectedOptionBorder: "var(--rk-colors-selectedOptionBorder)", standby: "var(--rk-colors-standby)" }, fonts: { body: "var(--rk-fonts-body)" }, radii: { actionButton: "var(--rk-radii-actionButton)", connectButton: "var(--rk-radii-connectButton)", menuButton: "var(--rk-radii-menuButton)", modal: "var(--rk-radii-modal)", modalMobile: "var(--rk-radii-modalMobile)" }, shadows: { connectButton: "var(--rk-shadows-connectButton)", dialog: "var(--rk-shadows-dialog)", profileDetailsAction: "var(--rk-shadows-profileDetailsAction)", selectedOption: "var(--rk-shadows-selectedOption)", selectedWallet: "var(--rk-shadows-selectedWallet)", walletLogo: "var(--rk-shadows-walletLogo)" }, blurs: { modalOverlay: "var(--rk-blurs-modalOverlay)" } };
var active = { shrink: "_12cbo8i6", shrinkSm: "_12cbo8i7" };
var base = "_12cbo8i3 ju367v8r";
var hover = { grow: "_12cbo8i4", growLg: "_12cbo8i5" };
function touchableStyles({ active: active2, hover: hover2 }) {
  return [base, hover2 && hover[hover2], active[active2]];
}
function createAuthenticationAdapter(adapter) {
  return adapter;
}
var AuthenticationContext = (0, import_react5.createContext)(null);
function RainbowKitAuthenticationProvider({
  adapter,
  children,
  enabled = true,
  status
}) {
  const { connector } = useAccount({
    onDisconnect: () => {
      adapter.signOut();
    }
  });
  const { isDisconnected } = useAccount();
  const onceRef = (0, import_react5.useRef)(false);
  (0, import_react5.useEffect)(() => {
    if (onceRef.current)
      return;
    onceRef.current = true;
    if (isDisconnected && status === "authenticated") {
      adapter.signOut();
    }
  }, [status, adapter, isDisconnected]);
  const handleChangedAccount = (0, import_react5.useCallback)(({ account }) => {
    if (account)
      adapter.signOut();
  }, [adapter]);
  (0, import_react5.useEffect)(() => {
    if (connector && status === "authenticated") {
      connector.on("change", handleChangedAccount);
      return () => {
        connector == null ? void 0 : connector.off("change", handleChangedAccount);
      };
    }
  }, [connector, status, handleChangedAccount]);
  return import_react5.default.createElement(AuthenticationContext.Provider, {
    value: (0, import_react5.useMemo)(() => enabled ? { adapter, status } : null, [enabled, adapter, status])
  }, children);
}
function useAuthenticationAdapter() {
  var _a;
  const { adapter } = (_a = (0, import_react5.useContext)(AuthenticationContext)) != null ? _a : {};
  if (!adapter) {
    throw new Error("No authentication adapter found");
  }
  return adapter;
}
function useAuthenticationStatus() {
  var _a;
  const contextValue = (0, import_react5.useContext)(AuthenticationContext);
  return (_a = contextValue == null ? void 0 : contextValue.status) != null ? _a : null;
}
function useConnectionStatus() {
  const authenticationStatus = useAuthenticationStatus();
  const { isConnected } = useAccount();
  if (!isConnected) {
    return "disconnected";
  }
  if (!authenticationStatus) {
    return "connected";
  }
  if (authenticationStatus === "loading" || authenticationStatus === "unauthenticated") {
    return authenticationStatus;
  }
  return "connected";
}
function isAndroid() {
  return typeof navigator !== "undefined" && /android/i.test(navigator.userAgent);
}
function isSmallIOS() {
  return typeof navigator !== "undefined" && /iPhone|iPod/.test(navigator.userAgent);
}
function isLargeIOS() {
  return typeof navigator !== "undefined" && (/iPad/.test(navigator.userAgent) || navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
}
function isIOS() {
  return isSmallIOS() || isLargeIOS();
}
function isMobile() {
  return isAndroid() || isIOS();
}
var base2 = "iekbcc0";
var element = { a: "iekbcca", blockquote: "iekbcc2", button: "iekbcc9", input: "iekbcc8 iekbcc5 iekbcc4", mark: "iekbcc6", ol: "iekbcc1", q: "iekbcc2", select: "iekbcc7 iekbcc5 iekbcc4", table: "iekbcc3", textarea: "iekbcc5 iekbcc4", ul: "iekbcc1" };
var atoms = ({ reset, ...rest }) => {
  if (!reset)
    return sprinkles(rest);
  const elementReset = element[reset];
  const sprinklesClasses = sprinkles(rest);
  return clsx_default(base2, elementReset, sprinklesClasses);
};
var Box = React22.forwardRef(({ as = "div", className, testId, ...props }, ref) => {
  const atomProps = {};
  const nativeProps = {};
  for (const key in props) {
    if (sprinkles.properties.has(key)) {
      atomProps[key] = props[key];
    } else {
      nativeProps[key] = props[key];
    }
  }
  const atomicClasses = atoms({
    reset: typeof as === "string" ? as : "div",
    ...atomProps
  });
  return React22.createElement(as, {
    className: clsx_default(atomicClasses, className),
    ...nativeProps,
    "data-testid": testId ? `rk-${testId.replace(/^rk-/, "")}` : void 0,
    ref
  });
});
Box.displayName = "Box";
var cachedUrls = /* @__PURE__ */ new Map();
var cachedRequestPromises = /* @__PURE__ */ new Map();
async function loadAsyncImage(asyncImage) {
  const cachedRequestPromise = cachedRequestPromises.get(asyncImage);
  if (cachedRequestPromise) {
    return cachedRequestPromise;
  }
  const load = async () => asyncImage().then(async (url) => {
    cachedUrls.set(asyncImage, url);
    return url;
  });
  const requestPromise = load().catch((_err) => {
    return load().catch((_err2) => {
      cachedRequestPromises.delete(asyncImage);
    });
  });
  cachedRequestPromises.set(asyncImage, requestPromise);
  return requestPromise;
}
async function loadImages(...urls) {
  return await Promise.all(urls.map((url) => typeof url === "function" ? loadAsyncImage(url) : url));
}
function useForceUpdate() {
  const [, forceUpdate] = (0, import_react7.useReducer)((x) => x + 1, 0);
  return forceUpdate;
}
function useAsyncImage(url) {
  const cachedUrl = typeof url === "function" ? cachedUrls.get(url) : void 0;
  const forceUpdate = useForceUpdate();
  (0, import_react7.useEffect)(() => {
    if (typeof url === "function" && !cachedUrl) {
      loadAsyncImage(url).then(forceUpdate);
    }
  }, [url, cachedUrl, forceUpdate]);
  return typeof url === "function" ? cachedUrl : url;
}
function AsyncImage({
  alt,
  background,
  borderColor,
  borderRadius,
  boxShadow,
  height,
  src: srcProp,
  width,
  testId
}) {
  const ios = isIOS();
  const src7 = useAsyncImage(srcProp);
  const isRemoteImage = src7 && /^http/.test(src7);
  const [isRemoteImageLoaded, setRemoteImageLoaded] = (0, import_react6.useReducer)(() => true, false);
  return import_react6.default.createElement(Box, {
    "aria-label": alt,
    borderRadius,
    boxShadow,
    height: typeof height === "string" ? height : void 0,
    overflow: "hidden",
    position: "relative",
    role: "img",
    style: {
      background,
      height: typeof height === "number" ? height : void 0,
      width: typeof width === "number" ? width : void 0
    },
    width: typeof width === "string" ? width : void 0,
    testId
  }, import_react6.default.createElement(Box, {
    ...isRemoteImage ? {
      "aria-hidden": true,
      "as": "img",
      "onLoad": setRemoteImageLoaded,
      "src": src7
    } : {
      backgroundSize: "cover"
    },
    height: "full",
    position: "absolute",
    ...ios ? { WebkitUserSelect: "none" } : {},
    style: {
      touchCallout: "none",
      transition: "opacity .15s linear",
      userSelect: "none",
      ...isRemoteImage ? {
        opacity: isRemoteImageLoaded ? 1 : 0
      } : {
        backgroundImage: src7 ? `url(${src7})` : void 0,
        backgroundRepeat: "no-repeat",
        opacity: src7 ? 1 : 0
      }
    },
    width: "full"
  }), borderColor ? import_react6.default.createElement(Box, {
    ...typeof borderColor === "object" && "custom" in borderColor ? { style: { borderColor: borderColor.custom } } : { borderColor },
    borderRadius,
    borderStyle: "solid",
    borderWidth: "1",
    height: "full",
    position: "relative",
    width: "full"
  }) : null);
}
var SpinnerIconClassName = "_1luule42";
var SpinnerIconPathClassName = "_1luule43";
var useRandomId = (prefix) => (0, import_react9.useMemo)(() => `${prefix}_${Math.round(Math.random() * 1e9)}`, [prefix]);
var SpinnerIcon = ({
  height = 21,
  width = 21
}) => {
  const id = useRandomId("spinner");
  return import_react9.default.createElement("svg", {
    className: SpinnerIconClassName,
    fill: "none",
    height,
    viewBox: "0 0 21 21",
    width,
    xmlns: "http://www.w3.org/2000/svg"
  }, import_react9.default.createElement("title", null, "Loading"), import_react9.default.createElement("clipPath", {
    id
  }, import_react9.default.createElement("path", {
    d: "M10.5 3C6.35786 3 3 6.35786 3 10.5C3 14.6421 6.35786 18 10.5 18C11.3284 18 12 18.6716 12 19.5C12 20.3284 11.3284 21 10.5 21C4.70101 21 0 16.299 0 10.5C0 4.70101 4.70101 0 10.5 0C16.299 0 21 4.70101 21 10.5C21 11.3284 20.3284 12 19.5 12C18.6716 12 18 11.3284 18 10.5C18 6.35786 14.6421 3 10.5 3Z"
  })), import_react9.default.createElement("foreignObject", {
    clipPath: `url(#${id})`,
    height: "21",
    width: "21",
    x: "0",
    y: "0"
  }, import_react9.default.createElement("div", {
    className: SpinnerIconPathClassName
  })));
};
var colors = [
  "#FC5C54",
  "#FFD95A",
  "#E95D72",
  "#6A87C8",
  "#5FD0F3",
  "#75C06B",
  "#FFDD86",
  "#5FC6D4",
  "#FF949A",
  "#FF8024",
  "#9BA1A4",
  "#EC66FF",
  "#FF8CBC",
  "#FF9A23",
  "#C5DADB",
  "#A8CE63",
  "#71ABFF",
  "#FFE279",
  "#B6B1B6",
  "#FF6780",
  "#A575FF",
  "#4D82FF",
  "#FFB35A"
];
var avatars = [
  { color: colors[0], emoji: "🌶" },
  { color: colors[1], emoji: "🤑" },
  { color: colors[2], emoji: "🐙" },
  { color: colors[3], emoji: "🫐" },
  { color: colors[4], emoji: "🐳" },
  { color: colors[0], emoji: "🤶" },
  { color: colors[5], emoji: "🌲" },
  { color: colors[6], emoji: "🌞" },
  { color: colors[7], emoji: "🐒" },
  { color: colors[8], emoji: "🐵" },
  { color: colors[9], emoji: "🦊" },
  { color: colors[10], emoji: "🐼" },
  { color: colors[11], emoji: "🦄" },
  { color: colors[12], emoji: "🐷" },
  { color: colors[13], emoji: "🐧" },
  { color: colors[8], emoji: "🦩" },
  { color: colors[14], emoji: "👽" },
  { color: colors[0], emoji: "🎈" },
  { color: colors[8], emoji: "🍉" },
  { color: colors[1], emoji: "🎉" },
  { color: colors[15], emoji: "🐲" },
  { color: colors[16], emoji: "🌎" },
  { color: colors[17], emoji: "🍊" },
  { color: colors[18], emoji: "🐭" },
  { color: colors[19], emoji: "🍣" },
  { color: colors[1], emoji: "🐥" },
  { color: colors[20], emoji: "👾" },
  { color: colors[15], emoji: "🥦" },
  { color: colors[0], emoji: "👹" },
  { color: colors[17], emoji: "🙀" },
  { color: colors[4], emoji: "⛱" },
  { color: colors[21], emoji: "⛵️" },
  { color: colors[17], emoji: "🥳" },
  { color: colors[8], emoji: "🤯" },
  { color: colors[22], emoji: "🤠" }
];
function hashCode(text) {
  let hash = 0;
  if (text.length === 0)
    return hash;
  for (let i = 0; i < text.length; i++) {
    const chr = text.charCodeAt(i);
    hash = (hash << 5) - hash + chr;
    hash |= 0;
  }
  return hash;
}
function emojiAvatarForAddress(address) {
  const resolvedAddress = typeof address === "string" ? address : "";
  const avatarIndex = Math.abs(hashCode(resolvedAddress.toLowerCase()) % avatars.length);
  return avatars[avatarIndex != null ? avatarIndex : 0];
}
var EmojiAvatar = ({ address, ensImage, size }) => {
  const [loaded, setLoaded] = (0, import_react11.useState)(false);
  (0, import_react11.useEffect)(() => {
    if (ensImage) {
      const img = new Image();
      img.src = ensImage;
      img.onload = () => setLoaded(true);
    }
  }, [ensImage]);
  const { color: backgroundColor, emoji } = (0, import_react11.useMemo)(() => emojiAvatarForAddress(address), [address]);
  return ensImage ? loaded ? import_react11.default.createElement(Box, {
    backgroundSize: "cover",
    borderRadius: "full",
    position: "absolute",
    style: {
      backgroundImage: `url(${ensImage})`,
      backgroundPosition: "center",
      height: size,
      width: size
    }
  }) : import_react11.default.createElement(Box, {
    alignItems: "center",
    backgroundSize: "cover",
    borderRadius: "full",
    color: "modalText",
    display: "flex",
    justifyContent: "center",
    position: "absolute",
    style: {
      height: size,
      width: size
    }
  }, import_react11.default.createElement(SpinnerIcon, null)) : import_react11.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    justifyContent: "center",
    overflow: "hidden",
    style: {
      ...!ensImage && { backgroundColor },
      height: size,
      width: size
    }
  }, emoji);
};
var defaultAvatar = EmojiAvatar;
var AvatarContext = (0, import_react10.createContext)(defaultAvatar);
function Avatar({ address, imageUrl, loading, size }) {
  const AvatarComponent2 = (0, import_react8.useContext)(AvatarContext);
  return import_react8.default.createElement(Box, {
    "aria-hidden": true,
    borderRadius: "full",
    overflow: "hidden",
    position: "relative",
    style: {
      height: `${size}px`,
      width: `${size}px`
    },
    userSelect: "none"
  }, import_react8.default.createElement(Box, {
    alignItems: "center",
    borderRadius: "full",
    display: "flex",
    justifyContent: "center",
    overflow: "hidden",
    position: "absolute",
    style: {
      fontSize: `${Math.round(size * 0.55)}px`,
      height: `${size}px`,
      transform: loading ? "scale(0.72)" : void 0,
      transition: ".25s ease",
      transitionDelay: loading ? void 0 : ".1s",
      width: `${size}px`,
      willChange: "transform"
    },
    userSelect: "none"
  }, import_react8.default.createElement(AvatarComponent2, {
    address,
    ensImage: imageUrl,
    size
  })), loading && import_react8.default.createElement(Box, {
    color: "accentColor",
    display: "flex",
    height: "full",
    position: "absolute",
    width: "full"
  }, import_react8.default.createElement(SpinnerIcon, {
    height: "100%",
    width: "100%"
  })));
}
var DropdownIcon = () => import_react12.default.createElement("svg", {
  fill: "none",
  height: "7",
  width: "14",
  xmlns: "http://www.w3.org/2000/svg"
}, import_react12.default.createElement("title", null, "Dropdown"), import_react12.default.createElement("path", {
  d: "M12.75 1.54001L8.51647 5.0038C7.77974 5.60658 6.72026 5.60658 5.98352 5.0038L1.75 1.54001",
  stroke: "currentColor",
  strokeLinecap: "round",
  strokeLinejoin: "round",
  strokeWidth: "2.5",
  xmlns: "http://www.w3.org/2000/svg"
}));
var defaultOptions = {
  defaultLocale: "en",
  locale: "en"
};
var I18n = class {
  constructor(localeTranslations) {
    this.listeners = /* @__PURE__ */ new Set();
    this.defaultLocale = defaultOptions.defaultLocale;
    this.enableFallback = false;
    this.locale = defaultOptions.locale;
    this.cachedLocales = [];
    this.translations = {};
    for (const [locale, translation] of Object.entries(localeTranslations)) {
      this.cachedLocales = [...this.cachedLocales, locale];
      this.translations = {
        ...this.translations,
        ...this.flattenTranslation(translation, locale)
      };
    }
  }
  missingMessage(key) {
    return `[missing: "${this.locale}.${key}" translation]`;
  }
  flattenTranslation(translationObject, locale) {
    const result = {};
    const flatten2 = (currentTranslationObj, parentKey) => {
      for (const key of Object.keys(currentTranslationObj)) {
        const newKey = `${parentKey}.${key}`;
        const currentValue = currentTranslationObj[key];
        if (typeof currentValue === "object" && currentValue !== null) {
          flatten2(currentValue, newKey);
        } else {
          result[newKey] = currentValue;
        }
      }
    };
    flatten2(translationObject, locale);
    return result;
  }
  translateWithReplacements(translation, replacements = {}) {
    let translatedString = translation;
    for (const placeholder in replacements) {
      const replacementValue = replacements[placeholder];
      translatedString = translatedString.replace(`%{${placeholder}}`, replacementValue);
    }
    return translatedString;
  }
  t(key, replacements) {
    const translationKey = `${this.locale}.${key}`;
    const translation = this.translations[translationKey];
    if (!translation) {
      if (this.enableFallback) {
        const fallbackTranslationKey = `${this.defaultLocale}.${key}`;
        const fallbackTranslation = this.translations[fallbackTranslationKey];
        if (fallbackTranslation) {
          return this.translateWithReplacements(fallbackTranslation, replacements);
        }
      }
      return this.missingMessage(key);
    }
    return this.translateWithReplacements(translation, replacements);
  }
  isLocaleCached(locale) {
    return this.cachedLocales.includes(locale);
  }
  updateLocale(locale) {
    this.locale = locale;
    this.notifyListeners();
  }
  setTranslations(locale, translations) {
    const cachedLocale = this.isLocaleCached(locale);
    if (!cachedLocale) {
      this.cachedLocales = [...this.cachedLocales, locale];
      this.translations = {
        ...this.translations,
        ...this.flattenTranslation(translations, locale)
      };
    }
    this.locale = locale;
    this.notifyListeners();
  }
  notifyListeners() {
    for (const listener of this.listeners) {
      listener();
    }
  }
  onChange(fn) {
    this.listeners.add(fn);
    return () => {
      this.listeners.delete(fn);
    };
  }
};
var i18n = new I18n({
  en: JSON.parse(en_US_default),
  "en-US": JSON.parse(en_US_default)
});
i18n.defaultLocale = "en-US";
i18n.locale = "en-US";
i18n.enableFallback = true;
var fetchTranslations = async (locale) => {
  switch (locale) {
    case "ar":
    case "ar-AR":
      return (await import("./ar_AR-PQJDYWVZ-VFBKE5GI.js")).default;
    case "en":
    case "en-US":
      return (await import("./en_US-76OUGVJ2-M7RVXYUP.js")).default;
    case "es":
    case "es-419":
      return (await import("./es_419-GLICGTYE-FUIS32BW.js")).default;
    case "fr":
    case "fr-FR":
      return (await import("./fr_FR-UC7Z4T6O-HYQND4RO.js")).default;
    case "hi":
    case "hi-IN":
      return (await import("./hi_IN-RGKVTIVE-CALQBI47.js")).default;
    case "id":
    case "id-ID":
      return (await import("./id_ID-3SKVJ2RK-XBHA2IMX.js")).default;
    case "ja":
    case "ja-JP":
      return (await import("./ja_JP-GYCPH6AT-NMSJCXWW.js")).default;
    case "ko":
    case "ko-KR":
      return (await import("./ko_KR-V2HAEAHG-XFE4S5Z3.js")).default;
    case "pt":
    case "pt-BR":
      return (await import("./pt_BR-JDDVMLRA-2KTUD4TY.js")).default;
    case "ru":
    case "ru-RU":
      return (await import("./ru_RU-3W6WVVOI-TORHYYYE.js")).default;
    case "th":
    case "th-TH":
      return (await import("./th_TH-UWDENI2F-X3VIJTUR.js")).default;
    case "tr":
    case "tr-TR":
      return (await import("./tr_TR-NAI3OICG-BYGC22TI.js")).default;
    case "ua":
    case "uk-UA":
      return (await import("./uk_UA-H7BFRWP5-YP2RDM56.js")).default;
    case "zh":
    case "zh-CN":
      return (await import("./zh_CN-BO5MSGV2-7IKXILYD.js")).default;
    default:
      return (await import("./en_US-76OUGVJ2-M7RVXYUP.js")).default;
  }
};
async function setLocale(locale) {
  const isCached = i18n.isLocaleCached(locale);
  if (isCached) {
    i18n.updateLocale(locale);
    return;
  }
  const translations = await fetchTranslations(locale);
  i18n.setTranslations(locale, JSON.parse(translations));
}
var detectedBrowserLocale = () => {
  var _a;
  if (typeof window !== "undefined" && typeof navigator !== "undefined") {
    if ((_a = navigator.languages) == null ? void 0 : _a.length) {
      return navigator.languages[0];
    }
    if (navigator.language) {
      return navigator.language;
    }
  }
};
var I18nContext = (0, import_react13.createContext)({ i18n });
var I18nProvider = ({ children, locale }) => {
  const [updateCount, setUpdateCount] = (0, import_react13.useState)(0);
  const browserLocale = (0, import_react13.useMemo)(() => detectedBrowserLocale(), []);
  (0, import_react13.useEffect)(() => {
    const unsubscribe = i18n.onChange(() => {
      setUpdateCount((count) => count + 1);
    });
    return unsubscribe;
  }, []);
  (0, import_react13.useEffect)(() => {
    if (locale && locale !== i18n.locale) {
      setLocale(locale);
    } else if (!locale && browserLocale && browserLocale !== i18n.locale) {
      setLocale(browserLocale);
    }
  }, [locale, browserLocale]);
  const memoizedValue = (0, import_react13.useMemo)(() => {
    const t = (key, options) => i18n.t(key, options);
    return { t, i18n };
  }, [updateCount]);
  return import_react13.default.createElement(I18nContext.Provider, {
    value: memoizedValue
  }, children);
};
function isNotNullish(value) {
  return value != null;
}
var arbitrumIcon = {
  iconBackground: "#96bedc",
  iconUrl: async () => (await import("./arbitrum-LYDBJZP3-GEIHJWSM.js")).default
};
var avalancheIcon = {
  iconBackground: "#e84141",
  iconUrl: async () => (await import("./avalanche-TFPKP544-MHOCSF24.js")).default
};
var baseIcon = {
  iconBackground: "#0052ff",
  iconUrl: async () => (await import("./base-3MIUIYGA-XUDDQ4H4.js")).default
};
var bscIcon = {
  iconBackground: "#ebac0e",
  iconUrl: async () => (await import("./bsc-S2GSW6VX-ECEMQSPC.js")).default
};
var cronosIcon = {
  iconBackground: "#002D74",
  iconUrl: async () => (await import("./cronos-DQKKIEX7-T6V2VNQR.js")).default
};
var ethereumIcon = {
  iconBackground: "#484c50",
  iconUrl: async () => (await import("./ethereum-4FY57XJF-6P77MWLN.js")).default
};
var hardhatIcon = {
  iconBackground: "#f9f7ec",
  iconUrl: async () => (await import("./hardhat-ARRFHFKB-C5DI57RA.js")).default
};
var optimismIcon = {
  iconBackground: "#ff5a57",
  iconUrl: async () => (await import("./optimism-UUP5Y7TB-GVHADC5V.js")).default
};
var polygonIcon = {
  iconBackground: "#9f71ec",
  iconUrl: async () => (await import("./polygon-Z4QITDL7-22IUNWIP.js")).default
};
var xdcIcon = {
  iconBackground: "#f9f7ec",
  iconUrl: async () => (await import("./xdc-5UHQ25DW-ZWI4MESG.js")).default
};
var zkSyncIcon = {
  iconBackground: "#f9f7ec",
  iconUrl: async () => (await import("./zkSync-XRUC4ZHO-6K3CSY3U.js")).default
};
var zoraIcon = {
  iconBackground: "#000000",
  iconUrl: async () => (await import("./zora-KVO7WIOK-UN44PJ7V.js")).default
};
var chainMetadataByName = {
  arbitrum: { chainId: 42161, name: "Arbitrum", ...arbitrumIcon },
  arbitrumGoerli: { chainId: 421613, ...arbitrumIcon },
  arbitrumSepolia: { chainId: 421614, ...arbitrumIcon },
  avalanche: { chainId: 43114, ...avalancheIcon },
  avalancheFuji: { chainId: 43113, ...avalancheIcon },
  base: { chainId: 8453, name: "Base", ...baseIcon },
  baseGoerli: { chainId: 84531, ...baseIcon },
  baseSepolia: { chainId: 84532, ...baseIcon },
  bsc: { chainId: 56, name: "BSC", ...bscIcon },
  bscTestnet: { chainId: 97, ...bscIcon },
  cronos: { chainId: 25, ...cronosIcon },
  cronosTestnet: { chainId: 338, ...cronosIcon },
  goerli: { chainId: 5, ...ethereumIcon },
  hardhat: { chainId: 31337, ...hardhatIcon },
  holesky: { chainId: 17e3, ...ethereumIcon },
  kovan: { chainId: 42, ...ethereumIcon },
  localhost: { chainId: 1337, ...ethereumIcon },
  mainnet: { chainId: 1, name: "Ethereum", ...ethereumIcon },
  optimism: { chainId: 10, name: "Optimism", ...optimismIcon },
  optimismGoerli: { chainId: 420, ...optimismIcon },
  optimismKovan: { chainId: 69, ...optimismIcon },
  optimismSepolia: { chainId: 11155420, ...optimismIcon },
  polygon: { chainId: 137, name: "Polygon", ...polygonIcon },
  polygonMumbai: { chainId: 80001, ...polygonIcon },
  rinkeby: { chainId: 4, ...ethereumIcon },
  ropsten: { chainId: 3, ...ethereumIcon },
  sepolia: { chainId: 11155111, ...ethereumIcon },
  xdc: { chainId: 50, name: "XinFin", ...xdcIcon },
  xdcTestnet: { chainId: 51, ...xdcIcon },
  zkSync: { chainId: 324, name: "zkSync", ...zkSyncIcon },
  zkSyncTestnet: { chainId: 280, ...zkSyncIcon },
  zora: { chainId: 7777777, name: "Zora", ...zoraIcon },
  zoraSepolia: { chainId: 999999999, ...zoraIcon },
  zoraTestnet: { chainId: 999, ...zoraIcon }
};
var chainMetadataById = Object.fromEntries(Object.values(chainMetadataByName).filter(isNotNullish).map(({ chainId, ...metadata }) => [chainId, metadata]));
var provideRainbowKitChains = (chains) => chains.map((chain) => {
  var _a, _b, _c, _d;
  const defaultMetadata = (_a = chainMetadataById[chain.id]) != null ? _a : {};
  return {
    ...chain,
    name: (_b = defaultMetadata.name) != null ? _b : chain.name,
    iconUrl: (_c = chain.iconUrl) != null ? _c : defaultMetadata.iconUrl,
    iconBackground: (_d = chain.iconBackground) != null ? _d : defaultMetadata.iconBackground
  };
});
var RainbowKitChainContext = (0, import_react14.createContext)({
  chains: []
});
function RainbowKitChainProvider({
  chains,
  children,
  initialChain
}) {
  return import_react14.default.createElement(RainbowKitChainContext.Provider, {
    value: (0, import_react14.useMemo)(() => ({
      chains: provideRainbowKitChains(chains),
      initialChainId: typeof initialChain === "number" ? initialChain : initialChain == null ? void 0 : initialChain.id
    }), [chains, initialChain])
  }, children);
}
var useRainbowKitChains = () => (0, import_react14.useContext)(RainbowKitChainContext).chains;
var useInitialChainId = () => (0, import_react14.useContext)(RainbowKitChainContext).initialChainId;
var useRainbowKitChainsById = () => {
  const rainbowkitChains = useRainbowKitChains();
  return (0, import_react14.useMemo)(() => {
    const rainbowkitChainsById = {};
    for (const rkChain of rainbowkitChains) {
      rainbowkitChainsById[rkChain.id] = rkChain;
    }
    return rainbowkitChainsById;
  }, [rainbowkitChains]);
};
var ShowBalanceContext = (0, import_react15.createContext)({
  showBalance: void 0,
  setShowBalance: () => {
  }
});
function ShowBalanceProvider({ children }) {
  const [showBalance, setShowBalance] = (0, import_react15.useState)();
  return import_react15.default.createElement(ShowBalanceContext.Provider, {
    value: { showBalance, setShowBalance }
  }, children);
}
var useShowBalance = () => (0, import_react15.useContext)(ShowBalanceContext);
function useIsMounted() {
  const [isMounted, setIsMounted] = (0, import_react17.useState)(false);
  (0, import_react17.useEffect)(() => {
    setIsMounted(true);
    return () => {
      setIsMounted(false);
    };
  }, []);
  return (0, import_react17.useCallback)(() => isMounted, [isMounted]);
}
function useMainnet() {
  const chainId = mainnet.id;
  const provider = usePublicClient();
  const chains = Array.isArray(provider.chains) ? provider.chains : [];
  const enabled = chains == null ? void 0 : chains.some((chain) => (chain == null ? void 0 : chain.id) === chainId);
  return { chainId, enabled };
}
function useMainnetEnsAvatar(name) {
  const { chainId, enabled } = useMainnet();
  const { data: ensAvatar } = useEnsAvatar({
    chainId,
    enabled,
    name
  });
  return ensAvatar;
}
function useMainnetEnsName(address) {
  const { chainId, enabled } = useMainnet();
  const { data: ensName } = useEnsName({
    address,
    chainId,
    enabled
  });
  return ensName;
}
function useChainId() {
  var _a;
  const { chain: activeChain } = useNetwork();
  return (_a = activeChain == null ? void 0 : activeChain.id) != null ? _a : null;
}
var storageKey = "rk-transactions";
function safeParseJsonData(string) {
  try {
    const value = string ? JSON.parse(string) : {};
    return typeof value === "object" ? value : {};
  } catch {
    return {};
  }
}
function loadData() {
  return safeParseJsonData(typeof localStorage !== "undefined" ? localStorage.getItem(storageKey) : null);
}
var transactionHashRegex = /^0x([A-Fa-f0-9]{64})$/;
function validateTransaction(transaction) {
  const errors = [];
  if (!transactionHashRegex.test(transaction.hash)) {
    errors.push("Invalid transaction hash");
  }
  if (typeof transaction.description !== "string") {
    errors.push("Transaction must have a description");
  }
  if (typeof transaction.confirmations !== "undefined" && (!Number.isInteger(transaction.confirmations) || transaction.confirmations < 1)) {
    errors.push("Transaction confirmations must be a positiver integer");
  }
  return errors;
}
function createTransactionStore({
  provider: initialProvider
}) {
  let data = loadData();
  let provider = initialProvider;
  const listeners = /* @__PURE__ */ new Set();
  const transactionRequestCache = /* @__PURE__ */ new Map();
  function setProvider(newProvider) {
    provider = newProvider;
  }
  function getTransactions(account, chainId) {
    var _a, _b;
    return (_b = (_a = data[account]) == null ? void 0 : _a[chainId]) != null ? _b : [];
  }
  function addTransaction(account, chainId, transaction) {
    const errors = validateTransaction(transaction);
    if (errors.length > 0) {
      throw new Error(["Unable to add transaction", ...errors].join("\n"));
    }
    updateTransactions(account, chainId, (transactions) => {
      return [
        { ...transaction, status: "pending" },
        ...transactions.filter(({ hash }) => {
          return hash !== transaction.hash;
        })
      ];
    });
  }
  function clearTransactions(account, chainId) {
    updateTransactions(account, chainId, () => {
      return [];
    });
  }
  function setTransactionStatus(account, chainId, hash, status) {
    updateTransactions(account, chainId, (transactions) => {
      return transactions.map((transaction) => transaction.hash === hash ? { ...transaction, status } : transaction);
    });
  }
  async function waitForPendingTransactions(account, chainId) {
    await Promise.all(getTransactions(account, chainId).filter((transaction) => transaction.status === "pending").map(async (transaction) => {
      const { confirmations, hash } = transaction;
      const existingRequest = transactionRequestCache.get(hash);
      if (existingRequest) {
        return await existingRequest;
      }
      const requestPromise = provider.waitForTransactionReceipt({
        confirmations,
        hash,
        timeout: 3e5
      }).then(({ status }) => {
        transactionRequestCache.delete(hash);
        if (status === void 0) {
          return;
        }
        setTransactionStatus(account, chainId, hash, status === 0 || status === "reverted" ? "failed" : "confirmed");
      }).catch(() => {
        setTransactionStatus(account, chainId, hash, "failed");
      });
      transactionRequestCache.set(hash, requestPromise);
      return await requestPromise;
    }));
  }
  function updateTransactions(account, chainId, updateFn) {
    var _a, _b;
    data = loadData();
    data[account] = (_a = data[account]) != null ? _a : {};
    let completedTransactionCount = 0;
    const MAX_COMPLETED_TRANSACTIONS = 10;
    const transactions = updateFn((_b = data[account][chainId]) != null ? _b : []).filter(({ status }) => {
      return status === "pending" ? true : completedTransactionCount++ <= MAX_COMPLETED_TRANSACTIONS;
    });
    data[account][chainId] = transactions.length > 0 ? transactions : void 0;
    persistData();
    notifyListeners();
    waitForPendingTransactions(account, chainId);
  }
  function persistData() {
    localStorage.setItem(storageKey, JSON.stringify(data));
  }
  function notifyListeners() {
    for (const listener of listeners) {
      listener();
    }
  }
  function onChange(fn) {
    listeners.add(fn);
    return () => {
      listeners.delete(fn);
    };
  }
  return {
    addTransaction,
    clearTransactions,
    getTransactions,
    onChange,
    setProvider,
    waitForPendingTransactions
  };
}
var storeSingleton;
var TransactionStoreContext = (0, import_react19.createContext)(null);
function TransactionStoreProvider({
  children
}) {
  const provider = usePublicClient();
  const { address } = useAccount();
  const chainId = useChainId();
  const [store] = (0, import_react19.useState)(() => storeSingleton != null ? storeSingleton : storeSingleton = createTransactionStore({ provider }));
  (0, import_react19.useEffect)(() => {
    store.setProvider(provider);
  }, [store, provider]);
  (0, import_react19.useEffect)(() => {
    if (address && chainId) {
      store.waitForPendingTransactions(address, chainId);
    }
  }, [store, address, chainId]);
  return import_react19.default.createElement(TransactionStoreContext.Provider, {
    value: store
  }, children);
}
function useTransactionStore() {
  const store = (0, import_react19.useContext)(TransactionStoreContext);
  if (!store) {
    throw new Error("Transaction hooks must be used within RainbowKitProvider");
  }
  return store;
}
function useRecentTransactions() {
  const store = useTransactionStore();
  const { address } = useAccount();
  const chainId = useChainId();
  const [transactions, setTransactions] = (0, import_react18.useState)(() => store && address && chainId ? store.getTransactions(address, chainId) : []);
  (0, import_react18.useEffect)(() => {
    if (store && address && chainId) {
      setTransactions(store.getTransactions(address, chainId));
      return store.onChange(() => {
        setTransactions(store.getTransactions(address, chainId));
      });
    }
  }, [store, address, chainId]);
  return transactions;
}
var resolveThemeVars = (theme) => typeof theme === "function" ? theme() : theme;
function cssObjectFromTheme(theme, { extends: baseTheme2 } = {}) {
  const resolvedThemeVars = {
    ...assignInlineVars(themeVars, resolveThemeVars(theme))
  };
  if (!baseTheme2) {
    return resolvedThemeVars;
  }
  const resolvedBaseThemeVars = assignInlineVars(themeVars, resolveThemeVars(baseTheme2));
  const filteredVars = Object.fromEntries(Object.entries(resolvedThemeVars).filter(([varName, value]) => value !== resolvedBaseThemeVars[varName]));
  return filteredVars;
}
function cssStringFromTheme(theme, options = {}) {
  return Object.entries(cssObjectFromTheme(theme, options)).map(([key, value]) => `${key}:${value.replace(/[:;{}</>]/g, "")};`).join("");
}
var defaultAppInfo = {
  appName: void 0,
  disclaimer: void 0,
  learnMoreUrl: "https://learn.rainbow.me/understanding-web3?utm_source=rainbowkit&utm_campaign=learnmore"
};
var AppContext = (0, import_react24.createContext)(defaultAppInfo);
var CoolModeContext = (0, import_react25.createContext)(false);
function debounce(fn, ms) {
  let timer;
  return () => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      timer = null;
      fn();
    }, ms);
  };
}
var useWindowSize = () => {
  const [windowSize, setWindowSize] = (0, import_react27.useState)({
    height: void 0,
    width: void 0
  });
  (0, import_react27.useEffect)(() => {
    const handleResize = debounce(() => {
      setWindowSize({
        height: window.innerHeight,
        width: window.innerWidth
      });
    }, 500);
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  return windowSize;
};
var WalletButtonContext = (0, import_react28.createContext)({
  connector: null,
  setConnector: () => {
  }
});
function WalletButtonProvider({ children }) {
  const [connector, setConnector] = (0, import_react28.useState)(null);
  return import_react28.default.createElement(WalletButtonContext.Provider, {
    value: (0, import_react28.useMemo)(() => ({
      connector,
      setConnector
    }), [connector])
  }, children);
}
var ModalSizeOptions = {
  COMPACT: "compact",
  WIDE: "wide"
};
var ModalSizeContext = (0, import_react26.createContext)(ModalSizeOptions.WIDE);
function ModalSizeProvider({
  children,
  modalSize
}) {
  const { width } = useWindowSize();
  const isSmallScreen = width && width < largeScreenMinWidth;
  const { connector } = (0, import_react26.useContext)(WalletButtonContext);
  return import_react26.default.createElement(ModalSizeContext.Provider, {
    value: isSmallScreen || connector ? ModalSizeOptions.COMPACT : modalSize
  }, children);
}
var ShowRecentTransactionsContext = (0, import_react29.createContext)(false);
var storageKey2 = "rk-version";
function setRainbowKitVersion({ version }) {
  localStorage.setItem(storageKey2, version);
}
function useFingerprint() {
  const fingerprint = (0, import_react30.useCallback)(() => {
    setRainbowKitVersion({ version: "1.3.7" });
  }, []);
  (0, import_react30.useEffect)(() => {
    fingerprint();
  }, [fingerprint]);
}
function flatten(array) {
  const flattenedItems = [];
  for (const items of array) {
    flattenedItems.push(...items);
  }
  return flattenedItems;
}
function indexBy(items, getKey) {
  const indexedItems = {};
  for (const item of items) {
    const key = getKey(item);
    if (!key) {
      continue;
    }
    indexedItems[key] = item;
  }
  return indexedItems;
}
function isSafari() {
  return typeof navigator !== "undefined" && /Version\/([0-9._]+).*Safari/.test(navigator.userAgent);
}
function isArc() {
  return typeof document !== "undefined" && getComputedStyle(document.body).getPropertyValue("--arc-palette-focus") !== "";
}
function getBrowser() {
  var _a;
  if (typeof navigator === "undefined")
    return "Browser";
  const ua2 = navigator.userAgent.toLowerCase();
  if ((_a = navigator.brave) == null ? void 0 : _a.isBrave)
    return "Brave";
  if (ua2.indexOf("edg/") > -1)
    return "Edge";
  if (ua2.indexOf("op") > -1)
    return "Opera";
  if (isArc())
    return "Arc";
  if (ua2.indexOf("chrome") > -1)
    return "Chrome";
  if (ua2.indexOf("firefox") > -1)
    return "Firefox";
  if (isSafari())
    return "Safari";
  return "Browser";
}
var ua = (0, import_ua_parser_js.UAParser)();
var { os } = ua;
function isWindows() {
  return os.name === "Windows";
}
function isMacOS() {
  return os.name === "Mac OS";
}
function isLinux() {
  return ["Ubuntu", "Mint", "Fedora", "Debian", "Arch", "Linux"].includes(os.name);
}
function getPlatform() {
  if (isWindows())
    return "Windows";
  if (isMacOS())
    return "macOS";
  if (isLinux())
    return "Linux";
  return "Desktop";
}
var getExtensionDownloadUrl = (wallet) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;
  const browser = getBrowser();
  return (_l = {
    [
      "Arc"
      /* Arc */
    ]: (_a = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _a.chrome,
    [
      "Brave"
      /* Brave */
    ]: (_b = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _b.chrome,
    [
      "Chrome"
      /* Chrome */
    ]: (_c = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _c.chrome,
    [
      "Edge"
      /* Edge */
    ]: ((_d = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _d.edge) || ((_e = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _e.chrome),
    [
      "Firefox"
      /* Firefox */
    ]: (_f = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _f.firefox,
    [
      "Opera"
      /* Opera */
    ]: ((_g = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _g.opera) || ((_h = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _h.chrome),
    [
      "Safari"
      /* Safari */
    ]: (_i = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _i.safari,
    [
      "Browser"
      /* Browser */
    ]: (_j = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _j.browserExtension
  }[browser]) != null ? _l : (_k = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _k.browserExtension;
};
var getMobileDownloadUrl = (wallet) => {
  var _a, _b, _c, _d;
  const ios = isIOS();
  return (_d = ios ? (_a = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _a.ios : (_b = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _b.android) != null ? _d : (_c = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _c.mobile;
};
var getDesktopDownloadUrl = (wallet) => {
  var _a, _b, _c, _d, _e, _f;
  const platform = getPlatform();
  return (_f = {
    [
      "Windows"
      /* Windows */
    ]: (_a = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _a.windows,
    [
      "macOS"
      /* MacOS */
    ]: (_b = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _b.macos,
    [
      "Linux"
      /* Linux */
    ]: (_c = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _c.linux,
    [
      "Desktop"
      /* Desktop */
    ]: (_d = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _d.desktop
  }[platform]) != null ? _f : (_e = wallet == null ? void 0 : wallet.downloadUrls) == null ? void 0 : _e.desktop;
};
var storageKey3 = "rk-recent";
function safeParseJsonArray(string) {
  try {
    const value = string ? JSON.parse(string) : [];
    return Array.isArray(value) ? value : [];
  } catch {
    return [];
  }
}
function getRecentWalletIds() {
  return typeof localStorage !== "undefined" ? safeParseJsonArray(localStorage.getItem(storageKey3)) : [];
}
function dedupe(array) {
  return [...new Set(array)];
}
function addRecentWalletId(walletId) {
  const newValue = dedupe([walletId, ...getRecentWalletIds()]);
  localStorage.setItem(storageKey3, JSON.stringify(newValue));
}
function useWalletConnectors() {
  var _a;
  const rainbowKitChains = useRainbowKitChains();
  const intialChainId = useInitialChainId();
  const { connectAsync, connectors: defaultConnectors_untyped } = useConnect();
  const defaultConnectors = defaultConnectors_untyped;
  async function connectWallet(walletId, connector) {
    var _a2, _b, _c;
    const walletChainId = await connector.getChainId();
    const result = await connectAsync({
      chainId: (_c = intialChainId != null ? intialChainId : (_a2 = rainbowKitChains.find(({ id }) => id === walletChainId)) == null ? void 0 : _a2.id) != null ? _c : (_b = rainbowKitChains[0]) == null ? void 0 : _b.id,
      connector
    });
    if (result) {
      addRecentWalletId(walletId);
    }
    return result;
  }
  async function connectToWalletConnectModal(walletId, walletConnectModalConnector) {
    try {
      return await connectWallet(walletId, walletConnectModalConnector);
    } catch (err) {
      const isUserRejection = err.name === "UserRejectedRequestError" || err.message === "Connection request reset. Please try again.";
      if (!isUserRejection) {
        throw err;
      }
    }
  }
  const walletInstances = flatten(defaultConnectors.map((connector) => {
    var _a2;
    return (_a2 = connector._wallets) != null ? _a2 : [];
  })).sort((a, b) => a.index - b.index);
  const walletInstanceById = indexBy(walletInstances, (walletInstance) => walletInstance.id);
  const MAX_RECENT_WALLETS = 3;
  const recentWallets = getRecentWalletIds().map((walletId) => walletInstanceById[walletId]).filter(isNotNullish).slice(0, MAX_RECENT_WALLETS);
  const groupedWallets = [
    ...recentWallets,
    ...walletInstances.filter((walletInstance) => !recentWallets.includes(walletInstance))
  ];
  const walletConnectors = [];
  for (const wallet of groupedWallets) {
    if (!wallet) {
      continue;
    }
    const recent = recentWallets.includes(wallet);
    walletConnectors.push({
      ...wallet,
      connect: () => wallet.connector.showQrModal ? connectToWalletConnectModal(wallet.id, wallet.connector) : connectWallet(wallet.id, wallet.connector),
      desktopDownloadUrl: getDesktopDownloadUrl(wallet),
      extensionDownloadUrl: getExtensionDownloadUrl(wallet),
      groupName: wallet.groupName,
      mobileDownloadUrl: getMobileDownloadUrl(wallet),
      onConnecting: (fn) => wallet.connector.on("message", ({ type }) => type === "connecting" ? fn() : void 0),
      ready: ((_a = wallet.installed) != null ? _a : true) && wallet.connector.ready,
      recent,
      showWalletConnectModal: wallet.walletConnectModalConnector ? () => connectToWalletConnectModal(wallet.id, wallet.walletConnectModalConnector) : void 0
    });
  }
  return walletConnectors;
}
var src = async () => (await import("./assets-26YY4GVD-VMA45AQX.js")).default;
var preloadAssetsIcon = () => loadImages(src);
var AssetsIcon = () => import_react32.default.createElement(AsyncImage, {
  background: "#d0d5de",
  borderRadius: "10",
  height: "48",
  src,
  width: "48"
});
var src2 = async () => (await import("./login-ZSMM5UYL-TDGOX3AQ.js")).default;
var preloadLoginIcon = () => loadImages(src2);
var LoginIcon = () => import_react33.default.createElement(AsyncImage, {
  background: "#d0d5de",
  borderRadius: "10",
  height: "48",
  src: src2,
  width: "48"
});
var Text = import_react36.default.forwardRef(({
  as = "div",
  children,
  className,
  color,
  display,
  font = "body",
  id,
  size = "16",
  style,
  tabIndex,
  textAlign = "inherit",
  weight = "regular",
  testId
}, ref) => {
  return import_react36.default.createElement(Box, {
    as,
    className,
    color,
    display,
    fontFamily: font,
    fontSize: size,
    fontWeight: weight,
    id,
    ref,
    style,
    tabIndex,
    textAlign,
    testId
  }, children);
});
Text.displayName = "Text";
var sizeVariants = {
  large: {
    fontSize: "16",
    paddingX: "24",
    paddingY: "10"
  },
  medium: {
    fontSize: "14",
    height: "28",
    paddingX: "12",
    paddingY: "4"
  },
  small: {
    fontSize: "14",
    paddingX: "10",
    paddingY: "5"
  }
};
function ActionButton({
  disabled = false,
  href,
  label,
  onClick,
  rel = "noreferrer noopener",
  size = "medium",
  target = "_blank",
  testId,
  type = "primary"
}) {
  const isPrimary = type === "primary";
  const isNotLarge = size !== "large";
  const mobile = isMobile();
  const background = !disabled ? isPrimary ? "accentColor" : isNotLarge ? "actionButtonSecondaryBackground" : null : "actionButtonSecondaryBackground";
  const { fontSize, height, paddingX, paddingY } = sizeVariants[size];
  const hasBorder = !mobile || !isNotLarge;
  return import_react35.default.createElement(Box, {
    ...href ? !disabled ? { as: "a", href, rel, target } : {} : { as: "button", type: "button" },
    onClick: !disabled ? onClick : void 0,
    ...hasBorder ? {
      borderColor: mobile && !isNotLarge && !isPrimary ? "actionButtonBorderMobile" : "actionButtonBorder",
      borderStyle: "solid",
      borderWidth: "1"
    } : {},
    borderRadius: "actionButton",
    className: !disabled && touchableStyles({ active: "shrinkSm", hover: "grow" }),
    display: "block",
    paddingX,
    paddingY,
    style: { willChange: "transform" },
    testId,
    textAlign: "center",
    transition: "transform",
    ...background ? { background } : {},
    ...height ? { height } : {}
  }, import_react35.default.createElement(Text, {
    color: !disabled ? isPrimary ? "accentColorForeground" : "accentColor" : "modalTextSecondary",
    size: fontSize,
    weight: "bold"
  }, label));
}
var CloseIcon = () => {
  return isMobile() ? import_react38.default.createElement("svg", {
    "aria-hidden": true,
    fill: "none",
    height: "11.5",
    viewBox: "0 0 11.5 11.5",
    width: "11.5",
    xmlns: "http://www.w3.org/2000/svg"
  }, import_react38.default.createElement("title", null, "Close"), import_react38.default.createElement("path", {
    d: "M2.13388 0.366117C1.64573 -0.122039 0.854272 -0.122039 0.366117 0.366117C-0.122039 0.854272 -0.122039 1.64573 0.366117 2.13388L3.98223 5.75L0.366117 9.36612C-0.122039 9.85427 -0.122039 10.6457 0.366117 11.1339C0.854272 11.622 1.64573 11.622 2.13388 11.1339L5.75 7.51777L9.36612 11.1339C9.85427 11.622 10.6457 11.622 11.1339 11.1339C11.622 10.6457 11.622 9.85427 11.1339 9.36612L7.51777 5.75L11.1339 2.13388C11.622 1.64573 11.622 0.854272 11.1339 0.366117C10.6457 -0.122039 9.85427 -0.122039 9.36612 0.366117L5.75 3.98223L2.13388 0.366117Z",
    fill: "currentColor"
  })) : import_react38.default.createElement("svg", {
    "aria-hidden": true,
    fill: "none",
    height: "10",
    viewBox: "0 0 10 10",
    width: "10",
    xmlns: "http://www.w3.org/2000/svg"
  }, import_react38.default.createElement("title", null, "Close"), import_react38.default.createElement("path", {
    d: "M1.70711 0.292893C1.31658 -0.0976311 0.683417 -0.0976311 0.292893 0.292893C-0.0976311 0.683417 -0.0976311 1.31658 0.292893 1.70711L3.58579 5L0.292893 8.29289C-0.0976311 8.68342 -0.0976311 9.31658 0.292893 9.70711C0.683417 10.0976 1.31658 10.0976 1.70711 9.70711L5 6.41421L8.29289 9.70711C8.68342 10.0976 9.31658 10.0976 9.70711 9.70711C10.0976 9.31658 10.0976 8.68342 9.70711 8.29289L6.41421 5L9.70711 1.70711C10.0976 1.31658 10.0976 0.683417 9.70711 0.292893C9.31658 -0.0976311 8.68342 -0.0976311 8.29289 0.292893L5 3.58579L1.70711 0.292893Z",
    fill: "currentColor"
  }));
};
var CloseButton = ({
  "aria-label": ariaLabel = "Close",
  onClose
}) => {
  const mobile = isMobile();
  return import_react37.default.createElement(Box, {
    alignItems: "center",
    "aria-label": ariaLabel,
    as: "button",
    background: "closeButtonBackground",
    borderColor: "actionButtonBorder",
    borderRadius: "full",
    borderStyle: "solid",
    borderWidth: mobile ? "0" : "1",
    className: touchableStyles({ active: "shrinkSm", hover: "growLg" }),
    color: "closeButton",
    display: "flex",
    height: mobile ? "30" : "28",
    justifyContent: "center",
    onClick: onClose,
    style: { willChange: "transform" },
    transition: "default",
    type: "button",
    width: mobile ? "30" : "28"
  }, import_react37.default.createElement(CloseIcon, null));
};
var signInIcon = async () => (await import("./sign-FZVB2CS6-3PEOK7NA.js")).default;
function SignIn({
  onClose,
  onCloseModal
}) {
  const { i18n: i18n2 } = (0, import_react34.useContext)(I18nContext);
  const [{ status, ...state }, setState] = import_react34.default.useState({ status: "idle" });
  const authAdapter = useAuthenticationAdapter();
  const getNonce2 = (0, import_react34.useCallback)(async () => {
    try {
      const nonce = await authAdapter.getNonce();
      setState((x) => ({ ...x, nonce }));
    } catch {
      setState((x) => ({
        ...x,
        errorMessage: i18n2.t("sign_in.message.preparing_error"),
        status: "idle"
      }));
    }
  }, [authAdapter, i18n2.t]);
  const onceRef = (0, import_react34.useRef)(false);
  import_react34.default.useEffect(() => {
    if (onceRef.current)
      return;
    onceRef.current = true;
    getNonce2();
  }, [getNonce2]);
  const mobile = isMobile();
  const { address } = useAccount();
  const { chain: activeChain } = useNetwork();
  const { signMessageAsync } = useSignMessage();
  const signIn = async () => {
    try {
      const chainId = activeChain == null ? void 0 : activeChain.id;
      const { nonce } = state;
      if (!address || !chainId || !nonce) {
        return;
      }
      setState((x) => ({
        ...x,
        errorMessage: void 0,
        status: "signing"
      }));
      const message = authAdapter.createMessage({ address, chainId, nonce });
      let signature;
      try {
        signature = await signMessageAsync({
          message: authAdapter.getMessageBody({ message })
        });
      } catch (error) {
        if (error instanceof UserRejectedRequestError) {
          return setState((x) => ({
            ...x,
            status: "idle"
          }));
        }
        return setState((x) => ({
          ...x,
          errorMessage: i18n2.t("sign_in.signature.signing_error"),
          status: "idle"
        }));
      }
      setState((x) => ({ ...x, status: "verifying" }));
      try {
        const verified = await authAdapter.verify({ message, signature });
        if (verified) {
          onCloseModal();
          return;
        }
        throw new Error();
      } catch {
        return setState((x) => ({
          ...x,
          errorMessage: i18n2.t("sign_in.signature.verifying_error"),
          status: "idle"
        }));
      }
    } catch {
      setState({
        errorMessage: i18n2.t("sign_in.signature.oops_error"),
        status: "idle"
      });
    }
  };
  return import_react34.default.createElement(Box, {
    position: "relative"
  }, import_react34.default.createElement(Box, {
    display: "flex",
    paddingRight: "16",
    paddingTop: "16",
    position: "absolute",
    right: "0"
  }, import_react34.default.createElement(CloseButton, {
    onClose
  })), import_react34.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: mobile ? "32" : "24",
    padding: "24",
    paddingX: "18",
    style: { paddingTop: mobile ? "60px" : "36px" }
  }, import_react34.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: mobile ? "6" : "4",
    style: { maxWidth: mobile ? 320 : 280 }
  }, import_react34.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: mobile ? "32" : "16"
  }, import_react34.default.createElement(AsyncImage, {
    height: 40,
    src: signInIcon,
    width: 40
  }), import_react34.default.createElement(Text, {
    color: "modalText",
    size: mobile ? "20" : "18",
    textAlign: "center",
    weight: "heavy"
  }, i18n2.t("sign_in.label"))), import_react34.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: mobile ? "16" : "12"
  }, import_react34.default.createElement(Text, {
    color: "modalTextSecondary",
    size: mobile ? "16" : "14",
    textAlign: "center"
  }, i18n2.t("sign_in.description")), status === "idle" && state.errorMessage ? import_react34.default.createElement(Text, {
    color: "error",
    size: mobile ? "16" : "14",
    textAlign: "center",
    weight: "bold"
  }, state.errorMessage) : null)), import_react34.default.createElement(Box, {
    alignItems: !mobile ? "center" : void 0,
    display: "flex",
    flexDirection: "column",
    gap: "8",
    width: "full"
  }, import_react34.default.createElement(ActionButton, {
    disabled: !state.nonce || status === "signing" || status === "verifying",
    label: !state.nonce ? i18n2.t("sign_in.message.preparing") : status === "signing" ? i18n2.t("sign_in.signature.waiting") : status === "verifying" ? i18n2.t("sign_in.signature.verifying") : i18n2.t("sign_in.message.send"),
    onClick: signIn,
    size: mobile ? "large" : "medium",
    testId: "auth-message-button"
  }), mobile ? import_react34.default.createElement(ActionButton, {
    label: "Cancel",
    onClick: onClose,
    size: "large",
    type: "secondary"
  }) : import_react34.default.createElement(Box, {
    as: "button",
    borderRadius: "full",
    className: touchableStyles({ active: "shrink", hover: "grow" }),
    display: "block",
    onClick: onClose,
    paddingX: "10",
    paddingY: "5",
    rel: "noreferrer",
    style: { willChange: "transform" },
    target: "_blank",
    transition: "default"
  }, import_react34.default.createElement(Text, {
    color: "closeButton",
    size: mobile ? "16" : "14",
    weight: "bold"
  }, i18n2.t("sign_in.message.cancel"))))));
}
function usePreloadImages() {
  const rainbowKitChains = useRainbowKitChains();
  const walletConnectors = useWalletConnectors();
  const isUnauthenticated = useAuthenticationStatus() === "unauthenticated";
  const preloadImages = (0, import_react31.useCallback)(() => {
    loadImages(...walletConnectors.map((wallet) => wallet.iconUrl), ...rainbowKitChains.map((chain) => chain.iconUrl).filter(isNotNullish));
    if (!isMobile()) {
      preloadAssetsIcon();
      preloadLoginIcon();
    }
    if (isUnauthenticated) {
      loadImages(signInIcon);
    }
  }, [walletConnectors, rainbowKitChains, isUnauthenticated]);
  (0, import_react31.useEffect)(() => {
    preloadImages();
  }, [preloadImages]);
}
var storageKey4 = "WALLETCONNECT_DEEPLINK_CHOICE";
function setWalletConnectDeepLink({
  mobileUri,
  name
}) {
  localStorage.setItem(storageKey4, JSON.stringify({
    href: mobileUri.split("?")[0],
    name
  }));
}
function clearWalletConnectDeepLink() {
  localStorage.removeItem(storageKey4);
}
var ThemeIdContext = (0, import_react23.createContext)(void 0);
var attr = "data-rk";
var createThemeRootProps = (id) => ({ [attr]: id || "" });
var createThemeRootSelector = (id) => {
  if (id && !/^[a-zA-Z0-9_]+$/.test(id)) {
    throw new Error(`Invalid ID: ${id}`);
  }
  return id ? `[${attr}="${id}"]` : `[${attr}]`;
};
var useThemeRootProps = () => {
  const id = (0, import_react23.useContext)(ThemeIdContext);
  return createThemeRootProps(id);
};
var defaultTheme = lightTheme();
function RainbowKitProvider({
  appInfo,
  avatar,
  chains,
  children,
  coolMode = false,
  id,
  initialChain,
  locale,
  modalSize = ModalSizeOptions.WIDE,
  showRecentTransactions = false,
  theme = defaultTheme
}) {
  usePreloadImages();
  useFingerprint();
  useAccount({ onDisconnect: clearWalletConnectDeepLink });
  if (typeof theme === "function") {
    throw new Error('A theme function was provided to the "theme" prop instead of a theme object. You must execute this function to get the resulting theme object.');
  }
  const selector = createThemeRootSelector(id);
  const appContext = {
    ...defaultAppInfo,
    ...appInfo
  };
  const avatarContext = avatar != null ? avatar : defaultAvatar;
  return import_react23.default.createElement(RainbowKitChainProvider, {
    chains,
    initialChain
  }, import_react23.default.createElement(WalletButtonProvider, null, import_react23.default.createElement(I18nProvider, {
    locale
  }, import_react23.default.createElement(CoolModeContext.Provider, {
    value: coolMode
  }, import_react23.default.createElement(ModalSizeProvider, {
    modalSize
  }, import_react23.default.createElement(ShowRecentTransactionsContext.Provider, {
    value: showRecentTransactions
  }, import_react23.default.createElement(TransactionStoreProvider, null, import_react23.default.createElement(AvatarContext.Provider, {
    value: avatarContext
  }, import_react23.default.createElement(AppContext.Provider, {
    value: appContext
  }, import_react23.default.createElement(ThemeIdContext.Provider, {
    value: id
  }, import_react23.default.createElement(ShowBalanceProvider, null, import_react23.default.createElement(ModalProvider, null, theme ? import_react23.default.createElement("div", {
    ...createThemeRootProps(id)
  }, import_react23.default.createElement("style", {
    dangerouslySetInnerHTML: {
      __html: [
        `${selector}{${cssStringFromTheme("lightMode" in theme ? theme.lightMode : theme)}}`,
        "darkMode" in theme ? `@media(prefers-color-scheme:dark){${selector}{${cssStringFromTheme(theme.darkMode, { extends: theme.lightMode })}}}` : null
      ].join("")
    }
  }), children) : children))))))))))));
}
var content = "_9pm4ki5 ju367va ju367v15 ju367v8r";
var overlay = "_9pm4ki3 ju367v9h ju367vb3 ju367va ju367v2q ju367v8q";
var moveFocusWithin = (element2, position) => {
  const focusableElements = element2.querySelectorAll("button:not(:disabled), a[href]");
  if (focusableElements.length === 0)
    return;
  focusableElements[position === "end" ? focusableElements.length - 1 : 0].focus();
};
function FocusTrap(props) {
  const contentRef = (0, import_react39.useRef)(null);
  (0, import_react39.useEffect)(() => {
    const previouslyActiveElement = document.activeElement;
    return () => {
      var _a;
      (_a = previouslyActiveElement.focus) == null ? void 0 : _a.call(previouslyActiveElement);
    };
  }, []);
  (0, import_react39.useEffect)(() => {
    if (contentRef.current) {
      const elementToFocus = contentRef.current.querySelector("[data-auto-focus]");
      if (elementToFocus) {
        elementToFocus.focus();
      } else {
        contentRef.current.focus();
      }
    }
  }, []);
  return import_react39.default.createElement(import_react39.default.Fragment, null, import_react39.default.createElement("div", {
    onFocus: (0, import_react39.useCallback)(() => contentRef.current && moveFocusWithin(contentRef.current, "end"), []),
    tabIndex: 0
  }), import_react39.default.createElement("div", {
    ref: contentRef,
    style: { outline: "none" },
    tabIndex: -1,
    ...props
  }), import_react39.default.createElement("div", {
    onFocus: (0, import_react39.useCallback)(() => contentRef.current && moveFocusWithin(contentRef.current, "start"), []),
    tabIndex: 0
  }));
}
var stopPropagation = (event) => event.stopPropagation();
function Dialog({ children, onClose, open, titleId }) {
  (0, import_react22.useEffect)(() => {
    const handleEscape = (event) => open && event.key === "Escape" && onClose();
    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [open, onClose]);
  const [bodyScrollable, setBodyScrollable] = (0, import_react22.useState)(true);
  (0, import_react22.useEffect)(() => {
    setBodyScrollable(getComputedStyle(window.document.body).overflow !== "hidden");
  }, []);
  const handleBackdropClick = (0, import_react22.useCallback)(() => onClose(), [onClose]);
  const themeRootProps = useThemeRootProps();
  const mobile = isMobile();
  return import_react22.default.createElement(import_react22.default.Fragment, null, open ? (0, import_react_dom.createPortal)(import_react22.default.createElement(Combination_default, {
    enabled: bodyScrollable
  }, import_react22.default.createElement(Box, {
    ...themeRootProps
  }, import_react22.default.createElement(Box, {
    ...themeRootProps,
    alignItems: mobile ? "flex-end" : "center",
    "aria-labelledby": titleId,
    "aria-modal": true,
    className: overlay,
    onClick: handleBackdropClick,
    position: "fixed",
    role: "dialog"
  }, import_react22.default.createElement(FocusTrap, {
    className: content,
    onClick: stopPropagation,
    role: "document"
  }, children)))), document.body) : null);
}
var bottomSheetOverrides = "_1ckjpok7";
var dialogContent = "_1ckjpok1 ju367vb6 ju367vdr ju367vp ju367vt ju367vv ju367vel ju367va ju367v15 ju367v6c ju367v8r";
var dialogContentCompactMode = "_1ckjpok4 _1ckjpok1 ju367vb6 ju367vdr ju367vp ju367vt ju367vv ju367vel ju367va ju367v15 ju367v6c ju367v8r";
var dialogContentMobile = "_1ckjpok6 ju367vq";
var dialogContentWideDesktop = "_1ckjpok3 _1ckjpok1 ju367vb6 ju367vdr ju367vp ju367vt ju367vv ju367vel ju367va ju367v15 ju367v6c ju367v8r";
var dialogContentWideMobile = "_1ckjpok2 _1ckjpok1 ju367vb6 ju367vdr ju367vp ju367vt ju367vv ju367vel ju367va ju367v15 ju367v6c ju367v8r";
function DialogContent({
  bottomSheetOnMobile = false,
  children,
  marginTop,
  padding = "16",
  paddingBottom,
  wide = false
}) {
  const mobile = isMobile();
  const modalSize = (0, import_react40.useContext)(ModalSizeContext);
  const compactModeEnabled = modalSize === ModalSizeOptions.COMPACT;
  return import_react40.default.createElement(Box, {
    marginTop
  }, import_react40.default.createElement(Box, {
    className: [
      wide ? mobile ? dialogContentWideMobile : compactModeEnabled ? dialogContentCompactMode : dialogContentWideDesktop : dialogContent,
      mobile ? dialogContentMobile : null,
      mobile && bottomSheetOnMobile ? bottomSheetOverrides : null
    ].join(" ")
  }, import_react40.default.createElement(Box, {
    padding,
    paddingBottom: paddingBottom != null ? paddingBottom : padding
  }, children)));
}
var units = ["k", "m", "b", "t"];
function toPrecision(number, precision = 1) {
  return number.toString().replace(new RegExp(`(.+\\.\\d{${precision}})\\d+`), "$1").replace(/(\.[1-9]*)0+$/, "$1").replace(/\.$/, "");
}
function abbreviateETHBalance(number) {
  if (number < 1)
    return toPrecision(number, 3);
  if (number < 10 ** 2)
    return toPrecision(number, 2);
  if (number < 10 ** 4)
    return new Intl.NumberFormat().format(parseFloat(toPrecision(number, 1)));
  const decimalsDivisor = 10 ** 1;
  let result = String(number);
  for (let i = units.length - 1; i >= 0; i--) {
    const size = 10 ** ((i + 1) * 3);
    if (size <= number) {
      number = number * decimalsDivisor / size / decimalsDivisor;
      result = toPrecision(number, 1) + units[i];
      break;
    }
  }
  return result;
}
function formatAddress(address) {
  const leadingChars = 4;
  const trailingChars = 4;
  return address.length < leadingChars + trailingChars ? address : `${address.substring(0, leadingChars)}…${address.substring(address.length - trailingChars)}`;
}
function formatENS(name) {
  const parts = name.split(".");
  const last = parts.pop();
  if (parts.join(".").length > 24) {
    return `${parts.join(".").substring(0, 24)}...`;
  }
  return `${parts.join(".")}.${last}`;
}
var CopiedIcon = () => import_react42.default.createElement("svg", {
  fill: "none",
  height: "13",
  viewBox: "0 0 13 13",
  width: "13",
  xmlns: "http://www.w3.org/2000/svg"
}, import_react42.default.createElement("title", null, "Copied"), import_react42.default.createElement("path", {
  d: "M4.94568 12.2646C5.41052 12.2646 5.77283 12.0869 6.01892 11.7109L12.39 1.96973C12.5677 1.69629 12.6429 1.44336 12.6429 1.2041C12.6429 0.561523 12.1644 0.0966797 11.5082 0.0966797C11.057 0.0966797 10.7767 0.260742 10.5033 0.691406L4.9115 9.50977L2.07458 5.98926C1.82166 5.68848 1.54822 5.55176 1.16541 5.55176C0.502319 5.55176 0.0238037 6.02344 0.0238037 6.66602C0.0238037 6.95312 0.112671 7.20605 0.358765 7.48633L3.88611 11.7588C4.18005 12.1074 4.50818 12.2646 4.94568 12.2646Z",
  fill: "currentColor"
}));
var CopyIcon = () => import_react43.default.createElement("svg", {
  fill: "none",
  height: "16",
  viewBox: "0 0 17 16",
  width: "17",
  xmlns: "http://www.w3.org/2000/svg"
}, import_react43.default.createElement("title", null, "Copy"), import_react43.default.createElement("path", {
  d: "M3.04236 12.3027H4.18396V13.3008C4.18396 14.8525 5.03845 15.7002 6.59705 15.7002H13.6244C15.183 15.7002 16.0375 14.8525 16.0375 13.3008V6.24609C16.0375 4.69434 15.183 3.84668 13.6244 3.84668H12.4828V2.8418C12.4828 1.29688 11.6283 0.442383 10.0697 0.442383H3.04236C1.48376 0.442383 0.629272 1.29004 0.629272 2.8418V9.90332C0.629272 11.4551 1.48376 12.3027 3.04236 12.3027ZM3.23376 10.5391C2.68689 10.5391 2.39294 10.2656 2.39294 9.68457V3.06055C2.39294 2.47949 2.68689 2.21289 3.23376 2.21289H9.8783C10.4252 2.21289 10.7191 2.47949 10.7191 3.06055V3.84668H6.59705C5.03845 3.84668 4.18396 4.69434 4.18396 6.24609V10.5391H3.23376ZM6.78845 13.9365C6.24158 13.9365 5.94763 13.6699 5.94763 13.0889V6.45801C5.94763 5.87695 6.24158 5.61035 6.78845 5.61035H13.433C13.9799 5.61035 14.2738 5.87695 14.2738 6.45801V13.0889C14.2738 13.6699 13.9799 13.9365 13.433 13.9365H6.78845Z",
  fill: "currentColor"
}));
var DisconnectIcon = () => import_react44.default.createElement("svg", {
  fill: "none",
  height: "16",
  viewBox: "0 0 18 16",
  width: "18",
  xmlns: "http://www.w3.org/2000/svg"
}, import_react44.default.createElement("title", null, "Disconnect"), import_react44.default.createElement("path", {
  d: "M2.67834 15.5908H9.99963C11.5514 15.5908 12.399 14.7432 12.399 13.1777V10.2656H10.6354V12.9863C10.6354 13.5332 10.3688 13.8271 9.78772 13.8271H2.89026C2.3092 13.8271 2.0426 13.5332 2.0426 12.9863V3.15625C2.0426 2.60254 2.3092 2.30859 2.89026 2.30859H9.78772C10.3688 2.30859 10.6354 2.60254 10.6354 3.15625V5.89746H12.399V2.95801C12.399 1.39941 11.5514 0.544922 9.99963 0.544922H2.67834C1.12659 0.544922 0.278931 1.39941 0.278931 2.95801V13.1777C0.278931 14.7432 1.12659 15.5908 2.67834 15.5908ZM7.43616 8.85059H14.0875L15.0924 8.78906L14.566 9.14453L13.6842 9.96484C13.5406 10.1016 13.4586 10.2861 13.4586 10.4844C13.4586 10.8398 13.7321 11.168 14.1217 11.168C14.3199 11.168 14.4635 11.0928 14.6002 10.9561L16.7809 8.68652C16.986 8.48145 17.0543 8.27637 17.0543 8.06445C17.0543 7.85254 16.986 7.64746 16.7809 7.43555L14.6002 5.17285C14.4635 5.03613 14.3199 4.9541 14.1217 4.9541C13.7321 4.9541 13.4586 5.27539 13.4586 5.6377C13.4586 5.83594 13.5406 6.02734 13.6842 6.15723L14.566 6.98438L15.0924 7.33984L14.0875 7.27148H7.43616C7.01917 7.27148 6.65686 7.62012 6.65686 8.06445C6.65686 8.50195 7.01917 8.85059 7.43616 8.85059Z",
  fill: "currentColor"
}));
function useClearRecentTransactions() {
  const store = useTransactionStore();
  const { address } = useAccount();
  const chainId = useChainId();
  return (0, import_react46.useCallback)(() => {
    if (!address || !chainId) {
      throw new Error("No address or chain ID found");
    }
    store.clearTransactions(address, chainId);
  }, [store, address, chainId]);
}
var chainToExplorerUrl = (chain) => {
  var _a, _b;
  return (_b = (_a = chain == null ? void 0 : chain.blockExplorers) == null ? void 0 : _a.default) == null ? void 0 : _b.url;
};
var ExternalLinkIcon = () => import_react47.default.createElement("svg", {
  fill: "none",
  height: "19",
  viewBox: "0 0 20 19",
  width: "20",
  xmlns: "http://www.w3.org/2000/svg"
}, import_react47.default.createElement("title", null, "Link"), import_react47.default.createElement("path", {
  d: "M10 18.9443C15.0977 18.9443 19.2812 14.752 19.2812 9.6543C19.2812 4.56543 15.0889 0.373047 10 0.373047C4.90234 0.373047 0.71875 4.56543 0.71875 9.6543C0.71875 14.752 4.91113 18.9443 10 18.9443ZM10 16.6328C6.1416 16.6328 3.03906 13.5215 3.03906 9.6543C3.03906 5.7959 6.13281 2.68457 10 2.68457C13.8584 2.68457 16.9697 5.7959 16.9697 9.6543C16.9785 13.5215 13.8672 16.6328 10 16.6328ZM12.7158 12.1416C13.2432 12.1416 13.5684 11.7549 13.5684 11.1836V7.19336C13.5684 6.44629 13.1377 6.05957 12.417 6.05957H8.40918C7.8291 6.05957 7.45117 6.38477 7.45117 6.91211C7.45117 7.43945 7.8291 7.77344 8.40918 7.77344H9.69238L10.7207 7.63281L9.53418 8.67871L6.73047 11.4912C6.53711 11.6758 6.41406 11.9395 6.41406 12.2031C6.41406 12.7832 6.85352 13.1699 7.39844 13.1699C7.68848 13.1699 7.92578 13.0732 8.1543 12.8623L10.9316 10.0762L11.9775 8.89844L11.8545 9.98828V11.1836C11.8545 11.7725 12.1885 12.1416 12.7158 12.1416Z",
  fill: "currentColor"
}));
var CancelIcon = () => import_react49.default.createElement("svg", {
  fill: "none",
  height: "19",
  viewBox: "0 0 20 19",
  width: "20",
  xmlns: "http://www.w3.org/2000/svg"
}, import_react49.default.createElement("title", null, "Cancel"), import_react49.default.createElement("path", {
  d: "M10 18.9443C15.0977 18.9443 19.2812 14.752 19.2812 9.6543C19.2812 4.56543 15.0889 0.373047 10 0.373047C4.90234 0.373047 0.71875 4.56543 0.71875 9.6543C0.71875 14.752 4.91113 18.9443 10 18.9443ZM10 16.6328C6.1416 16.6328 3.03906 13.5215 3.03906 9.6543C3.03906 5.7959 6.13281 2.68457 10 2.68457C13.8584 2.68457 16.9697 5.7959 16.9697 9.6543C16.9785 13.5215 13.8672 16.6328 10 16.6328ZM7.29297 13.3018C7.58301 13.3018 7.81152 13.2139 7.99609 13.0205L10 11.0166L12.0127 13.0205C12.1973 13.2051 12.4258 13.3018 12.707 13.3018C13.2432 13.3018 13.6562 12.8887 13.6562 12.3525C13.6562 12.0977 13.5508 11.8691 13.3662 11.6934L11.3535 9.67188L13.375 7.6416C13.5596 7.44824 13.6562 7.22852 13.6562 6.98242C13.6562 6.44629 13.2432 6.0332 12.7158 6.0332C12.4346 6.0332 12.2148 6.12109 12.0215 6.31445L10 8.32715L7.9873 6.32324C7.80273 6.12988 7.58301 6.04199 7.29297 6.04199C6.76562 6.04199 6.35254 6.45508 6.35254 6.99121C6.35254 7.2373 6.44922 7.46582 6.63379 7.6416L8.65527 9.67188L6.63379 11.6934C6.44922 11.8691 6.35254 12.1064 6.35254 12.3525C6.35254 12.8887 6.76562 13.3018 7.29297 13.3018Z",
  fill: "currentColor"
}));
var SuccessIcon = () => import_react50.default.createElement("svg", {
  fill: "none",
  height: "20",
  viewBox: "0 0 20 20",
  width: "20",
  xmlns: "http://www.w3.org/2000/svg"
}, import_react50.default.createElement("title", null, "Success"), import_react50.default.createElement("path", {
  d: "M10 19.4443C15.0977 19.4443 19.2812 15.252 19.2812 10.1543C19.2812 5.06543 15.0889 0.873047 10 0.873047C4.90234 0.873047 0.71875 5.06543 0.71875 10.1543C0.71875 15.252 4.91113 19.4443 10 19.4443ZM10 17.1328C6.1416 17.1328 3.03906 14.0215 3.03906 10.1543C3.03906 6.2959 6.13281 3.18457 10 3.18457C13.8584 3.18457 16.9697 6.2959 16.9697 10.1543C16.9785 14.0215 13.8672 17.1328 10 17.1328ZM9.07715 14.3379C9.4375 14.3379 9.7627 14.1533 9.97363 13.8369L13.7441 8.00977C13.8848 7.79883 13.9814 7.5791 13.9814 7.36816C13.9814 6.84961 13.5244 6.48926 13.0322 6.48926C12.707 6.48926 12.4258 6.66504 12.2148 7.0166L9.05957 12.0967L7.5918 10.2949C7.37207 10.0225 7.13477 9.9082 6.84473 9.9082C6.33496 9.9082 5.92188 10.3125 5.92188 10.8223C5.92188 11.0684 6.00098 11.2793 6.18555 11.5078L8.1543 13.8545C8.40918 14.1709 8.70801 14.3379 9.07715 14.3379Z",
  fill: "currentColor"
}));
var getTxStatusIcon = (status) => {
  switch (status) {
    case "pending":
      return SpinnerIcon;
    case "confirmed":
      return SuccessIcon;
    case "failed":
      return CancelIcon;
    default:
      return SpinnerIcon;
  }
};
function TxItem({ tx }) {
  const mobile = isMobile();
  const Icon = getTxStatusIcon(tx.status);
  const color = tx.status === "failed" ? "error" : "accentColor";
  const { chain: activeChain } = useNetwork();
  const confirmationStatus = tx.status === "confirmed" ? "Confirmed" : tx.status === "failed" ? "Failed" : "Pending";
  const explorerLink = chainToExplorerUrl(activeChain);
  return import_react48.default.createElement(import_react48.default.Fragment, null, import_react48.default.createElement(Box, {
    ...explorerLink ? {
      as: "a",
      background: { hover: "profileForeground" },
      borderRadius: "menuButton",
      className: touchableStyles({ active: "shrink" }),
      href: `${explorerLink}/tx/${tx.hash}`,
      rel: "noreferrer noopener",
      target: "_blank",
      transition: "default"
    } : {},
    color: "modalText",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    padding: "8",
    width: "full"
  }, import_react48.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
    gap: mobile ? "16" : "14"
  }, import_react48.default.createElement(Box, {
    color
  }, import_react48.default.createElement(Icon, null)), import_react48.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: mobile ? "3" : "1"
  }, import_react48.default.createElement(Box, null, import_react48.default.createElement(Text, {
    color: "modalText",
    font: "body",
    size: mobile ? "16" : "14",
    weight: "bold"
  }, tx == null ? void 0 : tx.description)), import_react48.default.createElement(Box, null, import_react48.default.createElement(Text, {
    color: tx.status === "pending" ? "modalTextSecondary" : color,
    font: "body",
    size: "14",
    weight: mobile ? "medium" : "regular"
  }, confirmationStatus)))), explorerLink && import_react48.default.createElement(Box, {
    alignItems: "center",
    color: "modalTextDim",
    display: "flex"
  }, import_react48.default.createElement(ExternalLinkIcon, null))));
}
var NUMBER_OF_VISIBLE_TXS = 3;
function TxList({ address }) {
  const recentTransactions = useRecentTransactions();
  const clearRecentTransactions = useClearRecentTransactions();
  const { chain: activeChain } = useNetwork();
  const explorerLink = chainToExplorerUrl(activeChain);
  const visibleTxs = recentTransactions.slice(0, NUMBER_OF_VISIBLE_TXS);
  const hasTransactions = visibleTxs.length > 0;
  const mobile = isMobile();
  const { appName } = (0, import_react45.useContext)(AppContext);
  const { i18n: i18n2 } = (0, import_react45.useContext)(I18nContext);
  return import_react45.default.createElement(import_react45.default.Fragment, null, import_react45.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "10",
    paddingBottom: "2",
    paddingTop: "16",
    paddingX: mobile ? "8" : "18"
  }, hasTransactions && import_react45.default.createElement(Box, {
    paddingBottom: mobile ? "4" : "0",
    paddingTop: "8",
    paddingX: mobile ? "12" : "6"
  }, import_react45.default.createElement(Box, {
    display: "flex",
    justifyContent: "space-between"
  }, import_react45.default.createElement(Text, {
    color: "modalTextSecondary",
    size: mobile ? "16" : "14",
    weight: "semibold"
  }, i18n2.t("profile.transactions.recent.title")), import_react45.default.createElement(Box, {
    style: {
      marginBottom: -6,
      marginLeft: -10,
      marginRight: -10,
      marginTop: -6
    }
  }, import_react45.default.createElement(Box, {
    as: "button",
    background: {
      hover: "profileForeground"
    },
    borderRadius: "actionButton",
    className: touchableStyles({ active: "shrink" }),
    onClick: clearRecentTransactions,
    paddingX: mobile ? "8" : "12",
    paddingY: mobile ? "4" : "5",
    transition: "default",
    type: "button"
  }, import_react45.default.createElement(Text, {
    color: "modalTextSecondary",
    size: mobile ? "16" : "14",
    weight: "semibold"
  }, i18n2.t("profile.transactions.clear.label")))))), import_react45.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "4"
  }, hasTransactions ? visibleTxs.map((tx) => import_react45.default.createElement(TxItem, {
    key: tx.hash,
    tx
  })) : import_react45.default.createElement(import_react45.default.Fragment, null, import_react45.default.createElement(Box, {
    padding: mobile ? "12" : "8"
  }, import_react45.default.createElement(Text, {
    color: "modalTextDim",
    size: mobile ? "16" : "14",
    weight: mobile ? "medium" : "bold"
  }, appName ? i18n2.t("profile.transactions.description", {
    appName
  }) : i18n2.t("profile.transactions.description_fallback"))), mobile && import_react45.default.createElement(Box, {
    background: "generalBorderDim",
    height: "1",
    marginX: "12",
    marginY: "8"
  })))), explorerLink && import_react45.default.createElement(Box, {
    paddingBottom: "18",
    paddingX: mobile ? "8" : "18"
  }, import_react45.default.createElement(Box, {
    alignItems: "center",
    as: "a",
    background: { hover: "profileForeground" },
    borderRadius: "menuButton",
    className: touchableStyles({ active: "shrink" }),
    color: "modalTextDim",
    display: "flex",
    flexDirection: "row",
    href: `${explorerLink}/address/${address}`,
    justifyContent: "space-between",
    paddingX: "8",
    paddingY: "12",
    rel: "noreferrer noopener",
    style: { willChange: "transform" },
    target: "_blank",
    transition: "default",
    width: "full",
    ...mobile ? { paddingLeft: "12" } : {}
  }, import_react45.default.createElement(Text, {
    color: "modalText",
    font: "body",
    size: mobile ? "16" : "14",
    weight: mobile ? "semibold" : "bold"
  }, i18n2.t("profile.explorer.label")), import_react45.default.createElement(ExternalLinkIcon, null))));
}
function ProfileDetailsAction({
  action,
  icon,
  label,
  testId,
  url
}) {
  const mobile = isMobile();
  return import_react51.default.createElement(Box, {
    ...url ? { as: "a", href: url, rel: "noreferrer noopener", target: "_blank" } : { as: "button", type: "button" },
    background: {
      base: "profileAction",
      ...!mobile ? { hover: "profileActionHover" } : {}
    },
    borderRadius: "menuButton",
    boxShadow: "profileDetailsAction",
    className: touchableStyles({
      active: "shrinkSm",
      hover: !mobile ? "grow" : void 0
    }),
    display: "flex",
    onClick: action,
    padding: mobile ? "6" : "8",
    style: { willChange: "transform" },
    testId,
    transition: "default",
    width: "full"
  }, import_react51.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "1",
    justifyContent: "center",
    paddingTop: "2",
    width: "full"
  }, import_react51.default.createElement(Box, {
    color: "modalText",
    height: "max"
  }, icon), import_react51.default.createElement(Box, null, import_react51.default.createElement(Text, {
    color: "modalText",
    size: mobile ? "12" : "13",
    weight: "semibold"
  }, label))));
}
function ProfileDetails({
  address,
  ensAvatar,
  ensName,
  onClose,
  onDisconnect
}) {
  const showRecentTransactions = (0, import_react41.useContext)(ShowRecentTransactionsContext);
  const { data: balanceData } = useBalance({
    address
  });
  const [copiedAddress, setCopiedAddress] = (0, import_react41.useState)(false);
  const copyAddressAction = (0, import_react41.useCallback)(() => {
    if (address) {
      navigator.clipboard.writeText(address);
      setCopiedAddress(true);
    }
  }, [address]);
  (0, import_react41.useEffect)(() => {
    if (copiedAddress) {
      const timer = setTimeout(() => {
        setCopiedAddress(false);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [copiedAddress]);
  if (!address) {
    return null;
  }
  const accountName = ensName ? formatENS(ensName) : formatAddress(address);
  const ethBalance = balanceData == null ? void 0 : balanceData.formatted;
  const displayBalance = ethBalance ? abbreviateETHBalance(parseFloat(ethBalance)) : void 0;
  const titleId = "rk_profile_title";
  const mobile = isMobile();
  const { i18n: i18n2 } = (0, import_react41.useContext)(I18nContext);
  return import_react41.default.createElement(import_react41.default.Fragment, null, import_react41.default.createElement(Box, {
    display: "flex",
    flexDirection: "column"
  }, import_react41.default.createElement(Box, {
    background: "profileForeground",
    padding: "16"
  }, import_react41.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: mobile ? "16" : "12",
    justifyContent: "center",
    margin: "8",
    style: { textAlign: "center" }
  }, import_react41.default.createElement(Box, {
    style: {
      position: "absolute",
      right: 16,
      top: 16,
      willChange: "transform"
    }
  }, import_react41.default.createElement(CloseButton, {
    onClose
  })), " ", import_react41.default.createElement(Box, {
    marginTop: mobile ? "24" : "0"
  }, import_react41.default.createElement(Avatar, {
    address,
    imageUrl: ensAvatar,
    size: mobile ? 82 : 74
  })), import_react41.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: mobile ? "4" : "0",
    textAlign: "center"
  }, import_react41.default.createElement(Box, {
    textAlign: "center"
  }, import_react41.default.createElement(Text, {
    as: "h1",
    color: "modalText",
    id: titleId,
    size: mobile ? "20" : "18",
    weight: "heavy"
  }, accountName)), balanceData && import_react41.default.createElement(Box, {
    textAlign: "center"
  }, import_react41.default.createElement(Text, {
    as: "h1",
    color: "modalTextSecondary",
    id: titleId,
    size: mobile ? "16" : "14",
    weight: "semibold"
  }, displayBalance, " ", balanceData.symbol)))), import_react41.default.createElement(Box, {
    display: "flex",
    flexDirection: "row",
    gap: "8",
    margin: "2",
    marginTop: "16"
  }, import_react41.default.createElement(ProfileDetailsAction, {
    action: copyAddressAction,
    icon: copiedAddress ? import_react41.default.createElement(CopiedIcon, null) : import_react41.default.createElement(CopyIcon, null),
    label: copiedAddress ? i18n2.t("profile.copy_address.copied") : i18n2.t("profile.copy_address.label")
  }), import_react41.default.createElement(ProfileDetailsAction, {
    action: onDisconnect,
    icon: import_react41.default.createElement(DisconnectIcon, null),
    label: i18n2.t("profile.disconnect.label"),
    testId: "disconnect-button"
  }))), showRecentTransactions && import_react41.default.createElement(import_react41.default.Fragment, null, import_react41.default.createElement(Box, {
    background: "generalBorder",
    height: "1",
    marginTop: "-1"
  }), import_react41.default.createElement(Box, null, import_react41.default.createElement(TxList, {
    address
  })))));
}
function AccountModal({ onClose, open }) {
  const { address } = useAccount();
  const ensName = useMainnetEnsName(address);
  const ensAvatar = useMainnetEnsAvatar(ensName);
  const { disconnect } = useDisconnect();
  if (!address) {
    return null;
  }
  const titleId = "rk_account_modal_title";
  return import_react21.default.createElement(import_react21.default.Fragment, null, address && import_react21.default.createElement(Dialog, {
    onClose,
    open,
    titleId
  }, import_react21.default.createElement(DialogContent, {
    bottomSheetOnMobile: true,
    padding: "0"
  }, import_react21.default.createElement(ProfileDetails, {
    address,
    ensAvatar,
    ensName,
    onClose,
    onDisconnect: disconnect
  }))));
}
var DisconnectSqIcon = ({ size }) => import_react53.default.createElement("svg", {
  fill: "none",
  height: size,
  viewBox: "0 0 28 28",
  width: size,
  xmlns: "http://www.w3.org/2000/svg"
}, import_react53.default.createElement("title", null, "Disconnect"), import_react53.default.createElement("path", {
  d: "M6.742 22.195h8.367c1.774 0 2.743-.968 2.743-2.758V16.11h-2.016v3.11c0 .625-.305.96-.969.96H6.984c-.664 0-.968-.335-.968-.96V7.984c0-.632.304-.968.968-.968h7.883c.664 0 .969.336.969.968v3.133h2.016v-3.36c0-1.78-.97-2.757-2.743-2.757H6.742C4.97 5 4 5.977 4 7.758v11.68c0 1.789.969 2.757 2.742 2.757Zm5.438-7.703h7.601l1.149-.07-.602.406-1.008.938a.816.816 0 0 0-.258.593c0 .407.313.782.758.782.227 0 .39-.086.547-.243l2.492-2.593c.235-.235.313-.47.313-.711 0-.242-.078-.477-.313-.719l-2.492-2.586c-.156-.156-.32-.25-.547-.25-.445 0-.758.367-.758.781 0 .227.094.446.258.594l1.008.945.602.407-1.149-.079H12.18a.904.904 0 0 0 0 1.805Z",
  fill: "currentColor"
}));
var unsetBackgroundOnHover = "v9horb0";
var MenuButton = import_react54.default.forwardRef(({
  children,
  currentlySelected = false,
  onClick,
  testId,
  ...urlProps
}, ref) => {
  const mobile = isMobile();
  return import_react54.default.createElement(Box, {
    as: "button",
    borderRadius: "menuButton",
    disabled: currentlySelected,
    display: "flex",
    onClick,
    ref,
    testId,
    type: "button"
  }, import_react54.default.createElement(Box, {
    borderRadius: "menuButton",
    className: [
      mobile ? unsetBackgroundOnHover : void 0,
      !currentlySelected && touchableStyles({ active: "shrink" })
    ],
    padding: mobile ? "8" : "6",
    transition: "default",
    width: "full",
    ...currentlySelected ? {
      background: "accentColor",
      borderColor: "selectedOptionBorder",
      borderStyle: "solid",
      borderWidth: "1",
      boxShadow: "selectedOption",
      color: "accentColorForeground"
    } : {
      background: { hover: "menuItemBackground" },
      color: "modalText",
      transition: "default"
    },
    ...urlProps
  }, children));
});
MenuButton.displayName = "MenuButton";
var DesktopScrollClassName = "_18dqw9x0";
var MobileScrollClassName = "_18dqw9x1";
function ChainModal({ onClose, open }) {
  var _a;
  const { chain: activeChain } = useNetwork();
  const { chains, pendingChainId, reset, switchNetwork } = useSwitchNetwork({
    onSettled: () => {
      reset();
      onClose();
    }
  });
  const { i18n: i18n2 } = (0, import_react52.useContext)(I18nContext);
  const { disconnect } = useDisconnect();
  const titleId = "rk_chain_modal_title";
  const mobile = isMobile();
  const unsupportedChain = (_a = activeChain == null ? void 0 : activeChain.unsupported) != null ? _a : false;
  const chainIconSize = mobile ? "36" : "28";
  const { appName } = (0, import_react52.useContext)(AppContext);
  const rainbowkitChains = useRainbowKitChains();
  if (!activeChain || !(activeChain == null ? void 0 : activeChain.id)) {
    return null;
  }
  return import_react52.default.createElement(Dialog, {
    onClose,
    open,
    titleId
  }, import_react52.default.createElement(DialogContent, {
    bottomSheetOnMobile: true,
    paddingBottom: "0"
  }, import_react52.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "14"
  }, import_react52.default.createElement(Box, {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between"
  }, mobile && import_react52.default.createElement(Box, {
    width: "30"
  }), import_react52.default.createElement(Box, {
    paddingBottom: "0",
    paddingLeft: "8",
    paddingTop: "4"
  }, import_react52.default.createElement(Text, {
    as: "h1",
    color: "modalText",
    id: titleId,
    size: mobile ? "20" : "18",
    weight: "heavy"
  }, i18n2.t("chains.title"))), import_react52.default.createElement(CloseButton, {
    onClose
  })), unsupportedChain && import_react52.default.createElement(Box, {
    marginX: "8",
    textAlign: mobile ? "center" : "left"
  }, import_react52.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    weight: "medium"
  }, i18n2.t("chains.wrong_network"))), import_react52.default.createElement(Box, {
    className: mobile ? MobileScrollClassName : DesktopScrollClassName,
    display: "flex",
    flexDirection: "column",
    gap: "4",
    padding: "2",
    paddingBottom: "16"
  }, switchNetwork ? rainbowkitChains.map(({ iconBackground, iconUrl, id, name }, idx) => {
    const chain = chains.find((c) => c.id === id);
    if (!chain)
      return null;
    const isCurrentChain = chain.id === (activeChain == null ? void 0 : activeChain.id);
    const switching = !isCurrentChain && chain.id === pendingChainId;
    return import_react52.default.createElement(import_react52.Fragment, {
      key: chain.id
    }, import_react52.default.createElement(MenuButton, {
      currentlySelected: isCurrentChain,
      onClick: isCurrentChain ? void 0 : () => switchNetwork(chain.id),
      testId: `chain-option-${chain.id}`
    }, import_react52.default.createElement(Box, {
      fontFamily: "body",
      fontSize: "16",
      fontWeight: "bold"
    }, import_react52.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between"
    }, import_react52.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      gap: "4",
      height: chainIconSize
    }, iconUrl && import_react52.default.createElement(Box, {
      height: "full",
      marginRight: "8"
    }, import_react52.default.createElement(AsyncImage, {
      alt: name != null ? name : chain.name,
      background: iconBackground,
      borderRadius: "full",
      height: chainIconSize,
      src: iconUrl,
      width: chainIconSize,
      testId: `chain-option-${chain.id}-icon`
    })), import_react52.default.createElement("div", null, name != null ? name : chain.name)), isCurrentChain && import_react52.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      marginRight: "6"
    }, import_react52.default.createElement(Text, {
      color: "accentColorForeground",
      size: "14",
      weight: "medium"
    }, i18n2.t("chains.connected")), import_react52.default.createElement(Box, {
      background: "connectionIndicator",
      borderColor: "selectedOptionBorder",
      borderRadius: "full",
      borderStyle: "solid",
      borderWidth: "1",
      height: "8",
      marginLeft: "8",
      width: "8"
    })), switching && import_react52.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      marginRight: "6"
    }, import_react52.default.createElement(Text, {
      color: "modalText",
      size: "14",
      weight: "medium"
    }, i18n2.t("chains.confirm")), import_react52.default.createElement(Box, {
      background: "standby",
      borderRadius: "full",
      height: "8",
      marginLeft: "8",
      width: "8"
    }))))), mobile && idx < rainbowkitChains.length - 1 && import_react52.default.createElement(Box, {
      background: "generalBorderDim",
      height: "1",
      marginX: "8"
    }));
  }) : import_react52.default.createElement(Box, {
    background: "generalBorder",
    borderRadius: "menuButton",
    paddingX: "18",
    paddingY: "12"
  }, import_react52.default.createElement(Text, {
    color: "modalText",
    size: "14",
    weight: "medium"
  }, appName ? i18n2.t("chains.switching_not_supported", {
    appName
  }) : i18n2.t("chains.switching_not_supported_fallback"))), unsupportedChain && import_react52.default.createElement(import_react52.default.Fragment, null, import_react52.default.createElement(Box, {
    background: "generalBorderDim",
    height: "1",
    marginX: "8"
  }), import_react52.default.createElement(MenuButton, {
    onClick: () => disconnect(),
    testId: "chain-option-disconnect"
  }, import_react52.default.createElement(Box, {
    color: "error",
    fontFamily: "body",
    fontSize: "16",
    fontWeight: "bold"
  }, import_react52.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between"
  }, import_react52.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
    gap: "4",
    height: chainIconSize
  }, import_react52.default.createElement(Box, {
    alignItems: "center",
    color: "error",
    height: chainIconSize,
    justifyContent: "center",
    marginRight: "8"
  }, import_react52.default.createElement(DisconnectSqIcon, {
    size: Number(chainIconSize)
  })), import_react52.default.createElement("div", null, i18n2.t("chains.disconnect")))))))))));
}
function groupBy(items, getKey) {
  const groupedItems = {};
  for (const item of items) {
    const key = getKey(item);
    if (!key) {
      continue;
    }
    if (!groupedItems[key]) {
      groupedItems[key] = [];
    }
    groupedItems[key].push(item);
  }
  return groupedItems;
}
var storageKey5 = "rk-latest-id";
function getLatestWalletId() {
  return typeof localStorage !== "undefined" ? localStorage.getItem(storageKey5) || "" : "";
}
function addLatestWalletId(walletId) {
  localStorage.setItem(storageKey5, walletId);
}
function clearLatestWalletId() {
  localStorage.removeItem(storageKey5);
}
var DisclaimerLink = ({
  children,
  href
}) => {
  return import_react59.default.createElement(Box, {
    as: "a",
    color: "accentColor",
    href,
    rel: "noreferrer",
    target: "_blank"
  }, children);
};
var DisclaimerText = ({ children }) => {
  return import_react60.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "12",
    weight: "medium"
  }, children);
};
function ConnectModalIntro({
  compactModeEnabled = false,
  getWallet
}) {
  const { disclaimer: Disclaimer, learnMoreUrl } = (0, import_react58.useContext)(AppContext);
  const { i18n: i18n2 } = (0, import_react58.useContext)(I18nContext);
  return import_react58.default.createElement(import_react58.default.Fragment, null, import_react58.default.createElement(Box, {
    alignItems: "center",
    color: "accentColor",
    display: "flex",
    flexDirection: "column",
    height: "full",
    justifyContent: "space-around"
  }, import_react58.default.createElement(Box, {
    marginBottom: "10"
  }, !compactModeEnabled && import_react58.default.createElement(Text, {
    color: "modalText",
    size: "18",
    weight: "heavy"
  }, i18n2.t("intro.title"))), import_react58.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "32",
    justifyContent: "center",
    marginY: "20",
    style: { maxWidth: 312 }
  }, import_react58.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
    gap: "16"
  }, import_react58.default.createElement(Box, {
    borderRadius: "6",
    height: "48",
    minWidth: "48",
    width: "48"
  }, import_react58.default.createElement(AssetsIcon, null)), import_react58.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "4"
  }, import_react58.default.createElement(Text, {
    color: "modalText",
    size: "14",
    weight: "bold"
  }, i18n2.t("intro.digital_asset.title")), import_react58.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    weight: "medium"
  }, i18n2.t("intro.digital_asset.description")))), import_react58.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
    gap: "16"
  }, import_react58.default.createElement(Box, {
    borderRadius: "6",
    height: "48",
    minWidth: "48",
    width: "48"
  }, import_react58.default.createElement(LoginIcon, null)), import_react58.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "4"
  }, import_react58.default.createElement(Text, {
    color: "modalText",
    size: "14",
    weight: "bold"
  }, i18n2.t("intro.login.title")), import_react58.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    weight: "medium"
  }, i18n2.t("intro.login.description"))))), import_react58.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "12",
    justifyContent: "center",
    margin: "10"
  }, import_react58.default.createElement(ActionButton, {
    label: i18n2.t("intro.get.label"),
    onClick: getWallet
  }), import_react58.default.createElement(Box, {
    as: "a",
    className: touchableStyles({ active: "shrink", hover: "grow" }),
    display: "block",
    href: learnMoreUrl,
    paddingX: "12",
    paddingY: "4",
    rel: "noreferrer",
    style: { willChange: "transform" },
    target: "_blank",
    transition: "default"
  }, import_react58.default.createElement(Text, {
    color: "accentColor",
    size: "14",
    weight: "bold"
  }, i18n2.t("intro.learn_more.label")))), Disclaimer && !compactModeEnabled && import_react58.default.createElement(Box, {
    marginBottom: "8",
    marginTop: "12",
    textAlign: "center"
  }, import_react58.default.createElement(Disclaimer, {
    Link: DisclaimerLink,
    Text: DisclaimerText
  }))));
}
var BackIcon = () => import_react61.default.createElement("svg", {
  fill: "none",
  height: "17",
  viewBox: "0 0 11 17",
  width: "11",
  xmlns: "http://www.w3.org/2000/svg"
}, import_react61.default.createElement("title", null, "Back"), import_react61.default.createElement("path", {
  d: "M0.99707 8.6543C0.99707 9.08496 1.15527 9.44531 1.51562 9.79688L8.16016 16.3096C8.43262 16.5732 8.74902 16.7051 9.13574 16.7051C9.90918 16.7051 10.5508 16.0811 10.5508 15.3076C10.5508 14.9121 10.3838 14.5605 10.0938 14.2705L4.30176 8.64551L10.0938 3.0293C10.3838 2.74805 10.5508 2.3877 10.5508 2.00098C10.5508 1.23633 9.90918 0.603516 9.13574 0.603516C8.74902 0.603516 8.43262 0.735352 8.16016 0.999023L1.51562 7.51172C1.15527 7.85449 1.00586 8.21484 0.99707 8.6543Z",
  fill: "currentColor"
}));
var InfoIcon = () => import_react63.default.createElement("svg", {
  fill: "none",
  height: "12",
  viewBox: "0 0 8 12",
  width: "8",
  xmlns: "http://www.w3.org/2000/svg"
}, import_react63.default.createElement("title", null, "Info"), import_react63.default.createElement("path", {
  d: "M3.64258 7.99609C4.19336 7.99609 4.5625 7.73828 4.68555 7.24609C4.69141 7.21094 4.70312 7.16406 4.70898 7.13477C4.80859 6.60742 5.05469 6.35547 6.04492 5.76367C7.14648 5.10156 7.67969 4.3457 7.67969 3.24414C7.67969 1.39844 6.17383 0.255859 3.95898 0.255859C2.32422 0.255859 1.05859 0.894531 0.548828 1.86719C0.396484 2.14844 0.320312 2.44727 0.320312 2.74023C0.314453 3.37305 0.742188 3.79492 1.42188 3.79492C1.91406 3.79492 2.33594 3.54883 2.53516 3.11523C2.78711 2.47656 3.23242 2.21289 3.83594 2.21289C4.55664 2.21289 5.10742 2.65234 5.10742 3.29102C5.10742 3.9707 4.7793 4.29883 3.81836 4.87891C3.02148 5.36523 2.50586 5.92773 2.50586 6.76562V6.90039C2.50586 7.55664 2.96289 7.99609 3.64258 7.99609ZM3.67188 11.4473C4.42773 11.4473 5.04297 10.8672 5.04297 10.1406C5.04297 9.41406 4.42773 8.83984 3.67188 8.83984C2.91602 8.83984 2.30664 9.41406 2.30664 10.1406C2.30664 10.8672 2.91602 11.4473 3.67188 11.4473Z",
  fill: "currentColor"
}));
var InfoButton = ({
  "aria-label": ariaLabel = "Info",
  onClick
}) => {
  const mobile = isMobile();
  return import_react62.default.createElement(Box, {
    alignItems: "center",
    "aria-label": ariaLabel,
    as: "button",
    background: "closeButtonBackground",
    borderColor: "actionButtonBorder",
    borderRadius: "full",
    borderStyle: "solid",
    borderWidth: mobile ? "0" : "1",
    className: touchableStyles({ active: "shrinkSm", hover: "growLg" }),
    color: "closeButton",
    display: "flex",
    height: mobile ? "30" : "28",
    justifyContent: "center",
    onClick,
    style: { willChange: "transform" },
    transition: "default",
    type: "button",
    width: mobile ? "30" : "28"
  }, import_react62.default.createElement(InfoIcon, null));
};
var useCoolMode = (imageUrl) => {
  const ref = (0, import_react65.useRef)(null);
  const coolModeEnabled = (0, import_react65.useContext)(CoolModeContext);
  const resolvedImageUrl = useAsyncImage(imageUrl);
  (0, import_react65.useEffect)(() => {
    if (coolModeEnabled && ref.current && resolvedImageUrl) {
      return makeElementCool(ref.current, resolvedImageUrl);
    }
  }, [coolModeEnabled, resolvedImageUrl]);
  return ref;
};
var getContainer = () => {
  const id = "_rk_coolMode";
  const existingContainer = document.getElementById(id);
  if (existingContainer) {
    return existingContainer;
  }
  const container = document.createElement("div");
  container.setAttribute("id", id);
  container.setAttribute("style", [
    "overflow:hidden",
    "position:fixed",
    "height:100%",
    "top:0",
    "left:0",
    "right:0",
    "bottom:0",
    "pointer-events:none",
    "z-index:2147483647"
  ].join(";"));
  document.body.appendChild(container);
  return container;
};
var instanceCounter = 0;
function makeElementCool(element2, imageUrl) {
  instanceCounter++;
  const sizes = [15, 20, 25, 35, 45];
  const limit = 35;
  let particles = [];
  let autoAddParticle = false;
  let mouseX = 0;
  let mouseY = 0;
  const container = getContainer();
  function createParticle() {
    const size = sizes[Math.floor(Math.random() * sizes.length)];
    const speedHorz = Math.random() * 10;
    const speedUp = Math.random() * 25;
    const spinVal = Math.random() * 360;
    const spinSpeed = Math.random() * 35 * (Math.random() <= 0.5 ? -1 : 1);
    const top = mouseY - size / 2;
    const left = mouseX - size / 2;
    const direction = Math.random() <= 0.5 ? -1 : 1;
    const particle = document.createElement("div");
    particle.innerHTML = `<img src="${imageUrl}" width="${size}" height="${size}" style="border-radius: 25%">`;
    particle.setAttribute("style", [
      "position:absolute",
      "will-change:transform",
      `top:${top}px`,
      `left:${left}px`,
      `transform:rotate(${spinVal}deg)`
    ].join(";"));
    container.appendChild(particle);
    particles.push({
      direction,
      element: particle,
      left,
      size,
      speedHorz,
      speedUp,
      spinSpeed,
      spinVal,
      top
    });
  }
  function updateParticles() {
    for (const p of particles) {
      p.left = p.left - p.speedHorz * p.direction;
      p.top = p.top - p.speedUp;
      p.speedUp = Math.min(p.size, p.speedUp - 1);
      p.spinVal = p.spinVal + p.spinSpeed;
      if (p.top >= Math.max(window.innerHeight, document.body.clientHeight) + p.size) {
        particles = particles.filter((o) => o !== p);
        p.element.remove();
      }
      p.element.setAttribute("style", [
        "position:absolute",
        "will-change:transform",
        `top:${p.top}px`,
        `left:${p.left}px`,
        `transform:rotate(${p.spinVal}deg)`
      ].join(";"));
    }
  }
  let animationFrame;
  function loop() {
    if (autoAddParticle && particles.length < limit) {
      createParticle();
    }
    updateParticles();
    animationFrame = requestAnimationFrame(loop);
  }
  loop();
  const isTouchInteraction = "ontouchstart" in window || navigator.msMaxTouchPoints;
  const tap = isTouchInteraction ? "touchstart" : "mousedown";
  const tapEnd = isTouchInteraction ? "touchend" : "mouseup";
  const move = isTouchInteraction ? "touchmove" : "mousemove";
  const updateMousePosition = (e) => {
    var _a, _b;
    if ("touches" in e) {
      mouseX = (_a = e.touches) == null ? void 0 : _a[0].clientX;
      mouseY = (_b = e.touches) == null ? void 0 : _b[0].clientY;
    } else {
      mouseX = e.clientX;
      mouseY = e.clientY;
    }
  };
  const tapHandler = (e) => {
    updateMousePosition(e);
    autoAddParticle = true;
  };
  const disableAutoAddParticle = () => {
    autoAddParticle = false;
  };
  element2.addEventListener(move, updateMousePosition, { passive: false });
  element2.addEventListener(tap, tapHandler);
  element2.addEventListener(tapEnd, disableAutoAddParticle);
  element2.addEventListener("mouseleave", disableAutoAddParticle);
  return () => {
    element2.removeEventListener(move, updateMousePosition);
    element2.removeEventListener(tap, tapHandler);
    element2.removeEventListener(tapEnd, disableAutoAddParticle);
    element2.removeEventListener("mouseleave", disableAutoAddParticle);
    const interval = setInterval(() => {
      if (animationFrame && particles.length === 0) {
        cancelAnimationFrame(animationFrame);
        clearInterval(interval);
        if (--instanceCounter === 0) {
          container.remove();
        }
      }
    }, 500);
  };
}
var transparentBorder = "g5kl0l0";
var ModalSelection = ({
  as = "button",
  currentlySelected = false,
  iconBackground,
  iconUrl,
  name,
  onClick,
  ready,
  recent,
  testId,
  ...urlProps
}) => {
  const coolModeRef = useCoolMode(iconUrl);
  const [isMouseOver, setIsMouseOver] = (0, import_react64.useState)(false);
  const { i18n: i18n2 } = (0, import_react64.useContext)(I18nContext);
  return import_react64.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    onMouseEnter: () => setIsMouseOver(true),
    onMouseLeave: () => setIsMouseOver(false),
    ref: coolModeRef
  }, import_react64.default.createElement(Box, {
    as,
    borderRadius: "menuButton",
    borderStyle: "solid",
    borderWidth: "1",
    className: !currentlySelected ? [
      transparentBorder,
      touchableStyles({
        active: "shrink"
      })
    ] : void 0,
    disabled: currentlySelected,
    onClick,
    padding: "5",
    style: { willChange: "transform" },
    testId,
    transition: "default",
    width: "full",
    ...currentlySelected ? {
      background: "accentColor",
      borderColor: "selectedOptionBorder",
      boxShadow: "selectedWallet"
    } : {
      background: { hover: "menuItemBackground" }
    },
    ...urlProps
  }, import_react64.default.createElement(Box, {
    color: currentlySelected ? "accentColorForeground" : "modalText",
    disabled: !ready,
    fontFamily: "body",
    fontSize: "16",
    fontWeight: "bold",
    transition: "default"
  }, import_react64.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
    gap: "12"
  }, import_react64.default.createElement(AsyncImage, {
    background: iconBackground,
    ...isMouseOver ? {} : { borderColor: "actionButtonBorder" },
    borderRadius: "6",
    height: "28",
    src: iconUrl,
    width: "28"
  }), import_react64.default.createElement(Box, null, import_react64.default.createElement(Box, {
    style: { marginTop: recent ? -2 : void 0 }
  }, name), recent && import_react64.default.createElement(Text, {
    color: currentlySelected ? "accentColorForeground" : "accentColor",
    size: "12",
    style: { lineHeight: 1, marginTop: -1 },
    weight: "medium"
  }, i18n2.t("connect.recent")))))));
};
ModalSelection.displayName = "ModalSelection";
var convertHexToRGBA = (hexCode, opacity = 1) => {
  let hex = hexCode.replace("#", "");
  if (hex.length === 3) {
    hex = `${hex[0]}${hex[0]}${hex[1]}${hex[1]}${hex[2]}${hex[2]}`;
  }
  const r2 = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  if (opacity > 1 && opacity <= 100) {
    opacity = opacity / 100;
  }
  return `rgba(${r2},${g},${b},${opacity})`;
};
var getGradientRGBAs = (hexColor) => {
  if (!hexColor)
    return null;
  return [
    convertHexToRGBA(hexColor, 0.2),
    convertHexToRGBA(hexColor, 0.14),
    convertHexToRGBA(hexColor, 0.1)
  ];
};
var isHexString = (color) => {
  return /^#([0-9a-f]{3}){1,2}$/i.test(color);
};
var src3 = async () => (await import("./connect-XNDTNVUH-QAJI4ZIH.js")).default;
var preloadConnectIcon = () => loadImages(src3);
var ConnectIcon = () => import_react67.default.createElement(AsyncImage, {
  background: "#515a70",
  borderColor: "generalBorder",
  borderRadius: "10",
  height: "48",
  src: src3,
  width: "48"
});
var src4 = async () => (await import("./create-PAJXJDV3-2CF7KOG3.js")).default;
var preloadCreateIcon = () => loadImages(src4);
var CreateIcon = () => import_react68.default.createElement(AsyncImage, {
  background: "#e3a5e8",
  borderColor: "generalBorder",
  borderRadius: "10",
  height: "48",
  src: src4,
  width: "48"
});
var src5 = async () => (await import("./refresh-5KGGHTJP-Y4QZO536.js")).default;
var preloadRefreshIcon = () => loadImages(src5);
var RefreshIcon = () => import_react69.default.createElement(AsyncImage, {
  background: "#515a70",
  borderColor: "generalBorder",
  borderRadius: "10",
  height: "48",
  src: src5,
  width: "48"
});
var src6 = async () => (await import("./scan-HZBLXLM4-2IQZGBF2.js")).default;
var preloadScanIcon = () => loadImages(src6);
var ScanIcon = () => import_react70.default.createElement(AsyncImage, {
  background: "#515a70",
  borderColor: "generalBorder",
  borderRadius: "10",
  height: "48",
  src: src6,
  width: "48"
});
var QRCodeBackgroundClassName = "_1vwt0cg0";
var ScrollClassName = "_1vwt0cg2 ju367v7a ju367v7v";
var sidebar = "_1vwt0cg3";
var sidebarCompactMode = "_1vwt0cg4";
var generateMatrix = (value, errorCorrectionLevel) => {
  const arr = Array.prototype.slice.call(import_qrcode.default.create(value, { errorCorrectionLevel }).modules.data, 0);
  const sqrt = Math.sqrt(arr.length);
  return arr.reduce((rows, key, index) => (index % sqrt === 0 ? rows.push([key]) : rows[rows.length - 1].push(key)) && rows, []);
};
function QRCode({
  ecl = "M",
  logoBackground,
  logoMargin = 10,
  logoSize = 50,
  logoUrl,
  size: sizeProp = 200,
  uri
}) {
  const padding = "20";
  const size = sizeProp - parseInt(padding, 10) * 2;
  const dots = (0, import_react71.useMemo)(() => {
    const dots2 = [];
    const matrix = generateMatrix(uri, ecl);
    const cellSize = size / matrix.length;
    const qrList = [
      { x: 0, y: 0 },
      { x: 1, y: 0 },
      { x: 0, y: 1 }
    ];
    qrList.forEach(({ x, y }) => {
      const x1 = (matrix.length - 7) * cellSize * x;
      const y1 = (matrix.length - 7) * cellSize * y;
      for (let i = 0; i < 3; i++) {
        dots2.push(import_react71.default.createElement("rect", {
          fill: i % 2 !== 0 ? "white" : "black",
          height: cellSize * (7 - i * 2),
          key: `${i}-${x}-${y}`,
          rx: (i - 2) * -5 + (i === 0 ? 2 : 0),
          ry: (i - 2) * -5 + (i === 0 ? 2 : 0),
          width: cellSize * (7 - i * 2),
          x: x1 + cellSize * i,
          y: y1 + cellSize * i
        }));
      }
    });
    const clearArenaSize = Math.floor((logoSize + 25) / cellSize);
    const matrixMiddleStart = matrix.length / 2 - clearArenaSize / 2;
    const matrixMiddleEnd = matrix.length / 2 + clearArenaSize / 2 - 1;
    matrix.forEach((row, i) => {
      row.forEach((_, j) => {
        if (matrix[i][j]) {
          if (!(i < 7 && j < 7 || i > matrix.length - 8 && j < 7 || i < 7 && j > matrix.length - 8)) {
            if (!(i > matrixMiddleStart && i < matrixMiddleEnd && j > matrixMiddleStart && j < matrixMiddleEnd)) {
              dots2.push(import_react71.default.createElement("circle", {
                cx: i * cellSize + cellSize / 2,
                cy: j * cellSize + cellSize / 2,
                fill: "black",
                key: `circle-${i}-${j}`,
                r: cellSize / 3
              }));
            }
          }
        }
      });
    });
    return dots2;
  }, [ecl, logoSize, size, uri]);
  const logoPosition = size / 2 - logoSize / 2;
  const logoWrapperSize = logoSize + logoMargin * 2;
  return import_react71.default.createElement(Box, {
    borderColor: "generalBorder",
    borderRadius: "menuButton",
    borderStyle: "solid",
    borderWidth: "1",
    className: QRCodeBackgroundClassName,
    padding,
    width: "max"
  }, import_react71.default.createElement(Box, {
    style: {
      height: size,
      userSelect: "none",
      width: size
    },
    userSelect: "none"
  }, import_react71.default.createElement(Box, {
    display: "flex",
    justifyContent: "center",
    position: "relative",
    style: {
      height: 0,
      top: logoPosition,
      width: size
    },
    width: "full"
  }, import_react71.default.createElement(AsyncImage, {
    background: logoBackground,
    borderColor: { custom: "rgba(0, 0, 0, 0.06)" },
    borderRadius: "13",
    height: logoSize,
    src: logoUrl,
    width: logoSize
  })), import_react71.default.createElement("svg", {
    height: size,
    style: { all: "revert" },
    width: size
  }, import_react71.default.createElement("title", null, "QR Code"), import_react71.default.createElement("defs", null, import_react71.default.createElement("clipPath", {
    id: "clip-wrapper"
  }, import_react71.default.createElement("rect", {
    height: logoWrapperSize,
    width: logoWrapperSize
  })), import_react71.default.createElement("clipPath", {
    id: "clip-logo"
  }, import_react71.default.createElement("rect", {
    height: logoSize,
    width: logoSize
  }))), import_react71.default.createElement("rect", {
    fill: "transparent",
    height: size,
    width: size
  }), dots)));
}
var getBrowserSrc = async () => {
  const browser = getBrowser();
  switch (browser) {
    case "Arc":
      return (await import("./Arc-QDJFTGH2-2ZHYPCCL.js")).default;
    case "Brave":
      return (await import("./Brave-YATE5BIM-TYKTJQQU.js")).default;
    case "Chrome":
      return (await import("./Chrome-LGF33C3S-UUEWHMUF.js")).default;
    case "Edge":
      return (await import("./Edge-K2JEGI5S-7UIQ5C3R.js")).default;
    case "Firefox":
      return (await import("./Firefox-NP5SYEK5-KA5CB4LH.js")).default;
    case "Opera":
      return (await import("./Opera-KV54PXPA-LWURRUWT.js")).default;
    case "Safari":
      return (await import("./Safari-2QIYKJ4P-KJVJ72DK.js")).default;
    default:
      return (await import("./Browser-HN7O5MN7-G3T5NHIE.js")).default;
  }
};
var preloadBrowserIcon = () => loadImages(getBrowserSrc);
var getPlatformSrc = async () => {
  const platform = getPlatform();
  switch (platform) {
    case "Windows":
      return (await import("./Windows-R3CKAIUV-ZC6AQRHL.js")).default;
    case "macOS":
      return (await import("./Macos-2KTZ2XLP-3GQE6SJX.js")).default;
    case "Linux":
      return (await import("./Linux-NS2LQPT4-X2BVTHN7.js")).default;
    default:
      return (await import("./Linux-NS2LQPT4-X2BVTHN7.js")).default;
  }
};
var preloadPlatformIcon = () => loadImages(getPlatformSrc);
function GetDetail({
  getWalletDownload,
  compactModeEnabled
}) {
  const wallets = useWalletConnectors();
  const shownWallets = wallets.splice(0, 5);
  const { i18n: i18n2 } = (0, import_react66.useContext)(I18nContext);
  return import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    height: "full",
    marginTop: "18",
    width: "full"
  }, import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "28",
    height: "full",
    width: "full"
  }, shownWallets == null ? void 0 : shownWallets.filter((wallet) => {
    var _a;
    return wallet.extensionDownloadUrl || wallet.desktopDownloadUrl || wallet.qrCode && ((_a = wallet.downloadUrls) == null ? void 0 : _a.qrCode);
  }).map((wallet) => {
    const { downloadUrls, iconBackground, iconUrl, id, name, qrCode } = wallet;
    const hasMobileCompanionApp = (downloadUrls == null ? void 0 : downloadUrls.qrCode) && qrCode;
    const hasExtension = !!wallet.extensionDownloadUrl;
    const hasMobileAndExtension = (downloadUrls == null ? void 0 : downloadUrls.qrCode) && hasExtension;
    const hasMobileAndDesktop = (downloadUrls == null ? void 0 : downloadUrls.qrCode) && !!wallet.desktopDownloadUrl;
    return import_react66.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      gap: "16",
      justifyContent: "space-between",
      key: wallet.id,
      width: "full"
    }, import_react66.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      gap: "16"
    }, import_react66.default.createElement(AsyncImage, {
      background: iconBackground,
      borderColor: "actionButtonBorder",
      borderRadius: "10",
      height: "48",
      src: iconUrl,
      width: "48"
    }), import_react66.default.createElement(Box, {
      display: "flex",
      flexDirection: "column",
      gap: "2"
    }, import_react66.default.createElement(Text, {
      color: "modalText",
      size: "14",
      weight: "bold"
    }, name), import_react66.default.createElement(Text, {
      color: "modalTextSecondary",
      size: "14",
      weight: "medium"
    }, hasMobileAndExtension ? i18n2.t("get.mobile_and_extension.description") : hasMobileAndDesktop ? i18n2.t("get.mobile_and_desktop.description") : hasMobileCompanionApp ? i18n2.t("get.mobile.description") : hasExtension ? i18n2.t("get.extension.description") : null))), import_react66.default.createElement(Box, {
      display: "flex",
      flexDirection: "column",
      gap: "4"
    }, import_react66.default.createElement(ActionButton, {
      label: i18n2.t("get.action.label"),
      onClick: () => getWalletDownload(id),
      type: "secondary"
    })));
  })), import_react66.default.createElement(Box, {
    alignItems: "center",
    borderRadius: "10",
    display: "flex",
    flexDirection: "column",
    gap: "8",
    justifyContent: "space-between",
    marginBottom: "4",
    paddingY: "8",
    style: { maxWidth: 275, textAlign: "center" }
  }, import_react66.default.createElement(Text, {
    color: "modalText",
    size: "14",
    weight: "bold"
  }, i18n2.t("get.looking_for.title")), import_react66.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    weight: "medium"
  }, compactModeEnabled ? i18n2.t("get.looking_for.desktop.compact_description") : i18n2.t("get.looking_for.desktop.wide_description"))));
}
var LOGO_SIZE = "44";
function ConnectDetail({
  changeWalletStep,
  compactModeEnabled,
  connectionError,
  onClose,
  qrCodeUri,
  reconnect,
  wallet
}) {
  var _a;
  const {
    downloadUrls,
    iconBackground,
    iconUrl,
    name,
    qrCode,
    ready,
    showWalletConnectModal
  } = wallet;
  const getDesktopDeepLink = (_a = wallet.desktop) == null ? void 0 : _a.getUri;
  const safari = isSafari();
  const { i18n: i18n2 } = (0, import_react66.useContext)(I18nContext);
  const hasExtension = !!wallet.extensionDownloadUrl;
  const hasQrCodeAndExtension = (downloadUrls == null ? void 0 : downloadUrls.qrCode) && hasExtension;
  const hasQrCodeAndDesktop = (downloadUrls == null ? void 0 : downloadUrls.qrCode) && !!wallet.desktopDownloadUrl;
  const hasQrCode = qrCode && qrCodeUri;
  const secondaryAction = showWalletConnectModal ? {
    description: !compactModeEnabled ? i18n2.t("connect.walletconnect.description.full") : i18n2.t("connect.walletconnect.description.compact"),
    label: i18n2.t("connect.walletconnect.open.label"),
    onClick: () => {
      onClose();
      showWalletConnectModal();
    }
  } : hasQrCode ? {
    description: i18n2.t("connect.secondary_action.get.description", {
      wallet: name
    }),
    label: i18n2.t("connect.secondary_action.get.label"),
    onClick: () => changeWalletStep(
      hasQrCodeAndExtension || hasQrCodeAndDesktop ? "DOWNLOAD_OPTIONS" : "DOWNLOAD"
      /* Download */
    )
  } : null;
  const { width: windowWidth } = useWindowSize();
  const smallWindow = windowWidth && windowWidth < 768;
  (0, import_react66.useEffect)(() => {
    preloadBrowserIcon();
    preloadPlatformIcon();
  }, []);
  return import_react66.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    height: "full",
    width: "full"
  }, hasQrCode ? import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    height: "full",
    justifyContent: "center"
  }, import_react66.default.createElement(QRCode, {
    logoBackground: iconBackground,
    logoSize: compactModeEnabled ? 60 : 72,
    logoUrl: iconUrl,
    size: compactModeEnabled ? 318 : smallWindow ? Math.max(280, Math.min(windowWidth - 308, 382)) : 382,
    uri: qrCodeUri
  })) : import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    justifyContent: "center",
    style: { flexGrow: 1 }
  }, import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "8"
  }, import_react66.default.createElement(Box, {
    borderRadius: "10",
    height: LOGO_SIZE,
    overflow: "hidden"
  }, import_react66.default.createElement(AsyncImage, {
    height: LOGO_SIZE,
    src: iconUrl,
    width: LOGO_SIZE
  })), import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "4",
    paddingX: "32",
    style: { textAlign: "center" }
  }, import_react66.default.createElement(Text, {
    color: "modalText",
    size: "18",
    weight: "bold"
  }, ready ? i18n2.t("connect.status.opening", {
    wallet: name
  }) : hasExtension ? i18n2.t("connect.status.not_installed", {
    wallet: name
  }) : i18n2.t("connect.status.not_available", {
    wallet: name
  })), !ready && hasExtension ? import_react66.default.createElement(Box, {
    paddingTop: "20"
  }, import_react66.default.createElement(ActionButton, {
    href: wallet.extensionDownloadUrl,
    label: i18n2.t("connect.secondary_action.install.label"),
    type: "secondary"
  })) : null, ready && !hasQrCode && import_react66.default.createElement(import_react66.default.Fragment, null, import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center"
  }, import_react66.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    textAlign: "center",
    weight: "medium"
  }, i18n2.t("connect.status.confirm"))), import_react66.default.createElement(Box, {
    alignItems: "center",
    color: "modalText",
    display: "flex",
    flexDirection: "row",
    height: "32",
    marginTop: "8"
  }, connectionError ? import_react66.default.createElement(ActionButton, {
    label: i18n2.t("connect.secondary_action.retry.label"),
    onClick: getDesktopDeepLink ? async () => {
      const uri = await getDesktopDeepLink();
      window.open(uri, safari ? "_blank" : "_self");
    } : () => {
      reconnect(wallet);
    }
  }) : import_react66.default.createElement(Box, {
    color: "modalTextSecondary"
  }, import_react66.default.createElement(SpinnerIcon, null))))))), import_react66.default.createElement(Box, {
    alignItems: "center",
    borderRadius: "10",
    display: "flex",
    flexDirection: "row",
    gap: "8",
    height: "28",
    justifyContent: "space-between",
    marginTop: "12"
  }, ready && secondaryAction && import_react66.default.createElement(import_react66.default.Fragment, null, import_react66.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    weight: "medium"
  }, secondaryAction.description), import_react66.default.createElement(ActionButton, {
    label: secondaryAction.label,
    onClick: secondaryAction.onClick,
    type: "secondary"
  }))));
}
var DownloadOptionsBox = ({
  actionLabel,
  description,
  iconAccent,
  iconBackground,
  iconUrl,
  isCompact,
  onAction,
  title,
  url,
  variant
}) => {
  const isBrowserCard = variant === "browser";
  const gradientRgbas = !isBrowserCard && iconAccent && getGradientRGBAs(iconAccent);
  return import_react66.default.createElement(Box, {
    alignItems: "center",
    borderRadius: "13",
    display: "flex",
    justifyContent: "center",
    overflow: "hidden",
    paddingX: isCompact ? "18" : "44",
    position: "relative",
    style: { flex: 1, isolation: "isolate" },
    width: "full"
  }, import_react66.default.createElement(Box, {
    borderColor: "actionButtonBorder",
    borderRadius: "13",
    borderStyle: "solid",
    borderWidth: "1",
    style: {
      bottom: "0",
      left: "0",
      position: "absolute",
      right: "0",
      top: "0",
      zIndex: 1
    }
  }), isBrowserCard && import_react66.default.createElement(Box, {
    background: "downloadTopCardBackground",
    height: "full",
    position: "absolute",
    style: {
      zIndex: 0
    },
    width: "full"
  }, import_react66.default.createElement(Box, {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    style: {
      bottom: "0",
      filter: "blur(20px)",
      left: "0",
      position: "absolute",
      right: "0",
      top: "0",
      transform: "translate3d(0, 0, 0)"
    }
  }, import_react66.default.createElement(Box, {
    style: {
      filter: "blur(100px)",
      marginLeft: -27,
      marginTop: -20,
      opacity: 0.6,
      transform: "translate3d(0, 0, 0)"
    }
  }, import_react66.default.createElement(AsyncImage, {
    borderRadius: "full",
    height: "200",
    src: iconUrl,
    width: "200"
  })), import_react66.default.createElement(Box, {
    style: {
      filter: "blur(100px)",
      marginRight: 0,
      marginTop: 105,
      opacity: 0.6,
      overflow: "auto",
      transform: "translate3d(0, 0, 0)"
    }
  }, import_react66.default.createElement(AsyncImage, {
    borderRadius: "full",
    height: "200",
    src: iconUrl,
    width: "200"
  })))), !isBrowserCard && gradientRgbas && import_react66.default.createElement(Box, {
    background: "downloadBottomCardBackground",
    style: {
      bottom: "0",
      left: "0",
      position: "absolute",
      right: "0",
      top: "0"
    }
  }, import_react66.default.createElement(Box, {
    position: "absolute",
    style: {
      background: `radial-gradient(50% 50% at 50% 50%, ${gradientRgbas[0]} 0%, ${gradientRgbas[1]} 25%, rgba(0,0,0,0) 100%)`,
      height: 564,
      left: -215,
      top: -197,
      transform: "translate3d(0, 0, 0)",
      width: 564
    }
  }), import_react66.default.createElement(Box, {
    position: "absolute",
    style: {
      background: `radial-gradient(50% 50% at 50% 50%, ${gradientRgbas[2]} 0%, rgba(0, 0, 0, 0) 100%)`,
      height: 564,
      left: -1,
      top: -76,
      transform: "translate3d(0, 0, 0)",
      width: 564
    }
  })), import_react66.default.createElement(Box, {
    alignItems: "flex-start",
    display: "flex",
    flexDirection: "row",
    gap: "24",
    height: "max",
    justifyContent: "center",
    style: { zIndex: 1 }
  }, import_react66.default.createElement(Box, null, import_react66.default.createElement(AsyncImage, {
    height: "60",
    src: iconUrl,
    width: "60",
    ...iconBackground ? {
      background: iconBackground,
      borderColor: "generalBorder",
      borderRadius: "10"
    } : null
  })), import_react66.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "4",
    style: { flex: 1 },
    width: "full"
  }, import_react66.default.createElement(Text, {
    color: "modalText",
    size: "14",
    weight: "bold"
  }, title), import_react66.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    weight: "medium"
  }, description), import_react66.default.createElement(Box, {
    marginTop: "14",
    width: "max"
  }, import_react66.default.createElement(ActionButton, {
    href: url,
    label: actionLabel,
    onClick: onAction,
    size: "medium"
  })))));
};
function DownloadOptionsDetail({
  changeWalletStep,
  wallet
}) {
  const browser = getBrowser();
  const platform = getPlatform();
  const modalSize = (0, import_react66.useContext)(ModalSizeContext);
  const isCompact = modalSize === "compact";
  const {
    desktop,
    desktopDownloadUrl,
    extension,
    extensionDownloadUrl,
    mobileDownloadUrl
  } = wallet;
  const { i18n: i18n2 } = (0, import_react66.useContext)(I18nContext);
  (0, import_react66.useEffect)(() => {
    preloadCreateIcon();
    preloadScanIcon();
    preloadRefreshIcon();
    preloadConnectIcon();
  }, []);
  return import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "24",
    height: "full",
    marginBottom: "8",
    marginTop: "4",
    width: "full"
  }, import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "8",
    height: "full",
    justifyContent: "center",
    width: "full"
  }, extensionDownloadUrl && import_react66.default.createElement(DownloadOptionsBox, {
    actionLabel: i18n2.t("get_options.extension.download.label", {
      browser
    }),
    description: i18n2.t("get_options.extension.description"),
    iconUrl: getBrowserSrc,
    isCompact,
    onAction: () => changeWalletStep(
      (extension == null ? void 0 : extension.instructions) ? "INSTRUCTIONS_EXTENSION" : "CONNECT"
      /* Connect */
    ),
    title: i18n2.t("get_options.extension.title", {
      wallet: wallet.name,
      browser
    }),
    url: extensionDownloadUrl,
    variant: "browser"
  }), desktopDownloadUrl && import_react66.default.createElement(DownloadOptionsBox, {
    actionLabel: i18n2.t("get_options.desktop.download.label", {
      platform
    }),
    description: i18n2.t("get_options.desktop.description"),
    iconUrl: getPlatformSrc,
    isCompact,
    onAction: () => changeWalletStep(
      (desktop == null ? void 0 : desktop.instructions) ? "INSTRUCTIONS_DESKTOP" : "CONNECT"
      /* Connect */
    ),
    title: i18n2.t("get_options.desktop.title", {
      wallet: wallet.name,
      platform
    }),
    url: desktopDownloadUrl,
    variant: "desktop"
  }), mobileDownloadUrl && import_react66.default.createElement(DownloadOptionsBox, {
    actionLabel: i18n2.t("get_options.mobile.download.label", {
      wallet: wallet.name
    }),
    description: i18n2.t("get_options.mobile.description"),
    iconAccent: wallet.iconAccent,
    iconBackground: wallet.iconBackground,
    iconUrl: wallet.iconUrl,
    isCompact,
    onAction: () => {
      changeWalletStep(
        "DOWNLOAD"
        /* Download */
      );
    },
    title: i18n2.t("get_options.mobile.title", { wallet: wallet.name }),
    variant: "app"
  })));
}
function DownloadDetail({
  changeWalletStep,
  wallet
}) {
  const { downloadUrls, qrCode } = wallet;
  const { i18n: i18n2 } = (0, import_react66.useContext)(I18nContext);
  (0, import_react66.useEffect)(() => {
    preloadCreateIcon();
    preloadScanIcon();
  }, []);
  return import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "24",
    height: "full",
    width: "full"
  }, import_react66.default.createElement(Box, {
    style: { maxWidth: 220, textAlign: "center" }
  }, import_react66.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    weight: "semibold"
  }, i18n2.t("get_mobile.description"))), import_react66.default.createElement(Box, {
    height: "full"
  }, (downloadUrls == null ? void 0 : downloadUrls.qrCode) ? import_react66.default.createElement(QRCode, {
    logoSize: 0,
    size: 268,
    uri: downloadUrls.qrCode
  }) : null), import_react66.default.createElement(Box, {
    alignItems: "center",
    borderRadius: "10",
    display: "flex",
    flexDirection: "row",
    gap: "8",
    height: "34",
    justifyContent: "space-between",
    marginBottom: "12",
    paddingY: "8"
  }, import_react66.default.createElement(ActionButton, {
    label: i18n2.t("get_mobile.continue.label"),
    onClick: () => changeWalletStep(
      (qrCode == null ? void 0 : qrCode.instructions) ? "INSTRUCTIONS_MOBILE" : "CONNECT"
      /* Connect */
    )
  })));
}
var stepIcons = {
  connect: () => import_react66.default.createElement(ConnectIcon, null),
  create: () => import_react66.default.createElement(CreateIcon, null),
  install: (wallet) => import_react66.default.createElement(AsyncImage, {
    background: wallet.iconBackground,
    borderColor: "generalBorder",
    borderRadius: "10",
    height: "48",
    src: wallet.iconUrl,
    width: "48"
  }),
  refresh: () => import_react66.default.createElement(RefreshIcon, null),
  scan: () => import_react66.default.createElement(ScanIcon, null)
};
function InstructionMobileDetail({
  connectWallet,
  wallet
}) {
  var _a, _b, _c, _d;
  const { i18n: i18n2 } = (0, import_react66.useContext)(I18nContext);
  return import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    height: "full",
    width: "full"
  }, import_react66.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "28",
    height: "full",
    justifyContent: "center",
    paddingY: "32",
    style: { maxWidth: 320 }
  }, (_b = (_a = wallet == null ? void 0 : wallet.qrCode) == null ? void 0 : _a.instructions) == null ? void 0 : _b.steps.map((d, idx) => {
    var _a2;
    return import_react66.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      gap: "16",
      key: idx
    }, import_react66.default.createElement(Box, {
      borderRadius: "10",
      height: "48",
      minWidth: "48",
      overflow: "hidden",
      position: "relative",
      width: "48"
    }, (_a2 = stepIcons[d.step]) == null ? void 0 : _a2.call(stepIcons, wallet)), import_react66.default.createElement(Box, {
      display: "flex",
      flexDirection: "column",
      gap: "4"
    }, import_react66.default.createElement(Text, {
      color: "modalText",
      size: "14",
      weight: "bold"
    }, i18n2.t(d.title)), import_react66.default.createElement(Text, {
      color: "modalTextSecondary",
      size: "14",
      weight: "medium"
    }, i18n2.t(d.description))));
  })), import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "12",
    justifyContent: "center",
    marginBottom: "16"
  }, import_react66.default.createElement(ActionButton, {
    label: i18n2.t("get_instructions.mobile.connect.label"),
    onClick: () => connectWallet(wallet)
  }), import_react66.default.createElement(Box, {
    as: "a",
    className: touchableStyles({ active: "shrink", hover: "grow" }),
    display: "block",
    href: (_d = (_c = wallet == null ? void 0 : wallet.qrCode) == null ? void 0 : _c.instructions) == null ? void 0 : _d.learnMoreUrl,
    paddingX: "12",
    paddingY: "4",
    rel: "noreferrer",
    style: { willChange: "transform" },
    target: "_blank",
    transition: "default"
  }, import_react66.default.createElement(Text, {
    color: "accentColor",
    size: "14",
    weight: "bold"
  }, i18n2.t("get_instructions.mobile.learn_more.label")))));
}
function InstructionExtensionDetail({
  wallet
}) {
  var _a, _b, _c, _d;
  const { i18n: i18n2 } = (0, import_react66.useContext)(I18nContext);
  return import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    height: "full",
    width: "full"
  }, import_react66.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "28",
    height: "full",
    justifyContent: "center",
    paddingY: "32",
    style: { maxWidth: 320 }
  }, (_b = (_a = wallet == null ? void 0 : wallet.extension) == null ? void 0 : _a.instructions) == null ? void 0 : _b.steps.map((d, idx) => {
    var _a2;
    return import_react66.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      gap: "16",
      key: idx
    }, import_react66.default.createElement(Box, {
      borderRadius: "10",
      height: "48",
      minWidth: "48",
      overflow: "hidden",
      position: "relative",
      width: "48"
    }, (_a2 = stepIcons[d.step]) == null ? void 0 : _a2.call(stepIcons, wallet)), import_react66.default.createElement(Box, {
      display: "flex",
      flexDirection: "column",
      gap: "4"
    }, import_react66.default.createElement(Text, {
      color: "modalText",
      size: "14",
      weight: "bold"
    }, i18n2.t(d.title)), import_react66.default.createElement(Text, {
      color: "modalTextSecondary",
      size: "14",
      weight: "medium"
    }, i18n2.t(d.description))));
  })), import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "12",
    justifyContent: "center",
    marginBottom: "16"
  }, import_react66.default.createElement(ActionButton, {
    label: i18n2.t("get_instructions.extension.refresh.label"),
    onClick: window.location.reload.bind(window.location)
  }), import_react66.default.createElement(Box, {
    as: "a",
    className: touchableStyles({ active: "shrink", hover: "grow" }),
    display: "block",
    href: (_d = (_c = wallet == null ? void 0 : wallet.extension) == null ? void 0 : _c.instructions) == null ? void 0 : _d.learnMoreUrl,
    paddingX: "12",
    paddingY: "4",
    rel: "noreferrer",
    style: { willChange: "transform" },
    target: "_blank",
    transition: "default"
  }, import_react66.default.createElement(Text, {
    color: "accentColor",
    size: "14",
    weight: "bold"
  }, i18n2.t("get_instructions.extension.learn_more.label")))));
}
function InstructionDesktopDetail({
  connectWallet,
  wallet
}) {
  var _a, _b, _c, _d;
  const { i18n: i18n2 } = (0, import_react66.useContext)(I18nContext);
  return import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    height: "full",
    width: "full"
  }, import_react66.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "28",
    height: "full",
    justifyContent: "center",
    paddingY: "32",
    style: { maxWidth: 320 }
  }, (_b = (_a = wallet == null ? void 0 : wallet.desktop) == null ? void 0 : _a.instructions) == null ? void 0 : _b.steps.map((d, idx) => {
    var _a2;
    return import_react66.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      gap: "16",
      key: idx
    }, import_react66.default.createElement(Box, {
      borderRadius: "10",
      height: "48",
      minWidth: "48",
      overflow: "hidden",
      position: "relative",
      width: "48"
    }, (_a2 = stepIcons[d.step]) == null ? void 0 : _a2.call(stepIcons, wallet)), import_react66.default.createElement(Box, {
      display: "flex",
      flexDirection: "column",
      gap: "4"
    }, import_react66.default.createElement(Text, {
      color: "modalText",
      size: "14",
      weight: "bold"
    }, i18n2.t(d.title)), import_react66.default.createElement(Text, {
      color: "modalTextSecondary",
      size: "14",
      weight: "medium"
    }, i18n2.t(d.description))));
  })), import_react66.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "12",
    justifyContent: "center",
    marginBottom: "16"
  }, import_react66.default.createElement(ActionButton, {
    label: i18n2.t("get_instructions.desktop.connect.label"),
    onClick: () => connectWallet(wallet)
  }), import_react66.default.createElement(Box, {
    as: "a",
    className: touchableStyles({ active: "shrink", hover: "grow" }),
    display: "block",
    href: (_d = (_c = wallet == null ? void 0 : wallet.desktop) == null ? void 0 : _c.instructions) == null ? void 0 : _d.learnMoreUrl,
    paddingX: "12",
    paddingY: "4",
    rel: "noreferrer",
    style: { willChange: "transform" },
    target: "_blank",
    transition: "default"
  }, import_react66.default.createElement(Text, {
    color: "accentColor",
    size: "14",
    weight: "bold"
  }, i18n2.t("get_instructions.desktop.learn_more.label")))));
}
function DesktopOptions({ onClose }) {
  const titleId = "rk_connect_title";
  const safari = isSafari();
  const [selectedOptionId, setSelectedOptionId] = (0, import_react57.useState)();
  const [selectedWallet, setSelectedWallet] = (0, import_react57.useState)();
  const [qrCodeUri, setQrCodeUri] = (0, import_react57.useState)();
  const hasQrCode = !!(selectedWallet == null ? void 0 : selectedWallet.qrCode) && qrCodeUri;
  const [connectionError, setConnectionError] = (0, import_react57.useState)(false);
  const modalSize = (0, import_react57.useContext)(ModalSizeContext);
  const compactModeEnabled = modalSize === ModalSizeOptions.COMPACT;
  const { disclaimer: Disclaimer } = (0, import_react57.useContext)(AppContext);
  const { i18n: i18n2 } = (0, import_react57.useContext)(I18nContext);
  const initialized = (0, import_react57.useRef)(false);
  const { connector } = (0, import_react57.useContext)(WalletButtonContext);
  const wallets = useWalletConnectors().filter((wallet) => wallet.ready || !!wallet.extensionDownloadUrl).sort((a, b) => a.groupIndex - b.groupIndex);
  const groupedWallets = groupBy(wallets, (wallet) => wallet.groupName);
  const supportedI18nGroupNames = [
    "Recommended",
    "Other",
    "Popular",
    "More",
    "Others"
  ];
  (0, import_react57.useEffect)(() => {
    if (connector && !initialized.current) {
      changeWalletStep(
        "CONNECT"
        /* Connect */
      );
      selectWallet(connector);
      initialized.current = true;
    }
  }, [connector]);
  const connectToWallet = (wallet) => {
    var _a, _b, _c;
    setConnectionError(false);
    if (wallet.ready) {
      (_b = (_a = wallet == null ? void 0 : wallet.connect) == null ? void 0 : _a.call(wallet)) == null ? void 0 : _b.catch(() => {
        setConnectionError(true);
      });
      const getDesktopDeepLink = (_c = wallet.desktop) == null ? void 0 : _c.getUri;
      if (getDesktopDeepLink) {
        setTimeout(async () => {
          const uri = await getDesktopDeepLink();
          window.open(uri, safari ? "_blank" : "_self");
        }, 0);
      }
    }
  };
  const selectWallet = (wallet) => {
    var _a;
    addLatestWalletId(wallet.id);
    connectToWallet(wallet);
    setSelectedOptionId(wallet.id);
    if (wallet.ready) {
      let callbackFired = false;
      (_a = wallet == null ? void 0 : wallet.onConnecting) == null ? void 0 : _a.call(wallet, async () => {
        var _a2, _b;
        if (callbackFired)
          return;
        callbackFired = true;
        const sWallet = wallets.find((w) => wallet.id === w.id);
        const uri = await ((_a2 = sWallet == null ? void 0 : sWallet.qrCode) == null ? void 0 : _a2.getUri());
        setQrCodeUri(uri);
        setTimeout(() => {
          setSelectedWallet(sWallet);
          changeWalletStep(
            "CONNECT"
            /* Connect */
          );
        }, uri ? 0 : 50);
        const provider = await (sWallet == null ? void 0 : sWallet.connector.getProvider());
        const connection = (_b = provider == null ? void 0 : provider.signer) == null ? void 0 : _b.connection;
        if ((connection == null ? void 0 : connection.on) && (connection == null ? void 0 : connection.off)) {
          const handleConnectionClose = () => {
            removeHandlers();
            selectWallet(wallet);
          };
          const removeHandlers = () => {
            connection.off("close", handleConnectionClose);
            connection.off("open", removeHandlers);
          };
          connection.on("close", handleConnectionClose);
          connection.on("open", removeHandlers);
        }
      });
    } else {
      setSelectedWallet(wallet);
      changeWalletStep(
        (wallet == null ? void 0 : wallet.extensionDownloadUrl) ? "DOWNLOAD_OPTIONS" : "CONNECT"
        /* Connect */
      );
    }
  };
  const getWalletDownload = (id) => {
    var _a;
    setSelectedOptionId(id);
    const sWallet = wallets.find((w) => id === w.id);
    const isMobile2 = (_a = sWallet == null ? void 0 : sWallet.downloadUrls) == null ? void 0 : _a.qrCode;
    const isDesktop = !!(sWallet == null ? void 0 : sWallet.desktopDownloadUrl);
    const isExtension = !!(sWallet == null ? void 0 : sWallet.extensionDownloadUrl);
    setSelectedWallet(sWallet);
    if (isMobile2 && (isExtension || isDesktop)) {
      changeWalletStep(
        "DOWNLOAD_OPTIONS"
        /* DownloadOptions */
      );
    } else if (isMobile2) {
      changeWalletStep(
        "DOWNLOAD"
        /* Download */
      );
    } else if (isDesktop) {
      changeWalletStep(
        "INSTRUCTIONS_DESKTOP"
        /* InstructionsDesktop */
      );
    } else {
      changeWalletStep(
        "INSTRUCTIONS_EXTENSION"
        /* InstructionsExtension */
      );
    }
  };
  const clearSelectedWallet = () => {
    setSelectedOptionId(void 0);
    setSelectedWallet(void 0);
    setQrCodeUri(void 0);
  };
  const changeWalletStep = (newWalletStep, isBack = false) => {
    if (isBack && newWalletStep === "GET" && initialWalletStep === "GET") {
      clearSelectedWallet();
    } else if (!isBack && newWalletStep === "GET") {
      setInitialWalletStep(
        "GET"
        /* Get */
      );
    } else if (!isBack && newWalletStep === "CONNECT") {
      setInitialWalletStep(
        "CONNECT"
        /* Connect */
      );
    }
    setWalletStep(newWalletStep);
  };
  const [initialWalletStep, setInitialWalletStep] = (0, import_react57.useState)(
    "NONE"
    /* None */
  );
  const [walletStep, setWalletStep] = (0, import_react57.useState)(
    "NONE"
    /* None */
  );
  let walletContent = null;
  let headerLabel = null;
  let headerBackButtonLink = null;
  let headerBackButtonCallback;
  (0, import_react57.useEffect)(() => {
    setConnectionError(false);
  }, [walletStep, selectedWallet]);
  const hasExtension = !!(selectedWallet == null ? void 0 : selectedWallet.extensionDownloadUrl);
  const hasExtensionAndMobile = !!(hasExtension && (selectedWallet == null ? void 0 : selectedWallet.mobileDownloadUrl));
  switch (walletStep) {
    case "NONE":
      walletContent = import_react57.default.createElement(ConnectModalIntro, {
        getWallet: () => changeWalletStep(
          "GET"
          /* Get */
        )
      });
      break;
    case "LEARN_COMPACT":
      walletContent = import_react57.default.createElement(ConnectModalIntro, {
        compactModeEnabled,
        getWallet: () => changeWalletStep(
          "GET"
          /* Get */
        )
      });
      headerLabel = i18n2.t("intro.title");
      headerBackButtonLink = "NONE";
      break;
    case "GET":
      walletContent = import_react57.default.createElement(GetDetail, {
        getWalletDownload,
        compactModeEnabled
      });
      headerLabel = i18n2.t("get.title");
      headerBackButtonLink = compactModeEnabled ? "LEARN_COMPACT" : "NONE";
      break;
    case "CONNECT":
      walletContent = selectedWallet && import_react57.default.createElement(ConnectDetail, {
        changeWalletStep,
        compactModeEnabled,
        connectionError,
        onClose,
        qrCodeUri,
        reconnect: connectToWallet,
        wallet: selectedWallet
      });
      headerLabel = hasQrCode && (selectedWallet.name === "WalletConnect" ? i18n2.t("connect_scan.fallback_title") : i18n2.t("connect_scan.title", {
        wallet: selectedWallet.name
      }));
      headerBackButtonLink = compactModeEnabled ? connector ? null : "NONE" : null;
      headerBackButtonCallback = compactModeEnabled ? !connector ? clearSelectedWallet : () => {
      } : () => {
      };
      break;
    case "DOWNLOAD_OPTIONS":
      walletContent = selectedWallet && import_react57.default.createElement(DownloadOptionsDetail, {
        changeWalletStep,
        wallet: selectedWallet
      });
      headerLabel = selectedWallet && i18n2.t("get_options.short_title", { wallet: selectedWallet.name });
      headerBackButtonLink = connector ? "CONNECT" : compactModeEnabled ? "NONE" : null;
      break;
    case "DOWNLOAD":
      walletContent = selectedWallet && import_react57.default.createElement(DownloadDetail, {
        changeWalletStep,
        wallet: selectedWallet
      });
      headerLabel = selectedWallet && i18n2.t("get_mobile.title", { wallet: selectedWallet.name });
      headerBackButtonLink = hasExtensionAndMobile ? "DOWNLOAD_OPTIONS" : initialWalletStep;
      break;
    case "INSTRUCTIONS_MOBILE":
      walletContent = selectedWallet && import_react57.default.createElement(InstructionMobileDetail, {
        connectWallet: selectWallet,
        wallet: selectedWallet
      });
      headerLabel = selectedWallet && i18n2.t("get_options.title", {
        wallet: compactModeEnabled ? selectedWallet.shortName || selectedWallet.name : selectedWallet.name
      });
      headerBackButtonLink = "DOWNLOAD";
      break;
    case "INSTRUCTIONS_EXTENSION":
      walletContent = selectedWallet && import_react57.default.createElement(InstructionExtensionDetail, {
        wallet: selectedWallet
      });
      headerLabel = selectedWallet && i18n2.t("get_options.title", {
        wallet: compactModeEnabled ? selectedWallet.shortName || selectedWallet.name : selectedWallet.name
      });
      headerBackButtonLink = "DOWNLOAD_OPTIONS";
      break;
    case "INSTRUCTIONS_DESKTOP":
      walletContent = selectedWallet && import_react57.default.createElement(InstructionDesktopDetail, {
        connectWallet: selectWallet,
        wallet: selectedWallet
      });
      headerLabel = selectedWallet && i18n2.t("get_options.title", {
        wallet: compactModeEnabled ? selectedWallet.shortName || selectedWallet.name : selectedWallet.name
      });
      headerBackButtonLink = "DOWNLOAD_OPTIONS";
      break;
    default:
      break;
  }
  return import_react57.default.createElement(Box, {
    display: "flex",
    flexDirection: "row",
    style: { maxHeight: compactModeEnabled ? 468 : 504 }
  }, (compactModeEnabled ? walletStep === "NONE" : true) && import_react57.default.createElement(Box, {
    className: compactModeEnabled ? sidebarCompactMode : sidebar,
    display: "flex",
    flexDirection: "column",
    marginTop: "16"
  }, import_react57.default.createElement(Box, {
    display: "flex",
    justifyContent: "space-between"
  }, compactModeEnabled && Disclaimer && import_react57.default.createElement(Box, {
    marginLeft: "16",
    width: "28"
  }, import_react57.default.createElement(InfoButton, {
    onClick: () => changeWalletStep(
      "LEARN_COMPACT"
      /* LearnCompact */
    )
  })), compactModeEnabled && !Disclaimer && import_react57.default.createElement(Box, {
    marginLeft: "16",
    width: "28"
  }), import_react57.default.createElement(Box, {
    marginLeft: compactModeEnabled ? "0" : "6",
    paddingBottom: "8",
    paddingTop: "2",
    paddingX: "18"
  }, import_react57.default.createElement(Text, {
    as: "h1",
    color: "modalText",
    id: titleId,
    size: "18",
    weight: "heavy",
    testId: "connect-header-label"
  }, i18n2.t("connect.title"))), compactModeEnabled && import_react57.default.createElement(Box, {
    marginRight: "16"
  }, import_react57.default.createElement(CloseButton, {
    onClose
  }))), import_react57.default.createElement(Box, {
    className: ScrollClassName,
    paddingBottom: "18"
  }, Object.entries(groupedWallets).map(([groupName, wallets2], index) => wallets2.length > 0 && import_react57.default.createElement(import_react57.Fragment, {
    key: index
  }, groupName ? import_react57.default.createElement(Box, {
    marginBottom: "8",
    marginTop: "16",
    marginX: "6"
  }, import_react57.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    weight: "bold"
  }, supportedI18nGroupNames.includes(groupName) ? i18n2.t(`connector_group.${groupName.toLowerCase()}`) : groupName)) : null, import_react57.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    gap: "4"
  }, wallets2.map((wallet) => {
    return import_react57.default.createElement(ModalSelection, {
      currentlySelected: wallet.id === selectedOptionId,
      iconBackground: wallet.iconBackground,
      iconUrl: wallet.iconUrl,
      key: wallet.id,
      name: wallet.name,
      onClick: () => selectWallet(wallet),
      ready: wallet.ready,
      recent: wallet.recent,
      testId: `wallet-option-${wallet.id}`
    });
  }))))), compactModeEnabled && import_react57.default.createElement(import_react57.default.Fragment, null, import_react57.default.createElement(Box, {
    background: "generalBorder",
    height: "1",
    marginTop: "-1"
  }), Disclaimer ? import_react57.default.createElement(Box, {
    paddingX: "24",
    paddingY: "16",
    textAlign: "center"
  }, import_react57.default.createElement(Disclaimer, {
    Link: DisclaimerLink,
    Text: DisclaimerText
  })) : import_react57.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    justifyContent: "space-between",
    paddingX: "24",
    paddingY: "16"
  }, import_react57.default.createElement(Box, {
    paddingY: "4"
  }, import_react57.default.createElement(Text, {
    color: "modalTextSecondary",
    size: "14",
    weight: "medium"
  }, i18n2.t("connect.new_to_ethereum.description"))), import_react57.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
    gap: "4",
    justifyContent: "center"
  }, import_react57.default.createElement(Box, {
    className: touchableStyles({
      active: "shrink",
      hover: "grow"
    }),
    cursor: "pointer",
    onClick: () => changeWalletStep(
      "LEARN_COMPACT"
      /* LearnCompact */
    ),
    paddingY: "4",
    style: { willChange: "transform" },
    transition: "default"
  }, import_react57.default.createElement(Text, {
    color: "accentColor",
    size: "14",
    weight: "bold"
  }, i18n2.t("connect.new_to_ethereum.learn_more.label"))))))), (compactModeEnabled ? walletStep !== "NONE" : true) && import_react57.default.createElement(import_react57.default.Fragment, null, !compactModeEnabled && import_react57.default.createElement(Box, {
    background: "generalBorder",
    minWidth: "1",
    width: "1"
  }), import_react57.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    margin: "16",
    style: { flexGrow: 1 }
  }, import_react57.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    justifyContent: "space-between",
    marginBottom: "12"
  }, import_react57.default.createElement(Box, {
    width: "28"
  }, headerBackButtonLink && import_react57.default.createElement(Box, {
    as: "button",
    className: touchableStyles({
      active: "shrinkSm",
      hover: "growLg"
    }),
    color: "accentColor",
    onClick: () => {
      headerBackButtonLink && changeWalletStep(headerBackButtonLink, true);
      headerBackButtonCallback == null ? void 0 : headerBackButtonCallback();
    },
    paddingX: "8",
    paddingY: "4",
    style: {
      boxSizing: "content-box",
      height: 17,
      willChange: "transform"
    },
    transition: "default",
    type: "button"
  }, import_react57.default.createElement(BackIcon, null))), import_react57.default.createElement(Box, {
    display: "flex",
    justifyContent: "center",
    style: { flexGrow: 1 }
  }, headerLabel && import_react57.default.createElement(Text, {
    color: "modalText",
    size: "18",
    textAlign: "center",
    weight: "heavy"
  }, headerLabel)), import_react57.default.createElement(CloseButton, {
    onClose
  })), import_react57.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    style: { minHeight: compactModeEnabled ? 396 : 432 }
  }, import_react57.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    gap: "6",
    height: "full",
    justifyContent: "center",
    marginX: "8"
  }, walletContent)))));
}
var rotatingBorder = "_1am14412";
var scroll = "_1am14410";
var spinner = "_1am14413";
var LoadingSpinner = ({ wallet }) => {
  const width = 80;
  const height = 80;
  const radiusFactor = 20;
  const perimeter = 2 * (width + height - 4 * radiusFactor);
  return import_react72.default.createElement("svg", {
    className: spinner,
    viewBox: "0 0 86 86",
    width: "86",
    height: "86"
  }, import_react72.default.createElement("title", null, "Loading"), import_react72.default.createElement("rect", {
    x: "3",
    y: "3",
    width,
    height,
    rx: radiusFactor,
    ry: radiusFactor,
    strokeDasharray: `${perimeter / 3} ${2 * perimeter / 3}`,
    strokeDashoffset: perimeter,
    className: rotatingBorder,
    style: {
      stroke: (wallet == null ? void 0 : wallet.iconAccent) || "#0D3887"
    }
  }));
};
function WalletButton({
  onClose,
  wallet,
  connecting
}) {
  const {
    connect,
    connector,
    iconBackground,
    iconUrl,
    id,
    mobile,
    name,
    onConnecting,
    ready,
    shortName
  } = wallet;
  const getMobileUri = mobile == null ? void 0 : mobile.getUri;
  const coolModeRef = useCoolMode(iconUrl);
  const initialized = (0, import_react72.useRef)(false);
  const { i18n: i18n2 } = (0, import_react72.useContext)(I18nContext);
  const onConnect = (0, import_react72.useCallback)(async () => {
    var _a;
    if (id === "walletConnect")
      onClose == null ? void 0 : onClose();
    (_a = connect == null ? void 0 : connect()) == null ? void 0 : _a.catch(() => {
    });
    let callbackFired = false;
    onConnecting == null ? void 0 : onConnecting(async () => {
      if (callbackFired)
        return;
      callbackFired = true;
      if (getMobileUri) {
        const mobileUri = await getMobileUri();
        if (connector.id === "walletConnect" || connector.id === "walletConnectLegacy") {
          setWalletConnectDeepLink({ mobileUri, name });
        }
        if (mobileUri.startsWith("http")) {
          const link = document.createElement("a");
          link.href = mobileUri;
          link.target = "_blank";
          link.rel = "noreferrer noopener";
          link.click();
        } else {
          window.location.href = mobileUri;
        }
      }
    });
  }, [connector, connect, getMobileUri, onConnecting, onClose, name, id]);
  (0, import_react72.useEffect)(() => {
    if (connecting && !initialized.current) {
      onConnect();
      initialized.current = true;
    }
  }, [connecting, onConnect]);
  return import_react72.default.createElement(Box, {
    as: "button",
    color: ready ? "modalText" : "modalTextSecondary",
    disabled: !ready,
    fontFamily: "body",
    key: id,
    onClick: onConnect,
    ref: coolModeRef,
    style: { overflow: "visible", textAlign: "center" },
    testId: `wallet-option-${id}`,
    type: "button",
    width: "full"
  }, import_react72.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center"
  }, import_react72.default.createElement(Box, {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    paddingBottom: "8",
    paddingTop: "10",
    position: "relative"
  }, connecting ? import_react72.default.createElement(LoadingSpinner, {
    wallet
  }) : null, import_react72.default.createElement(AsyncImage, {
    background: iconBackground,
    borderRadius: "13",
    boxShadow: "walletLogo",
    height: "60",
    src: iconUrl,
    width: "60"
  })), !connecting ? import_react72.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    textAlign: "center"
  }, import_react72.default.createElement(Text, {
    as: "h2",
    color: wallet.ready ? "modalText" : "modalTextSecondary",
    size: "13",
    weight: "medium"
  }, import_react72.default.createElement(Box, {
    as: "span",
    position: "relative"
  }, shortName != null ? shortName : name, !wallet.ready && " (unsupported)")), wallet.recent && import_react72.default.createElement(Text, {
    color: "accentColor",
    size: "12",
    weight: "medium"
  }, i18n2.t("connect.recent"))) : null));
}
function MobileOptions({ onClose }) {
  var _a;
  const titleId = "rk_connect_title";
  const wallets = useWalletConnectors();
  const { disclaimer: Disclaimer, learnMoreUrl } = (0, import_react72.useContext)(AppContext);
  let headerLabel = null;
  let walletContent = null;
  let headerBackgroundContrast = false;
  let headerBackButtonLink = null;
  const [walletStep, setWalletStep] = (0, import_react72.useState)(
    "CONNECT"
    /* Connect */
  );
  const { i18n: i18n2 } = (0, import_react72.useContext)(I18nContext);
  const ios = isIOS();
  switch (walletStep) {
    case "CONNECT": {
      headerLabel = i18n2.t("connect.title");
      headerBackgroundContrast = true;
      walletContent = import_react72.default.createElement(Box, null, import_react72.default.createElement(Box, {
        background: "profileForeground",
        className: scroll,
        display: "flex",
        paddingBottom: "20",
        paddingTop: "6"
      }, import_react72.default.createElement(Box, {
        display: "flex",
        style: { margin: "0 auto" }
      }, wallets.filter((wallet) => wallet.ready).map((wallet) => {
        return import_react72.default.createElement(Box, {
          key: wallet.id,
          paddingX: "20"
        }, import_react72.default.createElement(Box, {
          width: "60"
        }, import_react72.default.createElement(WalletButton, {
          onClose,
          wallet
        })));
      }))), import_react72.default.createElement(Box, {
        background: "generalBorder",
        height: "1",
        marginBottom: "32",
        marginTop: "-1"
      }), import_react72.default.createElement(Box, {
        alignItems: "center",
        display: "flex",
        flexDirection: "column",
        gap: "32",
        paddingX: "32",
        style: { textAlign: "center" }
      }, import_react72.default.createElement(Box, {
        display: "flex",
        flexDirection: "column",
        gap: "8",
        textAlign: "center"
      }, import_react72.default.createElement(Text, {
        color: "modalText",
        size: "16",
        weight: "bold"
      }, i18n2.t("intro.title")), import_react72.default.createElement(Text, {
        color: "modalTextSecondary",
        size: "16"
      }, i18n2.t("intro.description")))), import_react72.default.createElement(Box, {
        paddingTop: "32",
        paddingX: "20"
      }, import_react72.default.createElement(Box, {
        display: "flex",
        gap: "14",
        justifyContent: "center"
      }, import_react72.default.createElement(ActionButton, {
        label: i18n2.t("intro.get.label"),
        onClick: () => setWalletStep(
          "GET"
          /* Get */
        ),
        size: "large",
        type: "secondary"
      }), import_react72.default.createElement(ActionButton, {
        href: learnMoreUrl,
        label: i18n2.t("intro.learn_more.label"),
        size: "large",
        type: "secondary"
      }))), Disclaimer && import_react72.default.createElement(Box, {
        marginTop: "28",
        marginX: "32",
        textAlign: "center"
      }, import_react72.default.createElement(Disclaimer, {
        Link: DisclaimerLink,
        Text: DisclaimerText
      })));
      break;
    }
    case "GET": {
      headerLabel = i18n2.t("get.title");
      headerBackButtonLink = "CONNECT";
      const mobileWallets = (_a = wallets == null ? void 0 : wallets.filter((wallet) => {
        var _a2, _b, _c;
        return ((_a2 = wallet.downloadUrls) == null ? void 0 : _a2.ios) || ((_b = wallet.downloadUrls) == null ? void 0 : _b.android) || ((_c = wallet.downloadUrls) == null ? void 0 : _c.mobile);
      })) == null ? void 0 : _a.splice(0, 3);
      walletContent = import_react72.default.createElement(Box, null, import_react72.default.createElement(Box, {
        alignItems: "center",
        display: "flex",
        flexDirection: "column",
        height: "full",
        marginBottom: "36",
        marginTop: "5",
        paddingTop: "12",
        width: "full"
      }, mobileWallets.map((wallet, index) => {
        const { downloadUrls, iconBackground, iconUrl, name } = wallet;
        if (!(downloadUrls == null ? void 0 : downloadUrls.ios) && !(downloadUrls == null ? void 0 : downloadUrls.android) && !(downloadUrls == null ? void 0 : downloadUrls.mobile)) {
          return null;
        }
        return import_react72.default.createElement(Box, {
          display: "flex",
          gap: "16",
          key: wallet.id,
          paddingX: "20",
          width: "full"
        }, import_react72.default.createElement(Box, {
          style: { minHeight: 48, minWidth: 48 }
        }, import_react72.default.createElement(AsyncImage, {
          background: iconBackground,
          borderColor: "generalBorder",
          borderRadius: "10",
          height: "48",
          src: iconUrl,
          width: "48"
        })), import_react72.default.createElement(Box, {
          display: "flex",
          flexDirection: "column",
          width: "full"
        }, import_react72.default.createElement(Box, {
          alignItems: "center",
          display: "flex",
          height: "48"
        }, import_react72.default.createElement(Box, {
          width: "full"
        }, import_react72.default.createElement(Text, {
          color: "modalText",
          size: "18",
          weight: "bold"
        }, name)), import_react72.default.createElement(ActionButton, {
          href: (ios ? downloadUrls == null ? void 0 : downloadUrls.ios : downloadUrls == null ? void 0 : downloadUrls.android) || (downloadUrls == null ? void 0 : downloadUrls.mobile),
          label: i18n2.t("get.action.label"),
          size: "small",
          type: "secondary"
        })), index < mobileWallets.length - 1 && import_react72.default.createElement(Box, {
          background: "generalBorderDim",
          height: "1",
          marginY: "10",
          width: "full"
        })));
      })), import_react72.default.createElement(Box, {
        style: { marginBottom: "42px" }
      }), import_react72.default.createElement(Box, {
        alignItems: "center",
        display: "flex",
        flexDirection: "column",
        gap: "36",
        paddingX: "36",
        style: { textAlign: "center" }
      }, import_react72.default.createElement(Box, {
        display: "flex",
        flexDirection: "column",
        gap: "12",
        textAlign: "center"
      }, import_react72.default.createElement(Text, {
        color: "modalText",
        size: "16",
        weight: "bold"
      }, i18n2.t("get.looking_for.title")), import_react72.default.createElement(Text, {
        color: "modalTextSecondary",
        size: "16"
      }, i18n2.t("get.looking_for.mobile.description")))));
      break;
    }
  }
  return import_react72.default.createElement(Box, {
    display: "flex",
    flexDirection: "column",
    paddingBottom: "36"
  }, import_react72.default.createElement(Box, {
    background: headerBackgroundContrast ? "profileForeground" : "modalBackground",
    display: "flex",
    flexDirection: "column",
    paddingBottom: "4",
    paddingTop: "14"
  }, import_react72.default.createElement(Box, {
    display: "flex",
    justifyContent: "center",
    paddingBottom: "6",
    paddingX: "20",
    position: "relative"
  }, headerBackButtonLink && import_react72.default.createElement(Box, {
    display: "flex",
    position: "absolute",
    style: {
      left: 0,
      marginBottom: -20,
      marginTop: -20
    }
  }, import_react72.default.createElement(Box, {
    alignItems: "center",
    as: "button",
    className: touchableStyles({
      active: "shrinkSm",
      hover: "growLg"
    }),
    color: "accentColor",
    display: "flex",
    marginLeft: "4",
    marginTop: "20",
    onClick: () => setWalletStep(headerBackButtonLink),
    padding: "16",
    style: { height: 17, willChange: "transform" },
    transition: "default",
    type: "button"
  }, import_react72.default.createElement(BackIcon, null))), import_react72.default.createElement(Box, {
    marginTop: "4",
    textAlign: "center",
    width: "full"
  }, import_react72.default.createElement(Text, {
    as: "h1",
    color: "modalText",
    id: titleId,
    size: "20",
    weight: "bold"
  }, headerLabel)), import_react72.default.createElement(Box, {
    alignItems: "center",
    display: "flex",
    height: "32",
    paddingRight: "14",
    position: "absolute",
    right: "0"
  }, import_react72.default.createElement(Box, {
    style: { marginBottom: -20, marginTop: -20 }
  }, import_react72.default.createElement(CloseButton, {
    onClose
  }))))), import_react72.default.createElement(Box, {
    display: "flex",
    flexDirection: "column"
  }, walletContent));
}
var MobileStatus = ({ onClose }) => {
  const { connector } = (0, import_react73.useContext)(WalletButtonContext);
  const { i18n: i18n2 } = (0, import_react73.useContext)(I18nContext);
  const connectorName = (connector == null ? void 0 : connector.name) || "";
  return import_react73.default.createElement(Box, null, import_react73.default.createElement(Box, {
    display: "flex",
    paddingBottom: "32",
    justifyContent: "center",
    alignItems: "center",
    background: "profileForeground",
    flexDirection: "column"
  }, import_react73.default.createElement(Box, {
    width: "full",
    display: "flex",
    justifyContent: "flex-end",
    marginTop: "18",
    marginRight: "24"
  }, import_react73.default.createElement(CloseButton, {
    onClose
  })), import_react73.default.createElement(Box, {
    width: "60"
  }, import_react73.default.createElement(WalletButton, {
    onClose,
    wallet: connector,
    connecting: true
  })), import_react73.default.createElement(Box, {
    marginTop: "20"
  }, import_react73.default.createElement(Text, {
    textAlign: "center",
    color: "modalText",
    size: "18",
    weight: "semibold"
  }, i18n2.t("connect.status.connect_mobile", {
    wallet: connectorName
  }))), import_react73.default.createElement(Box, {
    maxWidth: "full",
    marginTop: "8"
  }, import_react73.default.createElement(Text, {
    textAlign: "center",
    color: "modalText",
    size: "16",
    weight: "medium"
  }, i18n2.t("connect.status.confirm_mobile", {
    wallet: connectorName
  })))));
};
function ConnectOptions({ onClose }) {
  const { connector } = (0, import_react56.useContext)(WalletButtonContext);
  return isMobile() ? connector ? import_react56.default.createElement(MobileStatus, {
    onClose
  }) : import_react56.default.createElement(MobileOptions, {
    onClose
  }) : import_react56.default.createElement(DesktopOptions, {
    onClose
  });
}
function ConnectModal({ onClose, open }) {
  const titleId = "rk_connect_title";
  const connectionStatus = useConnectionStatus();
  const { disconnect } = useDisconnect();
  const { isConnecting } = useAccount();
  const onAuthCancel = import_react55.default.useCallback(() => {
    onClose();
    disconnect();
  }, [onClose, disconnect]);
  const onConnectModalCancel = import_react55.default.useCallback(() => {
    if (isConnecting)
      disconnect();
    onClose();
  }, [onClose, disconnect, isConnecting]);
  if (connectionStatus === "disconnected") {
    return import_react55.default.createElement(Dialog, {
      onClose: onConnectModalCancel,
      open,
      titleId
    }, import_react55.default.createElement(DialogContent, {
      bottomSheetOnMobile: true,
      padding: "0",
      wide: true
    }, import_react55.default.createElement(ConnectOptions, {
      onClose: onConnectModalCancel
    })));
  }
  if (connectionStatus === "unauthenticated") {
    return import_react55.default.createElement(Dialog, {
      onClose: onAuthCancel,
      open,
      titleId
    }, import_react55.default.createElement(DialogContent, {
      bottomSheetOnMobile: true,
      padding: "0"
    }, import_react55.default.createElement(SignIn, {
      onClose: onAuthCancel,
      onCloseModal: onClose
    })));
  }
  return null;
}
function useModalStateValue() {
  const [isModalOpen, setModalOpen] = (0, import_react20.useState)(false);
  return {
    closeModal: (0, import_react20.useCallback)(() => setModalOpen(false), []),
    isModalOpen,
    openModal: (0, import_react20.useCallback)(() => setModalOpen(true), [])
  };
}
var ModalContext = (0, import_react20.createContext)({
  accountModalOpen: false,
  chainModalOpen: false,
  connectModalOpen: false
});
function ModalProvider({ children }) {
  const {
    closeModal: closeConnectModal,
    isModalOpen: connectModalOpen,
    openModal: openConnectModal
  } = useModalStateValue();
  const {
    closeModal: closeAccountModal,
    isModalOpen: accountModalOpen,
    openModal: openAccountModal
  } = useModalStateValue();
  const {
    closeModal: closeChainModal,
    isModalOpen: chainModalOpen,
    openModal: openChainModal
  } = useModalStateValue();
  const connectionStatus = useConnectionStatus();
  const { chain } = useNetwork();
  const chainSupported = !(chain == null ? void 0 : chain.unsupported);
  function closeModals({
    keepConnectModalOpen = false
  } = {}) {
    if (!keepConnectModalOpen) {
      closeConnectModal();
    }
    closeAccountModal();
    closeChainModal();
  }
  const isUnauthenticated = useAuthenticationStatus() === "unauthenticated";
  useAccount({
    onConnect: () => closeModals({ keepConnectModalOpen: isUnauthenticated }),
    onDisconnect: () => closeModals()
  });
  return import_react20.default.createElement(ModalContext.Provider, {
    value: (0, import_react20.useMemo)(() => ({
      accountModalOpen,
      chainModalOpen,
      connectModalOpen,
      openAccountModal: chainSupported && connectionStatus === "connected" ? openAccountModal : void 0,
      openChainModal: connectionStatus === "connected" ? openChainModal : void 0,
      openConnectModal: connectionStatus === "disconnected" || connectionStatus === "unauthenticated" ? openConnectModal : void 0
    }), [
      connectionStatus,
      chainSupported,
      accountModalOpen,
      chainModalOpen,
      connectModalOpen,
      openAccountModal,
      openChainModal,
      openConnectModal
    ])
  }, children, import_react20.default.createElement(ConnectModal, {
    onClose: closeConnectModal,
    open: connectModalOpen
  }), import_react20.default.createElement(AccountModal, {
    onClose: closeAccountModal,
    open: accountModalOpen
  }), import_react20.default.createElement(ChainModal, {
    onClose: closeChainModal,
    open: chainModalOpen
  }));
}
function useModalState() {
  const { accountModalOpen, chainModalOpen, connectModalOpen } = (0, import_react20.useContext)(ModalContext);
  return {
    accountModalOpen,
    chainModalOpen,
    connectModalOpen
  };
}
function useAccountModal() {
  const { accountModalOpen, openAccountModal } = (0, import_react20.useContext)(ModalContext);
  return { accountModalOpen, openAccountModal };
}
function useChainModal() {
  const { chainModalOpen, openChainModal } = (0, import_react20.useContext)(ModalContext);
  return { chainModalOpen, openChainModal };
}
function useConnectModal() {
  const { connectModalOpen, openConnectModal } = (0, import_react20.useContext)(ModalContext);
  return { connectModalOpen, openConnectModal };
}
var noop = () => {
};
function ConnectButtonRenderer({
  children
}) {
  var _a, _b, _c, _d;
  const isMounted = useIsMounted();
  const { address } = useAccount();
  const ensName = useMainnetEnsName(address);
  const ensAvatar = useMainnetEnsAvatar(ensName);
  const rainbowkitChainsById = useRainbowKitChainsById();
  const authenticationStatus = (_a = useAuthenticationStatus()) != null ? _a : void 0;
  const { chain: activeChain } = useNetwork();
  const rainbowKitChain = activeChain ? rainbowkitChainsById[activeChain.id] : void 0;
  const chainName = (_b = rainbowKitChain == null ? void 0 : rainbowKitChain.name) != null ? _b : void 0;
  const chainIconUrl = (_c = rainbowKitChain == null ? void 0 : rainbowKitChain.iconUrl) != null ? _c : void 0;
  const chainIconBackground = (_d = rainbowKitChain == null ? void 0 : rainbowKitChain.iconBackground) != null ? _d : void 0;
  const resolvedChainIconUrl = useAsyncImage(chainIconUrl);
  const showRecentTransactions = (0, import_react16.useContext)(ShowRecentTransactionsContext);
  const hasPendingTransactions = useRecentTransactions().some(({ status }) => status === "pending") && showRecentTransactions;
  const { showBalance } = useShowBalance();
  const shouldShowBalance = showBalance ? normalizeResponsiveValue(showBalance)[isMobile() ? "smallScreen" : "largeScreen"] : true;
  const { data: balanceData } = useBalance({
    address: shouldShowBalance ? address : void 0
  });
  const displayBalance = balanceData ? `${abbreviateETHBalance(parseFloat(balanceData.formatted))} ${balanceData.symbol}` : void 0;
  const { openConnectModal } = useConnectModal();
  const { openChainModal } = useChainModal();
  const { openAccountModal } = useAccountModal();
  const { accountModalOpen, chainModalOpen, connectModalOpen } = useModalState();
  return import_react16.default.createElement(import_react16.default.Fragment, null, children({
    account: address ? {
      address,
      balanceDecimals: balanceData == null ? void 0 : balanceData.decimals,
      balanceFormatted: balanceData == null ? void 0 : balanceData.formatted,
      balanceSymbol: balanceData == null ? void 0 : balanceData.symbol,
      displayBalance,
      displayName: ensName ? formatENS(ensName) : formatAddress(address),
      ensAvatar: ensAvatar != null ? ensAvatar : void 0,
      ensName: ensName != null ? ensName : void 0,
      hasPendingTransactions
    } : void 0,
    accountModalOpen,
    authenticationStatus,
    chain: activeChain ? {
      hasIcon: Boolean(chainIconUrl),
      iconBackground: chainIconBackground,
      iconUrl: resolvedChainIconUrl,
      id: activeChain.id,
      name: chainName != null ? chainName : activeChain.name,
      unsupported: activeChain.unsupported
    } : void 0,
    chainModalOpen,
    connectModalOpen,
    mounted: isMounted(),
    openAccountModal: openAccountModal != null ? openAccountModal : noop,
    openChainModal: openChainModal != null ? openChainModal : noop,
    openConnectModal: openConnectModal != null ? openConnectModal : noop
  }));
}
ConnectButtonRenderer.displayName = "ConnectButton.Custom";
var defaultProps = {
  accountStatus: "full",
  chainStatus: { largeScreen: "full", smallScreen: "icon" },
  label: "Connect Wallet",
  showBalance: { largeScreen: true, smallScreen: false }
};
function ConnectButton({
  accountStatus = defaultProps.accountStatus,
  chainStatus = defaultProps.chainStatus,
  label = defaultProps.label,
  showBalance = defaultProps.showBalance
}) {
  const chains = useRainbowKitChains();
  const connectionStatus = useConnectionStatus();
  const { setShowBalance } = useShowBalance();
  const [ready, setReady] = (0, import_react4.useState)(false);
  const { i18n: i18n2 } = (0, import_react4.useContext)(I18nContext);
  (0, import_react4.useEffect)(() => {
    setShowBalance(showBalance);
    if (!ready)
      setReady(true);
  }, [showBalance, setShowBalance]);
  return ready ? import_react4.default.createElement(ConnectButtonRenderer, null, ({
    account,
    chain,
    mounted,
    openAccountModal,
    openChainModal,
    openConnectModal
  }) => {
    var _a, _b, _c;
    const ready2 = mounted && connectionStatus !== "loading";
    const unsupportedChain = (_a = chain == null ? void 0 : chain.unsupported) != null ? _a : false;
    return import_react4.default.createElement(Box, {
      display: "flex",
      gap: "12",
      ...!ready2 && {
        "aria-hidden": true,
        style: {
          opacity: 0,
          pointerEvents: "none",
          userSelect: "none"
        }
      }
    }, ready2 && account && connectionStatus === "connected" ? import_react4.default.createElement(import_react4.default.Fragment, null, chain && (chains.length > 1 || unsupportedChain) && import_react4.default.createElement(Box, {
      alignItems: "center",
      "aria-label": "Chain Selector",
      as: "button",
      background: unsupportedChain ? "connectButtonBackgroundError" : "connectButtonBackground",
      borderRadius: "connectButton",
      boxShadow: "connectButton",
      className: touchableStyles({
        active: "shrink",
        hover: "grow"
      }),
      color: unsupportedChain ? "connectButtonTextError" : "connectButtonText",
      display: mapResponsiveValue(chainStatus, (value) => value === "none" ? "none" : "flex"),
      fontFamily: "body",
      fontWeight: "bold",
      gap: "6",
      key: unsupportedChain ? "unsupported" : "supported",
      onClick: openChainModal,
      paddingX: "10",
      paddingY: "8",
      testId: unsupportedChain ? "wrong-network-button" : "chain-button",
      transition: "default",
      type: "button"
    }, unsupportedChain ? import_react4.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      height: "24",
      paddingX: "4"
    }, i18n2.t("connect_wallet.wrong_network.label")) : import_react4.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      gap: "6"
    }, chain.hasIcon ? import_react4.default.createElement(Box, {
      display: mapResponsiveValue(chainStatus, (value) => value === "full" || value === "icon" ? "block" : "none"),
      height: "24",
      width: "24"
    }, import_react4.default.createElement(AsyncImage, {
      alt: (_b = chain.name) != null ? _b : "Chain icon",
      background: chain.iconBackground,
      borderRadius: "full",
      height: "24",
      src: chain.iconUrl,
      width: "24"
    })) : null, import_react4.default.createElement(Box, {
      display: mapResponsiveValue(chainStatus, (value) => {
        if (value === "icon" && !chain.iconUrl) {
          return "block";
        }
        return value === "full" || value === "name" ? "block" : "none";
      })
    }, (_c = chain.name) != null ? _c : chain.id)), import_react4.default.createElement(DropdownIcon, null)), !unsupportedChain && import_react4.default.createElement(Box, {
      alignItems: "center",
      as: "button",
      background: "connectButtonBackground",
      borderRadius: "connectButton",
      boxShadow: "connectButton",
      className: touchableStyles({
        active: "shrink",
        hover: "grow"
      }),
      color: "connectButtonText",
      display: "flex",
      fontFamily: "body",
      fontWeight: "bold",
      onClick: openAccountModal,
      testId: "account-button",
      transition: "default",
      type: "button"
    }, account.displayBalance && import_react4.default.createElement(Box, {
      display: mapResponsiveValue(showBalance, (value) => value ? "block" : "none"),
      padding: "8",
      paddingLeft: "12"
    }, account.displayBalance), import_react4.default.createElement(Box, {
      background: normalizeResponsiveValue(showBalance)[isMobile() ? "smallScreen" : "largeScreen"] ? "connectButtonInnerBackground" : "connectButtonBackground",
      borderColor: "connectButtonBackground",
      borderRadius: "connectButton",
      borderStyle: "solid",
      borderWidth: "2",
      color: "connectButtonText",
      fontFamily: "body",
      fontWeight: "bold",
      paddingX: "8",
      paddingY: "6",
      transition: "default"
    }, import_react4.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      gap: "6",
      height: "24"
    }, import_react4.default.createElement(Box, {
      display: mapResponsiveValue(accountStatus, (value) => value === "full" || value === "avatar" ? "block" : "none")
    }, import_react4.default.createElement(Avatar, {
      address: account.address,
      imageUrl: account.ensAvatar,
      loading: account.hasPendingTransactions,
      size: 24
    })), import_react4.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      gap: "6"
    }, import_react4.default.createElement(Box, {
      display: mapResponsiveValue(accountStatus, (value) => value === "full" || value === "address" ? "block" : "none")
    }, account.displayName), import_react4.default.createElement(DropdownIcon, null)))))) : import_react4.default.createElement(Box, {
      as: "button",
      background: "accentColor",
      borderRadius: "connectButton",
      boxShadow: "connectButton",
      className: touchableStyles({
        active: "shrink",
        hover: "grow"
      }),
      color: "accentColorForeground",
      fontFamily: "body",
      fontWeight: "bold",
      height: "40",
      key: "connect",
      onClick: openConnectModal,
      paddingX: "14",
      testId: "connect-button",
      transition: "default",
      type: "button"
    }, mounted && label === "Connect Wallet" ? i18n2.t("connect_wallet.label") : label));
  }) : import_react4.default.createElement(import_react4.default.Fragment, null);
}
ConnectButton.__defaultProps = defaultProps;
ConnectButton.Custom = ConnectButtonRenderer;
var border = "_1y2lnfi0";
var maxWidth = "_1y2lnfi1";
function WalletButtonRenderer({
  wallet = "rainbow",
  children
}) {
  const isMounted = useIsMounted();
  const { openConnectModal } = useConnectModal();
  const { connectModalOpen } = useModalState();
  const { connector, setConnector } = (0, import_react75.useContext)(WalletButtonContext);
  const [firstConnector] = useWalletConnectors().filter((_wallet) => _wallet.id.toLowerCase() === wallet.toLowerCase()).sort((a, b) => a.groupIndex - b.groupIndex);
  if (!firstConnector) {
    throw new Error("Connector not found");
  }
  const connectionStatus = useConnectionStatus();
  const [loading, setLoading] = (0, import_react75.useState)(false);
  const [isError, setIsError] = (0, import_react75.useState)(false);
  const mobile = isMobile();
  (0, import_react75.useEffect)(() => {
    if (!connectModalOpen && connector)
      setConnector(null);
  }, [connectModalOpen, connector, setConnector]);
  const { isConnected, isConnecting } = useAccount({
    onConnect: () => {
      if (isError)
        setIsError(false);
    },
    onDisconnect: clearLatestWalletId
  });
  const isLastWalletIdConnected = (0, import_react75.useMemo)(() => {
    const lastWalletId = getLatestWalletId();
    if (!lastWalletId || !(firstConnector == null ? void 0 : firstConnector.id)) {
      return false;
    }
    if (!isConnected)
      return false;
    return lastWalletId === (firstConnector == null ? void 0 : firstConnector.id);
  }, [isConnected, firstConnector]);
  const connectWallet = async () => {
    var _a;
    try {
      setLoading(true);
      if (isError)
        setIsError(false);
      await ((_a = firstConnector == null ? void 0 : firstConnector.connect) == null ? void 0 : _a.call(firstConnector));
    } catch {
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };
  const isStatusLoading = connectionStatus === "loading";
  const ready = !isConnecting && !!openConnectModal && firstConnector && !isStatusLoading;
  const isNotSupported = !(firstConnector == null ? void 0 : firstConnector.installed) || !(firstConnector == null ? void 0 : firstConnector.ready);
  return import_react75.default.createElement(import_react75.default.Fragment, null, children({
    error: isError,
    loading,
    connected: isLastWalletIdConnected,
    ready,
    mounted: isMounted(),
    connector: firstConnector,
    connect: async () => {
      addLatestWalletId((firstConnector == null ? void 0 : firstConnector.id) || "");
      if (mobile || isNotSupported) {
        openConnectModal == null ? void 0 : openConnectModal();
        setConnector(firstConnector);
        return;
      }
      connectWallet();
    }
  }));
}
var WalletButton2 = ({ wallet }) => {
  return import_react74.default.createElement(WalletButtonRenderer, {
    wallet
  }, ({ ready, connect, connected, mounted, connector, loading }) => {
    const isDisabled = !ready || loading;
    const { i18n: i18n2 } = (0, import_react74.useContext)(I18nContext);
    const connectorName = (connector == null ? void 0 : connector.name) || "";
    if (!mounted)
      return;
    return import_react74.default.createElement(Box, {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      disabled: isDisabled,
      pointerEvents: isDisabled ? "none" : "all"
    }, import_react74.default.createElement(Box, {
      as: "button",
      borderRadius: "menuButton",
      borderStyle: "solid",
      borderWidth: "1",
      className: [
        maxWidth,
        border,
        touchableStyles({
          active: "shrink",
          hover: "grow"
        })
      ],
      minHeight: "44",
      onClick: connect,
      disabled: !ready || loading,
      padding: "6",
      style: { willChange: "transform" },
      testId: `wallet-button-${(connector == null ? void 0 : connector.id) || ""}`,
      transition: "default",
      width: "full",
      background: "connectButtonBackground"
    }, import_react74.default.createElement(Box, {
      color: "modalText",
      fontFamily: "body",
      fontSize: "16",
      fontWeight: "bold",
      transition: "default",
      display: "flex",
      alignItems: "center"
    }, import_react74.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
      gap: "12",
      paddingRight: "6"
    }, import_react74.default.createElement(Box, null, loading ? import_react74.default.createElement(SpinnerIcon, null) : import_react74.default.createElement(AsyncImage, {
      background: connector == null ? void 0 : connector.iconBackground,
      borderRadius: "6",
      height: "28",
      src: connector == null ? void 0 : connector.iconUrl,
      width: "28"
    })), import_react74.default.createElement(Box, {
      alignItems: "center",
      display: "flex",
      flexDirection: "column",
      color: "modalText"
    }, import_react74.default.createElement(Box, {
      testId: `wallet-button-label-${(connector == null ? void 0 : connector.id) || ""}`
    }, loading ? i18n2.t("connect.status.connecting", {
      wallet: connectorName
    }) : connectorName)), connected ? import_react74.default.createElement(Box, {
      background: "connectionIndicator",
      borderColor: "selectedOptionBorder",
      borderRadius: "full",
      borderStyle: "solid",
      borderWidth: "1",
      height: "8",
      width: "8"
    }) : null))));
  });
};
WalletButton2.Custom = WalletButtonRenderer;
function omitUndefinedValues(obj) {
  return Object.fromEntries(Object.entries(obj).filter(([_key, value]) => value !== void 0));
}
function connectorsForWallets(walletList) {
  if ("groupName" in walletList[0]) {
    return _connectorsForWallets(walletList);
  }
  return _connectorsForWallets([
    {
      groupName: "",
      wallets: walletList
    }
  ])();
}
var _connectorsForWallets = (walletList) => {
  return () => {
    let index = -1;
    const connectors = [];
    const visibleWallets = [];
    const potentiallyHiddenWallets = [];
    const walletInstances = [];
    walletList.forEach(({ groupName, wallets }, groupIndex) => {
      wallets.forEach((wallet) => {
        index++;
        if ((wallet == null ? void 0 : wallet.iconAccent) && !isHexString(wallet == null ? void 0 : wallet.iconAccent)) {
          throw new Error(`Property \`iconAccent\` is not a hex value for wallet: ${wallet.name}`);
        }
        const walletListItem = {
          ...wallet,
          groupIndex,
          groupName,
          index
        };
        if (typeof wallet.hidden === "function") {
          potentiallyHiddenWallets.push(walletListItem);
        } else {
          visibleWallets.push(walletListItem);
        }
      });
    });
    const walletListItems = [
      ...visibleWallets,
      ...potentiallyHiddenWallets
    ];
    walletListItems.forEach(({
      createConnector: createConnector2,
      groupIndex,
      groupName,
      hidden,
      index: index2,
      ...walletMeta
    }) => {
      if (typeof hidden === "function") {
        const isHidden = hidden({
          wallets: [
            ...walletInstances.map(({ connector: connector2, id, installed, name }) => ({
              connector: connector2,
              id,
              installed,
              name
            }))
          ]
        });
        if (isHidden) {
          return;
        }
      }
      const { connector, ...connectionMethods } = omitUndefinedValues(createConnector2());
      let walletConnectModalConnector;
      if (walletMeta.id === "walletConnect" && connectionMethods.qrCode && !isMobile()) {
        const { chains, options } = connector;
        walletConnectModalConnector = new WalletConnectConnector({
          chains,
          options: {
            ...options,
            showQrModal: true
          }
        });
        connectors.push(walletConnectModalConnector);
      }
      const walletInstance = {
        connector,
        groupIndex,
        groupName,
        index: index2,
        walletConnectModalConnector,
        ...walletMeta,
        ...connectionMethods
      };
      walletInstances.push(walletInstance);
      if (!connectors.includes(connector)) {
        connectors.push(connector);
        connector._wallets = [];
      }
      connector._wallets.push(walletInstance);
    });
    return connectors;
  };
};
var braveWallet = ({
  chains,
  ...options
}) => {
  var _a;
  return {
    id: "brave",
    name: "Brave Wallet",
    iconUrl: async () => (await import("./braveWallet-BTBH4MDN-CY77K7CO.js")).default,
    iconBackground: "#fff",
    installed: typeof window !== "undefined" && ((_a = window.ethereum) == null ? void 0 : _a.isBraveWallet) === true,
    downloadUrls: {},
    createConnector: () => ({
      connector: new InjectedConnector({
        chains,
        options
      })
    })
  };
};
function getExplicitInjectedProvider(flag) {
  if (typeof window === "undefined" || typeof window.ethereum === "undefined")
    return;
  const providers = window.ethereum.providers;
  return providers ? providers.find((provider) => provider[flag]) : window.ethereum[flag] ? window.ethereum : void 0;
}
function hasInjectedProvider(flag) {
  return Boolean(getExplicitInjectedProvider(flag));
}
function getInjectedProvider(flag) {
  if (typeof window === "undefined" || typeof window.ethereum === "undefined")
    return;
  const providers = window.ethereum.providers;
  const provider = getExplicitInjectedProvider(flag);
  if (provider)
    return provider;
  if (typeof providers !== "undefined" && providers.length > 0)
    return providers[0];
  return window.ethereum;
}
function getInjectedConnector({
  chains,
  flag,
  options
}) {
  return new InjectedConnector({
    chains,
    options: {
      getProvider: () => getInjectedProvider(flag),
      ...options
    }
  });
}
var coinbaseWallet = ({
  appName,
  chains,
  ...options
}) => {
  const isCoinbaseWalletInjected = hasInjectedProvider("isCoinbaseWallet");
  return {
    id: "coinbase",
    name: "Coinbase Wallet",
    shortName: "Coinbase",
    iconUrl: async () => (await import("./coinbaseWallet-2OUR5TUP-M6AL4MQR.js")).default,
    iconAccent: "#2c5ff6",
    iconBackground: "#2c5ff6",
    installed: isCoinbaseWalletInjected || void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=org.toshi",
      ios: "https://apps.apple.com/us/app/coinbase-wallet-store-crypto/id1278383455",
      mobile: "https://coinbase.com/wallet/downloads",
      qrCode: "https://coinbase-wallet.onelink.me/q5Sx/fdb9b250",
      chrome: "https://chrome.google.com/webstore/detail/coinbase-wallet-extension/hnfanknocfeofbddgcijnmhnfnkdnaad",
      browserExtension: "https://coinbase.com/wallet"
    },
    createConnector: () => {
      const ios = isIOS();
      const connector = new CoinbaseWalletConnector({
        chains,
        options: {
          appName,
          headlessMode: true,
          ...options
        }
      });
      const getUri = async () => (await connector.getProvider()).qrUrl;
      return {
        connector,
        ...ios ? {} : {
          qrCode: {
            getUri,
            instructions: {
              learnMoreUrl: "https://coinbase.com/wallet/articles/getting-started-mobile",
              steps: [
                {
                  description: "wallet_connectors.coinbase.qr_code.step1.description",
                  step: "install",
                  title: "wallet_connectors.coinbase.qr_code.step1.title"
                },
                {
                  description: "wallet_connectors.coinbase.qr_code.step2.description",
                  step: "create",
                  title: "wallet_connectors.coinbase.qr_code.step2.title"
                },
                {
                  description: "wallet_connectors.coinbase.qr_code.step3.description",
                  step: "scan",
                  title: "wallet_connectors.coinbase.qr_code.step3.title"
                }
              ]
            }
          },
          extension: {
            instructions: {
              learnMoreUrl: "https://coinbase.com/wallet/articles/getting-started-extension",
              steps: [
                {
                  description: "wallet_connectors.coinbase.extension.step1.description",
                  step: "install",
                  title: "wallet_connectors.coinbase.extension.step1.title"
                },
                {
                  description: "wallet_connectors.coinbase.extension.step2.description",
                  step: "create",
                  title: "wallet_connectors.coinbase.extension.step2.title"
                },
                {
                  description: "wallet_connectors.coinbase.extension.step3.description",
                  step: "refresh",
                  title: "wallet_connectors.coinbase.extension.step3.title"
                }
              ]
            }
          }
        }
      };
    }
  };
};
var injectedWallet = ({
  chains,
  ...options
}) => ({
  id: "injected",
  name: "Browser Wallet",
  iconUrl: async () => (await import("./injectedWallet-EUKDEAIU-OCQPWFOC.js")).default,
  iconBackground: "#fff",
  hidden: ({ wallets }) => wallets.some((wallet) => wallet.installed && wallet.name === wallet.connector.name && (wallet.connector instanceof InjectedConnector || wallet.id === "coinbase")),
  createConnector: () => ({
    connector: new InjectedConnector({
      chains,
      options
    })
  })
});
async function getWalletConnectUri(connector, version) {
  const provider = await connector.getProvider();
  return version === "2" ? new Promise((resolve) => provider.once("display_uri", resolve)) : provider.connector.uri;
}
var sharedConnectors = /* @__PURE__ */ new Map();
function createConnector(version, config) {
  const connector = version === "1" ? new WalletConnectLegacyConnector(config) : new WalletConnectConnector(config);
  sharedConnectors.set(JSON.stringify(config), connector);
  return connector;
}
function getWalletConnectConnector({
  chains,
  options = {},
  projectId,
  version = "2"
}) {
  const exampleProjectId = "21fef48091f12692cad574a6f7753643";
  if (version === "2") {
    if (!projectId || projectId === "")
      throw new Error("No projectId found. Every dApp must now provide a WalletConnect Cloud projectId to enable WalletConnect v2 https://www.rainbowkit.com/docs/installation#configure");
    if (projectId === "YOUR_PROJECT_ID" || projectId === exampleProjectId)
      console.warn("Invalid projectId. Please create a unique WalletConnect Cloud projectId for your dApp https://www.rainbowkit.com/docs/installation#configure");
  }
  const config = {
    chains,
    options: version === "1" ? {
      qrcode: false,
      ...options
    } : {
      projectId: projectId === "YOUR_PROJECT_ID" ? exampleProjectId : projectId,
      showQrModal: false,
      ...options
    }
  };
  const serializedConfig = JSON.stringify(config);
  const sharedConnector = sharedConnectors.get(serializedConfig);
  return sharedConnector != null ? sharedConnector : createConnector(version, config);
}
function isMetaMask(ethereum) {
  if (!(ethereum == null ? void 0 : ethereum.isMetaMask))
    return false;
  if (ethereum.isBraveWallet && !ethereum._events && !ethereum._state)
    return false;
  if (ethereum.isApexWallet)
    return false;
  if (ethereum.isAvalanche)
    return false;
  if (ethereum.isBackpack)
    return false;
  if (ethereum.isBifrost)
    return false;
  if (ethereum.isBitKeep)
    return false;
  if (ethereum.isBitski)
    return false;
  if (ethereum.isBlockWallet)
    return false;
  if (ethereum.isCoinbaseWallet)
    return false;
  if (ethereum.isDawn)
    return false;
  if (ethereum.isEnkrypt)
    return false;
  if (ethereum.isExodus)
    return false;
  if (ethereum.isFrame)
    return false;
  if (ethereum.isFrontier)
    return false;
  if (ethereum.isGamestop)
    return false;
  if (ethereum.isHyperPay)
    return false;
  if (ethereum.isImToken)
    return false;
  if (ethereum.isKuCoinWallet)
    return false;
  if (ethereum.isMathWallet)
    return false;
  if (ethereum.isOkxWallet || ethereum.isOKExWallet)
    return false;
  if (ethereum.isOneInchIOSWallet || ethereum.isOneInchAndroidWallet)
    return false;
  if (ethereum.isOpera)
    return false;
  if (ethereum.isPhantom)
    return false;
  if (ethereum.isPortal)
    return false;
  if (ethereum.isRabby)
    return false;
  if (ethereum.isRainbow)
    return false;
  if (ethereum.isStatus)
    return false;
  if (ethereum.isTalisman)
    return false;
  if (ethereum.isTally)
    return false;
  if (ethereum.isTokenPocket)
    return false;
  if (ethereum.isTokenary)
    return false;
  if (ethereum.isTrust || ethereum.isTrustWallet)
    return false;
  if (ethereum.isXDEFI)
    return false;
  if (ethereum.isZeal)
    return false;
  if (ethereum.isZerion)
    return false;
  return true;
}
var metaMaskWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  var _a, _b;
  const providers = typeof window !== "undefined" && ((_a = window.ethereum) == null ? void 0 : _a.providers);
  const isMetaMaskInjected = typeof window !== "undefined" && typeof window.ethereum !== "undefined" && (((_b = window.ethereum.providers) == null ? void 0 : _b.some(isMetaMask)) || window.ethereum.isMetaMask);
  const shouldUseWalletConnect = !isMetaMaskInjected;
  return {
    id: "metaMask",
    name: "MetaMask",
    iconUrl: async () => (await import("./metaMaskWallet-ORHUNQRP-3JCALJUW.js")).default,
    iconAccent: "#f6851a",
    iconBackground: "#fff",
    installed: !shouldUseWalletConnect ? isMetaMaskInjected : void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=io.metamask",
      ios: "https://apps.apple.com/us/app/metamask/id1438144202",
      mobile: "https://metamask.io/download",
      qrCode: "https://metamask.io/download",
      chrome: "https://chrome.google.com/webstore/detail/metamask/nkbihfbeogaeaoehlefnkodbefgpgknn",
      edge: "https://microsoftedge.microsoft.com/addons/detail/metamask/ejbalbakoplchlghecdalmeeeajnimhm",
      firefox: "https://addons.mozilla.org/firefox/addon/ether-metamask",
      opera: "https://addons.opera.com/extensions/details/metamask-10",
      browserExtension: "https://metamask.io/download"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : new MetaMaskConnector({
        chains,
        options: {
          getProvider: () => Array.isArray(providers) && providers.find(isMetaMask) || (typeof window !== "undefined" ? window.ethereum : void 0),
          ...options
        }
      });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return isAndroid() ? uri : isIOS() ? `metamask://wc?uri=${encodeURIComponent(uri)}` : `https://metamask.app.link/wc?uri=${encodeURIComponent(uri)}`;
      };
      return {
        connector,
        mobile: {
          getUri: shouldUseWalletConnect ? getUri : void 0
        },
        qrCode: shouldUseWalletConnect ? {
          getUri,
          instructions: {
            learnMoreUrl: "https://metamask.io/faqs/",
            steps: [
              {
                description: "wallet_connectors.metamask.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.metamask.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.metamask.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.metamask.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.metamask.qr_code.step3.description",
                step: "refresh",
                title: "wallet_connectors.metamask.qr_code.step3.title"
              }
            ]
          }
        } : void 0,
        extension: {
          instructions: {
            learnMoreUrl: "https://metamask.io/faqs/",
            steps: [
              {
                description: "wallet_connectors.metamask.extension.step1.description",
                step: "install",
                title: "wallet_connectors.metamask.extension.step1.title"
              },
              {
                description: "wallet_connectors.metamask.extension.step2.description",
                step: "create",
                title: "wallet_connectors.metamask.extension.step2.title"
              },
              {
                description: "wallet_connectors.metamask.extension.step3.description",
                step: "refresh",
                title: "wallet_connectors.metamask.extension.step3.title"
              }
            ]
          }
        }
      };
    }
  };
};
var rainbowWallet = ({
  chains,
  projectId,
  walletConnectOptions,
  walletConnectVersion = "2",
  ...options
}) => {
  const isRainbowInjected = hasInjectedProvider("isRainbow");
  const shouldUseWalletConnect = !isRainbowInjected;
  return {
    id: "rainbow",
    name: "Rainbow",
    iconUrl: async () => (await import("./rainbowWallet-GGU64QEI-YZJAHWRJ.js")).default,
    iconBackground: "#0c2f78",
    installed: !shouldUseWalletConnect ? isRainbowInjected : void 0,
    downloadUrls: {
      android: "https://play.google.com/store/apps/details?id=me.rainbow&referrer=utm_source%3Drainbowkit&utm_source=rainbowkit",
      ios: "https://apps.apple.com/app/apple-store/id1457119021?pt=119997837&ct=rainbowkit&mt=8",
      mobile: "https://rainbow.download?utm_source=rainbowkit",
      qrCode: "https://rainbow.download?utm_source=rainbowkit&utm_medium=qrcode",
      browserExtension: "https://rainbow.me/extension?utm_source=rainbowkit"
    },
    createConnector: () => {
      const connector = shouldUseWalletConnect ? getWalletConnectConnector({
        projectId,
        chains,
        version: walletConnectVersion,
        options: walletConnectOptions
      }) : getInjectedConnector({ flag: "isRainbow", chains, options });
      const getUri = async () => {
        const uri = await getWalletConnectUri(connector, walletConnectVersion);
        return isAndroid() ? uri : isIOS() ? `rainbow://wc?uri=${encodeURIComponent(uri)}&connector=rainbowkit` : `https://rnbwapp.com/wc?uri=${encodeURIComponent(uri)}&connector=rainbowkit`;
      };
      return {
        connector,
        mobile: { getUri: shouldUseWalletConnect ? getUri : void 0 },
        qrCode: shouldUseWalletConnect ? {
          getUri,
          instructions: {
            learnMoreUrl: "https://learn.rainbow.me/connect-to-a-website-or-app?utm_source=rainbowkit&utm_medium=connector&utm_campaign=learnmore",
            steps: [
              {
                description: "wallet_connectors.rainbow.qr_code.step1.description",
                step: "install",
                title: "wallet_connectors.rainbow.qr_code.step1.title"
              },
              {
                description: "wallet_connectors.rainbow.qr_code.step2.description",
                step: "create",
                title: "wallet_connectors.rainbow.qr_code.step2.title"
              },
              {
                description: "wallet_connectors.rainbow.qr_code.step3.description",
                step: "scan",
                title: "wallet_connectors.rainbow.qr_code.step3.title"
              }
            ]
          }
        } : void 0
      };
    }
  };
};
var safeWallet = ({
  chains,
  ...options
}) => ({
  id: "safe",
  name: "Safe",
  iconAccent: "#12ff80",
  iconBackground: "#fff",
  iconUrl: async () => (await import("./safeWallet-DFMLSLCR-ZAGOXXCL.js")).default,
  installed: !(typeof window === "undefined") && (window == null ? void 0 : window.parent) !== window,
  downloadUrls: {},
  createConnector: () => ({
    connector: new SafeConnector({ chains, options })
  })
});
var walletConnectWallet = ({
  chains,
  options,
  projectId,
  version = "2"
}) => ({
  id: "walletConnect",
  name: "WalletConnect",
  iconUrl: async () => (await import("./walletConnectWallet-D6ZADJM7-UT35VDJY.js")).default,
  iconBackground: "#3b99fc",
  createConnector: () => {
    const ios = isIOS();
    const connector = version === "1" ? getWalletConnectConnector({
      version: "1",
      chains,
      options: {
        qrcode: ios,
        ...options
      }
    }) : getWalletConnectConnector({
      version: "2",
      chains,
      projectId,
      options: {
        showQrModal: ios,
        ...options
      }
    });
    const getUri = async () => getWalletConnectUri(connector, version);
    return {
      connector,
      ...ios ? {} : {
        mobile: { getUri },
        qrCode: { getUri }
      }
    };
  }
});
var getDefaultWallets = ({
  appName,
  chains,
  projectId
}) => {
  const wallets = [
    {
      groupName: "Popular",
      wallets: [
        injectedWallet({ chains }),
        safeWallet({ chains }),
        rainbowWallet({ chains, projectId }),
        coinbaseWallet({ appName, chains }),
        metaMaskWallet({ chains, projectId }),
        walletConnectWallet({ chains, projectId }),
        braveWallet({ chains })
      ]
    }
  ];
  return {
    connectors: connectorsForWallets(wallets),
    wallets
  };
};
function useAddRecentTransaction() {
  const store = useTransactionStore();
  const { address } = useAccount();
  const chainId = useChainId();
  return (0, import_react76.useCallback)((transaction) => {
    if (!address || !chainId) {
      throw new Error("No address or chain ID found");
    }
    store.addTransaction(address, chainId, transaction);
  }, [store, address, chainId]);
}
var __private__ = {
  DesktopOptions,
  dialogContent,
  dialogContentMobile,
  MobileOptions
};
export {
  ConnectButton,
  RainbowKitAuthenticationProvider,
  RainbowKitProvider,
  WalletButton2 as WalletButton,
  __private__,
  connectorsForWallets,
  createAuthenticationAdapter,
  cssObjectFromTheme,
  cssStringFromTheme,
  darkTheme,
  getDefaultWallets,
  getWalletConnectConnector,
  lightTheme,
  midnightTheme,
  useAccountModal,
  useAddRecentTransaction,
  useChainModal,
  useConnectModal
};
//# sourceMappingURL=@rainbow-me_rainbowkit.js.map
