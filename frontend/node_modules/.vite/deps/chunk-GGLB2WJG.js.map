{"version": 3, "sources": ["../../@rainbow-me/rainbowkit/dist/chunk-22HF4D4H.js"], "sourcesContent": ["\"use client\";\n// src/locales/en_US.json\nvar en_US_default = `{\n  \"connect_wallet\": {\n    \"label\": \"Connect Wallet\",\n    \"wrong_network\": {\n      \"label\": \"Wrong network\"\n    }\n  },\n\n  \"intro\": {\n    \"title\": \"What is a Wallet?\",\n    \"description\": \"A wallet is used to send, receive, store, and display digital assets. It's also a new way to log in, without needing to create new accounts and passwords on every website.\",\n    \"digital_asset\": {\n      \"title\": \"A Home for your Digital Assets\",\n      \"description\": \"Wallets are used to send, receive, store, and display digital assets like Ethereum and NFTs.\"\n    },\n    \"login\": {\n      \"title\": \"A New Way to Log In\",\n      \"description\": \"Instead of creating new accounts and passwords on every website, just connect your wallet.\"\n    },\n    \"get\": {\n      \"label\": \"Get a Wallet\"\n    },\n    \"learn_more\": {\n      \"label\": \"Learn More\"\n    }\n  },\n\n  \"sign_in\": {\n    \"label\": \"Verify your account\",\n    \"description\": \"To finish connecting, you must sign a message in your wallet to verify that you are the owner of this account.\",\n    \"message\": {\n      \"send\": \"Sign message\",\n      \"preparing\": \"Preparing message...\",\n      \"cancel\": \"Cancel\",\n      \"preparing_error\": \"Error preparing message, please retry!\"\n    },\n    \"signature\": {\n      \"waiting\": \"Waiting for signature...\",\n      \"verifying\": \"Verifying signature...\",\n      \"signing_error\": \"Error signing message, please retry!\",\n      \"verifying_error\": \"Error verifying signature, please retry!\",\n      \"oops_error\": \"Oops, something went wrong!\"\n    }\n  },\n\n  \"connect\": {\n    \"label\": \"Connect\",\n    \"title\": \"Connect a Wallet\",\n    \"new_to_ethereum\": {\n      \"description\": \"New to Ethereum wallets?\",\n      \"learn_more\": {\n        \"label\": \"Learn More\"\n      }\n    },\n    \"learn_more\": {\n      \"label\": \"Learn more\"\n    },\n    \"recent\": \"Recent\",\n    \"status\": {\n      \"opening\": \"Opening %{wallet}...\",\n      \"connecting\": \"Connecting\",\n      \"connect_mobile\": \"Continue in %{wallet}\",\n      \"not_installed\": \"%{wallet} is not installed\",\n      \"not_available\": \"%{wallet} is not available\",\n      \"confirm\": \"Confirm connection in the extension\",\n      \"confirm_mobile\": \"Accept connection request in the wallet\"\n    },\n    \"secondary_action\": {\n      \"get\": {\n        \"description\": \"Don't have %{wallet}?\",\n        \"label\": \"GET\"\n      },\n      \"install\": {\n        \"label\": \"INSTALL\"\n      },\n      \"retry\": {\n        \"label\": \"RETRY\"\n      }\n    },\n    \"walletconnect\": {\n      \"description\": {\n        \"full\": \"Need the official WalletConnect modal?\",\n        \"compact\": \"Need the WalletConnect modal?\"\n      },\n      \"open\": {\n        \"label\": \"OPEN\"\n      }\n    }\n  },\n\n  \"connect_scan\": {\n    \"title\": \"Scan with %{wallet}\",\n    \"fallback_title\": \"Scan with your phone\"\n  },\n\n  \"connector_group\": {\n    \"recommended\": \"Recommended\",\n    \"other\": \"Other\",\n    \"popular\": \"Popular\",\n    \"more\": \"More\",\n    \"others\": \"Others\"\n  },\n\n  \"get\": {\n    \"title\": \"Get a Wallet\",\n    \"action\": {\n      \"label\": \"GET\"\n    },\n    \"mobile\": {\n      \"description\": \"Mobile Wallet\"\n    },\n    \"extension\": {\n      \"description\": \"Browser Extension\"\n    },\n    \"mobile_and_extension\": {\n      \"description\": \"Mobile Wallet and Extension\"\n    },\n    \"mobile_and_desktop\": {\n      \"description\": \"Mobile and Desktop Wallet\"\n    },\n    \"looking_for\": {\n      \"title\": \"Not what you're looking for?\",\n      \"mobile\": {\n        \"description\": \"Select a wallet on the main screen to get started with a different wallet provider.\"\n      },\n      \"desktop\": {\n        \"compact_description\": \"Select a wallet on the main screen to get started with a different wallet provider.\",\n        \"wide_description\": \"Select a wallet on the left to get started with a different wallet provider.\"\n      }\n    }\n  },\n\n  \"get_options\": {\n    \"title\": \"Get started with %{wallet}\",\n    \"short_title\": \"Get %{wallet}\",\n    \"mobile\": {\n      \"title\": \"%{wallet} for Mobile\",\n      \"description\": \"Use the mobile wallet to explore the world of Ethereum.\",\n      \"download\": {\n        \"label\": \"Get the app\"\n      }\n    },\n    \"extension\": {\n      \"title\": \"%{wallet} for %{browser}\",\n      \"description\": \"Access your wallet right from your favorite web browser.\",\n      \"download\": {\n        \"label\": \"Add to %{browser}\"\n      }\n    },\n    \"desktop\": {\n      \"title\": \"%{wallet} for %{platform}\",\n      \"description\": \"Access your wallet natively from your powerful desktop.\",\n      \"download\": {\n        \"label\": \"Add to %{platform}\"\n      }\n    }\n  },\n\n  \"get_mobile\": {\n    \"title\": \"Install %{wallet}\",\n    \"description\": \"Scan with your phone to download on iOS or Android\",\n    \"continue\": {\n      \"label\": \"Continue\"\n    }\n  },\n\n  \"get_instructions\": {\n    \"mobile\": {\n      \"connect\": {\n        \"label\": \"Connect\"\n      },\n      \"learn_more\": {\n        \"label\": \"Learn More\"\n      }\n    },\n    \"extension\": {\n      \"refresh\": {\n        \"label\": \"Refresh\"\n      },\n      \"learn_more\": {\n        \"label\": \"Learn More\"\n      }\n    },\n    \"desktop\": {\n      \"connect\": {\n        \"label\": \"Connect\"\n      },\n      \"learn_more\": {\n        \"label\": \"Learn More\"\n      }\n    }\n  },\n\n  \"chains\": {\n    \"title\": \"Switch Networks\",\n    \"wrong_network\": \"Wrong network detected, switch or disconnect to continue.\",\n    \"confirm\": \"Confirm in Wallet\",\n    \"switching_not_supported\": \"Your wallet does not support switching networks from %{appName}. Try switching networks from within your wallet instead.\",\n    \"switching_not_supported_fallback\": \"Your wallet does not support switching networks from this app. Try switching networks from within your wallet instead.\",\n    \"disconnect\": \"Disconnect\",\n    \"connected\": \"Connected\"\n  },\n\n  \"profile\": {\n    \"disconnect\": {\n      \"label\": \"Disconnect\"\n    },\n    \"copy_address\": {\n      \"label\": \"Copy Address\",\n      \"copied\": \"Copied!\"\n    },\n    \"explorer\": {\n      \"label\": \"View more on explorer\"\n    },\n    \"transactions\": {\n      \"description\": \"%{appName} transactions will appear here...\",\n      \"description_fallback\": \"Your transactions will appear here...\",\n      \"recent\": {\n        \"title\": \"Recent Transactions\"\n      },\n      \"clear\": {\n        \"label\": \"Clear All\"\n      }\n    }\n  },\n\n  \"wallet_connectors\": {\n    \"argent\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"Put Argent on your home screen for faster access to your wallet.\",\n          \"title\": \"Open the Argent app\"\n        },\n        \"step2\": {\n          \"description\": \"Create a wallet and username, or import an existing wallet.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\",\n          \"title\": \"Tap the Scan QR button\"\n        }\n      }\n    },\n\n    \"bifrost\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"We recommend putting Bifrost Wallet on your home screen for quicker access.\",\n          \"title\": \"Open the Bifrost Wallet app\"\n        },\n        \"step2\": {\n          \"description\": \"Create or import a wallet using your recovery phrase.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\",\n          \"title\": \"Tap the scan button\"\n        }\n      }\n    },\n\n    \"bitget\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"We recommend putting Bitget Wallet on your home screen for quicker access.\",\n          \"title\": \"Open the Bitget Wallet app\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\",\n          \"title\": \"Tap the scan button\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"We recommend pinning Bitget Wallet to your taskbar for quicker access to your wallet.\",\n          \"title\": \"Install the Bitget Wallet extension\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\",\n          \"title\": \"Refresh your browser\"\n        }\n      }\n    },\n\n    \"bitski\": {\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"We recommend pinning Bitski to your taskbar for quicker access to your wallet.\",\n          \"title\": \"Install the Bitski extension\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\",\n          \"title\": \"Refresh your browser\"\n        }\n      }\n    },\n\n    \"coin98\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"We recommend putting Coin98 Wallet on your home screen for faster access to your wallet.\",\n          \"title\": \"Open the Coin98 Wallet app\"\n        },\n        \"step2\": {\n          \"description\": \"You can easily backup your wallet using our backup feature on your phone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\",\n          \"title\": \"Tap the WalletConnect button\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"Click at the top right of your browser and pin Coin98 Wallet for easy access.\",\n          \"title\": \"Install the Coin98 Wallet extension\"\n        },\n        \"step2\": {\n          \"description\": \"Create a new wallet or import an existing one.\",\n          \"title\": \"Create or Import a wallet\"\n        },\n        \"step3\": {\n          \"description\": \"Once you set up Coin98 Wallet, click below to refresh the browser and load up the extension.\",\n          \"title\": \"Refresh your browser\"\n        }\n      }\n    },\n\n    \"coinbase\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"We recommend putting Coinbase Wallet on your home screen for quicker access.\",\n          \"title\": \"Open the Coinbase Wallet app\"\n        },\n        \"step2\": {\n          \"description\": \"You can easily backup your wallet using the cloud backup feature.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\",\n          \"title\": \"Tap the scan button\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"We recommend pinning Coinbase Wallet to your taskbar for quicker access to your wallet.\",\n          \"title\": \"Install the Coinbase Wallet extension\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\",\n          \"title\": \"Refresh your browser\"\n        }\n      }\n    },\n\n    \"core\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"We recommend putting Core on your home screen for faster access to your wallet.\",\n          \"title\": \"Open the Core app\"\n        },\n        \"step2\": {\n          \"description\": \"You can easily backup your wallet using our backup feature on your phone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\",\n          \"title\": \"Tap the WalletConnect button\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"We recommend pinning Core to your taskbar for quicker access to your wallet.\",\n          \"title\": \"Install the Core extension\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\",\n          \"title\": \"Refresh your browser\"\n        }\n      }\n    },\n\n    \"fox\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"We recommend putting FoxWallet on your home screen for quicker access.\",\n          \"title\": \"Open the FoxWallet app\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\",\n          \"title\": \"Tap the scan button\"\n        }\n      }\n    },\n\n    \"frontier\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"description\": \"We recommend putting Frontier Wallet on your home screen for quicker access.\",\n          \"title\": \"Open the Frontier Wallet app\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\",\n          \"title\": \"Tap the scan button\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"We recommend pinning Frontier Wallet to your taskbar for quicker access to your wallet.\",\n          \"title\": \"Install the Frontier Wallet extension\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\",\n          \"title\": \"Refresh your browser\"\n        }\n      }\n    },\n\n    \"im_token\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the imToken app\",\n          \"description\": \"Put imToken app on your home screen for faster access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Create a new wallet or import an existing one.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap Scanner Icon in top right corner\",\n          \"description\": \"Choose New Connection, then scan the QR code and confirm the prompt to connect.\"\n        }\n      }\n    },\n\n    \"metamask\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the MetaMask app\",\n          \"description\": \"We recommend putting MetaMask on your home screen for quicker access.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap the scan button\",\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the MetaMask extension\",\n          \"description\": \"We recommend pinning MetaMask to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"okx\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the OKX Wallet app\",\n          \"description\": \"We recommend putting OKX Wallet on your home screen for quicker access.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap the scan button\",\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the OKX Wallet extension\",\n          \"description\": \"We recommend pinning OKX Wallet to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"omni\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the Omni app\",\n          \"description\": \"Add Omni to your home screen for faster access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Create a new wallet or import an existing one.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap the QR icon and scan\",\n          \"description\": \"Tap the QR icon on your home screen, scan the code and confirm the prompt to connect.\"\n        }\n      }\n    },\n\n    \"token_pocket\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the TokenPocket app\",\n          \"description\": \"We recommend putting TokenPocket on your home screen for quicker access.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap the scan button\",\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the TokenPocket extension\",\n          \"description\": \"We recommend pinning TokenPocket to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"trust\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the Trust Wallet app\",\n          \"description\": \"Put Trust Wallet on your home screen for faster access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Create a new wallet or import an existing one.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap WalletConnect in Settings\",\n          \"description\": \"Choose New Connection, then scan the QR code and confirm the prompt to connect.\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the Trust Wallet extension\",\n          \"description\": \"Click at the top right of your browser and pin Trust Wallet for easy access.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a wallet\",\n          \"description\": \"Create a new wallet or import an existing one.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up Trust Wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"uniswap\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the Uniswap app\",\n          \"description\": \"Add Uniswap Wallet to your home screen for faster access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Create a new wallet or import an existing one.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap the QR icon and scan\",\n          \"description\": \"Tap the QR icon on your homescreen, scan the code and confirm the prompt to connect.\"\n        }\n      }\n    },\n\n    \"zerion\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the Zerion app\",\n          \"description\": \"We recommend putting Zerion on your home screen for quicker access.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap the scan button\",\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\"\n        }\n      },\n\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the Zerion extension\",\n          \"description\": \"We recommend pinning Zerion to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"rainbow\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the Rainbow app\",\n          \"description\": \"We recommend putting Rainbow on your home screen for faster access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"You can easily backup your wallet using our backup feature on your phone.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap the scan button\",\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\"\n        }\n      }\n    },\n\n    \"enkrypt\": {\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"We recommend pinning Enkrypt Wallet to your taskbar for quicker access to your wallet.\",\n          \"title\": \"Install the Enkrypt Wallet extension\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\",\n          \"title\": \"Refresh your browser\"\n        }\n      }\n    },\n\n    \"frame\": {\n      \"extension\": {\n        \"step1\": {\n          \"description\": \"We recommend pinning Frame to your taskbar for quicker access to your wallet.\",\n          \"title\": \"Install Frame & the companion extension\"\n        },\n        \"step2\": {\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\",\n          \"title\": \"Create or Import a Wallet\"\n        },\n        \"step3\": {\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\",\n          \"title\": \"Refresh your browser\"\n        }\n      }\n    },\n\n    \"one_key\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the OneKey Wallet extension\",\n          \"description\": \"We recommend pinning OneKey Wallet to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"phantom\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the Phantom extension\",\n          \"description\": \"We recommend pinning Phantom to your taskbar for easier access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret recovery phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"rabby\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the Rabby extension\",\n          \"description\": \"We recommend pinning Rabby to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"safeheron\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the Core extension\",\n          \"description\": \"We recommend pinning Safeheron to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"taho\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the Taho extension\",\n          \"description\": \"We recommend pinning Taho to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"talisman\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the Talisman extension\",\n          \"description\": \"We recommend pinning Talisman to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import an Ethereum Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your recovery phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"xdefi\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the XDEFI Wallet extension\",\n          \"description\": \"We recommend pinning XDEFI Wallet to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"zeal\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the Zeal extension\",\n          \"description\": \"We recommend pinning Zeal to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"safepal\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the SafePal Wallet extension\",\n          \"description\": \"Click at the top right of your browser and pin SafePal Wallet for easy access.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a wallet\",\n          \"description\": \"Create a new wallet or import an existing one.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up SafePal Wallet, click below to refresh the browser and load up the extension.\"\n        }\n      },\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the SafePal Wallet app\",\n          \"description\": \"Put SafePal Wallet on your home screen for faster access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Create a new wallet or import an existing one.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap WalletConnect in Settings\",\n          \"description\": \"Choose New Connection, then scan the QR code and confirm the prompt to connect.\"\n        }\n      }\n    },\n\n    \"desig\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the Desig extension\",\n          \"description\": \"We recommend pinning Desig to your taskbar for easier access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      }\n    },\n\n    \"subwallet\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the SubWallet extension\",\n          \"description\": \"We recommend pinning SubWallet to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your recovery phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      },\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the SubWallet app\",\n          \"description\": \"We recommend putting SubWallet on your home screen for quicker access.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap the scan button\",\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\"\n        }\n      }\n    },\n\n    \"clv\": {\n      \"extension\": {\n        \"step1\": {\n          \"title\": \"Install the CLV Wallet extension\",\n          \"description\": \"We recommend pinning CLV Wallet to your taskbar for quicker access to your wallet.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Refresh your browser\",\n          \"description\": \"Once you set up your wallet, click below to refresh the browser and load up the extension.\"\n        }\n      },\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the CLV Wallet app\",\n          \"description\": \"We recommend putting CLV Wallet on your home screen for quicker access.\"\n        },\n        \"step2\": {\n          \"title\": \"Create or Import a Wallet\",\n          \"description\": \"Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone.\"\n        },\n        \"step3\": {\n          \"title\": \"Tap the scan button\",\n          \"description\": \"After you scan, a connection prompt will appear for you to connect your wallet.\"\n        }\n      }\n    },\n\n    \"okto\": {\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the Okto app\",\n          \"description\": \"Add Okto to your home screen for quick access\"\n        },\n        \"step2\": {\n          \"title\": \"Create an MPC Wallet\",\n          \"description\": \"Create an account and generate a wallet\"\n        },\n        \"step3\": {\n          \"title\": \"Tap WalletConnect in Settings\",\n          \"description\": \"Tap the Scan QR icon at the top right and confirm the prompt to connect.\"\n        }\n      }\n    },\n\n    \"ledger\": {\n      \"desktop\": {\n        \"step1\": {\n          \"title\": \"Open the Ledger Live app\",\n          \"description\": \"We recommend putting Ledger Live on your home screen for quicker access.\"\n        },\n        \"step2\": {\n          \"title\": \"Set up your Ledger\",\n          \"description\": \"Set up a new Ledger or connect to an existing one.\"\n        },\n        \"step3\": {\n          \"title\": \"Connect\",\n          \"description\": \"A connection prompt will appear for you to connect your wallet.\"\n        }\n      },\n      \"qr_code\": {\n        \"step1\": {\n          \"title\": \"Open the Ledger Live app\",\n          \"description\": \"We recommend putting Ledger Live on your home screen for quicker access.\"\n        },\n        \"step2\": {\n          \"title\": \"Set up your Ledger\",\n          \"description\": \"You can either sync with the desktop app or connect your Ledger.\"\n        },\n        \"step3\": {\n          \"title\": \"Scan the code\",\n          \"description\": \"Tap WalletConnect then Switch to Scanner. After you scan, a connection prompt will appear for you to connect your wallet.\"\n        }\n      }\n    }\n  }\n}\n`;\n\nexport {\n  en_US_default\n};\n"], "mappings": ";AAEA,IAAI,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}