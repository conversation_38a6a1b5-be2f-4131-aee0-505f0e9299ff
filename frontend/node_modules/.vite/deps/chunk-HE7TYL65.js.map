{"version": 3, "sources": ["../../eventemitter3/index.js", "../../@wagmi/connectors/dist/chunk-UGBGYVBH.js", "../../@wagmi/connectors/dist/chunk-W65LBPLT.js", "../../@wagmi/connectors/dist/chunk-OQILYQDO.js", "../../@wagmi/connectors/dist/chunk-P4NRLOY7.js", "../../zustand/esm/middleware.mjs", "../../zustand/esm/vanilla.mjs", "../../zustand/esm/shallow.mjs", "../../@wagmi/core/dist/chunk-TSH6VVF4.js"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n", "// src/errors.ts\nvar ChainNotConfiguredForConnectorError = class extends Error {\n  constructor({\n    chainId,\n    connectorId\n  }) {\n    super(`Chain \"${chainId}\" not configured for connector \"${connectorId}\".`);\n    this.name = \"ChainNotConfiguredForConnectorError\";\n  }\n};\nvar ConnectorNotFoundError = class extends Error {\n  constructor() {\n    super(...arguments);\n    this.name = \"ConnectorNotFoundError\";\n    this.message = \"Connector not found\";\n  }\n};\n\nexport {\n  ChainNotConfiguredForConnectorError,\n  ConnectorNotFoundError\n};\n", "var __accessCheck = (obj, member, msg) => {\n  if (!member.has(obj))\n    throw TypeError(\"Cannot \" + msg);\n};\nvar __privateGet = (obj, member, getter) => {\n  __accessCheck(obj, member, \"read from private field\");\n  return getter ? getter.call(obj) : member.get(obj);\n};\nvar __privateAdd = (obj, member, value) => {\n  if (member.has(obj))\n    throw TypeError(\"Cannot add the same private member more than once\");\n  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\n};\nvar __privateSet = (obj, member, value, setter) => {\n  __accessCheck(obj, member, \"write to private field\");\n  setter ? setter.call(obj, value) : member.set(obj, value);\n  return value;\n};\nvar __privateMethod = (obj, member, method) => {\n  __accessCheck(obj, member, \"access private method\");\n  return method;\n};\n\n// src/base.ts\nimport { default as EventEmitter } from \"eventemitter3\";\nimport { goerli, mainnet } from \"viem/chains\";\nvar Connector = class extends EventEmitter {\n  constructor({\n    chains = [mainnet, goerli],\n    options\n  }) {\n    super();\n    this.chains = chains;\n    this.options = options;\n  }\n  getBlockExplorerUrls(chain) {\n    const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n    if (blockExplorer)\n      return [\n        blockExplorer.url,\n        ...Object.values(blockExplorers).map((x) => x.url)\n      ];\n  }\n  isChainUnsupported(chainId) {\n    return !this.chains.some((x) => x.id === chainId);\n  }\n  setStorage(storage) {\n    this.storage = storage;\n  }\n};\n\nexport {\n  __privateGet,\n  __privateAdd,\n  __privateSet,\n  __privateMethod,\n  Connector\n};\n", "// src/utils/normalizeChainId.ts\nfunction normalizeChainId(chainId) {\n  if (typeof chainId === \"string\")\n    return Number.parseInt(\n      chainId,\n      chainId.trim().substring(0, 2) === \"0x\" ? 16 : 10\n    );\n  if (typeof chainId === \"bigint\")\n    return Number(chainId);\n  return chainId;\n}\n\nexport {\n  normalizeChainId\n};\n", "import {\n  ChainNotConfiguredForConnectorError,\n  ConnectorNotFoundError\n} from \"./chunk-UGBGYVBH.js\";\nimport {\n  normalizeChainId\n} from \"./chunk-OQILYQDO.js\";\nimport {\n  Connector,\n  __privateAdd,\n  __privateGet,\n  __privateSet\n} from \"./chunk-W65LBPLT.js\";\n\n// src/injected.ts\nimport {\n  ResourceUnavailableRpcError,\n  SwitchChainError,\n  UserRejectedRequestError,\n  createWalletClient,\n  custom,\n  getAddress,\n  numberToHex\n} from \"viem\";\n\n// src/utils/getInjectedName.ts\nfunction getInjectedName(ethereum) {\n  if (!ethereum)\n    return \"Injected\";\n  const getName = (provider) => {\n    if (provider.isApexWallet)\n      return \"Apex Wallet\";\n    if (provider.isAvalanche)\n      return \"Core Wallet\";\n    if (provider.isBackpack)\n      return \"Backpack\";\n    if (provider.isBifrost)\n      return \"Bifrost Wallet\";\n    if (provider.isBitKeep)\n      return \"BitKeep\";\n    if (provider.isBitski)\n      return \"Bitski\";\n    if (provider.isBlockWallet)\n      return \"BlockWallet\";\n    if (provider.isBraveWallet)\n      return \"Brave Wallet\";\n    if (provider.isCoin98)\n      return \"Coin98 Wallet\";\n    if (provider.isCoinbaseWallet)\n      return \"Coinbase Wallet\";\n    if (provider.isDawn)\n      return \"Dawn Wallet\";\n    if (provider.isDefiant)\n      return \"Defiant\";\n    if (provider.isDesig)\n      return \"Desig Wallet\";\n    if (provider.isEnkrypt)\n      return \"Enkrypt\";\n    if (provider.isExodus)\n      return \"Exodus\";\n    if (provider.isFordefi)\n      return \"Fordefi\";\n    if (provider.isFrame)\n      return \"Frame\";\n    if (provider.isFrontier)\n      return \"Frontier Wallet\";\n    if (provider.isGamestop)\n      return \"GameStop Wallet\";\n    if (provider.isHaqqWallet)\n      return \"HAQQ Wallet\";\n    if (provider.isHyperPay)\n      return \"HyperPay Wallet\";\n    if (provider.isImToken)\n      return \"ImToken\";\n    if (provider.isHaloWallet)\n      return \"Halo Wallet\";\n    if (provider.isKuCoinWallet)\n      return \"KuCoin Wallet\";\n    if (provider.isMathWallet)\n      return \"MathWallet\";\n    if (provider.isNovaWallet)\n      return \"Nova Wallet\";\n    if (provider.isOkxWallet || provider.isOKExWallet)\n      return \"OKX Wallet\";\n    if (provider.isOktoWallet)\n      return \"Okto Wallet\";\n    if (provider.isOneInchIOSWallet || provider.isOneInchAndroidWallet)\n      return \"1inch Wallet\";\n    if (provider.isOneKey)\n      return \"OneKey Wallet\";\n    if (provider.isOpera)\n      return \"Opera\";\n    if (provider.isPhantom)\n      return \"Phantom\";\n    if (provider.isPortal)\n      return \"Ripio Portal\";\n    if (provider.isRabby)\n      return \"Rabby Wallet\";\n    if (provider.isRainbow)\n      return \"Rainbow\";\n    if (provider.isSafePal)\n      return \"SafePal Wallet\";\n    if (provider.isStatus)\n      return \"Status\";\n    if (provider.isSubWallet)\n      return \"SubWallet\";\n    if (provider.isTalisman)\n      return \"Talisman\";\n    if (provider.isTally)\n      return \"Taho\";\n    if (provider.isTokenPocket)\n      return \"TokenPocket\";\n    if (provider.isTokenary)\n      return \"Tokenary\";\n    if (provider.isTrust || provider.isTrustWallet)\n      return \"Trust Wallet\";\n    if (provider.isTTWallet)\n      return \"TTWallet\";\n    if (provider.isXDEFI)\n      return \"XDEFI Wallet\";\n    if (provider.isZeal)\n      return \"Zeal\";\n    if (provider.isZerion)\n      return \"Zerion\";\n    if (provider.isMetaMask)\n      return \"MetaMask\";\n  };\n  if (ethereum.providers?.length) {\n    const nameSet = /* @__PURE__ */ new Set();\n    let unknownCount = 1;\n    for (const provider of ethereum.providers) {\n      let name = getName(provider);\n      if (!name) {\n        name = `Unknown Wallet #${unknownCount}`;\n        unknownCount += 1;\n      }\n      nameSet.add(name);\n    }\n    const names = [...nameSet];\n    if (names.length)\n      return names;\n    return names[0] ?? \"Injected\";\n  }\n  return getName(ethereum) ?? \"Injected\";\n}\n\n// src/injected.ts\nvar _provider;\nvar InjectedConnector = class extends Connector {\n  constructor({\n    chains,\n    options: options_\n  } = {}) {\n    const options = {\n      shimDisconnect: true,\n      getProvider() {\n        if (typeof window === \"undefined\")\n          return;\n        const ethereum = window.ethereum;\n        if (ethereum?.providers && ethereum.providers.length > 0)\n          return ethereum.providers[0];\n        return ethereum;\n      },\n      ...options_\n    };\n    super({ chains, options });\n    this.id = \"injected\";\n    __privateAdd(this, _provider, void 0);\n    this.shimDisconnectKey = `${this.id}.shimDisconnect`;\n    this.onAccountsChanged = (accounts) => {\n      if (accounts.length === 0)\n        this.emit(\"disconnect\");\n      else\n        this.emit(\"change\", {\n          account: getAddress(accounts[0])\n        });\n    };\n    this.onChainChanged = (chainId) => {\n      const id = normalizeChainId(chainId);\n      const unsupported = this.isChainUnsupported(id);\n      this.emit(\"change\", { chain: { id, unsupported } });\n    };\n    this.onDisconnect = async (error) => {\n      if (error.code === 1013) {\n        const provider = await this.getProvider();\n        if (provider) {\n          const isAuthorized = await this.getAccount();\n          if (isAuthorized)\n            return;\n        }\n      }\n      this.emit(\"disconnect\");\n      if (this.options.shimDisconnect)\n        this.storage?.removeItem(this.shimDisconnectKey);\n    };\n    const provider = options.getProvider();\n    if (typeof options.name === \"string\")\n      this.name = options.name;\n    else if (provider) {\n      const detectedName = getInjectedName(provider);\n      if (options.name)\n        this.name = options.name(detectedName);\n      else {\n        if (typeof detectedName === \"string\")\n          this.name = detectedName;\n        else\n          this.name = detectedName[0];\n      }\n    } else\n      this.name = \"Injected\";\n    this.ready = !!provider;\n  }\n  async connect({ chainId } = {}) {\n    try {\n      const provider = await this.getProvider();\n      if (!provider)\n        throw new ConnectorNotFoundError();\n      if (provider.on) {\n        provider.on(\"accountsChanged\", this.onAccountsChanged);\n        provider.on(\"chainChanged\", this.onChainChanged);\n        provider.on(\"disconnect\", this.onDisconnect);\n      }\n      this.emit(\"message\", { type: \"connecting\" });\n      const accounts = await provider.request({\n        method: \"eth_requestAccounts\"\n      });\n      const account = getAddress(accounts[0]);\n      let id = await this.getChainId();\n      let unsupported = this.isChainUnsupported(id);\n      if (chainId && id !== chainId) {\n        const chain = await this.switchChain(chainId);\n        id = chain.id;\n        unsupported = this.isChainUnsupported(id);\n      }\n      if (this.options.shimDisconnect)\n        this.storage?.setItem(this.shimDisconnectKey, true);\n      return { account, chain: { id, unsupported } };\n    } catch (error) {\n      if (this.isUserRejectedRequestError(error))\n        throw new UserRejectedRequestError(error);\n      if (error.code === -32002)\n        throw new ResourceUnavailableRpcError(error);\n      throw error;\n    }\n  }\n  async disconnect() {\n    const provider = await this.getProvider();\n    if (!provider?.removeListener)\n      return;\n    provider.removeListener(\"accountsChanged\", this.onAccountsChanged);\n    provider.removeListener(\"chainChanged\", this.onChainChanged);\n    provider.removeListener(\"disconnect\", this.onDisconnect);\n    if (this.options.shimDisconnect)\n      this.storage?.removeItem(this.shimDisconnectKey);\n  }\n  async getAccount() {\n    const provider = await this.getProvider();\n    if (!provider)\n      throw new ConnectorNotFoundError();\n    const accounts = await provider.request({\n      method: \"eth_accounts\"\n    });\n    return getAddress(accounts[0]);\n  }\n  async getChainId() {\n    const provider = await this.getProvider();\n    if (!provider)\n      throw new ConnectorNotFoundError();\n    return provider.request({ method: \"eth_chainId\" }).then(normalizeChainId);\n  }\n  async getProvider() {\n    const provider = this.options.getProvider();\n    if (provider)\n      __privateSet(this, _provider, provider);\n    return __privateGet(this, _provider);\n  }\n  async getWalletClient({\n    chainId\n  } = {}) {\n    const [provider, account] = await Promise.all([\n      this.getProvider(),\n      this.getAccount()\n    ]);\n    const chain = this.chains.find((x) => x.id === chainId);\n    if (!provider)\n      throw new Error(\"provider is required.\");\n    return createWalletClient({\n      account,\n      chain,\n      transport: custom(provider)\n    });\n  }\n  async isAuthorized() {\n    try {\n      if (this.options.shimDisconnect && !this.storage?.getItem(this.shimDisconnectKey))\n        return false;\n      const provider = await this.getProvider();\n      if (!provider)\n        throw new ConnectorNotFoundError();\n      const account = await this.getAccount();\n      return !!account;\n    } catch {\n      return false;\n    }\n  }\n  async switchChain(chainId) {\n    const provider = await this.getProvider();\n    if (!provider)\n      throw new ConnectorNotFoundError();\n    const id = numberToHex(chainId);\n    try {\n      await Promise.all([\n        provider.request({\n          method: \"wallet_switchEthereumChain\",\n          params: [{ chainId: id }]\n        }),\n        new Promise(\n          (res) => this.on(\"change\", ({ chain }) => {\n            if (chain?.id === chainId)\n              res();\n          })\n        )\n      ]);\n      return this.chains.find((x) => x.id === chainId) ?? {\n        id: chainId,\n        name: `Chain ${id}`,\n        network: `${id}`,\n        nativeCurrency: { name: \"Ether\", decimals: 18, symbol: \"ETH\" },\n        rpcUrls: { default: { http: [\"\"] }, public: { http: [\"\"] } }\n      };\n    } catch (error) {\n      const chain = this.chains.find((x) => x.id === chainId);\n      if (!chain)\n        throw new ChainNotConfiguredForConnectorError({\n          chainId,\n          connectorId: this.id\n        });\n      if (error.code === 4902 || error?.data?.originalError?.code === 4902) {\n        try {\n          await provider.request({\n            method: \"wallet_addEthereumChain\",\n            params: [\n              {\n                chainId: id,\n                chainName: chain.name,\n                nativeCurrency: chain.nativeCurrency,\n                rpcUrls: [chain.rpcUrls.public?.http[0] ?? \"\"],\n                blockExplorerUrls: this.getBlockExplorerUrls(chain)\n              }\n            ]\n          });\n          const currentChainId = await this.getChainId();\n          if (currentChainId !== chainId)\n            throw new UserRejectedRequestError(\n              new Error(\"User rejected switch after adding network.\")\n            );\n          return chain;\n        } catch (error2) {\n          throw new UserRejectedRequestError(error2);\n        }\n      }\n      if (this.isUserRejectedRequestError(error))\n        throw new UserRejectedRequestError(error);\n      throw new SwitchChainError(error);\n    }\n  }\n  async watchAsset({\n    address,\n    decimals = 18,\n    image,\n    symbol\n  }) {\n    const provider = await this.getProvider();\n    if (!provider)\n      throw new ConnectorNotFoundError();\n    return provider.request({\n      method: \"wallet_watchAsset\",\n      params: {\n        type: \"ERC20\",\n        options: {\n          address,\n          decimals,\n          image,\n          symbol\n        }\n      }\n    });\n  }\n  isUserRejectedRequestError(error) {\n    return error.code === 4001;\n  }\n};\n_provider = new WeakMap();\n\nexport {\n  InjectedConnector\n};\n", "const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (_e) {\n  }\n  if (!extensionConnector) {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && enabled) {\n      console.warn(\n        \"[zustand devtools middleware] Please install/enable Redux devtools extension\"\n      );\n    }\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format. \n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (_e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst oldImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    getStorage: () => localStorage,\n    serialize: JSON.stringify,\n    deserialize: JSON.parse,\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage;\n  try {\n    storage = options.getStorage();\n  } catch (_e) {\n  }\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const thenableSerialize = toThenable(options.serialize);\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    let errorInSync;\n    const thenable = thenableSerialize({ state, version: options.version }).then(\n      (serializedValue) => storage.setItem(options.name, serializedValue)\n    ).catch((e) => {\n      errorInSync = e;\n    });\n    if (errorInSync) {\n      throw errorInSync;\n    }\n    return thenable;\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => cb(get()));\n    const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue) => {\n      if (storageValue) {\n        return options.deserialize(storageValue);\n      }\n    }).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return deserializedStorageValue.state;\n        }\n      }\n    }).then((migratedState) => {\n      var _a2;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      return setItem();\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.getStorage) {\n        storage = newOptions.getStorage();\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  hydrate();\n  return stateFromStorage || configResult;\n};\nconst newImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return [\n              true,\n              options.migrate(\n                deserializedStorageValue.state,\n                deserializedStorageValue.version\n              )\n            ];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persistImpl = (config, baseOptions) => {\n  if (\"getStorage\" in baseOptions || \"serialize\" in baseOptions || \"deserialize\" in baseOptions) {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead.\"\n      );\n    }\n    return oldImpl(config, baseOptions);\n  }\n  return newImpl(config, baseOptions);\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\"\n      );\n    }\n    listeners.clear();\n  };\n  const api = { setState, getState, getInitialState, subscribe, destroy };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\"\n    );\n  }\n  return createStore(createState);\n};\n\nexport { createStore, vanilla as default };\n", "function shallow$1(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [key, value] of objA) {\n      if (!Object.is(value, objB.get(key))) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const value of objA) {\n      if (!objB.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (const keyA of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nvar shallow = (objA, objB) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { shallow } from 'zustand/shallow'`.\"\n    );\n  }\n  return shallow$1(objA, objB);\n};\n\nexport { shallow as default, shallow$1 as shallow };\n", "import {\n  InjectedConnector\n} from \"./chunk-BVC4KGLQ.js\";\nimport {\n  __privateAdd,\n  __privateGet,\n  __privateMethod,\n  __privateSet\n} from \"./chunk-MQXBDTVK.js\";\n\n// src/utils/configureChains.ts\nimport { createPublicClient, fallback, http, webSocket } from \"viem\";\nfunction configureChains(defaultChains, providers, {\n  batch = { multicall: { wait: 32 } },\n  pollingInterval = 4e3,\n  rank,\n  retryCount,\n  retryDelay,\n  stallTimeout\n} = {}) {\n  if (!defaultChains.length)\n    throw new Error(\"must have at least one chain\");\n  let chains = [];\n  const httpUrls = {};\n  const wsUrls = {};\n  for (const chain of defaultChains) {\n    let configExists = false;\n    for (const provider of providers) {\n      const apiConfig = provider(chain);\n      if (!apiConfig)\n        continue;\n      configExists = true;\n      if (!chains.some(({ id }) => id === chain.id)) {\n        chains = [...chains, apiConfig.chain];\n      }\n      httpUrls[chain.id] = [\n        ...httpUrls[chain.id] || [],\n        ...apiConfig.rpcUrls.http\n      ];\n      if (apiConfig.rpcUrls.webSocket) {\n        wsUrls[chain.id] = [\n          ...wsUrls[chain.id] || [],\n          ...apiConfig.rpcUrls.webSocket\n        ];\n      }\n    }\n    if (!configExists) {\n      throw new Error(\n        [\n          `Could not find valid provider configuration for chain \"${chain.name}\".\n`,\n          \"You may need to add `jsonRpcProvider` to `configureChains` with the chain's RPC URLs.\",\n          \"Read more: https://wagmi.sh/core/providers/jsonRpc\"\n        ].join(\"\\n\")\n      );\n    }\n  }\n  return {\n    chains,\n    publicClient: ({ chainId }) => {\n      const activeChain = chains.find((x) => x.id === chainId) ?? defaultChains[0];\n      const chainHttpUrls = httpUrls[activeChain.id];\n      if (!chainHttpUrls || !chainHttpUrls[0])\n        throw new Error(`No providers configured for chain \"${activeChain.id}\"`);\n      const publicClient = createPublicClient({\n        batch,\n        chain: activeChain,\n        transport: fallback(\n          chainHttpUrls.map((url) => http(url, { timeout: stallTimeout })),\n          { rank, retryCount, retryDelay }\n        ),\n        pollingInterval\n      });\n      return Object.assign(publicClient, {\n        chains\n      });\n    },\n    webSocketPublicClient: ({ chainId }) => {\n      const activeChain = chains.find((x) => x.id === chainId) ?? defaultChains[0];\n      const chainWsUrls = wsUrls[activeChain.id];\n      if (!chainWsUrls || !chainWsUrls[0])\n        return void 0;\n      const publicClient = createPublicClient({\n        batch,\n        chain: activeChain,\n        transport: fallback(\n          chainWsUrls.map((url) => webSocket(url, { timeout: stallTimeout })),\n          { rank, retryCount, retryDelay }\n        ),\n        pollingInterval\n      });\n      return Object.assign(publicClient, {\n        chains\n      });\n    }\n  };\n}\n\n// src/errors.ts\nimport { ConnectorNotFoundError } from \"@wagmi/connectors\";\nvar ChainMismatchError = class extends Error {\n  constructor({\n    activeChain,\n    targetChain\n  }) {\n    super(\n      `Chain mismatch: Expected \"${targetChain}\", received \"${activeChain}\".`\n    );\n    this.name = \"ChainMismatchError\";\n  }\n};\nvar ChainNotConfiguredError = class extends Error {\n  constructor({\n    chainId,\n    connectorId\n  }) {\n    super(\n      `Chain \"${chainId}\" not configured${connectorId ? ` for connector \"${connectorId}\"` : \"\"}.`\n    );\n    this.name = \"ChainNotConfigured\";\n  }\n};\nvar ConnectorAlreadyConnectedError = class extends Error {\n  constructor() {\n    super(...arguments);\n    this.name = \"ConnectorAlreadyConnectedError\";\n    this.message = \"Connector already connected\";\n  }\n};\nvar ConfigChainsNotFound = class extends Error {\n  constructor() {\n    super(...arguments);\n    this.name = \"ConfigChainsNotFound\";\n    this.message = \"No chains were found on the wagmi config. Some functions that require a chain may not work.\";\n  }\n};\nvar SwitchChainNotSupportedError = class extends Error {\n  constructor({ connector }) {\n    super(`\"${connector.name}\" does not support programmatic chain switching.`);\n    this.name = \"SwitchChainNotSupportedError\";\n  }\n};\n\n// src/utils/deepEqual.ts\nfunction deepEqual(a, b) {\n  if (a === b)\n    return true;\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    if (a.constructor !== b.constructor)\n      return false;\n    let length;\n    let i;\n    if (Array.isArray(a) && Array.isArray(b)) {\n      length = a.length;\n      if (length != b.length)\n        return false;\n      for (i = length; i-- !== 0; )\n        if (!deepEqual(a[i], b[i]))\n          return false;\n      return true;\n    }\n    if (a.valueOf !== Object.prototype.valueOf)\n      return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString)\n      return a.toString() === b.toString();\n    const keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length)\n      return false;\n    for (i = length; i-- !== 0; )\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i]))\n        return false;\n    for (i = length; i-- !== 0; ) {\n      const key = keys[i];\n      if (key && !deepEqual(a[key], b[key]))\n        return false;\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\n// src/utils/deserialize.ts\nvar findAndReplace = (cacheRef, {\n  find,\n  replace\n}) => {\n  if (cacheRef && find(cacheRef)) {\n    return replace(cacheRef);\n  }\n  if (typeof cacheRef !== \"object\") {\n    return cacheRef;\n  }\n  if (Array.isArray(cacheRef)) {\n    return cacheRef.map((item) => findAndReplace(item, { find, replace }));\n  }\n  if (cacheRef instanceof Object) {\n    return Object.entries(cacheRef).reduce(\n      (curr, [key, value]) => ({\n        ...curr,\n        [key]: findAndReplace(value, { find, replace })\n      }),\n      {}\n    );\n  }\n  return cacheRef;\n};\nfunction deserialize(cachedString) {\n  const cache = JSON.parse(cachedString);\n  const deserializedCacheWithBigInts = findAndReplace(cache, {\n    find: (data) => typeof data === \"string\" && data.startsWith(\"#bigint.\"),\n    replace: (data) => BigInt(data.replace(\"#bigint.\", \"\"))\n  });\n  return deserializedCacheWithBigInts;\n}\n\n// src/utils/getParameters.ts\nfunction getCallParameters(args) {\n  return {\n    accessList: args.accessList,\n    account: args.account,\n    blockNumber: args.blockNumber,\n    blockTag: args.blockTag,\n    data: args.data,\n    gas: args.gas,\n    gasPrice: args.gasPrice,\n    maxFeePerGas: args.maxFeePerGas,\n    maxPriorityFeePerGas: args.maxPriorityFeePerGas,\n    nonce: args.nonce,\n    to: args.to,\n    value: args.value\n  };\n}\nfunction getSendTransactionParameters(args) {\n  return {\n    accessList: args.accessList,\n    account: args.account,\n    data: args.data,\n    gas: args.gas,\n    gasPrice: args.gasPrice,\n    maxFeePerGas: args.maxFeePerGas,\n    maxPriorityFeePerGas: args.maxPriorityFeePerGas,\n    nonce: args.nonce,\n    to: args.to,\n    value: args.value\n  };\n}\n\n// src/utils/getUnit.ts\nimport { weiUnits } from \"viem\";\nfunction getUnit(unit) {\n  if (typeof unit === \"number\")\n    return unit;\n  if (unit === \"wei\")\n    return 0;\n  return Math.abs(weiUnits[unit]);\n}\n\n// src/utils/serialize.ts\nfunction getReferenceKey(keys, cutoff) {\n  return keys.slice(0, cutoff).join(\".\") || \".\";\n}\nfunction getCutoff(array, value) {\n  const { length } = array;\n  for (let index = 0; index < length; ++index) {\n    if (array[index] === value) {\n      return index + 1;\n    }\n  }\n  return 0;\n}\nfunction createReplacer(replacer, circularReplacer) {\n  const hasReplacer = typeof replacer === \"function\";\n  const hasCircularReplacer = typeof circularReplacer === \"function\";\n  const cache = [];\n  const keys = [];\n  return function replace(key, value) {\n    if (typeof value === \"object\") {\n      if (cache.length) {\n        const thisCutoff = getCutoff(cache, this);\n        if (thisCutoff === 0) {\n          cache[cache.length] = this;\n        } else {\n          cache.splice(thisCutoff);\n          keys.splice(thisCutoff);\n        }\n        keys[keys.length] = key;\n        const valueCutoff = getCutoff(cache, value);\n        if (valueCutoff !== 0) {\n          return hasCircularReplacer ? circularReplacer.call(\n            this,\n            key,\n            value,\n            getReferenceKey(keys, valueCutoff)\n          ) : `[ref=${getReferenceKey(keys, valueCutoff)}]`;\n        }\n      } else {\n        cache[0] = value;\n        keys[0] = key;\n      }\n    }\n    return hasReplacer ? replacer.call(this, key, value) : value;\n  };\n}\nfunction serialize(value, replacer, indent, circularReplacer) {\n  return JSON.stringify(\n    value,\n    createReplacer((key, value_) => {\n      const value2 = typeof value_ === \"bigint\" ? `#bigint.${value_.toString()}` : value_;\n      return replacer?.(key, value2) || value2;\n    }, circularReplacer),\n    indent ?? void 0\n  );\n}\n\n// src/config.ts\nimport { persist, subscribeWithSelector } from \"zustand/middleware\";\nimport { createStore } from \"zustand/vanilla\";\n\n// src/storage.ts\nvar noopStorage = {\n  getItem: (_key) => \"\",\n  setItem: (_key, _value) => null,\n  removeItem: (_key) => null\n};\nfunction createStorage({\n  deserialize: deserialize2 = deserialize,\n  key: prefix = \"wagmi\",\n  serialize: serialize2 = serialize,\n  storage\n}) {\n  return {\n    ...storage,\n    getItem: (key, defaultState = null) => {\n      const value = storage.getItem(`${prefix}.${key}`);\n      try {\n        return value ? deserialize2(value) : defaultState;\n      } catch (error) {\n        console.warn(error);\n        return defaultState;\n      }\n    },\n    setItem: (key, value) => {\n      if (value === null) {\n        storage.removeItem(`${prefix}.${key}`);\n      } else {\n        try {\n          storage.setItem(`${prefix}.${key}`, serialize2(value));\n        } catch (err) {\n          console.error(err);\n        }\n      }\n    },\n    removeItem: (key) => storage.removeItem(`${prefix}.${key}`)\n  };\n}\n\n// src/config.ts\nvar storeKey = \"store\";\nvar _isAutoConnecting, _lastUsedConnector, _addEffects, addEffects_fn;\nvar Config = class {\n  constructor({\n    autoConnect = false,\n    connectors = [new InjectedConnector()],\n    publicClient,\n    storage = createStorage({\n      storage: typeof window !== \"undefined\" ? window.localStorage : noopStorage\n    }),\n    logger = {\n      warn: console.warn\n    },\n    webSocketPublicClient\n  }) {\n    __privateAdd(this, _addEffects);\n    this.publicClients = /* @__PURE__ */ new Map();\n    this.webSocketPublicClients = /* @__PURE__ */ new Map();\n    __privateAdd(this, _isAutoConnecting, void 0);\n    __privateAdd(this, _lastUsedConnector, void 0);\n    this.args = {\n      autoConnect,\n      connectors,\n      logger,\n      publicClient,\n      storage,\n      webSocketPublicClient\n    };\n    let status = \"disconnected\";\n    let chainId;\n    if (autoConnect) {\n      try {\n        const rawState = storage.getItem(storeKey);\n        const data = rawState?.state?.data;\n        status = data?.account ? \"reconnecting\" : \"connecting\";\n        chainId = data?.chain?.id;\n      } catch (_error) {\n      }\n    }\n    const connectors_ = typeof connectors === \"function\" ? connectors() : connectors;\n    connectors_.forEach((connector) => connector.setStorage(storage));\n    this.store = createStore(\n      subscribeWithSelector(\n        persist(\n          () => ({\n            connectors: connectors_,\n            publicClient: this.getPublicClient({ chainId }),\n            status,\n            webSocketPublicClient: this.getWebSocketPublicClient({ chainId })\n          }),\n          {\n            name: storeKey,\n            storage,\n            partialize: (state) => ({\n              ...autoConnect && {\n                data: {\n                  account: state?.data?.account,\n                  chain: state?.data?.chain\n                }\n              },\n              chains: state?.chains\n            }),\n            version: 2\n          }\n        )\n      )\n    );\n    this.storage = storage;\n    __privateSet(this, _lastUsedConnector, storage?.getItem(\"wallet\"));\n    __privateMethod(this, _addEffects, addEffects_fn).call(this);\n    if (autoConnect && typeof window !== \"undefined\")\n      setTimeout(async () => await this.autoConnect(), 0);\n  }\n  get chains() {\n    return this.store.getState().chains;\n  }\n  get connectors() {\n    return this.store.getState().connectors;\n  }\n  get connector() {\n    return this.store.getState().connector;\n  }\n  get data() {\n    return this.store.getState().data;\n  }\n  get error() {\n    return this.store.getState().error;\n  }\n  get lastUsedChainId() {\n    return this.data?.chain?.id;\n  }\n  get publicClient() {\n    return this.store.getState().publicClient;\n  }\n  get status() {\n    return this.store.getState().status;\n  }\n  get subscribe() {\n    return this.store.subscribe;\n  }\n  get webSocketPublicClient() {\n    return this.store.getState().webSocketPublicClient;\n  }\n  setState(updater) {\n    const newState = typeof updater === \"function\" ? updater(this.store.getState()) : updater;\n    this.store.setState(newState, true);\n  }\n  clearState() {\n    this.setState((x) => ({\n      ...x,\n      chains: void 0,\n      connector: void 0,\n      data: void 0,\n      error: void 0,\n      status: \"disconnected\"\n    }));\n  }\n  async destroy() {\n    if (this.connector)\n      await this.connector.disconnect?.();\n    __privateSet(this, _isAutoConnecting, false);\n    this.clearState();\n    this.store.destroy();\n  }\n  async autoConnect() {\n    if (__privateGet(this, _isAutoConnecting))\n      return;\n    __privateSet(this, _isAutoConnecting, true);\n    this.setState((x) => ({\n      ...x,\n      status: x.data?.account ? \"reconnecting\" : \"connecting\"\n    }));\n    const sorted = __privateGet(this, _lastUsedConnector) ? [...this.connectors].sort(\n      (x) => x.id === __privateGet(this, _lastUsedConnector) ? -1 : 1\n    ) : this.connectors;\n    let connected = false;\n    for (const connector of sorted) {\n      if (!connector.ready || !connector.isAuthorized)\n        continue;\n      const isAuthorized = await connector.isAuthorized();\n      if (!isAuthorized)\n        continue;\n      const data = await connector.connect();\n      this.setState((x) => ({\n        ...x,\n        connector,\n        chains: connector?.chains,\n        data,\n        status: \"connected\"\n      }));\n      connected = true;\n      break;\n    }\n    if (!connected)\n      this.setState((x) => ({\n        ...x,\n        data: void 0,\n        status: \"disconnected\"\n      }));\n    __privateSet(this, _isAutoConnecting, false);\n    return this.data;\n  }\n  setConnectors(connectors) {\n    this.args = {\n      ...this.args,\n      connectors\n    };\n    const connectors_ = typeof connectors === \"function\" ? connectors() : connectors;\n    connectors_.forEach((connector) => connector.setStorage(this.args.storage));\n    this.setState((x) => ({\n      ...x,\n      connectors: connectors_\n    }));\n  }\n  getPublicClient({ chainId } = {}) {\n    let publicClient_ = this.publicClients.get(-1);\n    if (publicClient_ && publicClient_?.chain.id === chainId)\n      return publicClient_;\n    publicClient_ = this.publicClients.get(chainId ?? -1);\n    if (publicClient_)\n      return publicClient_;\n    const { publicClient } = this.args;\n    publicClient_ = typeof publicClient === \"function\" ? publicClient({ chainId }) : publicClient;\n    this.publicClients.set(chainId ?? -1, publicClient_);\n    return publicClient_;\n  }\n  setPublicClient(publicClient) {\n    const chainId = this.data?.chain?.id;\n    this.args = {\n      ...this.args,\n      publicClient\n    };\n    this.publicClients.clear();\n    this.setState((x) => ({\n      ...x,\n      publicClient: this.getPublicClient({ chainId })\n    }));\n  }\n  getWebSocketPublicClient({ chainId } = {}) {\n    let webSocketPublicClient_ = this.webSocketPublicClients.get(-1);\n    if (webSocketPublicClient_ && webSocketPublicClient_?.chain.id === chainId)\n      return webSocketPublicClient_;\n    webSocketPublicClient_ = this.webSocketPublicClients.get(chainId ?? -1);\n    if (webSocketPublicClient_)\n      return webSocketPublicClient_;\n    const { webSocketPublicClient } = this.args;\n    webSocketPublicClient_ = typeof webSocketPublicClient === \"function\" ? webSocketPublicClient({ chainId }) : webSocketPublicClient;\n    if (webSocketPublicClient_)\n      this.webSocketPublicClients.set(chainId ?? -1, webSocketPublicClient_);\n    return webSocketPublicClient_;\n  }\n  setWebSocketPublicClient(webSocketPublicClient) {\n    const chainId = this.data?.chain?.id;\n    this.args = {\n      ...this.args,\n      webSocketPublicClient\n    };\n    this.webSocketPublicClients.clear();\n    this.setState((x) => ({\n      ...x,\n      webSocketPublicClient: this.getWebSocketPublicClient({\n        chainId\n      })\n    }));\n  }\n  setLastUsedConnector(lastUsedConnector = null) {\n    this.storage?.setItem(\"wallet\", lastUsedConnector);\n  }\n};\n_isAutoConnecting = new WeakMap();\n_lastUsedConnector = new WeakMap();\n_addEffects = new WeakSet();\naddEffects_fn = function() {\n  const onChange = (data) => {\n    this.setState((x) => ({\n      ...x,\n      data: { ...x.data, ...data }\n    }));\n  };\n  const onDisconnect = () => {\n    this.clearState();\n  };\n  const onError = (error) => {\n    this.setState((x) => ({ ...x, error }));\n  };\n  this.store.subscribe(\n    ({ connector }) => connector,\n    (connector, prevConnector) => {\n      prevConnector?.off?.(\"change\", onChange);\n      prevConnector?.off?.(\"disconnect\", onDisconnect);\n      prevConnector?.off?.(\"error\", onError);\n      if (!connector)\n        return;\n      connector.on?.(\"change\", onChange);\n      connector.on?.(\"disconnect\", onDisconnect);\n      connector.on?.(\"error\", onError);\n    }\n  );\n  const { publicClient, webSocketPublicClient } = this.args;\n  const subscribePublicClient = typeof publicClient === \"function\";\n  const subscribeWebSocketPublicClient = typeof webSocketPublicClient === \"function\";\n  if (subscribePublicClient || subscribeWebSocketPublicClient)\n    this.store.subscribe(\n      ({ data }) => data?.chain?.id,\n      (chainId) => {\n        this.setState((x) => ({\n          ...x,\n          publicClient: this.getPublicClient({ chainId }),\n          webSocketPublicClient: this.getWebSocketPublicClient({\n            chainId\n          })\n        }));\n      }\n    );\n};\nvar config;\nfunction createConfig(args) {\n  const config_ = new Config(args);\n  config = config_;\n  return config_;\n}\nfunction getConfig() {\n  if (!config) {\n    throw new Error(\n      \"No wagmi config found. Ensure you have set up a config: https://wagmi.sh/react/config\"\n    );\n  }\n  return config;\n}\n\n// src/actions/accounts/connect.ts\nasync function connect({ chainId, connector }) {\n  const config2 = getConfig();\n  const activeConnector = config2.connector;\n  if (activeConnector && connector.id === activeConnector.id)\n    throw new ConnectorAlreadyConnectedError();\n  try {\n    config2.setState((x) => ({ ...x, status: \"connecting\" }));\n    const data = await connector.connect({ chainId });\n    config2.setLastUsedConnector(connector.id);\n    config2.setState((x) => ({\n      ...x,\n      connector,\n      chains: connector?.chains,\n      data,\n      status: \"connected\"\n    }));\n    config2.storage.setItem(\"connected\", true);\n    return { ...data, connector };\n  } catch (err) {\n    config2.setState((x) => {\n      return {\n        ...x,\n        status: x.connector ? \"connected\" : \"disconnected\"\n      };\n    });\n    throw err;\n  }\n}\n\n// src/actions/accounts/disconnect.ts\nasync function disconnect() {\n  const config2 = getConfig();\n  if (config2.connector)\n    await config2.connector.disconnect();\n  config2.clearState();\n  config2.storage.removeItem(\"connected\");\n}\n\n// src/actions/accounts/fetchBalance.ts\nimport {\n  ContractFunctionExecutionError as ContractFunctionExecutionError3,\n  formatUnits as formatUnits2,\n  hexToString as hexToString2,\n  trim as trim2\n} from \"viem\";\n\n// src/constants/abis.ts\nvar erc20ABI = [\n  {\n    type: \"event\",\n    name: \"Approval\",\n    inputs: [\n      {\n        indexed: true,\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"spender\",\n        type: \"address\"\n      },\n      {\n        indexed: false,\n        name: \"value\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"event\",\n    name: \"Transfer\",\n    inputs: [\n      {\n        indexed: true,\n        name: \"from\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"to\",\n        type: \"address\"\n      },\n      {\n        indexed: false,\n        name: \"value\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"allowance\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        name: \"spender\",\n        type: \"address\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"approve\",\n    stateMutability: \"nonpayable\",\n    inputs: [\n      {\n        name: \"spender\",\n        type: \"address\"\n      },\n      {\n        name: \"amount\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"balanceOf\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"account\",\n        type: \"address\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"decimals\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint8\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"name\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"string\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"symbol\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"string\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"totalSupply\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"transfer\",\n    stateMutability: \"nonpayable\",\n    inputs: [\n      {\n        name: \"recipient\",\n        type: \"address\"\n      },\n      {\n        name: \"amount\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"transferFrom\",\n    stateMutability: \"nonpayable\",\n    inputs: [\n      {\n        name: \"sender\",\n        type: \"address\"\n      },\n      {\n        name: \"recipient\",\n        type: \"address\"\n      },\n      {\n        name: \"amount\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ]\n  }\n];\nvar erc20ABI_bytes32 = [\n  {\n    type: \"event\",\n    name: \"Approval\",\n    inputs: [\n      {\n        indexed: true,\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"spender\",\n        type: \"address\"\n      },\n      {\n        indexed: false,\n        name: \"value\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"event\",\n    name: \"Transfer\",\n    inputs: [\n      {\n        indexed: true,\n        name: \"from\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"to\",\n        type: \"address\"\n      },\n      {\n        indexed: false,\n        name: \"value\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"allowance\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        name: \"spender\",\n        type: \"address\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"approve\",\n    stateMutability: \"nonpayable\",\n    inputs: [\n      {\n        name: \"spender\",\n        type: \"address\"\n      },\n      {\n        name: \"amount\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"balanceOf\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"account\",\n        type: \"address\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"decimals\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint8\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"name\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"bytes32\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"symbol\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"bytes32\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"totalSupply\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"transfer\",\n    stateMutability: \"nonpayable\",\n    inputs: [\n      {\n        name: \"recipient\",\n        type: \"address\"\n      },\n      {\n        name: \"amount\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"transferFrom\",\n    stateMutability: \"nonpayable\",\n    inputs: [\n      {\n        name: \"sender\",\n        type: \"address\"\n      },\n      {\n        name: \"recipient\",\n        type: \"address\"\n      },\n      {\n        name: \"amount\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ]\n  }\n];\nvar erc721ABI = [\n  {\n    type: \"event\",\n    name: \"Approval\",\n    inputs: [\n      {\n        indexed: true,\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"spender\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"tokenId\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"event\",\n    name: \"ApprovalForAll\",\n    inputs: [\n      {\n        indexed: true,\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"operator\",\n        type: \"address\"\n      },\n      {\n        indexed: false,\n        name: \"approved\",\n        type: \"bool\"\n      }\n    ]\n  },\n  {\n    type: \"event\",\n    name: \"Transfer\",\n    inputs: [\n      {\n        indexed: true,\n        name: \"from\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"to\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"tokenId\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"approve\",\n    stateMutability: \"payable\",\n    inputs: [\n      {\n        name: \"spender\",\n        type: \"address\"\n      },\n      {\n        name: \"tokenId\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: []\n  },\n  {\n    type: \"function\",\n    name: \"balanceOf\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"account\",\n        type: \"address\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"getApproved\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"tokenId\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"address\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"isApprovedForAll\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        name: \"operator\",\n        type: \"address\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"name\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"string\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"ownerOf\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"tokenId\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"owner\",\n        type: \"address\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"safeTransferFrom\",\n    stateMutability: \"payable\",\n    inputs: [\n      {\n        name: \"from\",\n        type: \"address\"\n      },\n      {\n        name: \"to\",\n        type: \"address\"\n      },\n      {\n        name: \"tokenId\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: []\n  },\n  {\n    type: \"function\",\n    name: \"safeTransferFrom\",\n    stateMutability: \"nonpayable\",\n    inputs: [\n      {\n        name: \"from\",\n        type: \"address\"\n      },\n      {\n        name: \"to\",\n        type: \"address\"\n      },\n      {\n        name: \"id\",\n        type: \"uint256\"\n      },\n      {\n        name: \"data\",\n        type: \"bytes\"\n      }\n    ],\n    outputs: []\n  },\n  {\n    type: \"function\",\n    name: \"setApprovalForAll\",\n    stateMutability: \"nonpayable\",\n    inputs: [\n      {\n        name: \"operator\",\n        type: \"address\"\n      },\n      {\n        name: \"approved\",\n        type: \"bool\"\n      }\n    ],\n    outputs: []\n  },\n  {\n    type: \"function\",\n    name: \"symbol\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"string\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"tokenByIndex\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"index\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"tokenByIndex\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        name: \"index\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"tokenId\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"tokenURI\",\n    stateMutability: \"view\",\n    inputs: [\n      {\n        name: \"tokenId\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: [\n      {\n        name: \"\",\n        type: \"string\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"totalSupply\",\n    stateMutability: \"view\",\n    inputs: [],\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ]\n  },\n  {\n    type: \"function\",\n    name: \"transferFrom\",\n    stateMutability: \"payable\",\n    inputs: [\n      {\n        name: \"sender\",\n        type: \"address\"\n      },\n      {\n        name: \"recipient\",\n        type: \"address\"\n      },\n      {\n        name: \"tokeId\",\n        type: \"uint256\"\n      }\n    ],\n    outputs: []\n  }\n];\nvar erc4626ABI = [\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"spender\",\n        type: \"address\"\n      },\n      {\n        indexed: false,\n        name: \"value\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"Approval\",\n    type: \"event\"\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: \"sender\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"receiver\",\n        type: \"address\"\n      },\n      {\n        indexed: false,\n        name: \"assets\",\n        type: \"uint256\"\n      },\n      {\n        indexed: false,\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"Deposit\",\n    type: \"event\"\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: \"from\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"to\",\n        type: \"address\"\n      },\n      {\n        indexed: false,\n        name: \"value\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"Transfer\",\n    type: \"event\"\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: \"sender\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"receiver\",\n        type: \"address\"\n      },\n      {\n        indexed: true,\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        indexed: false,\n        name: \"assets\",\n        type: \"uint256\"\n      },\n      {\n        indexed: false,\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"Withdraw\",\n    type: \"event\"\n  },\n  {\n    inputs: [\n      {\n        name: \"owner\",\n        type: \"address\"\n      },\n      {\n        name: \"spender\",\n        type: \"address\"\n      }\n    ],\n    name: \"allowance\",\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"spender\",\n        type: \"address\"\n      },\n      {\n        name: \"amount\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"approve\",\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ],\n    stateMutability: \"nonpayable\",\n    type: \"function\"\n  },\n  {\n    inputs: [],\n    name: \"asset\",\n    outputs: [\n      {\n        name: \"assetTokenAddress\",\n        type: \"address\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"account\",\n        type: \"address\"\n      }\n    ],\n    name: \"balanceOf\",\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"convertToAssets\",\n    outputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"convertToShares\",\n    outputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      },\n      {\n        name: \"receiver\",\n        type: \"address\"\n      }\n    ],\n    name: \"deposit\",\n    outputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"nonpayable\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"caller\",\n        type: \"address\"\n      }\n    ],\n    name: \"maxDeposit\",\n    outputs: [\n      {\n        name: \"maxAssets\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"caller\",\n        type: \"address\"\n      }\n    ],\n    name: \"maxMint\",\n    outputs: [\n      {\n        name: \"maxShares\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"owner\",\n        type: \"address\"\n      }\n    ],\n    name: \"maxRedeem\",\n    outputs: [\n      {\n        name: \"maxShares\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"owner\",\n        type: \"address\"\n      }\n    ],\n    name: \"maxWithdraw\",\n    outputs: [\n      {\n        name: \"maxAssets\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      },\n      {\n        name: \"receiver\",\n        type: \"address\"\n      }\n    ],\n    name: \"mint\",\n    outputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"nonpayable\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"previewDeposit\",\n    outputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"previewMint\",\n    outputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"previewRedeem\",\n    outputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"previewWithdraw\",\n    outputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      },\n      {\n        name: \"receiver\",\n        type: \"address\"\n      },\n      {\n        name: \"owner\",\n        type: \"address\"\n      }\n    ],\n    name: \"redeem\",\n    outputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"nonpayable\",\n    type: \"function\"\n  },\n  {\n    inputs: [],\n    name: \"totalAssets\",\n    outputs: [\n      {\n        name: \"totalManagedAssets\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [],\n    name: \"totalSupply\",\n    outputs: [\n      {\n        name: \"\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"view\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"to\",\n        type: \"address\"\n      },\n      {\n        name: \"amount\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"transfer\",\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ],\n    stateMutability: \"nonpayable\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"from\",\n        type: \"address\"\n      },\n      {\n        name: \"to\",\n        type: \"address\"\n      },\n      {\n        name: \"amount\",\n        type: \"uint256\"\n      }\n    ],\n    name: \"transferFrom\",\n    outputs: [\n      {\n        name: \"\",\n        type: \"bool\"\n      }\n    ],\n    stateMutability: \"nonpayable\",\n    type: \"function\"\n  },\n  {\n    inputs: [\n      {\n        name: \"assets\",\n        type: \"uint256\"\n      },\n      {\n        name: \"receiver\",\n        type: \"address\"\n      },\n      {\n        name: \"owner\",\n        type: \"address\"\n      }\n    ],\n    name: \"withdraw\",\n    outputs: [\n      {\n        name: \"shares\",\n        type: \"uint256\"\n      }\n    ],\n    stateMutability: \"nonpayable\",\n    type: \"function\"\n  }\n];\n\n// src/actions/contracts/fetchToken.ts\nimport {\n  ContractFunctionExecutionError,\n  formatUnits,\n  hexToString,\n  trim\n} from \"viem\";\nasync function fetchToken({\n  address,\n  chainId,\n  formatUnits: unit = 18\n}) {\n  async function fetchToken_({ abi }) {\n    const erc20Config = { address, abi, chainId };\n    const [decimals, name, symbol, totalSupply] = await readContracts({\n      allowFailure: false,\n      contracts: [\n        { ...erc20Config, functionName: \"decimals\" },\n        { ...erc20Config, functionName: \"name\" },\n        { ...erc20Config, functionName: \"symbol\" },\n        { ...erc20Config, functionName: \"totalSupply\" }\n      ]\n    });\n    return {\n      address,\n      decimals,\n      name,\n      symbol,\n      totalSupply: {\n        formatted: formatUnits(totalSupply, getUnit(unit)),\n        value: totalSupply\n      }\n    };\n  }\n  try {\n    return await fetchToken_({ abi: erc20ABI });\n  } catch (err) {\n    if (err instanceof ContractFunctionExecutionError) {\n      const { name, symbol, ...rest } = await fetchToken_({\n        abi: erc20ABI_bytes32\n      });\n      return {\n        name: hexToString(trim(name, { dir: \"right\" })),\n        symbol: hexToString(trim(symbol, { dir: \"right\" })),\n        ...rest\n      };\n    }\n    throw err;\n  }\n}\n\n// src/actions/viem/getPublicClient.ts\nfunction getPublicClient({ chainId } = {}) {\n  const config2 = getConfig();\n  if (chainId)\n    return config2.getPublicClient({ chainId }) || config2.publicClient;\n  return config2.publicClient;\n}\n\n// src/actions/viem/getWalletClient.ts\nasync function getWalletClient({\n  chainId\n} = {}) {\n  const config2 = getConfig();\n  const walletClient = await config2.connector?.getWalletClient?.({ chainId }) || null;\n  return walletClient;\n}\n\n// src/actions/viem/getWebSocketPublicClient.ts\nfunction getWebSocketPublicClient({\n  chainId\n} = {}) {\n  const config2 = getConfig();\n  if (chainId)\n    return config2.getWebSocketPublicClient({ chainId }) || config2.webSocketPublicClient;\n  return config2.webSocketPublicClient;\n}\n\n// src/actions/viem/watchPublicClient.ts\nfunction watchPublicClient(args, callback) {\n  const config2 = getConfig();\n  const handleChange = async () => callback(getPublicClient(args));\n  const unsubscribe = config2.subscribe(\n    ({ publicClient }) => publicClient,\n    handleChange\n  );\n  return unsubscribe;\n}\n\n// src/actions/viem/watchWalletClient.ts\nimport { shallow } from \"zustand/shallow\";\nfunction watchWalletClient({ chainId }, callback) {\n  const config2 = getConfig();\n  const handleChange = async ({ chainId: chainId_ }) => {\n    if (chainId && chainId_ && chainId !== chainId_)\n      return;\n    const walletClient = await getWalletClient({ chainId });\n    if (!getConfig().connector)\n      return callback(null);\n    return callback(walletClient);\n  };\n  const unsubscribe = config2.subscribe(\n    ({ data, connector }) => ({\n      account: data?.account,\n      chainId: data?.chain?.id,\n      connector\n    }),\n    handleChange,\n    {\n      equalityFn: shallow\n    }\n  );\n  return unsubscribe;\n}\n\n// src/actions/viem/watchWebSocketPublicClient.ts\nfunction watchWebSocketPublicClient(args, callback) {\n  const config2 = getConfig();\n  const handleChange = async () => callback(getWebSocketPublicClient(args));\n  const unsubscribe = config2.subscribe(\n    ({ webSocketPublicClient }) => webSocketPublicClient,\n    handleChange\n  );\n  return unsubscribe;\n}\n\n// src/actions/contracts/prepareWriteContract.ts\nasync function prepareWriteContract({\n  abi,\n  address,\n  args,\n  chainId,\n  dataSuffix,\n  functionName,\n  walletClient: walletClient_,\n  ...config2\n}) {\n  const publicClient = getPublicClient({ chainId });\n  const walletClient = walletClient_ ?? await getWalletClient({ chainId });\n  if (!walletClient)\n    throw new ConnectorNotFoundError();\n  if (chainId)\n    assertActiveChain({ chainId });\n  const {\n    account,\n    accessList,\n    blockNumber,\n    blockTag,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    value\n  } = getCallParameters(config2);\n  const { result, request } = await publicClient.simulateContract({\n    abi,\n    address,\n    functionName,\n    args,\n    account: account || walletClient.account,\n    accessList,\n    blockNumber,\n    blockTag,\n    dataSuffix,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    value\n  });\n  const minimizedAbi = abi.filter(\n    (abiItem) => \"name\" in abiItem && abiItem.name === functionName\n  );\n  return {\n    mode: \"prepared\",\n    request: {\n      ...request,\n      abi: minimizedAbi,\n      chainId\n    },\n    result\n  };\n}\n\n// src/actions/contracts/getContract.ts\nimport { getContract as getContract_ } from \"viem\";\nfunction getContract({\n  address,\n  abi,\n  chainId,\n  walletClient\n}) {\n  const publicClient = getPublicClient({ chainId });\n  return getContract_({\n    address,\n    abi,\n    publicClient,\n    walletClient\n  });\n}\n\n// src/actions/contracts/multicall.ts\nasync function multicall({\n  chainId,\n  contracts,\n  blockNumber,\n  blockTag,\n  ...args\n}) {\n  const publicClient = getPublicClient({ chainId });\n  if (!publicClient.chains)\n    throw new ConfigChainsNotFound();\n  if (chainId && publicClient.chain.id !== chainId)\n    throw new ChainNotConfiguredError({ chainId });\n  return publicClient.multicall({\n    allowFailure: args.allowFailure ?? true,\n    blockNumber,\n    blockTag,\n    contracts\n  });\n}\n\n// src/actions/contracts/readContract.ts\nasync function readContract({\n  address,\n  account,\n  chainId,\n  abi,\n  args,\n  functionName,\n  blockNumber,\n  blockTag\n}) {\n  const publicClient = getPublicClient({ chainId });\n  return publicClient.readContract({\n    abi,\n    address,\n    account,\n    functionName,\n    args,\n    blockNumber,\n    blockTag\n  });\n}\n\n// src/actions/contracts/readContracts.ts\nimport { ContractFunctionExecutionError as ContractFunctionExecutionError2 } from \"viem\";\nasync function readContracts({\n  contracts,\n  blockNumber,\n  blockTag,\n  ...args\n}) {\n  const { allowFailure = true } = args;\n  try {\n    const publicClient = getPublicClient();\n    const contractsByChainId = contracts.reduce((contracts2, contract, index) => {\n      const chainId = contract.chainId ?? publicClient.chain.id;\n      return {\n        ...contracts2,\n        [chainId]: [...contracts2[chainId] || [], { contract, index }]\n      };\n    }, {});\n    const promises = () => Object.entries(contractsByChainId).map(\n      ([chainId, contracts2]) => multicall({\n        allowFailure,\n        chainId: parseInt(chainId),\n        contracts: contracts2.map(\n          ({ contract }) => contract\n        ),\n        blockNumber,\n        blockTag\n      })\n    );\n    const multicallResults = (await Promise.all(promises())).flat();\n    const resultIndexes = Object.values(contractsByChainId).flatMap(\n      (contracts2) => contracts2.map(({ index }) => index)\n    );\n    return multicallResults.reduce((results, result, index) => {\n      if (results)\n        results[resultIndexes[index]] = result;\n      return results;\n    }, []);\n  } catch (err) {\n    if (err instanceof ContractFunctionExecutionError2)\n      throw err;\n    const promises = () => contracts.map(\n      (contract) => readContract({ ...contract, blockNumber, blockTag })\n    );\n    if (allowFailure)\n      return (await Promise.allSettled(promises())).map((result) => {\n        if (result.status === \"fulfilled\")\n          return { result: result.value, status: \"success\" };\n        return { error: result.reason, result: void 0, status: \"failure\" };\n      });\n    return await Promise.all(promises());\n  }\n}\n\n// src/actions/contracts/watchContractEvent.ts\nimport { shallow as shallow2 } from \"zustand/shallow\";\nfunction watchContractEvent({\n  address,\n  abi,\n  chainId,\n  eventName\n}, callback) {\n  let unwatch;\n  const watchEvent = async () => {\n    if (unwatch)\n      unwatch();\n    const publicClient = getWebSocketPublicClient({ chainId }) || getPublicClient({ chainId });\n    unwatch = publicClient.watchContractEvent({\n      address,\n      abi,\n      eventName,\n      onLogs: callback\n    });\n  };\n  watchEvent();\n  const config2 = getConfig();\n  const unsubscribe = config2.subscribe(\n    ({ publicClient, webSocketPublicClient }) => ({\n      publicClient,\n      webSocketPublicClient\n    }),\n    watchEvent,\n    { equalityFn: shallow2 }\n  );\n  return () => {\n    unwatch?.();\n    unsubscribe();\n  };\n}\n\n// src/actions/network-status/watchBlockNumber.ts\nimport { shallow as shallow3 } from \"zustand/shallow\";\nfunction watchBlockNumber(args, callback) {\n  let unwatch;\n  const createListener = (publicClient) => {\n    if (unwatch)\n      unwatch();\n    unwatch = publicClient.watchBlockNumber({\n      onBlockNumber: callback,\n      emitOnBegin: true,\n      poll: true\n    });\n  };\n  const publicClient_ = getWebSocketPublicClient({ chainId: args.chainId }) ?? getPublicClient({ chainId: args.chainId });\n  if (args.listen)\n    createListener(publicClient_);\n  const config2 = getConfig();\n  const unsubscribe = config2.subscribe(\n    ({ publicClient, webSocketPublicClient }) => ({\n      publicClient,\n      webSocketPublicClient\n    }),\n    async ({ publicClient, webSocketPublicClient }) => {\n      const publicClient_2 = webSocketPublicClient ?? publicClient;\n      if (args.listen && !args.chainId && publicClient_2) {\n        createListener(publicClient_2);\n      }\n    },\n    {\n      equalityFn: shallow3\n    }\n  );\n  return () => {\n    unsubscribe();\n    unwatch?.();\n  };\n}\n\n// src/actions/contracts/watchMulticall.ts\nfunction watchMulticall(args, callback) {\n  const config2 = getConfig();\n  const handleChange = async () => callback(await multicall(args));\n  const unwatch = args.listenToBlock ? watchBlockNumber({ listen: true }, handleChange) : void 0;\n  const unsubscribe = config2.subscribe(\n    ({ publicClient }) => publicClient,\n    handleChange\n  );\n  return () => {\n    unsubscribe();\n    unwatch?.();\n  };\n}\n\n// src/actions/contracts/watchReadContract.ts\nfunction watchReadContract(args, callback) {\n  const config2 = getConfig();\n  const handleChange = async () => callback(await readContract(args));\n  const unwatch = args.listenToBlock ? watchBlockNumber({ listen: true }, handleChange) : void 0;\n  const unsubscribe = config2.subscribe(\n    ({ publicClient }) => publicClient,\n    handleChange\n  );\n  return () => {\n    unsubscribe();\n    unwatch?.();\n  };\n}\n\n// src/actions/contracts/watchReadContracts.ts\nfunction watchReadContracts(args, callback) {\n  const config2 = getConfig();\n  const handleChange = async () => callback(await readContracts(args));\n  const unwatch = args.listenToBlock ? watchBlockNumber({ listen: true }, handleChange) : void 0;\n  const unsubscribe = config2.subscribe(\n    ({ publicClient }) => publicClient,\n    handleChange\n  );\n  return () => {\n    unsubscribe();\n    unwatch?.();\n  };\n}\n\n// src/actions/contracts/writeContract.ts\nasync function writeContract(config2) {\n  const walletClient = await getWalletClient({ chainId: config2.chainId });\n  if (!walletClient)\n    throw new ConnectorNotFoundError();\n  if (config2.chainId)\n    assertActiveChain({ chainId: config2.chainId });\n  let request;\n  if (config2.mode === \"prepared\") {\n    request = config2.request;\n  } else {\n    const { chainId: _, mode: __, ...args } = config2;\n    const res = await prepareWriteContract(args);\n    request = res.request;\n  }\n  const hash = await walletClient.writeContract({\n    ...request,\n    chain: config2.chainId ? { id: config2.chainId } : null\n  });\n  return { hash };\n}\n\n// src/actions/accounts/fetchBalance.ts\nasync function fetchBalance({\n  address,\n  chainId,\n  formatUnits: unit,\n  token\n}) {\n  const config2 = getConfig();\n  const publicClient = getPublicClient({ chainId });\n  if (token) {\n    const fetchContractBalance = async ({ abi }) => {\n      const erc20Config = { abi, address: token, chainId };\n      const [value2, decimals, symbol] = await readContracts({\n        allowFailure: false,\n        contracts: [\n          {\n            ...erc20Config,\n            functionName: \"balanceOf\",\n            args: [address]\n          },\n          { ...erc20Config, functionName: \"decimals\" },\n          { ...erc20Config, functionName: \"symbol\" }\n        ]\n      });\n      return {\n        decimals,\n        formatted: formatUnits2(value2 ?? \"0\", getUnit(unit ?? decimals)),\n        symbol,\n        value: value2\n      };\n    };\n    try {\n      return await fetchContractBalance({ abi: erc20ABI });\n    } catch (err) {\n      if (err instanceof ContractFunctionExecutionError3) {\n        const { symbol, ...rest } = await fetchContractBalance({\n          abi: erc20ABI_bytes32\n        });\n        return {\n          symbol: hexToString2(trim2(symbol, { dir: \"right\" })),\n          ...rest\n        };\n      }\n      throw err;\n    }\n  }\n  const chains = [\n    ...config2.publicClient.chains || [],\n    ...config2.chains ?? []\n  ];\n  const value = await publicClient.getBalance({ address });\n  const chain = chains.find((x) => x.id === publicClient.chain.id);\n  return {\n    decimals: chain?.nativeCurrency.decimals ?? 18,\n    formatted: formatUnits2(value ?? \"0\", getUnit(unit ?? 18)),\n    symbol: chain?.nativeCurrency.symbol ?? \"ETH\",\n    value\n  };\n}\n\n// src/actions/accounts/getAccount.ts\nfunction getAccount() {\n  const { data, connector, status } = getConfig();\n  switch (status) {\n    case \"connected\":\n      return {\n        address: data?.account,\n        connector,\n        isConnected: true,\n        isConnecting: false,\n        isDisconnected: false,\n        isReconnecting: false,\n        status\n      };\n    case \"reconnecting\":\n      return {\n        address: data?.account,\n        connector,\n        isConnected: !!data?.account,\n        isConnecting: false,\n        isDisconnected: false,\n        isReconnecting: true,\n        status\n      };\n    case \"connecting\":\n      return {\n        address: data?.account,\n        connector,\n        isConnected: false,\n        isConnecting: true,\n        isDisconnected: false,\n        isReconnecting: false,\n        status\n      };\n    case \"disconnected\":\n      return {\n        address: void 0,\n        connector: void 0,\n        isConnected: false,\n        isConnecting: false,\n        isDisconnected: true,\n        isReconnecting: false,\n        status\n      };\n  }\n}\n\n// src/actions/accounts/getNetwork.ts\nfunction getNetwork() {\n  const config2 = getConfig();\n  const chainId = config2.data?.chain?.id;\n  const activeChains = config2.chains ?? [];\n  const activeChain = [\n    ...config2.publicClient?.chains || [],\n    ...activeChains\n  ].find((x) => x.id === chainId) ?? {\n    id: chainId,\n    name: `Chain ${chainId}`,\n    network: `${chainId}`,\n    nativeCurrency: { name: \"Ether\", decimals: 18, symbol: \"ETH\" },\n    rpcUrls: {\n      default: { http: [\"\"] },\n      public: { http: [\"\"] }\n    }\n  };\n  return {\n    chain: chainId ? {\n      ...activeChain,\n      ...config2.data?.chain,\n      id: chainId\n    } : void 0,\n    chains: activeChains\n  };\n}\n\n// src/actions/accounts/signMessage.ts\nasync function signMessage(args) {\n  const walletClient = await getWalletClient();\n  if (!walletClient)\n    throw new ConnectorNotFoundError();\n  return await walletClient.signMessage({\n    message: args.message\n  });\n}\n\n// src/actions/accounts/signTypedData.ts\nasync function signTypedData({\n  domain,\n  message,\n  primaryType,\n  types\n}) {\n  const walletClient = await getWalletClient();\n  if (!walletClient)\n    throw new ConnectorNotFoundError();\n  const { chainId } = domain;\n  if (chainId)\n    assertActiveChain({ chainId });\n  return walletClient.signTypedData({\n    message,\n    primaryType,\n    types,\n    domain\n  });\n}\n\n// src/actions/accounts/switchNetwork.ts\nasync function switchNetwork({\n  chainId\n}) {\n  const { connector } = getConfig();\n  if (!connector)\n    throw new ConnectorNotFoundError();\n  if (!connector.switchChain)\n    throw new SwitchChainNotSupportedError({\n      connector\n    });\n  return connector.switchChain(chainId);\n}\n\n// src/actions/accounts/watchAccount.ts\nimport { shallow as shallow4 } from \"zustand/shallow\";\nfunction watchAccount(callback, { selector = (x) => x } = {}) {\n  const config2 = getConfig();\n  const handleChange = () => callback(getAccount());\n  const unsubscribe = config2.subscribe(\n    ({ data, connector, status }) => selector({\n      address: data?.account,\n      connector,\n      status\n    }),\n    handleChange,\n    {\n      equalityFn: shallow4\n    }\n  );\n  return unsubscribe;\n}\n\n// src/actions/accounts/watchNetwork.ts\nimport { shallow as shallow5 } from \"zustand/shallow\";\nfunction watchNetwork(callback, { selector = (x) => x } = {}) {\n  const config2 = getConfig();\n  const handleChange = () => callback(getNetwork());\n  const unsubscribe = config2.subscribe(\n    ({ data, chains }) => selector({ chainId: data?.chain?.id, chains }),\n    handleChange,\n    {\n      equalityFn: shallow5\n    }\n  );\n  return unsubscribe;\n}\n\n// src/actions/ens/fetchEnsAddress.ts\nimport { getAddress } from \"viem\";\nasync function fetchEnsAddress({\n  chainId,\n  name\n}) {\n  const { normalize } = await import(\"viem/ens\");\n  const publicClient = getPublicClient({ chainId });\n  const address = await publicClient.getEnsAddress({\n    name: normalize(name)\n  });\n  try {\n    if (address === \"0x0000000000000000000000000000000000000000\")\n      return null;\n    return address ? getAddress(address) : null;\n  } catch (_error) {\n    return null;\n  }\n}\n\n// src/actions/ens/fetchEnsAvatar.ts\nasync function fetchEnsAvatar({\n  name,\n  chainId\n}) {\n  const { normalize } = await import(\"viem/ens\");\n  const publicClient = getPublicClient({ chainId });\n  const avatar = await publicClient.getEnsAvatar({ name: normalize(name) });\n  return avatar;\n}\n\n// src/actions/ens/fetchEnsName.ts\nimport { getAddress as getAddress2 } from \"viem\";\nasync function fetchEnsName({\n  address,\n  chainId\n}) {\n  const publicClient = getPublicClient({ chainId });\n  return publicClient.getEnsName({\n    address: getAddress2(address)\n  });\n}\n\n// src/actions/ens/fetchEnsResolver.ts\nasync function fetchEnsResolver({\n  chainId,\n  name\n}) {\n  const { normalize } = await import(\"viem/ens\");\n  const publicClient = getPublicClient({ chainId });\n  const resolver = await publicClient.getEnsResolver({ name: normalize(name) });\n  return resolver;\n}\n\n// src/actions/network-status/fetchBlockNumber.ts\nasync function fetchBlockNumber({\n  chainId\n} = {}) {\n  const publicClient = getPublicClient({ chainId });\n  const blockNumber = await publicClient.getBlockNumber();\n  return blockNumber;\n}\n\n// src/actions/network-status/fetchFeeData.ts\nimport { formatUnits as formatUnits3, parseGwei } from \"viem\";\nasync function fetchFeeData({\n  chainId,\n  formatUnits: units = \"gwei\"\n} = {}) {\n  const publicClient = getPublicClient({ chainId });\n  const block = await publicClient.getBlock();\n  let gasPrice = null;\n  try {\n    gasPrice = await publicClient.getGasPrice();\n  } catch {\n  }\n  let lastBaseFeePerGas = null;\n  let maxFeePerGas = null;\n  let maxPriorityFeePerGas = null;\n  if (block?.baseFeePerGas) {\n    lastBaseFeePerGas = block.baseFeePerGas;\n    maxPriorityFeePerGas = parseGwei(\"1\");\n    maxFeePerGas = block.baseFeePerGas * 2n + maxPriorityFeePerGas;\n  }\n  const unit = getUnit(units);\n  const formatted = {\n    gasPrice: gasPrice ? formatUnits3(gasPrice, unit) : null,\n    maxFeePerGas: maxFeePerGas ? formatUnits3(maxFeePerGas, unit) : null,\n    maxPriorityFeePerGas: maxPriorityFeePerGas ? formatUnits3(maxPriorityFeePerGas, unit) : null\n  };\n  return {\n    lastBaseFeePerGas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    formatted\n  };\n}\n\n// src/actions/transactions/fetchTransaction.ts\nasync function fetchTransaction({\n  chainId,\n  hash\n}) {\n  const publicClient = getPublicClient({ chainId });\n  return publicClient.getTransaction({ hash });\n}\n\n// src/actions/transactions/prepareSendTransaction.ts\nimport { isAddress } from \"viem\";\nasync function prepareSendTransaction({\n  accessList,\n  account,\n  chainId,\n  data,\n  gas: gas_,\n  gasPrice,\n  maxFeePerGas,\n  maxPriorityFeePerGas,\n  nonce,\n  to: to_,\n  value,\n  walletClient: walletClient_\n}) {\n  const publicClient = getPublicClient({ chainId });\n  const walletClient = walletClient_ ?? await getWalletClient({ chainId });\n  if (!walletClient)\n    throw new ConnectorNotFoundError();\n  if (chainId)\n    assertActiveChain({ chainId });\n  const to = (to_ && !isAddress(to_) ? await fetchEnsAddress({ name: to_ }) : to_) || void 0;\n  if (to && !isAddress(to))\n    throw new Error(\"Invalid address\");\n  const gas = typeof gas_ === \"undefined\" ? await publicClient.estimateGas({\n    accessList,\n    account: walletClient.account,\n    data,\n    gas: gas_ ?? void 0,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    to,\n    value\n  }) : gas_ || void 0;\n  return {\n    accessList,\n    account,\n    data,\n    gas,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    mode: \"prepared\",\n    nonce,\n    to,\n    value,\n    ...chainId ? { chainId } : {}\n  };\n}\n\n// src/actions/transactions/sendTransaction.ts\nasync function sendTransaction({\n  accessList,\n  account,\n  chainId,\n  data,\n  gas,\n  gasPrice,\n  maxFeePerGas,\n  maxPriorityFeePerGas,\n  mode,\n  nonce,\n  to,\n  value\n}) {\n  const walletClient = await getWalletClient({ chainId });\n  if (!walletClient)\n    throw new ConnectorNotFoundError();\n  if (chainId)\n    assertActiveChain({ chainId });\n  let args;\n  if (mode === \"prepared\") {\n    args = {\n      account,\n      accessList,\n      chain: null,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to,\n      value\n    };\n  } else {\n    args = await prepareSendTransaction({\n      accessList,\n      account,\n      chainId,\n      data,\n      gas: gas || null,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to,\n      value\n    });\n  }\n  const hash = await walletClient.sendTransaction({\n    ...args,\n    chain: chainId ? { id: chainId } : null\n  });\n  return { hash };\n}\n\n// src/actions/transactions/waitForTransaction.ts\nimport { hexToString as hexToString3 } from \"viem\";\nasync function waitForTransaction({\n  chainId,\n  confirmations = 1,\n  hash,\n  onReplaced,\n  timeout = 0\n}) {\n  const publicClient = getPublicClient({ chainId });\n  const receipt = await publicClient.waitForTransactionReceipt({\n    hash,\n    confirmations,\n    onReplaced,\n    timeout\n  });\n  if (receipt.status === \"reverted\") {\n    const txn = await publicClient.getTransaction({\n      hash: receipt.transactionHash\n    });\n    const code = await publicClient.call({\n      ...txn,\n      gasPrice: txn.type !== \"eip1559\" ? txn.gasPrice : void 0,\n      maxFeePerGas: txn.type === \"eip1559\" ? txn.maxFeePerGas : void 0,\n      maxPriorityFeePerGas: txn.type === \"eip1559\" ? txn.maxPriorityFeePerGas : void 0\n    });\n    const reason = hexToString3(`0x${code.substring(138)}`);\n    throw new Error(reason);\n  }\n  return receipt;\n}\n\n// src/actions/transactions/watchPendingTransactions.ts\nimport { shallow as shallow6 } from \"zustand/shallow\";\nfunction watchPendingTransactions(args, callback) {\n  let unwatch;\n  const createListener = (publicClient) => {\n    if (unwatch)\n      unwatch();\n    unwatch = publicClient.watchPendingTransactions({\n      onTransactions: callback,\n      poll: true\n    });\n  };\n  const publicClient_ = getWebSocketPublicClient({ chainId: args.chainId }) ?? getPublicClient({ chainId: args.chainId });\n  createListener(publicClient_);\n  const config2 = getConfig();\n  const unsubscribe = config2.subscribe(\n    ({ publicClient, webSocketPublicClient }) => ({\n      publicClient,\n      webSocketPublicClient\n    }),\n    async ({ publicClient, webSocketPublicClient }) => {\n      const publicClient_2 = webSocketPublicClient ?? publicClient;\n      if (!args.chainId && publicClient_2) {\n        createListener(publicClient_2);\n      }\n    },\n    {\n      equalityFn: shallow6\n    }\n  );\n  return () => {\n    unsubscribe();\n    unwatch?.();\n  };\n}\n\n// src/utils/assertActiveChain.ts\nfunction assertActiveChain({ chainId }) {\n  const { chain: activeChain, chains } = getNetwork();\n  const activeChainId = activeChain?.id;\n  if (activeChainId && chainId !== activeChainId) {\n    throw new ChainMismatchError({\n      activeChain: chains.find((x) => x.id === activeChainId)?.name ?? `Chain ${activeChainId}`,\n      targetChain: chains.find((x) => x.id === chainId)?.name ?? `Chain ${chainId}`\n    });\n  }\n}\n\nexport {\n  configureChains,\n  ChainMismatchError,\n  ChainNotConfiguredError,\n  ConnectorAlreadyConnectedError,\n  ConfigChainsNotFound,\n  SwitchChainNotSupportedError,\n  ConnectorNotFoundError,\n  deepEqual,\n  deserialize,\n  getCallParameters,\n  getSendTransactionParameters,\n  getUnit,\n  serialize,\n  noopStorage,\n  createStorage,\n  Config,\n  createConfig,\n  getConfig,\n  connect,\n  disconnect,\n  erc20ABI,\n  erc721ABI,\n  erc4626ABI,\n  fetchToken,\n  getPublicClient,\n  getWalletClient,\n  getWebSocketPublicClient,\n  watchPublicClient,\n  watchWalletClient,\n  watchWebSocketPublicClient,\n  prepareWriteContract,\n  getContract,\n  multicall,\n  readContract,\n  readContracts,\n  watchContractEvent,\n  watchBlockNumber,\n  watchMulticall,\n  watchReadContract,\n  watchReadContracts,\n  writeContract,\n  fetchBalance,\n  getAccount,\n  getNetwork,\n  signMessage,\n  signTypedData,\n  switchNetwork,\n  watchAccount,\n  watchNetwork,\n  fetchEnsAddress,\n  fetchEnsAvatar,\n  fetchEnsName,\n  fetchEnsResolver,\n  fetchBlockNumber,\n  fetchFeeData,\n  fetchTransaction,\n  prepareSendTransaction,\n  sendTransaction,\n  waitForTransaction,\n  watchPendingTransactions\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI,SAAS;AASb,aAAS,SAAS;AAAA,IAAC;AASnB,QAAI,OAAO,QAAQ;AACjB,aAAO,YAAY,uBAAO,OAAO,IAAI;AAMrC,UAAI,CAAC,IAAI,OAAO,EAAE;AAAW,iBAAS;AAAA,IACxC;AAWA,aAAS,GAAG,IAAI,SAAS,MAAM;AAC7B,WAAK,KAAK;AACV,WAAK,UAAU;AACf,WAAK,OAAO,QAAQ;AAAA,IACtB;AAaA,aAAS,YAAY,SAAS,OAAO,IAAI,SAAS,MAAM;AACtD,UAAI,OAAO,OAAO,YAAY;AAC5B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,WAAW,IAAI,GAAG,IAAI,WAAW,SAAS,IAAI,GAC9C,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,QAAQ,QAAQ,GAAG;AAAG,gBAAQ,QAAQ,GAAG,IAAI,UAAU,QAAQ;AAAA,eAC3D,CAAC,QAAQ,QAAQ,GAAG,EAAE;AAAI,gBAAQ,QAAQ,GAAG,EAAE,KAAK,QAAQ;AAAA;AAChE,gBAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,QAAQ,GAAG,GAAG,QAAQ;AAE3D,aAAO;AAAA,IACT;AASA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAI,EAAE,QAAQ,iBAAiB;AAAG,gBAAQ,UAAU,IAAI,OAAO;AAAA;AAC1D,eAAO,QAAQ,QAAQ,GAAG;AAAA,IACjC;AASA,aAASA,gBAAe;AACtB,WAAK,UAAU,IAAI,OAAO;AAC1B,WAAK,eAAe;AAAA,IACtB;AASA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,UAAI,QAAQ,CAAC,GACT,QACA;AAEJ,UAAI,KAAK,iBAAiB;AAAG,eAAO;AAEpC,WAAK,QAAS,SAAS,KAAK,SAAU;AACpC,YAAI,IAAI,KAAK,QAAQ,IAAI;AAAG,gBAAM,KAAK,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACtE;AAEA,UAAI,OAAO,uBAAuB;AAChC,eAAO,MAAM,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAC1D;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,OAAO;AAC3D,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,WAAW,KAAK,QAAQ,GAAG;AAE/B,UAAI,CAAC;AAAU,eAAO,CAAC;AACvB,UAAI,SAAS;AAAI,eAAO,CAAC,SAAS,EAAE;AAEpC,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK;AAClE,WAAG,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,gBAAgB,SAAS,cAAc,OAAO;AACnE,UAAI,MAAM,SAAS,SAAS,QAAQ,OAChC,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,CAAC;AAAW,eAAO;AACvB,UAAI,UAAU;AAAI,eAAO;AACzB,aAAO,UAAU;AAAA,IACnB;AASA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AACrE,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAE/B,UAAI,YAAY,KAAK,QAAQ,GAAG,GAC5B,MAAM,UAAU,QAChB,MACA;AAEJ,UAAI,UAAU,IAAI;AAChB,YAAI,UAAU;AAAM,eAAK,eAAe,OAAO,UAAU,IAAI,QAAW,IAAI;AAE5E,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,OAAO,GAAG;AAAA,UACrD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,EAAE,GAAG;AAAA,UACzD,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,EAAE,GAAG;AAAA,UAC7D,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,EAAE,GAAG;AAAA,UACjE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,UACrE,KAAK;AAAG,mBAAO,UAAU,GAAG,KAAK,UAAU,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAAA,QAC3E;AAEA,aAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAClD,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,QAC3B;AAEA,kBAAU,GAAG,MAAM,UAAU,SAAS,IAAI;AAAA,MAC5C,OAAO;AACL,YAAI,SAAS,UAAU,QACnB;AAEJ,aAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,cAAI,UAAU,CAAC,EAAE;AAAM,iBAAK,eAAe,OAAO,UAAU,CAAC,EAAE,IAAI,QAAW,IAAI;AAElF,kBAAQ,KAAK;AAAA,YACX,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,OAAO;AAAG;AAAA,YACpD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,EAAE;AAAG;AAAA,YACxD,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,EAAE;AAAG;AAAA,YAC5D,KAAK;AAAG,wBAAU,CAAC,EAAE,GAAG,KAAK,UAAU,CAAC,EAAE,SAAS,IAAI,IAAI,EAAE;AAAG;AAAA,YAChE;AACE,kBAAI,CAAC;AAAM,qBAAK,IAAI,GAAG,OAAO,IAAI,MAAM,MAAK,CAAC,GAAG,IAAI,KAAK,KAAK;AAC7D,uBAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAAA,gBAC3B;AAEA,wBAAU,CAAC,EAAE,GAAG,MAAM,UAAU,CAAC,EAAE,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAWA,IAAAA,cAAa,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,SAAS;AAC1D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,KAAK;AAAA,IACpD;AAWA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,OAAO,IAAI,SAAS;AAC9D,aAAO,YAAY,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,IACnD;AAYA,IAAAA,cAAa,UAAU,iBAAiB,SAAS,eAAe,OAAO,IAAI,SAAS,MAAM;AACxF,UAAI,MAAM,SAAS,SAAS,QAAQ;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AAAG,eAAO;AAC/B,UAAI,CAAC,IAAI;AACP,mBAAW,MAAM,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,KAAK,QAAQ,GAAG;AAEhC,UAAI,UAAU,IAAI;AAChB,YACE,UAAU,OAAO,OAChB,CAAC,QAAQ,UAAU,UACnB,CAAC,WAAW,UAAU,YAAY,UACnC;AACA,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,SAAS,CAAC,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AACvE,cACE,UAAU,CAAC,EAAE,OAAO,MACnB,QAAQ,CAAC,UAAU,CAAC,EAAE,QACtB,WAAW,UAAU,CAAC,EAAE,YAAY,SACrC;AACA,mBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF;AAKA,YAAI,OAAO;AAAQ,eAAK,QAAQ,GAAG,IAAI,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAAA;AACpE,qBAAW,MAAM,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AASA,IAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAmB,OAAO;AAC7E,UAAI;AAEJ,UAAI,OAAO;AACT,cAAM,SAAS,SAAS,QAAQ;AAChC,YAAI,KAAK,QAAQ,GAAG;AAAG,qBAAW,MAAM,GAAG;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,IAAI,OAAO;AAC1B,aAAK,eAAe;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAKA,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AACpD,IAAAA,cAAa,UAAU,cAAcA,cAAa,UAAU;AAK5D,IAAAA,cAAa,WAAW;AAKxB,IAAAA,cAAa,eAAeA;AAK5B,QAAI,gBAAgB,OAAO,QAAQ;AACjC,aAAO,UAAUA;AAAA,IACnB;AAAA;AAAA;;;AC9UA,IAAI,sCAAsC,cAAc,MAAM;AAAA,EAC5D,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,UAAU,OAAO,mCAAmC,WAAW,IAAI;AACzE,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,yBAAyB,cAAc,MAAM;AAAA,EAC/C,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACjB;AACF;;;ACQA,2BAAwC;AAxBxC,IAAI,gBAAgB,CAAC,KAAK,QAAQ,QAAQ;AACxC,MAAI,CAAC,OAAO,IAAI,GAAG;AACjB,UAAM,UAAU,YAAY,GAAG;AACnC;AACA,IAAIC,gBAAe,CAAC,KAAK,QAAQ,WAAW;AAC1C,gBAAc,KAAK,QAAQ,yBAAyB;AACpD,SAAO,SAAS,OAAO,KAAK,GAAG,IAAI,OAAO,IAAI,GAAG;AACnD;AACA,IAAIC,gBAAe,CAAC,KAAK,QAAQ,UAAU;AACzC,MAAI,OAAO,IAAI,GAAG;AAChB,UAAM,UAAU,mDAAmD;AACrE,oBAAkB,UAAU,OAAO,IAAI,GAAG,IAAI,OAAO,IAAI,KAAK,KAAK;AACrE;AACA,IAAIC,gBAAe,CAAC,KAAK,QAAQ,OAAO,WAAW;AACjD,gBAAc,KAAK,QAAQ,wBAAwB;AACnD,WAAS,OAAO,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,KAAK,KAAK;AACxD,SAAO;AACT;AACA,IAAIC,mBAAkB,CAAC,KAAK,QAAQ,WAAW;AAC7C,gBAAc,KAAK,QAAQ,uBAAuB;AAClD,SAAO;AACT;AAKA,IAAI,YAAY,cAAc,qBAAAC,QAAa;AAAA,EACzC,YAAY;AAAA,IACV,SAAS,CAAC,SAAS,MAAM;AAAA,IACzB;AAAA,EACF,GAAG;AACD,UAAM;AACN,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,qBAAqB,OAAO;AAC1B,UAAM,EAAE,SAAS,eAAe,GAAG,eAAe,IAAI,MAAM,kBAAkB,CAAC;AAC/E,QAAI;AACF,aAAO;AAAA,QACL,cAAc;AAAA,QACd,GAAG,OAAO,OAAO,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG;AAAA,MACnD;AAAA,EACJ;AAAA,EACA,mBAAmB,SAAS;AAC1B,WAAO,CAAC,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAAA,EAClD;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,UAAU;AAAA,EACjB;AACF;;;AChDA,SAAS,iBAAiB,SAAS;AACjC,MAAI,OAAO,YAAY;AACrB,WAAO,OAAO;AAAA,MACZ;AAAA,MACA,QAAQ,KAAK,EAAE,UAAU,GAAG,CAAC,MAAM,OAAO,KAAK;AAAA,IACjD;AACF,MAAI,OAAO,YAAY;AACrB,WAAO,OAAO,OAAO;AACvB,SAAO;AACT;;;ACgBA,SAAS,gBAAgB,UAAU;AA1BnC;AA2BE,MAAI,CAAC;AACH,WAAO;AACT,QAAM,UAAU,CAAC,aAAa;AAC5B,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS,eAAe,SAAS;AACnC,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS,sBAAsB,SAAS;AAC1C,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS,WAAW,SAAS;AAC/B,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AACT,QAAI,SAAS;AACX,aAAO;AAAA,EACX;AACA,OAAI,cAAS,cAAT,mBAAoB,QAAQ;AAC9B,UAAM,UAA0B,oBAAI,IAAI;AACxC,QAAI,eAAe;AACnB,eAAW,YAAY,SAAS,WAAW;AACzC,UAAI,OAAO,QAAQ,QAAQ;AAC3B,UAAI,CAAC,MAAM;AACT,eAAO,mBAAmB,YAAY;AACtC,wBAAgB;AAAA,MAClB;AACA,cAAQ,IAAI,IAAI;AAAA,IAClB;AACA,UAAM,QAAQ,CAAC,GAAG,OAAO;AACzB,QAAI,MAAM;AACR,aAAO;AACT,WAAO,MAAM,CAAC,KAAK;AAAA,EACrB;AACA,SAAO,QAAQ,QAAQ,KAAK;AAC9B;AAGA,IAAI;AACJ,IAAI,oBAAoB,cAAc,UAAU;AAAA,EAC9C,YAAY;AAAA,IACV;AAAA,IACA,SAAS;AAAA,EACX,IAAI,CAAC,GAAG;AACN,UAAM,UAAU;AAAA,MACd,gBAAgB;AAAA,MAChB,cAAc;AACZ,YAAI,OAAO,WAAW;AACpB;AACF,cAAM,WAAW,OAAO;AACxB,aAAI,qCAAU,cAAa,SAAS,UAAU,SAAS;AACrD,iBAAO,SAAS,UAAU,CAAC;AAC7B,eAAO;AAAA,MACT;AAAA,MACA,GAAG;AAAA,IACL;AACA,UAAM,EAAE,QAAQ,QAAQ,CAAC;AACzB,SAAK,KAAK;AACV,IAAAC,cAAa,MAAM,WAAW,MAAM;AACpC,SAAK,oBAAoB,GAAG,KAAK,EAAE;AACnC,SAAK,oBAAoB,CAAC,aAAa;AACrC,UAAI,SAAS,WAAW;AACtB,aAAK,KAAK,YAAY;AAAA;AAEtB,aAAK,KAAK,UAAU;AAAA,UAClB,SAAS,WAAW,SAAS,CAAC,CAAC;AAAA,QACjC,CAAC;AAAA,IACL;AACA,SAAK,iBAAiB,CAAC,YAAY;AACjC,YAAM,KAAK,iBAAiB,OAAO;AACnC,YAAM,cAAc,KAAK,mBAAmB,EAAE;AAC9C,WAAK,KAAK,UAAU,EAAE,OAAO,EAAE,IAAI,YAAY,EAAE,CAAC;AAAA,IACpD;AACA,SAAK,eAAe,OAAO,UAAU;AAtLzC;AAuLM,UAAI,MAAM,SAAS,MAAM;AACvB,cAAMC,YAAW,MAAM,KAAK,YAAY;AACxC,YAAIA,WAAU;AACZ,gBAAM,eAAe,MAAM,KAAK,WAAW;AAC3C,cAAI;AACF;AAAA,QACJ;AAAA,MACF;AACA,WAAK,KAAK,YAAY;AACtB,UAAI,KAAK,QAAQ;AACf,mBAAK,YAAL,mBAAc,WAAW,KAAK;AAAA,IAClC;AACA,UAAM,WAAW,QAAQ,YAAY;AACrC,QAAI,OAAO,QAAQ,SAAS;AAC1B,WAAK,OAAO,QAAQ;AAAA,aACb,UAAU;AACjB,YAAM,eAAe,gBAAgB,QAAQ;AAC7C,UAAI,QAAQ;AACV,aAAK,OAAO,QAAQ,KAAK,YAAY;AAAA,WAClC;AACH,YAAI,OAAO,iBAAiB;AAC1B,eAAK,OAAO;AAAA;AAEZ,eAAK,OAAO,aAAa,CAAC;AAAA,MAC9B;AAAA,IACF;AACE,WAAK,OAAO;AACd,SAAK,QAAQ,CAAC,CAAC;AAAA,EACjB;AAAA,EACA,MAAM,QAAQ,EAAE,QAAQ,IAAI,CAAC,GAAG;AApNlC;AAqNI,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,YAAY;AACxC,UAAI,CAAC;AACH,cAAM,IAAI,uBAAuB;AACnC,UAAI,SAAS,IAAI;AACf,iBAAS,GAAG,mBAAmB,KAAK,iBAAiB;AACrD,iBAAS,GAAG,gBAAgB,KAAK,cAAc;AAC/C,iBAAS,GAAG,cAAc,KAAK,YAAY;AAAA,MAC7C;AACA,WAAK,KAAK,WAAW,EAAE,MAAM,aAAa,CAAC;AAC3C,YAAM,WAAW,MAAM,SAAS,QAAQ;AAAA,QACtC,QAAQ;AAAA,MACV,CAAC;AACD,YAAM,UAAU,WAAW,SAAS,CAAC,CAAC;AACtC,UAAI,KAAK,MAAM,KAAK,WAAW;AAC/B,UAAI,cAAc,KAAK,mBAAmB,EAAE;AAC5C,UAAI,WAAW,OAAO,SAAS;AAC7B,cAAM,QAAQ,MAAM,KAAK,YAAY,OAAO;AAC5C,aAAK,MAAM;AACX,sBAAc,KAAK,mBAAmB,EAAE;AAAA,MAC1C;AACA,UAAI,KAAK,QAAQ;AACf,mBAAK,YAAL,mBAAc,QAAQ,KAAK,mBAAmB;AAChD,aAAO,EAAE,SAAS,OAAO,EAAE,IAAI,YAAY,EAAE;AAAA,IAC/C,SAAS,OAAO;AACd,UAAI,KAAK,2BAA2B,KAAK;AACvC,cAAM,IAAI,yBAAyB,KAAK;AAC1C,UAAI,MAAM,SAAS;AACjB,cAAM,IAAI,4BAA4B,KAAK;AAC7C,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,MAAM,aAAa;AArPrB;AAsPI,UAAM,WAAW,MAAM,KAAK,YAAY;AACxC,QAAI,EAAC,qCAAU;AACb;AACF,aAAS,eAAe,mBAAmB,KAAK,iBAAiB;AACjE,aAAS,eAAe,gBAAgB,KAAK,cAAc;AAC3D,aAAS,eAAe,cAAc,KAAK,YAAY;AACvD,QAAI,KAAK,QAAQ;AACf,iBAAK,YAAL,mBAAc,WAAW,KAAK;AAAA,EAClC;AAAA,EACA,MAAM,aAAa;AACjB,UAAM,WAAW,MAAM,KAAK,YAAY;AACxC,QAAI,CAAC;AACH,YAAM,IAAI,uBAAuB;AACnC,UAAM,WAAW,MAAM,SAAS,QAAQ;AAAA,MACtC,QAAQ;AAAA,IACV,CAAC;AACD,WAAO,WAAW,SAAS,CAAC,CAAC;AAAA,EAC/B;AAAA,EACA,MAAM,aAAa;AACjB,UAAM,WAAW,MAAM,KAAK,YAAY;AACxC,QAAI,CAAC;AACH,YAAM,IAAI,uBAAuB;AACnC,WAAO,SAAS,QAAQ,EAAE,QAAQ,cAAc,CAAC,EAAE,KAAK,gBAAgB;AAAA,EAC1E;AAAA,EACA,MAAM,cAAc;AAClB,UAAM,WAAW,KAAK,QAAQ,YAAY;AAC1C,QAAI;AACF,MAAAC,cAAa,MAAM,WAAW,QAAQ;AACxC,WAAOC,cAAa,MAAM,SAAS;AAAA,EACrC;AAAA,EACA,MAAM,gBAAgB;AAAA,IACpB;AAAA,EACF,IAAI,CAAC,GAAG;AACN,UAAM,CAAC,UAAU,OAAO,IAAI,MAAM,QAAQ,IAAI;AAAA,MAC5C,KAAK,YAAY;AAAA,MACjB,KAAK,WAAW;AAAA,IAClB,CAAC;AACD,UAAM,QAAQ,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACtD,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,uBAAuB;AACzC,WAAO,mBAAmB;AAAA,MACxB;AAAA,MACA;AAAA,MACA,WAAW,OAAO,QAAQ;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,MAAM,eAAe;AApSvB;AAqSI,QAAI;AACF,UAAI,KAAK,QAAQ,kBAAkB,GAAC,UAAK,YAAL,mBAAc,QAAQ,KAAK;AAC7D,eAAO;AACT,YAAM,WAAW,MAAM,KAAK,YAAY;AACxC,UAAI,CAAC;AACH,cAAM,IAAI,uBAAuB;AACnC,YAAM,UAAU,MAAM,KAAK,WAAW;AACtC,aAAO,CAAC,CAAC;AAAA,IACX,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,MAAM,YAAY,SAAS;AAjT7B;AAkTI,UAAM,WAAW,MAAM,KAAK,YAAY;AACxC,QAAI,CAAC;AACH,YAAM,IAAI,uBAAuB;AACnC,UAAM,KAAK,YAAY,OAAO;AAC9B,QAAI;AACF,YAAM,QAAQ,IAAI;AAAA,QAChB,SAAS,QAAQ;AAAA,UACf,QAAQ;AAAA,UACR,QAAQ,CAAC,EAAE,SAAS,GAAG,CAAC;AAAA,QAC1B,CAAC;AAAA,QACD,IAAI;AAAA,UACF,CAAC,QAAQ,KAAK,GAAG,UAAU,CAAC,EAAE,MAAM,MAAM;AACxC,iBAAI,+BAAO,QAAO;AAChB,kBAAI;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,KAAK;AAAA,QAClD,IAAI;AAAA,QACJ,MAAM,SAAS,EAAE;AAAA,QACjB,SAAS,GAAG,EAAE;AAAA,QACd,gBAAgB,EAAE,MAAM,SAAS,UAAU,IAAI,QAAQ,MAAM;AAAA,QAC7D,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE;AAAA,MAC7D;AAAA,IACF,SAAS,OAAO;AACd,YAAM,QAAQ,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACtD,UAAI,CAAC;AACH,cAAM,IAAI,oCAAoC;AAAA,UAC5C;AAAA,UACA,aAAa,KAAK;AAAA,QACpB,CAAC;AACH,UAAI,MAAM,SAAS,UAAQ,0CAAO,SAAP,mBAAa,kBAAb,mBAA4B,UAAS,MAAM;AACpE,YAAI;AACF,gBAAM,SAAS,QAAQ;AAAA,YACrB,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,gBACT,WAAW,MAAM;AAAA,gBACjB,gBAAgB,MAAM;AAAA,gBACtB,SAAS,GAAC,WAAM,QAAQ,WAAd,mBAAsB,KAAK,OAAM,EAAE;AAAA,gBAC7C,mBAAmB,KAAK,qBAAqB,KAAK;AAAA,cACpD;AAAA,YACF;AAAA,UACF,CAAC;AACD,gBAAM,iBAAiB,MAAM,KAAK,WAAW;AAC7C,cAAI,mBAAmB;AACrB,kBAAM,IAAI;AAAA,cACR,IAAI,MAAM,4CAA4C;AAAA,YACxD;AACF,iBAAO;AAAA,QACT,SAAS,QAAQ;AACf,gBAAM,IAAI,yBAAyB,MAAM;AAAA,QAC3C;AAAA,MACF;AACA,UAAI,KAAK,2BAA2B,KAAK;AACvC,cAAM,IAAI,yBAAyB,KAAK;AAC1C,YAAM,IAAI,iBAAiB,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EACA,MAAM,WAAW;AAAA,IACf;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,WAAW,MAAM,KAAK,YAAY;AACxC,QAAI,CAAC;AACH,YAAM,IAAI,uBAAuB;AACnC,WAAO,SAAS,QAAQ;AAAA,MACtB,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,2BAA2B,OAAO;AAChC,WAAO,MAAM,SAAS;AAAA,EACxB;AACF;AACA,YAAY,oBAAI,QAAQ;;;AC5KxB,IAAM,4BAA4B,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ;AAC3D,QAAM,gBAAgB,IAAI;AAC1B,MAAI,YAAY,CAAC,UAAU,aAAa,YAAY;AAClD,QAAI,WAAW;AACf,QAAI,aAAa;AACf,YAAM,cAAc,WAAW,OAAO,SAAS,QAAQ,eAAe,OAAO;AAC7E,UAAI,eAAe,SAAS,IAAI,SAAS,CAAC;AAC1C,iBAAW,CAAC,UAAU;AACpB,cAAM,YAAY,SAAS,KAAK;AAChC,YAAI,CAAC,WAAW,cAAc,SAAS,GAAG;AACxC,gBAAM,gBAAgB;AACtB,sBAAY,eAAe,WAAW,aAAa;AAAA,QACrD;AAAA,MACF;AACA,UAAI,WAAW,OAAO,SAAS,QAAQ,iBAAiB;AACtD,oBAAY,cAAc,YAAY;AAAA,MACxC;AAAA,IACF;AACA,WAAO,cAAc,QAAQ;AAAA,EAC/B;AACA,QAAM,eAAe,GAAG,KAAK,KAAK,GAAG;AACrC,SAAO;AACT;AACA,IAAM,wBAAwB;AAI9B,SAAS,kBAAkB,YAAY,SAAS;AAC9C,MAAI;AACJ,MAAI;AACF,cAAU,WAAW;AAAA,EACvB,SAAS,IAAI;AACX;AAAA,EACF;AACA,QAAM,iBAAiB;AAAA,IACrB,SAAS,CAAC,SAAS;AACjB,UAAI;AACJ,YAAM,QAAQ,CAAC,SAAS;AACtB,YAAI,SAAS,MAAM;AACjB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,MAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO;AAAA,MACpE;AACA,YAAM,OAAO,KAAK,QAAQ,QAAQ,IAAI,MAAM,OAAO,KAAK;AACxD,UAAI,eAAe,SAAS;AAC1B,eAAO,IAAI,KAAK,KAAK;AAAA,MACvB;AACA,aAAO,MAAM,GAAG;AAAA,IAClB;AAAA,IACA,SAAS,CAAC,MAAM,aAAa,QAAQ;AAAA,MACnC;AAAA,MACA,KAAK,UAAU,UAAU,WAAW,OAAO,SAAS,QAAQ,QAAQ;AAAA,IACtE;AAAA,IACA,YAAY,CAAC,SAAS,QAAQ,WAAW,IAAI;AAAA,EAC/C;AACA,SAAO;AACT;AACA,IAAM,aAAa,CAAC,OAAO,CAAC,UAAU;AACpC,MAAI;AACF,UAAM,SAAS,GAAG,KAAK;AACvB,QAAI,kBAAkB,SAAS;AAC7B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,KAAK,aAAa;AAChB,eAAO,WAAW,WAAW,EAAE,MAAM;AAAA,MACvC;AAAA,MACA,MAAM,aAAa;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,WAAO;AAAA,MACL,KAAK,cAAc;AACjB,eAAO;AAAA,MACT;AAAA,MACA,MAAM,YAAY;AAChB,eAAO,WAAW,UAAU,EAAE,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,UAAU,CAACC,SAAQ,gBAAgB,CAAC,KAAK,KAAK,QAAQ;AAC1D,MAAI,UAAU;AAAA,IACZ,YAAY,MAAM;AAAA,IAClB,WAAW,KAAK;AAAA,IAChB,aAAa,KAAK;AAAA,IAClB,YAAY,CAAC,UAAU;AAAA,IACvB,SAAS;AAAA,IACT,OAAO,CAAC,gBAAgB,kBAAkB;AAAA,MACxC,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,GAAG;AAAA,EACL;AACA,MAAI,cAAc;AAClB,QAAM,qBAAqC,oBAAI,IAAI;AACnD,QAAM,2BAA2C,oBAAI,IAAI;AACzD,MAAI;AACJ,MAAI;AACF,cAAU,QAAQ,WAAW;AAAA,EAC/B,SAAS,IAAI;AAAA,EACb;AACA,MAAI,CAAC,SAAS;AACZ,WAAOA;AAAA,MACL,IAAI,SAAS;AACX,gBAAQ;AAAA,UACN,uDAAuD,QAAQ,IAAI;AAAA,QACrE;AACA,YAAI,GAAG,IAAI;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,oBAAoB,WAAW,QAAQ,SAAS;AACtD,QAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,QAAQ,WAAW,EAAE,GAAG,IAAI,EAAE,CAAC;AAC7C,QAAI;AACJ,UAAM,WAAW,kBAAkB,EAAE,OAAO,SAAS,QAAQ,QAAQ,CAAC,EAAE;AAAA,MACtE,CAAC,oBAAoB,QAAQ,QAAQ,QAAQ,MAAM,eAAe;AAAA,IACpE,EAAE,MAAM,CAAC,MAAM;AACb,oBAAc;AAAA,IAChB,CAAC;AACD,QAAI,aAAa;AACf,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACA,QAAM,gBAAgB,IAAI;AAC1B,MAAI,WAAW,CAAC,OAAO,YAAY;AACjC,kBAAc,OAAO,OAAO;AAC5B,SAAK,QAAQ;AAAA,EACf;AACA,QAAM,eAAeA;AAAA,IACnB,IAAI,SAAS;AACX,UAAI,GAAG,IAAI;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI;AACJ,QAAM,UAAU,MAAM;AACpB,QAAI;AACJ,QAAI,CAAC;AAAS;AACd,kBAAc;AACd,uBAAmB,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;AAC5C,UAAM,4BAA4B,KAAK,QAAQ,uBAAuB,OAAO,SAAS,GAAG,KAAK,SAAS,IAAI,CAAC,MAAM;AAClH,WAAO,WAAW,QAAQ,QAAQ,KAAK,OAAO,CAAC,EAAE,QAAQ,IAAI,EAAE,KAAK,CAAC,iBAAiB;AACpF,UAAI,cAAc;AAChB,eAAO,QAAQ,YAAY,YAAY;AAAA,MACzC;AAAA,IACF,CAAC,EAAE,KAAK,CAAC,6BAA6B;AACpC,UAAI,0BAA0B;AAC5B,YAAI,OAAO,yBAAyB,YAAY,YAAY,yBAAyB,YAAY,QAAQ,SAAS;AAChH,cAAI,QAAQ,SAAS;AACnB,mBAAO,QAAQ;AAAA,cACb,yBAAyB;AAAA,cACzB,yBAAyB;AAAA,YAC3B;AAAA,UACF;AACA,kBAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO,yBAAyB;AAAA,QAClC;AAAA,MACF;AAAA,IACF,CAAC,EAAE,KAAK,CAAC,kBAAkB;AACzB,UAAI;AACJ,yBAAmB,QAAQ;AAAA,QACzB;AAAA,SACC,MAAM,IAAI,MAAM,OAAO,MAAM;AAAA,MAChC;AACA,UAAI,kBAAkB,IAAI;AAC1B,aAAO,QAAQ;AAAA,IACjB,CAAC,EAAE,KAAK,MAAM;AACZ,iCAA2B,OAAO,SAAS,wBAAwB,kBAAkB,MAAM;AAC3F,oBAAc;AACd,+BAAyB,QAAQ,CAAC,OAAO,GAAG,gBAAgB,CAAC;AAAA,IAC/D,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,iCAA2B,OAAO,SAAS,wBAAwB,QAAQ,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AACA,MAAI,UAAU;AAAA,IACZ,YAAY,CAAC,eAAe;AAC1B,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,UAAI,WAAW,YAAY;AACzB,kBAAU,WAAW,WAAW;AAAA,MAClC;AAAA,IACF;AAAA,IACA,cAAc,MAAM;AAClB,iBAAW,OAAO,SAAS,QAAQ,WAAW,QAAQ,IAAI;AAAA,IAC5D;AAAA,IACA,YAAY,MAAM;AAAA,IAClB,WAAW,MAAM,QAAQ;AAAA,IACzB,aAAa,MAAM;AAAA,IACnB,WAAW,CAAC,OAAO;AACjB,yBAAmB,IAAI,EAAE;AACzB,aAAO,MAAM;AACX,2BAAmB,OAAO,EAAE;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,OAAO;AACzB,+BAAyB,IAAI,EAAE;AAC/B,aAAO,MAAM;AACX,iCAAyB,OAAO,EAAE;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACA,UAAQ;AACR,SAAO,oBAAoB;AAC7B;AACA,IAAM,UAAU,CAACA,SAAQ,gBAAgB,CAAC,KAAK,KAAK,QAAQ;AAC1D,MAAI,UAAU;AAAA,IACZ,SAAS,kBAAkB,MAAM,YAAY;AAAA,IAC7C,YAAY,CAAC,UAAU;AAAA,IACvB,SAAS;AAAA,IACT,OAAO,CAAC,gBAAgB,kBAAkB;AAAA,MACxC,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,GAAG;AAAA,EACL;AACA,MAAI,cAAc;AAClB,QAAM,qBAAqC,oBAAI,IAAI;AACnD,QAAM,2BAA2C,oBAAI,IAAI;AACzD,MAAI,UAAU,QAAQ;AACtB,MAAI,CAAC,SAAS;AACZ,WAAOA;AAAA,MACL,IAAI,SAAS;AACX,gBAAQ;AAAA,UACN,uDAAuD,QAAQ,IAAI;AAAA,QACrE;AACA,YAAI,GAAG,IAAI;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,QAAQ,WAAW,EAAE,GAAG,IAAI,EAAE,CAAC;AAC7C,WAAO,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MACnC;AAAA,MACA,SAAS,QAAQ;AAAA,IACnB,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,IAAI;AAC1B,MAAI,WAAW,CAAC,OAAO,YAAY;AACjC,kBAAc,OAAO,OAAO;AAC5B,SAAK,QAAQ;AAAA,EACf;AACA,QAAM,eAAeA;AAAA,IACnB,IAAI,SAAS;AACX,UAAI,GAAG,IAAI;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,kBAAkB,MAAM;AAC5B,MAAI;AACJ,QAAM,UAAU,MAAM;AACpB,QAAI,IAAI;AACR,QAAI,CAAC;AAAS;AACd,kBAAc;AACd,uBAAmB,QAAQ,CAAC,OAAO;AACjC,UAAI;AACJ,aAAO,IAAI,MAAM,IAAI,MAAM,OAAO,MAAM,YAAY;AAAA,IACtD,CAAC;AACD,UAAM,4BAA4B,KAAK,QAAQ,uBAAuB,OAAO,SAAS,GAAG,KAAK,UAAU,KAAK,IAAI,MAAM,OAAO,KAAK,YAAY,MAAM;AACrJ,WAAO,WAAW,QAAQ,QAAQ,KAAK,OAAO,CAAC,EAAE,QAAQ,IAAI,EAAE,KAAK,CAAC,6BAA6B;AAChG,UAAI,0BAA0B;AAC5B,YAAI,OAAO,yBAAyB,YAAY,YAAY,yBAAyB,YAAY,QAAQ,SAAS;AAChH,cAAI,QAAQ,SAAS;AACnB,mBAAO;AAAA,cACL;AAAA,cACA,QAAQ;AAAA,gBACN,yBAAyB;AAAA,gBACzB,yBAAyB;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AACA,kBAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO,CAAC,OAAO,yBAAyB,KAAK;AAAA,QAC/C;AAAA,MACF;AACA,aAAO,CAAC,OAAO,MAAM;AAAA,IACvB,CAAC,EAAE,KAAK,CAAC,oBAAoB;AAC3B,UAAI;AACJ,YAAM,CAAC,UAAU,aAAa,IAAI;AAClC,yBAAmB,QAAQ;AAAA,QACzB;AAAA,SACC,MAAM,IAAI,MAAM,OAAO,MAAM;AAAA,MAChC;AACA,UAAI,kBAAkB,IAAI;AAC1B,UAAI,UAAU;AACZ,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC,EAAE,KAAK,MAAM;AACZ,iCAA2B,OAAO,SAAS,wBAAwB,kBAAkB,MAAM;AAC3F,yBAAmB,IAAI;AACvB,oBAAc;AACd,+BAAyB,QAAQ,CAAC,OAAO,GAAG,gBAAgB,CAAC;AAAA,IAC/D,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,iCAA2B,OAAO,SAAS,wBAAwB,QAAQ,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AACA,MAAI,UAAU;AAAA,IACZ,YAAY,CAAC,eAAe;AAC1B,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,UAAI,WAAW,SAAS;AACtB,kBAAU,WAAW;AAAA,MACvB;AAAA,IACF;AAAA,IACA,cAAc,MAAM;AAClB,iBAAW,OAAO,SAAS,QAAQ,WAAW,QAAQ,IAAI;AAAA,IAC5D;AAAA,IACA,YAAY,MAAM;AAAA,IAClB,WAAW,MAAM,QAAQ;AAAA,IACzB,aAAa,MAAM;AAAA,IACnB,WAAW,CAAC,OAAO;AACjB,yBAAmB,IAAI,EAAE;AACzB,aAAO,MAAM;AACX,2BAAmB,OAAO,EAAE;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,OAAO;AACzB,+BAAyB,IAAI,EAAE;AAC/B,aAAO,MAAM;AACX,iCAAyB,OAAO,EAAE;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,eAAe;AAC1B,YAAQ;AAAA,EACV;AACA,SAAO,oBAAoB;AAC7B;AACA,IAAM,cAAc,CAACA,SAAQ,gBAAgB;AAC3C,MAAI,gBAAgB,eAAe,eAAe,eAAe,iBAAiB,aAAa;AAC7F,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,cAAc;AACtE,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AACA,WAAO,QAAQA,SAAQ,WAAW;AAAA,EACpC;AACA,SAAO,QAAQA,SAAQ,WAAW;AACpC;AACA,IAAM,UAAU;;;ACpkBhB,IAAM,kBAAkB,CAAC,gBAAgB;AACvC,MAAI;AACJ,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,WAAW,CAAC,SAAS,YAAY;AACrC,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,CAAC,OAAO,GAAG,WAAW,KAAK,GAAG;AAChC,YAAM,gBAAgB;AACtB,eAAS,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,OAAO,SAAS;AAC1I,gBAAU,QAAQ,CAAC,aAAa,SAAS,OAAO,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACvB,QAAM,kBAAkB,MAAM;AAC9B,QAAM,YAAY,CAAC,aAAa;AAC9B,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM,UAAU,OAAO,QAAQ;AAAA,EACxC;AACA,QAAM,UAAU,MAAM;AACpB,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,cAAc;AACtE,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AACA,cAAU,MAAM;AAAA,EAClB;AACA,QAAM,MAAM,EAAE,UAAU,UAAU,iBAAiB,WAAW,QAAQ;AACtE,QAAM,eAAe,QAAQ,YAAY,UAAU,UAAU,GAAG;AAChE,SAAO;AACT;AACA,IAAM,cAAc,CAAC,gBAAgB,cAAc,gBAAgB,WAAW,IAAI;;;AC7BlF,SAAS,UAAU,MAAM,MAAM;AAC7B,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK;AAAM,aAAO;AACpC,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC/B,UAAI,CAAC,OAAO,GAAG,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK;AAAM,aAAO;AACpC,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG;AAC3F,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACvBA,SAAS,gBAAgB,eAAe,WAAW;AAAA,EACjD,QAAQ,EAAE,WAAW,EAAE,MAAM,GAAG,EAAE;AAAA,EAClC,kBAAkB;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,CAAC,GAAG;AACN,MAAI,CAAC,cAAc;AACjB,UAAM,IAAI,MAAM,8BAA8B;AAChD,MAAI,SAAS,CAAC;AACd,QAAM,WAAW,CAAC;AAClB,QAAM,SAAS,CAAC;AAChB,aAAW,SAAS,eAAe;AACjC,QAAI,eAAe;AACnB,eAAW,YAAY,WAAW;AAChC,YAAM,YAAY,SAAS,KAAK;AAChC,UAAI,CAAC;AACH;AACF,qBAAe;AACf,UAAI,CAAC,OAAO,KAAK,CAAC,EAAE,GAAG,MAAM,OAAO,MAAM,EAAE,GAAG;AAC7C,iBAAS,CAAC,GAAG,QAAQ,UAAU,KAAK;AAAA,MACtC;AACA,eAAS,MAAM,EAAE,IAAI;AAAA,QACnB,GAAG,SAAS,MAAM,EAAE,KAAK,CAAC;AAAA,QAC1B,GAAG,UAAU,QAAQ;AAAA,MACvB;AACA,UAAI,UAAU,QAAQ,WAAW;AAC/B,eAAO,MAAM,EAAE,IAAI;AAAA,UACjB,GAAG,OAAO,MAAM,EAAE,KAAK,CAAC;AAAA,UACxB,GAAG,UAAU,QAAQ;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI;AAAA,QACR;AAAA,UACE,0DAA0D,MAAM,IAAI;AAAA;AAAA,UAEpE;AAAA,UACA;AAAA,QACF,EAAE,KAAK,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,cAAc,CAAC,EAAE,QAAQ,MAAM;AAC7B,YAAM,cAAc,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,KAAK,cAAc,CAAC;AAC3E,YAAM,gBAAgB,SAAS,YAAY,EAAE;AAC7C,UAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;AACpC,cAAM,IAAI,MAAM,sCAAsC,YAAY,EAAE,GAAG;AACzE,YAAM,eAAe,mBAAmB;AAAA,QACtC;AAAA,QACA,OAAO;AAAA,QACP,WAAW;AAAA,UACT,cAAc,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE,SAAS,aAAa,CAAC,CAAC;AAAA,UAC/D,EAAE,MAAM,YAAY,WAAW;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AACD,aAAO,OAAO,OAAO,cAAc;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,uBAAuB,CAAC,EAAE,QAAQ,MAAM;AACtC,YAAM,cAAc,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,KAAK,cAAc,CAAC;AAC3E,YAAM,cAAc,OAAO,YAAY,EAAE;AACzC,UAAI,CAAC,eAAe,CAAC,YAAY,CAAC;AAChC,eAAO;AACT,YAAM,eAAe,mBAAmB;AAAA,QACtC;AAAA,QACA,OAAO;AAAA,QACP,WAAW;AAAA,UACT,YAAY,IAAI,CAAC,QAAQ,UAAU,KAAK,EAAE,SAAS,aAAa,CAAC,CAAC;AAAA,UAClE,EAAE,MAAM,YAAY,WAAW;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AACD,aAAO,OAAO,OAAO,cAAc;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAIA,IAAI,qBAAqB,cAAc,MAAM;AAAA,EAC3C,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG;AACD;AAAA,MACE,6BAA6B,WAAW,gBAAgB,WAAW;AAAA,IACrE;AACA,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,0BAA0B,cAAc,MAAM;AAAA,EAChD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG;AACD;AAAA,MACE,UAAU,OAAO,mBAAmB,cAAc,mBAAmB,WAAW,MAAM,EAAE;AAAA,IAC1F;AACA,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAI,iCAAiC,cAAc,MAAM;AAAA,EACvD,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACjB;AACF;AACA,IAAI,uBAAuB,cAAc,MAAM;AAAA,EAC7C,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACjB;AACF;AACA,IAAI,+BAA+B,cAAc,MAAM;AAAA,EACrD,YAAY,EAAE,UAAU,GAAG;AACzB,UAAM,IAAI,UAAU,IAAI,kDAAkD;AAC1E,SAAK,OAAO;AAAA,EACd;AACF;AAGA,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,MAAM;AACR,WAAO;AACT,MAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,QAAI,EAAE,gBAAgB,EAAE;AACtB,aAAO;AACT,QAAI;AACJ,QAAI;AACJ,QAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACxC,eAAS,EAAE;AACX,UAAI,UAAU,EAAE;AACd,eAAO;AACT,WAAK,IAAI,QAAQ,QAAQ;AACvB,YAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACvB,iBAAO;AACX,aAAO;AAAA,IACT;AACA,QAAI,EAAE,YAAY,OAAO,UAAU;AACjC,aAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AACnC,QAAI,EAAE,aAAa,OAAO,UAAU;AAClC,aAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AACrC,UAAM,OAAO,OAAO,KAAK,CAAC;AAC1B,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE;AAC5B,aAAO;AACT,SAAK,IAAI,QAAQ,QAAQ;AACvB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC;AAClD,eAAO;AACX,SAAK,IAAI,QAAQ,QAAQ,KAAK;AAC5B,YAAM,MAAM,KAAK,CAAC;AAClB,UAAI,OAAO,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAClC,eAAO;AAAA,IACX;AACA,WAAO;AAAA,EACT;AACA,SAAO,MAAM,KAAK,MAAM;AAC1B;AAGA,IAAI,iBAAiB,CAAC,UAAU;AAAA,EAC9B;AAAA,EACA;AACF,MAAM;AACJ,MAAI,YAAY,KAAK,QAAQ,GAAG;AAC9B,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,MAAI,OAAO,aAAa,UAAU;AAChC,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,WAAO,SAAS,IAAI,CAAC,SAAS,eAAe,MAAM,EAAE,MAAM,QAAQ,CAAC,CAAC;AAAA,EACvE;AACA,MAAI,oBAAoB,QAAQ;AAC9B,WAAO,OAAO,QAAQ,QAAQ,EAAE;AAAA,MAC9B,CAAC,MAAM,CAAC,KAAK,KAAK,OAAO;AAAA,QACvB,GAAG;AAAA,QACH,CAAC,GAAG,GAAG,eAAe,OAAO,EAAE,MAAM,QAAQ,CAAC;AAAA,MAChD;AAAA,MACA,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,cAAc;AACjC,QAAM,QAAQ,KAAK,MAAM,YAAY;AACrC,QAAM,+BAA+B,eAAe,OAAO;AAAA,IACzD,MAAM,CAAC,SAAS,OAAO,SAAS,YAAY,KAAK,WAAW,UAAU;AAAA,IACtE,SAAS,CAAC,SAAS,OAAO,KAAK,QAAQ,YAAY,EAAE,CAAC;AAAA,EACxD,CAAC;AACD,SAAO;AACT;AAGA,SAAS,kBAAkB,MAAM;AAC/B,SAAO;AAAA,IACL,YAAY,KAAK;AAAA,IACjB,SAAS,KAAK;AAAA,IACd,aAAa,KAAK;AAAA,IAClB,UAAU,KAAK;AAAA,IACf,MAAM,KAAK;AAAA,IACX,KAAK,KAAK;AAAA,IACV,UAAU,KAAK;AAAA,IACf,cAAc,KAAK;AAAA,IACnB,sBAAsB,KAAK;AAAA,IAC3B,OAAO,KAAK;AAAA,IACZ,IAAI,KAAK;AAAA,IACT,OAAO,KAAK;AAAA,EACd;AACF;AACA,SAAS,6BAA6B,MAAM;AAC1C,SAAO;AAAA,IACL,YAAY,KAAK;AAAA,IACjB,SAAS,KAAK;AAAA,IACd,MAAM,KAAK;AAAA,IACX,KAAK,KAAK;AAAA,IACV,UAAU,KAAK;AAAA,IACf,cAAc,KAAK;AAAA,IACnB,sBAAsB,KAAK;AAAA,IAC3B,OAAO,KAAK;AAAA,IACZ,IAAI,KAAK;AAAA,IACT,OAAO,KAAK;AAAA,EACd;AACF;AAIA,SAAS,QAAQ,MAAM;AACrB,MAAI,OAAO,SAAS;AAClB,WAAO;AACT,MAAI,SAAS;AACX,WAAO;AACT,SAAO,KAAK,IAAI,SAAS,IAAI,CAAC;AAChC;AAGA,SAAS,gBAAgB,MAAM,QAAQ;AACrC,SAAO,KAAK,MAAM,GAAG,MAAM,EAAE,KAAK,GAAG,KAAK;AAC5C;AACA,SAAS,UAAU,OAAO,OAAO;AAC/B,QAAM,EAAE,OAAO,IAAI;AACnB,WAAS,QAAQ,GAAG,QAAQ,QAAQ,EAAE,OAAO;AAC3C,QAAI,MAAM,KAAK,MAAM,OAAO;AAC1B,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,UAAU,kBAAkB;AAClD,QAAM,cAAc,OAAO,aAAa;AACxC,QAAM,sBAAsB,OAAO,qBAAqB;AACxD,QAAM,QAAQ,CAAC;AACf,QAAM,OAAO,CAAC;AACd,SAAO,SAAS,QAAQ,KAAK,OAAO;AAClC,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,MAAM,QAAQ;AAChB,cAAM,aAAa,UAAU,OAAO,IAAI;AACxC,YAAI,eAAe,GAAG;AACpB,gBAAM,MAAM,MAAM,IAAI;AAAA,QACxB,OAAO;AACL,gBAAM,OAAO,UAAU;AACvB,eAAK,OAAO,UAAU;AAAA,QACxB;AACA,aAAK,KAAK,MAAM,IAAI;AACpB,cAAM,cAAc,UAAU,OAAO,KAAK;AAC1C,YAAI,gBAAgB,GAAG;AACrB,iBAAO,sBAAsB,iBAAiB;AAAA,YAC5C;AAAA,YACA;AAAA,YACA;AAAA,YACA,gBAAgB,MAAM,WAAW;AAAA,UACnC,IAAI,QAAQ,gBAAgB,MAAM,WAAW,CAAC;AAAA,QAChD;AAAA,MACF,OAAO;AACL,cAAM,CAAC,IAAI;AACX,aAAK,CAAC,IAAI;AAAA,MACZ;AAAA,IACF;AACA,WAAO,cAAc,SAAS,KAAK,MAAM,KAAK,KAAK,IAAI;AAAA,EACzD;AACF;AACA,SAAS,UAAU,OAAO,UAAU,QAAQ,kBAAkB;AAC5D,SAAO,KAAK;AAAA,IACV;AAAA,IACA,eAAe,CAAC,KAAK,WAAW;AAC9B,YAAM,SAAS,OAAO,WAAW,WAAW,WAAW,OAAO,SAAS,CAAC,KAAK;AAC7E,cAAO,qCAAW,KAAK,YAAW;AAAA,IACpC,GAAG,gBAAgB;AAAA,IACnB,UAAU;AAAA,EACZ;AACF;AAOA,IAAI,cAAc;AAAA,EAChB,SAAS,CAAC,SAAS;AAAA,EACnB,SAAS,CAAC,MAAM,WAAW;AAAA,EAC3B,YAAY,CAAC,SAAS;AACxB;AACA,SAAS,cAAc;AAAA,EACrB,aAAa,eAAe;AAAA,EAC5B,KAAK,SAAS;AAAA,EACd,WAAW,aAAa;AAAA,EACxB;AACF,GAAG;AACD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS,CAAC,KAAK,eAAe,SAAS;AACrC,YAAM,QAAQ,QAAQ,QAAQ,GAAG,MAAM,IAAI,GAAG,EAAE;AAChD,UAAI;AACF,eAAO,QAAQ,aAAa,KAAK,IAAI;AAAA,MACvC,SAAS,OAAO;AACd,gBAAQ,KAAK,KAAK;AAClB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,SAAS,CAAC,KAAK,UAAU;AACvB,UAAI,UAAU,MAAM;AAClB,gBAAQ,WAAW,GAAG,MAAM,IAAI,GAAG,EAAE;AAAA,MACvC,OAAO;AACL,YAAI;AACF,kBAAQ,QAAQ,GAAG,MAAM,IAAI,GAAG,IAAI,WAAW,KAAK,CAAC;AAAA,QACvD,SAAS,KAAK;AACZ,kBAAQ,MAAM,GAAG;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY,CAAC,QAAQ,QAAQ,WAAW,GAAG,MAAM,IAAI,GAAG,EAAE;AAAA,EAC5D;AACF;AAGA,IAAI,WAAW;AACf,IAAI;AAAJ,IAAuB;AAAvB,IAA2C;AAA3C,IAAwD;AACxD,IAAI,SAAS,MAAM;AAAA,EACjB,YAAY;AAAA,IACV,cAAc;AAAA,IACd,aAAa,CAAC,IAAI,kBAAkB,CAAC;AAAA,IACrC;AAAA,IACA,UAAU,cAAc;AAAA,MACtB,SAAS,OAAO,WAAW,cAAc,OAAO,eAAe;AAAA,IACjE,CAAC;AAAA,IACD,SAAS;AAAA,MACP,MAAM,QAAQ;AAAA,IAChB;AAAA,IACA;AAAA,EACF,GAAG;AApXL;AAqXI,iBAAa,MAAM,WAAW;AAC9B,SAAK,gBAAgC,oBAAI,IAAI;AAC7C,SAAK,yBAAyC,oBAAI,IAAI;AACtD,iBAAa,MAAM,mBAAmB,MAAM;AAC5C,iBAAa,MAAM,oBAAoB,MAAM;AAC7C,SAAK,OAAO;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,SAAS;AACb,QAAI;AACJ,QAAI,aAAa;AACf,UAAI;AACF,cAAM,WAAW,QAAQ,QAAQ,QAAQ;AACzC,cAAM,QAAO,0CAAU,UAAV,mBAAiB;AAC9B,kBAAS,6BAAM,WAAU,iBAAiB;AAC1C,mBAAU,kCAAM,UAAN,mBAAa;AAAA,MACzB,SAAS,QAAQ;AAAA,MACjB;AAAA,IACF;AACA,UAAM,cAAc,OAAO,eAAe,aAAa,WAAW,IAAI;AACtE,gBAAY,QAAQ,CAAC,cAAc,UAAU,WAAW,OAAO,CAAC;AAChE,SAAK,QAAQ;AAAA,MACX;AAAA,QACE;AAAA,UACE,OAAO;AAAA,YACL,YAAY;AAAA,YACZ,cAAc,KAAK,gBAAgB,EAAE,QAAQ,CAAC;AAAA,YAC9C;AAAA,YACA,uBAAuB,KAAK,yBAAyB,EAAE,QAAQ,CAAC;AAAA,UAClE;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN;AAAA,YACA,YAAY,CAAC,UAAO;AA3ZhC,kBAAAC,KAAAC;AA2ZoC;AAAA,gBACtB,GAAG,eAAe;AAAA,kBAChB,MAAM;AAAA,oBACJ,UAASD,MAAA,+BAAO,SAAP,gBAAAA,IAAa;AAAA,oBACtB,QAAOC,MAAA,+BAAO,SAAP,gBAAAA,IAAa;AAAA,kBACtB;AAAA,gBACF;AAAA,gBACA,QAAQ,+BAAO;AAAA,cACjB;AAAA;AAAA,YACA,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,UAAU;AACf,iBAAa,MAAM,oBAAoB,mCAAS,QAAQ,SAAS;AACjE,oBAAgB,MAAM,aAAa,aAAa,EAAE,KAAK,IAAI;AAC3D,QAAI,eAAe,OAAO,WAAW;AACnC,iBAAW,YAAY,MAAM,KAAK,YAAY,GAAG,CAAC;AAAA,EACtD;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,MAAM,SAAS,EAAE;AAAA,EAC/B;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,MAAM,SAAS,EAAE;AAAA,EAC/B;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,MAAM,SAAS,EAAE;AAAA,EAC/B;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,MAAM,SAAS,EAAE;AAAA,EAC/B;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,MAAM,SAAS,EAAE;AAAA,EAC/B;AAAA,EACA,IAAI,kBAAkB;AA9bxB;AA+bI,YAAO,gBAAK,SAAL,mBAAW,UAAX,mBAAkB;AAAA,EAC3B;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,MAAM,SAAS,EAAE;AAAA,EAC/B;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,MAAM,SAAS,EAAE;AAAA,EAC/B;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,wBAAwB;AAC1B,WAAO,KAAK,MAAM,SAAS,EAAE;AAAA,EAC/B;AAAA,EACA,SAAS,SAAS;AAChB,UAAM,WAAW,OAAO,YAAY,aAAa,QAAQ,KAAK,MAAM,SAAS,CAAC,IAAI;AAClF,SAAK,MAAM,SAAS,UAAU,IAAI;AAAA,EACpC;AAAA,EACA,aAAa;AACX,SAAK,SAAS,CAAC,OAAO;AAAA,MACpB,GAAG;AAAA,MACH,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,EAAE;AAAA,EACJ;AAAA,EACA,MAAM,UAAU;AA3dlB;AA4dI,QAAI,KAAK;AACP,cAAM,gBAAK,WAAU,eAAf;AACR,iBAAa,MAAM,mBAAmB,KAAK;AAC3C,SAAK,WAAW;AAChB,SAAK,MAAM,QAAQ;AAAA,EACrB;AAAA,EACA,MAAM,cAAc;AAClB,QAAI,aAAa,MAAM,iBAAiB;AACtC;AACF,iBAAa,MAAM,mBAAmB,IAAI;AAC1C,SAAK,SAAS,CAAC,MAAG;AAtetB;AAse0B;AAAA,QACpB,GAAG;AAAA,QACH,UAAQ,OAAE,SAAF,mBAAQ,WAAU,iBAAiB;AAAA,MAC7C;AAAA,KAAE;AACF,UAAM,SAAS,aAAa,MAAM,kBAAkB,IAAI,CAAC,GAAG,KAAK,UAAU,EAAE;AAAA,MAC3E,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,kBAAkB,IAAI,KAAK;AAAA,IAChE,IAAI,KAAK;AACT,QAAI,YAAY;AAChB,eAAW,aAAa,QAAQ;AAC9B,UAAI,CAAC,UAAU,SAAS,CAAC,UAAU;AACjC;AACF,YAAM,eAAe,MAAM,UAAU,aAAa;AAClD,UAAI,CAAC;AACH;AACF,YAAM,OAAO,MAAM,UAAU,QAAQ;AACrC,WAAK,SAAS,CAAC,OAAO;AAAA,QACpB,GAAG;AAAA,QACH;AAAA,QACA,QAAQ,uCAAW;AAAA,QACnB;AAAA,QACA,QAAQ;AAAA,MACV,EAAE;AACF,kBAAY;AACZ;AAAA,IACF;AACA,QAAI,CAAC;AACH,WAAK,SAAS,CAAC,OAAO;AAAA,QACpB,GAAG;AAAA,QACH,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,EAAE;AACJ,iBAAa,MAAM,mBAAmB,KAAK;AAC3C,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,YAAY;AACxB,SAAK,OAAO;AAAA,MACV,GAAG,KAAK;AAAA,MACR;AAAA,IACF;AACA,UAAM,cAAc,OAAO,eAAe,aAAa,WAAW,IAAI;AACtE,gBAAY,QAAQ,CAAC,cAAc,UAAU,WAAW,KAAK,KAAK,OAAO,CAAC;AAC1E,SAAK,SAAS,CAAC,OAAO;AAAA,MACpB,GAAG;AAAA,MACH,YAAY;AAAA,IACd,EAAE;AAAA,EACJ;AAAA,EACA,gBAAgB,EAAE,QAAQ,IAAI,CAAC,GAAG;AAChC,QAAI,gBAAgB,KAAK,cAAc,IAAI,EAAE;AAC7C,QAAI,kBAAiB,+CAAe,MAAM,QAAO;AAC/C,aAAO;AACT,oBAAgB,KAAK,cAAc,IAAI,WAAW,EAAE;AACpD,QAAI;AACF,aAAO;AACT,UAAM,EAAE,aAAa,IAAI,KAAK;AAC9B,oBAAgB,OAAO,iBAAiB,aAAa,aAAa,EAAE,QAAQ,CAAC,IAAI;AACjF,SAAK,cAAc,IAAI,WAAW,IAAI,aAAa;AACnD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,cAAc;AAhiBhC;AAiiBI,UAAM,WAAU,gBAAK,SAAL,mBAAW,UAAX,mBAAkB;AAClC,SAAK,OAAO;AAAA,MACV,GAAG,KAAK;AAAA,MACR;AAAA,IACF;AACA,SAAK,cAAc,MAAM;AACzB,SAAK,SAAS,CAAC,OAAO;AAAA,MACpB,GAAG;AAAA,MACH,cAAc,KAAK,gBAAgB,EAAE,QAAQ,CAAC;AAAA,IAChD,EAAE;AAAA,EACJ;AAAA,EACA,yBAAyB,EAAE,QAAQ,IAAI,CAAC,GAAG;AACzC,QAAI,yBAAyB,KAAK,uBAAuB,IAAI,EAAE;AAC/D,QAAI,2BAA0B,iEAAwB,MAAM,QAAO;AACjE,aAAO;AACT,6BAAyB,KAAK,uBAAuB,IAAI,WAAW,EAAE;AACtE,QAAI;AACF,aAAO;AACT,UAAM,EAAE,sBAAsB,IAAI,KAAK;AACvC,6BAAyB,OAAO,0BAA0B,aAAa,sBAAsB,EAAE,QAAQ,CAAC,IAAI;AAC5G,QAAI;AACF,WAAK,uBAAuB,IAAI,WAAW,IAAI,sBAAsB;AACvE,WAAO;AAAA,EACT;AAAA,EACA,yBAAyB,uBAAuB;AAzjBlD;AA0jBI,UAAM,WAAU,gBAAK,SAAL,mBAAW,UAAX,mBAAkB;AAClC,SAAK,OAAO;AAAA,MACV,GAAG,KAAK;AAAA,MACR;AAAA,IACF;AACA,SAAK,uBAAuB,MAAM;AAClC,SAAK,SAAS,CAAC,OAAO;AAAA,MACpB,GAAG;AAAA,MACH,uBAAuB,KAAK,yBAAyB;AAAA,QACnD;AAAA,MACF,CAAC;AAAA,IACH,EAAE;AAAA,EACJ;AAAA,EACA,qBAAqB,oBAAoB,MAAM;AAvkBjD;AAwkBI,eAAK,YAAL,mBAAc,QAAQ,UAAU;AAAA,EAClC;AACF;AACA,oBAAoB,oBAAI,QAAQ;AAChC,qBAAqB,oBAAI,QAAQ;AACjC,cAAc,oBAAI,QAAQ;AAC1B,gBAAgB,WAAW;AACzB,QAAM,WAAW,CAAC,SAAS;AACzB,SAAK,SAAS,CAAC,OAAO;AAAA,MACpB,GAAG;AAAA,MACH,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,KAAK;AAAA,IAC7B,EAAE;AAAA,EACJ;AACA,QAAM,eAAe,MAAM;AACzB,SAAK,WAAW;AAAA,EAClB;AACA,QAAM,UAAU,CAAC,UAAU;AACzB,SAAK,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,MAAM,EAAE;AAAA,EACxC;AACA,OAAK,MAAM;AAAA,IACT,CAAC,EAAE,UAAU,MAAM;AAAA,IACnB,CAAC,WAAW,kBAAkB;AA7lBlC;AA8lBM,2DAAe,QAAf,uCAAqB,UAAU;AAC/B,2DAAe,QAAf,uCAAqB,cAAc;AACnC,2DAAe,QAAf,uCAAqB,SAAS;AAC9B,UAAI,CAAC;AACH;AACF,sBAAU,OAAV,mCAAe,UAAU;AACzB,sBAAU,OAAV,mCAAe,cAAc;AAC7B,sBAAU,OAAV,mCAAe,SAAS;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,EAAE,cAAc,sBAAsB,IAAI,KAAK;AACrD,QAAM,wBAAwB,OAAO,iBAAiB;AACtD,QAAM,iCAAiC,OAAO,0BAA0B;AACxE,MAAI,yBAAyB;AAC3B,SAAK,MAAM;AAAA,MACT,CAAC,EAAE,KAAK,MAAG;AA7mBjB;AA6mBoB,kDAAM,UAAN,mBAAa;AAAA;AAAA,MAC3B,CAAC,YAAY;AACX,aAAK,SAAS,CAAC,OAAO;AAAA,UACpB,GAAG;AAAA,UACH,cAAc,KAAK,gBAAgB,EAAE,QAAQ,CAAC;AAAA,UAC9C,uBAAuB,KAAK,yBAAyB;AAAA,YACnD;AAAA,UACF,CAAC;AAAA,QACH,EAAE;AAAA,MACJ;AAAA,IACF;AACJ;AACA,IAAI;AACJ,SAAS,aAAa,MAAM;AAC1B,QAAM,UAAU,IAAI,OAAO,IAAI;AAC/B,WAAS;AACT,SAAO;AACT;AACA,SAAS,YAAY;AACnB,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAGA,eAAe,QAAQ,EAAE,SAAS,UAAU,GAAG;AAC7C,QAAM,UAAU,UAAU;AAC1B,QAAM,kBAAkB,QAAQ;AAChC,MAAI,mBAAmB,UAAU,OAAO,gBAAgB;AACtD,UAAM,IAAI,+BAA+B;AAC3C,MAAI;AACF,YAAQ,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,QAAQ,aAAa,EAAE;AACxD,UAAM,OAAO,MAAM,UAAU,QAAQ,EAAE,QAAQ,CAAC;AAChD,YAAQ,qBAAqB,UAAU,EAAE;AACzC,YAAQ,SAAS,CAAC,OAAO;AAAA,MACvB,GAAG;AAAA,MACH;AAAA,MACA,QAAQ,uCAAW;AAAA,MACnB;AAAA,MACA,QAAQ;AAAA,IACV,EAAE;AACF,YAAQ,QAAQ,QAAQ,aAAa,IAAI;AACzC,WAAO,EAAE,GAAG,MAAM,UAAU;AAAA,EAC9B,SAAS,KAAK;AACZ,YAAQ,SAAS,CAAC,MAAM;AACtB,aAAO;AAAA,QACL,GAAG;AAAA,QACH,QAAQ,EAAE,YAAY,cAAc;AAAA,MACtC;AAAA,IACF,CAAC;AACD,UAAM;AAAA,EACR;AACF;AAGA,eAAe,aAAa;AAC1B,QAAM,UAAU,UAAU;AAC1B,MAAI,QAAQ;AACV,UAAM,QAAQ,UAAU,WAAW;AACrC,UAAQ,WAAW;AACnB,UAAQ,QAAQ,WAAW,WAAW;AACxC;AAWA,IAAI,WAAW;AAAA,EACb;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,mBAAmB;AAAA,EACrB;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,YAAY;AAAA,EACd;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS,CAAC;AAAA,EACZ;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,SAAS,CAAC;AAAA,EACZ;AACF;AACA,IAAI,aAAa;AAAA,EACf;AAAA,IACE,WAAW;AAAA,IACX,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,WAAW;AAAA,IACX,QAAQ;AAAA,MACN;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ,CAAC;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ,CAAC;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ,CAAC;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA;AAAA,IACE,QAAQ;AAAA,MACN;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,IACjB,MAAM;AAAA,EACR;AACF;AASA,eAAe,WAAW;AAAA,EACxB;AAAA,EACA;AAAA,EACA,aAAa,OAAO;AACtB,GAAG;AACD,iBAAe,YAAY,EAAE,IAAI,GAAG;AAClC,UAAM,cAAc,EAAE,SAAS,KAAK,QAAQ;AAC5C,UAAM,CAAC,UAAU,MAAM,QAAQ,WAAW,IAAI,MAAM,cAAc;AAAA,MAChE,cAAc;AAAA,MACd,WAAW;AAAA,QACT,EAAE,GAAG,aAAa,cAAc,WAAW;AAAA,QAC3C,EAAE,GAAG,aAAa,cAAc,OAAO;AAAA,QACvC,EAAE,GAAG,aAAa,cAAc,SAAS;AAAA,QACzC,EAAE,GAAG,aAAa,cAAc,cAAc;AAAA,MAChD;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa;AAAA,QACX,WAAW,YAAY,aAAa,QAAQ,IAAI,CAAC;AAAA,QACjD,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI;AACF,WAAO,MAAM,YAAY,EAAE,KAAK,SAAS,CAAC;AAAA,EAC5C,SAAS,KAAK;AACZ,QAAI,eAAe,gCAAgC;AACjD,YAAM,EAAE,MAAM,QAAQ,GAAG,KAAK,IAAI,MAAM,YAAY;AAAA,QAClD,KAAK;AAAA,MACP,CAAC;AACD,aAAO;AAAA,QACL,MAAM,YAAY,KAAK,MAAM,EAAE,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC9C,QAAQ,YAAY,KAAK,QAAQ,EAAE,KAAK,QAAQ,CAAC,CAAC;AAAA,QAClD,GAAG;AAAA,MACL;AAAA,IACF;AACA,UAAM;AAAA,EACR;AACF;AAGA,SAAS,gBAAgB,EAAE,QAAQ,IAAI,CAAC,GAAG;AACzC,QAAM,UAAU,UAAU;AAC1B,MAAI;AACF,WAAO,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,KAAK,QAAQ;AACzD,SAAO,QAAQ;AACjB;AAGA,eAAe,gBAAgB;AAAA,EAC7B;AACF,IAAI,CAAC,GAAG;AAj8DR;AAk8DE,QAAM,UAAU,UAAU;AAC1B,QAAM,eAAe,QAAM,mBAAQ,cAAR,mBAAmB,oBAAnB,4BAAqC,EAAE,QAAQ,OAAM;AAChF,SAAO;AACT;AAGA,SAAS,yBAAyB;AAAA,EAChC;AACF,IAAI,CAAC,GAAG;AACN,QAAM,UAAU,UAAU;AAC1B,MAAI;AACF,WAAO,QAAQ,yBAAyB,EAAE,QAAQ,CAAC,KAAK,QAAQ;AAClE,SAAO,QAAQ;AACjB;AAGA,SAAS,kBAAkB,MAAM,UAAU;AACzC,QAAM,UAAU,UAAU;AAC1B,QAAM,eAAe,YAAY,SAAS,gBAAgB,IAAI,CAAC;AAC/D,QAAM,cAAc,QAAQ;AAAA,IAC1B,CAAC,EAAE,aAAa,MAAM;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AAIA,SAAS,kBAAkB,EAAE,QAAQ,GAAG,UAAU;AAChD,QAAM,UAAU,UAAU;AAC1B,QAAM,eAAe,OAAO,EAAE,SAAS,SAAS,MAAM;AACpD,QAAI,WAAW,YAAY,YAAY;AACrC;AACF,UAAM,eAAe,MAAM,gBAAgB,EAAE,QAAQ,CAAC;AACtD,QAAI,CAAC,UAAU,EAAE;AACf,aAAO,SAAS,IAAI;AACtB,WAAO,SAAS,YAAY;AAAA,EAC9B;AACA,QAAM,cAAc,QAAQ;AAAA,IAC1B,CAAC,EAAE,MAAM,UAAU,MAAG;AAz+D1B;AAy+D8B;AAAA,QACxB,SAAS,6BAAM;AAAA,QACf,UAAS,kCAAM,UAAN,mBAAa;AAAA,QACtB;AAAA,MACF;AAAA;AAAA,IACA;AAAA,IACA;AAAA,MACE,YAAY;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,2BAA2B,MAAM,UAAU;AAClD,QAAM,UAAU,UAAU;AAC1B,QAAM,eAAe,YAAY,SAAS,yBAAyB,IAAI,CAAC;AACxE,QAAM,cAAc,QAAQ;AAAA,IAC1B,CAAC,EAAE,sBAAsB,MAAM;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AAGA,eAAe,qBAAqB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd,GAAG;AACL,GAAG;AACD,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,eAAe,iBAAiB,MAAM,gBAAgB,EAAE,QAAQ,CAAC;AACvE,MAAI,CAAC;AACH,UAAM,IAAI,uBAAuB;AACnC,MAAI;AACF,sBAAkB,EAAE,QAAQ,CAAC;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,kBAAkB,OAAO;AAC7B,QAAM,EAAE,QAAQ,QAAQ,IAAI,MAAM,aAAa,iBAAiB;AAAA,IAC9D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,WAAW,aAAa;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,eAAe,IAAI;AAAA,IACvB,CAAC,YAAY,UAAU,WAAW,QAAQ,SAAS;AAAA,EACrD;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,MACP,GAAG;AAAA,MACH,KAAK;AAAA,MACL;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF;AAoBA,eAAe,UAAU;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAG;AACD,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,qBAAqB;AACjC,MAAI,WAAW,aAAa,MAAM,OAAO;AACvC,UAAM,IAAI,wBAAwB,EAAE,QAAQ,CAAC;AAC/C,SAAO,aAAa,UAAU;AAAA,IAC5B,cAAc,KAAK,gBAAgB;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAGA,eAAe,aAAa;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,SAAO,aAAa,aAAa;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,eAAe,cAAc;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAG;AACD,QAAM,EAAE,eAAe,KAAK,IAAI;AAChC,MAAI;AACF,UAAM,eAAe,gBAAgB;AACrC,UAAM,qBAAqB,UAAU,OAAO,CAAC,YAAY,UAAU,UAAU;AAC3E,YAAM,UAAU,SAAS,WAAW,aAAa,MAAM;AACvD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,CAAC,OAAO,GAAG,CAAC,GAAG,WAAW,OAAO,KAAK,CAAC,GAAG,EAAE,UAAU,MAAM,CAAC;AAAA,MAC/D;AAAA,IACF,GAAG,CAAC,CAAC;AACL,UAAM,WAAW,MAAM,OAAO,QAAQ,kBAAkB,EAAE;AAAA,MACxD,CAAC,CAAC,SAAS,UAAU,MAAM,UAAU;AAAA,QACnC;AAAA,QACA,SAAS,SAAS,OAAO;AAAA,QACzB,WAAW,WAAW;AAAA,UACpB,CAAC,EAAE,SAAS,MAAM;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,oBAAoB,MAAM,QAAQ,IAAI,SAAS,CAAC,GAAG,KAAK;AAC9D,UAAM,gBAAgB,OAAO,OAAO,kBAAkB,EAAE;AAAA,MACtD,CAAC,eAAe,WAAW,IAAI,CAAC,EAAE,MAAM,MAAM,KAAK;AAAA,IACrD;AACA,WAAO,iBAAiB,OAAO,CAAC,SAAS,QAAQ,UAAU;AACzD,UAAI;AACF,gBAAQ,cAAc,KAAK,CAAC,IAAI;AAClC,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP,SAAS,KAAK;AACZ,QAAI,eAAe;AACjB,YAAM;AACR,UAAM,WAAW,MAAM,UAAU;AAAA,MAC/B,CAAC,aAAa,aAAa,EAAE,GAAG,UAAU,aAAa,SAAS,CAAC;AAAA,IACnE;AACA,QAAI;AACF,cAAQ,MAAM,QAAQ,WAAW,SAAS,CAAC,GAAG,IAAI,CAAC,WAAW;AAC5D,YAAI,OAAO,WAAW;AACpB,iBAAO,EAAE,QAAQ,OAAO,OAAO,QAAQ,UAAU;AACnD,eAAO,EAAE,OAAO,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,UAAU;AAAA,MACnE,CAAC;AACH,WAAO,MAAM,QAAQ,IAAI,SAAS,CAAC;AAAA,EACrC;AACF;AA0HA,eAAe,cAAc,SAAS;AACpC,QAAM,eAAe,MAAM,gBAAgB,EAAE,SAAS,QAAQ,QAAQ,CAAC;AACvE,MAAI,CAAC;AACH,UAAM,IAAI,uBAAuB;AACnC,MAAI,QAAQ;AACV,sBAAkB,EAAE,SAAS,QAAQ,QAAQ,CAAC;AAChD,MAAI;AACJ,MAAI,QAAQ,SAAS,YAAY;AAC/B,cAAU,QAAQ;AAAA,EACpB,OAAO;AACL,UAAM,EAAE,SAAS,GAAG,MAAM,IAAI,GAAG,KAAK,IAAI;AAC1C,UAAM,MAAM,MAAM,qBAAqB,IAAI;AAC3C,cAAU,IAAI;AAAA,EAChB;AACA,QAAM,OAAO,MAAM,aAAa,cAAc;AAAA,IAC5C,GAAG;AAAA,IACH,OAAO,QAAQ,UAAU,EAAE,IAAI,QAAQ,QAAQ,IAAI;AAAA,EACrD,CAAC;AACD,SAAO,EAAE,KAAK;AAChB;AAGA,eAAe,aAAa;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb;AACF,GAAG;AACD,QAAM,UAAU,UAAU;AAC1B,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,MAAI,OAAO;AACT,UAAM,uBAAuB,OAAO,EAAE,IAAI,MAAM;AAC9C,YAAM,cAAc,EAAE,KAAK,SAAS,OAAO,QAAQ;AACnD,YAAM,CAAC,QAAQ,UAAU,MAAM,IAAI,MAAM,cAAc;AAAA,QACrD,cAAc;AAAA,QACd,WAAW;AAAA,UACT;AAAA,YACE,GAAG;AAAA,YACH,cAAc;AAAA,YACd,MAAM,CAAC,OAAO;AAAA,UAChB;AAAA,UACA,EAAE,GAAG,aAAa,cAAc,WAAW;AAAA,UAC3C,EAAE,GAAG,aAAa,cAAc,SAAS;AAAA,QAC3C;AAAA,MACF,CAAC;AACD,aAAO;AAAA,QACL;AAAA,QACA,WAAW,YAAa,UAAU,KAAK,QAAQ,QAAQ,QAAQ,CAAC;AAAA,QAChE;AAAA,QACA,OAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI;AACF,aAAO,MAAM,qBAAqB,EAAE,KAAK,SAAS,CAAC;AAAA,IACrD,SAAS,KAAK;AACZ,UAAI,eAAe,gCAAiC;AAClD,cAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,MAAM,qBAAqB;AAAA,UACrD,KAAK;AAAA,QACP,CAAC;AACD,eAAO;AAAA,UACL,QAAQ,YAAa,KAAM,QAAQ,EAAE,KAAK,QAAQ,CAAC,CAAC;AAAA,UACpD,GAAG;AAAA,QACL;AAAA,MACF;AACA,YAAM;AAAA,IACR;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,GAAG,QAAQ,aAAa,UAAU,CAAC;AAAA,IACnC,GAAG,QAAQ,UAAU,CAAC;AAAA,EACxB;AACA,QAAM,QAAQ,MAAM,aAAa,WAAW,EAAE,QAAQ,CAAC;AACvD,QAAM,QAAQ,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,EAAE;AAC/D,SAAO;AAAA,IACL,WAAU,+BAAO,eAAe,aAAY;AAAA,IAC5C,WAAW,YAAa,SAAS,KAAK,QAAQ,QAAQ,EAAE,CAAC;AAAA,IACzD,SAAQ,+BAAO,eAAe,WAAU;AAAA,IACxC;AAAA,EACF;AACF;AAGA,SAAS,aAAa;AACpB,QAAM,EAAE,MAAM,WAAW,OAAO,IAAI,UAAU;AAC9C,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO;AAAA,QACL,SAAS,6BAAM;AAAA,QACf;AAAA,QACA,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,SAAS,6BAAM;AAAA,QACf;AAAA,QACA,aAAa,CAAC,EAAC,6BAAM;AAAA,QACrB,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,SAAS,6BAAM;AAAA,QACf;AAAA,QACA,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,SAAS;AAAA,QACT,WAAW;AAAA,QACX,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB;AAAA,MACF;AAAA,EACJ;AACF;AAGA,SAAS,aAAa;AAz6EtB;AA06EE,QAAM,UAAU,UAAU;AAC1B,QAAM,WAAU,mBAAQ,SAAR,mBAAc,UAAd,mBAAqB;AACrC,QAAM,eAAe,QAAQ,UAAU,CAAC;AACxC,QAAM,cAAc;AAAA,IAClB,KAAG,aAAQ,iBAAR,mBAAsB,WAAU,CAAC;AAAA,IACpC,GAAG;AAAA,EACL,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,KAAK;AAAA,IACjC,IAAI;AAAA,IACJ,MAAM,SAAS,OAAO;AAAA,IACtB,SAAS,GAAG,OAAO;AAAA,IACnB,gBAAgB,EAAE,MAAM,SAAS,UAAU,IAAI,QAAQ,MAAM;AAAA,IAC7D,SAAS;AAAA,MACP,SAAS,EAAE,MAAM,CAAC,EAAE,EAAE;AAAA,MACtB,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO,UAAU;AAAA,MACf,GAAG;AAAA,MACH,IAAG,aAAQ,SAAR,mBAAc;AAAA,MACjB,IAAI;AAAA,IACN,IAAI;AAAA,IACJ,QAAQ;AAAA,EACV;AACF;AAGA,eAAe,YAAY,MAAM;AAC/B,QAAM,eAAe,MAAM,gBAAgB;AAC3C,MAAI,CAAC;AACH,UAAM,IAAI,uBAAuB;AACnC,SAAO,MAAM,aAAa,YAAY;AAAA,IACpC,SAAS,KAAK;AAAA,EAChB,CAAC;AACH;AAGA,eAAe,cAAc;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,eAAe,MAAM,gBAAgB;AAC3C,MAAI,CAAC;AACH,UAAM,IAAI,uBAAuB;AACnC,QAAM,EAAE,QAAQ,IAAI;AACpB,MAAI;AACF,sBAAkB,EAAE,QAAQ,CAAC;AAC/B,SAAO,aAAa,cAAc;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAGA,eAAe,cAAc;AAAA,EAC3B;AACF,GAAG;AACD,QAAM,EAAE,UAAU,IAAI,UAAU;AAChC,MAAI,CAAC;AACH,UAAM,IAAI,uBAAuB;AACnC,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,6BAA6B;AAAA,MACrC;AAAA,IACF,CAAC;AACH,SAAO,UAAU,YAAY,OAAO;AACtC;AAIA,SAAS,aAAa,UAAU,EAAE,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;AAC5D,QAAM,UAAU,UAAU;AAC1B,QAAM,eAAe,MAAM,SAAS,WAAW,CAAC;AAChD,QAAM,cAAc,QAAQ;AAAA,IAC1B,CAAC,EAAE,MAAM,WAAW,OAAO,MAAM,SAAS;AAAA,MACxC,SAAS,6BAAM;AAAA,MACf;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD;AAAA,IACA;AAAA,MACE,YAAY;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAIA,SAAS,aAAa,UAAU,EAAE,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;AAC5D,QAAM,UAAU,UAAU;AAC1B,QAAM,eAAe,MAAM,SAAS,WAAW,CAAC;AAChD,QAAM,cAAc,QAAQ;AAAA,IAC1B,CAAC,EAAE,MAAM,OAAO,MAAG;AA1gFvB;AA0gF0B,sBAAS,EAAE,UAAS,kCAAM,UAAN,mBAAa,IAAI,OAAO,CAAC;AAAA;AAAA,IACnE;AAAA,IACA;AAAA,MACE,YAAY;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAIA,eAAe,gBAAgB;AAAA,EAC7B;AAAA,EACA;AACF,GAAG;AACD,QAAM,EAAE,UAAU,IAAI,MAAM,OAAO,mBAAU;AAC7C,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,UAAU,MAAM,aAAa,cAAc;AAAA,IAC/C,MAAM,UAAU,IAAI;AAAA,EACtB,CAAC;AACD,MAAI;AACF,QAAI,YAAY;AACd,aAAO;AACT,WAAO,UAAU,WAAW,OAAO,IAAI;AAAA,EACzC,SAAS,QAAQ;AACf,WAAO;AAAA,EACT;AACF;AAGA,eAAe,eAAe;AAAA,EAC5B;AAAA,EACA;AACF,GAAG;AACD,QAAM,EAAE,UAAU,IAAI,MAAM,OAAO,mBAAU;AAC7C,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,SAAS,MAAM,aAAa,aAAa,EAAE,MAAM,UAAU,IAAI,EAAE,CAAC;AACxE,SAAO;AACT;AAIA,eAAe,aAAa;AAAA,EAC1B;AAAA,EACA;AACF,GAAG;AACD,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,SAAO,aAAa,WAAW;AAAA,IAC7B,SAAS,WAAY,OAAO;AAAA,EAC9B,CAAC;AACH;AAGA,eAAe,iBAAiB;AAAA,EAC9B;AAAA,EACA;AACF,GAAG;AACD,QAAM,EAAE,UAAU,IAAI,MAAM,OAAO,mBAAU;AAC7C,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,WAAW,MAAM,aAAa,eAAe,EAAE,MAAM,UAAU,IAAI,EAAE,CAAC;AAC5E,SAAO;AACT;AAGA,eAAe,iBAAiB;AAAA,EAC9B;AACF,IAAI,CAAC,GAAG;AACN,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,cAAc,MAAM,aAAa,eAAe;AACtD,SAAO;AACT;AAIA,eAAe,aAAa;AAAA,EAC1B;AAAA,EACA,aAAa,QAAQ;AACvB,IAAI,CAAC,GAAG;AACN,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,QAAQ,MAAM,aAAa,SAAS;AAC1C,MAAI,WAAW;AACf,MAAI;AACF,eAAW,MAAM,aAAa,YAAY;AAAA,EAC5C,QAAQ;AAAA,EACR;AACA,MAAI,oBAAoB;AACxB,MAAI,eAAe;AACnB,MAAI,uBAAuB;AAC3B,MAAI,+BAAO,eAAe;AACxB,wBAAoB,MAAM;AAC1B,2BAAuB,UAAU,GAAG;AACpC,mBAAe,MAAM,gBAAgB,KAAK;AAAA,EAC5C;AACA,QAAM,OAAO,QAAQ,KAAK;AAC1B,QAAM,YAAY;AAAA,IAChB,UAAU,WAAW,YAAa,UAAU,IAAI,IAAI;AAAA,IACpD,cAAc,eAAe,YAAa,cAAc,IAAI,IAAI;AAAA,IAChE,sBAAsB,uBAAuB,YAAa,sBAAsB,IAAI,IAAI;AAAA,EAC1F;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,eAAe,iBAAiB;AAAA,EAC9B;AAAA,EACA;AACF,GAAG;AACD,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,SAAO,aAAa,eAAe,EAAE,KAAK,CAAC;AAC7C;AAIA,eAAe,uBAAuB;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,KAAK;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ;AAAA,EACA,cAAc;AAChB,GAAG;AACD,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,eAAe,iBAAiB,MAAM,gBAAgB,EAAE,QAAQ,CAAC;AACvE,MAAI,CAAC;AACH,UAAM,IAAI,uBAAuB;AACnC,MAAI;AACF,sBAAkB,EAAE,QAAQ,CAAC;AAC/B,QAAM,MAAM,OAAO,CAAC,UAAU,GAAG,IAAI,MAAM,gBAAgB,EAAE,MAAM,IAAI,CAAC,IAAI,QAAQ;AACpF,MAAI,MAAM,CAAC,UAAU,EAAE;AACrB,UAAM,IAAI,MAAM,iBAAiB;AACnC,QAAM,MAAM,OAAO,SAAS,cAAc,MAAM,aAAa,YAAY;AAAA,IACvE;AAAA,IACA,SAAS,aAAa;AAAA,IACtB;AAAA,IACA,KAAK,QAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,QAAQ;AACb,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG,UAAU,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC9B;AACF;AAGA,eAAe,gBAAgB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,eAAe,MAAM,gBAAgB,EAAE,QAAQ,CAAC;AACtD,MAAI,CAAC;AACH,UAAM,IAAI,uBAAuB;AACnC,MAAI;AACF,sBAAkB,EAAE,QAAQ,CAAC;AAC/B,MAAI;AACJ,MAAI,SAAS,YAAY;AACvB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,MAAM,uBAAuB;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,OAAO;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,OAAO,MAAM,aAAa,gBAAgB;AAAA,IAC9C,GAAG;AAAA,IACH,OAAO,UAAU,EAAE,IAAI,QAAQ,IAAI;AAAA,EACrC,CAAC;AACD,SAAO,EAAE,KAAK;AAChB;AAIA,eAAe,mBAAmB;AAAA,EAChC;AAAA,EACA,gBAAgB;AAAA,EAChB;AAAA,EACA;AAAA,EACA,UAAU;AACZ,GAAG;AACD,QAAM,eAAe,gBAAgB,EAAE,QAAQ,CAAC;AAChD,QAAM,UAAU,MAAM,aAAa,0BAA0B;AAAA,IAC3D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,WAAW,YAAY;AACjC,UAAM,MAAM,MAAM,aAAa,eAAe;AAAA,MAC5C,MAAM,QAAQ;AAAA,IAChB,CAAC;AACD,UAAM,OAAO,MAAM,aAAa,KAAK;AAAA,MACnC,GAAG;AAAA,MACH,UAAU,IAAI,SAAS,YAAY,IAAI,WAAW;AAAA,MAClD,cAAc,IAAI,SAAS,YAAY,IAAI,eAAe;AAAA,MAC1D,sBAAsB,IAAI,SAAS,YAAY,IAAI,uBAAuB;AAAA,IAC5E,CAAC;AACD,UAAM,SAAS,YAAa,KAAK,KAAK,UAAU,GAAG,CAAC,EAAE;AACtD,UAAM,IAAI,MAAM,MAAM;AAAA,EACxB;AACA,SAAO;AACT;AAuCA,SAAS,kBAAkB,EAAE,QAAQ,GAAG;AAlzFxC;AAmzFE,QAAM,EAAE,OAAO,aAAa,OAAO,IAAI,WAAW;AAClD,QAAM,gBAAgB,2CAAa;AACnC,MAAI,iBAAiB,YAAY,eAAe;AAC9C,UAAM,IAAI,mBAAmB;AAAA,MAC3B,eAAa,YAAO,KAAK,CAAC,MAAM,EAAE,OAAO,aAAa,MAAzC,mBAA4C,SAAQ,SAAS,aAAa;AAAA,MACvF,eAAa,YAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,MAAnC,mBAAsC,SAAQ,SAAS,OAAO;AAAA,IAC7E,CAAC;AAAA,EACH;AACF;", "names": ["EventEmitter", "__privateGet", "__privateAdd", "__privateSet", "__privateMethod", "EventEmitter", "__privateAdd", "provider", "__privateSet", "__privateGet", "config", "_a", "_b"]}