"use client";
import "./chunk-W7S2ME4R.js";

// node_modules/@rainbow-me/rainbowkit/dist/wallets/walletConnectors/enkryptWallet-LVMJVNXI.js
var enkryptWallet_default = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI4IiBoZWlnaHQ9IjI4IiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTUuODc4NTIgNS44Nzg2OUM2LjU0MzgxIDUuMjEzMzkgNy40NDYxIDQuODM5NjUgOC4zODY5NCA0LjgzOTY1TDIzLjI3NDQgNC44Mzk1MVY3LjU2MTQyQzIzLjI3NDQgOC4yMzc3MSAyMy4wMDU3IDguODg2MjcgMjIuNTI3NiA5LjM2NDQ1QzIyLjA0OTQgOS44NDI2MiAyMS40MDA4IDEwLjExMTMgMjAuNzI0NiAxMC4xMTEzSDEzLjcyNUMxMi43ODQyIDEwLjExMTMgMTEuODgxOSAxMC40ODUgMTEuMjE2NiAxMS4xNTAzQzEwLjU1MTMgMTEuODE1NiAxMC4xNzc2IDEyLjcxNzggMTAuMTc3NiAxMy42NTg3VjE0LjU2MTVDMTAuMTc3NiAxNS41MDIzIDEwLjU1MTMgMTYuNDA0NiAxMS4yMTY2IDE3LjA2OThDMTEuODgxOSAxNy43MzUxIDEyLjc4NDIgMTguMTA4OCAxMy43MjUgMTguMTA4OEgyMC43MjQ2QzIxLjQwMDggMTguMTA4OCAyMi4wNDk0IDE4LjM3NzUgMjIuNTI3NiAxOC44NTU3QzIzLjAwNTcgMTkuMzMzOSAyMy4yNzQ0IDE5Ljk4MjQgMjMuMjc0NCAyMC42NTg3VjIzLjI3NDZIOC4zODY5NEM3LjQ0NjEgMjMuMjc0NiA2LjU0MzgxIDIyLjkwMDggNS44Nzg1MiAyMi4yMzU2QzUuMjEzMjYgMjEuNTcwMyA0LjgzOTUxIDIwLjY2OCA0LjgzOTUxIDE5LjcyNzFWOC4zODcwN0M0LjgzOTUxIDcuNDQ2MjQgNS4yMTMyNiA2LjU0Mzk1IDUuODc4NTIgNS44Nzg2OVpNMTMuODc4MyAxMS41OTQ5SDIxLjA1NzNDMjIuMjgxOCAxMS41OTQ5IDIzLjI3NDQgMTIuNTg3NSAyMy4yNzQ0IDEzLjgxMlYxNC40MDg1QzIzLjI3NDQgMTUuNjMyOSAyMi4yODE4IDE2LjYyNTYgMjEuMDU3MyAxNi42MjU2SDEzLjg3ODNDMTIuNjUzOCAxNi42MjU2IDExLjY2MTIgMTUuNjMyOSAxMS42NjEyIDE0LjQwODVWMTMuODEyQzExLjY2MTIgMTIuNTg3NSAxMi42NTM4IDExLjU5NDkgMTMuODc4MyAxMS41OTQ5WiIgZmlsbD0idXJsKCNwYWludDBfbGluZWFyXzMxNDJfMjk2KSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzMxNDJfMjk2IiB4MT0iNi42OTI0MiIgeTE9IjQuODM5NTEiIHgyPSIxOS42MjQxIiB5Mj0iMjQuMTI4NiIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjQzU0OUZGIi8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzY1NEJGRiIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=";
export {
  enkryptWallet_default as default
};
//# sourceMappingURL=enkryptWallet-LVMJVNXI-67JND4CN.js.map
