"use client";
import "./chunk-W7S2ME4R.js";

// node_modules/@rainbow-me/rainbowkit/dist/ja_JP-GYCPH6AT.js
var ja_JP_default = '{\n  "connect_wallet": {\n    "label": "ウォレットを接続",\n    "wrong_network": {\n      "label": "ネットワークが間違っています"\n    }\n  },\n  "intro": {\n    "title": "ウォレットとは何ですか？",\n    "description": "ウォレットは、デジタルアセットを送信、受信、保存、表示するために使用されます。また、各ウェブサイトで新たなアカウントやパスワードを作成する必要なく、ログインする新しい方法でもあります。",\n    "digital_asset": {\n      "title": "あなたのデジタル資産のための家",\n      "description": "ウォレットは、EthereumやNFTのようなデジタル資産を送信、受信、保存、表示するために使用されます。"\n    },\n    "login": {\n      "title": "新しいログイン方法",\n      "description": "すべてのウェブサイトで新しいアカウントとパスワードを作成する代わりに、ウォレットを接続します。"\n    },\n    "get": {\n      "label": "ウォレットを取得する"\n    },\n    "learn_more": {\n      "label": "詳しくはこちら"\n    }\n  },\n  "sign_in": {\n    "label": "アカウントを確認する",\n    "description": "接続を完了するには、このアカウントの所有者であることを証明するためにウォレットでメッセージに署名する必要があります。",\n    "message": {\n      "send": "メッセージを送信",\n      "preparing": "メッセージの準備中...",\n      "cancel": "キャンセル",\n      "preparing_error": "メッセージの準備中にエラーが発生しました、再試行してください！"\n    },\n    "signature": {\n      "waiting": "署名を待っています...",\n      "verifying": "署名を検証中...",\n      "signing_error": "メッセージの署名中にエラーが発生しました、再試行してください！",\n      "verifying_error": "署名の検証中にエラーが発生しました、再試行してください！",\n      "oops_error": "おっと、何かが間違っていました！"\n    }\n  },\n  "connect": {\n    "label": "接続",\n    "title": "ウォレットを接続する",\n    "new_to_ethereum": {\n      "description": "Ethereumのウォレットが初めてですか？",\n      "learn_more": {\n        "label": "詳しくはこちら"\n      }\n    },\n    "learn_more": {\n      "label": "詳しくはこちら"\n    },\n    "recent": "最近利用しました",\n    "status": {\n      "opening": "%{wallet}を開いています...",\n      "connecting": "接続中",\n      "connect_mobile": "%{wallet}で続行",\n      "not_installed": "%{wallet} はインストールされていません",\n      "not_available": "%{wallet} は利用できません",\n      "confirm": "エクステンションで接続を確認してください",\n      "confirm_mobile": "ウォレットでの接続リクエストを承認する"\n    },\n    "secondary_action": {\n      "get": {\n        "description": "%{wallet}がありませんか？",\n        "label": "取得"\n      },\n      "install": {\n        "label": "インストール"\n      },\n      "retry": {\n        "label": "再試行"\n      }\n    },\n    "walletconnect": {\n      "description": {\n        "full": "公式のWalletConnectモーダルが必要ですか？",\n        "compact": "WalletConnectモーダルが必要ですか？"\n      },\n      "open": {\n        "label": "開く"\n      }\n    }\n  },\n  "connect_scan": {\n    "title": "%{wallet}でスキャン",\n    "fallback_title": "携帯電話でスキャンしてください"\n  },\n  "connector_group": {\n    "recommended": "おすすめのウォレット",\n    "other": "その他",\n    "popular": "人気のウォレット",\n    "more": "もっと",\n    "others": "その他"\n  },\n  "get": {\n    "title": "ウォレットを取得",\n    "action": {\n      "label": "取得"\n    },\n    "mobile": {\n      "description": "モバイルウォレット"\n    },\n    "extension": {\n      "description": "ブラウザ拡張"\n    },\n    "mobile_and_extension": {\n      "description": "モバイルウォレットと拡張機能"\n    },\n    "mobile_and_desktop": {\n      "description": "モバイルとデスクトップウォレット"\n    },\n    "looking_for": {\n      "title": "お探しのウォレットがありませんか？",\n      "mobile": {\n        "description": "メイン画面でウォレットを選択し、異なるウォレットプロバイダーで始めてください。"\n      },\n      "desktop": {\n        "compact_description": "メイン画面でウォレットを選択し、異なるウォレットプロバイダーで始めてください。",\n        "wide_description": "左側のウォレットを選択して、別のウォレットプロバイダーで始めてください。"\n      }\n    }\n  },\n  "get_options": {\n    "title": "%{wallet}で始める",\n    "short_title": "%{wallet}を取得する",\n    "mobile": {\n      "title": "モバイル用 %{wallet}",\n      "description": "モバイルウォレットを使用して、イーサリアムの世界を探索します。",\n      "download": {\n        "label": "アプリを取得"\n      }\n    },\n    "extension": {\n      "title": "%{wallet} for %{browser}",\n      "description": "お好きなウェブブラウザからウォレットに直接アクセスします。",\n      "download": {\n        "label": "%{browser}に追加"\n      }\n    },\n    "desktop": {\n      "title": "%{wallet} for %{platform}",\n      "description": "あなたの強力なデスクトップからネイティブにウォレットにアクセスします。",\n      "download": {\n        "label": "%{platform}に追加する"\n      }\n    }\n  },\n  "get_mobile": {\n    "title": "%{wallet}をインストール",\n    "description": "iOSまたはAndroidでダウンロードするために電話でスキャン",\n    "continue": {\n      "label": "続行"\n    }\n  },\n  "get_instructions": {\n    "mobile": {\n      "connect": {\n        "label": "接続"\n      },\n      "learn_more": {\n        "label": "詳しくはこちら"\n      }\n    },\n    "extension": {\n      "refresh": {\n        "label": "更新"\n      },\n      "learn_more": {\n        "label": "詳しくはこちら"\n      }\n    },\n    "desktop": {\n      "connect": {\n        "label": "接続"\n      },\n      "learn_more": {\n        "label": "詳しくはこちら"\n      }\n    }\n  },\n  "chains": {\n    "title": "ネットワークを切り替える",\n    "wrong_network": "誤ったネットワークが検出されました、続行するには切り替えるか切断してください。",\n    "confirm": "ウォレットで確認する",\n    "switching_not_supported": "あなたのウォレットは %{appName}からネットワークを切り替えることをサポートしていません。ウォレット内でネットワークを切り替えてみてください。",\n    "switching_not_supported_fallback": "あなたのウォレットは、このアプリからネットワークを切り替えることをサポートしていません。代わりにウォレット内からネットワークを切り替えてみてください。",\n    "disconnect": "切断する",\n    "connected": "接続しました"\n  },\n  "profile": {\n    "disconnect": {\n      "label": "切断する"\n    },\n    "copy_address": {\n      "label": "アドレスをコピーする",\n      "copied": "コピーしました！"\n    },\n    "explorer": {\n      "label": "エクスプローラーで詳しく見る"\n    },\n    "transactions": {\n      "description": "%{appName} トランザクションがここに表示されます...",\n      "description_fallback": "あなたのトランザクションはここに表示されます...",\n      "recent": {\n        "title": "最近のトランザクション"\n      },\n      "clear": {\n        "label": "すべてクリア"\n      }\n    }\n  },\n  "wallet_connectors": {\n    "argent": {\n      "qr_code": {\n        "step1": {\n          "description": "より速くウォレットにアクセスするために、Argentをホーム画面に置いてください。",\n          "title": "Argentアプリを開く"\n        },\n        "step2": {\n          "description": "ウォレットとユーザーネームを作成するか、既存のウォレットをインポートします。",\n          "title": "ウォレットを作成またはインポート"\n        },\n        "step3": {\n          "description": "スキャン後、ウォレットを接続するための接続プロンプトが表示されます。",\n          "title": "「QRをスキャン」ボタンをタップします"\n        }\n      }\n    },\n    "bifrost": {\n      "qr_code": {\n        "step1": {\n          "description": "より速くアクセスできるように、Bifrost Walletをホーム画面に置くことをお勧めします。",\n          "title": "Bifrost Walletアプリを開きます"\n        },\n        "step2": {\n          "description": "リカバリーフレーズを使用してウォレットを作成またはインポートします。",\n          "title": "ウォレットを作成またはインポート"\n        },\n        "step3": {\n          "description": "スキャン後、ウォレットを接続するための接続プロンプトが表示されます。",\n          "title": "「スキャン」ボタンをタップします"\n        }\n      }\n    },\n    "bitget": {\n      "qr_code": {\n        "step1": {\n          "description": "より迅速なアクセスのために、ホーム画面にBitget Walletを配置することをお勧めします。",\n          "title": "Bitget Walletアプリを開く"\n        },\n        "step2": {\n          "description": "ウォレットは安全な方法でバックアップしてください。秘密のフレーズを誰とも共有しないでください。",\n          "title": "ウォレットを作成またはインポート"\n        },\n        "step3": {\n          "description": "スキャン後、ウォレットを接続するための接続プロンプトが表示されます。",\n          "title": "スキャンボタンをタップする"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "ウォレットへのより迅速なアクセスのためにBitget Walletをタスクバーにピン留めすることをお勧めします。",\n          "title": "Bitget Wallet拡張機能をインストールします"\n        },\n        "step2": {\n          "description": "ウォレットを安全な方法でバックアップしてください。秘密のフレーズを誰とも共有しないでください。",\n          "title": "ウォレットを作成またはインポートします"\n        },\n        "step3": {\n          "description": "ウォレットを設定したら、以下をクリックしてブラウザを更新し、拡張機能を読み込みます。",\n          "title": "ブラウザを更新する"\n        }\n      }\n    },\n    "bitski": {\n      "extension": {\n        "step1": {\n          "description": "ウォレットへの素早いアクセスのために、Bitskiをタスクバーにピン留めすることをお勧めします。",\n          "title": "Bitskiエクステンションをインストールする"\n        },\n        "step2": {\n          "description": "ウォレットを安全な方法でバックアップしてください。シークレットフレーズは誰とも共有しないでください。",\n          "title": "ウォレットを作成するか、インポートする"\n        },\n        "step3": {\n          "description": "ウォレットのセットアップが完了したら、以下をクリックしてブラウザを更新し、エクステンションを読み込みます。",\n          "title": "ブラウザを更新する"\n        }\n      }\n    },\n    "coin98": {\n      "qr_code": {\n        "step1": {\n          "description": "Coin98ウォレットをホーム画面に置くことで、ウォレットへのアクセスが高速化されることをお勧めします。",\n          "title": "Coin98ウォレットアプリを開きます"\n        },\n        "step2": {\n          "description": "電話のバックアップ機能を使用して、ウォレットを簡単にバックアップすることができます。",\n          "title": "ウォレットを作成またはインポートする"\n        },\n        "step3": {\n          "description": "スキャン後、ウォレットへの接続を促すプロンプトが表示されます。",\n          "title": "WalletConnectボタンをタップします"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "ブラウザの右上をクリックして、Coin98ウォレットをピン留めして簡単にアクセスできるようにします。",\n          "title": "Coin98ウォレットの拡張機能をインストールします"\n        },\n        "step2": {\n          "description": "新しいウォレットを作成するか、既存のものをインポートします。",\n          "title": "ウォレットを作成またはインポートする"\n        },\n        "step3": {\n          "description": "Coin98ウォレットをセットアップしたら、下のリンクをクリックしてブラウザを更新し、拡張機能をロードします。",\n          "title": "ブラウザを更新する"\n        }\n      }\n    },\n    "coinbase": {\n      "qr_code": {\n        "step1": {\n          "description": "より素早くアクセスできるように、Coinbaseウォレットをホームスクリーンに置くことをお勧めします。",\n          "title": "Coinbase Walletアプリを開く"\n        },\n        "step2": {\n          "description": "クラウドバックアップ機能を使用して、簡単にウォレットをバックアップできます。",\n          "title": "ウォレットを作成またはインポートする"\n        },\n        "step3": {\n          "description": "スキャン後、ウォレットを接続するための接続プロンプトが表示されます。",\n          "title": "スキャンボタンをタップする"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "タスクバーにCoinbase Walletをピン留めして、ウォレットにより早くアクセスできるように推奨します。",\n          "title": "Coinbase Wallet拡張機能をインストールする"\n        },\n        "step2": {\n          "description": "必ず安全な方法を使用してウォレットをバックアップしてください。秘密のフレーズを誰にも共有しないでください。",\n          "title": "ウォレットを作成またはインポート"\n        },\n        "step3": {\n          "description": "ウォレットの設定が完了したら、下のボタンをクリックしてブラウザを更新し、拡張機能をロードします。",\n          "title": "ブラウザを更新する"\n        }\n      }\n    },\n    "core": {\n      "qr_code": {\n        "step1": {\n          "description": "ウォレットへの迅速なアクセスのため、コアをホーム画面に設定することを推奨します。",\n          "title": "Coreアプリを開く"\n        },\n        "step2": {\n          "description": "電話のバックアップ機能を使って、簡単にウォレットをバックアップできます。",\n          "title": "ウォレットを作成またはインポート"\n        },\n        "step3": {\n          "description": "スキャン後、ウォレットを接続するようにプロンプトが表示されます。",\n          "title": "WalletConnectボタンをタップする"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "ウォレットへのより迅速なアクセスのために、タスクバーにCoreをピン留めすることをお勧めします。",\n          "title": "Core拡張機能をインストールする"\n        },\n        "step2": {\n          "description": "セキュアな方法を使用してウォレットをバックアップしてください。秘密のフレーズを誰とも共有しないでください。",\n          "title": "ウォレットを作成またはインポートする"\n        },\n        "step3": {\n          "description": "ウォレットの設定が完了したら、以下をクリックしてブラウザを更新し、拡張機能をロードします。",\n          "title": "ブラウザを更新する"\n        }\n      }\n    },\n    "fox": {\n      "qr_code": {\n        "step1": {\n          "description": "より迅速なアクセスのために、ホーム画面にFoxWalletを置くことをお勧めします。",\n          "title": "FoxWalletアプリを開く"\n        },\n        "step2": {\n          "description": "セキュアな方法を使用してウォレットをバックアップすることを確認してください。秘密のフレーズは誰とも共有しないでください。",\n          "title": "ウォレットを作成またはインポート"\n        },\n        "step3": {\n          "description": "スキャンした後、ウォレットを接続するための接続プロンプトが表示されます。",\n          "title": "スキャンボタンをタップします"\n        }\n      }\n    },\n    "frontier": {\n      "qr_code": {\n        "step1": {\n          "description": "Frontierウォレットをホーム画面に置くことで、より早くアクセスできることをお勧めします。",\n          "title": "Frontierウォレットアプリを開きます"\n        },\n        "step2": {\n          "description": "セキュアな方法を使用してウォレットをバックアップすることを確認してください。秘密のフレーズは誰とも共有しないでください。",\n          "title": "ウォレットを作成またはインポート"\n        },\n        "step3": {\n          "description": "スキャン後に、ウォレットの接続を促すメッセージが表示されます。",\n          "title": "スキャンボタンをタップします"\n        }\n      },\n      "extension": {\n        "step1": {\n          "description": "より迅速なウォレットへのアクセスを可能にするために、フロンティアウォレットをタスクバーにピン留めすることを推奨します。",\n          "title": "フロンティアウォレットの拡張機能をインストールします"\n        },\n        "step2": {\n          "description": "安全な方法を使用してウォレットをバックアップしてください。秘密のフレーズは決して誰とも共有しないでください。",\n          "title": "ウォレットを作成またはインポート"\n        },\n        "step3": {\n          "description": "ウォレットの設定が完了したら、ブラウザを更新して拡張機能を読み込みます。",\n          "title": "ブラウザを更新する"\n        }\n      }\n    },\n    "im_token": {\n      "qr_code": {\n        "step1": {\n          "title": "imTokenアプリを開く",\n          "description": "ウォレットへのアクセスを速くするために、imTokenアプリをホーム画面に置いてください。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "新しいウォレットを作成するか、既存のものをインポートします。"\n        },\n        "step3": {\n          "title": "右上隅のスキャナーアイコンをタップします",\n          "description": "新しい接続を選択し、QRコードをスキャンしてプロンプトを確認し接続します。"\n        }\n      }\n    },\n    "metamask": {\n      "qr_code": {\n        "step1": {\n          "title": "MetaMaskアプリを開きます",\n          "description": "迅速なアクセスのために、MetaMaskをホーム画面に置くことをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポートします",\n          "description": "必ず安全な方法を使用してウォレットをバックアップしてください。秘密の回復フレーズを誰にも共有しないでください。"\n        },\n        "step3": {\n          "title": "スキャンボタンをタップします",\n          "description": "スキャンすると、ウォレットを接続するための接続プロンプトが表示されます。"\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "MetaMaskの拡張機能をインストールします",\n          "description": "ウォレットへのより速いアクセスのために、MetaMaskをタスクバーにピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "安全な方法を使用してウォレットをバックアップし、秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新",\n          "description": "ウォレットを設定した後は、下のリンクをクリックしてブラウザを更新し、エクステンションを読み込んでください。"\n        }\n      }\n    },\n    "okx": {\n      "qr_code": {\n        "step1": {\n          "title": "OKX Walletアプリを開く",\n          "description": "OKX Walletをホーム画面に配置して、より早くアクセスできるようにすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "セキュアな方法を使ってウォレットをバックアップしてください。秘密フレーズは誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "スキャンボタンをタップする",\n          "description": "スキャン後、ウォレットを接続するための接続プロンプトが表示されます。"\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "OKXウォレット拡張機能をインストールする",\n          "description": "ウォレットへの迅速なアクセスのため、OKXウォレットをタスクバーにピン止めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成するか、インポートする",\n          "description": "セキュアな方法を使ってウォレットをバックアップしてください。秘密フレーズは誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットを設定したら、下をクリックしてブラウザをリフレッシュし、拡張機能を読み込みます。"\n        }\n      }\n    },\n    "omni": {\n      "qr_code": {\n        "step1": {\n          "title": "Omniアプリを開く",\n          "description": "Omniをホーム画面に追加して、ウォレットへのアクセスを早めます。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "新しいウォレットを作成するか、既存のものをインポートします。"\n        },\n        "step3": {\n          "title": "QRアイコンをタップしてスキャン",\n          "description": "ホーム画面のQRアイコンをタップし、コードをスキャンし、プロンプトを確認して接続します。"\n        }\n      }\n    },\n    "token_pocket": {\n      "qr_code": {\n        "step1": {\n          "title": "TokenPocketアプリを開く",\n          "description": "より速いアクセスのために、TokenPocketをホーム画面に置くことをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポートする",\n          "description": "必ず安全な方法を使用してウォレットをバックアップしてください。秘密の回復フレーズを誰にも共有しないでください。"\n        },\n        "step3": {\n          "title": "スキャンボタンをタップする",\n          "description": "スキャン後、ウォレットを接続するための接続プロンプトが表示されます。"\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "TokenPocketエクステンションをインストールする",\n          "description": "ウォレットへのより早いアクセスのために、TokenPocketをタスクバーにピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "ウォレットを安全な方法でバックアップすることを確認してください。シークレットフレーズを決して他の人と共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新",\n          "description": "ウォレットのセットアップが完了したら、以下をクリックしてブラウザを更新し、拡張機能をロードします。"\n        }\n      }\n    },\n    "trust": {\n      "qr_code": {\n        "step1": {\n          "title": "Trust Walletアプリを開く",\n          "description": "ウォレットへの高速アクセスのために、Trust Walletをホーム画面に置きます。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "新しいウォレットを作成するか、既存のものをインポートします。"\n        },\n        "step3": {\n          "title": "設定でWalletConnectをタップします",\n          "description": "新しい接続を選択し、QRコードをスキャンして、プロンプトで接続を確認します。"\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "Trust Wallet拡張機能をインストールします",\n          "description": "ブラウザの右上をクリックし、Trust Walletをピン留めして簡単にアクセスできるようにします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成するかインポートします",\n          "description": "新しいウォレットを作成するか、既存のものをインポートします。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "Trust Walletの設定が完了したら、以下をクリックしてブラウザを更新し、拡張機能を読み込みます。"\n        }\n      }\n    },\n    "uniswap": {\n      "qr_code": {\n        "step1": {\n          "title": "Uniswapアプリを開く",\n          "description": "Uniswapウォレットをホーム画面に追加して、ウォレットへのアクセスを高速化します。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポートする",\n          "description": "新しいウォレットを作成するか、既存のものをインポートします。"\n        },\n        "step3": {\n          "title": "QRアイコンをタップしてスキャンする",\n          "description": "ホーム画面のQRアイコンをタップし、コードをスキャンしてプロンプトを確認して接続します。"\n        }\n      }\n    },\n    "zerion": {\n      "qr_code": {\n        "step1": {\n          "title": "Zerionアプリを開く",\n          "description": "より速くアクセスするために、Zerionをホーム画面に置くことをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成するか、インポートする",\n          "description": "必ず安全な方法を使用してウォレットをバックアップしてください。秘密の回復フレーズを誰にも共有しないでください。"\n        },\n        "step3": {\n          "title": "スキャンボタンを押す",\n          "description": "スキャンした後、ウォレットを接続するための接続プロンプトが表示されます。"\n        }\n      },\n      "extension": {\n        "step1": {\n          "title": "Zerion拡張機能をインストールする",\n          "description": "ウォレットへの素早いアクセスのため、Zerionをタスクバーにピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "ウォレットをセキュアな方法でバックアップすることを確認してください。秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットをセットアップしたら、下のボタンをクリックしてブラウザを更新し、拡張機能をロードします。"\n        }\n      }\n    },\n    "rainbow": {\n      "qr_code": {\n        "step1": {\n          "title": "Rainbowアプリを開く",\n          "description": "ウォレットへの早いアクセスのために、Rainbowをホーム画面に置くことをおすすめします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "電話のバックアップ機能を使用して、簡単にウォレットをバックアップすることができます。"\n        },\n        "step3": {\n          "title": "スキャンボタンをタップする",\n          "description": "スキャンした後、ウォレットを接続するための接続プロンプトが表示されます。"\n        }\n      }\n    },\n    "enkrypt": {\n      "extension": {\n        "step1": {\n          "description": "ウォレットへのアクセスをより早くするため、タスクバーにEnkrypt Walletをピン留めすることを推奨します。",\n          "title": "Enkrypt Wallet拡張機能をインストールしてください"\n        },\n        "step2": {\n          "description": "安全な方法でウォレットのバックアップを必ず取り、秘密のフレーズを誰とも共有しないでください。",\n          "title": "ウォレットを作成するか、インポートする"\n        },\n        "step3": {\n          "description": "ウォレットの設定が完了したら、以下をクリックしてブラウザを更新し、拡張機能を読み込みます。",\n          "title": "ブラウザを更新する"\n        }\n      }\n    },\n    "frame": {\n      "extension": {\n        "step1": {\n          "description": "ウォレットへのアクセスをより早くするため、タスクバーにFrameをピン留めすることを推奨します。",\n          "title": "Frameとその付属の拡張機能をインストール"\n        },\n        "step2": {\n          "description": "ウォレットを安全な方法でバックアップしてください。秘密のフレーズを誰とも共有しないでください。",\n          "title": "ウォレットを作成、またはインポート"\n        },\n        "step3": {\n          "description": "ウォレットの設定が完了したら、下のリンクをクリックしてブラウザを更新し、拡張機能をロードします。",\n          "title": "ブラウザを更新"\n        }\n      }\n    },\n    "one_key": {\n      "extension": {\n        "step1": {\n          "title": "OneKey Wallet拡張機能をインストール",\n          "description": "ウォレットへのアクセスを素早く行うため、OneKey Walletをタスクバーにピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成、またはインポート",\n          "description": "安全な方法を使用してウォレットをバックアップしてください。秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットを設定したら、以下をクリックしてブラウザを更新し、拡張機能をロードします。"\n        }\n      }\n    },\n    "phantom": {\n      "extension": {\n        "step1": {\n          "title": "Phantom拡張機能をインストールする",\n          "description": "ウォレットへの容易なアクセスのため、Phantomをタスクバーにピン留めすることを推奨します。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポートする",\n          "description": "安全な方法を使用してウォレットをバックアップしてください。秘密の回復フレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットの設定が完了したら、以下をクリックしてブラウザを更新し、エクステンションを読み込みます。"\n        }\n      }\n    },\n    "rabby": {\n      "extension": {\n        "step1": {\n          "title": "Rabbyエクステンションをインストールする",\n          "description": "ウォレットへの素早いアクセスのため、タスクバーにRabbyをピン止めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "セキュアな方法を使用してウォレットをバックアップしてください。秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新",\n          "description": "ウォレットの設定が完了したら、以下をクリックしてブラウザを更新し、拡張機能をロードします。"\n        }\n      }\n    },\n    "safeheron": {\n      "extension": {\n        "step1": {\n          "title": "コア拡張機能をインストール",\n          "description": "ウォレットへの素早いアクセスのため、タスクバーにSafeheronをピン止めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "確実に安全な方法でウォレットをバックアップしてください。秘密のフレーズは決して誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットの設定が完了したら、以下をクリックしてブラウザを更新し、拡張機能をロードします。"\n        }\n      }\n    },\n    "taho": {\n      "extension": {\n        "step1": {\n          "title": "Taho拡張機能をインストールする",\n          "description": "ウォレットへのより迅速なアクセスのため、Tahoをタスクバーにピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成するか、インポートする",\n          "description": "確実に安全な方法でウォレットをバックアップしてください。秘密のフレーズは決して誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットの設定が完了したら、下をクリックしてブラウザを更新し、拡張機能をロードします。"\n        }\n      }\n    },\n    "talisman": {\n      "extension": {\n        "step1": {\n          "title": "Talisman拡張機能をインストールする",\n          "description": "ウォレットへのより早いアクセスのために、Talismanをタスクバーにピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "Ethereumウォレットを作成するか、インポートする",\n          "description": "ウォレットを安全な方法でバックアップしておくことを確認してください。リカバリーフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットの設定が完了したら、下をクリックしてブラウザを更新し、拡張機能をロードします。"\n        }\n      }\n    },\n    "xdefi": {\n      "extension": {\n        "step1": {\n          "title": "XDEFI Wallet拡張機能をインストールする",\n          "description": "XDEFI Walletをタスクバーにピン留めすることで、ウォレットへのアクセスが速くなることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットの作成またはインポート",\n          "description": "ウォレットを安全な方法でバックアップしてください。秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットの設定が完了したら、以下をクリックしてブラウザを更新し、拡張機能をロードしてください。"\n        }\n      }\n    },\n    "zeal": {\n      "extension": {\n        "step1": {\n          "title": "Zeal 拡張機能をインストール",\n          "description": "ウォレットに素早くアクセスするために、タスクバーに Zeal をピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "ウォレットは安全な方法でバックアップしてください。秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットを設定したら、以下をクリックしてブラウザを更新し、拡張機能を読み込みます。"\n        }\n      }\n    },\n    "safepal": {\n      "extension": {\n        "step1": {\n          "title": "SafePal Wallet拡張機能をインストールする",\n          "description": "ブラウザの右上でクリックし、Easy AccessのためにSafePal Walletをピン留めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポートする",\n          "description": "新しいウォレットを作成するか、既存のものをインポートします。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "SafePal Walletのセットアップが完了したら、以下をクリックしてブラウザをリフレッシュし、エクステンションをロードします。"\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "SafePal Walletアプリを開く",\n          "description": "SafePal Walletをホーム画面に置くことで、ウォレットへの素早いアクセスが可能になります。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "新しいウォレットを作成するか、既存のものをインポートします。"\n        },\n        "step3": {\n          "title": "設定でWalletConnectをタップします",\n          "description": "新しい接続を選択し、QRコードをスキャンしてプロンプトを確認し接続します。"\n        }\n      }\n    },\n    "desig": {\n      "extension": {\n        "step1": {\n          "title": "Desig拡張機能をインストール",\n          "description": "あなたのウォレットへの簡単なアクセスのために、Desigをタスクバーにピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成",\n          "description": "ウォレットは安全な方法でバックアップしてください。秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットを設定したら、以下をクリックしてブラウザを更新し、拡張機能を読み込みます。"\n        }\n      }\n    },\n    "subwallet": {\n      "extension": {\n        "step1": {\n          "title": "SubWallet拡張機能をインストール",\n          "description": "ウォレットへのより素早いアクセスのため、SubWalletをタスクバーにピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "ウォレットを安全な方法でバックアップしておくことを確認してください。リカバリーフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットを設定したら、以下をクリックしてブラウザを更新し、拡張機能を読み込みます。"\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "SubWalletアプリを開く",\n          "description": "より迅速なアクセスのために、SubWalletをホーム画面に置くことをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "ウォレットは安全な方法でバックアップしてください。秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "「スキャン」ボタンをタップします",\n          "description": "スキャン後、ウォレットを接続するための接続プロンプトが表示されます。"\n        }\n      }\n    },\n    "clv": {\n      "extension": {\n        "step1": {\n          "title": "CLV Wallet拡張機能をインストール",\n          "description": "ウォレットへのより素早いアクセスのため、CLV Walletをタスクバーにピン留めすることをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "ウォレットは安全な方法でバックアップしてください。秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "ブラウザを更新する",\n          "description": "ウォレットを設定したら、以下をクリックしてブラウザを更新し、拡張機能を読み込みます。"\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "CLV Walletアプリを開く",\n          "description": "より迅速なアクセスのために、ホーム画面にCLV Walletを置くことをお勧めします。"\n        },\n        "step2": {\n          "title": "ウォレットを作成またはインポート",\n          "description": "ウォレットは安全な方法でバックアップしてください。秘密のフレーズを誰とも共有しないでください。"\n        },\n        "step3": {\n          "title": "「スキャン」ボタンをタップします",\n          "description": "スキャン後、ウォレットを接続するための接続プロンプトが表示されます。"\n        }\n      }\n    },\n    "okto": {\n      "qr_code": {\n        "step1": {\n          "title": "Oktoアプリを開く",\n          "description": "素早くアクセスするために、ホーム画面にOktoを追加します"\n        },\n        "step2": {\n          "title": "MPCウォレットを作成する",\n          "description": "アカウントを作成し、ウォレットを生成します"\n        },\n        "step3": {\n          "title": "設定でWalletConnectをタップします",\n          "description": "右上のScan QRアイコンをタップし、接続するためのプロンプトを確認します。"\n        }\n      }\n    },\n    "ledger": {\n      "desktop": {\n        "step1": {\n          "title": "Ledger Liveアプリを開く",\n          "description": "より速いアクセスのために、ホーム画面にLedger Liveを置くことを推奨します。"\n        },\n        "step2": {\n          "title": "あなたのLedgerを設定する",\n          "description": "新しいLedgerを設定するか、既存のものに接続します。"\n        },\n        "step3": {\n          "title": "接続",\n          "description": "スキャン後、ウォレットを接続するための接続プロンプトが表示されます。"\n        }\n      },\n      "qr_code": {\n        "step1": {\n          "title": "Ledger Liveアプリを開く",\n          "description": "より速いアクセスのために、ホーム画面にLedger Liveを置くことを推奨します。"\n        },\n        "step2": {\n          "title": "あなたのLedgerを設定する",\n          "description": "デスクトップアプリと同期するか、あなたのLedgerに接続することができます。"\n        },\n        "step3": {\n          "title": "コードをスキャンする",\n          "description": "WalletConnectをタップし、スキャナーに切り替えてください。スキャン後、ウォレットを接続するための接続プロンプトが表示されます。"\n        }\n      }\n    }\n  }\n}\n';
export {
  ja_JP_default as default
};
//# sourceMappingURL=ja_JP-GYCPH6AT-NMSJCXWW.js.map
