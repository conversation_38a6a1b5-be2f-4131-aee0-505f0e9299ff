{"version": 3, "sources": ["../../@wagmi/core/dist/providers/jsonRpc.js"], "sourcesContent": ["import \"../chunk-MQXBDTVK.js\";\n\n// src/providers/jsonRpc.ts\nfunction jsonRpcProvider({\n  rpc\n}) {\n  return function(chain) {\n    const rpcConfig = rpc(chain);\n    if (!rpcConfig || rpcConfig.http === \"\")\n      return null;\n    return {\n      chain: {\n        ...chain,\n        rpcUrls: {\n          ...chain.rpcUrls,\n          default: { http: [rpcConfig.http] }\n        }\n      },\n      rpcUrls: {\n        http: [rpcConfig.http],\n        webSocket: rpcConfig.webSocket ? [rpcConfig.webSocket] : void 0\n      }\n    };\n  };\n}\nexport {\n  jsonRpcProvider\n};\n"], "mappings": ";;;;;AAGA,SAAS,gBAAgB;AAAA,EACvB;AACF,GAAG;AACD,SAAO,SAAS,OAAO;AACrB,UAAM,YAAY,IAAI,KAAK;AAC3B,QAAI,CAAC,aAAa,UAAU,SAAS;AACnC,aAAO;AACT,WAAO;AAAA,MACL,OAAO;AAAA,QACL,GAAG;AAAA,QACH,SAAS;AAAA,UACP,GAAG,MAAM;AAAA,UACT,SAAS,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE;AAAA,QACpC;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,MAAM,CAAC,UAAU,IAAI;AAAA,QACrB,WAAW,UAAU,YAAY,CAAC,UAAU,SAAS,IAAI;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACF;", "names": []}