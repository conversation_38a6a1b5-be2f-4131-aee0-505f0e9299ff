{"version": 3, "sources": ["../../@rainbow-me/rainbowkit/dist/wallets/walletConnectors/coreWallet-HRVLR2XS.js"], "sourcesContent": ["\"use client\";\n// src/wallets/walletConnectors/coreWallet/coreWallet.svg\nvar coreWallet_default = \"data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\";\nexport {\n  coreWallet_default as default\n};\n"], "mappings": ";;;;AAEA,IAAI,qBAAqB;", "names": []}