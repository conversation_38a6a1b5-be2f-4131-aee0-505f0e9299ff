import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const shimmerTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 1073,
  name: 'Shimmer Testnet',
  network: 'shimmer-testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON>mmer',
    symbol: 'SMR',
  },
  rpcUrls: {
    public: {
      http: ['https://json-rpc.evm.testnet.shimmer.network'],
    },
    default: {
      http: ['https://json-rpc.evm.testnet.shimmer.network'],
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON>mmer Network Explorer',
      url: 'https://explorer.evm.testnet.shimmer.network',
    },
  },
  testnet: true,
})
