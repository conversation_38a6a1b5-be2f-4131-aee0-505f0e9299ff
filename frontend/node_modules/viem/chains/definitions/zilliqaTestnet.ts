import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const zilliqaTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 33101,
  name: 'Zilliqa Testnet',
  network: 'zilliqa-testnet',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON><PERSON>', symbol: 'ZIL', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://dev-api.zilliqa.com'],
    },
    public: {
      http: ['https://dev-api.zilliqa.com'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Ether<PERSON>',
      url: 'https://evmx.testnet.zilliqa.com',
    },
  },
  testnet: true,
})
