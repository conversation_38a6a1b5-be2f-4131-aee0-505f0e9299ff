import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const songbirdTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 16,
  name: '<PERSON><PERSON>',
  network: 'coston',
  nativeCurrency: {
    decimals: 18,
    name: 'costonflare',
    symbol: 'CFLR',
  },
  rpcUrls: {
    default: { http: ['https://coston-api.flare.network/ext/C/rpc'] },
    public: { http: ['https://coston-api.flare.network/ext/C/rpc'] },
  },
  blockExplorers: {
    default: {
      name: 'Coston Explorer',
      url: 'https://coston-explorer.flare.network',
    },
  },
  testnet: true,
})
