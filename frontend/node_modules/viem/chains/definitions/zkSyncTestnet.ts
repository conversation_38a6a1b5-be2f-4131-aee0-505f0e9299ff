import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'
import { formattersZkSync } from '../zksync/formatters.js'
import { serializersZkSync } from '../zksync/serializers.js'

/** @deprecated Use `zkSyncSepoliaTestnet` */
export const zkSyncTestnet = /*#__PURE__*/ define<PERSON>hain(
  {
    id: 280,
    name: 'zkSync Era Testnet',
    network: 'zksync-era-testnet',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
      default: {
        http: ['https://testnet.era.zksync.dev'],
        webSocket: ['wss://testnet.era.zksync.dev/ws'],
      },
      public: {
        http: ['https://testnet.era.zksync.dev'],
        webSocket: ['wss://testnet.era.zksync.dev/ws'],
      },
    },
    blockExplorers: {
      default: {
        name: 'zkExplorer',
        url: 'https://goerli.explorer.zksync.io',
      },
    },
    contracts: {
      multicall3: {
        address: '******************************************',
      },
    },
    testnet: true,
  },
  {
    serializers: serializersZkSync,
    formatters: formattersZkSync,
  },
)
