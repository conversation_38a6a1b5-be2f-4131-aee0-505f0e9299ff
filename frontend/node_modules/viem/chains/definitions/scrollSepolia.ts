import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const scrollSepolia = /*#__PURE__*/ defineChain({
  id: 534_351,
  name: '<PERSON>roll Sepolia',
  network: 'scroll-sepolia',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://sepolia-rpc.scroll.io'],
    },
    public: {
      http: ['https://sepolia-rpc.scroll.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Blockscout',
      url: 'https://sepolia-blockscout.scroll.io',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 9473,
    },
  },
  testnet: true,
})
