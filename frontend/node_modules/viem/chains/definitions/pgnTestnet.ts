import { define<PERSON>hain } from '../../utils/chain/defineChain.js'
import { formattersOptimism } from '../optimism/formatters.js'

export const pgnTestnet = /*#__PURE__*/ define<PERSON>hain(
  {
    id: 58008,
    network: 'pgn-testnet',
    name: 'PG<PERSON> ',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
      default: {
        http: ['https://sepolia.publicgoods.network'],
      },
      public: {
        http: ['https://sepolia.publicgoods.network'],
      },
    },
    blockExplorers: {
      default: {
        name: 'PGN Testnet Explorer',
        url: 'https://explorer.sepolia.publicgoods.network',
      },
      blocksout: {
        name: 'PGN Testnet Explorer',
        url: 'https://explorer.sepolia.publicgoods.network',
      },
    },
    contracts: {
      multicall3: {
        address: '******************************************',
        blockCreated: 3754925,
      },
    },
    testnet: true,
  },
  {
    formatters: formattersOptimism,
  },
)
