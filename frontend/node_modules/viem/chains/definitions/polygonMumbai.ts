import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const polygonMumbai = /*#__PURE__*/ define<PERSON>hain({
  id: 80_001,
  name: 'Polygon Mumbai',
  network: 'maticmum',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'MATIC', decimals: 18 },
  rpcUrls: {
    alchemy: {
      http: ['https://polygon-mumbai.g.alchemy.com/v2'],
      webSocket: ['wss://polygon-mumbai.g.alchemy.com/v2'],
    },
    infura: {
      http: ['https://polygon-mumbai.infura.io/v3'],
      webSocket: ['wss://polygon-mumbai.infura.io/ws/v3'],
    },
    default: {
      http: ['https://rpc.ankr.com/polygon_mumbai'],
    },
    public: {
      http: ['https://rpc.ankr.com/polygon_mumbai'],
    },
  },
  blockExplorers: {
    etherscan: {
      name: 'PolygonScan',
      url: 'https://mumbai.polygonscan.com',
    },
    default: {
      name: 'PolygonScan',
      url: 'https://mumbai.polygonscan.com',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 25770160,
    },
  },
  testnet: true,
})
