import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const taraxaTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 842,
  name: 'Taraxa Testnet',
  network: 'taraxa-testnet',
  nativeCurrency: { name: '<PERSON>', symbol: 'TARA', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.testnet.taraxa.io'],
    },
    public: {
      http: ['https://rpc.testnet.taraxa.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Taraxa Explorer',
      url: 'https://explorer.testnet.taraxa.io',
    },
  },
  testnet: true,
})
