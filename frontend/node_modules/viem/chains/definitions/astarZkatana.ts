import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const astarZkatana = /*#__PURE__*/ define<PERSON>hain({
  id: 1_261_120,
  name: 'Astar zkEVM Testnet zKatana',
  network: 'zKatana',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: [
        'https://rpc.zkatana.gelato.digital',
        'https://rpc.startale.com/zkatana',
      ],
    },
    public: {
      http: [
        'https://rpc.zkatana.gelato.digital',
        'https://rpc.startale.com/zkatana',
      ],
    },
  },
  blockExplorers: {
    blockscout: {
      name: 'Blockscout zKatana chain explorer',
      url: 'https://zkatana.blockscout.com',
    },
    default: {
      name: 'zKatana Explorer',
      url: 'https://zkatana.explorer.startale.com',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 31317,
    },
  },
  testnet: true,
})
