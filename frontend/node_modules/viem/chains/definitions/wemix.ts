import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const wemix = /*#__PURE__*/ define<PERSON>hain({
  id: 1111,
  name: 'WEMIX',
  network: 'wemix-mainnet',
  nativeCurrency: { name: 'WEMIX', symbol: 'WEMIX', decimals: 18 },
  rpcUrls: {
    default: { http: ['https://api.wemix.com'] },
    public: { http: ['https://api.wemix.com'] },
  },
  blockExplorers: {
    etherscan: { name: 'wemixExplorer', url: 'https://explorer.wemix.com' },
    default: { name: 'wemixExplorer', url: 'https://explorer.wemix.com' },
  },
})
