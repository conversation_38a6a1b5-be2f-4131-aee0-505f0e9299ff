export declare const wanchain: import("../../types/utils.js").Assign<{
    readonly id: 888;
    readonly name: "<PERSON>cha<PERSON>";
    readonly network: "wanchain";
    readonly nativeCurrency: {
        readonly name: "<PERSON><PERSON><PERSON><PERSON>";
        readonly symbol: "WAN";
        readonly decimals: 18;
    };
    readonly rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://gwan-ssl.wandevs.org:56891", "https://gwan2-ssl.wandevs.org"];
        };
        readonly public: {
            readonly http: readonly ["https://gwan-ssl.wandevs.org:56891", "https://gwan2-ssl.wandevs.org"];
        };
    };
    readonly blockExplorers: {
        readonly etherscan: {
            readonly name: "WanScan";
            readonly url: "https://wanscan.org";
        };
        readonly default: {
            readonly name: "WanS<PERSON>";
            readonly url: "https://wanscan.org";
        };
    };
    readonly contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 25312390;
        };
    };
}, import("../../types/chain.js").ChainConfig<undefined>>;
//# sourceMappingURL=wanchain.d.ts.map