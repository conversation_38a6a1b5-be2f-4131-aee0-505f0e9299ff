export declare const shibarium: import("../../types/utils.js").Assign<{
    readonly id: 109;
    readonly name: "Shibarium";
    readonly network: "shibarium";
    readonly nativeCurrency: {
        readonly name: "<PERSON>";
        readonly symbol: "<PERSON><PERSON><PERSON>";
        readonly decimals: 18;
    };
    readonly rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.shibrpc.com"];
        };
        readonly public: {
            readonly http: readonly ["https://rpc.shibrpc.com"];
        };
    };
    readonly blockExplorers: {
        readonly etherscan: {
            readonly name: "Blockscout";
            readonly url: "https://shibariumscan.io";
        };
        readonly default: {
            readonly name: "Blockscout";
            readonly url: "https://shibariumscan.io";
        };
    };
    readonly contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 265900;
        };
    };
}, import("../../types/chain.js").ChainConfig<undefined>>;
//# sourceMappingURL=shibarium.d.ts.map