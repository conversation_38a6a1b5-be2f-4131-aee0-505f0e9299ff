export declare const taikoJolnir: import("../../types/utils.js").Assign<{
    readonly id: 167007;
    readonly name: "<PERSON><PERSON> (Alpha-5 Testnet)";
    readonly network: "tko-jolnir";
    readonly nativeCurrency: {
        readonly name: "<PERSON><PERSON>";
        readonly symbol: "ETH";
        readonly decimals: 18;
    };
    readonly rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.jolnir.taiko.xyz"];
        };
        readonly public: {
            readonly http: readonly ["https://rpc.jolnir.taiko.xyz"];
        };
    };
    readonly blockExplorers: {
        readonly default: {
            readonly name: "blockscout";
            readonly url: "https://explorer.jolnir.taiko.xyz";
        };
    };
}, import("../../types/chain.js").ChainConfig<undefined>>;
//# sourceMappingURL=taikoJolnir.d.ts.map