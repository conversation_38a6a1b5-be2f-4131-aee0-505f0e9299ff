export declare const shimmer: import("../../types/utils.js").Assign<{
    readonly id: 148;
    readonly name: "<PERSON><PERSON>";
    readonly network: "shimmer";
    readonly nativeCurrency: {
        readonly decimals: 18;
        readonly name: "<PERSON><PERSON>";
        readonly symbol: "SMR";
    };
    readonly rpcUrls: {
        readonly public: {
            readonly http: readonly ["https://json-rpc.evm.shimmer.network"];
        };
        readonly default: {
            readonly http: readonly ["https://json-rpc.evm.shimmer.network"];
        };
    };
    readonly blockExplorers: {
        readonly default: {
            readonly name: "Shimmer Network Explorer";
            readonly url: "https://explorer.evm.shimmer.network";
        };
    };
}, import("../../types/chain.js").ChainConfig<undefined>>;
//# sourceMappingURL=shimmer.d.ts.map