export declare const wanchainTestnet: import("../../types/utils.js").Assign<{
    readonly id: 999;
    readonly name: "Wanchain Testnet";
    readonly network: "wanchainTestnet";
    readonly nativeCurrency: {
        readonly name: "<PERSON><PERSON><PERSON><PERSON>";
        readonly symbol: "WANt";
        readonly decimals: 18;
    };
    readonly rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://gwan-ssl.wandevs.org:46891"];
        };
        readonly public: {
            readonly http: readonly ["https://gwan-ssl.wandevs.org:46891"];
        };
    };
    readonly blockExplorers: {
        readonly etherscan: {
            readonly name: "WanScanTest";
            readonly url: "https://wanscan.org";
        };
        readonly default: {
            readonly name: "WanScanTest";
            readonly url: "https://wanscan.org";
        };
    };
    readonly contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 24743448;
        };
    };
    readonly testnet: true;
}, import("../../types/chain.js").ChainConfig<undefined>>;
//# sourceMappingURL=wanchainTestnet.d.ts.map