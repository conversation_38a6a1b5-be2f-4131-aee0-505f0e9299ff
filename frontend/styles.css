/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #0d1421;
    color: #ffffff;
    min-height: 100vh;
    line-height: 1.6;
}

/* Navigation */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(13, 20, 33, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: bold;
    color: #ff007a;
}

.logo-icon {
    font-size: 2rem;
}

.nav-links {
    display: flex;
    gap: 1rem;
}

.nav-link {
    background: none;
    border: none;
    color: #8b949e;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.nav-link:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    color: #ff007a;
    background: rgba(255, 0, 122, 0.1);
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.network-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 20px;
    font-size: 0.875rem;
}

.network-dot {
    width: 8px;
    height: 8px;
    background: #22c55e;
    border-radius: 50%;
}

.connect-btn {
    background: linear-gradient(135deg, #ff007a, #ff6b9d);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.connect-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 0, 122, 0.3);
}

/* Wallet Banner */
.wallet-banner {
    background: rgba(255, 0, 122, 0.1);
    border-bottom: 1px solid rgba(255, 0, 122, 0.2);
    padding: 0.75rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wallet-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.wallet-address {
    font-family: monospace;
    color: #ff007a;
    font-weight: 600;
}

.wallet-balance {
    color: #8b949e;
    font-size: 0.875rem;
}

.wallet-actions {
    display: flex;
    gap: 0.5rem;
}

.wallet-action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
}

.wallet-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Main Container */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: calc(100vh - 140px);
}

/* Page System */
.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ff007a, #ff6b9d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-header p {
    color: #8b949e;
    font-size: 1.125rem;
}

/* Cards */
.swap-card, .liquidity-card, .swap-forward-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 1.5rem;
    max-width: 480px;
    margin: 0 auto;
    backdrop-filter: blur(10px);
}

.swap-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.swap-title {
    font-size: 1.25rem;
    font-weight: 600;
}

.settings-btn {
    background: none;
    border: none;
    color: #8b949e;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s;
    font-size: 1.125rem;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* Token Input */
.token-input {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s;
    position: relative;
}

.token-input:hover {
    border-color: rgba(255, 255, 255, 0.2);
}

.token-input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.token-label {
    font-size: 0.875rem;
    color: #8b949e;
    font-weight: 500;
}

.token-balance {
    font-size: 0.875rem;
    color: #8b949e;
    cursor: pointer;
    transition: color 0.2s;
}

.token-balance:hover {
    color: #ff007a;
}

.token-input-main {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.amount-input {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 500;
    flex: 1;
    outline: none;
    width: 100%;
}

.amount-input::placeholder {
    color: #8b949e;
}

.token-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 600;
}

.token-selector:hover {
    background: rgba(255, 255, 255, 0.15);
}

.token-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff007a, #ff6b9d);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.dropdown-arrow {
    font-size: 0.75rem;
    color: #8b949e;
}

.max-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(255, 0, 122, 0.2);
    border: 1px solid rgba(255, 0, 122, 0.3);
    color: #ff007a;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.max-btn:hover {
    background: rgba(255, 0, 122, 0.3);
}

/* Swap Arrow */
.swap-arrow-container {
    display: flex;
    justify-content: center;
    margin: 0.5rem 0;
    position: relative;
}

.swap-arrow {
    background: rgba(255, 255, 255, 0.1);
    border: 4px solid #0d1421;
    border-radius: 12px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
    color: #8b949e;
}

.swap-arrow:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    transform: rotate(180deg);
}

/* Price Info */
.price-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
    font-size: 0.875rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    color: #8b949e;
}

.price-row:last-child {
    margin-bottom: 0;
}

.price-row span:last-child {
    color: #ffffff;
    font-weight: 500;
}

.price-impact-low {
    color: #22c55e !important;
}

.price-impact-medium {
    color: #f59e0b !important;
}

.price-impact-high {
    color: #ef4444 !important;
}

/* Buttons */
.swap-btn, .action-btn {
    width: 100%;
    background: linear-gradient(135deg, #ff007a, #ff6b9d);
    border: none;
    border-radius: 16px;
    padding: 1rem;
    color: white;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: 1rem;
}

.swap-btn:hover:not(:disabled), .action-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 0, 122, 0.3);
}

.swap-btn:disabled, .action-btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    color: #8b949e;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Status Messages */
.status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 12px;
    text-align: center;
    font-weight: 500;
}

.status.success {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.status.error {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status.info {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Tabs */
.liquidity-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.tab-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #8b949e;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
}

.tab-btn.active {
    background: linear-gradient(135deg, #ff007a, #ff6b9d);
    color: #ffffff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Liquidity Components */
.pair-selector {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.token-pair {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.pair-token {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s;
}

.pair-token:hover {
    background: rgba(255, 255, 255, 0.15);
}

.pair-separator {
    font-size: 1.5rem;
    color: #8b949e;
}

.fee-tier {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: center;
}

.fee-tier select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    padding: 0.5rem;
}

.liquidity-amounts {
    display: grid;
    gap: 1rem;
    margin-bottom: 1rem;
}

.amount-input-group {
    position: relative;
}

.amount-input-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #8b949e;
    font-size: 0.875rem;
}

.amount-input-group input {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    color: #ffffff;
    font-size: 1.125rem;
}

/* Swap & Forward Components */
.feature-highlight {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 0, 122, 0.1);
    border: 1px solid rgba(255, 0, 122, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.highlight-icon {
    font-size: 2rem;
}

.highlight-text h3 {
    margin-bottom: 0.25rem;
    color: #ff007a;
}

.highlight-text p {
    color: #8b949e;
    font-size: 0.875rem;
}

.recipient-section {
    margin-top: 1rem;
}

.recipient-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.recipient-toggle input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.recipient-input {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
}

.recipient-input label {
    display: block;
    margin-bottom: 0.5rem;
    color: #8b949e;
    font-size: 0.875rem;
}

.recipient-input input {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.75rem;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.recipient-presets {
    display: flex;
    gap: 0.5rem;
}

.recipient-presets button {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #8b949e;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
}

.recipient-presets button:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
}

/* Fees Page */
.fees-grid {
    display: grid;
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.fee-stats-card, .claimable-fees-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.fee-stats-card h3, .claimable-fees-card h3 {
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #ff007a;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #8b949e;
    font-size: 0.875rem;
}

.claimable-amount {
    text-align: center;
    margin-bottom: 2rem;
}

.amount-display {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.amount-display .amount {
    color: #ff007a;
}

.amount-display .token {
    color: #8b949e;
    font-size: 1.5rem;
}

.amount-usd {
    color: #8b949e;
    font-size: 1.125rem;
}

.claim-btn {
    width: 100%;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border: none;
    border-radius: 16px;
    padding: 1rem;
    color: white;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 1rem;
}

.claim-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.claim-btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    color: #8b949e;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.claim-info {
    text-align: center;
    color: #8b949e;
    font-size: 0.875rem;
}

/* Admin Panel */
.admin-panel {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 20px;
    padding: 2rem;
}

.admin-panel h3 {
    color: #ef4444;
    margin-bottom: 1.5rem;
}

.admin-actions {
    display: grid;
    gap: 1.5rem;
}

.admin-action {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
}

.admin-action label {
    display: block;
    margin-bottom: 0.5rem;
    color: #8b949e;
    font-size: 0.875rem;
}

.action-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-row input, .action-row select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.5rem;
    color: #ffffff;
    flex: 1;
}

.input-suffix {
    color: #8b949e;
    font-size: 0.875rem;
}

.admin-btn {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 600;
}

.admin-btn:hover {
    background: rgba(239, 68, 68, 0.3);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: #0d1421;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    color: #8b949e;
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.2s;
}

.close-btn:hover {
    color: #ffffff;
}

.token-search input {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    color: #ffffff;
    margin-bottom: 1rem;
}

.token-list {
    max-height: 300px;
    overflow-y: auto;
}

.settings-content {
    display: grid;
    gap: 1.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #8b949e;
    font-size: 0.875rem;
}

.slippage-options {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.slippage-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #8b949e;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.slippage-btn:hover, .slippage-btn.active {
    background: rgba(255, 0, 122, 0.2);
    color: #ff007a;
}

.deadline-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.deadline-input input {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.5rem;
    color: #ffffff;
    width: 80px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 0, 122, 0.3);
    border-top: 3px solid #ff007a;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #ffffff;
    font-size: 1.125rem;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .nav-left {
        flex-direction: column;
        gap: 1rem;
    }

    .main-container {
        padding: 1rem;
    }

    .swap-card, .liquidity-card, .swap-forward-card {
        padding: 1rem;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .wallet-banner {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .amount-display {
        font-size: 2rem;
    }

    .modal-content {
        padding: 1rem;
        width: 95%;
    }
}
