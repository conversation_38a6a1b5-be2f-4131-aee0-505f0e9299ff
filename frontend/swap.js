// Swap Functionality
class SwapManager {
    constructor() {
        this.currentTokenIn = 'ETH';
        this.currentTokenOut = 'UBA';
        this.priceUpdateInterval = null;
        this.lastQuote = null;
        
        this.initializeSwapListeners();
    }
    
    // Initialize swap-related event listeners
    initializeSwapListeners() {
        // Amount input listeners
        const amountInInput = document.getElementById('amountIn');
        if (amountInInput) {
            amountInInput.addEventListener('input', () => {
                this.updateSwapButton();
                this.updatePriceInfo();
            });
        }
        
        // Token selector listeners
        const tokenInSelector = document.getElementById('tokenInSelector');
        const tokenOutSelector = document.getElementById('tokenOutSelector');
        
        if (tokenInSelector) {
            tokenInSelector.addEventListener('click', () => {
                window.openTokenSelector('in');
            });
        }
        
        if (tokenOutSelector) {
            tokenOutSelector.addEventListener('click', () => {
                window.openTokenSelector('out');
            });
        }
        
        // Swap button listener
        const swapButton = document.getElementById('swapButton');
        if (swapButton) {
            swapButton.addEventListener('click', () => {
                this.executeSwap();
            });
        }
        
        // Max button listener
        const fromBalance = document.getElementById('fromBalance');
        if (fromBalance) {
            fromBalance.addEventListener('click', () => {
                this.setMaxAmount();
            });
        }
    }
    
    // Set maximum amount for input
    async setMaxAmount() {
        try {
            if (!window.uiManager.isWalletConnected) {
                window.uiManager.showStatus(CONFIG.ERRORS.WALLET_NOT_CONNECTED, 'error');
                return;
            }
            
            const balance = window.uiManager.balances[this.currentTokenIn] || 0;
            let maxAmount = parseFloat(balance);
            
            // Leave some ETH for gas if swapping ETH
            if (this.currentTokenIn === 'ETH') {
                maxAmount = Math.max(0, maxAmount - 0.01);
            }
            
            const amountInput = document.getElementById('amountIn');
            if (amountInput) {
                amountInput.value = maxAmount.toFixed(6);
                this.updateSwapButton();
                this.updatePriceInfo();
            }
            
        } catch (error) {
            console.error('Error setting max amount:', error);
            window.uiManager.showStatus('Error setting maximum amount', 'error');
        }
    }
    
    // Swap token positions
    swapTokens() {
        const tempToken = this.currentTokenIn;
        this.currentTokenIn = this.currentTokenOut;
        this.currentTokenOut = tempToken;
        
        // Update global variables
        window.currentTokenIn = this.currentTokenIn;
        window.currentTokenOut = this.currentTokenOut;
        
        // Update UI
        window.uiManager.updateTokenUI();
        window.uiManager.updateTokenInputBalances();
        
        // Clear amounts
        const amountInInput = document.getElementById('amountIn');
        const amountOutInput = document.getElementById('amountOut');
        
        if (amountInInput) amountInInput.value = '';
        if (amountOutInput) amountOutInput.value = '';
        
        // Hide price info
        const priceInfo = document.getElementById('priceInfo');
        if (priceInfo) priceInfo.style.display = 'none';
        
        this.updateSwapButton();
    }
    
    // Update swap button text and state
    updateSwapButton() {
        const swapButton = document.getElementById('swapButton');
        const amountInput = document.getElementById('amountIn');
        
        if (!swapButton) return;
        
        if (!window.uiManager.isWalletConnected) {
            swapButton.textContent = 'Connect Wallet';
            swapButton.disabled = true;
            return;
        }
        
        const amount = parseFloat(amountInput?.value || 0);
        
        if (!amount || amount <= 0) {
            swapButton.textContent = 'Enter an amount';
            swapButton.disabled = true;
            return;
        }
        
        const balance = parseFloat(window.uiManager.balances[this.currentTokenIn] || 0);
        if (amount > balance) {
            swapButton.textContent = 'Insufficient balance';
            swapButton.disabled = true;
            return;
        }
        
        if (this.currentTokenIn === this.currentTokenOut) {
            swapButton.textContent = 'Select different tokens';
            swapButton.disabled = true;
            return;
        }
        
        swapButton.textContent = `Swap ${CONFIG.TOKENS[this.currentTokenIn].symbol} for ${CONFIG.TOKENS[this.currentTokenOut].symbol}`;
        swapButton.disabled = false;
    }
    
    // Update price information
    async updatePriceInfo() {
        const amountInput = document.getElementById('amountIn');
        const amountOutInput = document.getElementById('amountOut');
        const priceInfo = document.getElementById('priceInfo');
        
        if (!amountInput || !amountOutInput || !priceInfo) return;
        
        const amount = parseFloat(amountInput.value);
        
        if (!amount || amount <= 0) {
            priceInfo.style.display = 'none';
            amountOutInput.value = '';
            return;
        }
        
        try {
            // Simple mock price calculation for demo
            // In production, this would call Uniswap quoter
            let estimatedOutput;
            let exchangeRateText;
            
            if (this.currentTokenIn === 'ETH' && this.currentTokenOut === 'UBA') {
                estimatedOutput = amount * 1000; // 1 ETH = 1000 UBA (mock rate)
                exchangeRateText = '1 ETH = 1000 UBA';
            } else if (this.currentTokenIn === 'UBA' && this.currentTokenOut === 'ETH') {
                estimatedOutput = amount / 1000; // 1000 UBA = 1 ETH (mock rate)
                exchangeRateText = '1000 UBA = 1 ETH';
            } else {
                estimatedOutput = amount;
                exchangeRateText = '1:1';
            }
            
            // Calculate fees
            const protocolFeeAmount = estimatedOutput * (CONFIG.PROTOCOL.FEE_BPS / CONFIG.PROTOCOL.BASIS_POINTS);
            const minimumReceived = estimatedOutput - protocolFeeAmount;
            
            // Update UI
            amountOutInput.value = estimatedOutput.toFixed(6);
            
            // Update price info
            document.getElementById('exchangeRate').textContent = exchangeRateText;
            document.getElementById('protocolFee').textContent = `${protocolFeeAmount.toFixed(6)} ${CONFIG.TOKENS[this.currentTokenOut].symbol}`;
            document.getElementById('minimumReceived').textContent = `${minimumReceived.toFixed(6)} ${CONFIG.TOKENS[this.currentTokenOut].symbol}`;
            
            // Show price info
            priceInfo.style.display = 'block';
            
        } catch (error) {
            console.error('Error updating price info:', error);
            priceInfo.style.display = 'none';
        }
    }
    
    // Execute swap transaction
    async executeSwap() {
        try {
            if (!window.uiManager.isWalletConnected) {
                window.uiManager.showStatus(CONFIG.ERRORS.WALLET_NOT_CONNECTED, 'error');
                return;
            }
            
            const amountInput = document.getElementById('amountIn');
            const amount = parseFloat(amountInput.value);
            
            if (!amount || amount <= 0) {
                window.uiManager.showStatus(CONFIG.ERRORS.INVALID_AMOUNT, 'error');
                return;
            }
            
            // Validate balance
            const balance = parseFloat(window.uiManager.balances[this.currentTokenIn] || 0);
            if (amount > balance) {
                window.uiManager.showStatus(CONFIG.ERRORS.INSUFFICIENT_BALANCE, 'error');
                return;
            }
            
            window.uiManager.showLoading('Preparing swap transaction...');
            
            const contracts = window.contractFactory.getAllContracts();
            const swapContract = contracts.swapAndForward.getInstance();
            
            // Prepare transaction parameters
            const tokenIn = CONFIG.TOKENS[this.currentTokenIn].address;
            const tokenOut = CONFIG.TOKENS[this.currentTokenOut].address;
            const amountInWei = ContractUtils.parseTokenAmount(amount);
            const deadline = Math.floor(Date.now() / 1000) + (window.uiManager.settings.deadline * 60);
            const fee = CONFIG.FEE_TIERS.MEDIUM; // 0.3% pool fee
            const amountOutMin = 0; // Accept any amount for demo (should calculate based on slippage)
            const recipient = window.uiManager.userAddress;
            
            let tx;
            
            if (this.currentTokenIn === 'ETH') {
                // ETH to Token swap
                window.uiManager.showLoading('Executing ETH to token swap...');
                
                tx = await swapContract.swapAndForwardSingleHop(
                    tokenIn,
                    amountInWei,
                    tokenOut,
                    amountOutMin,
                    recipient,
                    fee,
                    deadline,
                    { value: amountInWei }
                );
            } else {
                // Token to ETH/Token swap
                const tokenContract = new ethers.Contract(
                    tokenIn,
                    CONTRACT_ABIS.ERC20,
                    window.signer
                );
                
                // Check allowance
                const allowance = await tokenContract.allowance(
                    window.uiManager.userAddress,
                    CONFIG.CONTRACTS.SWAP_AND_FORWARD
                );
                
                if (allowance.lt(amountInWei)) {
                    window.uiManager.showLoading('Approving token spend...');
                    
                    const approveTx = await tokenContract.approve(
                        CONFIG.CONTRACTS.SWAP_AND_FORWARD,
                        amountInWei
                    );
                    await approveTx.wait();
                }
                
                window.uiManager.showLoading('Executing token swap...');
                
                tx = await swapContract.swapAndForwardSingleHop(
                    tokenIn,
                    amountInWei,
                    tokenOut,
                    amountOutMin,
                    recipient,
                    fee,
                    deadline
                );
            }
            
            window.uiManager.showLoading('Waiting for transaction confirmation...');
            
            const receipt = await tx.wait();
            
            window.uiManager.hideLoading();
            window.uiManager.showStatus(
                `${CONFIG.SUCCESS.SWAP_COMPLETED} Transaction: ${receipt.transactionHash}`,
                'success'
            );
            
            // Clear form
            amountInput.value = '';
            document.getElementById('amountOut').value = '';
            document.getElementById('priceInfo').style.display = 'none';
            
            // Update balances
            await window.uiManager.updateTokenBalances();
            this.updateSwapButton();
            
        } catch (error) {
            console.error('Swap error:', error);
            window.uiManager.hideLoading();
            
            let errorMessage = CONFIG.ERRORS.TRANSACTION_FAILED;
            
            if (error.code === 4001) {
                errorMessage = CONFIG.ERRORS.USER_REJECTED;
            } else if (error.message.includes('slippage')) {
                errorMessage = CONFIG.ERRORS.SLIPPAGE_TOO_HIGH;
            }
            
            window.uiManager.showStatus(errorMessage, 'error');
        }
    }
}

// Global swap manager instance
window.swapManager = new SwapManager();

// Global utility functions
window.setMaxAmount = () => window.swapManager.setMaxAmount();
window.swapTokens = () => window.swapManager.swapTokens();
window.currentTokenIn = 'ETH';
window.currentTokenOut = 'UBA';
