// Fees Management
class FeesManager {
    constructor() {
        this.isAdmin = false;
        this.feeStats = {
            totalFeesCollected: '0',
            totalUBABurned: '0',
            totalTransactions: '0',
            protocolFeeRate: '0.5%'
        };
        this.claimableAmount = '0';
        
        this.initializeFeesListeners();
    }
    
    // Initialize fees-related event listeners
    initializeFeesListeners() {
        // Claim fees button
        const claimFeesBtn = document.getElementById('claimFeesBtn');
        if (claimFeesBtn) {
            claimFeesBtn.addEventListener('click', () => {
                this.claimFees();
            });
        }
        
        // Admin buttons
        const processFeesBtn = document.getElementById('processFeesBtn');
        const updateMinBtn = document.getElementById('updateMinBtn');
        const updateFeeBtn = document.getElementById('updateFeeBtn');
        
        if (processFeesBtn) {
            processFeesBtn.addEventListener('click', () => {
                this.processFees();
            });
        }
        
        if (updateMinBtn) {
            updateMinBtn.addEventListener('click', () => {
                this.updateMinimumAmount();
            });
        }
        
        if (updateFeeBtn) {
            updateFeeBtn.addEventListener('click', () => {
                this.updateProtocolFee();
            });
        }
    }
    
    // Load fee statistics
    async loadFeeStatistics() {
        try {
            if (!window.contractFactory) return;
            
            const contracts = window.contractFactory.getAllContracts();
            
            // Get protocol fee rate
            const protocolFeeBps = await contracts.swapAndForward.getInstance().protocolFeeBps();
            const feeRate = (protocolFeeBps / CONFIG.PROTOCOL.BASIS_POINTS * 100).toFixed(1);
            
            // For demo purposes, we'll use mock data for other statistics
            // In production, these would be fetched from events or a subgraph
            this.feeStats = {
                totalFeesCollected: '12.5 ETH',
                totalUBABurned: '25,000 UBA',
                totalTransactions: '1,247',
                protocolFeeRate: `${feeRate}%`
            };
            
            this.updateFeeStatsUI();
            
        } catch (error) {
            console.error('Error loading fee statistics:', error);
            
            // Use default values on error
            this.feeStats = {
                totalFeesCollected: '0 ETH',
                totalUBABurned: '0 UBA',
                totalTransactions: '0',
                protocolFeeRate: '0.5%'
            };
            
            this.updateFeeStatsUI();
        }
    }
    
    // Update fee statistics UI
    updateFeeStatsUI() {
        const elements = {
            totalFeesCollected: document.getElementById('totalFeesCollected'),
            totalUBABurned: document.getElementById('totalUBABurned'),
            totalTransactions: document.getElementById('totalTransactions'),
            protocolFeeRate: document.getElementById('protocolFeeRate')
        };
        
        Object.keys(elements).forEach(key => {
            if (elements[key]) {
                elements[key].textContent = this.feeStats[key];
            }
        });
    }
    
    // Load claimable fees for user
    async loadClaimableFees() {
        try {
            if (!window.uiManager.isWalletConnected) return;
            
            // For demo purposes, we'll calculate based on user's UBA balance
            // In production, this would be based on liquidity provision history
            const ubaBalance = parseFloat(window.uiManager.balances.UBA || 0);
            const claimableAmount = Math.max(0, ubaBalance * 0.01); // 1% of UBA balance as claimable
            
            this.claimableAmount = claimableAmount.toFixed(4);
            
            // Update UI
            const claimableAmountEl = document.getElementById('claimableAmount');
            const claimableUSDEl = document.getElementById('claimableUSD');
            const claimFeesBtn = document.getElementById('claimFeesBtn');
            
            if (claimableAmountEl) {
                claimableAmountEl.textContent = this.claimableAmount;
            }
            
            if (claimableUSDEl) {
                // Mock USD value calculation
                const usdValue = (parseFloat(this.claimableAmount) * 2.5).toFixed(2);
                claimableUSDEl.textContent = `~$${usdValue}`;
            }
            
            if (claimFeesBtn) {
                claimFeesBtn.disabled = parseFloat(this.claimableAmount) <= 0;
            }
            
        } catch (error) {
            console.error('Error loading claimable fees:', error);
        }
    }
    
    // Check if user has admin access
    async checkAdminAccess() {
        try {
            if (!window.uiManager.isWalletConnected || !window.contractFactory) return;
            
            const contracts = window.contractFactory.getAllContracts();
            
            // Check if user is owner of SwapAndForward contract
            const swapContract = contracts.swapAndForward.getInstance();
            
            try {
                // Try to call an owner-only function to check access
                // This is a simple check - in production you'd have a proper owner() function
                const owner = await swapContract.owner();
                this.isAdmin = owner.toLowerCase() === window.uiManager.userAddress.toLowerCase();
            } catch (error) {
                // If the contract doesn't have owner() function, assume not admin
                this.isAdmin = false;
            }
            
            // Show/hide admin panel
            const adminPanel = document.getElementById('adminPanel');
            if (adminPanel) {
                if (this.isAdmin) {
                    adminPanel.classList.remove('hidden');
                } else {
                    adminPanel.classList.add('hidden');
                }
            }
            
        } catch (error) {
            console.error('Error checking admin access:', error);
            this.isAdmin = false;
        }
    }
    
    // Claim fees for user
    async claimFees() {
        try {
            if (!window.uiManager.isWalletConnected) {
                window.uiManager.showStatus(CONFIG.ERRORS.WALLET_NOT_CONNECTED, 'error');
                return;
            }
            
            const claimableAmount = parseFloat(this.claimableAmount);
            if (claimableAmount <= 0) {
                window.uiManager.showStatus('No fees available to claim', 'error');
                return;
            }
            
            window.uiManager.showLoading('Claiming fees...');
            
            // For demo purposes, we'll simulate the transaction
            // In production, this would call the actual fee claiming function
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            window.uiManager.hideLoading();
            window.uiManager.showStatus(
                `Successfully claimed ${this.claimableAmount} UBA in fees!`,
                'success'
            );
            
            // Reset claimable amount
            this.claimableAmount = '0';
            
            // Update UI
            await this.loadClaimableFees();
            await window.uiManager.updateTokenBalances();
            
        } catch (error) {
            console.error('Claim fees error:', error);
            window.uiManager.hideLoading();
            
            let errorMessage = CONFIG.ERRORS.TRANSACTION_FAILED;
            if (error.code === 4001) {
                errorMessage = CONFIG.ERRORS.USER_REJECTED;
            }
            
            window.uiManager.showStatus(errorMessage, 'error');
        }
    }
    
    // Process fees (Admin only)
    async processFees() {
        try {
            if (!this.isAdmin) {
                window.uiManager.showStatus('Access denied: Admin only', 'error');
                return;
            }
            
            const feeTokenSelect = document.getElementById('feeTokenSelect');
            const selectedToken = feeTokenSelect?.value || 'eth';
            
            window.uiManager.showLoading('Processing collected fees...');
            
            const contracts = window.contractFactory.getAllContracts();
            const feeDistributor = contracts.feeDistributor.getInstance();
            
            // Get the token address
            const tokenAddress = selectedToken === 'eth' 
                ? CONFIG.CONTRACTS.WETH 
                : CONFIG.CONTRACTS.UBA_TOKEN;
            
            // Process fees with medium fee tier
            const tx = await feeDistributor.processFees(tokenAddress, CONFIG.FEE_TIERS.MEDIUM);
            await tx.wait();
            
            window.uiManager.hideLoading();
            window.uiManager.showStatus('Fees processed successfully!', 'success');
            
            // Refresh statistics
            await this.loadFeeStatistics();
            
        } catch (error) {
            console.error('Process fees error:', error);
            window.uiManager.hideLoading();
            
            let errorMessage = CONFIG.ERRORS.TRANSACTION_FAILED;
            if (error.code === 4001) {
                errorMessage = CONFIG.ERRORS.USER_REJECTED;
            }
            
            window.uiManager.showStatus(errorMessage, 'error');
        }
    }
    
    // Update minimum process amount (Admin only)
    async updateMinimumAmount() {
        try {
            if (!this.isAdmin) {
                window.uiManager.showStatus('Access denied: Admin only', 'error');
                return;
            }
            
            const newMinAmountInput = document.getElementById('newMinAmount');
            const newAmount = parseFloat(newMinAmountInput?.value || 0);
            
            if (!newAmount || newAmount <= 0) {
                window.uiManager.showStatus('Please enter a valid amount', 'error');
                return;
            }
            
            window.uiManager.showLoading('Updating minimum process amount...');
            
            const contracts = window.contractFactory.getAllContracts();
            const feeDistributor = contracts.feeDistributor.getInstance();
            
            const amountWei = ContractUtils.parseTokenAmount(newAmount);
            const tx = await feeDistributor.updateMinimumProcessAmount(amountWei);
            await tx.wait();
            
            window.uiManager.hideLoading();
            window.uiManager.showStatus('Minimum process amount updated successfully!', 'success');
            
            // Clear input
            newMinAmountInput.value = '';
            
        } catch (error) {
            console.error('Update minimum amount error:', error);
            window.uiManager.hideLoading();
            
            let errorMessage = CONFIG.ERRORS.TRANSACTION_FAILED;
            if (error.code === 4001) {
                errorMessage = CONFIG.ERRORS.USER_REJECTED;
            }
            
            window.uiManager.showStatus(errorMessage, 'error');
        }
    }
    
    // Update protocol fee (Admin only)
    async updateProtocolFee() {
        try {
            if (!this.isAdmin) {
                window.uiManager.showStatus('Access denied: Admin only', 'error');
                return;
            }
            
            const newFeeRateInput = document.getElementById('newFeeRate');
            const newFeeBps = parseInt(newFeeRateInput?.value || 0);
            
            if (!newFeeBps || newFeeBps < 0 || newFeeBps > CONFIG.PROTOCOL.MAX_FEE_BPS) {
                window.uiManager.showStatus(`Fee must be between 0 and ${CONFIG.PROTOCOL.MAX_FEE_BPS} BPS`, 'error');
                return;
            }
            
            window.uiManager.showLoading('Updating protocol fee...');
            
            const contracts = window.contractFactory.getAllContracts();
            const swapContract = contracts.swapAndForward.getInstance();
            
            const tx = await swapContract.updateProtocolFee(newFeeBps);
            await tx.wait();
            
            window.uiManager.hideLoading();
            window.uiManager.showStatus('Protocol fee updated successfully!', 'success');
            
            // Clear input
            newFeeRateInput.value = '';
            
            // Refresh statistics
            await this.loadFeeStatistics();
            
        } catch (error) {
            console.error('Update protocol fee error:', error);
            window.uiManager.hideLoading();
            
            let errorMessage = CONFIG.ERRORS.TRANSACTION_FAILED;
            if (error.code === 4001) {
                errorMessage = CONFIG.ERRORS.USER_REJECTED;
            }
            
            window.uiManager.showStatus(errorMessage, 'error');
        }
    }
}

// Global fees manager instance
window.feesManager = new FeesManager();
