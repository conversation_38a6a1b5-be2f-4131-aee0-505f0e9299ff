// Contract ABIs and Interfaces
const CONTRACT_ABIS = {
    // ERC20 Standard ABI (simplified)
    ERC20: [
        "function balanceOf(address owner) view returns (uint256)",
        "function transfer(address to, uint256 amount) returns (bool)",
        "function approve(address spender, uint256 amount) returns (bool)",
        "function allowance(address owner, address spender) view returns (uint256)",
        "function symbol() view returns (string)",
        "function name() view returns (string)",
        "function decimals() view returns (uint8)",
        "function totalSupply() view returns (uint256)",
        "event Transfer(address indexed from, address indexed to, uint256 value)",
        "event Approval(address indexed owner, address indexed spender, uint256 value)"
    ],
    
    // UBA Token ABI
    UBA_TOKEN: [
        "function balanceOf(address owner) view returns (uint256)",
        "function transfer(address to, uint256 amount) returns (bool)",
        "function approve(address spender, uint256 amount) returns (bool)",
        "function allowance(address owner, address spender) view returns (uint256)",
        "function symbol() view returns (string)",
        "function name() view returns (string)",
        "function decimals() view returns (uint8)",
        "function totalSupply() view returns (uint256)",
        "function mint(address to, uint256 amount)",
        "function burnForFees(uint256 amount)",
        "function setFeeDistributorAddress(address _newFeeDistributor)",
        "function feeDistributorContractAddress() view returns (address)",
        "function MAX_SUPPLY() view returns (uint256)",
        "function INITIAL_SUPPLY() view returns (uint256)",
        "event TokensMinted(address indexed to, uint256 amount)",
        "event TokensBurnedForFees(uint256 amount, address indexed burner)"
    ],
    
    // SwapAndForward ABI
    SWAP_AND_FORWARD: [
        "function swapAndForwardSingleHop(address tokenIn, uint256 amountIn, address tokenOut, uint256 amountOutMin, address recipient, uint24 fee, uint256 deadline) payable returns (uint256)",
        "function protocolFeeBps() view returns (uint256)",
        "function updateProtocolFee(uint256 newFeeBps)",
        "function emergencyRecoverToken(address token, uint256 amount)",
        "function emergencyRecoverETH()",
        "event SwapAndForwardExecuted(address indexed user, address indexed tokenIn, address indexed tokenOut, uint256 amountIn, uint256 amountOut, uint256 protocolFee, address recipient)",
        "event FeesCollected(address indexed token, uint256 amount, address indexed feeDistributor)",
        "event ProtocolFeeUpdated(uint256 oldFee, uint256 newFee)"
    ],
    
    // FeeDistributor ABI
    FEE_DISTRIBUTOR: [
        "function collectFees(address token, uint256 amount)",
        "function processFees(address token, uint24 fee)",
        "function updateMinimumProcessAmount(uint256 newAmount)",
        "function emergencyRecoverToken(address token, uint256 amount)",
        "function getCollectedFees(address token) view returns (uint256)",
        "function minimumProcessAmount() view returns (uint256)",
        "function ubaToken() view returns (address)",
        "function uniswapRouter() view returns (address)",
        "function WETH() view returns (address)",
        "event FeesProcessed(address indexed token, uint256 amount, uint256 ubaAmount)",
        "event MinimumProcessAmountUpdated(uint256 oldAmount, uint256 newAmount)",
        "event FeesCollected(address indexed token, uint256 amount)"
    ],
    
    // Uniswap V3 Router ABI (simplified)
    UNISWAP_ROUTER: [
        "function exactInputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum, uint160 sqrtPriceLimitX96)) payable returns (uint256 amountOut)",
        "function exactInput((bytes path, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum)) payable returns (uint256 amountOut)"
    ],
    
    // Uniswap V3 Quoter ABI (simplified)
    UNISWAP_QUOTER: [
        "function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) returns (uint256 amountOut, uint160 sqrtPriceX96After, uint32 initializedTicksCrossed, uint256 gasEstimate)"
    ],
    
    // WETH ABI
    WETH: [
        "function deposit() payable",
        "function withdraw(uint256 amount)",
        "function balanceOf(address owner) view returns (uint256)",
        "function transfer(address to, uint256 amount) returns (bool)",
        "function approve(address spender, uint256 amount) returns (bool)",
        "function allowance(address owner, address spender) view returns (uint256)"
    ]
};

// Contract Interface Class
class ContractInterface {
    constructor(address, abi, signer) {
        this.address = address;
        this.abi = abi;
        this.contract = new ethers.Contract(address, abi, signer);
    }
    
    // Get contract instance
    getInstance() {
        return this.contract;
    }
    
    // Connect with different signer
    connect(signer) {
        this.contract = this.contract.connect(signer);
        return this;
    }
    
    // Get contract address
    getAddress() {
        return this.address;
    }
}

// Contract Factory
class ContractFactory {
    constructor(provider, signer) {
        this.provider = provider;
        this.signer = signer;
        this.contracts = {};
    }
    
    // Initialize all contracts
    async initializeContracts() {
        try {
            // UBA Token
            this.contracts.ubaToken = new ContractInterface(
                CONFIG.CONTRACTS.UBA_TOKEN,
                CONTRACT_ABIS.UBA_TOKEN,
                this.signer
            );
            
            // SwapAndForward
            this.contracts.swapAndForward = new ContractInterface(
                CONFIG.CONTRACTS.SWAP_AND_FORWARD,
                CONTRACT_ABIS.SWAP_AND_FORWARD,
                this.signer
            );
            
            // FeeDistributor
            this.contracts.feeDistributor = new ContractInterface(
                CONFIG.CONTRACTS.FEE_DISTRIBUTOR,
                CONTRACT_ABIS.FEE_DISTRIBUTOR,
                this.signer
            );
            
            // WETH
            this.contracts.weth = new ContractInterface(
                CONFIG.CONTRACTS.WETH,
                CONTRACT_ABIS.WETH,
                this.signer
            );
            
            // Uniswap Router
            this.contracts.uniswapRouter = new ContractInterface(
                CONFIG.CONTRACTS.UNISWAP_ROUTER,
                CONTRACT_ABIS.UNISWAP_ROUTER,
                this.signer
            );
            
            // Uniswap Quoter
            this.contracts.uniswapQuoter = new ContractInterface(
                CONFIG.CONTRACTS.UNISWAP_QUOTER,
                CONTRACT_ABIS.UNISWAP_QUOTER,
                this.signer
            );
            
            console.log('All contracts initialized successfully');
            return this.contracts;
            
        } catch (error) {
            console.error('Error initializing contracts:', error);
            throw error;
        }
    }
    
    // Get specific contract
    getContract(name) {
        return this.contracts[name];
    }
    
    // Get all contracts
    getAllContracts() {
        return this.contracts;
    }
    
    // Update signer for all contracts
    updateSigner(newSigner) {
        this.signer = newSigner;
        Object.values(this.contracts).forEach(contract => {
            contract.connect(newSigner);
        });
    }
}

// Utility functions for contract interactions
const ContractUtils = {
    // Format token amount for display
    formatTokenAmount(amount, decimals = 18, displayDecimals = 4) {
        try {
            const formatted = ethers.utils.formatUnits(amount, decimals);
            return parseFloat(formatted).toFixed(displayDecimals);
        } catch (error) {
            console.error('Error formatting token amount:', error);
            return '0.0000';
        }
    },
    
    // Parse token amount for contract calls
    parseTokenAmount(amount, decimals = 18) {
        try {
            return ethers.utils.parseUnits(amount.toString(), decimals);
        } catch (error) {
            console.error('Error parsing token amount:', error);
            throw new Error('Invalid amount format');
        }
    },
    
    // Check if address is valid
    isValidAddress(address) {
        try {
            return ethers.utils.isAddress(address);
        } catch (error) {
            return false;
        }
    },
    
    // Get transaction receipt with retry
    async getTransactionReceipt(txHash, maxRetries = 10) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                const receipt = await window.provider.getTransactionReceipt(txHash);
                if (receipt) {
                    return receipt;
                }
            } catch (error) {
                console.warn(`Retry ${i + 1} failed:`, error);
            }
            
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        throw new Error('Failed to get transaction receipt after retries');
    },
    
    // Estimate gas with buffer
    async estimateGasWithBuffer(contract, method, params, buffer = 1.2) {
        try {
            const estimated = await contract.estimateGas[method](...params);
            return estimated.mul(Math.floor(buffer * 100)).div(100);
        } catch (error) {
            console.error('Gas estimation failed:', error);
            // Return a default gas limit
            return ethers.BigNumber.from('300000');
        }
    }
};

// Export for global use
window.CONTRACT_ABIS = CONTRACT_ABIS;
window.ContractInterface = ContractInterface;
window.ContractFactory = ContractFactory;
window.ContractUtils = ContractUtils;
