// UbaSwap Main Application
class UbaSwapApp {
    constructor() {
        this.provider = null;
        this.signer = null;
        this.contractFactory = null;
        this.isInitialized = false;

        this.init();
    }

    // Initialize the application
    async init() {
        try {
            console.log('🚀 Initializing UbaSwap...');

            // Check if MetaMask is available
            if (typeof window.ethereum === 'undefined') {
                this.showMetaMaskError();
                return;
            }

            // Set up provider
            this.provider = new ethers.providers.Web3Provider(window.ethereum);
            window.provider = this.provider;

            // Check if already connected
            const accounts = await window.ethereum.request({ method: 'eth_accounts' });
            if (accounts.length > 0) {
                await this.connectWallet();
            }

            // Set up event listeners
            this.setupEventListeners();

            // Initialize UI
            this.initializeUI();

            this.isInitialized = true;
            console.log('✅ UbaSwap initialized successfully');

        } catch (error) {
            console.error('❌ Error initializing UbaSwap:', error);
            window.uiManager.showStatus('Failed to initialize application', 'error');
        }
    }

    // Set up event listeners
    setupEventListeners() {
        // Wallet connection
        const connectWalletBtn = document.getElementById('connectWallet');
        if (connectWalletBtn) {
            connectWalletBtn.addEventListener('click', () => this.connectWallet());
        }

        // MetaMask events
        if (window.ethereum) {
            window.ethereum.on('accountsChanged', (accounts) => this.handleAccountsChanged(accounts));
            window.ethereum.on('chainChanged', (chainId) => this.handleChainChanged(chainId));
            window.ethereum.on('disconnect', () => this.handleDisconnect());
        }
    }

    // Initialize UI components
    initializeUI() {
        // Set initial token selection
        window.currentTokenIn = 'ETH';
        window.currentTokenOut = 'UBA';

        // Update token UI
        if (window.uiManager) {
            window.uiManager.updateTokenUI();
        }
    }

    // Connect wallet
    async connectWallet() {
        try {
            console.log('🔗 Connecting wallet...');

            // Request account access
            const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });

            if (accounts.length === 0) {
                throw new Error('No accounts found');
            }

            // Check network
            const chainId = await window.ethereum.request({ method: 'eth_chainId' });
            if (chainId !== CONFIG.SEPOLIA_CHAIN_ID) {
                await this.switchToSepolia();
            }

            // Set up signer
            this.signer = this.provider.getSigner();
            window.signer = this.signer;

            const userAddress = accounts[0];

            // Initialize contracts
            this.contractFactory = new ContractFactory(this.provider, this.signer);
            window.contractFactory = this.contractFactory;

            await this.contractFactory.initializeContracts();

            // Update UI
            window.uiManager.updateWalletStatus(true, userAddress);

            // Load initial data
            await this.loadInitialData();

            console.log('✅ Wallet connected successfully');
            window.uiManager.showStatus(CONFIG.SUCCESS.WALLET_CONNECTED, 'success');

        } catch (error) {
            console.error('❌ Error connecting wallet:', error);

            let errorMessage = 'Failed to connect wallet';
            if (error.code === 4001) {
                errorMessage = 'Connection rejected by user';
            } else if (error.message.includes('network')) {
                errorMessage = 'Please switch to Sepolia network';
            }

            window.uiManager.showStatus(errorMessage, 'error');
        }
    }

    // Switch to Sepolia network
    async switchToSepolia() {
        try {
            await window.ethereum.request({
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: CONFIG.SEPOLIA_CHAIN_ID }],
            });
        } catch (switchError) {
            // Network not added to MetaMask
            if (switchError.code === 4902) {
                try {
                    await window.ethereum.request({
                        method: 'wallet_addEthereumChain',
                        params: [{
                            chainId: CONFIG.SEPOLIA_CHAIN_ID,
                            chainName: 'Sepolia Test Network',
                            nativeCurrency: {
                                name: 'ETH',
                                symbol: 'ETH',
                                decimals: 18,
                            },
                            rpcUrls: ['https://sepolia.infura.io/v3/'],
                            blockExplorerUrls: ['https://sepolia.etherscan.io/'],
                        }],
                    });
                } catch (addError) {
                    throw new Error('Failed to add Sepolia network');
                }
            } else {
                throw switchError;
            }
        }
    }

    // Load initial data after wallet connection
    async loadInitialData() {
        try {
            // Load fee statistics
            await window.feesManager.loadFeeStatistics();

            // Load user-specific data if connected
            if (window.uiManager.isWalletConnected) {
                await window.feesManager.loadClaimableFees();
                await window.feesManager.checkAdminAccess();
                await window.liquidityManager.loadPositions();
            }

        } catch (error) {
            console.error('Error loading initial data:', error);
        }
    }

    // Handle account changes
    async handleAccountsChanged(accounts) {
        if (accounts.length === 0) {
            // User disconnected
            this.disconnect();
        } else {
            // User switched accounts
            await this.connectWallet();
        }
    }

    // Handle chain changes
    handleChainChanged(chainId) {
        // Reload the page when chain changes
        window.location.reload();
    }

    // Handle disconnect
    handleDisconnect() {
        this.disconnect();
    }

    // Disconnect wallet
    disconnect() {
        this.signer = null;
        this.contractFactory = null;
        window.signer = null;
        window.contractFactory = null;

        window.uiManager.updateWalletStatus(false);

        console.log('🔌 Wallet disconnected');
    }

    // Show MetaMask error
    showMetaMaskError() {
        const errorHTML = `
            <div class="metamask-error">
                <h2>MetaMask Required</h2>
                <p>Please install MetaMask to use UbaSwap</p>
                <a href="https://metamask.io/" target="_blank" class="install-btn">Install MetaMask</a>
            </div>
        `;

        document.body.innerHTML = errorHTML;
    }
}

// Global utility functions
window.viewOnEtherscan = () => {
    if (window.uiManager.userAddress) {
        const url = `${CONFIG.LINKS.ETHERSCAN_BASE}/address/${window.uiManager.userAddress}`;
        window.open(url, '_blank');
    }
};

window.disconnectWallet = () => {
    if (window.ubaSwapApp) {
        window.ubaSwapApp.disconnect();
    }
};

// Swap & Forward specific functions
window.toggleCustomRecipient = () => {
    const checkbox = document.getElementById('customRecipient');
    const recipientInput = document.getElementById('recipientInput');

    if (checkbox && recipientInput) {
        if (checkbox.checked) {
            recipientInput.classList.remove('hidden');
        } else {
            recipientInput.classList.add('hidden');
        }
    }
};

window.setRecipient = (type) => {
    const recipientAddress = document.getElementById('recipientAddress');

    if (type === 'self' && window.uiManager.userAddress) {
        recipientAddress.value = window.uiManager.userAddress;
    } else if (type === 'saved') {
        // For demo purposes, use a placeholder
        recipientAddress.value = '0x742d35Cc6634C0532925a3b8D';
    }
};

// Swap & Forward functions
window.setSFMaxAmount = () => {
    // Similar to setMaxAmount but for swap & forward page
    window.setMaxAmount();
};

window.swapSFTokens = () => {
    // Similar to swapTokens but for swap & forward page
    window.swapTokens();
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎯 DOM loaded, initializing UbaSwap...');
    window.ubaSwapApp = new UbaSwapApp();
});
