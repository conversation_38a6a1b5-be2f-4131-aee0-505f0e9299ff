// Contract addresses on Sepolia
const CONTRACT_ADDRESSES = {
    UBA_TOKEN: '******************************************',
    SWAP_AND_FORWARD: '******************************************',
    FEE_DISTRIBUTOR: '******************************************',
    WETH: '******************************************',
    UNISWAP_ROUTER: '******************************************'
};

// Sepolia Chain ID
const SEPOLIA_CHAIN_ID = '0xaa36a7'; // 11155111 in hex

// Contract ABIs (simplified)
const UBA_TOKEN_ABI = [
    "function balanceOf(address owner) view returns (uint256)",
    "function approve(address spender, uint256 amount) returns (bool)",
    "function allowance(address owner, address spender) view returns (uint256)",
    "function symbol() view returns (string)",
    "function decimals() view returns (uint8)"
];

const SWAP_AND_FORWARD_ABI = [
    "function swapAndForwardSingleHop(address tokenIn, uint256 amountIn, address tokenOut, uint256 amountOutMin, address recipient, uint24 fee, uint256 deadline) payable returns (uint256)",
    "function protocolFeeBps() view returns (uint256)"
];

// Global variables
let provider;
let signer;
let userAddress;
let ubaTokenContract;
let swapContract;

// DOM elements
const connectWalletBtn = document.getElementById('connectWallet');
const walletInfo = document.getElementById('walletInfo');
const walletAddress = document.getElementById('walletAddress');
const networkName = document.getElementById('networkName');
const ethBalance = document.getElementById('ethBalance');
const ubaBalance = document.getElementById('ubaBalance');
const swapButton = document.getElementById('swapButton');
const swapStatus = document.getElementById('swapStatus');
const tokenInSelect = document.getElementById('tokenIn');
const tokenOutSelect = document.getElementById('tokenOut');
const amountInInput = document.getElementById('amountIn');
const recipientInput = document.getElementById('recipient');

// Initialize the app
async function init() {
    if (typeof window.ethereum !== 'undefined') {
        console.log('MetaMask is installed!');
        
        // Check if already connected
        const accounts = await window.ethereum.request({ method: 'eth_accounts' });
        if (accounts.length > 0) {
            await connectWallet();
        }
        
        // Listen for account changes
        window.ethereum.on('accountsChanged', handleAccountsChanged);
        window.ethereum.on('chainChanged', handleChainChanged);
    } else {
        showStatus('MetaMask is not installed. Please install MetaMask to use this dApp.', 'error');
    }
}

// Connect wallet function
async function connectWallet() {
    try {
        // Request account access
        const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
        
        // Check if we're on Sepolia
        const chainId = await window.ethereum.request({ method: 'eth_chainId' });
        if (chainId !== SEPOLIA_CHAIN_ID) {
            await switchToSepolia();
        }
        
        // Initialize ethers
        provider = new ethers.providers.Web3Provider(window.ethereum);
        signer = provider.getSigner();
        userAddress = accounts[0];
        
        // Initialize contracts
        ubaTokenContract = new ethers.Contract(CONTRACT_ADDRESSES.UBA_TOKEN, UBA_TOKEN_ABI, signer);
        swapContract = new ethers.Contract(CONTRACT_ADDRESSES.SWAP_AND_FORWARD, SWAP_AND_FORWARD_ABI, signer);
        
        // Update UI
        await updateWalletInfo();
        
        connectWalletBtn.textContent = 'Wallet Connected';
        connectWalletBtn.disabled = true;
        swapButton.disabled = false;
        swapButton.textContent = 'Swap Tokens';
        
        // Set recipient to current address by default
        recipientInput.value = userAddress;
        
        walletInfo.classList.remove('hidden');
        
        showStatus('Wallet connected successfully!', 'success');
        
    } catch (error) {
        console.error('Error connecting wallet:', error);
        showStatus('Failed to connect wallet: ' + error.message, 'error');
    }
}

// Switch to Sepolia network
async function switchToSepolia() {
    try {
        await window.ethereum.request({
            method: 'wallet_switchEthereumChain',
            params: [{ chainId: SEPOLIA_CHAIN_ID }],
        });
    } catch (switchError) {
        // This error code indicates that the chain has not been added to MetaMask
        if (switchError.code === 4902) {
            try {
                await window.ethereum.request({
                    method: 'wallet_addEthereumChain',
                    params: [{
                        chainId: SEPOLIA_CHAIN_ID,
                        chainName: 'Sepolia Test Network',
                        nativeCurrency: {
                            name: 'ETH',
                            symbol: 'ETH',
                            decimals: 18,
                        },
                        rpcUrls: ['https://sepolia.infura.io/v3/'],
                        blockExplorerUrls: ['https://sepolia.etherscan.io/'],
                    }],
                });
            } catch (addError) {
                throw new Error('Failed to add Sepolia network');
            }
        } else {
            throw switchError;
        }
    }
}

// Update wallet information
async function updateWalletInfo() {
    try {
        // Get ETH balance
        const balance = await provider.getBalance(userAddress);
        const ethBalanceFormatted = ethers.utils.formatEther(balance);
        
        // Get UBA balance
        const ubaBalanceWei = await ubaTokenContract.balanceOf(userAddress);
        const ubaBalanceFormatted = ethers.utils.formatEther(ubaBalanceWei);
        
        // Get network info
        const network = await provider.getNetwork();
        
        // Update UI
        walletAddress.textContent = userAddress;
        networkName.textContent = network.name || 'Unknown';
        ethBalance.textContent = parseFloat(ethBalanceFormatted).toFixed(4);
        ubaBalance.textContent = parseFloat(ubaBalanceFormatted).toFixed(4);
        
    } catch (error) {
        console.error('Error updating wallet info:', error);
    }
}

// Perform token swap
async function performSwap() {
    try {
        const tokenIn = tokenInSelect.value;
        const tokenOut = tokenOutSelect.value;
        const amountIn = amountInInput.value;
        const recipient = recipientInput.value;
        
        // Validation
        if (!amountIn || parseFloat(amountIn) <= 0) {
            throw new Error('Please enter a valid amount');
        }
        
        if (!ethers.utils.isAddress(recipient)) {
            throw new Error('Please enter a valid recipient address');
        }
        
        if (tokenIn === tokenOut) {
            throw new Error('Input and output tokens cannot be the same');
        }
        
        showStatus('Preparing swap...', 'info');
        swapButton.disabled = true;
        swapButton.textContent = 'Swapping...';
        
        const amountInWei = ethers.utils.parseEther(amountIn);
        const deadline = Math.floor(Date.now() / 1000) + 300; // 5 minutes from now
        const fee = 3000; // 0.3% pool fee
        const amountOutMin = 0; // Accept any amount of tokens out (for demo purposes)
        
        let tx;
        
        if (tokenIn === '******************************************') {
            // ETH to Token swap
            tx = await swapContract.swapAndForwardSingleHop(
                tokenIn,
                amountInWei,
                tokenOut,
                amountOutMin,
                recipient,
                fee,
                deadline,
                { value: amountInWei }
            );
        } else {
            // Token to ETH/Token swap
            // First check allowance
            const allowance = await ubaTokenContract.allowance(userAddress, CONTRACT_ADDRESSES.SWAP_AND_FORWARD);
            
            if (allowance.lt(amountInWei)) {
                showStatus('Approving token spend...', 'info');
                const approveTx = await ubaTokenContract.approve(CONTRACT_ADDRESSES.SWAP_AND_FORWARD, amountInWei);
                await approveTx.wait();
                showStatus('Token approved, executing swap...', 'info');
            }
            
            tx = await swapContract.swapAndForwardSingleHop(
                tokenIn,
                amountInWei,
                tokenOut,
                amountOutMin,
                recipient,
                fee,
                deadline
            );
        }
        
        showStatus('Transaction submitted. Waiting for confirmation...', 'info');
        
        const receipt = await tx.wait();
        
        showStatus(`Swap successful! Transaction hash: ${receipt.transactionHash}`, 'success');
        
        // Update balances
        await updateWalletInfo();
        
        // Clear form
        amountInInput.value = '';
        
    } catch (error) {
        console.error('Swap error:', error);
        showStatus('Swap failed: ' + error.message, 'error');
    } finally {
        swapButton.disabled = false;
        swapButton.textContent = 'Swap Tokens';
    }
}

// Utility functions
function showStatus(message, type) {
    swapStatus.textContent = message;
    swapStatus.className = `status ${type}`;
    swapStatus.classList.remove('hidden');
    
    if (type === 'success') {
        setTimeout(() => {
            swapStatus.classList.add('hidden');
        }, 5000);
    }
}

function handleAccountsChanged(accounts) {
    if (accounts.length === 0) {
        // User disconnected wallet
        location.reload();
    } else {
        // User switched accounts
        connectWallet();
    }
}

function handleChainChanged(chainId) {
    // Reload the page when chain changes
    location.reload();
}

// Event listeners
connectWalletBtn.addEventListener('click', connectWallet);
swapButton.addEventListener('click', performSwap);

// Initialize the app when page loads
window.addEventListener('load', init);
