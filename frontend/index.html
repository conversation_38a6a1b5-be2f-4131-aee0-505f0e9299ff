<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UbaSwap - Decentralized Exchange</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0d1421;
            color: #ffffff;
            min-height: 100vh;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background: rgba(13, 20, 33, 0.8);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff007a;
        }

        .logo::before {
            content: "🦄";
            font-size: 2rem;
        }

        .connect-btn {
            background: linear-gradient(135deg, #ff007a, #ff6b9d);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .connect-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 0, 122, 0.3);
        }

        .main-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 80px);
            padding: 2rem;
        }

        .swap-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 1.5rem;
            width: 100%;
            max-width: 480px;
            backdrop-filter: blur(10px);
        }

        .swap-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .swap-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .settings-btn {
            background: none;
            border: none;
            color: #8b949e;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .settings-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .token-input {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s;
        }

        .token-input:hover {
            border-color: rgba(255, 255, 255, 0.2);
        }

        .token-input-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .token-label {
            font-size: 0.875rem;
            color: #8b949e;
        }

        .token-balance {
            font-size: 0.875rem;
            color: #8b949e;
            cursor: pointer;
        }

        .token-balance:hover {
            color: #ffffff;
        }

        .token-input-main {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .amount-input {
            background: none;
            border: none;
            color: #ffffff;
            font-size: 1.5rem;
            font-weight: 500;
            flex: 1;
            outline: none;
        }

        .amount-input::placeholder {
            color: #8b949e;
        }

        .token-selector {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 20px;
            padding: 0.5rem 1rem;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.2s;
        }

        .token-selector:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .token-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff007a, #ff6b9d);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .swap-arrow-container {
            display: flex;
            justify-content: center;
            margin: 0.5rem 0;
            position: relative;
        }

        .swap-arrow {
            background: rgba(255, 255, 255, 0.1);
            border: 4px solid #0d1421;
            border-radius: 12px;
            padding: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .swap-arrow:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: rotate(180deg);
        }

        .swap-btn {
            width: 100%;
            background: linear-gradient(135deg, #ff007a, #ff6b9d);
            border: none;
            border-radius: 16px;
            padding: 1rem;
            color: white;
            font-size: 1.125rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin-top: 1rem;
        }

        .swap-btn:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 0, 122, 0.3);
        }

        .swap-btn:disabled {
            background: rgba(255, 255, 255, 0.1);
            color: #8b949e;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .wallet-info {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .wallet-address {
            font-family: monospace;
            color: #ff007a;
        }

        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 12px;
            text-align: center;
            font-weight: 500;
        }

        .status.success {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .status.error {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .status.info {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .hidden {
            display: none;
        }

        .price-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.875rem;
        }

        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .price-row:last-child {
            margin-bottom: 0;
        }

        .footer-info {
            text-align: center;
            margin-top: 2rem;
            color: #8b949e;
            font-size: 0.875rem;
        }

        .footer-info a {
            color: #ff007a;
            text-decoration: none;
        }

        .footer-info a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
            }

            .main-container {
                padding: 1rem;
            }

            .swap-card {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="logo">UbaSwap</div>
        <button id="connectWallet" class="connect-btn">Connect Wallet</button>
    </nav>

    <!-- Wallet Info (Hidden by default) -->
    <div id="walletInfo" class="wallet-info hidden" style="margin: 1rem 2rem;">
        <div>Connected: <span id="walletAddress" class="wallet-address"></span></div>
        <div>Network: <span id="networkName"></span> | ETH: <span id="ethBalance"></span> | UBA: <span id="ubaBalance"></span></div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <div class="swap-card">
            <!-- Swap Header -->
            <div class="swap-header">
                <div class="swap-title">Swap</div>
                <button class="settings-btn">⚙️</button>
            </div>

            <!-- From Token Input -->
            <div class="token-input">
                <div class="token-input-header">
                    <div class="token-label">From</div>
                    <div class="token-balance" id="fromBalance" onclick="setMaxAmount()">Balance: 0</div>
                </div>
                <div class="token-input-main">
                    <input type="number" class="amount-input" id="amountIn" placeholder="0.0" step="0.001" min="0">
                    <button class="token-selector" id="tokenInSelector">
                        <div class="token-icon" id="tokenInIcon">Ξ</div>
                        <span id="tokenInSymbol">ETH</span>
                        <span>▼</span>
                    </button>
                </div>
            </div>

            <!-- Swap Arrow -->
            <div class="swap-arrow-container">
                <div class="swap-arrow" onclick="swapTokens()">⇅</div>
            </div>

            <!-- To Token Input -->
            <div class="token-input">
                <div class="token-input-header">
                    <div class="token-label">To</div>
                    <div class="token-balance" id="toBalance">Balance: 0</div>
                </div>
                <div class="token-input-main">
                    <input type="number" class="amount-input" id="amountOut" placeholder="0.0" readonly>
                    <button class="token-selector" id="tokenOutSelector">
                        <div class="token-icon" id="tokenOutIcon">🦄</div>
                        <span id="tokenOutSymbol">UBA</span>
                        <span>▼</span>
                    </button>
                </div>
            </div>

            <!-- Price Info -->
            <div class="price-info" id="priceInfo" style="display: none;">
                <div class="price-row">
                    <span>Rate</span>
                    <span id="exchangeRate">-</span>
                </div>
                <div class="price-row">
                    <span>Protocol Fee (0.5%)</span>
                    <span id="protocolFee">-</span>
                </div>
                <div class="price-row">
                    <span>Minimum Received</span>
                    <span id="minimumReceived">-</span>
                </div>
            </div>

            <!-- Swap Button -->
            <button id="swapButton" class="swap-btn" disabled>Connect Wallet</button>

            <!-- Status -->
            <div id="swapStatus" class="status hidden"></div>
        </div>
    </div>

    <!-- Footer Info -->
    <div class="footer-info">
        <p>🔗 <a href="https://sepolia.etherscan.io/address/******************************************" target="_blank">UBA Token</a> |
        <a href="https://sepolia.etherscan.io/address/******************************************" target="_blank">SwapAndForward</a> |
        <a href="https://sepoliafaucet.com/" target="_blank">Get Sepolia ETH</a></p>
        <p>⚠️ Testnet Only - Use Test Funds</p>
    </div>

    <script src="app.js"></script>
</body>
</html>
