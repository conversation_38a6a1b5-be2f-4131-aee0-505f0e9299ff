<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UbaSwap - Decentralized Exchange</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-left">
            <div class="logo">
                <span class="logo-icon">🦄</span>
                <span class="logo-text">UbaSwap</span>
            </div>
            <div class="nav-links">
                <button class="nav-link active" data-page="swap">Tukar</button>
                <button class="nav-link" data-page="liquidity">Likuiditas</button>
                <button class="nav-link" data-page="swap-forward">Tukar & Teruskan</button>
                <button class="nav-link" data-page="fees">Biaya</button>
            </div>
        </div>
        <div class="nav-right">
            <div class="network-indicator" id="networkIndicator">
                <span class="network-dot"></span>
                <span class="network-name">Sepolia</span>
            </div>
            <button id="connectWallet" class="connect-btn">Connect Wallet</button>
        </div>
    </nav>

    <!-- Wallet Info Banner (Hidden by default) -->
    <div id="walletBanner" class="wallet-banner hidden">
        <div class="wallet-info">
            <span class="wallet-address" id="walletAddress"></span>
            <span class="wallet-balance">ETH: <span id="ethBalance">0</span> | UBA: <span id="ubaBalance">0</span></span>
        </div>
        <div class="wallet-actions">
            <button class="wallet-action-btn" onclick="viewOnEtherscan()">📊 Etherscan</button>
            <button class="wallet-action-btn" onclick="disconnectWallet()">🔌 Disconnect</button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Swap Page -->
        <div id="swapPage" class="page active">
            <div class="page-header">
                <h1>Tukar Token</h1>
                <p>Tukar token dengan mudah menggunakan likuiditas Uniswap V3</p>
            </div>

            <div class="swap-card">
                <div class="swap-header">
                    <div class="swap-title">Tukar</div>
                    <button class="settings-btn" onclick="openSettings()">⚙️</button>
                </div>

                <!-- From Token Input -->
                <div class="token-input">
                    <div class="token-input-header">
                        <div class="token-label">Anda membayar</div>
                        <div class="token-balance" id="fromBalance" onclick="setMaxAmount()">Balance: 0</div>
                    </div>
                    <div class="token-input-main">
                        <input type="number" class="amount-input" id="amountIn" placeholder="0.0" step="0.001" min="0">
                        <button class="token-selector" id="tokenInSelector" onclick="openTokenSelector('in')">
                            <div class="token-icon" id="tokenInIcon">Ξ</div>
                            <span id="tokenInSymbol">ETH</span>
                            <span class="dropdown-arrow">▼</span>
                        </button>
                    </div>
                    <button class="max-btn" onclick="setMaxAmount()">MAX</button>
                </div>

                <!-- Swap Arrow -->
                <div class="swap-arrow-container">
                    <div class="swap-arrow" onclick="swapTokens()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2"/>
                            <path d="M7 14L12 9L17 14" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                </div>

                <!-- To Token Input -->
                <div class="token-input">
                    <div class="token-input-header">
                        <div class="token-label">Anda menerima</div>
                        <div class="token-balance" id="toBalance">Balance: 0</div>
                    </div>
                    <div class="token-input-main">
                        <input type="number" class="amount-input" id="amountOut" placeholder="0.0" readonly>
                        <button class="token-selector" id="tokenOutSelector" onclick="openTokenSelector('out')">
                            <div class="token-icon" id="tokenOutIcon">🦄</div>
                            <span id="tokenOutSymbol">UBA</span>
                            <span class="dropdown-arrow">▼</span>
                        </button>
                    </div>
                </div>

                <!-- Price Info -->
                <div class="price-info" id="priceInfo" style="display: none;">
                    <div class="price-row">
                        <span>Kurs</span>
                        <span id="exchangeRate">-</span>
                    </div>
                    <div class="price-row">
                        <span>Biaya Protokol (0.5%)</span>
                        <span id="protocolFee">-</span>
                    </div>
                    <div class="price-row">
                        <span>Minimum Diterima</span>
                        <span id="minimumReceived">-</span>
                    </div>
                    <div class="price-row">
                        <span>Dampak Harga</span>
                        <span id="priceImpact" class="price-impact-low">< 0.01%</span>
                    </div>
                </div>

                <!-- Swap Button -->
                <button id="swapButton" class="swap-btn" disabled>Connect Wallet</button>

                <!-- Status -->
                <div id="swapStatus" class="status hidden"></div>
            </div>
        </div>

        <!-- Liquidity Page -->
        <div id="liquidityPage" class="page">
            <div class="page-header">
                <h1>Likuiditas</h1>
                <p>Sediakan likuiditas dan dapatkan fee dari setiap transaksi</p>
            </div>

            <div class="liquidity-tabs">
                <button class="tab-btn active" data-tab="add">Tambah Likuiditas</button>
                <button class="tab-btn" data-tab="remove">Hapus Likuiditas</button>
                <button class="tab-btn" data-tab="positions">Posisi Saya</button>
            </div>

            <!-- Add Liquidity Tab -->
            <div id="addLiquidityTab" class="tab-content active">
                <div class="liquidity-card">
                    <h3>Tambah Likuiditas ke Pool</h3>
                    <div class="pair-selector">
                        <div class="token-pair">
                            <button class="pair-token" onclick="selectPairToken(0)">
                                <div class="token-icon">Ξ</div>
                                <span>ETH</span>
                            </button>
                            <span class="pair-separator">+</span>
                            <button class="pair-token" onclick="selectPairToken(1)">
                                <div class="token-icon">🦄</div>
                                <span>UBA</span>
                            </button>
                        </div>
                        <div class="fee-tier">
                            <label>Fee Tier:</label>
                            <select id="feeTier">
                                <option value="500">0.05%</option>
                                <option value="3000" selected>0.3%</option>
                                <option value="10000">1%</option>
                            </select>
                        </div>
                    </div>

                    <div class="liquidity-amounts">
                        <div class="amount-input-group">
                            <label>Jumlah ETH</label>
                            <input type="number" id="liquidityAmount0" placeholder="0.0">
                            <button class="max-btn" onclick="setMaxLiquidity(0)">MAX</button>
                        </div>
                        <div class="amount-input-group">
                            <label>Jumlah UBA</label>
                            <input type="number" id="liquidityAmount1" placeholder="0.0">
                            <button class="max-btn" onclick="setMaxLiquidity(1)">MAX</button>
                        </div>
                    </div>

                    <button id="addLiquidityBtn" class="action-btn" disabled>Tambah Likuiditas</button>
                </div>
            </div>

            <!-- Remove Liquidity Tab -->
            <div id="removeLiquidityTab" class="tab-content">
                <div class="liquidity-card">
                    <h3>Hapus Likuiditas</h3>
                    <div id="positionsList" class="positions-list">
                        <div class="no-positions">
                            <p>Anda belum memiliki posisi likuiditas</p>
                            <button onclick="switchTab('add')">Tambah Likuiditas</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- My Positions Tab -->
            <div id="positionsTab" class="tab-content">
                <div class="liquidity-card">
                    <h3>Posisi Likuiditas Saya</h3>
                    <div id="myPositions" class="positions-grid">
                        <!-- Positions will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Swap & Forward Page -->
        <div id="swapForwardPage" class="page">
            <div class="page-header">
                <h1>Tukar & Teruskan</h1>
                <p>Tukar token dan kirim langsung ke alamat lain dalam satu transaksi</p>
            </div>

            <div class="swap-forward-card">
                <div class="feature-highlight">
                    <div class="highlight-icon">⚡</div>
                    <div class="highlight-text">
                        <h3>Transaksi Atomik</h3>
                        <p>Tukar dan kirim dalam satu langkah - hemat gas dan waktu</p>
                    </div>
                </div>

                <!-- Same swap interface as main swap -->
                <div class="token-input">
                    <div class="token-input-header">
                        <div class="token-label">Anda membayar</div>
                        <div class="token-balance" id="sfFromBalance">Balance: 0</div>
                    </div>
                    <div class="token-input-main">
                        <input type="number" class="amount-input" id="sfAmountIn" placeholder="0.0">
                        <button class="token-selector" id="sfTokenInSelector">
                            <div class="token-icon">Ξ</div>
                            <span>ETH</span>
                            <span class="dropdown-arrow">▼</span>
                        </button>
                    </div>
                    <button class="max-btn" onclick="setSFMaxAmount()">MAX</button>
                </div>

                <div class="swap-arrow-container">
                    <div class="swap-arrow" onclick="swapSFTokens()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2"/>
                            <path d="M7 14L12 9L17 14" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                </div>

                <div class="token-input">
                    <div class="token-input-header">
                        <div class="token-label">Penerima akan mendapat</div>
                        <div class="token-balance" id="sfToBalance">Balance: 0</div>
                    </div>
                    <div class="token-input-main">
                        <input type="number" class="amount-input" id="sfAmountOut" placeholder="0.0" readonly>
                        <button class="token-selector" id="sfTokenOutSelector">
                            <div class="token-icon">🦄</div>
                            <span>UBA</span>
                            <span class="dropdown-arrow">▼</span>
                        </button>
                    </div>
                </div>

                <!-- Recipient Address -->
                <div class="recipient-section">
                    <div class="recipient-toggle">
                        <input type="checkbox" id="customRecipient" onchange="toggleCustomRecipient()">
                        <label for="customRecipient">Kirim ke alamat lain</label>
                    </div>
                    <div class="recipient-input hidden" id="recipientInput">
                        <label>Alamat Penerima</label>
                        <input type="text" id="recipientAddress" placeholder="0x...">
                        <div class="recipient-presets">
                            <button onclick="setRecipient('self')">Dompet Saya</button>
                            <button onclick="setRecipient('saved')">Alamat Tersimpan</button>
                        </div>
                    </div>
                </div>

                <button id="swapForwardBtn" class="action-btn" disabled>Lakukan Tukar & Teruskan</button>
                <div id="sfStatus" class="status hidden"></div>
            </div>
        </div>

        <!-- Fees Page -->
        <div id="feesPage" class="page">
            <div class="page-header">
                <h1>Biaya Protokol</h1>
                <p>Lihat statistik fee dan kelola distribusi UBA token</p>
            </div>

            <div class="fees-grid">
                <!-- Fee Statistics -->
                <div class="fee-stats-card">
                    <h3>Statistik Fee</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="totalFeesCollected">0 ETH</div>
                            <div class="stat-label">Total Fee Terkumpul</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="totalUBABurned">0 UBA</div>
                            <div class="stat-label">Total UBA Dibakar</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="totalTransactions">0</div>
                            <div class="stat-label">Total Transaksi</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="protocolFeeRate">0.5%</div>
                            <div class="stat-label">Fee Rate</div>
                        </div>
                    </div>
                </div>

                <!-- Claimable Fees -->
                <div class="claimable-fees-card">
                    <h3>Biaya yang Dapat Saya Klaim</h3>
                    <div class="claimable-amount">
                        <div class="amount-display">
                            <span class="amount" id="claimableAmount">0</span>
                            <span class="token">UBA</span>
                        </div>
                        <div class="amount-usd" id="claimableUSD">~$0.00</div>
                    </div>
                    <button id="claimFeesBtn" class="claim-btn" disabled>Klaim Semua Biaya Saya</button>
                    <div class="claim-info">
                        <p>Fee diklaim dalam bentuk UBA token berdasarkan kontribusi likuiditas Anda</p>
                    </div>
                </div>

                <!-- Admin Panel (Only visible to contract owner) -->
                <div id="adminPanel" class="admin-panel hidden">
                    <h3>Panel Admin Fee Distributor</h3>
                    <div class="admin-actions">
                        <div class="admin-action">
                            <label>Proses Fee Terkumpul</label>
                            <div class="action-row">
                                <select id="feeTokenSelect">
                                    <option value="eth">ETH</option>
                                    <option value="uba">UBA</option>
                                </select>
                                <button id="processFeesBtn" class="admin-btn">Proses Biaya</button>
                            </div>
                        </div>
                        <div class="admin-action">
                            <label>Update Minimum Process Amount</label>
                            <div class="action-row">
                                <input type="number" id="newMinAmount" placeholder="1.0">
                                <button id="updateMinBtn" class="admin-btn">Perbarui</button>
                            </div>
                        </div>
                        <div class="admin-action">
                            <label>Update Protocol Fee</label>
                            <div class="action-row">
                                <input type="number" id="newFeeRate" placeholder="50" max="1000">
                                <span class="input-suffix">BPS</span>
                                <button id="updateFeeBtn" class="admin-btn">Perbarui</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Token Selector Modal -->
    <div id="tokenModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Pilih Token</h3>
                <button class="close-btn" onclick="closeTokenModal()">&times;</button>
            </div>
            <div class="token-search">
                <input type="text" id="tokenSearch" placeholder="Cari nama token atau alamat">
            </div>
            <div class="token-list" id="tokenList">
                <!-- Token list will be populated here -->
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Pengaturan Transaksi</h3>
                <button class="close-btn" onclick="closeSettings()">&times;</button>
            </div>
            <div class="settings-content">
                <div class="setting-group">
                    <label>Slippage Tolerance</label>
                    <div class="slippage-options">
                        <button class="slippage-btn" data-slippage="0.1">0.1%</button>
                        <button class="slippage-btn active" data-slippage="0.5">0.5%</button>
                        <button class="slippage-btn" data-slippage="1.0">1.0%</button>
                        <input type="number" id="customSlippage" placeholder="Custom" step="0.1" min="0" max="50">
                    </div>
                </div>
                <div class="setting-group">
                    <label>Transaction Deadline</label>
                    <div class="deadline-input">
                        <input type="number" id="deadline" value="20" min="1" max="4320">
                        <span>minutes</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-spinner"></div>
        <div class="loading-text">Processing transaction...</div>
    </div>

    <script src="config.js"></script>
    <script src="contracts.js"></script>
    <script src="ui.js"></script>
    <script src="swap.js"></script>
    <script src="liquidity.js"></script>
    <script src="fees.js"></script>
    <script src="app.js"></script>
</body>
</html>
