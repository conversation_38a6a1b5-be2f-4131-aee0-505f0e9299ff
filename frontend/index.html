<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UbaSwap DEX</title>
    <script src="https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .wallet-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .swap-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .contract-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
        }

        .contract-info h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .contract-info p {
            margin-bottom: 5px;
            word-break: break-all;
        }

        .hidden {
            display: none;
        }

        .token-balance {
            background: #e9ecef;
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            font-size: 14px;
        }

        .swap-arrow {
            text-align: center;
            margin: 15px 0;
            font-size: 24px;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 UbaSwap DEX</h1>
            <p>Decentralized Exchange on Sepolia Testnet</p>
        </div>

        <div class="wallet-section">
            <h2>Wallet Connection</h2>
            <button id="connectWallet" class="btn">Connect MetaMask Wallet</button>
            <div id="walletInfo" class="hidden">
                <p><strong>Connected:</strong> <span id="walletAddress"></span></p>
                <p><strong>Network:</strong> <span id="networkName"></span></p>
                <p><strong>ETH Balance:</strong> <span id="ethBalance"></span> ETH</p>
                <div class="token-balance">
                    <strong>UBA Token Balance:</strong> <span id="ubaBalance"></span> UBA
                </div>
            </div>
        </div>

        <div class="swap-container">
            <h2>Token Swap</h2>
            
            <div class="input-group">
                <label for="tokenIn">From Token:</label>
                <select id="tokenIn">
                    <option value="******************************************">ETH</option>
                    <option value="******************************************">UBA Token</option>
                </select>
            </div>

            <div class="input-group">
                <label for="amountIn">Amount:</label>
                <input type="number" id="amountIn" placeholder="0.0" step="0.001" min="0">
            </div>

            <div class="swap-arrow">⬇️</div>

            <div class="input-group">
                <label for="tokenOut">To Token:</label>
                <select id="tokenOut">
                    <option value="******************************************">UBA Token</option>
                    <option value="******************************************">ETH</option>
                </select>
            </div>

            <div class="input-group">
                <label for="recipient">Recipient Address:</label>
                <input type="text" id="recipient" placeholder="0x..." value="">
            </div>

            <button id="swapButton" class="btn" disabled>Connect Wallet First</button>

            <div id="swapStatus" class="status hidden"></div>

            <div class="contract-info">
                <h3>📋 Contract Information</h3>
                <p><strong>UBA Token:</strong> ******************************************</p>
                <p><strong>SwapAndForward:</strong> ******************************************</p>
                <p><strong>FeeDistributor:</strong> ******************************************</p>
                <p><strong>Network:</strong> Sepolia Testnet</p>
                <p><strong>Protocol Fee:</strong> 0.5%</p>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
