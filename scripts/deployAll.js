const { ethers, run, network } = require("hardhat");
require("dotenv").config();

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("Deploying all contracts with account:", deployer.address);
  console.log("Account balance:", (await deployer.getBalance()).toString());

  // Get required addresses from environment
  const uniswapRouterAddress = process.env.UNISWAP_ROUTER_ADDRESS;
  const uniswapQuoterAddress = process.env.UNISWAP_QUOTER_ADDRESS;
  const wethAddress = process.env.WETH_ADDRESS;
  const minimumProcessAmount = process.env.MINIMUM_PROCESS_AMOUNT;
  const protocolFeeBps = process.env.PROTOCOL_FEE_BPS || "50"; // 0.5% default

  if (!uniswapRouterAddress || !uniswapQuoterAddress || !wethAddress || !minimumProcessAmount) {
    console.error("Missing required environment variables:");
    console.error("UNISWAP_ROUTER_ADDRESS, UNISWAP_QUOTER_ADDRESS, WETH_ADDRESS, MINIMUM_PROCESS_AMOUNT");
    process.exit(1);
  }

  console.log("Deployment parameters:");
  console.log(`  Uniswap Router: ${uniswapRouterAddress}`);
  console.log(`  Uniswap Quoter: ${uniswapQuoterAddress}`);
  console.log(`  WETH: ${wethAddress}`);
  console.log(`  Minimum Process Amount: ${minimumProcessAmount}`);
  console.log(`  Protocol Fee BPS: ${protocolFeeBps}`);
  console.log(`  Owner: ${deployer.address}`);

  // Step 1: Deploy UBAToken
  console.log("\n=== Step 1: Deploying UBAToken ===");
  const UBAToken = await ethers.getContractFactory("UBAToken");
  const ubaToken = await UBAToken.deploy(deployer.address);
  await ubaToken.waitForDeployment();
  console.log(`UBAToken deployed to: ${ubaToken.target}`);

  // Step 2: Deploy FeeDistributor
  console.log("\n=== Step 2: Deploying FeeDistributor ===");
  const FeeDistributor = await ethers.getContractFactory("FeeDistributor");
  const feeDistributor = await FeeDistributor.deploy(
    ubaToken.target,
    uniswapRouterAddress,
    wethAddress,
    minimumProcessAmount,
    deployer.address
  );
  await feeDistributor.waitForDeployment();
  console.log(`FeeDistributor deployed to: ${feeDistributor.target}`);

  // Step 3: Set FeeDistributor address in UBAToken
  console.log("\n=== Step 3: Setting FeeDistributor address in UBAToken ===");
  const setFeeDistributorTx = await ubaToken.setFeeDistributorAddress(feeDistributor.target);
  await setFeeDistributorTx.wait();
  console.log("FeeDistributor address set in UBAToken");

  // Step 4: Deploy SwapAndForward
  console.log("\n=== Step 4: Deploying SwapAndForward ===");
  const SwapAndForward = await ethers.getContractFactory("SwapAndForward");
  const swapAndForward = await SwapAndForward.deploy(
    uniswapRouterAddress,
    uniswapQuoterAddress,
    wethAddress,
    feeDistributor.target,
    protocolFeeBps,
    deployer.address
  );
  await swapAndForward.waitForDeployment();
  console.log(`SwapAndForward deployed to: ${swapAndForward.target}`);

  // Verify contracts on Etherscan if on Sepolia
  if (network.config.chainId === 11155111 && process.env.ETHERSCAN_API_KEY) {
    console.log("\n=== Verifying contracts on Etherscan ===");
    console.log("Waiting for block confirmations...");
    
    // Wait for confirmations
    await new Promise(resolve => setTimeout(resolve, 30000)); // Wait 30 seconds
    
    // Verify UBAToken
    await verify(ubaToken.target, [deployer.address], "UBAToken");
    
    // Verify FeeDistributor
    await verify(feeDistributor.target, [
      ubaToken.target,
      uniswapRouterAddress,
      wethAddress,
      minimumProcessAmount,
      deployer.address
    ], "FeeDistributor");
    
    // Verify SwapAndForward
    await verify(swapAndForward.target, [
      uniswapRouterAddress,
      uniswapQuoterAddress,
      wethAddress,
      feeDistributor.target,
      protocolFeeBps,
      deployer.address
    ], "SwapAndForward");
  }

  // Final deployment summary
  console.log("\n=== DEPLOYMENT COMPLETE ===");
  console.log(`Network: ${network.name} (Chain ID: ${network.config.chainId})`);
  console.log(`Deployer: ${deployer.address}`);
  console.log(`UBAToken: ${ubaToken.target}`);
  console.log(`FeeDistributor: ${feeDistributor.target}`);
  console.log(`SwapAndForward: ${swapAndForward.target}`);
  
  console.log("\n=== Environment Variables for Frontend ===");
  console.log(`REACT_APP_UBA_TOKEN_ADDRESS=${ubaToken.target}`);
  console.log(`REACT_APP_FEE_DISTRIBUTOR_ADDRESS=${feeDistributor.target}`);
  console.log(`REACT_APP_SWAP_AND_FORWARD_ADDRESS=${swapAndForward.target}`);
  console.log(`REACT_APP_WETH_ADDRESS=${wethAddress}`);
  console.log(`REACT_APP_UNISWAP_ROUTER_ADDRESS=${uniswapRouterAddress}`);
  console.log(`REACT_APP_UNISWAP_QUOTER_ADDRESS=${uniswapQuoterAddress}`);
}

async function verify(contractAddress, args, contractName) {
  console.log(`Verifying ${contractName}...`);
  try {
    await run("verify:verify", {
      address: contractAddress,
      constructorArguments: args,
    });
    console.log(`${contractName} verified successfully!`);
  } catch (e) {
    if (e.message.toLowerCase().includes("already verified")) {
      console.log(`${contractName} already verified!`);
    } else {
      console.error(`Error verifying ${contractName}:`, e.message);
    }
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
