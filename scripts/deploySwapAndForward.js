const { ethers, run, network } = require("hardhat");
require("dotenv").config();

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("Deploying SwapAndForward with account:", deployer.address);
  console.log("Account balance:", (await deployer.getBalance()).toString());

  // Get required addresses from environment
  const uniswapRouterAddress = process.env.UNISWAP_ROUTER_ADDRESS;
  const uniswapQuoterAddress = process.env.UNISWAP_QUOTER_ADDRESS;
  const wethAddress = process.env.WETH_ADDRESS;
  const feeDistributorAddress = process.env.FEE_DISTRIBUTOR_ADDRESS;
  const protocolFeeBps = process.env.PROTOCOL_FEE_BPS || "50"; // 0.5% default

  if (!uniswapRouterAddress || !uniswapQuoterAddress || !wethAddress || !feeDistributorAddress) {
    console.error("Missing required environment variables:");
    console.error("UNISWAP_ROUTER_ADDRESS, UNISWAP_QUOTER_ADDRESS, WETH_ADDRESS, FEE_DISTRIBUTOR_ADDRESS");
    process.exit(1);
  }

  console.log("Deployment parameters:");
  console.log(`  Uniswap Router: ${uniswapRouterAddress}`);
  console.log(`  Uniswap Quoter: ${uniswapQuoterAddress}`);
  console.log(`  WETH: ${wethAddress}`);
  console.log(`  Fee Distributor: ${feeDistributorAddress}`);
  console.log(`  Protocol Fee BPS: ${protocolFeeBps}`);
  console.log(`  Owner: ${deployer.address}`);

  const SwapAndForward = await ethers.getContractFactory("SwapAndForward");
  const swapAndForward = await SwapAndForward.deploy(
    uniswapRouterAddress,
    uniswapQuoterAddress,
    wethAddress,
    feeDistributorAddress,
    protocolFeeBps,
    deployer.address
  );

  await swapAndForward.waitForDeployment();

  console.log(`SwapAndForward deployed to: ${swapAndForward.target}`);

  // Verify contract on Etherscan if on Sepolia
  if (network.config.chainId === 11155111 && process.env.ETHERSCAN_API_KEY) {
    console.log("Waiting for block confirmations...");
    await swapAndForward.deploymentTransaction().wait(6);
    await verify(swapAndForward.target, [
      uniswapRouterAddress,
      uniswapQuoterAddress,
      wethAddress,
      feeDistributorAddress,
      protocolFeeBps,
      deployer.address
    ]);
  }

  // Save deployment info
  console.log("\n=== Deployment Summary ===");
  console.log(`SwapAndForward Address: ${swapAndForward.target}`);
  console.log(`Owner: ${deployer.address}`);
  console.log(`Network: ${network.name}`);
  console.log(`Chain ID: ${network.config.chainId}`);
}

async function verify(contractAddress, args) {
  console.log("Verifying contract...");
  try {
    await run("verify:verify", {
      address: contractAddress,
      constructorArguments: args,
    });
    console.log("Contract verified successfully!");
  } catch (e) {
    if (e.message.toLowerCase().includes("already verified")) {
      console.log("Already verified!");
    } else {
      console.error(e);
    }
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
