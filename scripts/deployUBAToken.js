const { ethers, run, network } = require("hardhat");
require("dotenv").config();

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("Deploying UBAToken with account:", deployer.address);
  console.log("Account balance:", (await deployer.getBalance()).toString());

  const UBAToken = await ethers.getContractFactory("UBAToken");
  const ubaToken = await UBAToken.deploy(deployer.address);

  await ubaToken.waitForDeployment();

  console.log(`UBAToken deployed to: ${ubaToken.target}`);

  // Verify contract on Etherscan if on Sepolia
  if (network.config.chainId === ******** && process.env.ETHERSCAN_API_KEY) {
    console.log("Waiting for block confirmations...");
    await ubaToken.deploymentTransaction().wait(6);
    await verify(ubaToken.target, [deployer.address]);
  }

  // Save deployment info
  console.log("\n=== Deployment Summary ===");
  console.log(`UBAToken Address: ${ubaToken.target}`);
  console.log(`Owner: ${deployer.address}`);
  console.log(`Network: ${network.name}`);
  console.log(`Chain ID: ${network.config.chainId}`);
}

async function verify(contractAddress, args) {
  console.log("Verifying contract...");
  try {
    await run("verify:verify", {
      address: contractAddress,
      constructorArguments: args,
    });
    console.log("Contract verified successfully!");
  } catch (e) {
    if (e.message.toLowerCase().includes("already verified")) {
      console.log("Already verified!");
    } else {
      console.error(e);
    }
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
