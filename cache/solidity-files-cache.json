{"_format": "hh-sol-cache-2", "files": {"/home/<USER>/codingan/dex-smartcontract/contracts/FeeDistributor.sol": {"lastModificationDate": 1751898658312, "contentHash": "15cc4dac9f091fc3a36f3eb60ba052ef", "sourceName": "contracts/FeeDistributor.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "./interfaces/IUniswapV3SwapRouter.sol", "./interfaces/IWETH.sol", "./UBAToken.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["FeeDistributor"]}, "/home/<USER>/codingan/dex-smartcontract/contracts/interfaces/IUniswapV3SwapRouter.sol": {"lastModificationDate": 1751898392311, "contentHash": "d75bc4152fd86c59a8d21e74f2a258aa", "sourceName": "contracts/interfaces/IUniswapV3SwapRouter.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IUniswapV3SwapRouter"]}, "/home/<USER>/codingan/dex-smartcontract/contracts/interfaces/IWETH.sol": {"lastModificationDate": 1751898404583, "contentHash": "7eb7d7a57903d3e9c759cc7ffa6d400b", "sourceName": "contracts/interfaces/IWETH.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IWETH"]}, "/home/<USER>/codingan/dex-smartcontract/contracts/UBAToken.sol": {"lastModificationDate": 1751898696635, "contentHash": "c8726036e19e552a1eb24f7d87e5c2fa", "sourceName": "contracts/UBAToken.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Votes.sol", "@openzeppelin/contracts/utils/cryptography/EIP712.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["UBAToken"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"lastModificationDate": 1751897558709, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1751897558691, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1751897558670, "contentHash": "8f19f64d2adadf448840908bbaf431c8", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"lastModificationDate": 1751897558717, "contentHash": "1fb9edee1d763745a62d0b1954998792", "sourceName": "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol", "../../../interfaces/IERC1363.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SafeERC20"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1751897558623, "contentHash": "57d79df281f57bbb1b09214c7914f877", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/cryptography/EIP712.sol": {"lastModificationDate": 1751897558606, "contentHash": "b21731956bbef780b4cc856e8dda7426", "sourceName": "@openzeppelin/contracts/utils/cryptography/EIP712.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./MessageHashUtils.sol", "../ShortStrings.sol", "../../interfaces/IERC5267.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["EIP712"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"lastModificationDate": 1751897558623, "contentHash": "273d8d24b06f67207dd5f35c3a0c1086", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Burnable"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/token/ERC20/extensions/ERC20Votes.sol": {"lastModificationDate": 1751897558624, "contentHash": "51c2083b160453420aaa0a046c16d5ca", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Votes.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../governance/utils/Votes.sol", "../../../utils/structs/Checkpoints.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Votes"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1751897558565, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"lastModificationDate": 1751897558595, "contentHash": "267d92fe4de67b1bdb3302c08f387dbf", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1751897558671, "contentHash": "794db3115001aa372c79326fcfd44b1f", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20Metadata"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/ShortStrings.sol": {"lastModificationDate": 1751897558718, "contentHash": "c1be9487e5a64acf23b5d8028482e748", "sourceName": "@openzeppelin/contracts/utils/ShortStrings.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./StorageSlot.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ShortStrings"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/interfaces/IERC5267.sol": {"lastModificationDate": 1751897558677, "contentHash": "94364524cb1a39dcbc3d3afff6d8e53e", "sourceName": "@openzeppelin/contracts/interfaces/IERC5267.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC5267"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol": {"lastModificationDate": 1751897558686, "contentHash": "260f3968eefa3bbd30520cff5384cd93", "sourceName": "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Strings.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["MessageHashUtils"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/StorageSlot.sol": {"lastModificationDate": 1751897558728, "contentHash": "e656d64c4ce918f3d13030b91c935134", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["StorageSlot"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/Strings.sol": {"lastModificationDate": 1751897558728, "contentHash": "********************************", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./math/Math.sol", "./math/SafeCast.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Strings"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/math/Math.sol": {"lastModificationDate": 1751897558685, "contentHash": "5ec781e33d3a9ac91ffdc83d94420412", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Panic.sol", "./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Math"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/math/SafeCast.sol": {"lastModificationDate": 1751897558716, "contentHash": "2adca1150f58fc6f3d1f0a0f22ee7cca", "sourceName": "@openzeppelin/contracts/utils/math/SafeCast.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["SafeCast"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol": {"lastModificationDate": 1751897558719, "contentHash": "ae3528afb8bdb0a7dcfba5b115ee8074", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SignedMath"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/Panic.sol": {"lastModificationDate": 1751897558694, "contentHash": "2133dc13536b4a6a98131e431fac59e1", "sourceName": "@openzeppelin/contracts/utils/Panic.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Panic"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/interfaces/IERC1363.sol": {"lastModificationDate": 1751897558663, "contentHash": "6b7c5ee7a59c981072a804c99ab0fac9", "sourceName": "@openzeppelin/contracts/interfaces/IERC1363.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1363"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/interfaces/IERC20.sol": {"lastModificationDate": 1751897558667, "contentHash": "79af12d64eacc7d77b9ee2ac4b4d51ee", "sourceName": "@openzeppelin/contracts/interfaces/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../token/ERC20/IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": []}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/interfaces/IERC165.sol": {"lastModificationDate": 1751897558665, "contentHash": "f808b485ee0cdc6768ee8385ae5f9a2a", "sourceName": "@openzeppelin/contracts/interfaces/IERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.20"], "artifacts": []}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"lastModificationDate": 1751897558665, "contentHash": "bf0119eb2a570f219729ff38b6cd1df8", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC165"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/structs/Checkpoints.sol": {"lastModificationDate": 1751897558556, "contentHash": "7ca7c8421d39c1e99338f38f7952d1b8", "sourceName": "@openzeppelin/contracts/utils/structs/Checkpoints.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../math/Math.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Checkpoints"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/governance/utils/Votes.sol": {"lastModificationDate": 1751897558736, "contentHash": "95aceafdc639babdd22576e5e3774d64", "sourceName": "@openzeppelin/contracts/governance/utils/Votes.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../interfaces/IERC5805.sol", "../../utils/Context.sol", "../../utils/Nonces.sol", "../../utils/cryptography/EIP712.sol", "../../utils/structs/Checkpoints.sol", "../../utils/math/SafeCast.sol", "../../utils/cryptography/ECDSA.sol", "../../utils/types/Time.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Votes"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/Nonces.sol": {"lastModificationDate": 1751897558688, "contentHash": "c32d108058718efb9061b88e83a83f79", "sourceName": "@openzeppelin/contracts/utils/Nonces.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Nonces"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/interfaces/IERC5805.sol": {"lastModificationDate": 1751897558678, "contentHash": "65ba9f89b1057e2192e341b286d4e261", "sourceName": "@openzeppelin/contracts/interfaces/IERC5805.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../governance/utils/IVotes.sol", "./IERC6372.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC5805"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/cryptography/ECDSA.sol": {"lastModificationDate": 1751897558603, "contentHash": "81de029d56aa803972be03c5d277cb6c", "sourceName": "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["ECDSA"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/types/Time.sol": {"lastModificationDate": 1751897558731, "contentHash": "d83e7814a059fc1287fd765f424ce004", "sourceName": "@openzeppelin/contracts/utils/types/Time.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../math/Math.sol", "../math/SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Time"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/governance/utils/IVotes.sol": {"lastModificationDate": 1751897558685, "contentHash": "905ffceb29869fee4b5a649abe7e2927", "sourceName": "@openzeppelin/contracts/governance/utils/IVotes.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IVotes"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/interfaces/IERC6372.sol": {"lastModificationDate": 1751897558678, "contentHash": "414cd6acf090e4009cf016ff62ecbd88", "sourceName": "@openzeppelin/contracts/interfaces/IERC6372.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC6372"]}, "/home/<USER>/codingan/dex-smartcontract/contracts/SwapAndForward.sol": {"lastModificationDate": 1751898729435, "contentHash": "7e5e5512895701f4e723e711da816244", "sourceName": "contracts/SwapAndForward.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Address.sol", "./interfaces/IUniswapV3SwapRouter.sol", "./interfaces/IUniswapV3QuoterV2.sol", "./interfaces/IWETH.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SwapAndForward"]}, "/home/<USER>/codingan/dex-smartcontract/contracts/interfaces/IUniswapV3QuoterV2.sol": {"lastModificationDate": 1751898379645, "contentHash": "00bcfb8b9bd61287342e106662a96fc9", "sourceName": "contracts/interfaces/IUniswapV3QuoterV2.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IUniswapV3QuoterV2"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/Address.sol": {"lastModificationDate": 1751897558541, "contentHash": "5b9a49c25d7edbc48ffcbd2c7e8a40ef", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./Errors.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Address"]}, "/home/<USER>/codingan/dex-smartcontract/node_modules/@openzeppelin/contracts/utils/Errors.sol": {"lastModificationDate": 1751897558642, "contentHash": "6b5eac2b85500c3012977849cfd633d8", "sourceName": "@openzeppelin/contracts/utils/Errors.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Errors"]}}}