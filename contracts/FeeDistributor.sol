// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./interfaces/IUniswapV3SwapRouter.sol";
import "./interfaces/IWETH.sol";
import "./UBAToken.sol";

/**
 * @title FeeDistributor
 * @dev Kontrak untuk mengumpulkan dan mendistribusikan fee protokol.
 * @notice Mengkonversi fee yang dikumpulkan menjadi UBA token dan membakarnya.
 */
contract FeeDistributor is Ownable, ReentrancyGuard {
    using SafeERC20 for IERC20;

    /// @notice Token UBA untuk pembakaran
    UBAToken public immutable ubaToken;

    /// @notice Router Uniswap V3 untuk swap
    IUniswapV3SwapRouter public immutable uniswapRouter;

    /// @notice Token WETH
    IWETH public immutable WETH;

    /// @notice Jumlah minimum untuk memproses fee
    uint256 public minimumProcessAmount;

    /// @notice Mapping untuk melacak fee yang dikumpulkan per token
    mapping(address => uint256) public collectedFees;

    /// @notice Event yang dipancarkan saat fee diproses
    event FeesProcessed(address indexed token, uint256 amount, uint256 ubaAmount);

    /// @notice Event yang dipancarkan saat jumlah minimum diperbarui
    event MinimumProcessAmountUpdated(uint256 oldAmount, uint256 newAmount);

    /// @notice Event yang dipancarkan saat fee dikumpulkan
    event FeesCollected(address indexed token, uint256 amount);

    /**
     * @dev Konstruktor.
     * @param _ubaToken Alamat token UBA.
     * @param _uniswapRouter Alamat router Uniswap V3.
     * @param _weth Alamat token WETH.
     * @param _minimumProcessAmount Jumlah minimum untuk memproses fee.
     * @param _owner Alamat pemilik kontrak.
     */
    constructor(
        address _ubaToken,
        address _uniswapRouter,
        address _weth,
        uint256 _minimumProcessAmount,
        address _owner
    ) Ownable(_owner) {
        require(_ubaToken != address(0), "FeeDistributor: alamat UBA token tidak valid");
        require(_uniswapRouter != address(0), "FeeDistributor: alamat router tidak valid");
        require(_weth != address(0), "FeeDistributor: alamat WETH tidak valid");

        ubaToken = UBAToken(_ubaToken);
        uniswapRouter = IUniswapV3SwapRouter(_uniswapRouter);
        WETH = IWETH(_weth);
        minimumProcessAmount = _minimumProcessAmount;
    }

    /**
     * @notice Menerima fee dari kontrak SwapAndForward.
     * @param token Alamat token fee.
     * @param amount Jumlah fee.
     */
    function collectFees(address token, uint256 amount) external {
        require(amount > 0, "FeeDistributor: jumlah harus lebih besar dari 0");

        IERC20(token).safeTransferFrom(msg.sender, address(this), amount);
        collectedFees[token] += amount;

        emit FeesCollected(token, amount);
    }

    /**
     * @notice Memproses fee yang dikumpulkan untuk token tertentu.
     * @param token Alamat token yang akan diproses.
     * @param fee Fee pool Uniswap V3 untuk swap.
     */
    function processFees(address token, uint24 fee) external nonReentrant {
        uint256 amount = collectedFees[token];
        require(amount >= minimumProcessAmount, "FeeDistributor: jumlah di bawah minimum");

        collectedFees[token] = 0;

        if (token == address(ubaToken)) {
            // Jika fee sudah dalam UBA, langsung bakar
            ubaToken.burnForFees(amount);
            emit FeesProcessed(token, amount, amount);
        } else {
            // Swap token ke UBA dan bakar
            uint256 ubaAmount = _swapToUBA(token, amount, fee);
            if (ubaAmount > 0) {
                ubaToken.burnForFees(ubaAmount);
                emit FeesProcessed(token, amount, ubaAmount);
            }
        }
    }

    /**
     * @notice Swap token ke UBA melalui Uniswap V3.
     * @param token Alamat token input.
     * @param amount Jumlah token input.
     * @param fee Fee pool Uniswap V3.
     * @return ubaAmount Jumlah UBA yang diperoleh.
     */
    function _swapToUBA(address token, uint256 amount, uint24 fee) internal returns (uint256 ubaAmount) {
        IERC20(token).approve(address(uniswapRouter), amount);

        IUniswapV3SwapRouter.ExactInputSingleParams memory params = IUniswapV3SwapRouter
            .ExactInputSingleParams({
                tokenIn: token,
                tokenOut: address(ubaToken),
                fee: fee,
                recipient: address(this),
                deadline: block.timestamp + 300, // 5 menit
                amountIn: amount,
                amountOutMinimum: 0, // Terima slippage apapun untuk kesederhanaan
                sqrtPriceLimitX96: 0
            });

        try uniswapRouter.exactInputSingle(params) returns (uint256 _ubaAmount) {
            ubaAmount = _ubaAmount;
        } catch {
            // Jika swap gagal, kembalikan fee ke mapping
            collectedFees[token] += amount;
            IERC20(token).approve(address(uniswapRouter), 0);
        }
    }

    /**
     * @notice Memperbarui jumlah minimum untuk memproses fee.
     * @param newAmount Jumlah minimum baru.
     */
    function updateMinimumProcessAmount(uint256 newAmount) external onlyOwner {
        uint256 oldAmount = minimumProcessAmount;
        minimumProcessAmount = newAmount;
        emit MinimumProcessAmountUpdated(oldAmount, newAmount);
    }

    /**
     * @notice Fungsi darurat untuk memulihkan token.
     * @param token Alamat token yang akan dipulihkan.
     * @param amount Jumlah yang akan dipulihkan.
     */
    function emergencyRecoverToken(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(owner(), amount);
    }

    /**
     * @notice Mendapatkan jumlah fee yang dikumpulkan untuk token tertentu.
     * @param token Alamat token.
     * @return Jumlah fee yang dikumpulkan.
     */
    function getCollectedFees(address token) external view returns (uint256) {
        return collectedFees[token];
    }
}
