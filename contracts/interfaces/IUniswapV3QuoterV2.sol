// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/// @title IUniswapV3QuoterV2
/// @notice Antarmuka untuk kontrak Uniswap V3 QuoterV2, digunakan untuk mendapatkan kutipan swap yang akurat.
interface IUniswapV3QuoterV2 {
    /// @notice Mengembalikan jumlah output untuk kutipan input yang tepat.
    /// @param tokenIn Alamat token input.
    /// @param tokenOut Alamat token output.
    /// @param fee Tingkat biaya pool (misal, 500 untuk 0.05%, 3000 untuk 0.3%).
    /// @param amountIn Jumlah token input.
    /// @param sqrtPriceLimitX96 Batas harga opsional untuk mencegah perubahan harga besar.
    /// @return amountOut Jumlah token output.
    /// @return sqrtPriceX96AfterList sqrtPriceX96 setelah pool terakhir di jalur.
    /// @return initializedTicksInPath Tick yang diinisialisasi di jalur.
    /// @return gasEstimate Estimasi gas untuk kutipan.
    function quoteExactInputSingle(
        address tokenIn,
        address tokenOut,
        uint24 fee,
        uint256 amountIn,
        uint160 sqrtPriceLimitX96
    ) external returns (uint256 amountOut, uint160 sqrtPriceX96AfterList, uint32 initializedTicksInPath, uint256 gasEstimate);

    // Tambahkan fungsi kutipan relevan lainnya jika Anda mengimplementasikan multi-hop swaps atau exact output.
}