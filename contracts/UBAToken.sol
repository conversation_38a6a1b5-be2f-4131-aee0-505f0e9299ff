// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Votes.sol";
import "@openzeppelin/contracts/utils/cryptography/EIP712.sol";


/**
 * @title UBAToken
 * @dev Token tata kelola UBA dengan kemampuan pembakaran dan snapshotting.
 * @notice Token ini digunakan untuk tata kelola dan distribusi fee di DEX UbaSwap.
 */
contract UBAToken is ERC20, ERC20Burnable, Ownable, ERC20Votes {
    using SafeERC20 for IERC20;

    /// @notice Total pasokan maksimum token UBA (100,000 UBA)
    uint256 public constant MAX_SUPPLY = 100_000 * 10**18;

    /// @notice Pasokan awal yang dicetak ke deployer (30,000 UBA)
    uint256 public constant INITIAL_SUPPLY = 30_000 * 10**18;

    /// @notice Alamat kontrak FeeDistributor yang diizinkan untuk memanggil burnForFees.
    address public feeDistributorContractAddress;

    /// @notice Dipancarkan saat token dicetak.
    event TokensMinted(address indexed to, uint256 amount);

    /// @notice Dipancarkan saat token dibakar untuk distribusi fee.
    event TokensBurnedForFees(uint256 amount, address indexed burner);

    /// @notice Dipancarkan saat alamat FeeDistributor diperbarui.
    event FeeDistributorAddressUpdated(address indexed oldAddress, address indexed newAddress);

    /**
     * @dev Konstruktor yang mencetak pasokan awal ke deployer.
     * @param initialOwner Alamat yang akan memiliki kontrak.
     */
    constructor(address initialOwner)
        ERC20("UBA Token", "UBA")
        Ownable(initialOwner)
        EIP712("UBA Token", "1")
    {
        require(INITIAL_SUPPLY <= MAX_SUPPLY, "UBAToken: pasokan awal melebihi pasokan maks");
        _mint(initialOwner, INITIAL_SUPPLY);
        emit TokensMinted(initialOwner, INITIAL_SUPPLY);
    }

    /**
     * @notice Menyetel alamat kontrak FeeDistributor. Hanya dapat dipanggil sekali oleh pemilik.
     * @param _newFeeDistributor Alamat baru kontrak FeeDistributor.
     */
    function setFeeDistributorAddress(address _newFeeDistributor) external onlyOwner {
        require(feeDistributorContractAddress == address(0), "UBAToken: distributor fee sudah disetel");
        require(_newFeeDistributor != address(0), "UBAToken: alamat distributor fee baru tidak valid");
        emit FeeDistributorAddressUpdated(feeDistributorContractAddress, _newFeeDistributor);
        feeDistributorContractAddress = _newFeeDistributor;
    }

    /**
     * @notice Mencetak token baru (hanya pemilik).
     * @param to Alamat tujuan pencetakan token.
     * @param amount Jumlah token yang akan dicetak.
     */
    function mint(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "UBAToken: cetak ke alamat nol");
        require(totalSupply() + amount <= MAX_SUPPLY, "UBAToken: melebihi pasokan maks");

        _mint(to, amount);
        emit TokensMinted(to, amount);
    }

    /**
     * @notice Membakar token dari saldo pemanggil untuk mekanisme distribusi fee.
     * @param amount Jumlah token yang akan dibakar.
     */
    function burnForFees(uint256 amount) external {
        require(msg.sender == feeDistributorContractAddress, "UBAToken: hanya FeeDistributor yang dapat membakar untuk fee");
        require(amount > 0, "UBAToken: jumlah pembakaran harus lebih besar dari 0");
        _burn(msg.sender, amount);
        emit TokensBurnedForFees(amount, msg.sender);
    }

    /**
     * @notice Fungsi darurat untuk memulihkan token yang tidak sengaja terkirim.
     * @param token Alamat token yang akan dipulihkan.
     * @param amount Jumlah yang akan dipulihkan.
     */
    function recoverToken(address token, uint256 amount) external onlyOwner {
        require(token != address(this), "UBAToken: tidak dapat memulihkan token UBA");
        IERC20(token).safeTransfer(owner(), amount);
    }

    // Override required for ERC20Votes in OpenZeppelin v5
    function _update(address from, address to, uint256 value)
        internal
        override(ERC20, ERC20Votes)
    {
        super._update(from, to, value);
    }


}
