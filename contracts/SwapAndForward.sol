// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Address.sol";
import "./interfaces/IUniswapV3SwapRouter.sol";
import "./interfaces/IUniswapV3QuoterV2.sol";
import "./interfaces/IWETH.sol";

/**
 * @title SwapAndForward
 * @dev Kontrak untuk fungsionalitas swap dan forward atomik.
 * @notice Memungkinkan pengguna untuk menukar token melalui Uniswap V3 dan secara otomatis meneruskan hasilnya ke alamat lain.
 */
contract SwapAndForward is Ownable, ReentrancyGuard {
    using SafeERC20 for IERC20;
    using Address for address;

    /// @notice Antarmuka Uniswap V3 Router
    IUniswapV3SwapRouter public immutable uniswapRouter;

    /// @notice Antarmuka Uniswap V3 QuoterV2 (untuk kutipan harga)
    IUniswapV3QuoterV2 public immutable uniswapQuoter;

    /// @notice Antarmuka token WETH
    IWETH public immutable WETH;

    /// @notice Alamat kontrak distributor fee
    address public immutable feeDistributor;

    /// @notice Fee protokol dalam basis poin (misal, 5 = 0.05%)
    uint256 public protocolFeeBps;

    /// @notice Fee protokol maksimum (10% = 1000 basis poin)
    uint256 public constant MAX_PROTOCOL_FEE_BPS = 1000;

    /// @notice Denominator basis poin
    uint256 public constant BASIS_POINTS = 10000; // 10000 untuk BPS (1% = 100 BPS)

    /// @notice Dipancarkan saat swap dan forward dieksekusi.
    event SwapAndForwardExecuted(
        address indexed user,
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 amountOut,
        uint256 protocolFee,
        address recipient
    );

    /// @notice Dipancarkan saat fee protokol diperbarui.
    event ProtocolFeeUpdated(uint256 oldFee, uint256 newFee);

    /// @notice Dipancarkan saat fee dikumpulkan dan dikirim ke distributor.
    event FeesCollected(address indexed token, uint256 amount, address indexed distributor);

    /**
     * @dev Konstruktor.
     * @param _uniswapRouter Alamat Uniswap V3 Router.
     * @param _uniswapQuoter Alamat Uniswap V3 QuoterV2.
     * @param _weth Alamat token WETH.
     * @param _feeDistributor Alamat kontrak distributor fee.
     * @param _protocolFeeBps Fee protokol awal dalam basis poin.
     * @param _owner Alamat pemilik kontrak.
     */
    constructor(
        address _uniswapRouter,
        address _uniswapQuoter, // Parameter konstruktor baru untuk QuoterV2
        address _weth,
        address _feeDistributor,
        uint256 _protocolFeeBps,
        address _owner
    ) Ownable(_owner) {
        require(_uniswapRouter != address(0), "SwapAndForward: alamat router tidak valid");
        require(_uniswapQuoter != address(0), "SwapAndForward: alamat quoter tidak valid");
        require(_weth != address(0), "SwapAndForward: alamat WETH tidak valid");
        require(_feeDistributor != address(0), "SwapAndForward: alamat distributor fee tidak valid");
        require(_protocolFeeBps <= MAX_PROTOCOL_FEE_BPS, "SwapAndForward: fee terlalu tinggi");

        uniswapRouter = IUniswapV3SwapRouter(_uniswapRouter);
        uniswapQuoter = IUniswapV3QuoterV2(_uniswapQuoter); // Inisialisasi Quoter
        WETH = IWETH(_weth);
        feeDistributor = _feeDistributor;
        protocolFeeBps = _protocolFeeBps;
    }

    /// @notice Fungsi fallback untuk memungkinkan kontrak menerima ETH native.
    receive() external payable {}

    /**
     * @notice Eksekusi swap dan forward dalam satu transaksi.
     * @param tokenIn Alamat token input (gunakan address(0) untuk ETH).
     * @param amountIn Jumlah token input.
     * @param tokenOut Alamat token output (gunakan address(0) untuk ETH).
     * @param amountOutMin Jumlah minimum token output yang diharapkan.
     * @param recipient Alamat untuk menerima token yang ditukar.
     * @param fee Tingkat biaya pool Uniswap V3.
     * @param deadline Batas waktu transaksi.
     * @return amountOut Jumlah token yang diterima oleh penerima (setelah fee).
     */
    function swapAndForwardSingleHop(
        address tokenIn,
        uint256 amountIn,
        address tokenOut,
        uint256 amountOutMin,
        address recipient,
        uint24 fee,
        uint256 deadline
    ) external payable nonReentrant returns (uint256 amountOut) {
        require(recipient != address(0), "SwapAndForward: penerima tidak valid");
        require(amountIn > 0, "SwapAndForward: jumlah tidak valid");
        require(deadline >= block.timestamp, "SwapAndForward: transaksi kadaluarsa");

        address currentTokenIn = tokenIn;

        // Tangani token input (ETH atau ERC20)
        if (tokenIn == address(0)) {
            // Input ETH
            require(msg.value == amountIn, "SwapAndForward: jumlah ETH yang dikirim tidak sesuai");
            WETH.deposit{value: amountIn}();
            currentTokenIn = address(WETH); // Atur currentTokenIn ke WETH untuk swap
        } else {
            // Input ERC20
            require(msg.value == 0, "SwapAndForward: ETH tidak terduga dengan input ERC20");
            IERC20(currentTokenIn).safeTransferFrom(msg.sender, address(this), amountIn);
        }

        // Setujui router Uniswap untuk menarik jumlah tepat yang diperlukan untuk swap.
        // Menggunakan `approve` daripada `safeApprove` secara langsung untuk menghindari transaksi ganda (set 0 lalu set jumlah).
        // Jika persetujuan sudah disetel untuk jumlah yang lebih tinggi, ini hanya akan menyetel ulang.
        IERC20(currentTokenIn).approve(address(uniswapRouter), amountIn);

        // Eksekusi swap
        // Jika tokenOut asli adalah ETH (address(0)), tukar ke WETH terlebih dahulu, lalu unwrap nanti.
        address swapOutputToken = tokenOut == address(0) ? address(WETH) : tokenOut;

        IUniswapV3SwapRouter.ExactInputSingleParams memory params = IUniswapV3SwapRouter
            .ExactInputSingleParams({
                tokenIn: currentTokenIn,
                tokenOut: swapOutputToken,
                fee: fee,
                recipient: address(this), // Kontrak ini menerima output swap
                deadline: deadline,
                amountIn: amountIn,
                amountOutMinimum: amountOutMin,
                sqrtPriceLimitX96: 0
            });

        uint256 swapAmountOut;
        try uniswapRouter.exactInputSingle(params) returns (uint256 _swapAmountOut) {
            swapAmountOut = _swapAmountOut;
        } catch Error(string memory reason) {
            revert(string(abi.encodePacked("SwapAndForward: swap Uniswap gagal: ", reason)));
        } catch {
            revert("SwapAndForward: swap Uniswap gagal karena kesalahan tidak dikenal");
        }

        require(swapAmountOut >= amountOutMin, "SwapAndForward: slippage terlalu tinggi");

        // Hitung fee protokol
        uint256 protocolFee = (swapAmountOut * protocolFeeBps) / BASIS_POINTS;
        require(protocolFee <= swapAmountOut, "SwapAndForward: fee protokol melebihi jumlah swap"); // Pastikan fee tidak melebihi output
        uint256 amountAfterFee = swapAmountOut - protocolFee;

        // --- Tangani token output dan fee ---

        // 1. Kirim fee protokol ke distributor fee
        if (protocolFee > 0) {
            IERC20(swapOutputToken).safeTransfer(feeDistributor, protocolFee);
            emit FeesCollected(swapOutputToken, protocolFee, feeDistributor); // Pancarkan event
        }

        // 2. Tangani transfer akhir ke penerima (dan unwrap jika perlu)
        if (amountAfterFee > 0) {
            if (tokenOut == address(0)) {
                WETH.withdraw(amountAfterFee);
                (bool success, ) = recipient.call{value: amountAfterFee}("");
                require(success, "SwapAndForward: transfer ETH ke penerima gagal");
            } else {
                IERC20(tokenOut).safeTransfer(recipient, amountAfterFee);
            }
        }

        emit SwapAndForwardExecuted(
            msg.sender,
            tokenIn,
            tokenOut,
            amountIn,
            amountAfterFee,
            protocolFee,
            recipient
        );

        // --- Bersihkan Sisa WETH/ETH ---
        if (address(this).balance > 0) {
            payable(msg.sender).transfer(address(this).balance);
        }
        uint256 remainingWETH = WETH.balanceOf(address(this));
        if (remainingWETH > 0) {
             WETH.withdraw(remainingWETH);
             payable(msg.sender).transfer(remainingWETH);
        }
        return amountAfterFee;
    }

    /**
     * @notice Memperbarui fee protokol (hanya pemilik).
     * @param newFeeBps Fee protokol baru dalam basis poin.
     */
    function updateProtocolFee(uint256 newFeeBps) external onlyOwner {
        require(newFeeBps <= MAX_PROTOCOL_FEE_BPS, "SwapAndForward: fee baru terlalu tinggi");

        uint256 oldFee = protocolFeeBps;
        protocolFeeBps = newFeeBps;

        emit ProtocolFeeUpdated(oldFee, newFeeBps);
    }

    /**
     * @notice Mendapatkan kutipan untuk jumlah swap.
     * @dev Fungsi ini memanggil Uniswap V3 QuoterV2 untuk mendapatkan perkiraan jumlah output.
     * Ini juga menghitung fee protokol berdasarkan perkiraan ini.
     * @param tokenIn Alamat token input (gunakan address(0) untuk ETH).
     * @param tokenOut Alamat token output (gunakan address(0) untuk ETH).
     * @param amountIn Jumlah input.
     * @param fee Tingkat biaya pool.
     * @return amountOut Jumlah output yang diperkirakan (sebelum fee protokol).
     * @return protocolFee Jumlah fee protokol yang diperkirakan.
     */
    function getSwapQuote(
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint24 fee
    ) external view returns (uint256 amountOut, uint256 protocolFee) {
        require(amountIn > 0, "SwapAndForward: jumlah input harus > 0");

        address currentTokenIn = tokenIn == address(0) ? address(WETH) : tokenIn;
        address currentTokenOut = tokenOut == address(0) ? address(WETH) : tokenOut;

        (uint256 estimatedAmountOut, , , ) = uniswapQuoter.quoteExactInputSingle(
            currentTokenIn,
            currentTokenOut,
            fee,
            amountIn,
            0
        );

        uint256 estimatedProtocolFee = (estimatedAmountOut * protocolFeeBps) / BASIS_POINTS;

        if (estimatedProtocolFee > estimatedAmountOut) {
            estimatedProtocolFee = estimatedAmountOut;
        }

        return (estimatedAmountOut, estimatedProtocolFee);
    }

    /**
     * @notice Fungsi darurat untuk menarik token yang terperangkap (hanya pemilik).
     * @param token Alamat token yang akan ditarik.
     * @param amount Jumlah yang akan ditarik.
     */
    function withdrawStuckTokens(address token, uint256 amount) external onlyOwner {
        require(token != address(0), "SwapAndForward: alamat token tidak valid");
        require(token != address(WETH), "SwapAndForward: gunakan withdrawStuckEth untuk WETH/ETH");

        IERC20(token).safeTransfer(owner(), amount);
    }

    /**
     * @notice Fungsi darurat untuk menarik ETH yang terperangkap (hanya pemilik).
     * @param amount Jumlah ETH yang akan ditarik.
     */
    function withdrawStuckEth(uint256 amount) external onlyOwner {
        require(amount <= address(this).balance, "SwapAndForward: saldo ETH tidak cukup");
        uint256 stuckWETHBalance = WETH.balanceOf(address(this));
        if (stuckWETHBalance > 0) {
            WETH.withdraw(stuckWETHBalance);
        }
        (bool success, ) = owner().call{value: amount}("");
        require(success, "SwapAndForward: transfer ETH ke pemilik gagal");
    }

    /**
     * @notice Fungsi fallback.
     */
    fallback() external payable {
        revert("SwapAndForward: fungsi tidak ditemukan");
    }
}